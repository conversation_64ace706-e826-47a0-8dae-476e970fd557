"use client";

import React from 'react';
import { OnboardingStep } from '@/types/auth';
import { useOnboarding } from '@/hooks/useOnboarding';
import OnboardingLayout from './OnboardingLayout';
import OnboardingResumeDialog from './OnboardingResumeDialog';
import WelcomeStep from './steps/WelcomeStep';
import ProfileSetupStep from './steps/ProfileSetupStep';
import PreferencesStep from './steps/PreferencesStep';
import CompletionStep from './steps/CompletionStep';

const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome',
    description: 'Welcome to Agent Platform',
    component: WelcomeStep,
    isCompleted: false,
  },
  {
    id: 'profile',
    title: 'Profile Setup',
    description: 'Set up your profile information',
    component: ProfileSetupStep,
    isCompleted: false,
  },
  {
    id: 'preferences',
    title: 'Preferences',
    description: 'Configure your preferences',
    component: PreferencesStep,
    isCompleted: false,
    isOptional: true,
  },
  {
    id: 'completion',
    title: 'Complete',
    description: 'Finish your setup',
    component: CompletionStep,
    isCompleted: false,
  },
];

export default function OnboardingFlow() {
  const {
    currentStepIndex,
    steps,
    isLoading,
    progress,
    hasResumableProgress,
    goToNext,
    goToPrevious,
    completeOnboarding,
    resumeOnboarding,
    startOver,
  } = useOnboarding(ONBOARDING_STEPS);

  const currentStep = steps[currentStepIndex];
  const CurrentStepComponent = currentStep.component;

  const handleDismissResume = () => {
    // User chose to skip resuming, continue with current state
    // The hasResumableProgress flag will be cleared by the hook
  };

  return (
    <>
      {/* Resume Dialog */}
      {hasResumableProgress && progress && (
        <OnboardingResumeDialog
          isVisible={hasResumableProgress}
          progress={progress}
          onResume={resumeOnboarding}
          onStartOver={startOver}
          onDismiss={handleDismissResume}
        />
      )}

      {/* Main Onboarding Flow */}
      <OnboardingLayout
        steps={steps}
        currentStepIndex={currentStepIndex}
        isLoading={isLoading}
      >
        <CurrentStepComponent
          onNext={goToNext}
          onPrevious={goToPrevious}
          onComplete={completeOnboarding}
          isLoading={isLoading}
        />
      </OnboardingLayout>
    </>
  );
}
