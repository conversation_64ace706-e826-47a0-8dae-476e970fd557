{"tests/agents/test_orchestrator_agent.py": true, "tests/test_enhanced_error_handling.py": true, "tests/test_frontend_integration.py::TestClientTypeDetection::test_spa_request_detection": true, "tests/test_frontend_integration.py::TestClientTypeDetection::test_mobile_request_detection": true, "tests/test_frontend_integration.py::TestClientTypeDetection::test_api_client_detection": true, "tests/test_frontend_integration.py::TestClientTypeDetection::test_browser_request_detection": true, "tests/test_token_security.py::TestClient": true, "tests/test_token_security.py::TestRefreshTokenValidation": true, "tests/test_token_security.py::TestTokenRotation": true, "tests/test_token_security.py::TestRefreshTokenValidation::test_validate_refresh_token_success": true, "tests/test_token_security.py::TestRefreshTokenValidation::test_validate_revoked_token_raises_error": true, "tests/test_token_security.py::TestTokenRotation::test_refresh_token_rotation_success": true, "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_create_user_with_unique_email": true, "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_registration_endpoint_unique_email": true, "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_registration_endpoint_duplicate_email": true, "tests/test_email_uniqueness_validation.py::TestEmailValidationIntegration::test_full_registration_flow_with_validation": true, "tests/test_email_uniqueness_validation.py::TestEmailValidationIntegration::test_error_response_format_consistency": true, "tests/agents/test_planner_agent.py::TestPlannerAgent::test_fallback_action": true, "tests/agents/test_planner_agent.py::TestPlannerAgent::test_get_plan_success": true, "tests/agents/test_planner_agent.py::TestPlannerAgent::test_get_plan_validation_error_with_retry": true, "tests/agents/test_planner_agent.py::TestPlannerAgent::test_init": true, "tests/agents/test_planner_agent.py::TestPlannerAgent": true, "tests/test_chat_integration.py": true, "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration": true, "tests/test_supabase_integration.py::TestSupabaseSchemaCompatibility": true, "tests/test_token_security.py": true, "tests/test_token_security_integration.py::TestClient": true, "tests/test_token_security_integration.py::TestTokenSecurityIntegration": true, "tests/test_token_security_integration.py::TestGoogleOAuthSecurity": true, "tests/test_question_endpoint_unit.py::TestSQLAgent::test_simple_sql_generation": true, "tests/test_question_endpoint_unit.py::TestOutputAgent::test_json_output_formatting": true, "tests/test_question_endpoint_integration.py::TestAgentWorkflowIntegration::test_query_understanding_to_sql_pipeline": true, "tests/test_error_handling.py::TestQueryValidationErrors::test_empty_query_handling": true, "tests/test_question_endpoint_unit.py::TestQueryUnderstandingAgent::test_complex_query_analysis": true, "tests/test_question_endpoint_unit.py::TestQueryUnderstandingAgent::test_invalid_query_handling": true, "tests/test_question_endpoint_unit.py::TestQueryUnderstandingAgent::test_conversation_context_integration": true, "tests/test_question_endpoint_unit.py::TestSQLAgent::test_complex_sql_generation": true, "tests/test_question_endpoint_unit.py::TestSQLAgent::test_database_error_handling": true, "tests/test_question_endpoint_unit.py::TestSQLAgent::test_empty_database_infos": true, "tests/test_question_endpoint_unit.py::TestOutputAgent::test_meaningful_insights_generation": true, "tests/test_question_endpoint_unit.py::TestOutputAgent::test_empty_results_handling": true, "tests/test_question_endpoint_integration.py::TestAgentWorkflowIntegration::test_sql_to_output_pipeline": true, "tests/test_question_endpoint_integration.py::TestAgentWorkflowIntegration::test_full_agent_pipeline_coordination": true, "tests/test_question_endpoint_integration.py::TestAgentWorkflowIntegration::test_error_propagation_and_recovery": true, "tests/test_question_endpoint_integration.py::TestAgentWorkflowIntegration::test_streaming_workflow_integration": true, "tests/test_question_endpoint_integration.py::TestConversationalFlowIntegration::test_conversation_context_preservation": true, "tests/test_question_endpoint_integration.py::TestConversationalFlowIntegration::test_query_complexity_adaptation": true, "tests/test_question_endpoint_integration.py::TestConversationalFlowIntegration::test_multi_table_query_coordination": true, "tests/test_question_endpoint_integration.py::TestConversationalFlowIntegration::test_insight_quality_validation": true, "tests/test_question_endpoint_e2e.py": true, "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_database_connection": true, "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_schema_discovery": true, "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_table_schema_retrieval": true, "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_sql_query_execution": true, "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_complex_join_queries": true, "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_data_type_handling": true, "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_error_handling_with_real_database": true, "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_performance_with_large_datasets": true, "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_connection_recovery": true, "tests/test_error_handling.py::TestQueryValidationErrors::test_whitespace_only_query": true, "tests/test_error_handling.py::TestQueryValidationErrors::test_extremely_long_query": true, "tests/test_error_handling.py::TestQueryValidationErrors::test_special_characters_in_query": true, "tests/test_error_handling.py::TestQueryValidationErrors::test_sql_injection_attempts": true, "tests/test_error_handling.py::TestDatabaseConnectionErrors::test_database_connection_failure": true, "tests/test_error_handling.py::TestDatabaseConnectionErrors::test_database_timeout": true, "tests/test_error_handling.py::TestDatabaseConnectionErrors::test_invalid_sql_generation": true, "tests/test_error_handling.py::TestDatabaseConnectionErrors::test_database_permission_errors": true, "tests/test_error_handling.py::TestAgentProcessingErrors::test_bedrock_api_failure": true, "tests/test_error_handling.py::TestAgentProcessingErrors::test_agent_initialization_failure": true, "tests/test_error_handling.py::TestAgentProcessingErrors::test_malformed_agent_response": true, "tests/test_error_handling.py::TestAgentProcessingErrors::test_agent_communication_failure": true, "tests/test_error_handling.py::TestResourceLimitErrors::test_memory_intensive_query": true, "tests/test_error_handling.py::TestResourceLimitErrors::test_concurrent_request_limits": true, "tests/test_error_handling.py::TestResourceLimitErrors::test_large_result_set_handling": true, "tests/test_error_handling.py::TestGracefulDegradation::test_partial_service_failure": true, "tests/test_error_handling.py::TestGracefulDegradation::test_fallback_to_simple_responses": true, "tests/test_session_management.py::TestSessionCreationAndPersistence::test_new_session_creation": true, "tests/test_session_management.py::TestSessionCreationAndPersistence::test_existing_session_continuation": true, "tests/test_session_management.py::TestSessionCreationAndPersistence::test_session_id_validation": true, "tests/test_session_management.py::TestConversationHistoryMaintenance::test_conversation_history_accumulation": true, "tests/test_session_management.py::TestConversationHistoryMaintenance::test_context_preservation_across_queries": true, "tests/test_session_management.py::TestConversationHistoryMaintenance::test_conversation_history_limits": true, "tests/test_session_management.py::TestSessionIsolation::test_user_session_isolation": true, "tests/test_session_management.py::TestSessionIsolation::test_multiple_sessions_per_user": true, "tests/test_session_management.py::TestMemoryManagementAndCleanup::test_session_memory_management": true, "tests/test_session_management.py::TestMemoryManagementAndCleanup::test_inactive_session_handling": true, "tests/test_session_management.py::TestMemoryManagementAndCleanup::test_session_cleanup_on_completion": true}