import { useEffect, useMemo } from 'react';
import { usePageHeader } from '@/providers/PageHeaderContext';

interface UsePageTitleOptions {
  title: string;
  subtitle?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

/**
 * Custom hook for setting page-specific header information
 * 
 * Industry standard approach for managing page titles and metadata.
 * Automatically handles cleanup when component unmounts.
 * 
 * @param options - Page header configuration
 */
export const usePageTitle = (options: UsePageTitleOptions) => {
  const { setPageHeader, resetPageHeader } = usePageHeader();

  useEffect(() => {
    // Set the page header when component mounts or options change
    setPageHeader(options);

    // Cleanup: reset to default when component unmounts
    return () => {
      resetPageHeader();
    };
  }, [
    options.title, 
    options.subtitle, 
    options.icon, 
    setPageHeader, 
    resetPageHeader
  ]);
};

/**
 * Simplified hook for just setting a page title
 * 
 * @param title - Page title
 * @param icon - Optional icon component
 */
export const useSimplePageTitle = (title: string, icon?: React.ComponentType<{ className?: string }>) => {
  usePageTitle({ title, icon });
}; 