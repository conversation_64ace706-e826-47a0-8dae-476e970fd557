"""
Dashboard-Agent v2
─────────────────
Creates Plotly-based interactive dashboard from execution results.

Now uses standardized response format for better orchestrator integration.
"""

from __future__ import annotations
import uuid, logging, datetime as dt
from typing import Any, Dict, List

from app.agents.base import Agent, AgentResponse, StandardizedAgentOutputs, AgentMemoryItem
from app.services.report_storage_service import ReportStorageService

logger = logging.getLogger(__name__)


class DashboardAgent(Agent):
    def __init__(self, agent_id: str | None = None):
        self.agent_id = agent_id or "dashboard_agent"
        self._storage = ReportStorageService()
        self.initialised = False

    async def initialize(self) -> None:
        self.initialised = True

    async def process(self, msg: Dict[str, Any]) -> Dict[str, Any]:
        """Create interactive dashboard from execution results with standardized response format."""
        if not self.initialised:
            await self.initialize()

        try:
            session_id = msg.get("session_id", "unknown")
            exec_res = msg.get("execution_results", {})
            query = msg.get("query", "Analysis Dashboard")
            user_id = msg.get("user_id", "unknown")

            if not exec_res:
                return AgentResponse.error(
                    self.agent_id,
                    "No execution results provided for dashboard creation",
                    error_details={"provided_keys": list(msg.keys())}
                ).to_dict()

            logger.info(f"📊 Creating dashboard for session {session_id}")
            
            # Generate HTML dashboard
            html_content = self._render_html(exec_res, query)
            
            # Upload dashboard to storage
            dashboard_filename = f"dashboard_{session_id}.html"
            url = self._storage.upload_report(
                blob=html_content.encode('utf-8'),
                user_id=user_id,
                session_id=session_id,
                report_type="dashboard",
                file_extension="html"
            )
            
            # Count dashboard components
            component_count = self._count_dashboard_components(html_content)
            
            # Extract summary info
            summary_info = self._extract_summary_info(exec_res)

            # Create standardized response
            dashboard_id = f"dash_{uuid.uuid4().hex[:8]}"
            
            key_results = {
                "dashboard_created": True,
                "dashboard_url": url,
                "dashboard_id": dashboard_id,
                "component_count": component_count,
                "has_analysis_results": "analysis_results" in exec_res,
                "execution_status": exec_res.get("return_code", "unknown") if isinstance(exec_res, dict) else "unknown"
            }
            
            context_for_next_agent = {
                "dashboard_info": {
                    "url": url,
                    "dashboard_id": dashboard_id,
                    "component_count": component_count
                },
                "task_completed": True
            }
            
            memory_items = [
                AgentMemoryItem(
                    item_type="dashboard",
                    content={
                        "url": url,
                        "dashboard_id": dashboard_id,
                        "component_count": component_count,
                        "query": query
                    },
                    importance="high",
                    context_hint="Interactive dashboard created from analysis results"
                ).to_dict(),
                AgentMemoryItem(
                    item_type="final_deliverable",
                    content={
                        "type": "dashboard",
                        "url": url,
                        "summary": summary_info
                    },
                    importance="high",
                    context_hint="Final deliverable for user query"
                ).to_dict()
            ]

            logger.info(f"✅ Dashboard created successfully: {url}")
            
            return AgentResponse.success(
                self.agent_id,
                data={  # Legacy compatibility
                    "dashboard_id": dashboard_id,
                    "created_at": dt.datetime.utcnow().isoformat() + "Z",
                    "dashboard_url": url,
                    "component_count": component_count,
                    "summary_info": summary_info,
                    "execution_status": exec_res.get("return_code", "unknown") if isinstance(exec_res, dict) else "unknown"
                },
                result_summary=f"Created interactive dashboard with {component_count} components",
                key_results=key_results,
                context_for_next_agent=context_for_next_agent,
                memory_items=memory_items,
                metadata={
                    "dashboard_filename": dashboard_filename,
                    "html_size": len(html_content),
                    "query": query[:100] + "..." if len(query) > 100 else query
                }
            ).to_dict()

        except Exception as e:
            logger.error(f"❌ Dashboard creation failed: {e}")
            logger.error(f"❌ Message keys: {list(msg.keys())}")
            logger.error(f"❌ Message content: {str(msg)[:500]}...")
            
            # Return error response
            return AgentResponse.error(
                self.agent_id,
                f"Dashboard creation failed: {str(e)}",
                error_details={
                    "exception_type": type(e).__name__,
                    "session_id": msg.get("session_id", "unknown"),
                    "has_execution_results": bool(msg.get("execution_results")),
                    "provided_keys": list(msg.keys())
                }
            ).to_dict()

    def _count_dashboard_components(self, html_content: str) -> int:
        """Count the number of components in the dashboard."""
        # Count different types of components
        component_indicators = [
            '<div class="dashboard-section"',
            '<div class="chart-container"',
            '<div class="metric-card"',
            '<div class="summary-section"',
            '<table',
            'id="plotly-chart',
            'class="plotly-graph-div"'
        ]
        
        total_components = 0
        for indicator in component_indicators:
            total_components += html_content.count(indicator)
        
        # Ensure we have at least 1 component
        return max(1, total_components)

    def _render_html(self, exec_res: Dict[str, Any], query: str = "Analysis") -> str:
        """Enhanced HTML template with better formatting and comprehensive analysis results support."""
        
        # Extract different types of information
        summary_info = self._extract_summary_info(exec_res)
        output_files = exec_res.get("output_files", []) if isinstance(exec_res, dict) else []
        return_code = exec_res.get("return_code", "unknown") if isinstance(exec_res, dict) else "unknown"
        
        # Build status indicator
        status_color = "green" if return_code == 0 else "red" if return_code != "unknown" else "orange"
        status_text = "Success" if return_code == 0 else "Error" if return_code != "unknown" else "Unknown"
        
        # Format output files and embed visualizations
        files_html = ""
        visualizations_html = ""
        
        if output_files:
            image_files = []
            other_files = []
            
            for file in output_files:
                filename = file.get('filename', '') if isinstance(file, dict) else str(file)
                file_url = file.get('url', '') if isinstance(file, dict) else ''
                
                # Check if it's a visualization file
                if any(filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.svg']):
                    image_files.append((filename, file_url))
                else:
                    other_files.append((filename, file_url))
            
            # Create visualizations section
            if image_files:
                visualizations_html = "<h3>📊 Generated Visualizations</h3>"
                for filename, url in image_files:
                    # Create a clean title from filename
                    title = filename.replace('_', ' ').replace('.png', '').replace('.jpg', '').title()
                    visualizations_html += f"""
                    <div style="margin-bottom: 30px; text-align: center;">
                        <h4>{title}</h4>
                        <img app="{url}" alt="{title}" style="max-width: 800px; width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    """
            
            # Create files list for non-image files
            if other_files:
                files_html = "<h3>📁 Generated Files</h3><ul>"
                for filename, url in other_files:
                    if url:
                        files_html += f'<li><a href="{url}" target="_blank">{filename}</a></li>'
                    else:
                        files_html += f"<li>{filename}</li>"
                files_html += "</ul>"
        
        # Format summary information
        summary_html = ""
        if summary_info:
            summary_html = "<h3>Key Results</h3><ul>"
            for item in summary_info:
                summary_html += f"<li>{item}</li>"
            summary_html += "</ul>"
        
        return f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Analysis Dashboard</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }}
        .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .status {{ padding: 15px; border-radius: 5px; margin-bottom: 25px; font-weight: bold; }}
        .success {{ background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }}
        .error {{ background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }}
        .warning {{ background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }}
        h1 {{ color: #2c3e50; margin-bottom: 30px; text-align: center; }}
        h3 {{ color: #34495e; margin-top: 35px; margin-bottom: 20px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
        h4 {{ color: #555; margin: 15px 0 10px 0; }}
        ul {{ line-height: 1.8; }}
        li {{ margin-bottom: 8px; }}
        .query {{ background-color: #e3f2fd; padding: 20px; border-left: 5px solid #2196f3; margin-bottom: 25px; border-radius: 0 5px 5px 0; }}
        .metrics {{ display: flex; flex-wrap: wrap; gap: 15px; margin: 20px 0; }}
        .metric {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px; text-align: center; min-width: 150px; }}
        .metric-value {{ font-size: 1.8em; font-weight: bold; }}
        .metric-label {{ font-size: 0.9em; opacity: 0.9; }}
        a {{ color: #3498db; text-decoration: none; }}
        a:hover {{ text-decoration: underline; }}
        .timestamp {{ text-align: center; margin-top: 40px; color: #7f8c8d; font-style: italic; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Data Analysis Dashboard</h1>
        
        <div class="query">
            <strong>🎯 Analysis Query:</strong> {query}
        </div>
        
        <div class="status {status_color}">
            <strong>📊 Execution Status:</strong> {status_text}
        </div>
        
        {summary_html}
        
        {visualizations_html}
        
        {files_html}
        
        <div class="timestamp">
            <small>🕒 Generated on {dt.datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC</small>
        </div>
    </div>
</body>
</html>"""

    def _extract_summary_info(self, exec_res: Dict[str, Any]) -> List[str]:
        """Extract meaningful summary information from execution results."""
        summary = []
        
        if not isinstance(exec_res, dict):
            return ["Execution results format not recognized"]
        
        # Check for analysis results  
        if "analysis_results" in exec_res and exec_res["analysis_results"]:
            analysis_results = exec_res["analysis_results"]
            
            # Model accuracy (for predictive analysis)
            if "accuracy" in analysis_results:
                accuracy = analysis_results["accuracy"]
                summary.append(f"🎯 Model Accuracy: {accuracy:.1%}")
            
            # Best model (for ML analysis)
            if "best_model" in analysis_results:
                best_model = analysis_results["best_model"]
                summary.append(f"🏆 Best Model: {best_model}")
            
            # Key findings (for any analysis type)
            if "key_findings" in analysis_results:
                findings = analysis_results["key_findings"]
                if findings and len(findings) > 0:
                    summary.append(f"🔍 Key Findings: {len(findings)} insights discovered")
                    # Add the first key finding as a sample
                    if len(findings) > 0:
                        first_finding = findings[0][:100] + "..." if len(findings[0]) > 100 else findings[0]
                        summary.append(f"💡 Sample Insight: {first_finding}")
            
            # Data summary (for descriptive analysis)
            if "data_summary" in analysis_results:
                summary.append(f"📊 {analysis_results['data_summary']}")
                
            # Statistical metrics (for any statistical analysis)
            for metric in ['correlation', 'mean', 'median', 'std', 'count', 'total']:
                if metric in analysis_results:
                    value = analysis_results[metric]
                    summary.append(f"📈 {metric.title()}: {value:.3f}" if isinstance(value, float) else f"📈 {metric.title()}: {value}")
        
        # Check for basic execution info
        if "return_code" in exec_res:
            if exec_res["return_code"] == 0:
                summary.append("✅ Code executed successfully")
            else:
                summary.append(f"❌ Execution failed with return code {exec_res['return_code']}")
        
        # Check for output files
        output_files = exec_res.get("output_files", [])
        if output_files:
            summary.append(f"📁 Generated {len(output_files)} output files")
        
        # Check for standard output content
        stdout = exec_res.get("stdout", "")
        if stdout and isinstance(stdout, str):
            # Look for accuracy patterns in stdout
            import re
            accuracy_matches = re.findall(r'Accuracy:\s*([\d.]+)', stdout)
            if accuracy_matches:
                summary.append(f"📈 Best Accuracy Found: {float(accuracy_matches[-1]):.1%}")
            
            # Look for F1 score patterns
            f1_matches = re.findall(r'F1[-\s]*score:\s*([\d.]+)', stdout)
            if f1_matches:
                summary.append(f"📊 Best F1-Score: {float(f1_matches[-1]):.3f}")
        
        # Fallback if no summary could be extracted
        if not summary:
            summary.append("Analysis completed - check individual files for results")
        
        return summary
