"""Database Utilities

This module provides utilities for database connections and operations.
"""

import json
import logging
from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session

from app.config import settings
from app.models.auth_db import Base, UserDB, StoredCredentialDB

logger = logging.getLogger(__name__)

# Create SQLAlchemy engine
if hasattr(settings, 'AUTH_DATABASE_URL'):
    DATABASE_URL = settings.AUTH_DATABASE_URL
else:
    # Default to SQLite in-memory database for development
    DATABASE_URL = "sqlite:///./auth.db"

# Configure engine based on database type
if DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        DATABASE_URL, 
        connect_args={"check_same_thread": False}
    )
elif DATABASE_URL.startswith("postgresql") or DATABASE_URL.startswith("postgres"):
    # Use pg8000 driver for PostgreSQL with timeout configurations
    # pg8000 only supports basic connection parameters - PostgreSQL settings must be set via SQL
    engine = create_engine(
        DATABASE_URL.replace("postgresql://", "postgresql+pg8000://"),
        pool_pre_ping=True,
        pool_recycle=1800,  # Recycle connections after 30 minutes
        pool_timeout=30,    # 30 second timeout to get connection from pool
        connect_args={
            'timeout': 30,  # pg8000 connection timeout parameter
            'application_name': 'AgentReport_Auth_DB'  # pg8000 application name parameter
        }
    )
else:
    # For other database types
    engine = create_engine(DATABASE_URL)

# Create sessionmaker
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db() -> Generator[Session, None, None]:
    """Get a database session.
    
    Returns:
        Generator[Session, None, None]: A SQLAlchemy database session generator
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """Initialize the database and create tables if they don't exist."""
    try:
        # Import all models to ensure they're registered with Base
        from app.models.auth_db import UserDB, StoredCredentialDB  # All models
        
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise

def json_serialize(obj):
    """Serialize Python object to JSON string.
    
    Args:
        obj: The Python object to serialize
        
    Returns:
        str: A JSON string representation of the object
    """
    return json.dumps(obj)

def json_deserialize(json_str):
    """Deserialize JSON string to Python object.
    
    Args:
        json_str: The JSON string to deserialize
        
    Returns:
        Any: The deserialized Python object
    """
    if not json_str:
        return {}
    return json.loads(json_str) 
