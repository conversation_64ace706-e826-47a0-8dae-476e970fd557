"""Authentication Database Models

This module defines SQLAlchemy database models for user authentication.
"""

from datetime import datetime
import uuid
from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, Table, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

def generate_uuid():
    """Generate a UUID string."""
    return str(uuid.uuid4())

class UserDB(Base):
    """SQLAlchemy model for users table."""
    __tablename__ = "users"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=True)
    full_name = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_email_verified = Column(Boolean, default=False, nullable=False)
    role = Column(String(50), default="user", nullable=False)
    auth_provider = Column(String(50), default="local", nullable=False)
    auth_provider_id = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = Column(DateTime, nullable=True)
    profile_picture = Column(String(255), nullable=True)
    settings = Column(Text, nullable=True)  # JSON stored as text
    
    # Relationships
    refresh_tokens = relationship("RefreshTokenDB", back_populates="user", cascade="all, delete-orphan")
    oauth_accounts = relationship("OAuthAccountDB", back_populates="user", cascade="all, delete-orphan")

class RefreshTokenDB(Base):
    """SQLAlchemy model for refresh tokens table."""
    __tablename__ = "refresh_tokens"

    id = Column(String(36), primary_key=True, default=generate_uuid)
    token = Column(String(255), unique=True, index=True, nullable=False)
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    revoked = Column(Boolean, default=False, nullable=False)
    revoked_at = Column(DateTime, nullable=True)
    revoked_reason = Column(String(100), nullable=True)
    last_used_at = Column(DateTime, nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    use_count = Column(String(10), default="0", nullable=False)

    # Relationships
    user = relationship("UserDB", back_populates="refresh_tokens")

class OAuthAccountDB(Base):
    """SQLAlchemy model for OAuth accounts table."""
    __tablename__ = "oauth_accounts"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    provider = Column(String(50), nullable=False)  # google, etc.
    provider_user_id = Column(String(255), nullable=False)
    access_token = Column(String(255), nullable=False)
    refresh_token = Column(String(255), nullable=True)
    expires_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Create a unique constraint on provider and provider_user_id
    __table_args__ = (
        {"sqlite_autoincrement": True},
    )
    
    # Relationships
    user = relationship("UserDB", back_populates="oauth_accounts")

class PasswordResetDB(Base):
    """SQLAlchemy model for password reset tokens table."""
    __tablename__ = "password_resets"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    token = Column(String(255), unique=True, index=True, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    used = Column(Boolean, default=False, nullable=False)

class EmailVerificationDB(Base):
    """SQLAlchemy model for email verification tokens table."""
    __tablename__ = "email_verifications"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    token = Column(String(255), unique=True, index=True, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    used = Column(Boolean, default=False, nullable=False)

class StoredCredentialDB(Base):
    """SQLAlchemy model for storing encrypted database credentials."""
    __tablename__ = "stored_credentials"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    db_type = Column(String(50), nullable=False)
    
    # Encrypted credential fields
    encrypted_host = Column(Text, nullable=False)
    encrypted_port = Column(Text, nullable=False)
    encrypted_username = Column(Text, nullable=False)
    encrypted_password = Column(Text, nullable=False)
    encrypted_database = Column(Text, nullable=False)
    encrypted_db_schema = Column(Text, nullable=True)
    
    ssl_enabled = Column(Boolean, default=False)
    encrypted_connection_string = Column(Text, nullable=True)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class TokenBlacklistDB(Base):
    """SQLAlchemy model for blacklisted tokens table."""
    __tablename__ = "token_blacklist"

    id = Column(String(36), primary_key=True, default=generate_uuid)
    token_hash = Column(String(64), unique=True, index=True, nullable=False)  # SHA-256 hash
    token_type = Column(String(20), nullable=False)  # 'access' or 'refresh'
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    expires_at = Column(DateTime, nullable=False)
    blacklisted_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    reason = Column(String(100), nullable=True)  # Reason for blacklisting