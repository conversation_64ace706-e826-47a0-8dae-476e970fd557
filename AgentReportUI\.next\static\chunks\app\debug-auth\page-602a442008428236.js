(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[759],{47765:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(95155),l=t(12115),r=t(58189),o=t(45786);function n(){let[e,s]=(0,l.useState)({}),[t,n]=(0,l.useState)(!0),[i,d]=(0,l.useState)(null),{getUserProfile:c,completeOnboarding:g}=(0,r.g)(),m=async()=>{n(!0),d(null);try{var e,t,a,l;let r={timestamp:new Date().toISOString(),localStorage:{},profileData:null,profileError:null,completionTest:null,completionError:null};r.localStorage={accessToken:(null==(e=localStorage.getItem(o.d5.ACCESS_TOKEN))?void 0:e.substring(0,20))+"...",refreshToken:(null==(t=localStorage.getItem(o.d5.REFRESH_TOKEN))?void 0:t.substring(0,20))+"...",userId:localStorage.getItem(o.d5.USER_ID),expiresAt:localStorage.getItem(o.d5.EXPIRES_AT),tokenType:localStorage.getItem(o.d5.TOKEN_TYPE)};try{console.log("\uD83D\uDD0D Testing getUserProfile...");let e=await c();r.profileData=e,console.log("✅ getUserProfile success:",e)}catch(e){r.profileError={message:e.message,status:null==(a=e.response)?void 0:a.status,data:null==(l=e.response)?void 0:l.data},console.error("❌ getUserProfile failed:",e)}try{console.log("\uD83D\uDD0D Testing completion endpoint...");let e=await fetch("".concat("http://localhost:8000","/api/auth/onboarding/complete"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem(o.d5.ACCESS_TOKEN)),"Content-Type":"application/json"},body:JSON.stringify({})}),s=await e.text();r.completionTest={status:e.status,statusText:e.statusText,data:s},console.log("✅ Completion endpoint test:",r.completionTest)}catch(e){r.completionError={message:e.message},console.error("❌ Completion endpoint test failed:",e)}s(r)}catch(e){d(e.message)}finally{n(!1)}};(0,l.useEffect)(()=>{m()},[]);let u=async()=>{try{console.log("\uD83D\uDE80 Testing actual completion..."),await g(),console.log("✅ Completion successful!"),alert("Completion successful! Check console for details.")}catch(e){console.error("❌ Completion failed:",e),alert("Completion failed: ".concat(e.message))}};return t?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-6",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Debug Authentication"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),(0,a.jsx)("span",{children:"Loading debug information..."})]})]})})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-6",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"\uD83D\uDD0D Debug Authentication"}),(0,a.jsxs)("div",{className:"space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{m()},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg",children:"Refresh"}),(0,a.jsx)("button",{onClick:u,className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg",children:"Test Completion"})]})]}),i&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-red-800 mb-2",children:"Error"}),(0,a.jsx)("p",{className:"text-red-700",children:i})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3",children:"\uD83D\uDCE6 localStorage Data"}),(0,a.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(e.localStorage,null,2)})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3",children:"\uD83D\uDC64 Profile Data (POST /auth/me)"}),e.profileData?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Status:"}),(0,a.jsx)("span",{className:"ml-2 text-green-600",children:"✅ Success"})]}),(0,a.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(e.profileData,null,2)})]}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Status:"}),(0,a.jsx)("span",{className:"ml-2 text-red-600",children:"❌ Failed"})]}),(0,a.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(e.profileError,null,2)})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3",children:"\uD83C\uDFAF Completion Endpoint Test (POST /auth/onboarding/complete)"}),e.completionTest?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Status:"}),(0,a.jsxs)("span",{className:"ml-2 ".concat(200===e.completionTest.status?"text-green-600":"text-red-600"),children:[e.completionTest.status," ",e.completionTest.statusText]})]}),(0,a.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(e.completionTest,null,2)})]}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Status:"}),(0,a.jsx)("span",{className:"ml-2 text-red-600",children:"❌ Failed"})]}),(0,a.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(e.completionError,null,2)})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:"\uD83D\uDD0D What to Look For:"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Profile Data:"})," Check if `is_new_user` is true or false"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Completion Test:"})," Check the status code (200 = success, 400 = already completed, 401 = auth issue)"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"localStorage:"})," Verify tokens are present and not expired"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Console:"})," Check browser console for detailed logs"]})]})]})]})]})})})}},52813:(e,s,t)=>{Promise.resolve().then(t.bind(t,47765))}},e=>{var s=s=>e(e.s=s);e.O(0,[464,189,441,684,358],()=>s(52813)),_N_E=e.O()}]);