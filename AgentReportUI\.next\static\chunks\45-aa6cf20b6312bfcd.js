"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[45],{74045:(e,t,r)=>{r.d(t,{O:()=>_,A:()=>h});var o=r(95155),a=r(12115),s=r(35695),n=r(58189),l=r(45786);let i={maxRetries:3,baseDelay:1e3,maxDelay:1e4,backoffFactor:2};async function c(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"operation";for(let a=0;a<=r.maxRetries;a++)try{console.log("Attempting ".concat(o," (attempt ").concat(a+1,"/").concat(r.maxRetries+1,")"));let t=await e();return a>0&&console.log("".concat(o," succeeded after ").concat(a+1," attempts")),t}catch(s){if(t=s,console.warn("".concat(o," failed on attempt ").concat(a+1,":"),s),a===r.maxRetries)break;let e=Math.min(r.baseDelay*Math.pow(r.backoffFactor,a),r.maxDelay);console.log("Retrying ".concat(o," in ").concat(e,"ms...")),await new Promise(t=>setTimeout(t,e))}throw console.error("".concat(o," failed after ").concat(r.maxRetries+1," attempts")),t}async function u(e,t,r){try{return{data:await c(e,{...i,...r},t)}}catch(r){let e=function(e){var t,r,o,a,s,n,l,i,c,u,d,g,f,m,_,h,w,E,p,y,S;let R=new Date().toISOString();if("NETWORK_ERROR"===e.code||(null==(t=e.message)?void 0:t.includes("network"))||(null==(r=e.message)?void 0:r.includes("fetch")))return{code:"NETWORK_ERROR",message:e.message||"Network error occurred",retryable:!0,userMessage:"Network connection issue. Please check your internet connection and try again.",timestamp:R};if((null==(o=e.response)?void 0:o.status)===401||(null==(a=e.message)?void 0:a.includes("token"))||(null==(s=e.message)?void 0:s.includes("unauthorized")))return{code:"TOKEN_ERROR",message:e.message||"Authentication token invalid",retryable:!1,userMessage:"Your session has expired. Please log in again.",timestamp:R};if((null==(n=e.response)?void 0:n.status)>=500)return{code:"SERVER_ERROR",message:e.message||"Server error occurred",retryable:!0,userMessage:"Server is temporarily unavailable. Please try again in a moment.",timestamp:R};if((null==(l=e.response)?void 0:l.status)===409){let t=null==(f=e.response)||null==(g=f.data)?void 0:g.error;return(null==t?void 0:t.code)==="EMAIL_ALREADY_EXISTS"?{code:"EMAIL_ALREADY_EXISTS",message:t.message||"Email already exists",retryable:!1,userMessage:t.message||"An account with this email address already exists. Please use a different email or try logging in.",timestamp:R}:{code:"CONFLICT_ERROR",message:(null==(h=e.response)||null==(_=h.data)||null==(m=_.error)?void 0:m.message)||e.message||"Conflict error occurred",retryable:!1,userMessage:(null==(p=e.response)||null==(E=p.data)||null==(w=E.error)?void 0:w.message)||"There was a conflict with your request. Please try again.",timestamp:R}}if((null==(i=e.response)?void 0:i.status)>=400&&(null==(c=e.response)?void 0:c.status)<500){let t=null==(S=e.response)||null==(y=S.data)?void 0:y.error;return(null==t?void 0:t.message)?{code:t.code||"CLIENT_ERROR",message:t.message,retryable:!1,userMessage:t.message,timestamp:R}:{code:"CLIENT_ERROR",message:e.message||"Client error occurred",retryable:!1,userMessage:"There was an issue with your request. Please try again or contact support.",timestamp:R}}return(null==(u=e.message)?void 0:u.includes("onboarding"))||(null==(d=e.message)?void 0:d.includes("is_new_user"))?{code:"ONBOARDING_ERROR",message:e.message||"Onboarding error occurred",retryable:!0,userMessage:"There was an issue completing your setup. Please try again.",timestamp:R}:{code:"UNKNOWN_ERROR",message:e.message||"An unknown error occurred",retryable:!0,userMessage:"An unexpected error occurred. Please try again.",timestamp:R}}(r);return console.error("Safe API call failed for ".concat(t,":"),e),{error:e}}}function d(e){var t;if(!function(e){if(!e||"object"!=typeof e)return!1;for(let t of["access_token","user_id"])if(!e[t]||"string"!=typeof e[t])return!1;return void 0===e.is_new_user||"boolean"==typeof e.is_new_user}(e))throw Error("Invalid authentication response structure");return{...e,is_new_user:null!=(t=e.is_new_user)&&t,token_type:e.token_type||"bearer"}}function g(e,t){console.error("Auth Error:",{context:t,code:e.code,message:e.message,timestamp:e.timestamp,retryable:e.retryable,userAgent:window.navigator.userAgent,url:window.location.href})}var f=r(23464);let m=(0,a.createContext)(void 0),_=e=>{let{children:t}=e,[r,i]=(0,a.useState)(null),[c,_]=(0,a.useState)(!1),[h,w]=(0,a.useState)(!1),[E,p]=(0,a.useState)(Date.now()),[y,S]=(0,a.useState)(!1),[R,v]=(0,a.useState)(!1),{loginUser:b,registerUser:k,refreshToken:I,logoutUser:A,getUserProfile:T,completeOnboarding:O}=(0,n.g)(),N=(0,s.useRouter)(),D=(0,a.useCallback)(async()=>{if(!r)return;let{data:e,error:t}=await u(()=>T(),"refresh user state",{maxRetries:2});e?(w(e.is_new_user||!1),p(Date.now()),console.log("User state refreshed, is_new_user:",e.is_new_user)):t&&g(t,"refreshUserState")},[r,T,w,p]),C=(0,a.useCallback)(()=>Date.now()-E>3e5,[E]);(0,a.useEffect)(()=>{let e=async()=>{!document.hidden&&r&&C()&&(console.log("Tab became visible, refreshing user state..."),await D())};return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)},[r,C,D]),(0,a.useEffect)(()=>{if(!r)return;let e=setInterval(async()=>{C()&&(console.log("Periodic state refresh triggered..."),await D())},6e5);return()=>clearInterval(e)},[r,C,D]);let P=(0,a.useCallback)(async()=>{console.log("Manually initializing authentication from storage..."),_(!0);let e=localStorage.getItem(l.d5.ACCESS_TOKEN),t=localStorage.getItem(l.d5.USER_ID),r=localStorage.getItem(l.d5.REFRESH_TOKEN),o=localStorage.getItem(l.d5.EXPIRES_AT);if(!e||!t||!r||!o)return console.log("No complete auth data found in localStorage"),_(!1),!1;try{i({access_token:e,user_id:t,refresh_token:r,expires_at:o,token_type:"bearer"});let a=await T();w(a.is_new_user||!1),p(Date.now()),console.log("User profile fetched, is_new_user:",a.is_new_user);try{let e=new Date(o).getTime();if(Date.now()>e-3e5){console.log("Access token nearing expiry. Refreshing…");let e=await I();i(e)}}catch(e){console.warn("Proactive refresh check failed:",e)}return _(!1),!0}catch(e){var a;if(f.A.isAxiosError(e)&&(null==(a=e.response)?void 0:a.status)===401){console.warn("Access token invalid (401). Attempting refresh…");try{let e=await I();console.log("Token refresh successful after 401"),i(e);let t=await T();return w(t.is_new_user||!1),p(Date.now()),_(!1),!0}catch(e){console.error("Token refresh failed after 401:",e)}}else console.error("Failed to fetch user profile during initialization:",e);return localStorage.removeItem(l.d5.ACCESS_TOKEN),localStorage.removeItem(l.d5.REFRESH_TOKEN),localStorage.removeItem(l.d5.USER_ID),localStorage.removeItem(l.d5.EXPIRES_AT),i(null),_(!1),!1}},[I,T]);(0,a.useEffect)(()=>{!R&&(v(!0),localStorage.getItem(l.d5.ACCESS_TOKEN)&&localStorage.getItem(l.d5.REFRESH_TOKEN)&&console.log("Found stored auth data, but not auto-authenticating. User must manually sign in."))},[R]);let x=async e=>{let{data:t,error:r}=await u(()=>b(e),"user login");if(r)throw g(r,"login"),Error(r.userMessage);if(t){let e=d(t);i(e),w(e.is_new_user),p(Date.now()),e.is_new_user?N.push("/onboarding"):N.push("/dashboard")}},K=async function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(await P()){console.log("Authenticated from stored tokens"),t&&(h?N.push("/onboarding"):N.push("/dashboard"));return}if(e)await x(e);else if(t)N.push("/login");else throw Error("No valid authentication found")},M=async e=>{let{data:t,error:r}=await u(()=>k(e),"user registration");if(r)throw g(r,"register"),Error(r.userMessage);if(t){console.log("Registration successful, automatically logging in user...");try{let t={username:e.email,password:e.password},{data:r,error:o}=await u(()=>b(t),"auto-login after registration");if(o){g(o,"auto-login after registration"),console.warn("Auto-login failed after registration, redirecting to login page"),N.push("/login?registration=success");return}if(r){let e=d(r);i(e),w(!0),p(Date.now()),console.log("Auto-login successful, redirecting to onboarding..."),N.push("/onboarding")}}catch(e){console.error("Auto-login failed after registration:",e),N.push("/login?registration=success")}}},F=async()=>{try{await A()}catch(e){console.error("Logout API call failed:",e)}localStorage.removeItem(l.d5.ACCESS_TOKEN),localStorage.removeItem(l.d5.REFRESH_TOKEN),localStorage.removeItem(l.d5.USER_ID),localStorage.removeItem(l.d5.EXPIRES_AT),localStorage.removeItem(l.d5.TOKEN_TYPE),i(null),w(!1),v(!1),N.push("/")},U=async()=>{try{let e=await I();i(e),void 0!==e.is_new_user?(w(e.is_new_user),p(Date.now())):C()&&await D()}catch(e){throw console.error("Token refresh failed:",e),i(null),w(!1),p(Date.now()),N.push("/login"),e}},L=async()=>{try{S(!0),console.log("\uD83D\uDE80 Starting onboarding completion..."),await O(),console.log("✅ Backend onboarding completion successful");let e=await T();console.log("✅ Updated profile fetched:",{is_new_user:e.is_new_user}),w(e.is_new_user||!1),p(Date.now()),console.log("✅ User state updated, redirecting to dashboard..."),setTimeout(()=>{S(!1),N.push("/dashboard")},100)}catch(e){throw console.error("❌ Failed to complete onboarding:",e),S(!1),e}},X=(0,a.useCallback)(async e=>{try{let t=new Date(Date.now()+1e3*e.expires_in).toISOString();localStorage.setItem(l.d5.ACCESS_TOKEN,e.access_token),localStorage.setItem(l.d5.REFRESH_TOKEN,e.refresh_token),localStorage.setItem(l.d5.USER_ID,e.user_id),localStorage.setItem(l.d5.EXPIRES_AT,t),localStorage.setItem(l.d5.TOKEN_TYPE,e.token_type||"bearer");let r={access_token:e.access_token,token_type:e.token_type||"bearer",user_id:e.user_id,expires_at:t,refresh_token:e.refresh_token};i(r);try{let e=await T();w(e.is_new_user||!1),p(Date.now()),console.log("OAuth user profile fetched, is_new_user:",e.is_new_user)}catch(e){console.error("Failed to fetch user profile during OAuth callback:",e),w(!1)}}catch(e){throw console.error("OAuth callback handling failed:",e),e}},[i]);return(0,o.jsx)(m.Provider,{value:{isAuthenticated:!!r,user:r,isLoading:c,isNewUser:h,isCompletingOnboarding:y,login:x,signIn:K,initializeAuthFromStorage:P,register:M,logout:F,setUser:i,refreshUserToken:U,handleOAuthCallback:X,completeOnboarding:L},children:t})},h=()=>{let e=(0,a.useContext)(m);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}}]);