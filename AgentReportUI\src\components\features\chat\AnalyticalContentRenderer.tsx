import React, { useMemo } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/MarkdownRenderer';
import { BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AnalyticalContentRendererProps {
  content: string;
  className?: string;
  isStreaming?: boolean;
}

// Function to detect analytical content patterns
const detectAnalyticalSections = (content: string) => {
  const sections = {
    hasExecutiveSummary: /## Executive Summary/i.test(content),
    hasKeyInsights: /### Key Insights/i.test(content),
    hasBusinessImplications: /### Business Implications/i.test(content),
    hasRecommendations: /### Recommendation/i.test(content),
    hasStatisticalOverview: /### Statistical Overview/i.test(content),
    hasNumberedResults: /^\d+\.\s+\*\*/.test(content.replace(/\n/g, ' ')),
    hasBulletPoints: /^\*\s+\*\*/.test(content.replace(/\n/g, ' ')),
  };
  
  return sections;
};

// Enhanced preprocessing for analytical content
const preprocessAnalyticalContent = (content: string): string => {
  if (!content) return '';

  let processed = content;
  const sections = detectAnalyticalSections(content);

  // Add icons and enhanced styling for key sections
  if (sections.hasExecutiveSummary) {
    processed = processed.replace(
      /## Executive Summary/gi,
      '## 📊 Executive Summary'
    );
  }

  if (sections.hasKeyInsights) {
    processed = processed.replace(
      /### Key Insights:/gi,
      '### 💡 Key Insights:'
    );
  }

  if (sections.hasBusinessImplications) {
    processed = processed.replace(
      /### Business Implications:/gi,
      '### 🎯 Business Implications:'
    );
  }

  if (sections.hasRecommendations) {
    processed = processed.replace(
      /### Recommendation:/gi,
      '### 🚀 Recommendation:'
    );
  }

  if (sections.hasStatisticalOverview) {
    processed = processed.replace(
      /### Statistical Overview/gi,
      '### 📈 Statistical Overview'
    );
  }

  return processed;
};

export const AnalyticalContentRenderer: React.FC<AnalyticalContentRendererProps> = React.memo(({
  content,
  className = '',
  isStreaming = false,
}) => {
  // Memoize expensive operations to prevent infinite re-renders
  const sections = useMemo(() => detectAnalyticalSections(content), [content]);
  const processedContent = useMemo(() => preprocessAnalyticalContent(content), [content]);
  
  // Determine if this is analytical content
  const isAnalyticalContent = useMemo(() => Object.values(sections).some(Boolean), [sections]);

  return (
    <div className={cn(
      "analytical-content",
      className
    )}>
      <MarkdownRenderer
        content={processedContent}
        isStreaming={isStreaming}
        className={cn(
          isAnalyticalContent && "analytical-markdown",
          className
        )}
      />
    </div>
  );
});

AnalyticalContentRenderer.displayName = 'AnalyticalContentRenderer';

export default AnalyticalContentRenderer;
