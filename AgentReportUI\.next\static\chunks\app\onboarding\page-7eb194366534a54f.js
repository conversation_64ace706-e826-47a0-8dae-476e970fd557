(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[566],{1243:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},14186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},26082:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ec});var a=s(95155),l=s(12115),r=s(74045),n=s(35695),i=s(27874),o=s(28857),c=s(40646),d=s(19946);let u=(0,d.A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var x=s(46486);function m(e){let{steps:t,currentStepIndex:s,isLoading:l,children:r}=e,n=(s+1)/t.length*100;return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700 px-6 py-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-slate-900 dark:text-slate-100",children:"Welcome to Agent Platform"}),(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-400 mt-1",children:"Let's get you set up in just a few steps"})]}),(0,a.jsxs)("div",{className:"text-sm text-slate-500 dark:text-slate-400",children:["Step ",s+1," of ",t.length]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)(o.k,{value:n,className:"h-2"})}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:t.map((e,l)=>(0,a.jsxs)("div",{className:(0,x.cn)("flex items-center space-x-2",l<t.length-1&&"flex-1"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.isCompleted?(0,a.jsx)(c.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"}):(0,a.jsx)(u,{className:(0,x.cn)("h-5 w-5",l===s?"text-blue-600 dark:text-blue-400":"text-slate-300 dark:text-slate-600")}),(0,a.jsx)("span",{className:(0,x.cn)("text-sm font-medium",e.isCompleted?"text-green-600 dark:text-green-400":l===s?"text-blue-600 dark:text-blue-400":"text-slate-400 dark:text-slate-500"),children:e.title}),e.isOptional&&(0,a.jsx)("span",{className:"text-xs text-slate-400 dark:text-slate-500",children:"(Optional)"})]}),l<t.length-1&&(0,a.jsx)("div",{className:"flex-1 h-px bg-slate-200 dark:bg-slate-700 mx-4"})]},e.id))})]})}),(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center p-6",children:(0,a.jsx)("div",{className:"w-full max-w-2xl",children:r})})]})}var p=s(30285),h=s(66695),f=s(26126),g=s(14186),b=s(85690);let j=(0,d.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);function v(e){let{isVisible:t,progress:s,onResume:l,onStartOver:r,onDismiss:n}=e;if(!t)return null;let i=Math.round(s.completedSteps.length/s.totalSteps*100),d=new Date(s.lastUpdated),u=Math.round((Date.now()-d.getTime())/6e4);return(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm",children:(0,a.jsxs)(h.Zp,{className:"w-full max-w-md mx-4 shadow-2xl",children:[(0,a.jsxs)(h.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit",children:(0,a.jsx)(g.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)(h.ZB,{className:"text-xl font-bold text-slate-900 dark:text-slate-100",children:"Resume Your Setup?"}),(0,a.jsx)(h.BT,{className:"text-slate-600 dark:text-slate-400",children:"We found your previous onboarding progress"})]}),(0,a.jsxs)(h.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Progress"}),(0,a.jsxs)(f.E,{variant:"outline",className:"text-xs",children:[s.completedSteps.length," of ",s.totalSteps," steps"]})]}),(0,a.jsx)(o.k,{value:i,className:"h-2"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("span",{className:"text-lg font-semibold text-blue-600 dark:text-blue-400",children:[i,"%"]}),(0,a.jsx)("span",{className:"text-sm text-slate-500 dark:text-slate-400 ml-1",children:"complete"})]})]}),(0,a.jsxs)("div",{className:"p-4 bg-slate-50 dark:bg-slate-800 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),(0,a.jsx)("span",{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Next Step"})]}),(0,a.jsx)("p",{className:"text-slate-900 dark:text-slate-100 font-medium capitalize",children:s.currentStep.replace(/([A-Z])/g," $1").trim()})]}),s.completedSteps.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Completed Steps"}),(0,a.jsx)("div",{className:"space-y-1",children:s.completedSteps.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),(0,a.jsx)("span",{className:"text-sm text-slate-600 dark:text-slate-400 capitalize",children:e.replace(/([A-Z])/g," $1").trim()})]},e))})]}),(0,a.jsxs)("div",{className:"text-center text-xs text-slate-500 dark:text-slate-400",children:["Last updated ",(e=>{if(e<1)return"just now";if(e<60)return"".concat(e," minute").concat(1===e?"":"s"," ago");let t=Math.round(e/60);if(t<24)return"".concat(t," hour").concat(1===t?"":"s"," ago");let s=Math.round(t/24);return"".concat(s," day").concat(1===s?"":"s"," ago")})(u)]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(p.$,{onClick:l,className:"w-full flex items-center justify-center space-x-2",size:"lg",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Continue Setup"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)(p.$,{variant:"outline",onClick:r,className:"flex-1 flex items-center justify-center space-x-2",children:[(0,a.jsx)(j,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Start Over"})]}),(0,a.jsx)(p.$,{variant:"ghost",onClick:n,className:"flex-1",children:"Skip for Now"})]})]}),(0,a.jsx)("div",{className:"text-center text-xs text-slate-500 dark:text-slate-400",children:(0,a.jsx)("p",{children:"You can always complete your setup later from your profile settings."})})]})]})})}var y=s(54213),N=s(81497),k=s(72713);let w=(0,d.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),C=[{icon:y.A,title:"Connect Your Data",description:"Easily connect to multiple databases and data sources"},{icon:N.A,title:"AI-Powered Chat",description:"Ask questions about your data in natural language"},{icon:k.A,title:"Generate Reports",description:"Create beautiful reports and visualizations automatically"}];var S=s(62523),A=s(85057),z=s(88539),P=s(71007);let O=(0,d.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),R=(0,d.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var T=s(85185),M=s(6101),F=s(46081),E=s(5845),q=s(45503),D=s(11275),L=s(63655),Z="Switch",[I,B]=(0,F.A)(Z),[W,Y]=I(Z),$=l.forwardRef((e,t)=>{let{__scopeSwitch:s,name:r,checked:n,defaultChecked:i,required:o,disabled:c,value:d="on",onCheckedChange:u,form:x,...m}=e,[p,h]=l.useState(null),f=(0,M.s)(t,e=>h(e)),g=l.useRef(!1),b=!p||x||!!p.closest("form"),[j,v]=(0,E.i)({prop:n,defaultProp:null!=i&&i,onChange:u,caller:Z});return(0,a.jsxs)(W,{scope:s,checked:j,disabled:c,children:[(0,a.jsx)(L.sG.button,{type:"button",role:"switch","aria-checked":j,"aria-required":o,"data-state":G(j),"data-disabled":c?"":void 0,disabled:c,value:d,...m,ref:f,onClick:(0,T.m)(e.onClick,e=>{v(e=>!e),b&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),b&&(0,a.jsx)(J,{control:p,bubbles:!g.current,name:r,value:d,checked:j,required:o,disabled:c,form:x,style:{transform:"translateX(-100%)"}})]})});$.displayName=Z;var _="SwitchThumb",V=l.forwardRef((e,t)=>{let{__scopeSwitch:s,...l}=e,r=Y(_,s);return(0,a.jsx)(L.sG.span,{"data-state":G(r.checked),"data-disabled":r.disabled?"":void 0,...l,ref:t})});V.displayName=_;var J=l.forwardRef((e,t)=>{let{__scopeSwitch:s,control:r,checked:n,bubbles:i=!0,...o}=e,c=l.useRef(null),d=(0,M.s)(c,t),u=(0,q.Z)(n),x=(0,D.X)(r);return l.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==n&&t){let s=new Event("click",{bubbles:i});t.call(e,n),e.dispatchEvent(s)}},[u,n,i]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...o,tabIndex:-1,ref:d,style:{...o.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function G(e){return e?"checked":"unchecked"}J.displayName="SwitchBubbleInput";let H=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)($,{className:(0,x.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 focus-visible:ring-offset-white disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-slate-900 data-[state=unchecked]:bg-slate-200 dark:focus-visible:ring-slate-300 dark:focus-visible:ring-offset-slate-950 dark:data-[state=checked]:bg-slate-50 dark:data-[state=unchecked]:bg-slate-800",s),...l,ref:t,children:(0,a.jsx)(V,{className:(0,x.cn)("pointer-events-none block h-4 w-4 rounded-full bg-white shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0 dark:bg-slate-950")})})});H.displayName=$.displayName;var U=s(59409),X=s(381),Q=s(28883);let K=(0,d.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]);var ee=s(51154);function et(e){let{isVisible:t,message:s="Setting up your account...",submessage:l="This will only take a moment",type:r="loading"}=e;if(!t)return null;let n=(()=>{switch(r){case"success":return{bg:"bg-green-50 dark:bg-green-900/20",border:"border-green-200 dark:border-green-800",text:"text-green-900 dark:text-green-100",subtext:"text-green-700 dark:text-green-300"};case"completing":return{bg:"bg-blue-50 dark:bg-blue-900/20",border:"border-blue-200 dark:border-blue-800",text:"text-blue-900 dark:text-blue-100",subtext:"text-blue-700 dark:text-blue-300"};default:return{bg:"bg-white dark:bg-slate-900",border:"border-slate-200 dark:border-slate-700",text:"text-slate-900 dark:text-slate-100",subtext:"text-slate-600 dark:text-slate-400"}}})();return(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm",children:(0,a.jsx)(h.Zp,{className:(0,x.cn)("w-full max-w-md mx-4 shadow-2xl",n.bg,n.border),children:(0,a.jsxs)(h.Wu,{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"mb-6 flex justify-center",children:(()=>{switch(r){case"success":return(0,a.jsx)(c.A,{className:"h-12 w-12 text-green-600 dark:text-green-400"});case"completing":return(0,a.jsx)(w,{className:"h-12 w-12 text-blue-600 dark:text-blue-400 animate-pulse"});default:return(0,a.jsx)(ee.A,{className:"h-12 w-12 text-blue-600 dark:text-blue-400 animate-spin"})}})()}),(0,a.jsx)("h3",{className:(0,x.cn)("text-xl font-semibold mb-2",n.text),children:s}),(0,a.jsx)("p",{className:(0,x.cn)("text-sm",n.subtext),children:l}),"loading"===r&&(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("div",{className:"flex justify-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]})}),"completing"===r&&(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"w-8 h-1 bg-blue-200 dark:bg-blue-800 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"w-full h-full bg-blue-600 dark:bg-blue-400 rounded-full animate-pulse"})})})})]})})})}let es=[{icon:y.A,title:"Connect Your First Database",description:"Start by connecting to your data sources",action:"Go to Data Sources"},{icon:N.A,title:"Ask Your First Question",description:"Try asking questions about your data in natural language",action:"Start Chatting"},{icon:k.A,title:"Explore Reports",description:"View and create reports from your data",action:"View Reports"}],ea=[{id:"welcome",title:"Welcome",description:"Welcome to Agent Platform",component:function(e){let{onNext:t,isLoading:s}=e;return(0,a.jsxs)(h.Zp,{className:"border-0 shadow-lg",children:[(0,a.jsxs)(h.aR,{className:"text-center pb-6",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit",children:(0,a.jsx)(w,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)(h.ZB,{className:"text-3xl font-bold text-slate-900 dark:text-slate-100",children:"Welcome to Agent Platform!"}),(0,a.jsx)(h.BT,{className:"text-lg text-slate-600 dark:text-slate-400 mt-2",children:"Your intelligent data analysis companion is ready to help you unlock insights from your data."})]}),(0,a.jsxs)(h.Wu,{className:"space-y-6",children:[(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-3",children:C.map((e,t)=>(0,a.jsxs)("div",{className:"text-center p-4 rounded-lg bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700",children:[(0,a.jsx)("div",{className:"mx-auto mb-3 p-2 bg-white dark:bg-slate-700 rounded-lg w-fit",children:(0,a.jsx)(e.icon,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)("h3",{className:"font-semibold text-slate-900 dark:text-slate-100 mb-1",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:e.description})]},t))}),(0,a.jsxs)("div",{className:"text-center py-6",children:[(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-400 mb-6",children:"Let's get you set up with a quick tour and personalize your experience. This will only take a few minutes!"}),(0,a.jsx)(p.$,{onClick:t,disabled:s,size:"lg",className:"px-8 py-3 text-lg",children:"Get Started"})]}),(0,a.jsx)("div",{className:"text-center text-sm text-slate-500 dark:text-slate-400",children:(0,a.jsx)("p",{children:"You can always change these settings later in your profile."})})]})]})},isCompleted:!1},{id:"profile",title:"Profile Setup",description:"Set up your profile information",component:function(e){let{onNext:t,onPrevious:s,isLoading:r}=e,[n,i]=(0,l.useState)({fullName:"",jobTitle:"",company:"",bio:""}),[o,c]=(0,l.useState)({}),d=(e,t)=>{i(s=>({...s,[e]:t})),o[e]&&c(t=>({...t,[e]:void 0}))},u=()=>{let e={};return n.fullName.trim()||(e.fullName="Full name is required"),c(e),0===Object.keys(e).length};return(0,a.jsxs)(h.Zp,{className:"border-0 shadow-lg",children:[(0,a.jsxs)(h.aR,{className:"text-center pb-6",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit",children:(0,a.jsx)(P.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)(h.ZB,{className:"text-2xl font-bold text-slate-900 dark:text-slate-100",children:"Set Up Your Profile"}),(0,a.jsx)(h.BT,{className:"text-slate-600 dark:text-slate-400 mt-2",children:"Tell us a bit about yourself to personalize your experience."})]}),(0,a.jsxs)(h.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(A.J,{htmlFor:"fullName",className:"text-sm font-medium",children:"Full Name *"}),(0,a.jsx)(S.p,{id:"fullName",type:"text",placeholder:"Enter your full name",value:n.fullName,onChange:e=>d("fullName",e.target.value),className:o.fullName?"border-red-500":""}),o.fullName&&(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:o.fullName})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(A.J,{htmlFor:"jobTitle",className:"text-sm font-medium",children:"Job Title"}),(0,a.jsx)(S.p,{id:"jobTitle",type:"text",placeholder:"e.g., Data Analyst, Business Intelligence Manager",value:n.jobTitle,onChange:e=>d("jobTitle",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(A.J,{htmlFor:"company",className:"text-sm font-medium",children:"Company"}),(0,a.jsx)(S.p,{id:"company",type:"text",placeholder:"Enter your company name",value:n.company,onChange:e=>d("company",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(A.J,{htmlFor:"bio",className:"text-sm font-medium",children:"Bio (Optional)"}),(0,a.jsx)(z.T,{id:"bio",placeholder:"Tell us about your role and what you hope to achieve with Agent Platform...",value:n.bio,onChange:e=>d("bio",e.target.value),rows:3})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,a.jsxs)(p.$,{variant:"outline",onClick:s,disabled:r,className:"flex items-center space-x-2",children:[(0,a.jsx)(O,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Previous"})]}),(0,a.jsxs)(p.$,{onClick:()=>{u()&&(console.log("Profile data:",n),t())},disabled:r,className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"Continue"}),(0,a.jsx)(R,{className:"h-4 w-4"})]})]}),(0,a.jsx)("div",{className:"text-center text-sm text-slate-500 dark:text-slate-400",children:(0,a.jsx)("p",{children:"* Required fields"})})]})]})},isCompleted:!1},{id:"preferences",title:"Preferences",description:"Configure your preferences",component:function(e){let{onNext:t,onPrevious:s,isLoading:r}=e,[n,i]=(0,l.useState)({emailNotifications:!0,weeklyDigest:!0,securityAlerts:!0,defaultOutputFormat:"excel",theme:"system",timezone:Intl.DateTimeFormat().resolvedOptions().timeZone}),o=(e,t)=>{i(s=>({...s,[e]:t}))},c=(e,t)=>{i(s=>({...s,[e]:t}))};return(0,a.jsxs)(h.Zp,{className:"border-0 shadow-lg",children:[(0,a.jsxs)(h.aR,{className:"text-center pb-6",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit",children:(0,a.jsx)(X.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)(h.ZB,{className:"text-2xl font-bold text-slate-900 dark:text-slate-100",children:"Configure Your Preferences"}),(0,a.jsx)(h.BT,{className:"text-slate-600 dark:text-slate-400 mt-2",children:"Customize your experience with Agent Platform. You can change these anytime."})]}),(0,a.jsxs)(h.Wu,{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(Q.A,{className:"h-5 w-5 text-slate-600 dark:text-slate-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100",children:"Notifications"})]}),(0,a.jsxs)("div",{className:"space-y-4 pl-7",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Receive updates about your queries and reports"})]}),(0,a.jsx)(H,{checked:n.emailNotifications,onCheckedChange:e=>o("emailNotifications",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Weekly Digest"}),(0,a.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Get a summary of your activity and insights"})]}),(0,a.jsx)(H,{checked:n.weeklyDigest,onCheckedChange:e=>o("weeklyDigest",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Security Alerts"}),(0,a.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Important security and account notifications"})]}),(0,a.jsx)(H,{checked:n.securityAlerts,onCheckedChange:e=>o("securityAlerts",e)})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(K,{className:"h-5 w-5 text-slate-600 dark:text-slate-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100",children:"Application"})]}),(0,a.jsxs)("div",{className:"space-y-4 pl-7",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Default Output Format"}),(0,a.jsxs)(U.l6,{value:n.defaultOutputFormat,onValueChange:e=>c("defaultOutputFormat",e),children:[(0,a.jsx)(U.bq,{children:(0,a.jsx)(U.yv,{})}),(0,a.jsxs)(U.gC,{children:[(0,a.jsx)(U.eb,{value:"excel",children:"Excel (.xlsx)"}),(0,a.jsx)(U.eb,{value:"csv",children:"CSV"}),(0,a.jsx)(U.eb,{value:"json",children:"JSON"}),(0,a.jsx)(U.eb,{value:"table",children:"Table View"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(A.J,{className:"text-sm font-medium",children:"Theme"}),(0,a.jsxs)(U.l6,{value:n.theme,onValueChange:e=>c("theme",e),children:[(0,a.jsx)(U.bq,{children:(0,a.jsx)(U.yv,{})}),(0,a.jsxs)(U.gC,{children:[(0,a.jsx)(U.eb,{value:"system",children:"System"}),(0,a.jsx)(U.eb,{value:"light",children:"Light"}),(0,a.jsx)(U.eb,{value:"dark",children:"Dark"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,a.jsxs)(p.$,{variant:"outline",onClick:s,disabled:r,className:"flex items-center space-x-2",children:[(0,a.jsx)(O,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Previous"})]}),(0,a.jsxs)(p.$,{onClick:()=>{console.log("Preferences data:",n),t()},disabled:r,className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"Continue"}),(0,a.jsx)(R,{className:"h-4 w-4"})]})]}),(0,a.jsx)("div",{className:"text-center text-sm text-slate-500 dark:text-slate-400",children:(0,a.jsx)("p",{children:"This step is optional - you can skip and configure these later"})})]})]})},isCompleted:!1,isOptional:!0},{id:"completion",title:"Complete",description:"Finish your setup",component:function(e){let{onPrevious:t,onComplete:s,isLoading:r}=e,[n,i]=(0,l.useState)(!1),[o,d]=(0,l.useState)(null),u=async()=>{i(!0),d(null);try{await s()}catch(c){var e,t,a,l,r,n,o;console.error("Failed to complete onboarding:",c);let s="Failed to complete setup. Please try again.";(null==(e=c.message)?void 0:e.includes("required steps"))?s=c.message:(null==(t=c.message)?void 0:t.includes("network"))||(null==(a=c.message)?void 0:a.includes("fetch"))?s="Network error. Please check your connection and try again.":(null==(l=c.message)?void 0:l.includes("server"))||(null==(r=c.message)?void 0:r.includes("500"))?s="Server error. Please try again in a moment.":((null==(n=c.message)?void 0:n.includes("unauthorized"))||(null==(o=c.message)?void 0:o.includes("401")))&&(s="Session expired. Please log in again."),d(s),i(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(et,{isVisible:n,message:"Completing your setup...",submessage:"Finalizing your Agent Platform account",type:"completing"}),(0,a.jsxs)(h.Zp,{className:"border-0 shadow-lg",children:[(0,a.jsxs)(h.aR,{className:"text-center pb-6",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-green-100 dark:bg-green-900 rounded-full w-fit",children:(0,a.jsx)(c.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,a.jsx)(h.ZB,{className:"text-2xl font-bold text-slate-900 dark:text-slate-100",children:"You're All Set!"}),(0,a.jsx)(h.BT,{className:"text-slate-600 dark:text-slate-400 mt-2",children:"Welcome to Agent Platform! Your account is ready and you can start exploring your data."})]}),(0,a.jsxs)(h.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg w-fit",children:(0,a.jsx)(w,{className:"h-12 w-12 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2",children:"Setup Complete!"}),(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-400",children:"Your Agent Platform account is now configured and ready to use."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100 text-center",children:"What's Next?"}),(0,a.jsx)("div",{className:"grid gap-4",children:es.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 rounded-lg bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700",children:[(0,a.jsx)("div",{className:"p-2 bg-white dark:bg-slate-700 rounded-lg",children:(0,a.jsx)(e.icon,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h5",{className:"font-semibold text-slate-900 dark:text-slate-100",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:e.description})]}),(0,a.jsx)("div",{className:"text-sm text-blue-600 dark:text-blue-400 font-medium",children:e.action})]},t))})]}),o&&(0,a.jsxs)("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,a.jsx)("div",{className:"flex items-start space-x-3",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400 font-medium mb-1",children:"Setup Error"}),(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:o})]})}),(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsx)(p.$,{onClick:u,variant:"outline",size:"sm",className:"text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-700 dark:hover:bg-red-900/20",children:"Try Again"})})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,a.jsxs)(p.$,{variant:"outline",onClick:t,disabled:r||n,className:"flex items-center space-x-2",children:[(0,a.jsx)(O,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Previous"})]}),(0,a.jsx)(p.$,{onClick:u,disabled:r||n,size:"lg",className:"px-8 flex items-center space-x-2",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ee.A,{className:"h-4 w-4 animate-spin"}),(0,a.jsx)("span",{children:"Completing Setup..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:"Complete Setup"}),(0,a.jsx)(c.A,{className:"h-4 w-4"})]})})]}),(0,a.jsx)("div",{className:"text-center text-sm text-slate-500 dark:text-slate-400",children:(0,a.jsx)("p",{children:"You can always access these features from your dashboard or the navigation menu."})})]})]})]})},isCompleted:!1}];function el(){let{currentStepIndex:e,steps:t,isLoading:s,progress:n,hasResumableProgress:o,goToNext:c,goToPrevious:d,completeOnboarding:u,resumeOnboarding:x,startOver:p}=function(e){let[t,s]=(0,l.useState)(0),[a,n]=(0,l.useState)(e),[o,c]=(0,l.useState)(!1),[d,u]=(0,l.useState)(null),[x,m]=(0,l.useState)(!1),{completeOnboarding:p}=(0,r.A)();(0,l.useEffect)(()=>{let t=(0,i.Hg)();if(t){let{steps:a,currentStepIndex:l}=(0,i.ZS)(e,t);n(a),s(l),u(t),m(t.completedSteps.length>0),console.log("Restored onboarding progress:",t)}else u((0,i.YL)(e,0))},[e]);let h=t<a.length-1,f=t>0,g=(0,l.useCallback)(()=>{if(h){let e=t+1;n(s=>{let a=s.map((e,s)=>s===t?{...e,isCompleted:!0}:e),l=(0,i.YL)(a,e);return u(l),(0,i.Wr)(l),a}),s(e)}},[t,h]),b=(0,l.useCallback)(()=>{f&&s(e=>e-1)},[f]),j=(0,l.useCallback)(e=>{n(t=>t.map((t,s)=>s===e?{...t,isCompleted:!0}:t))},[]),v=(0,l.useCallback)(async()=>{console.log("\uD83D\uDE80 Starting onboarding completion..."),console.log("Current step index:",t),console.log("Current steps:",a.map(e=>({id:e.id,title:e.title,completed:e.isCompleted,optional:e.isOptional}))),c(!0);try{let e=a.map((e,t)=>e.isOptional?e:{...e,isCompleted:!0});console.log("Updated steps for completion:",e.map(e=>({id:e.id,title:e.title,completed:e.isCompleted,optional:e.isOptional}))),n(e);let t=(0,i.uE)(e);console.log("Validation result:",t),t.canComplete||console.warn("⚠️ Validation failed, but proceeding with completion since user reached completion step"),console.log("✅ Proceeding with backend call..."),await p(),console.log("✅ Backend call successful, cleaning up..."),(0,i.qy)(),u(null),m(!1),console.log("\uD83C\uDF89 Onboarding completion successful!")}catch(e){throw console.error("❌ Failed to complete onboarding:",e),c(!1),e}},[t,p,a]),y=(0,l.useCallback)(()=>{s(0),n(e.map(e=>({...e,isCompleted:!1}))),c(!1),(0,i.qy)(),u((0,i.YL)(e,0)),m(!1)},[e]);return{currentStepIndex:t,steps:a,isLoading:o,canGoNext:h,canGoPrevious:f,progress:d,hasResumableProgress:x,goToNext:g,goToPrevious:b,completeOnboarding:v,markStepCompleted:j,resetOnboarding:y,resumeOnboarding:(0,l.useCallback)(()=>{let t=(0,i.Hg)();if(t){let{steps:a,currentStepIndex:l}=(0,i.ZS)(e,t);n(a),s(l),u(t),m(!1),console.log("Resumed onboarding from step:",t.currentStep)}},[e]),startOver:(0,l.useCallback)(()=>{(0,i.qy)(),s(0),n(e.map(e=>({...e,isCompleted:!1}))),u((0,i.YL)(e,0)),m(!1),c(!1),console.log("Started onboarding over from the beginning")},[e])}}(ea),h=t[e].component;return(0,a.jsxs)(a.Fragment,{children:[o&&n&&(0,a.jsx)(v,{isVisible:o,progress:n,onResume:x,onStartOver:p,onDismiss:()=>{}}),(0,a.jsx)(m,{steps:t,currentStepIndex:e,isLoading:s,children:(0,a.jsx)(h,{onNext:c,onPrevious:d,onComplete:u,isLoading:s})})]})}var er=s(1243),en=s(53904);let ei=(0,d.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);class eo extends l.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Onboarding Error Boundary caught an error:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-6",children:(0,a.jsxs)(h.Zp,{className:"w-full max-w-md border-red-200 dark:border-red-800",children:[(0,a.jsxs)(h.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-red-100 dark:bg-red-900 rounded-full w-fit",children:(0,a.jsx)(er.A,{className:"h-8 w-8 text-red-600 dark:text-red-400"})}),(0,a.jsx)(h.ZB,{className:"text-xl font-bold text-red-900 dark:text-red-100",children:"Onboarding Error"}),(0,a.jsx)(h.BT,{className:"text-red-700 dark:text-red-300",children:"Something went wrong during the setup process"})]}),(0,a.jsxs)(h.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400 mb-4",children:"We encountered an unexpected error while setting up your account. You can try again or return to the home page."}),!1]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsxs)(p.$,{onClick:this.handleRetry,className:"flex-1 flex items-center justify-center space-x-2",children:[(0,a.jsx)(en.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Try Again"})]}),(0,a.jsxs)(p.$,{variant:"outline",onClick:this.handleGoHome,className:"flex-1 flex items-center justify-center space-x-2",children:[(0,a.jsx)(ei,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Go Home"})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-xs text-slate-500 dark:text-slate-400",children:"If this problem persists, please contact support."})})]})]})}):this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})},this.handleGoHome=()=>{window.location.href="/"},this.state={hasError:!1}}}function ec(){let{isAuthenticated:e,isLoading:t,isNewUser:s,isCompletingOnboarding:i}=(0,r.A)(),o=(0,n.useRouter)();return((0,l.useEffect)(()=>{t||i||(e?s||(console.log("\uD83D\uDD04 Onboarding page: Not a new user, redirecting to dashboard"),o.push("/dashboard")):(console.log("\uD83D\uDD04 Onboarding page: Not authenticated, redirecting to login"),o.push("/login")))},[t,e,s,i,o]),t)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-400",children:"Loading..."})]})}):e&&s?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,a.jsx)(eo,{children:(0,a.jsx)(el,{})})}):null}},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(95155);s(12115);var l=s(74466),r=s(46486);let n=(0,l.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...l}=e;return(0,a.jsx)("div",{className:(0,r.cn)(n({variant:s}),t),...l})}},27874:(e,t,s)=>{"use strict";s.d(t,{Hg:()=>r,Wr:()=>l,YL:()=>i,ZS:()=>o,qy:()=>n,uE:()=>c});let a="onboarding_progress";function l(e){try{localStorage.setItem(a,JSON.stringify(e))}catch(e){console.warn("Failed to save onboarding progress to localStorage:",e)}}function r(){try{let e=localStorage.getItem(a);if(!e)return null;let t=JSON.parse(e);if(!t.currentStep||!Array.isArray(t.completedSteps))return null;return t}catch(e){return console.warn("Failed to load onboarding progress from localStorage:",e),null}}function n(){try{localStorage.removeItem(a)}catch(e){console.warn("Failed to clear onboarding progress from localStorage:",e)}}function i(e,t){var s;let a=e.slice(0,t).filter(e=>e.isCompleted).map(e=>e.id);return{currentStep:(null==(s=e[t])?void 0:s.id)||"welcome",completedSteps:a,lastUpdated:new Date().toISOString(),canResume:a.length>0,totalSteps:e.length}}function o(e,t){var s;if(!t||!((s=t)&&"object"==typeof s&&"string"==typeof s.currentStep&&Array.isArray(s.completedSteps)&&"string"==typeof s.lastUpdated&&"boolean"==typeof s.canResume&&"number"==typeof s.totalSteps))return{steps:e,currentStepIndex:0};let a=e.map(e=>({...e,isCompleted:t.completedSteps.includes(e.id)})),l=function(e,t){let s=e.findIndex(e=>e.id===t);return s>=0?s:0}(a,t.currentStep);return{steps:a,currentStepIndex:l}}function c(e){let t=[],s=[],a=["completion","complete","finish","final"],l=e.filter(e=>!e.isOptional&&!a.includes(e.id.toLowerCase()));for(let t of(console.log("\uD83D\uDD0D Validating completion requirements:"),console.log("All steps:",e.map(e=>({id:e.id,title:e.title,completed:e.isCompleted,optional:e.isOptional}))),console.log("Required steps:",l.map(e=>({id:e.id,title:e.title,completed:e.isCompleted}))),l))t.isCompleted||s.push(t.title);console.log("Missing steps:",s),s.length>0&&t.push("Please complete the following required steps: ".concat(s.join(", ")));let r={canComplete:0===t.length,missingSteps:s,errors:t};return console.log("Validation result:",r),r}},28857:(e,t,s)=>{"use strict";s.d(t,{k:()=>y});var a=s(95155),l=s(12115),r=s(46081),n=s(63655),i="Progress",[o,c]=(0,r.A)(i),[d,u]=o(i),x=l.forwardRef((e,t)=>{var s,l,r,i;let{__scopeProgress:o,value:c=null,max:u,getValueLabel:x=h,...m}=e;(u||0===u)&&!b(u)&&console.error((s="".concat(u),l="Progress","Invalid prop `max` of value `".concat(s,"` supplied to `").concat(l,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=b(u)?u:100;null===c||j(c,p)||console.error((r="".concat(c),i="Progress","Invalid prop `value` of value `".concat(r,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let v=j(c,p)?c:null,y=g(v)?x(v,p):void 0;return(0,a.jsx)(d,{scope:o,value:v,max:p,children:(0,a.jsx)(n.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":g(v)?v:void 0,"aria-valuetext":y,role:"progressbar","data-state":f(v,p),"data-value":null!=v?v:void 0,"data-max":p,...m,ref:t})})});x.displayName=i;var m="ProgressIndicator",p=l.forwardRef((e,t)=>{var s;let{__scopeProgress:l,...r}=e,i=u(m,l);return(0,a.jsx)(n.sG.div,{"data-state":f(i.value,i.max),"data-value":null!=(s=i.value)?s:void 0,"data-max":i.max,...r,ref:t})});function h(e,t){return"".concat(Math.round(e/t*100),"%")}function f(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function b(e){return g(e)&&!isNaN(e)&&e>0}function j(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}p.displayName=m;var v=s(46486);let y=l.forwardRef((e,t)=>{let{className:s,value:l,...r}=e;return(0,a.jsx)(x,{ref:t,className:(0,v.cn)("relative h-2 w-full overflow-hidden rounded-full bg-slate-100 dark:bg-slate-800",s),...r,children:(0,a.jsx)(p,{className:"h-full w-full flex-1 bg-slate-900 transition-all dark:bg-slate-50",style:{transform:"translateX(-".concat(100-(l||0),"%)")}})})});y.displayName=x.displayName},28883:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var a=s(95155),l=s(12115),r=s(99708),n=s(74466),i=s(46486);let o=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=l.forwardRef((e,t)=>{let{className:s,variant:l,size:n,asChild:c=!1,...d}=e,u=c?r.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(o({variant:l,size:n,className:s})),ref:t,...d})});c.displayName="Button"},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},40968:(e,t,s)=>{"use strict";s.d(t,{b:()=>i});var a=s(12115),l=s(63655),r=s(95155),n=a.forwardRef((e,t)=>(0,r.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n},46486:(e,t,s)=>{"use strict";s.d(t,{cn:()=>r});var a=s(52596),l=s(39688);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,l.QP)((0,a.$)(t))}},53904:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>c,yv:()=>d});var a=s(95155);s(12115);var l=s(14582),r=s(66474),n=s(5196),i=s(47863),o=s(46486);function c(e){let{...t}=e;return(0,a.jsx)(l.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,a.jsx)(l.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:s="default",children:n,...i}=e;return(0,a.jsxs)(l.l9,{"data-slot":"select-trigger","data-size":s,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[n,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:s,position:r="popper",...n}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...n,children:[(0,a.jsx)(p,{}),(0,a.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(h,{})]})})}function m(e){let{className:t,children:s,...r}=e;return(0,a.jsxs)(l.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(l.p4,{children:s})]})}function p(e){let{className:t,...s}=e;return(0,a.jsx)(l.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(i.A,{className:"size-4"})})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(l.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(r.A,{className:"size-4"})})}},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>r});var a=s(95155);s(12115);var l=s(46486);function r(e){let{className:t,type:s,...r}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...r})}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>r,aR:()=>n});var a=s(95155);s(12115);var l=s(46486);function r(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,l.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,l.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,l.cn)("leading-none font-semibold text-white",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,l.cn)("px-6",t),...s})}},85057:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(95155);s(12115);var l=s(40968),r=s(46486);function n(e){let{className:t,...s}=e;return(0,a.jsx)(l.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},85690:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},88539:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var a=s(95155),l=s(12115),r=s(46486);let n=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...l})});n.displayName="Textarea"},92276:(e,t,s)=>{Promise.resolve().then(s.bind(s,26082))}},e=>{var t=t=>e(e.s=t);e.O(0,[464,817,786,913,189,45,441,684,358],()=>t(92276)),_N_E=e.O()}]);