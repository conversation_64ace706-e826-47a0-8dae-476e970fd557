"""
Chat API – list chats & fetch chat history

Routes
------
POST /api/chats/listchats
    →  [{ session_id, title, last_seen, message_count }, …]

POST /api/chats/getchathistory
    →  [{ role, content }, …]   (oldest → newest)

These endpoints are read-only.  All writing is handled implicitly by
DatabaseManagerService / ConversationService when the user actually
asks a question.
"""

from typing import List, Dict, Any

from fastapi import APIRouter, Depends
from pydantic import BaseModel

from app.services.database_manager_service import DatabaseManagerService
from app.utils.security import get_current_user

router = APIRouter(prefix="/api/chats", tags=["chats"])
db_manager_service = DatabaseManagerService()


# ──────────────────────────────────────────
# Request models
# ──────────────────────────────────────────
class ChatHistoryRequest(BaseModel):
    session_id: str


class DeleteChatRequest(BaseModel):
    session_id: str


class RenameChatRequest(BaseModel):
    session_id: str
    title: str


# ──────────────────────────────────────────
# helper to extract real user id from token
# ──────────────────────────────────────────
async def get_current_user_id(current_user=Depends(get_current_user)) -> str:
    return current_user.id


# ──────────────────────────────────────────
# Public endpoints
# ──────────────────────────────────────────
@router.post(
    "/listchats",
    summary="List all chat sessions for sidebar",
    response_model=List[Dict[str, Any]],
)
async def list_chats(user_id: str = Depends(get_current_user_id)):
    """
    Return one meta row per chat so the front-end can
    render the sidebar without fetching every message.
    """
    return await db_manager_service.list_user_chats(user_id)


@router.post(
    "/getchathistory",
    summary="Fetch chat history for a session",
    response_model=List[Dict[str, str]],
)
async def get_chat_history(
    request: ChatHistoryRequest,
    user_id: str = Depends(get_current_user_id),
):
    """
    Return the last 100 messages (oldest → newest) for the given session.
    """
    return await db_manager_service.get_user_chat_history(user_id, request.session_id)


@router.post(
    "/deletechat",
    summary="Delete a chat session",
    response_model=Dict[str, str],
)
async def delete_chat(
    request: DeleteChatRequest,
    user_id: str = Depends(get_current_user_id),
):
    """
    Delete a chat session and all its history.
    """
    return await db_manager_service.delete_user_chat(user_id, request.session_id)

@router.post(
    "/renamechat",
    summary="Rename a chat session",
    response_model=Dict[str, str],
)
async def rename_chat(
    request: RenameChatRequest,
    user_id: str = Depends(get_current_user_id),
):
    """
    Rename a chat session.
    """
    return await db_manager_service.rename_user_chat(user_id, request.session_id, request.title)
