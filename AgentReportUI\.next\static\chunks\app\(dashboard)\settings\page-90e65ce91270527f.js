(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[289],{15582:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>z});var t=a(95155),r=a(12115),l=a(74677),d=a(74045),i=a(56543),n=a(87914),c=a(30285),o=a(66695),m=a(62523),x=a(85057),u=a(46102),h=a(47262),p=a(381),b=a(40646),j=a(54416),g=a(1243),f=a(75525),N=a(69803),w=a(78749),v=a(92657),k=a(53904),y=a(32919),P=a(74575),C=a(14738),A=a(62098),S=a(93509),E=a(28883),D=a(4229),T=a(54213),F=a(91788),O=a(62525);let Z=()=>{let{user:e,logout:s}=(0,d.A)(),{theme:a,setTheme:l}=(0,i.D)(),Z=(0,r.useMemo)(()=>({title:"Settings",icon:p.A}),[]);(0,n.H)(Z);let[$,z]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[B,J]=(0,r.useState)({marketingEmails:!0,securityAlerts:!0,productUpdates:!1,weeklyDigest:!0}),[L,R]=(0,r.useState)({profileVisibility:"private",dataCollection:!0,analyticsOptOut:!1,thirdPartySharing:!1}),[_,U]=(0,r.useState)(!1),[W,G]=(0,r.useState)(!1),[H,I]=(0,r.useState)(!1),[M,q]=(0,r.useState)(!1),[K,V]=(0,r.useState)(!1),[Q,X]=(0,r.useState)(!1),[Y,ee]=(0,r.useState)(!1),[es,ea]=(0,r.useState)(""),[et,er]=(0,r.useState)(!1),[el,ed]=(0,r.useState)(null),[ei,en]=(0,r.useState)(null),[ec,eo]=(0,r.useState)({}),em=(0,r.useCallback)(()=>{let e={};return $.currentPassword||(e.currentPassword="Current password is required"),$.newPassword?$.newPassword.length<8?e.newPassword="Password must be at least 8 characters":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test($.newPassword)||(e.newPassword="Password must contain uppercase, lowercase, and number"):e.newPassword="New password is required",$.confirmPassword?$.newPassword!==$.confirmPassword&&(e.confirmPassword="Passwords do not match"):e.confirmPassword="Please confirm your new password",eo(e),0===Object.keys(e).length},[$]),ex=(0,r.useCallback)((e,s)=>{z(a=>({...a,[e]:s})),ec[e]&&eo(s=>({...s,[e]:""}))},[ec]),eu=(0,r.useCallback)(async()=>{if(em()){q(!0),ed(null);try{await new Promise(e=>setTimeout(e,1500)),z({currentPassword:"",newPassword:"",confirmPassword:""}),en("Password changed successfully!"),setTimeout(()=>en(null),3e3)}catch(e){console.error("Failed to change password:",e),ed("Failed to change password. Please check your current password and try again.")}finally{q(!1)}}},[$,em]),eh=(0,r.useCallback)(async()=>{V(!0),ed(null);try{await new Promise(e=>setTimeout(e,1e3)),en("Email preferences saved successfully!"),setTimeout(()=>en(null),3e3)}catch(e){console.error("Failed to save email preferences:",e),ed("Failed to save email preferences. Please try again.")}finally{V(!1)}},[B]),ep=(0,r.useCallback)(async()=>{V(!0),ed(null);try{await new Promise(e=>setTimeout(e,1e3)),en("Privacy settings saved successfully!"),setTimeout(()=>en(null),3e3)}catch(e){console.error("Failed to save privacy settings:",e),ed("Failed to save privacy settings. Please try again.")}finally{V(!1)}},[L]),eb=(0,r.useCallback)(async()=>{X(!0),ed(null);try{await new Promise(e=>setTimeout(e,2e3));let e=document.createElement("a");e.href="data:text/json;charset=utf-8,"+encodeURIComponent(JSON.stringify({message:"This would be your actual data export",timestamp:new Date().toISOString()})),e.download="user-data-export-".concat(Date.now(),".json"),e.click(),en("Data export completed successfully!"),setTimeout(()=>en(null),3e3)}catch(e){console.error("Failed to export data:",e),ed("Failed to export data. Please try again.")}finally{X(!1)}},[]),ej=(0,r.useCallback)(async()=>{if("DELETE"!==es)return void ed('Please type "DELETE" to confirm account deletion');er(!0),ed(null);try{await new Promise(e=>setTimeout(e,2e3)),await s()}catch(e){console.error("Failed to delete account:",e),ed("Failed to delete account. Please try again.")}finally{er(!1)}},[es,s]),eg=(0,r.useCallback)(()=>{ed(null),en(null)},[]);return(0,t.jsx)(u.Bc,{children:(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,t.jsx)("div",{className:"container mx-auto p-4 sm:p-6 lg:p-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[ei&&(0,t.jsx)("div",{className:"p-4 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(b.A,{className:"h-5 w-5 text-green-600 dark:text-green-400 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-green-800 dark:text-green-200",children:[(0,t.jsx)("div",{className:"font-medium",children:"Success"}),(0,t.jsx)("div",{className:"text-sm",children:ei})]})]}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:eg,children:(0,t.jsx)(j.A,{className:"h-4 w-4"})})]})}),el&&(0,t.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-red-800 dark:text-red-200",children:[(0,t.jsx)("div",{className:"font-medium",children:"Error"}),(0,t.jsx)("div",{className:"text-sm",children:el})]})]}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:eg,children:(0,t.jsx)(j.A,{className:"h-4 w-4"})})]})}),(0,t.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Account Security"]}),(0,t.jsx)(o.BT,{className:"text-slate-600 dark:text-slate-400",children:"Manage your password and security settings"})]}),(0,t.jsxs)(o.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-medium text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4"}),"Change Password"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.p,{id:"currentPassword",type:_?"text":"password",value:$.currentPassword,onChange:e=>ex("currentPassword",e.target.value),placeholder:"Enter current password",className:ec.currentPassword?"border-red-300 dark:border-red-700":""}),(0,t.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>U(!_),children:_?(0,t.jsx)(w.A,{className:"h-4 w-4"}):(0,t.jsx)(v.A,{className:"h-4 w-4"})})]}),ec.currentPassword&&(0,t.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:ec.currentPassword})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"newPassword",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.p,{id:"newPassword",type:W?"text":"password",value:$.newPassword,onChange:e=>ex("newPassword",e.target.value),placeholder:"Enter new password",className:ec.newPassword?"border-red-300 dark:border-red-700":""}),(0,t.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>G(!W),children:W?(0,t.jsx)(w.A,{className:"h-4 w-4"}):(0,t.jsx)(v.A,{className:"h-4 w-4"})})]}),ec.newPassword&&(0,t.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:ec.newPassword})]}),(0,t.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,t.jsx)(x.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.p,{id:"confirmPassword",type:H?"text":"password",value:$.confirmPassword,onChange:e=>ex("confirmPassword",e.target.value),placeholder:"Confirm new password",className:ec.confirmPassword?"border-red-300 dark:border-red-700":""}),(0,t.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>I(!H),children:H?(0,t.jsx)(w.A,{className:"h-4 w-4"}):(0,t.jsx)(v.A,{className:"h-4 w-4"})})]}),ec.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:ec.confirmPassword})]})]}),(0,t.jsxs)(c.$,{onClick:eu,disabled:M,className:"bg-blue-600 hover:bg-blue-700",children:[M?(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),M?"Changing Password...":"Change Password"]})]}),(0,t.jsxs)("div",{className:"border-t border-slate-200 dark:border-slate-700 pt-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-medium text-slate-900 dark:text-slate-100 flex items-center gap-2 mb-4",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),"Connected Accounts"]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-red-600 dark:text-red-400 text-sm font-bold",children:"G"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"Google"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Connect with Google OAuth"})]})]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Connect"]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-100 dark:bg-gray-900 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400 text-sm font-bold",children:"GH"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"GitHub"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Connect with GitHub OAuth"})]})]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Connect"]})]})]})]})]})]}),(0,t.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,t.jsx)(C.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Theme Preferences"]}),(0,t.jsx)(o.BT,{className:"text-slate-600 dark:text-slate-400",children:"Choose your preferred theme and appearance"})]}),(0,t.jsx)(o.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{className:"p-4 border-2 rounded-lg cursor-pointer transition-all ".concat("light"===a?"border-blue-500 bg-blue-50 dark:bg-blue-950":"border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600"),onClick:()=>l("light"),children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(A.A,{className:"h-5 w-5 text-yellow-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"Light"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Light theme"})]})]})}),(0,t.jsx)("div",{className:"p-4 border-2 rounded-lg cursor-pointer transition-all ".concat("dark"===a?"border-blue-500 bg-blue-50 dark:bg-blue-950":"border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600"),onClick:()=>l("dark"),children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(S.A,{className:"h-5 w-5 text-slate-600 dark:text-slate-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"Dark"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Dark theme"})]})]})}),(0,t.jsx)("div",{className:"p-4 border-2 rounded-lg cursor-pointer transition-all ".concat("system"===a?"border-blue-500 bg-blue-50 dark:bg-blue-950":"border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600"),onClick:()=>l("system"),children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(C.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"System"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Follow system"})]})]})})]})})]}),(0,t.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,t.jsx)(E.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Email Preferences"]}),(0,t.jsx)(o.BT,{className:"text-slate-600 dark:text-slate-400",children:"Control what emails you receive from us"})]}),(0,t.jsxs)(o.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(h.S,{id:"marketingEmails",checked:B.marketingEmails,onCheckedChange:e=>J(s=>({...s,marketingEmails:!!e}))}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(x.J,{htmlFor:"marketingEmails",className:"text-sm font-medium cursor-pointer",children:"Marketing emails"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Receive updates about new features and promotions"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(h.S,{id:"securityAlerts",checked:B.securityAlerts,onCheckedChange:e=>J(s=>({...s,securityAlerts:!!e}))}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(x.J,{htmlFor:"securityAlerts",className:"text-sm font-medium cursor-pointer",children:"Security alerts"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Important notifications about your account security"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(h.S,{id:"productUpdates",checked:B.productUpdates,onCheckedChange:e=>J(s=>({...s,productUpdates:!!e}))}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(x.J,{htmlFor:"productUpdates",className:"text-sm font-medium cursor-pointer",children:"Product updates"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"News about product improvements and new releases"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(h.S,{id:"weeklyDigest",checked:B.weeklyDigest,onCheckedChange:e=>J(s=>({...s,weeklyDigest:!!e}))}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(x.J,{htmlFor:"weeklyDigest",className:"text-sm font-medium cursor-pointer",children:"Weekly digest"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Summary of your activity and insights"})]})]})]}),(0,t.jsxs)(c.$,{onClick:eh,disabled:K,className:"bg-blue-600 hover:bg-blue-700",children:[K?(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(D.A,{className:"h-4 w-4 mr-2"}),K?"Saving...":"Save Preferences"]})]})]}),(0,t.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,t.jsx)(T.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Privacy & Data"]}),(0,t.jsx)(o.BT,{className:"text-slate-600 dark:text-slate-400",children:"Manage your data and privacy settings"})]}),(0,t.jsxs)(o.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-slate-900 dark:text-slate-100",children:"Privacy Settings"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(h.S,{id:"dataCollection",checked:L.dataCollection,onCheckedChange:e=>R(s=>({...s,dataCollection:!!e}))}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(x.J,{htmlFor:"dataCollection",className:"text-sm font-medium cursor-pointer",children:"Allow data collection for service improvement"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Help us improve our services by collecting anonymous usage data"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(h.S,{id:"analyticsOptOut",checked:L.analyticsOptOut,onCheckedChange:e=>R(s=>({...s,analyticsOptOut:!!e}))}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(x.J,{htmlFor:"analyticsOptOut",className:"text-sm font-medium cursor-pointer",children:"Opt out of analytics tracking"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Disable analytics and tracking cookies"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(h.S,{id:"thirdPartySharing",checked:L.thirdPartySharing,onCheckedChange:e=>R(s=>({...s,thirdPartySharing:!!e}))}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(x.J,{htmlFor:"thirdPartySharing",className:"text-sm font-medium cursor-pointer",children:"Allow third-party data sharing"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Share anonymized data with trusted partners for research"})]})]})]}),(0,t.jsxs)(c.$,{onClick:ep,disabled:K,className:"bg-blue-600 hover:bg-blue-700",children:[K?(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(D.A,{className:"h-4 w-4 mr-2"}),K?"Saving...":"Save Privacy Settings"]})]}),(0,t.jsxs)("div",{className:"border-t border-slate-200 dark:border-slate-700 pt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-slate-900 dark:text-slate-100 mb-4",children:"Data Management"}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"Export your data"}),(0,t.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Download a copy of all your account data"})]}),(0,t.jsxs)(c.$,{onClick:eb,disabled:Q,variant:"outline",children:[Q?(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(F.A,{className:"h-4 w-4 mr-2"}),Q?"Exporting...":"Export Data"]})]})})]})]})]}),(0,t.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-red-200 dark:border-red-800",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"text-xl font-semibold text-red-900 dark:text-red-100 flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 text-red-600 dark:text-red-400"}),"Danger Zone"]}),(0,t.jsx)(o.BT,{className:"text-red-700 dark:text-red-300",children:"Irreversible actions that will permanently affect your account"})]}),(0,t.jsx)(o.Wu,{children:Y?(0,t.jsxs)("div",{className:"p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950 space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-red-900 dark:text-red-100 mb-2",children:"Are you absolutely sure?"}),(0,t.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300 mb-4",children:"This action cannot be undone. This will permanently delete your account and remove all of your data from our servers."}),(0,t.jsxs)(x.J,{htmlFor:"deleteConfirmation",className:"text-sm font-medium text-red-900 dark:text-red-100",children:["Type ",(0,t.jsx)("strong",{children:"DELETE"})," to confirm:"]}),(0,t.jsx)(m.p,{id:"deleteConfirmation",value:es,onChange:e=>ea(e.target.value),placeholder:"DELETE",className:"mt-2 border-red-300 dark:border-red-700"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)(c.$,{onClick:()=>{ee(!1),ea("")},variant:"outline",disabled:et,children:"Cancel"}),(0,t.jsxs)(c.$,{onClick:ej,disabled:et||"DELETE"!==es,variant:"destructive",className:"bg-red-600 hover:bg-red-700",children:[et?(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(O.A,{className:"h-4 w-4 mr-2"}),et?"Deleting Account...":"Delete Account"]})]})]}):(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-red-900 dark:text-red-100",children:"Delete Account"}),(0,t.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:"Permanently delete your account and all associated data"})]}),(0,t.jsxs)(c.$,{onClick:()=>ee(!0),variant:"destructive",className:"bg-red-600 hover:bg-red-700",children:[(0,t.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"Delete Account"]})]})})]})]})})})})};var $=a(35695);function z(){let{isAuthenticated:e,isLoading:s}=(0,d.A)(),a=(0,$.useRouter)();return(r.useEffect(()=>{s||e||a.push("/login")},[s,e,a]),s||!e)?(0,t.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-slate-600 dark:text-slate-400",children:"Loading..."})]})}):(0,t.jsx)(l.A,{children:(0,t.jsx)(Z,{})})}},23793:(e,s,a)=>{Promise.resolve().then(a.bind(a,15582))},46102:(e,s,a)=>{"use strict";a.d(s,{Bc:()=>i,ZI:()=>o,k$:()=>c,m_:()=>n});var t=a(95155),r=a(12115),l=a(89613),d=a(46486);let i=l.Kq,n=l.bL,c=l.l9,o=r.forwardRef((e,s)=>{let{className:a,sideOffset:r=4,...i}=e;return(0,t.jsx)(l.UC,{ref:s,sideOffset:r,className:(0,d.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...i})});o.displayName=l.UC.displayName},47262:(e,s,a)=>{"use strict";a.d(s,{S:()=>i});var t=a(95155);a(12115);var r=a(76981),l=a(5196),d=a(46486);function i(e){let{className:s,...a}=e;return(0,t.jsx)(r.bL,{"data-slot":"checkbox",className:(0,d.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...a,children:(0,t.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(l.A,{className:"size-3.5"})})})}},62523:(e,s,a)=>{"use strict";a.d(s,{p:()=>l});var t=a(95155);a(12115);var r=a(46486);function l(e){let{className:s,type:a,...l}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...l})}},66695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>n,Wu:()=>c,ZB:()=>i,Zp:()=>l,aR:()=>d});var t=a(95155);a(12115);var r=a(46486);function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold text-white",s),...a})}function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...a})}},85057:(e,s,a)=>{"use strict";a.d(s,{J:()=>d});var t=a(95155);a(12115);var r=a(40968),l=a(46486);function d(e){let{className:s,...a}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}}},e=>{var s=s=>e(e.s=s);e.O(0,[464,817,874,786,826,613,28,189,45,179,30,441,684,358],()=>s(23793)),_N_E=e.O()}]);