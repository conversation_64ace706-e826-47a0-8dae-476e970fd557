(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[678],{13815:(e,r,a)=>{Promise.resolve().then(a.bind(a,50685))},28883:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},30285:(e,r,a)=>{"use strict";a.d(r,{$:()=>n});var t=a(95155),s=a(12115),d=a(99708),o=a(74466),l=a(46486);let i=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=s.forwardRef((e,r)=>{let{className:a,variant:s,size:o,asChild:n=!1,...c}=e,u=n?d.DX:"button";return(0,t.jsx)(u,{className:(0,l.cn)(i({variant:s,size:o,className:a})),ref:r,...c})});n.displayName="Button"},32919:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},46486:(e,r,a)=>{"use strict";a.d(r,{cn:()=>d});var t=a(52596),s=a(39688);function d(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,s.QP)((0,t.$)(r))}},50685:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>m});var t=a(95155),s=a(12115),d=a(62523),o=a(30285),l=a(74045),i=a(71007),n=a(28883),c=a(32919),u=a(78749),b=a(92657),x=a(6874),h=a.n(x);let g=()=>{let[e,r]=(0,s.useState)(""),[a,x]=(0,s.useState)(""),[g,m]=(0,s.useState)(""),[y,p]=(0,s.useState)(""),[f,v]=(0,s.useState)(!1),[k,w]=(0,s.useState)(!1),[j,N]=(0,s.useState)(null),[A,E]=(0,s.useState)(null),[S,_]=(0,s.useState)(!1),{register:L}=(0,l.A)(),R=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),C=e=>/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(e),z=()=>g.trim()?e.trim()?R(e)?a?C(a)?a===y||(N("Passwords do not match"),!1):(N("Password must be at least 8 characters with uppercase, lowercase, number, and special character"),!1):(N("Password is required"),!1):(N("Please enter a valid email address"),!1):(N("Email is required"),!1):(N("Full name is required"),!1),P=async r=>{if(r.preventDefault(),N(null),E(null),z()){_(!0);try{await L({email:e.trim(),password:a,full_name:g.trim()})}catch(e){var t,s;if(console.error("Registration failed",e),null==(s=e.response)||null==(t=s.data)?void 0:t.error){let r=e.response.data.error;"EMAIL_ALREADY_EXISTS"===r.code?(N(r.message||"An account with this email address already exists. Please use a different email or try logging in."),E("EMAIL_ALREADY_EXISTS"),setTimeout(()=>{let e=document.getElementById("email");e&&(e.focus(),e.select())},100)):(N(r.message||"Registration failed. Please try again."),E("GENERAL_ERROR"))}else e.message?N(e.message):N("Registration failed. Please try again."),E("GENERAL_ERROR")}_(!1)}};return(0,t.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-5",children:[(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10",children:(0,t.jsx)(i.A,{className:"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200"})}),(0,t.jsx)(d.p,{id:"fullName",type:"text",value:g,onChange:e=>m(e.target.value),placeholder:"Full name",className:"w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500",required:!0,disabled:S})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10",children:(0,t.jsx)(n.A,{className:"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200"})}),(0,t.jsx)(d.p,{id:"email",type:"email",value:e,onChange:e=>r(e.target.value),placeholder:"Email address",className:"w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500",required:!0,disabled:S})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10",children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200"})}),(0,t.jsx)(d.p,{id:"password",type:f?"text":"password",value:a,onChange:e=>x(e.target.value),placeholder:"Password",className:"w-full pl-12 pr-14 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500",required:!0,disabled:S}),(0,t.jsx)("button",{type:"button",onClick:()=>v(!f),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100 transition-colors duration-200 z-10 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700",disabled:S,children:f?(0,t.jsx)(u.A,{className:"h-5 w-5"}):(0,t.jsx)(b.A,{className:"h-5 w-5"})})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10",children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200"})}),(0,t.jsx)(d.p,{id:"confirmPassword",type:k?"text":"password",value:y,onChange:e=>p(e.target.value),placeholder:"Confirm password",className:"w-full pl-12 pr-14 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500",required:!0,disabled:S}),(0,t.jsx)("button",{type:"button",onClick:()=>w(!k),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100 transition-colors duration-200 z-10 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700",disabled:S,children:k?(0,t.jsx)(u.A,{className:"h-5 w-5"}):(0,t.jsx)(b.A,{className:"h-5 w-5"})})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/30 border-2 border-blue-200 dark:border-blue-500/50 rounded-2xl p-4 shadow-sm",children:[(0,t.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300 font-medium mb-2",children:"Password requirements:"}),(0,t.jsxs)("ul",{className:"text-xs text-blue-600 dark:text-blue-400 space-y-1",children:[(0,t.jsx)("li",{children:"• At least 8 characters long"}),(0,t.jsx)("li",{children:"• Contains uppercase and lowercase letters"}),(0,t.jsx)("li",{children:"• Contains at least one number"}),(0,t.jsx)("li",{children:"• Contains at least one special character (@$!%*?&)"})]})]}),j&&(0,t.jsxs)("div",{className:"border-2 rounded-2xl p-4 animate-in slide-in-from-top-2 duration-300 shadow-sm ".concat("EMAIL_ALREADY_EXISTS"===A?"bg-amber-50 dark:bg-amber-900/30 border-amber-200 dark:border-amber-500/50":"bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-500/50"),children:[(0,t.jsx)("p",{className:"text-sm text-center font-medium ".concat("EMAIL_ALREADY_EXISTS"===A?"text-amber-700 dark:text-amber-300":"text-red-700 dark:text-red-300"),children:j}),"EMAIL_ALREADY_EXISTS"===A&&(0,t.jsx)("div",{className:"mt-3 text-center",children:(0,t.jsx)(h(),{href:"/login",className:"inline-flex items-center px-4 py-2 text-sm font-medium text-amber-700 dark:text-amber-300 bg-amber-100 dark:bg-amber-800/50 border border-amber-300 dark:border-amber-600 rounded-lg hover:bg-amber-200 dark:hover:bg-amber-800/70 transition-colors duration-200",children:"Go to Login Page"})})]}),(0,t.jsx)(o.$,{type:"submit",className:"w-full bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-500 dark:to-blue-600 hover:from-blue-700 hover:to-blue-800 dark:hover:from-blue-600 dark:hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg shadow-blue-500/30 dark:shadow-blue-400/20 hover:shadow-blue-500/50 dark:hover:shadow-blue-400/30 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:shadow-blue-500/25 text-base border-0",disabled:S,children:S?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-3"}),"Creating account..."]}):"Create account"}),(0,t.jsx)("div",{className:"text-center pt-4",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Already have an account?"," ",(0,t.jsx)(h(),{href:"/login",className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 font-semibold hover:underline",children:"Sign in"})]})})]})},m=()=>(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,t.jsx)("div",{className:"absolute top-0 left-0 p-6 z-20",children:(0,t.jsxs)(h(),{href:"/",className:"flex items-center space-x-2 group",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-blue-500 group-hover:bg-blue-600 transition-colors duration-200 shadow-sm",children:(0,t.jsx)("svg",{viewBox:"0 0 24 24",fill:"none",className:"w-5 h-5 text-white",children:(0,t.jsx)("path",{d:"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,t.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-500 transition-colors duration-200",children:"Agent"})]})}),(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center p-6",children:(0,t.jsxs)("div",{className:"w-full max-w-md",children:[(0,t.jsxs)("div",{className:"backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 rounded-3xl border border-gray-200/50 dark:border-gray-700/50 shadow-xl",children:[(0,t.jsxs)("div",{className:"p-8 pb-0 text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-light text-gray-900 dark:text-white mb-2 tracking-tight",children:"Create your account"}),(0,t.jsx)("p",{className:"text-gray-500 dark:text-gray-400 font-light",children:"Join us and start your journey"})]}),(0,t.jsx)("div",{className:"p-8",children:(0,t.jsx)(g,{})})]}),(0,t.jsx)("div",{className:"text-center mt-8",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["By creating an account, you agree to our"," ",(0,t.jsx)("a",{href:"#",className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 hover:underline",children:"Terms of Service"})," ","and"," ",(0,t.jsx)("a",{href:"#",className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 hover:underline",children:"Privacy Policy"})]})})]})})]})},62523:(e,r,a)=>{"use strict";a.d(r,{p:()=>d});var t=a(95155);a(12115);var s=a(46486);function d(e){let{className:r,type:a,...d}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...d})}},71007:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},78749:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},92657:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[464,817,874,189,45,441,684,358],()=>r(13815)),_N_E=e.O()}]);