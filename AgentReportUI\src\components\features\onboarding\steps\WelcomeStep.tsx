"use client";

import React from 'react';
import { OnboardingStepProps } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Sparkles, Database, MessageSquare, BarChart3 } from 'lucide-react';

const features = [
  {
    icon: Database,
    title: 'Connect Your Data',
    description: 'Easily connect to multiple databases and data sources',
  },
  {
    icon: MessageSquare,
    title: 'AI-Powered Chat',
    description: 'Ask questions about your data in natural language',
  },
  {
    icon: BarChart3,
    title: 'Generate Reports',
    description: 'Create beautiful reports and visualizations automatically',
  },
];

export default function WelcomeStep({ onNext, isLoading }: OnboardingStepProps) {
  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="text-center pb-6">
        <div className="mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit">
          <Sparkles className="h-8 w-8 text-blue-600 dark:text-blue-400" />
        </div>
        <CardTitle className="text-3xl font-bold text-slate-900 dark:text-slate-100">
          Welcome to Agent Platform!
        </CardTitle>
        <CardDescription className="text-lg text-slate-600 dark:text-slate-400 mt-2">
          Your intelligent data analysis companion is ready to help you unlock insights from your data.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Features grid */}
        <div className="grid gap-4 md:grid-cols-3">
          {features.map((feature, index) => (
            <div
              key={index}
              className="text-center p-4 rounded-lg bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700"
            >
              <div className="mx-auto mb-3 p-2 bg-white dark:bg-slate-700 rounded-lg w-fit">
                <feature.icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="font-semibold text-slate-900 dark:text-slate-100 mb-1">
                {feature.title}
              </h3>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Getting started message */}
        <div className="text-center py-6">
          <p className="text-slate-600 dark:text-slate-400 mb-6">
            Let's get you set up with a quick tour and personalize your experience.
            This will only take a few minutes!
          </p>

          <Button
            onClick={onNext}
            disabled={isLoading}
            size="lg"
            className="px-8 py-3 text-lg"
          >
            Get Started
          </Button>
        </div>

        {/* Additional info */}
        <div className="text-center text-sm text-slate-500 dark:text-slate-400">
          <p>
            You can always change these settings later in your profile.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
