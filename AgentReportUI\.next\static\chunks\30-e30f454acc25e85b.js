"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[30],{30285:(e,r,t)=>{t.d(r,{$:()=>c});var a=t(95155),s=t(12115),o=t(99708),n=t(74466),l=t(46486);let i=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,r)=>{let{className:t,variant:s,size:n,asChild:c=!1,...d}=e,u=c?o.DX:"button";return(0,a.jsx)(u,{className:(0,l.cn)(i({variant:s,size:n,className:t})),ref:r,...d})});c.displayName="Button"},44838:(e,r,t)=>{t.d(r,{SQ:()=>i,_2:()=>c,rI:()=>n,ty:()=>l});var a=t(95155);t(12115);var s=t(9449),o=t(46486);function n(e){let{...r}=e;return(0,a.jsx)(s.bL,{"data-slot":"dropdown-menu",...r})}function l(e){let{...r}=e;return(0,a.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...r})}function i(e){let{className:r,sideOffset:t=4,...n}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",r),...n})})}function c(e){let{className:r,inset:t,variant:n="default",...l}=e;return(0,a.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground text-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",r),...l})}},46486:(e,r,t)=>{t.d(r,{cn:()=>o});var a=t(52596),s=t(39688);function o(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},74677:(e,r,t)=>{t.d(r,{A:()=>Q});var a=t(95155),s=t(12115),o=t(35695),n=t(10871),l=t(10071),i=t(30285),c=t(15452);function d(e){let{...r}=e;return(0,a.jsx)(c.bL,{"data-slot":"sheet",...r})}function u(e){let{...r}=e;return(0,a.jsx)(c.l9,{"data-slot":"sheet-trigger",...r})}var m=t(73783),x=t(49376),g=t(74783),b=t(13052),h=t(84616),v=t(91788),f=t(81497),p=t(74045),y=t(56543),C=t(46486);let j=s.forwardRef((e,r)=>{let{...t}=e;return(0,a.jsx)("nav",{ref:r,"aria-label":"breadcrumb",...t})});j.displayName="Breadcrumb";let k=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("ol",{ref:r,className:(0,C.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t),...s})});k.displayName="BreadcrumbList";let w=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("li",{ref:r,className:(0,C.cn)("inline-flex items-center gap-1.5",t),...s})});w.displayName="BreadcrumbItem";let N=s.forwardRef((e,r)=>{let{asChild:t,className:o,...n}=e;return t?(0,a.jsx)(s.Fragment,{...n}):(0,a.jsx)("a",{ref:r,className:(0,C.cn)("transition-colors hover:text-foreground",o),...n})});N.displayName="BreadcrumbLink";let M=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("span",{ref:r,role:"link","aria-disabled":"true","aria-current":"page",className:(0,C.cn)("font-normal text-foreground",t),...s})});M.displayName="BreadcrumbPage";let T=e=>{let{children:r,className:t,...s}=e;return(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,C.cn)("[&>svg]:size-3.5",t),...s,children:null!=r?r:(0,a.jsx)(b.A,{})})};T.displayName="BreadcrumbSeparator";let L=()=>{let{isAuthenticated:e}=(0,p.A)(),{theme:r,setTheme:t}=(0,y.D)(),{pageInfo:n,actions:c}=(0,l.s)(),C=(0,o.usePathname)(),L=n.breadcrumbs&&n.breadcrumbs.length>1,{onCreateChart:A,chartCount:E=0,maxCharts:S=12,onCreateAnalysis:z,isCreatingAnalysis:_=!1,onCreateDashboard:D,onExport:P,onToggleChat:R,isChatOpen:X=!1}=c,B=E<S,$="/dashboard"===C,Q="/ai-workflows"===C,W=$&&A,q=$&&D,I=Q&&z,V=Q&&(P||R),O="/dashboard"===C?m.A:"/ai-workflows"===C?x.A:n.icon||m.A;return(0,a.jsxs)("header",{className:"flex items-center justify-between whitespace-nowrap border-b border-solid border-sidebar-border bg-sidebar-bg px-4 py-2 h-12",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 text-sidebar-text-primary",children:[e&&(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)(d,{children:(0,a.jsx)(u,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"outline",size:"icon",className:"h-7 w-7",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})})})}),L?(0,a.jsx)(j,{children:(0,a.jsx)(k,{children:n.breadcrumbs.map((e,r)=>(0,a.jsxs)(s.Fragment,{children:[(0,a.jsx)(w,{children:0===r?e.onClick||e.href?(0,a.jsxs)(N,{...e.href?{href:e.href}:{},...e.onClick?{onClick:e.onClick}:{},className:"flex items-center space-x-1.5 cursor-pointer hover:text-primary text-sm font-medium",children:[(0,a.jsx)(O,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.label})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-1.5 text-sm font-medium",children:[(0,a.jsx)(O,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.label})]}):r===n.breadcrumbs.length-1?(0,a.jsx)(M,{className:"font-medium text-sm",children:e.label}):(0,a.jsx)(N,{...e.href?{href:e.href}:{},...e.onClick?{onClick:e.onClick}:{},className:"cursor-pointer hover:text-primary text-sm font-medium",children:e.label})}),r<n.breadcrumbs.length-1&&(0,a.jsx)(T,{children:(0,a.jsx)(b.A,{className:"h-3 w-3"})})]},e.label))})}):(0,a.jsx)("h1",{className:"text-sm font-medium leading-tight tracking-[-0.015em] text-sidebar-text-primary",children:n.title})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[W&&(0,a.jsxs)(i.$,{onClick:A,disabled:!B,className:"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs",style:{backgroundColor:"var(--surface-selected)",color:"var(--sidebar-text-primary)"},title:B?void 0:"Maximum of ".concat(S," charts allowed"),onMouseEnter:e=>{B&&(e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)")},onMouseLeave:e=>{B&&(e.currentTarget.style.backgroundColor="var(--surface-selected)")},children:[(0,a.jsx)(h.A,{className:"h-3 w-3 mr-1.5"}),"Chart"]}),q&&(0,a.jsxs)(i.$,{onClick:D,className:"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs",style:{backgroundColor:"var(--surface-selected)",color:"var(--sidebar-text-primary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="var(--surface-selected)"},children:[(0,a.jsx)(h.A,{className:"h-3 w-3 mr-1.5"}),"Dashboard"]}),I&&(0,a.jsxs)(i.$,{onClick:z,disabled:_,className:"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs",style:{backgroundColor:"var(--surface-selected)",color:"var(--sidebar-text-primary)"},title:_?"Analysis is being created":void 0,onMouseEnter:e=>{_||(e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)")},onMouseLeave:e=>{_||(e.currentTarget.style.backgroundColor="var(--surface-selected)")},children:[(0,a.jsx)(h.A,{className:"h-3 w-3 mr-1.5"}),_?"Creating...":"Analysis"]}),V&&(0,a.jsxs)(a.Fragment,{children:[P&&(0,a.jsxs)(i.$,{onClick:P,className:"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs",style:{backgroundColor:"var(--surface-selected)",color:"var(--sidebar-text-primary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="var(--surface-selected)"},children:[(0,a.jsx)(v.A,{className:"h-3 w-3 mr-1.5"}),"Export"]}),R&&(0,a.jsxs)(i.$,{onClick:R,className:"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs",style:{backgroundColor:X?"var(--interactive-bg-secondary-hover)":"var(--surface-selected)",color:"var(--sidebar-text-primary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor=X?"var(--interactive-bg-secondary-hover)":"var(--surface-selected)"},children:[(0,a.jsx)(f.A,{className:"h-3 w-3 mr-1.5"}),"Chat"]})]})]})]})};var A=t(6874),E=t.n(A),S=t(72713),z=t(54213),_=t(44020),D=t(38857),P=t(71007),R=t(381),X=t(34835),B=t(44838);let $=e=>{let{onNewChat:r,onToggleCollapse:t,isCreatingNewChat:l=!1}=e,{chatHistory:c,isLoadingChats:d,deleteChat:u,renameChat:g}=(0,n.m)(),{logout:b,user:v}=(0,p.A)(),f=(0,o.usePathname)(),y=(0,o.useRouter)(),[C,j]=(0,s.useState)(null),[k,w]=(0,s.useState)(null),[N,M]=(0,s.useState)(""),[T,L]=(0,s.useState)(!1);console.log("Sidebar component rendered. Current pathname:",f);let A=(null==f?void 0:f.split("/").includes("chat"))?f.split("/").pop():null;console.log("Current chatId extracted from pathname:",A);let $=(e,r)=>{console.log("Renaming chat with id:",e,"and current title:",r),w(e),M(r),j(null)},Q=e=>{console.log("Submitting rename for chat with id:",e,"and new title:",N),N.trim()&&g(e,N.trim()),w(null),M("")},W=async e=>{try{if(await u(e),j(null),A===e){let t=c.filter(r=>r.id!==e);t.length>0?y.push("/chat/".concat(t[0].id)):(r(),y.push("/chat"))}}catch(e){console.error("Failed to delete chat:",e)}};return(0,a.jsxs)("aside",{className:"hidden lg:flex flex-col ".concat(T?"w-16":"w-sidebar"," h-full min-h-0 transition-all duration-300 flex-shrink-0 relative overflow-y-auto"),style:{backgroundColor:"var(--sidebar-bg)",borderRight:"1px solid var(--sidebar-border)",scrollbarColor:"var(--sidebar-surface-tertiary) transparent"},children:[(0,a.jsx)("div",{className:"sticky top-0 z-10 p-2 transition-all duration-300 ease-in-out ".concat(T?"flex justify-center":"flex justify-end"),style:{backgroundColor:"var(--sidebar-bg)"},children:(0,a.jsx)(i.$,{variant:"ghost",size:"icon",className:"w-10 h-10 flex items-center justify-center rounded-lg border-0 transition-all duration-300 ease-in-out transform-gpu",style:{color:"var(--sidebar-icon) !important",backgroundColor:"transparent !important"},onMouseEnter:e=>{e.currentTarget.style.setProperty("background-color","var(--interactive-bg-secondary-hover)","important"),e.currentTarget.style.transform="scale(1.05)"},onMouseLeave:e=>{e.currentTarget.style.setProperty("background-color","transparent","important"),e.currentTarget.style.transform="scale(1)"},onClick:()=>{let e=!T;L(e),null==t||t(e)},children:(0,a.jsx)("svg",{className:"h-5 w-5 transition-transform duration-300 ease-in-out",viewBox:"0 0 20 20",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",style:{transform:T?"scaleX(-1)":"scaleX(1)"},children:(0,a.jsx)("path",{d:"M6.83496 3.99992C6.38353 4.00411 6.01421 4.0122 5.69824 4.03801C5.31232 4.06954 5.03904 4.12266 4.82227 4.20012L4.62207 4.28606C4.18264 4.50996 3.81498 4.85035 3.55859 5.26848L3.45605 5.45207C3.33013 5.69922 3.25006 6.01354 3.20801 6.52824C3.16533 7.05065 3.16504 7.71885 3.16504 8.66301V11.3271C3.16504 12.2712 3.16533 12.9394 3.20801 13.4618C3.25006 13.9766 3.33013 14.2909 3.45605 14.538L3.55859 14.7216C3.81498 15.1397 4.18266 15.4801 4.62207 15.704L4.82227 15.79C5.03904 15.8674 5.31234 15.9205 5.69824 15.9521C6.01398 15.9779 6.383 15.986 6.83398 15.9902L6.83496 3.99992ZM18.165 11.3271C18.165 12.2493 18.1653 12.9811 18.1172 13.5702C18.0745 14.0924 17.9916 14.5472 17.8125 14.9648L17.7295 15.1415C17.394 15.8 16.8834 16.3511 16.2568 16.7353L15.9814 16.8896C15.5157 17.1268 15.0069 17.2285 14.4102 17.2773C13.821 17.3254 13.0893 17.3251 12.167 17.3251H7.83301C6.91071 17.3251 6.17898 17.3254 5.58984 17.2773C5.06757 17.2346 4.61294 17.1508 4.19531 16.9716L4.01855 16.8896C3.36014 16.5541 2.80898 16.0434 2.4248 15.4169L2.27051 15.1415C2.03328 14.6758 1.93158 14.167 1.88281 13.5702C1.83468 12.9811 1.83496 12.2493 1.83496 11.3271V8.66301C1.83496 7.74072 1.83468 7.00898 1.88281 6.41985C1.93157 5.82309 2.03329 5.31432 2.27051 4.84856L2.4248 4.57317C2.80898 3.94666 3.36012 3.436 4.01855 3.10051L4.19531 3.0175C4.61285 2.83843 5.06771 2.75548 5.58984 2.71281C6.17898 2.66468 6.91071 2.66496 7.83301 2.66496H12.17C13.0893 2.66496 13.821 2.66468 14.4102 2.71281C15.0069 2.76157 15.5157 2.86329 15.9814 3.10051L16.2568 3.25481C16.8833 3.63898 17.394 4.19012 17.7295 4.84856L17.8125 5.02531C17.9916 5.44285 18.0745 5.89771 18.1172 6.41985C18.1653 7.00898 18.165 7.74072 18.165 8.66301V11.3271ZM8.16406 15.995H12.167C13.1112 15.995 13.7794 15.9947 14.3018 15.9521C14.8164 15.91 15.1308 15.8299 15.3779 15.704L15.5615 15.6015C15.9797 15.3451 16.32 14.9774 16.5439 14.538L16.6299 14.3378C16.7074 14.121 16.7605 13.8478 16.792 13.4618C16.8347 12.9394 16.835 12.2712 16.835 11.3271V8.66301C16.835 7.71885 16.8347 7.05065 16.792 6.52824C16.7605 6.14232 16.7073 5.86904 16.6299 5.65227L16.5439 5.45207C16.32 5.01264 15.9796 4.64498 15.5615 4.3886L15.3779 4.28606C15.1308 4.16013 14.8165 4.08006 14.3018 4.03801C13.7794 3.99533 13.1112 3.99504 12.167 3.99504H8.16406C8.16407 3.99667 8.16504 3.99829 8.16504 3.99992L8.16406 15.995Z"})})})}),!T&&(0,a.jsxs)("div",{className:"flex flex-col h-full px-3",children:[(0,a.jsxs)("div",{className:"space-y-1 mb-4",children:[(0,a.jsx)(E(),{href:"/dashboard",children:(0,a.jsxs)(i.$,{variant:"ghost",className:"w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ".concat(""),style:{color:"/dashboard"===f?"var(--sidebar-text-primary)":"var(--sidebar-text-secondary)",backgroundColor:"/dashboard"===f?"var(--surface-selected)":"transparent"},onMouseEnter:e=>{"/dashboard"!==f&&(e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)")},onMouseLeave:e=>{"/dashboard"!==f&&(e.currentTarget.style.backgroundColor="transparent")},children:[(0,a.jsx)(m.A,{className:"h-4 w-4",style:{color:"var(--sidebar-icon)"}}),"Dashboard"]})}),(0,a.jsx)(E(),{href:"/reports",children:(0,a.jsxs)(i.$,{variant:"ghost",className:"w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ".concat(""),style:{color:"/reports"===f?"var(--sidebar-text-primary)":"var(--sidebar-text-secondary)",backgroundColor:"/reports"===f?"var(--surface-selected)":"transparent"},onMouseEnter:e=>{"/reports"!==f&&(e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)")},onMouseLeave:e=>{"/reports"!==f&&(e.currentTarget.style.backgroundColor="transparent")},children:[(0,a.jsx)(S.A,{className:"h-4 w-4",style:{color:"var(--sidebar-icon)"}}),"Reports"]})}),(0,a.jsx)(E(),{href:"/datasources",children:(0,a.jsxs)(i.$,{variant:"ghost",className:"w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10",style:{color:"/datasources"===f?"var(--sidebar-text-primary)":"var(--sidebar-text-secondary)",backgroundColor:"/datasources"===f?"var(--surface-selected)":"transparent"},onMouseEnter:e=>{"/datasources"!==f&&(e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)")},onMouseLeave:e=>{"/datasources"!==f&&(e.currentTarget.style.backgroundColor="transparent")},children:[(0,a.jsx)(z.A,{className:"h-4 w-4",style:{color:"var(--sidebar-icon)"}}),"Data Sources"]})}),(0,a.jsx)(E(),{href:"/ai-workflows",children:(0,a.jsxs)(i.$,{variant:"ghost",className:"w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10",style:{color:"/ai-workflows"===f?"var(--sidebar-text-primary)":"var(--sidebar-text-secondary)",backgroundColor:"/ai-workflows"===f?"var(--surface-selected)":"transparent"},onMouseEnter:e=>{"/ai-workflows"!==f&&(e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)")},onMouseLeave:e=>{"/ai-workflows"!==f&&(e.currentTarget.style.backgroundColor="transparent")},children:[(0,a.jsx)(x.A,{className:"h-4 w-4",style:{color:"var(--sidebar-icon)"}}),"AI Workflows"]})})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)(i.$,{variant:"ghost",className:"w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10",onClick:r,disabled:l,style:{color:"var(--sidebar-text-secondary)",backgroundColor:"transparent"},onMouseEnter:e=>{l||(e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)")},onMouseLeave:e=>{l||(e.currentTarget.style.backgroundColor="transparent")},children:[(0,a.jsx)(h.A,{className:"h-4 w-4",style:{color:"var(--sidebar-icon)"}}),l?"Creating...":"New Chat"]})}),(0,a.jsxs)("div",{className:"flex flex-col gap-1 overflow-y-auto flex-1 pb-4",children:[(0,a.jsx)("h3",{className:"text-xs font-medium uppercase tracking-wider px-2 py-2 sticky top-0 z-10",style:{color:"var(--sidebar-text-tertiary)",backgroundColor:"var(--sidebar-bg)"},children:"Chat History"}),d&&(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"text-xs",style:{color:"var(--sidebar-text-tertiary)"},children:"Loading chats..."})}),!d&&c.map(e=>{let r=e.id===A,t=k===e.id;return(0,a.jsx)("div",{className:"group flex items-center justify-between py-2 px-2 rounded-lg cursor-pointer transition-all duration-200 ".concat(""),style:{backgroundColor:r?"var(--surface-selected)":"transparent"},onMouseEnter:e=>{r||(e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)")},onMouseLeave:e=>{r||(e.currentTarget.style.backgroundColor="transparent")},onClick:()=>y.push("/chat/".concat(e.id)),children:t?(0,a.jsxs)("form",{onSubmit:r=>{r.preventDefault(),Q(e.id)},className:"flex items-center gap-2 w-full",children:[(0,a.jsx)("input",{autoFocus:!0,className:"truncate max-w-[120px] text-sm font-normal bg-transparent border-b border-blue-400 outline-none px-1",style:{color:"var(--sidebar-text-primary)"},value:N,onChange:e=>M(e.target.value),onKeyDown:e=>{"Escape"===e.key&&w(null)}}),(0,a.jsx)(i.$,{type:"submit",size:"sm",variant:"ghost",className:"text-blue-500 px-2 text-xs rounded border-0",children:"Save"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(0,a.jsx)("span",{className:"truncate text-sm font-normal block",style:{color:"var(--sidebar-text-primary)"},children:e.title})}),(0,a.jsx)("div",{className:"relative ml-2 flex-shrink-0",onClick:e=>e.stopPropagation(),children:(0,a.jsxs)(B.rI,{children:[(0,a.jsx)(B.ty,{asChild:!0,children:(0,a.jsx)(i.$,{type:"button",size:"icon",variant:"ghost",className:"w-7 h-7 p-0 opacity-0 group-hover:opacity-100 transition-all duration-200 rounded border-0",style:{color:"var(--sidebar-icon)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},"aria-label":"More actions",children:(0,a.jsx)(_.A,{className:"w-4 h-4"})})}),(0,a.jsxs)(B.SQ,{align:"start",side:"bottom",sideOffset:8,className:"border-none shadow-xl rounded-xl p-2",style:{backgroundColor:"var(--sidebar-surface-secondary)",color:"var(--sidebar-text-primary)"},children:[(0,a.jsx)(B._2,{onClick:()=>$(e.id,e.title),className:"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200",style:{color:"var(--sidebar-text-primary)",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:"Rename"}),(0,a.jsx)(B._2,{onClick:()=>W(e.id),className:"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200",style:{color:"#ff8583",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="rgba(255, 133, 131, 0.1)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:"Delete"})]})]})})]})},e.id)}),!d&&0===c.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(D.A,{className:"h-12 w-12 mx-auto mb-3",style:{color:"var(--sidebar-text-tertiary)"}}),(0,a.jsx)("p",{className:"text-xs",style:{color:"var(--sidebar-text-tertiary)"},children:"No chats yet"}),(0,a.jsx)("p",{className:"text-xs mt-1",style:{color:"var(--sidebar-text-tertiary)"},children:"Start a new conversation"})]})]}),(0,a.jsx)("div",{className:"mt-auto pt-2 pb-4 border-t",style:{borderColor:"var(--sidebar-border)"},children:(0,a.jsxs)(B.rI,{children:[(0,a.jsx)(B.ty,{asChild:!0,children:(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-all duration-200 hover:bg-opacity-80",style:{backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,a.jsx)("div",{className:"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600",style:{backgroundImage:'url("https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y")'}}),(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(0,a.jsx)("span",{className:"text-sm font-medium block truncate",style:{color:"var(--sidebar-text-primary)"},children:"User"})})]})}),(0,a.jsxs)(B.SQ,{align:"start",side:"top",sideOffset:8,className:"border-none shadow-xl rounded-xl p-2",style:{backgroundColor:"var(--sidebar-surface-secondary)",color:"var(--sidebar-text-primary)"},children:[(0,a.jsx)(B._2,{asChild:!0,children:(0,a.jsxs)(E(),{href:"/profile",className:"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200",style:{color:"var(--sidebar-text-primary)",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,a.jsx)(P.A,{className:"w-4 h-4"}),"Profile"]})}),(0,a.jsx)(B._2,{asChild:!0,children:(0,a.jsxs)(E(),{href:"/settings",className:"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200",style:{color:"var(--sidebar-text-primary)",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,a.jsx)(R.A,{className:"w-4 h-4"}),"Settings"]})}),(0,a.jsxs)(B._2,{onClick:b,className:"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200",style:{color:"#ff8583",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="rgba(255, 133, 131, 0.1)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,a.jsx)(X.A,{className:"w-4 h-4"}),"Logout"]})]})]})})]}),T&&(0,a.jsx)("div",{className:"mt-auto p-2 pb-4 border-t",style:{borderColor:"var(--sidebar-border)"},children:(0,a.jsxs)(B.rI,{children:[(0,a.jsx)(B.ty,{asChild:!0,children:(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600 cursor-pointer transition-all duration-200 hover:scale-105",style:{backgroundImage:'url("https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y")'}})})}),(0,a.jsxs)(B.SQ,{align:"start",side:"right",sideOffset:8,className:"border-none shadow-xl rounded-xl p-2",style:{backgroundColor:"var(--sidebar-surface-secondary)",color:"var(--sidebar-text-primary)"},children:[(0,a.jsx)(B._2,{asChild:!0,children:(0,a.jsxs)(E(),{href:"/profile",className:"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200",style:{color:"var(--sidebar-text-primary)",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,a.jsx)(P.A,{className:"w-4 h-4"}),"Profile"]})}),(0,a.jsx)(B._2,{asChild:!0,children:(0,a.jsxs)(E(),{href:"/settings",className:"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200",style:{color:"var(--sidebar-text-primary)",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,a.jsx)(R.A,{className:"w-4 h-4"}),"Settings"]})}),(0,a.jsxs)(B._2,{onClick:b,className:"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200",style:{color:"#ff8583",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="rgba(255, 133, 131, 0.1)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,a.jsx)(X.A,{className:"w-4 h-4"}),"Logout"]})]})]})})]})},Q=e=>{let{children:r}=e,{setActiveChat:t}=(0,n.m)(),i=(0,o.useRouter)();(0,o.usePathname)();let[c,d]=(0,s.useState)(!1),[u,m]=(0,s.useState)(!1),x=(0,s.useRef)(null),g=(0,s.useCallback)(async()=>{if(u)return void console.log("New chat creation already in progress, ignoring click");x.current&&clearTimeout(x.current),x.current=setTimeout(async()=>{m(!0);try{await t(null),i.push("/chat")}catch(e){console.error("Error creating new chat:",e)}finally{m(!1)}},300)},[u,t,i]);return s.useEffect(()=>()=>{x.current&&clearTimeout(x.current)},[]),(0,a.jsx)(l._,{children:(0,a.jsxs)("div",{className:"flex h-screen bg-sidebar-bg",style:{fontFamily:"var(--font-inter), var(--font-noto-sans), sans-serif"},children:[(0,a.jsx)($,{onNewChat:g,onToggleCollapse:e=>{d(e)},isCreatingNewChat:u}),(0,a.jsxs)("main",{className:"flex flex-col flex-1 min-w-0 bg-sidebar-bg overflow-y-auto",children:[(0,a.jsx)(L,{}),(0,a.jsx)("div",{className:"flex-1",children:r})]})]})})}},87914:(e,r,t)=>{t.d(r,{H:()=>o});var a=t(12115),s=t(10071);let o=e=>{let{setPageHeader:r,resetPageHeader:t}=(0,s.s)();(0,a.useEffect)(()=>(r(e),()=>{t()}),[e.title,e.subtitle,e.icon,r,t])}}}]);