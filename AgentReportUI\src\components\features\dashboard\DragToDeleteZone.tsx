"use client";

import React from 'react';
import { Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { DragToDeleteZoneProps } from '@/types';

const DragToDeleteZone: React.FC<DragToDeleteZoneProps> = ({
  isVisible,
  isHovered,
}) => {
  if (!isVisible) return null;

  return (
    <div
      className={cn(
        "fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50",
        "transition-all duration-300 ease-in-out",
        isVisible ? "opacity-100 scale-100" : "opacity-0 scale-95"
      )}
    >
      <div
        className={cn(
          "flex items-center justify-center",
          "w-20 h-20 rounded-full",
          "border-2 border-dashed",
          "transition-all duration-200 ease-in-out",
          "shadow-lg backdrop-blur-sm",
          "drag-delete-zone", // Add class for collision detection
          isHovered && "hovered", // Add hovered class for CSS animation
          isHovered
            ? "bg-destructive/20 border-destructive text-destructive scale-110"
            : "bg-muted/80 border-muted-foreground/50 text-muted-foreground hover:bg-muted/90"
        )}
      >
        <Trash2 
          className={cn(
            "transition-all duration-200",
            isHovered ? "w-8 h-8" : "w-6 h-6"
          )} 
        />
      </div>
      
      {/* Helper text */}
      <div className="absolute top-full mt-2 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
        <div className={cn(
          "text-xs font-medium px-2 py-1 rounded",
          "bg-background/90 border shadow-sm",
          isHovered 
            ? "text-destructive border-destructive/20" 
            : "text-muted-foreground border-border"
        )}>
          {isHovered ? "Release to delete" : "Drop here to delete"}
        </div>
      </div>
    </div>
  );
};

export default DragToDeleteZone;
