"""Database API

This module provides the API endpoints for database management.
All methods are POST for security and consistency.
All logic and error handling is delegated to the service layer.
Controllers only handle routing and return data.
"""

import logging
from typing import Dict, List, Any
from fastapi import APIRouter, Depends

from app.models.api_models import (
    DatabaseConnectionRequest, 
    DisconnectDatabaseRequest,
    ListDatabasesRequest, 
    DatabaseSchemaRequest
)
from app.services.database_manager_service import DatabaseManagerService
from app.utils.security import get_current_user

router = APIRouter(
    prefix="/api/databases",
    tags=["databases"],
    responses={404: {"description": "Not found"}},
)

logger = logging.getLogger(__name__)
db_manager = DatabaseManagerService()

@router.post("/connectdatabase")
async def connect_database(request: DatabaseConnectionRequest, user_data=Depends(get_current_user)):
    """Connect to a database."""
    return await db_manager.connect_database_with_error_handling(
        user_id=user_data.id,
        name=request.name,
        description=request.description,
        db_type=request.type,
        host=request.host,
        port=request.port,
        username=request.username,
        password=request.password,
        database=request.database,
        db_schema=request.db_schema,
        ssl_enabled=request.ssl_enabled,
        connection_string=request.connection_string
    )

@router.post("/listdatabases")
async def list_databases(request: ListDatabasesRequest, user_data=Depends(get_current_user)):
    """List all connected databases for the current user."""
    return await db_manager.list_databases_with_error_handling(user_data.id)

@router.post("/disconnectdatabase")
async def disconnect_database(request: DisconnectDatabaseRequest, user_data=Depends(get_current_user)):
    """Disconnect from a database."""
    return await db_manager.disconnect_database_with_error_handling(user_data.id, request.db_id)

@router.post("/schema")
async def get_database_schema(request: DatabaseSchemaRequest, user_data=Depends(get_current_user)):
    """Get schema information for all connected databases."""
    return await db_manager.get_database_schema_with_error_handling(user_data.id) 
