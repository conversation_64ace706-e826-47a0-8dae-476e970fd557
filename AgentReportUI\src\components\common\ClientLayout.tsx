"use client";
import React, { ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ApiProvider } from "@/providers/ApiContext";
import { AuthProvider } from "@/providers/AuthContext";
import { PageHeaderProvider } from "@/providers/PageHeaderContext";
import { DataSourcesProvider } from "@/providers/DataSourcesContext";

// Create a client
const queryClient = new QueryClient();

interface ClientLayoutProps {
  children: ReactNode;
}

const ClientLayout: React.FC<ClientLayoutProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <ApiProvider>
        <AuthProvider>
          <DataSourcesProvider>
            <PageHeaderProvider>
              {children}
            </PageHeaderProvider>
          </DataSourcesProvider>
        </AuthProvider>
      </ApiProvider>
    </QueryClientProvider>
  );
};

export default ClientLayout;