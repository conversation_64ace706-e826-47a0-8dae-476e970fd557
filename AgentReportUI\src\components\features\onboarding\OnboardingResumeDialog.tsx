"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { OnboardingProgress } from '@/types/auth';
import { Play, RotateCcw, CheckCircle, Clock } from 'lucide-react';

interface OnboardingResumeDialogProps {
  isVisible: boolean;
  progress: OnboardingProgress;
  onResume: () => void;
  onStartOver: () => void;
  onDismiss: () => void;
}

export default function OnboardingResumeDialog({
  isVisible,
  progress,
  onResume,
  onStartOver,
  onDismiss,
}: OnboardingResumeDialogProps) {
  if (!isVisible) return null;

  const completionPercentage = Math.round((progress.completedSteps.length / progress.totalSteps) * 100);
  const lastUpdated = new Date(progress.lastUpdated);
  const timeSinceUpdate = Math.round((Date.now() - lastUpdated.getTime()) / (1000 * 60)); // minutes

  const formatTimeAgo = (minutes: number): string => {
    if (minutes < 1) return 'just now';
    if (minutes < 60) return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
    const hours = Math.round(minutes / 60);
    if (hours < 24) return `${hours} hour${hours === 1 ? '' : 's'} ago`;
    const days = Math.round(hours / 24);
    return `${days} day${days === 1 ? '' : 's'} ago`;
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <Card className="w-full max-w-md mx-4 shadow-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit">
            <Clock className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <CardTitle className="text-xl font-bold text-slate-900 dark:text-slate-100">
            Resume Your Setup?
          </CardTitle>
          <CardDescription className="text-slate-600 dark:text-slate-400">
            We found your previous onboarding progress
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Progress Overview */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Progress
              </span>
              <Badge variant="outline" className="text-xs">
                {progress.completedSteps.length} of {progress.totalSteps} steps
              </Badge>
            </div>
            
            <Progress value={completionPercentage} className="h-2" />
            
            <div className="text-center">
              <span className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                {completionPercentage}%
              </span>
              <span className="text-sm text-slate-500 dark:text-slate-400 ml-1">
                complete
              </span>
            </div>
          </div>

          {/* Current Step */}
          <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Play className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Next Step
              </span>
            </div>
            <p className="text-slate-900 dark:text-slate-100 font-medium capitalize">
              {progress.currentStep.replace(/([A-Z])/g, ' $1').trim()}
            </p>
          </div>

          {/* Completed Steps */}
          {progress.completedSteps.length > 0 && (
            <div className="space-y-2">
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Completed Steps
              </span>
              <div className="space-y-1">
                {progress.completedSteps.map((stepId, index) => (
                  <div key={stepId} className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                    <span className="text-sm text-slate-600 dark:text-slate-400 capitalize">
                      {stepId.replace(/([A-Z])/g, ' $1').trim()}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Last Updated */}
          <div className="text-center text-xs text-slate-500 dark:text-slate-400">
            Last updated {formatTimeAgo(timeSinceUpdate)}
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={onResume}
              className="w-full flex items-center justify-center space-x-2"
              size="lg"
            >
              <Play className="h-4 w-4" />
              <span>Continue Setup</span>
            </Button>
            
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={onStartOver}
                className="flex-1 flex items-center justify-center space-x-2"
              >
                <RotateCcw className="h-4 w-4" />
                <span>Start Over</span>
              </Button>
              
              <Button
                variant="ghost"
                onClick={onDismiss}
                className="flex-1"
              >
                Skip for Now
              </Button>
            </div>
          </div>

          {/* Help Text */}
          <div className="text-center text-xs text-slate-500 dark:text-slate-400">
            <p>
              You can always complete your setup later from your profile settings.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
