"""
Conversational Context Service
=============================

Enhanced context management for ChatGPT-like conversations about data and tables.
Maintains memory of discussed tables, queries, results, and conversation flow.
"""

import json
import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class ContextType(Enum):
    """Types of conversational context."""
    TABLE_REFERENCE = "table_reference"
    QUERY_RESULT = "query_result"
    DATA_INSIGHT = "data_insight"
    USER_INTENT = "user_intent"
    BUSINESS_ENTITY = "business_entity"


@dataclass
class TableContext:
    """Context about a table discussed in conversation."""
    table_name: str
    database_id: str
    columns_mentioned: List[str]
    sample_data: Optional[Dict[str, Any]] = None
    row_count: Optional[int] = None
    last_accessed: Optional[str] = None
    relationships: List[str] = None
    
    def __post_init__(self):
        if self.relationships is None:
            self.relationships = []
        if self.last_accessed is None:
            self.last_accessed = datetime.utcnow().isoformat()


@dataclass
class QueryContext:
    """Context about a query and its results."""
    query_text: str
    sql_generated: str
    result_summary: str
    tables_involved: List[str]
    key_findings: List[str]
    timestamp: str
    session_turn: int
    result_data: Optional[Dict[str, Any]] = None  # Store actual result data for reference
    query_intent: Optional[str] = None  # Store the intent (analytical, comparative, etc.)
    entities_mentioned: Optional[List[str]] = None  # Store entities from the query

    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.utcnow().isoformat()
        if self.entities_mentioned is None:
            self.entities_mentioned = []


@dataclass
class ConversationContext:
    """Complete conversational context for a session."""
    session_id: str
    user_id: str
    tables_discussed: Dict[str, TableContext]
    queries_executed: List[QueryContext]
    current_focus: Optional[str] = None  # Current table/topic of focus
    business_entities: Set[str] = None
    conversation_themes: List[str] = None
    last_updated: Optional[str] = None
    
    def __post_init__(self):
        if self.business_entities is None:
            self.business_entities = set()
        if self.conversation_themes is None:
            self.conversation_themes = []
        if self.last_updated is None:
            self.last_updated = datetime.utcnow().isoformat()


class ConversationalContextService:
    """Service for managing enhanced conversational context."""
    
    def __init__(self):
        self._context_cache: Dict[str, ConversationContext] = {}
        self._cache_ttl = timedelta(hours=2)  # Context cache TTL
        
    async def get_conversation_context(
        self, 
        session_id: str, 
        user_id: str
    ) -> ConversationContext:
        """Get or create conversation context for a session."""
        
        # Check cache first
        if session_id in self._context_cache:
            context = self._context_cache[session_id]
            # Check if cache is still valid
            last_updated = datetime.fromisoformat(context.last_updated.replace('Z', '+00:00'))
            if datetime.utcnow() - last_updated.replace(tzinfo=None) < self._cache_ttl:
                return context
        
        # Create new context
        context = ConversationContext(
            session_id=session_id,
            user_id=user_id,
            tables_discussed={},
            queries_executed=[]
        )
        
        self._context_cache[session_id] = context
        return context
    
    async def update_table_context(
        self,
        session_id: str,
        table_name: str,
        database_id: str,
        columns_mentioned: List[str] = None,
        sample_data: Dict[str, Any] = None,
        row_count: int = None
    ) -> None:
        """Update context about a table being discussed."""
        
        if session_id not in self._context_cache:
            logger.warning(f"No context found for session {session_id}")
            return
            
        context = self._context_cache[session_id]
        table_key = f"{database_id}.{table_name}"
        
        if table_key in context.tables_discussed:
            # Update existing table context
            table_ctx = context.tables_discussed[table_key]
            if columns_mentioned:
                # Merge columns, keeping unique ones
                existing_cols = set(table_ctx.columns_mentioned)
                new_cols = set(columns_mentioned)
                table_ctx.columns_mentioned = list(existing_cols.union(new_cols))
            if sample_data:
                table_ctx.sample_data = sample_data
            if row_count:
                table_ctx.row_count = row_count
            table_ctx.last_accessed = datetime.utcnow().isoformat()
        else:
            # Create new table context
            context.tables_discussed[table_key] = TableContext(
                table_name=table_name,
                database_id=database_id,
                columns_mentioned=columns_mentioned or [],
                sample_data=sample_data,
                row_count=row_count
            )
        
        # Update current focus
        context.current_focus = table_key
        context.last_updated = datetime.utcnow().isoformat()
    
    async def add_query_context(
        self,
        session_id: str,
        query_text: str,
        sql_generated: str,
        result_summary: str,
        tables_involved: List[str],
        key_findings: List[str] = None,
        result_data: Dict[str, Any] = None,
        query_intent: str = None,
        entities_mentioned: List[str] = None
    ) -> None:
        """Add context about a query that was executed with enhanced metadata."""

        if session_id not in self._context_cache:
            logger.warning(f"No context found for session {session_id}")
            return

        context = self._context_cache[session_id]

        query_ctx = QueryContext(
            query_text=query_text,
            sql_generated=sql_generated,
            result_summary=result_summary,
            tables_involved=tables_involved,
            key_findings=key_findings or [],
            timestamp=datetime.utcnow().isoformat(),
            session_turn=len(context.queries_executed) + 1,
            result_data=result_data,
            query_intent=query_intent,
            entities_mentioned=entities_mentioned or []
        )

        context.queries_executed.append(query_ctx)
        context.last_updated = datetime.utcnow().isoformat()

        # Update current focus based on the most recent query
        if tables_involved:
            context.current_focus = f"{tables_involved[0]}"  # Focus on primary table

        # Update business entities from the query
        if entities_mentioned:
            context.business_entities.update(entities_mentioned)
    
    async def extract_context_for_query(
        self,
        session_id: str,
        current_query: str
    ) -> Dict[str, Any]:
        """Extract relevant context for enhancing a current query."""
        
        if session_id not in self._context_cache:
            return {}
            
        context = self._context_cache[session_id]
        
        # Build context summary
        context_summary = {
            "current_focus": context.current_focus,
            "tables_discussed": {},
            "recent_queries": [],
            "business_entities": list(context.business_entities),
            "conversation_themes": context.conversation_themes
        }
        
        # Add table context
        for table_key, table_ctx in context.tables_discussed.items():
            context_summary["tables_discussed"][table_key] = {
                "table_name": table_ctx.table_name,
                "database_id": table_ctx.database_id,
                "columns_mentioned": table_ctx.columns_mentioned,
                "row_count": table_ctx.row_count,
                "last_accessed": table_ctx.last_accessed
            }
        
        # Add recent query context (last 3 queries)
        recent_queries = context.queries_executed[-3:] if context.queries_executed else []
        for query_ctx in recent_queries:
            context_summary["recent_queries"].append({
                "query_text": query_ctx.query_text,
                "result_summary": query_ctx.result_summary,
                "tables_involved": query_ctx.tables_involved,
                "key_findings": query_ctx.key_findings,
                "session_turn": query_ctx.session_turn
            })
        
        return context_summary
    
    async def identify_references_in_query(
        self,
        session_id: str,
        query: str
    ) -> Dict[str, Any]:
        """Identify what the query is referring to based on context with enhanced follow-up detection."""

        context_data = await self.extract_context_for_query(session_id, query)
        query_lower = query.lower()

        references = {
            "table_references": [],
            "column_references": [],
            "result_references": [],
            "entity_references": [],
            "needs_context": False,
            "follow_up_type": None,
            "previous_query_reference": None
        }

        # Enhanced pronoun and reference detection
        pronouns = ["it", "they", "these", "those", "that", "this"]
        comparative_terms = ["compare", "comparison", "versus", "vs", "against", "better", "worse", "higher", "lower"]
        result_references = ["numbers", "results", "data", "values", "findings", "records"]

        has_pronouns = any(pronoun in query_lower.split() for pronoun in pronouns)
        has_comparative = any(term in query_lower for term in comparative_terms)
        has_result_refs = any(ref in query_lower for ref in result_references)

        if has_pronouns or has_comparative or has_result_refs:
            references["needs_context"] = True

            # Determine follow-up type
            if has_comparative:
                references["follow_up_type"] = "comparative"
            elif has_result_refs:
                references["follow_up_type"] = "result_analysis"
            elif has_pronouns:
                references["follow_up_type"] = "pronoun_reference"

            # Try to resolve based on recent queries
            recent_queries = context_data.get("recent_queries", [])
            if recent_queries:
                last_query = recent_queries[-1]
                references["previous_query_reference"] = {
                    "query_text": last_query["query_text"],
                    "tables_involved": last_query["tables_involved"],
                    "key_findings": last_query["key_findings"],
                    "session_turn": last_query["session_turn"]
                }

                # Add result references for comparative queries
                if has_comparative or has_result_refs:
                    references["result_references"].append({
                        "reference_type": "previous_results",
                        "source_query": last_query["query_text"],
                        "confidence": 0.9
                    })

            # Try to resolve based on current focus
            if context_data.get("current_focus"):
                table_key = context_data["current_focus"]
                if table_key in context_data.get("tables_discussed", {}):
                    table_info = context_data["tables_discussed"][table_key]
                    references["table_references"].append({
                        "table_name": table_info["table_name"],
                        "database_id": table_info["database_id"],
                        "confidence": 0.8
                    })

        # Check for explicit table name references
        for table_key, table_info in context_data.get("tables_discussed", {}).items():
            table_name = table_info["table_name"].lower()
            if table_name in query_lower:
                references["table_references"].append({
                    "table_name": table_info["table_name"],
                    "database_id": table_info["database_id"],
                    "confidence": 0.9
                })

        # Check for column references
        for table_key, table_info in context_data.get("tables_discussed", {}).items():
            for column in table_info.get("columns_mentioned", []):
                if column.lower() in query_lower:
                    references["column_references"].append({
                        "column_name": column,
                        "table_name": table_info["table_name"],
                        "confidence": 0.7
                    })

        return references
    
    async def generate_context_enhanced_query(
        self,
        session_id: str,
        original_query: str
    ) -> str:
        """Generate a context-enhanced version of the query with intelligent follow-up handling."""

        references = await self.identify_references_in_query(session_id, original_query)

        if not references["needs_context"]:
            return original_query

        enhanced_query = original_query
        context_data = await self.extract_context_for_query(session_id, original_query)
        follow_up_type = references.get("follow_up_type")

        # Handle different types of follow-up questions
        if follow_up_type == "comparative":
            enhanced_query = await self._enhance_comparative_query(
                original_query, references, context_data
            )
        elif follow_up_type == "result_analysis":
            enhanced_query = await self._enhance_result_analysis_query(
                original_query, references, context_data
            )
        elif follow_up_type == "pronoun_reference":
            enhanced_query = await self._enhance_pronoun_query(
                original_query, references, context_data
            )

        # Additional context enhancement based on table references
        if references["table_references"] and enhanced_query == original_query:
            table_ref = references["table_references"][0]  # Use highest confidence
            table_name = table_ref["table_name"]

            # Enhanced pronoun replacement with better context awareness
            replacements = {
                " it ": f" {table_name} ",
                " they ": f" {table_name} records ",
                " these ": f" these {table_name} records ",
                " those ": f" those {table_name} records ",
                " that ": f" that {table_name} ",
                " this ": f" this {table_name} "
            }

            for old, new in replacements.items():
                if old in enhanced_query.lower():
                    enhanced_query = enhanced_query.replace(old, new)

        return enhanced_query

    async def _enhance_comparative_query(
        self,
        original_query: str,
        references: Dict[str, Any],
        context_data: Dict[str, Any]
    ) -> str:
        """Enhance comparative follow-up queries."""
        enhanced_query = original_query

        # Get the previous query context
        previous_ref = references.get("previous_query_reference")
        if previous_ref:
            last_query_text = previous_ref["query_text"]

            # Identify what was being analyzed in the previous query
            if "top" in last_query_text.lower() or "most" in last_query_text.lower():
                # Replace vague references with specific context
                enhanced_query = enhanced_query.replace(
                    "these rental numbers",
                    "the top rental numbers from the previous query"
                )
                enhanced_query = enhanced_query.replace(
                    "these numbers",
                    "the top rental results from the previous query"
                )
                enhanced_query = enhanced_query.replace(
                    "these",
                    "the top results from the previous query"
                )

                # Add context about what we're comparing to
                if "least" in enhanced_query.lower() or "worst" in enhanced_query.lower():
                    if "compare" in enhanced_query.lower():
                        enhanced_query += " (comparing the highest performing vs lowest performing)"

            # Add table context if available
            tables_involved = previous_ref.get("tables_involved", [])
            if tables_involved and "database" not in enhanced_query.lower():
                enhanced_query += f" in the {tables_involved[0]} table"

        return enhanced_query

    async def _enhance_result_analysis_query(
        self,
        original_query: str,
        references: Dict[str, Any],
        context_data: Dict[str, Any]
    ) -> str:
        """Enhance queries that reference previous results."""
        enhanced_query = original_query

        previous_ref = references.get("previous_query_reference")
        if previous_ref:
            # Replace generic result references with specific context
            enhanced_query = enhanced_query.replace(
                "the numbers",
                f"the results from: {previous_ref['query_text']}"
            )
            enhanced_query = enhanced_query.replace(
                "the data",
                f"the data from: {previous_ref['query_text']}"
            )
            enhanced_query = enhanced_query.replace(
                "the results",
                f"the results from: {previous_ref['query_text']}"
            )

        return enhanced_query

    async def _enhance_pronoun_query(
        self,
        original_query: str,
        references: Dict[str, Any],
        context_data: Dict[str, Any]
    ) -> str:
        """Enhance queries with pronoun references."""
        enhanced_query = original_query

        # Use table references if available
        if references["table_references"]:
            table_ref = references["table_references"][0]
            table_name = table_ref["table_name"]

            # Replace pronouns with table context
            replacements = {
                " it ": f" the {table_name} table ",
                " they ": f" the {table_name} records ",
                " them ": f" the {table_name} records "
            }

            for old, new in replacements.items():
                if old in enhanced_query.lower():
                    enhanced_query = enhanced_query.replace(old, new)

        return enhanced_query
    
    def clear_session_context(self, session_id: str) -> None:
        """Clear context for a specific session."""
        if session_id in self._context_cache:
            del self._context_cache[session_id]
    
    def cleanup_expired_contexts(self) -> None:
        """Clean up expired contexts from cache."""
        current_time = datetime.utcnow()
        expired_sessions = []
        
        for session_id, context in self._context_cache.items():
            last_updated = datetime.fromisoformat(context.last_updated.replace('Z', '+00:00'))
            if current_time - last_updated.replace(tzinfo=None) > self._cache_ttl:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self._context_cache[session_id]
        
        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired conversation contexts")


# Global instance
conversational_context_service = ConversationalContextService()
