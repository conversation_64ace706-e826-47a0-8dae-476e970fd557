import axios from 'axios';
import { getApiBaseUrl, STORAGE_KEYS } from '@/lib/constants';
import { SSEEvent } from '@/types/streaming';

export interface StreamingOptions {
  sessionId: string;
  query: string;
  outputFormat?: string;
  onTokenReceived?: (token: string) => void;
  onComplete?: (finalContent: string, completeResponse?: any) => void;
  onError?: (error: string) => void;
}

export class TokenStreamingService {
  private eventSource: EventSource | null = null;
  private accumulatedContent = '';
  private isConnected = false;
  private isCompleted = false; // Track if we've already completed to prevent duplicates

  constructor() {
    this.cleanup = this.cleanup.bind(this);
  }

  async startStreaming(options: StreamingOptions): Promise<void> {
    const {
      sessionId,
      query,
      outputFormat = 'excel',
      onTokenReceived,
      onComplete,
      onError
    } = options;

    // Reset state
    this.accumulatedContent = '';
    this.isCompleted = false;
    this.cleanup();

    // Get auth token
    const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    if (!token) {
      const error = 'No authentication token found';
      onError?.(error);
      return;
    }

    try {
      // Since EventSource only supports GET but backend expects POST with JSON body,
      // we need to first make a POST request to initiate streaming, then connect to SSE
      const baseUrl = getApiBaseUrl();

      // First, make a POST request to start the streaming session using FormData
      const formData = new FormData();
      formData.append('query', query);
      formData.append('output_format', outputFormat);
      formData.append('session_id', sessionId);
      formData.append('enable_token_streaming', 'true');

      console.log('🔗 Making POST request to:', `${baseUrl}/ask/question`);
      console.log('📤 Request data (FormData):');
      console.log('  - query:', query);
      console.log('  - output_format:', outputFormat);
      console.log('  - session_id:', sessionId);
      console.log('  - enable_token_streaming: true');

      const initResponse = await fetch(`${baseUrl}/ask/question`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          // Don't set Content-Type - let browser set it with boundary for FormData
        },
        body: formData,
      });

      console.log('📥 Response status:', initResponse.status);
      console.log('📥 Response headers:', Object.fromEntries(initResponse.headers.entries()));

      if (!initResponse.ok) {
        // Log the error response for debugging
        let errorDetails = '';
        try {
          const errorBody = await initResponse.text();
          console.error('❌ Error response body:', errorBody);
          errorDetails = ` - ${errorBody}`;
        } catch (e) {
          console.error('❌ Could not read error response body');
        }
        throw new Error(`Failed to initiate streaming: ${initResponse.status} ${initResponse.statusText}${errorDetails}`);
      }

      // Check if the response indicates streaming is available
      const contentType = initResponse.headers.get('content-type');
      console.log('📋 Response content-type:', contentType);

      if (contentType?.includes('text/event-stream')) {
        // The response is already an SSE stream - handle it directly with fetch
        console.log('🔄 Processing SSE stream from POST response...');
        this.handleSSEResponse(initResponse, onTokenReceived, onComplete, onError);
      } else {
        // The response is JSON, check if it contains a streaming URL or session info
        const responseData = await initResponse.json();
        console.log('📄 JSON response received:', responseData);

        if (responseData.streaming_url) {
          // Backend provided a streaming URL
          this.connectToSSE(responseData.streaming_url, onTokenReceived, onComplete, onError);
        } else {
          // No streaming available, return the regular response
          const content = responseData.summary || responseData.message || responseData.content || 'No response available';
          onComplete?.(content, responseData);
        }
      }

    } catch (error) {
      console.error('Error starting streaming connection:', error);
      const errorMsg = error instanceof Error ? error.message : 'Failed to start streaming connection';
      onError?.(errorMsg);
    }
  }

  private async handleSSEResponse(
    response: Response,
    onTokenReceived?: (token: string) => void,
    onComplete?: (finalContent: string, completeResponse?: any) => void,
    onError?: (error: string) => void
  ): Promise<void> {
    if (!response.body) {
      onError?.('No response body available for streaming');
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
      this.isConnected = true;
      console.log('✅ SSE stream connected');

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          console.log('📡 SSE stream ended');
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        this.processSSEChunk(chunk, onTokenReceived, onComplete, onError);
      }
    } catch (error) {
      console.error('❌ Error reading SSE stream:', error);
      onError?.('Error reading streaming response');
    } finally {
      reader.releaseLock();
      this.isConnected = false;
    }
  }

  private connectToSSE(
    url: string,
    onTokenReceived?: (token: string) => void,
    onComplete?: (finalContent: string, completeResponse?: any) => void,
    onError?: (error: string) => void
  ): void {
    console.log('🔗 Connecting to SSE URL:', url);

    this.eventSource = new EventSource(url);

    this.eventSource.onopen = () => {
      console.log('✅ SSE connection opened');
      this.isConnected = true;
    };

    this.eventSource.onmessage = (event) => {
      this.processSSEEvent(event.data, onTokenReceived, onComplete, onError);
    };

    this.eventSource.onerror = (error) => {
      console.error('❌ SSE connection error:', error);
      this.isConnected = false;

      if (this.eventSource?.readyState === EventSource.CLOSED) {
        onError?.('Connection to server lost');
        this.cleanup();
      }
    };
  }

  private processSSEChunk(
    chunk: string,
    onTokenReceived?: (token: string) => void,
    onComplete?: (finalContent: string, completeResponse?: any) => void,
    onError?: (error: string) => void
  ): void {
    // Process SSE chunk data
    const lines = chunk.split('\n');

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const eventData = line.substring(6);
        this.processSSEEvent(eventData, onTokenReceived, onComplete, onError);
      }
    }
  }

  private processSSEEvent(
    eventData: string,
    onTokenReceived?: (token: string) => void,
    onComplete?: (finalContent: string, completeResponse?: any) => void,
    onError?: (error: string) => void
  ): void {
    try {
      const data = JSON.parse(eventData);
      console.log('📨 SSE event received:', data);

      // Skip processing if already completed
      if (this.isCompleted && ['token_complete', 'conversation_complete'].includes(data.type)) {
        console.log('⏭️ Skipping duplicate completion event:', data.type);
        return;
      }

      switch (data.type) {
        case 'token_stream':
          // Backend sends token directly in the event, not in data object
          const token = data.token;

          console.log('🔍 Raw token received:', token);

          if (token && typeof token === 'string') {
            console.log(`⏰ Processing token: "${token}"`);

            // Accumulate the raw tokens for final content
            this.accumulatedContent += token;

            // Send the token directly to the UI for real-time display
            onTokenReceived?.(token);
          }
          break;

        case 'token_complete':
          // Backend sends complete_response in data object
          const completeResponse = data.data?.complete_response || data.data?.message;
          const fullResponseData = data.data; // Pass the complete data object

          console.log('🎯 Processing token_complete event');
          console.log('Complete response:', completeResponse);
          console.log('Full response data:', fullResponseData);

          // Only process if we haven't completed yet and have a complete response
          if (!this.isCompleted && completeResponse) {
            console.log('✅ Using complete response from token_complete');
            this.isCompleted = true;
            onComplete?.(completeResponse, fullResponseData);
            this.cleanup();
          }
          break;

        case 'conversation_complete':
          // Backend sends final message in data object
          const finalMessage = data.data?.message;

          console.log('🎯 Processing conversation_complete event');
          console.log('Final message:', finalMessage);

          // Only process if we haven't completed yet
          if (!this.isCompleted) {
            if (finalMessage) {
              console.log('✅ Using final message from conversation_complete');
              this.isCompleted = true;
              onComplete?.(finalMessage);
            } else if (this.accumulatedContent) {
              console.log('✅ Using accumulated content from tokens');
              this.isCompleted = true;
              onComplete?.(this.accumulatedContent);
            }
            this.cleanup();
          }
          break;

        case 'error':
          const errorMsg = data.data?.error || data.error || 'Unknown streaming error';
          onError?.(errorMsg);
          this.cleanup();
          break;

        default:
          // Ignore agent_status and agent_result events
          if (!['agent_status', 'agent_result'].includes(data.type)) {
            console.warn('Unknown SSE event type:', data.type);
          }
      }
    } catch (parseError) {
      console.error('Error parsing SSE event:', parseError);
      console.error('Raw event data:', eventData);
      // Don't treat parse errors as fatal - continue streaming
    }
  }

  stopStreaming(): void {
    this.cleanup();
  }

  private extractCleanToken(token: string): string | null {
    console.log(`🔍 Extracting clean token from: "${token}"`);

    // Skip JSON structure tokens that don't contain actual content
    const skipTokens = [
      '{\n  "',
      '\n  "message',
      'message": "',
      '",\n}',
      '."\n}',
      '{\n  "message": "',
      '"',
      '\n',
      '  ',
      '{',
      '}',
      '": "',
    ];

    // If token is just JSON structure, skip it
    if (skipTokens.some(skipToken => token === skipToken)) {
      console.log(`⏭️ Skipping JSON structure token: "${token}"`);
      return null;
    }

    // If token contains JSON structure at the beginning, extract the content part
    if (token.includes('": "')) {
      const match = token.match(/": "(.+)/);
      const extracted = match ? match[1] : null;
      console.log(`📝 Extracted from JSON key-value: "${extracted}"`);
      return extracted;
    }

    // If token ends with JSON structure, clean it
    if (token.endsWith('",') || token.endsWith('."\n}') || token.endsWith('"')) {
      const cleaned = token.replace(/[",\n}]+$/, '');
      console.log(`🧹 Cleaned trailing JSON: "${cleaned}"`);
      return cleaned;
    }

    // If token starts with JSON structure, remove it
    if (token.startsWith('{\n  "') || token.startsWith('"')) {
      const cleaned = token.replace(/^[{\n\s"]+/, '');
      console.log(`🧹 Cleaned leading JSON: "${cleaned}"`);
      return cleaned || null;
    }

    // Return clean content tokens as-is
    console.log(`✅ Clean content token: "${token}"`);
    return token;
  }

  private cleanup(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.isConnected = false;
    this.isCompleted = false;
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  getAccumulatedContent(): string {
    return this.accumulatedContent;
  }
}

// Export a singleton instance
export const tokenStreamingService = new TokenStreamingService();
