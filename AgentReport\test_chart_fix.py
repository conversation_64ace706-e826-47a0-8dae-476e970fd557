#!/usr/bin/env python3
"""
Test script to verify the chart service database connection fix.
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.chart_service import ChartSelectionService
from app.models.chart import ChartQueryRequest

async def test_chart_creation():
    """Test chart creation with database connection."""
    print("Testing chart service database connection fix...")
    
    # Initialize the chart service
    chart_service = ChartSelectionService()
    
    # Create a test request with a real database ID to test schema-aware SQL generation
    request = ChartQueryRequest(
        prompt="Show me users by their role",
        user_id="ef947981-0766-49ed-928b-3d39d3ee11f0",  # Use real user ID from logs
        database_id="db_6fa39d8a"  # Use real database ID from logs
    )
    
    try:
        # Generate the chart
        result = await chart_service.process_chart_query(request)
        
        if result.success:
            print("✅ Chart generation successful!")
            print(f"Chart Type: {result.data.chartType}")
            print(f"Title: {result.data.title}")
            print(f"Data Points: {len(result.data.data)}")
            print(f"Data Source: {result.data.metadata.dataSource}")
            if result.data.metadata.sqlQuery:
                print(f"SQL Query: {result.data.metadata.sqlQuery}")
            print("✅ Test passed - chart service is working correctly")
        else:
            print(f"❌ Chart generation failed: {result.error}")
            print("❌ Test failed")
            
    except Exception as e:
        print(f"❌ Exception during chart generation: {e}")
        print("❌ Test failed")

if __name__ == "__main__":
    asyncio.run(test_chart_creation())
