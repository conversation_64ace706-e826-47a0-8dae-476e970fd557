(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7],{1243:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5196:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},30095:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>i});var a=t(95155),s=t(12115),o=t(74045),n=t(35695),l=t(45786);function i(e){let{children:r,requireAuth:t=!0,redirectTo:i="/login"}=e,{isAuthenticated:d,isLoading:c,isNewUser:u,signIn:m}=(0,o.A)(),b=(0,n.useRouter)(),p=(0,n.usePathname)(),[x,g]=(0,s.useState)(!0),[y,h]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{(async()=>{if(!t||[l.bw.HOME,l.bw.LOGIN,l.bw.REGISTER,l.bw.AUTH.CALLBACK,l.bw.OAUTH.CALLBACK,l.bw.ONBOARDING].includes(p))return g(!1);if(d){if(u&&"/onboarding"!==p){console.log("New user on protected route, redirecting to onboarding"),b.push("/onboarding");return}if(!u&&"/onboarding"===p){console.log("Existing user on onboarding, redirecting to dashboard"),b.push("/dashboard");return}g(!1);return}if(!d&&!y&&!c){h(!0),console.log("Attempting automatic authentication from stored tokens...");try{await m(void 0,!1)}catch(e){console.log("Auto-authentication failed, redirecting to login"),b.push(i)}return}if(!d&&y&&!c){console.log("Not authenticated, redirecting to login"),b.push(i);return}g(!1)})()},[d,c,u,p,t,i,b,m,y]),x||c)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Checking authentication..."})]})}):t&&!d?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Authentication Required"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please sign in to access this page."}),(0,a.jsx)("button",{onClick:()=>b.push(i),className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Sign In"})]})}):(0,a.jsx)(a.Fragment,{children:r})}},40968:(e,r,t)=>{"use strict";t.d(r,{b:()=>l});var a=t(12115),s=t(63655),o=t(95155),n=a.forwardRef((e,r)=>(0,o.jsx)(s.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var l=n},45503:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});var a=t(12115);function s(e){let r=a.useRef({value:e,previous:e});return a.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}},46102:(e,r,t)=>{"use strict";t.d(r,{Bc:()=>l,ZI:()=>c,k$:()=>d,m_:()=>i});var a=t(95155),s=t(12115),o=t(89613),n=t(46486);let l=o.Kq,i=o.bL,d=o.l9,c=s.forwardRef((e,r)=>{let{className:t,sideOffset:s=4,...l}=e;return(0,a.jsx)(o.UC,{ref:r,sideOffset:s,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...l})});c.displayName=o.UC.displayName},47262:(e,r,t)=>{"use strict";t.d(r,{S:()=>l});var a=t(95155);t(12115);var s=t(76981),o=t(5196),n=t(46486);function l(e){let{className:r,...t}=e;return(0,a.jsx)(s.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",r),...t,children:(0,a.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,a.jsx)(o.A,{className:"size-3.5"})})})}},51154:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},51156:(e,r,t)=>{Promise.resolve().then(t.bind(t,71e3))},54416:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62523:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var a=t(95155);t(12115);var s=t(46486);function o(e){let{className:r,type:t,...o}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...o})}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>i,Wu:()=>d,ZB:()=>l,Zp:()=>o,aR:()=>n});var a=t(95155);t(12115);var s=t(46486);function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function n(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold text-white",r),...t})}function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...t})}},66932:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},71e3:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>T});var a=t(95155),s=t(12115),o=t(74677),n=t(58189),l=t(74045),i=t(87914),d=t(30285),c=t(66695),u=t(54213),m=t(44020),b=t(1243);let p=(0,t(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var x=t(66932),g=t(62523),y=t(85057),h=t(47262),f=t(54416),v=t(51154);let j=s.memo(e=>{let{field:r,value:t,onChange:s,disabled:o}=e;return(0,a.jsxs)("div",{className:"flex items-center space-x-3 py-2",children:[(0,a.jsx)(h.S,{id:r.name,name:r.name,checked:t,onCheckedChange:e=>{s(r.name,!0===e)},disabled:o,className:"border-sidebar-border data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"}),(0,a.jsx)("label",{htmlFor:r.name,className:"text-sm cursor-pointer",style:{color:"var(--sidebar-text-primary)"},children:r.label})]})});j.displayName="CheckboxField";let N=s.memo(e=>{let{field:r,value:t,onChange:s,disabled:o}=e;return(0,a.jsx)(g.p,{id:r.name,name:r.name,type:r.type,placeholder:r.placeholder,value:String(t||""),onChange:e=>s(r.name,e.target.value),disabled:o,className:"border-sidebar-border transition-all duration-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 h-9 text-sm",style:{backgroundColor:"rgba(255, 255, 255, 0.05)",color:"var(--sidebar-text-primary)"}})});N.displayName="InputFieldBasic";let k=s.memo(function(e){let{isOpen:r,onClose:t,dataSourceType:o,onConnect:n,apiError:l}=e,[i,c]=(0,s.useState)({}),[u,m]=(0,s.useState)(!1),[p,x]=(0,s.useState)(null),[g,h]=(0,s.useState)(null),k=(0,s.useCallback)((e,r)=>{c(t=>({...t,[e]:r})),x(null),h(null)},[]);(0,s.useEffect)(()=>{if(o&&r){let e={};o.connectionFields.forEach(r=>{"checkbox"===r.type?"ssl_enabled"===r.name&&(e[r.name]=!1):e[r.name]=void 0}),c(e),x(null),h(null)}},[null==o?void 0:o.id,o,r]),(0,s.useEffect)(()=>{h(l||null)},[l]);let w=(0,s.useCallback)(async e=>{if(e.preventDefault(),!o)return;let r=[];for(let e of o.connectionFields)e.required&&(void 0===i[e.name]||""===i[e.name])&&r.push(e.label);if(r.length>0)return void(1===r.length?x("Please fill in the ".concat(r[0]," field.")):x("Please fill in the following required fields: ".concat(r.join(", "),".")));x(null),m(!0),h(null);let a={};o.connectionFields.forEach(e=>{let r=i[e.name];void 0!==r&&""!==r&&("port"===e.name?a.port=Number(r):"ssl_enabled"===e.name?a.ssl_enabled=!!r:a[e.name]=r)}),a.name=i.name||o.name,a.type=o.id.toUpperCase(),"postgresql"!==o.id&&"mysql"!==o.id||a.ssl_enabled||(a.ssl_enabled=!1),console.log("ConnectDataSourceModal: Submitting params:",a);try{await n(a)&&t()}catch(e){console.error("Connection error:",e)}finally{m(!1)}},[o,i,n,t]);return((0,s.useEffect)(()=>(r?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[r]),(0,s.useEffect)(()=>{let e=e=>{"Escape"===e.key&&r&&!u&&t()};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[r,t,u]),r)?(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-6",style:{backgroundColor:"transparent"},onClick:()=>!u&&t(),children:(0,a.jsxs)("div",{className:"w-full max-w-md flex flex-col max-h-[85vh] overflow-hidden transform-gpu border border-sidebar-border rounded-2xl shadow-2xl",style:{backgroundColor:"#323232",willChange:"transform"},onClick:e=>e.stopPropagation(),children:[(0,a.jsx)("div",{className:"absolute top-4 right-4 z-10",children:(0,a.jsx)(d.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>!u&&t(),disabled:u,className:"w-8 h-8 rounded-lg border-0 transition-all duration-200",style:{color:"var(--sidebar-text-secondary)",backgroundColor:"transparent"},onMouseEnter:e=>{u||(e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)",e.currentTarget.style.setProperty("color","var(--sidebar-text-primary)","important"))},onMouseLeave:e=>{u||(e.currentTarget.style.backgroundColor="transparent",e.currentTarget.style.setProperty("color","var(--sidebar-text-secondary)","important"))},children:(0,a.jsx)(f.A,{className:"w-4 h-4"})})}),(0,a.jsxs)("form",{onSubmit:w,className:"flex flex-col flex-grow overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex-grow p-6 space-y-4 overflow-y-auto",style:{overscrollBehavior:"contain",WebkitOverflowScrolling:"touch"},children:[null==o?void 0:o.connectionFields.map(e=>{let r=["port"].includes(e.name);return(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(y.J,{htmlFor:e.name,className:"text-sm font-medium",style:{color:"var(--sidebar-text-primary)"},children:e.label}),"checkbox"===e.type?(0,a.jsx)("div",{className:"pt-1",children:(0,a.jsx)(j,{field:e,value:!!i[e.name],onChange:k,disabled:u})}):(0,a.jsx)("div",{className:"".concat(r?"max-w-[140px]":""),children:(0,a.jsx)(N,{field:e,value:"boolean"==typeof i[e.name]?"":String(i[e.name]||""),onChange:k,disabled:u})})]},e.name)}),(p||g)&&(0,a.jsx)("div",{className:"p-3 border border-red-200 rounded-lg mt-4",style:{backgroundColor:"rgba(239, 68, 68, 0.05)"},children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-red-500 flex-shrink-0 mt-0.5"}),(0,a.jsxs)("div",{className:"text-red-600 text-sm",children:[p&&(0,a.jsx)("div",{children:p}),g&&(0,a.jsx)("div",{children:g})]})]})})]}),(0,a.jsx)("div",{className:"p-6 pt-4",children:(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>!u&&t(),disabled:u,className:"flex-1 border border-sidebar-border rounded-lg transition-all duration-200 h-9",style:{color:"var(--sidebar-text-secondary)",backgroundColor:"transparent"},onMouseEnter:e=>{u||(e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)",e.currentTarget.style.setProperty("color","var(--sidebar-text-primary)","important"))},onMouseLeave:e=>{u||(e.currentTarget.style.backgroundColor="transparent",e.currentTarget.style.setProperty("color","var(--sidebar-text-secondary)","important"))},children:"Cancel"}),(0,a.jsx)(d.$,{type:"submit",disabled:u,variant:"outline",className:"flex-1 border border-sidebar-border rounded-lg transition-all duration-200 h-9 font-medium",style:{color:"var(--sidebar-text-secondary)",backgroundColor:"transparent"},onMouseEnter:e=>{u||(e.currentTarget.style.backgroundColor="var(--sidebar-text-primary)",e.currentTarget.style.setProperty("color","var(--sidebar-bg)","important"),e.currentTarget.style.transform="scale(1.02)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0, 0, 0, 0.15)")},onMouseLeave:e=>{u||(e.currentTarget.style.backgroundColor="transparent",e.currentTarget.style.setProperty("color","var(--sidebar-text-secondary)","important"),e.currentTarget.style.transform="scale(1)",e.currentTarget.style.boxShadow="none")},children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Connecting..."]}):"Connect"})]})})]})]})}):null});k.displayName="ConnectDataSourceModal";var w=t(46102),C=t(44838);let S=[{id:"postgresql",name:"PostgreSQL",typeCategory:"database",description:"Connect to your PostgreSQL relational database for powerful queries and analytics.",icon:e=>{let{className:r}=e;return(0,a.jsx)("img",{src:"/icons/postgres.svg",alt:"PostgreSQL",className:r})},connectionFields:[{label:"Connection Name",name:"name",type:"text",required:!0,placeholder:"My PostgreSQL DB"},{label:"Host",name:"host",type:"text",required:!0,placeholder:"localhost"},{label:"Port",name:"port",type:"number",required:!0,placeholder:"5432"},{label:"Username",name:"username",type:"text",required:!0,placeholder:"postgres"},{label:"Password",name:"password",type:"password",required:!0},{label:"Database Name",name:"database",type:"text",required:!0,placeholder:"mydatabase"},{label:"Schema (optional)",name:"db_schema",type:"text",placeholder:"public"},{label:"Enable SSL",name:"ssl_enabled",type:"checkbox"}]},{id:"supabase",name:"Supabase",typeCategory:"database",description:"Connect to your Supabase database for real-time data access.",icon:e=>{let{className:r}=e;return(0,a.jsx)("img",{src:"/icons/supabase-logo-icon.svg",alt:"Supabase",className:r})},connectionFields:[{label:"Connection Name",name:"name",type:"text",required:!0,placeholder:"My Supabase DB"},{label:"Host",name:"host",type:"text",required:!0,placeholder:"aws-0-us-east-2.pooler.supabase.com"},{label:"Port",name:"port",type:"number",required:!0,placeholder:"6543"},{label:"Username",name:"username",type:"text",required:!0,placeholder:"postgres.fnwrnsojpsmleatmvmjw"},{label:"Password",name:"password",type:"password",required:!0},{label:"Database Name",name:"database",type:"text",required:!0,placeholder:"postgres"},{label:"Enable SSL",name:"ssl_enabled",type:"checkbox",required:!0}]},{id:"mongodb",name:"MongoDB",typeCategory:"database",description:"Connect to your MongoDB NoSQL document database for flexible data operations.",icon:e=>{let{className:r}=e;return(0,a.jsx)("img",{src:"/icons/mongoDB.svg",alt:"MongoDB",className:r})},connectionFields:[{label:"Connection Name",name:"name",type:"text",required:!0,placeholder:"My MongoDB Atlas"},{label:"Connection String",name:"connection_string",type:"text",required:!0,placeholder:"mongodb+srv://user:<EMAIL>/dbname"}]},{id:"mysql",name:"MySQL",typeCategory:"database",description:"Connect to your MySQL open-source relational database for reliable data access.",icon:e=>{let{className:r}=e;return(0,a.jsx)("img",{src:"/icons/mysql.svg",alt:"MySQL",className:r})},connectionFields:[{label:"Connection Name",name:"name",type:"text",required:!0,placeholder:"My MySQL DB"},{label:"Host",name:"host",type:"text",required:!0,placeholder:"localhost"},{label:"Port",name:"port",type:"number",required:!0,placeholder:"3306"},{label:"Username",name:"username",type:"text",required:!0,placeholder:"root"},{label:"Password",name:"password",type:"password",required:!0},{label:"Database Name",name:"database",type:"text",required:!0,placeholder:"mydatabase"}]}],A=()=>{let{listDatabases:e,connectNewDatabase:r,disconnectExistingDatabase:t}=(0,n.g)(),{isAuthenticated:o}=(0,l.A)(),g=(0,s.useMemo)(()=>({title:"Data Sources",icon:u.A}),[]);(0,i.H)(g);let[y,h]=(0,s.useState)([]),[f,v]=(0,s.useState)(!0),[j,N]=(0,s.useState)(!1),[A,P]=(0,s.useState)(null),[T,E]=(0,s.useState)(!1),[M,L]=(0,s.useState)(null),[q,D]=(0,s.useState)(null),[_]=(0,s.useState)(""),[F]=(0,s.useState)("all");console.log("DataSourcesPageContent: Component render"),(0,s.useEffect)(()=>{console.log("DataSourcesPageContent: Auth state changed, isAuthenticated:",o),o?(async()=>{console.log("DataSourcesPageContent: Starting initial fetch"),v(!0),P(null);try{let r=await e();console.log("DataSourcesPageContent: Initial fetch completed, sources:",r.length),h(r)}catch(e){console.error("Failed to fetch connected data sources",e),P("Could not load connected data sources. Please try again."),h([])}finally{v(!1)}})():(h([]),P(null),v(!1))},[o,e]),(0,s.useCallback)(async()=>{if(!o)return void P("Please log in to refresh data sources.");N(!0),P(null);try{let r=await e();h(r)}catch(e){console.error("Failed to refresh connected data sources",e),P("Could not refresh data sources. Please try again.")}finally{N(!1)}},[o,e]);let B=(0,s.useCallback)(async()=>{if(o){P(null),v(!0);try{let r=await e();h(r)}catch(e){console.error("Failed to retry loading data sources",e),P("Could not load data sources. Please try again.")}finally{v(!1)}}},[o,e]),R=S.filter(e=>{let r=e.name.toLowerCase().includes(_.toLowerCase())||e.description.toLowerCase().includes(_.toLowerCase()),t="all"===F||e.typeCategory===F;return r&&t}),z=y.filter(e=>e.name.toLowerCase().includes(_.toLowerCase())||e.type.toLowerCase().includes(_.toLowerCase())),I=(0,s.useCallback)(async()=>{P(null);try{let r=await e();h(r)}catch(e){console.error("Failed to refresh after change",e),P("Could not refresh data sources after the operation.")}},[e]),O=(0,s.useCallback)(e=>{L(e),E(!0)},[]),Q=(0,s.useCallback)(()=>{E(!1),L(null)},[]),U=(0,s.useCallback)(e=>{console.log("Edit functionality not yet implemented for connection:",e.name),D(null)},[]),H=(0,s.useCallback)(async(e,r)=>{if(console.log("Deleting connection:",r),D(null),window.confirm('Are you sure you want to delete the connection "'.concat(r,'"? This action cannot be undone.'))){P(null);try{console.log("Disconnecting database with ID:",e);let r=await t(e);console.log("Successfully disconnected database:",r.message),await I()}catch(r){console.error("Failed to delete connection",r);let e="Failed to delete connection. Please try again.";if(r&&"object"==typeof r&&"response"in r){var a,s,o,n,l,i,d;if(console.log("Error response:",null==(a=r.response)?void 0:a.data),(null==(s=r.response)?void 0:s.status)===422){let t=null==(d=r.response.data)?void 0:d.detail;if(Array.isArray(t)){let r=t.map(e=>{if(e&&"object"==typeof e&&"loc"in e&&"msg"in e){let r=Array.isArray(e.loc)?e.loc.join("."):"unknown field";return"".concat(r,": ").concat(e.msg)}return"validation error"}).join(", ");e="Validation error: ".concat(r)}else e="string"==typeof t?t:"Validation failed. Please check your input."}else(null==(n=r.response)||null==(o=n.data)?void 0:o.detail)&&"string"==typeof r.response.data.detail?e=r.response.data.detail:(null==(i=r.response)||null==(l=i.data)?void 0:l.message)&&(e=r.response.data.message)}else r&&"object"==typeof r&&"message"in r&&"string"==typeof r.message&&(e=r.message);P(e)}}},[t,I]),Z=(0,s.useCallback)(async e=>{if(!M)return!1;P(null);try{let t={...e,type:M.id};return console.log("DataSourcesPageContent: Sending connect request with params:",t),await r(t),await I(),Q(),!0}catch(r){console.error("Failed to connect data source",r);let e="Failed to connect. Please check details and try again.";if(r&&"object"==typeof r&&"response"in r){var t,a,s,o,n,l,i;if(console.log("Error response:",null==(t=r.response)?void 0:t.data),(null==(a=r.response)?void 0:a.status)===422){let t=null==(i=r.response.data)?void 0:i.detail;if(Array.isArray(t)){let r=t.map(e=>{if(e&&"object"==typeof e&&"loc"in e&&"msg"in e){let r=Array.isArray(e.loc)?e.loc.join("."):"unknown field";return"".concat(r,": ").concat(e.msg)}return"validation error"}).join(", ");e="Validation error: ".concat(r)}else e="string"==typeof t?t:"Validation failed. Please check your input."}else(null==(o=r.response)||null==(s=o.data)?void 0:s.detail)&&"string"==typeof r.response.data.detail?e=r.response.data.detail:(null==(l=r.response)||null==(n=l.data)?void 0:n.message)&&(e=r.response.data.message)}else r&&"object"==typeof r&&"message"in r&&"string"==typeof r.message&&(e=r.message);return P(e),!1}},[M,r,I,Q]);return(0,a.jsx)(w.Bc,{children:(0,a.jsx)("div",{className:"min-h-screen bg-sidebar-bg",role:"main","aria-label":"Data Sources Management",children:(0,a.jsxs)("div",{className:"container mx-auto space-y-6 sm:space-y-8 p-4 sm:p-6 lg:p-8",children:[A&&(0,a.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5"}),(0,a.jsxs)("div",{className:"text-red-800 dark:text-red-200",children:[(0,a.jsx)("div",{className:"font-medium",children:"Error"}),(0,a.jsx)("div",{className:"text-sm",children:A})]})]}),(0,a.jsx)(d.$,{onClick:B,disabled:f||j,variant:"outline",size:"sm",className:"ml-4 border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900",children:"Try Again"})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl sm:text-2xl font-semibold text-sidebar-text-primary flex items-center gap-2",children:"Connected Sources"}),(0,a.jsxs)("p",{className:"text-sm sm:text-base text-sidebar-text-secondary mt-1",children:[z.length," of ",y.length," ",1===y.length?"source":"sources",_&&" matching your search"]})]}),f?(0,a.jsx)("div",{className:"flex flex-wrap gap-4",children:[void 0,void 0,void 0,void 0].map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg border border-sidebar-border animate-pulse min-w-fit",style:{backgroundColor:"var(--sidebar-bg)"},children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded bg-sidebar-text-secondary opacity-20"}),(0,a.jsx)("div",{className:"min-w-0",children:(0,a.jsx)("div",{className:"h-4 bg-sidebar-text-secondary opacity-20 rounded w-24"})})]},r))}):z.length>0?(0,a.jsx)("div",{className:"flex flex-wrap gap-4",children:z.map(e=>{let r=S.find(r=>r.id.toUpperCase()===e.type.toUpperCase()),t=(null==r?void 0:r.icon)||u.A;return(0,a.jsxs)("div",{className:"group flex items-center gap-3 p-3 rounded-lg transition-all duration-200 border border-sidebar-border min-w-fit",style:{backgroundColor:"var(--sidebar-bg)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="var(--sidebar-bg)"},children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded flex items-center justify-center",children:(0,a.jsx)(t,{className:"h-5 w-5"})}),(0,a.jsx)("div",{className:"cursor-pointer",onClick:()=>{console.log("Clicked on database:",e.name)},children:(0,a.jsx)("span",{className:"text-sm font-medium whitespace-nowrap block",style:{color:"var(--sidebar-text-primary)"},children:e.name})}),(0,a.jsx)("div",{className:"relative ml-2 flex-shrink-0",onClick:e=>e.stopPropagation(),children:(0,a.jsxs)(C.rI,{children:[(0,a.jsx)(C.ty,{asChild:!0,children:(0,a.jsx)(d.$,{type:"button",size:"icon",variant:"ghost",className:"w-7 h-7 p-0 transition-all duration-200 rounded border-0",style:{color:"var(--sidebar-text-secondary)",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.setProperty("background-color","var(--interactive-bg-secondary-hover)","important"),e.currentTarget.style.setProperty("color","var(--sidebar-text-primary)","important")},onMouseLeave:e=>{e.currentTarget.style.setProperty("background-color","transparent","important"),e.currentTarget.style.setProperty("color","var(--sidebar-text-secondary)","important")},"aria-label":"More actions for ".concat(e.name),children:(0,a.jsx)(m.A,{className:"w-4 h-4"})})}),(0,a.jsxs)(C.SQ,{align:"start",side:"bottom",sideOffset:8,className:"border-none shadow-xl rounded-xl p-2",style:{backgroundColor:"var(--sidebar-surface-secondary)",color:"var(--sidebar-text-primary)"},children:[(0,a.jsx)(C._2,{onClick:()=>U(e),className:"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200",style:{color:"var(--sidebar-text-primary)",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:"Edit Connection"}),(0,a.jsx)(C._2,{onClick:()=>H(e.id,e.name),className:"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200",style:{color:"#ff8583",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="rgba(255, 133, 131, 0.1)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:"Delete Connection"})]})]})})]},e.id)})}):y.length>0?(0,a.jsx)(c.Zp,{className:"text-center py-12 border-dashed border-2 border-sidebar-border bg-sidebar-bg",children:(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-sidebar-text-secondary w-fit mx-auto",children:(0,a.jsx)(p,{className:"h-8 w-8 text-sidebar-text-secondary"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-sidebar-text-primary",children:"No sources match your search"}),(0,a.jsx)("p",{className:"text-sidebar-text-secondary mt-2",children:"Try adjusting your search terms or filters"})]})]})}):(0,a.jsx)(c.Zp,{className:"text-center py-12 border-dashed border-2 border-sidebar-border bg-sidebar-bg",children:(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-sidebar-text-secondary w-fit mx-auto",children:(0,a.jsx)(u.A,{className:"h-8 w-8 text-sidebar-text-secondary"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-sidebar-text-primary",children:"No data sources connected"}),(0,a.jsx)("p",{className:"text-sidebar-text-secondary mt-2",children:"Connect your first data source to get started with AI-powered queries"})]})]})})]}),(0,a.jsx)("div",{className:"border-t border-sidebar-border my-8"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl sm:text-2xl font-semibold text-sidebar-text-primary flex items-center gap-2",children:"Add New Connection"}),(0,a.jsxs)("p",{className:"text-sm sm:text-base text-sidebar-text-secondary mt-1",children:[R.length," available data source",1!==R.length?"s":"",_&&" matching your search"]})]}),R.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:R.map(e=>{let r=e.icon;return(0,a.jsx)(c.Zp,{className:"group h-full border border-sidebar-border bg-sidebar-bg",style:{backgroundColor:"var(--interactive-bg-secondary-hover)"},children:(0,a.jsxs)(c.Wu,{className:"flex flex-col items-center justify-center p-6 text-center space-y-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 flex items-center justify-center",children:(0,a.jsx)(r,{className:"h-10 w-10"})}),(0,a.jsx)("h3",{className:"text-lg font-medium",style:{color:"var(--sidebar-text-primary)"},children:e.name}),(0,a.jsx)(d.$,{onClick:()=>O(e),variant:"outline",className:"w-full justify-center text-sm font-normal border border-sidebar-border rounded-lg transition-all duration-200 h-9",size:"sm",disabled:f||j,"aria-label":"Connect to ".concat(e.name),style:{color:"var(--sidebar-text-secondary)",backgroundColor:"transparent"},onMouseEnter:e=>{f||j||(e.currentTarget.style.backgroundColor="var(--sidebar-text-primary)",e.currentTarget.style.setProperty("color","var(--sidebar-bg)","important"),e.currentTarget.style.transform="scale(1.02)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0, 0, 0, 0.15)")},onMouseLeave:e=>{f||j||(e.currentTarget.style.backgroundColor="transparent",e.currentTarget.style.setProperty("color","var(--sidebar-text-secondary)","important"),e.currentTarget.style.transform="scale(1)",e.currentTarget.style.boxShadow="none")},children:"Connect"})]})},e.id)})}):(0,a.jsx)(c.Zp,{className:"text-center py-12 border-dashed border-2 border-sidebar-border bg-sidebar-bg",children:(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-sidebar-text-secondary w-fit mx-auto",children:(0,a.jsx)(x.A,{className:"h-8 w-8 text-sidebar-text-secondary"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-sidebar-text-primary",children:"No data sources match your filters"}),(0,a.jsx)("p",{className:"text-sidebar-text-secondary mt-2",children:"Try adjusting your search terms or category filters"})]})]})})]}),M&&(0,a.jsx)(k,{isOpen:T,onClose:Q,dataSourceType:M,onConnect:Z})]})})})};var P=t(30095);let T=()=>(0,a.jsx)(P.Ay,{children:(0,a.jsx)(o.A,{children:(0,a.jsx)(A,{})})})},76981:(e,r,t)=>{"use strict";t.d(r,{C1:()=>k,bL:()=>j});var a=t(12115),s=t(6101),o=t(46081),n=t(85185),l=t(5845),i=t(45503),d=t(11275),c=t(28905),u=t(63655),m=t(95155),b="Checkbox",[p,x]=(0,o.A)(b),[g,y]=p(b);function h(e){let{__scopeCheckbox:r,checked:t,children:s,defaultChecked:o,disabled:n,form:i,name:d,onCheckedChange:c,required:u,value:p="on",internal_do_not_use_render:x}=e,[y,h]=(0,l.i)({prop:t,defaultProp:null!=o&&o,onChange:c,caller:b}),[f,v]=a.useState(null),[j,N]=a.useState(null),k=a.useRef(!1),w=!f||!!i||!!f.closest("form"),C={checked:y,disabled:n,setChecked:h,control:f,setControl:v,name:d,form:i,value:p,hasConsumerStoppedPropagationRef:k,required:u,defaultChecked:!S(o)&&o,isFormControl:w,bubbleInput:j,setBubbleInput:N};return(0,m.jsx)(g,{scope:r,...C,children:"function"==typeof x?x(C):s})}var f="CheckboxTrigger",v=a.forwardRef((e,r)=>{let{__scopeCheckbox:t,onKeyDown:o,onClick:l,...i}=e,{control:d,value:c,disabled:b,checked:p,required:x,setControl:g,setChecked:h,hasConsumerStoppedPropagationRef:v,isFormControl:j,bubbleInput:N}=y(f,t),k=(0,s.s)(r,g),w=a.useRef(p);return a.useEffect(()=>{let e=null==d?void 0:d.form;if(e){let r=()=>h(w.current);return e.addEventListener("reset",r),()=>e.removeEventListener("reset",r)}},[d,h]),(0,m.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":S(p)?"mixed":p,"aria-required":x,"data-state":A(p),"data-disabled":b?"":void 0,disabled:b,value:c,...i,ref:k,onKeyDown:(0,n.m)(o,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,n.m)(l,e=>{h(e=>!!S(e)||!e),N&&j&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})})});v.displayName=f;var j=a.forwardRef((e,r)=>{let{__scopeCheckbox:t,name:a,checked:s,defaultChecked:o,required:n,disabled:l,value:i,onCheckedChange:d,form:c,...u}=e;return(0,m.jsx)(h,{__scopeCheckbox:t,checked:s,defaultChecked:o,disabled:l,required:n,onCheckedChange:d,name:a,form:c,value:i,internal_do_not_use_render:e=>{let{isFormControl:a}=e;return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(v,{...u,ref:r,__scopeCheckbox:t}),a&&(0,m.jsx)(C,{__scopeCheckbox:t})]})}})});j.displayName=b;var N="CheckboxIndicator",k=a.forwardRef((e,r)=>{let{__scopeCheckbox:t,forceMount:a,...s}=e,o=y(N,t);return(0,m.jsx)(c.C,{present:a||S(o.checked)||!0===o.checked,children:(0,m.jsx)(u.sG.span,{"data-state":A(o.checked),"data-disabled":o.disabled?"":void 0,...s,ref:r,style:{pointerEvents:"none",...e.style}})})});k.displayName=N;var w="CheckboxBubbleInput",C=a.forwardRef((e,r)=>{let{__scopeCheckbox:t,...o}=e,{control:n,hasConsumerStoppedPropagationRef:l,checked:c,defaultChecked:b,required:p,disabled:x,name:g,value:h,form:f,bubbleInput:v,setBubbleInput:j}=y(w,t),N=(0,s.s)(r,j),k=(0,i.Z)(c),C=(0,d.X)(n);a.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,r=!l.current;if(k!==c&&e){let t=new Event("click",{bubbles:r});v.indeterminate=S(c),e.call(v,!S(c)&&c),v.dispatchEvent(t)}},[v,k,c,l]);let A=a.useRef(!S(c)&&c);return(0,m.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=b?b:A.current,required:p,disabled:x,name:g,value:h,form:f,...o,tabIndex:-1,ref:N,style:{...o.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function S(e){return"indeterminate"===e}function A(e){return S(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=w},85057:(e,r,t)=>{"use strict";t.d(r,{J:()=>n});var a=t(95155);t(12115);var s=t(40968),o=t(46486);function n(e){let{className:r,...t}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}}},e=>{var r=r=>e(e.s=r);e.O(0,[464,817,874,786,826,613,189,45,179,30,441,684,358],()=>r(51156)),_N_E=e.O()}]);