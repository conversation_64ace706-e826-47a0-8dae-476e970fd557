(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[73],{7917:(e,t,r)=>{Promise.resolve().then(r.bind(r,89375))},35695:(e,t,r)=>{"use strict";var s=r(18999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},89375:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(95155),a=r(12115),n=r(35695),o=r(74045),l=r(45786);function c(){let e=(0,n.useRouter)(),t=(0,n.useSearchParams)(),{setUser:r}=(0,o.A)(),[c,i]=(0,a.useState)("loading"),[d,u]=(0,a.useState)("");return(0,a.useEffect)(()=>{(async()=>{try{console.log("OAuth callback page loaded"),console.log("Current URL:",window.location.href),console.log("Search params:",Object.fromEntries(t.entries()));let s=t.get("access_token"),a=t.get("refresh_token"),n=t.get("user_id"),o=t.get("expires_at"),c=t.get("token_type")||"bearer";if(s&&n){console.log("✅ Found tokens in URL params - OAuth successful!"),localStorage.setItem(l.d5.ACCESS_TOKEN,s),localStorage.setItem(l.d5.REFRESH_TOKEN,a||""),localStorage.setItem(l.d5.USER_ID,n),localStorage.setItem(l.d5.EXPIRES_AT,o||new Date(Date.now()+864e5).toISOString()),localStorage.setItem(l.d5.TOKEN_TYPE,c),console.log("✅ Tokens stored in localStorage");let t={access_token:s,token_type:c,user_id:n,expires_at:o||new Date(Date.now()+864e5).toISOString(),refresh_token:a||""};r(t),i("success"),console.log("✅ User authenticated, redirecting to dashboard..."),setTimeout(()=>{e.push("/dashboard")},1e3);return}let d=t.get("error"),u=t.get("error_description");if(d)throw Error("OAuth Error: ".concat(d," - ").concat(u||"Unknown error"));throw Error("No tokens received from OAuth callback. Please check your backend configuration.")}catch(t){console.error("OAuth callback error:",t),u(t.message||"Authentication failed"),i("error"),setTimeout(()=>{e.push("/login")},3e3)}})()},[t,e,r]),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8 p-8",children:(0,s.jsxs)("div",{className:"text-center",children:["loading"===c&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Completing sign in..."}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Please wait while we authenticate you with Google."})]}),"success"===c&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Sign in successful!"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Redirecting to dashboard..."})]}),"error"===c&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Authentication failed"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:d}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Redirecting to login page..."})]})]})})})}function i(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8 p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Loading..."}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Preparing authentication..."})]})})})}function d(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(i,{}),children:(0,s.jsx)(c,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,189,45,441,684,358],()=>t(7917)),_N_E=e.O()}]);