"use client";
import React, { useState, useEffect } from 'react';
import { useApi } from '@/providers/ApiContext';
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { CheckCircle, PlusCircle, Database, FileText, Cloud } from 'lucide-react';

interface DataSource {
  id: string;
  name: string;
  type: 'database' | 'file' | 'cloud' | 'other';
  description: string;
  icon: React.ElementType;
  // Example: Add specific fields for connection if needed
  // connectionFields?: { label: string; name: string; type: string }[]; 
}

const DATA_SOURCES: DataSource[] = [
  {
    id: 'postgresql',
    name: 'PostgreSQL',
    type: 'database',
    description: 'Connect to your PostgreSQL relational database.',
    icon: Database,
  },
  {
    id: 'mongodb',
    name: 'MongoDB',
    type: 'database',
    description: 'Connect to your MongoDB NoSQL document database.',
    icon: Database,
  },
  {
    id: 'mysql',
    name: 'MySQL',
    type: 'database',
    description: 'Connect to your MySQL open-source relational database.',
    icon: Database,
  },
  {
    id: 'excel',
    name: 'Excel File',
    type: 'file',
    description: 'Upload and process data from Excel spreadsheets.',
    icon: FileText,
  },
  {
    id: 'csv',
    name: 'CSV File',
    type: 'file',
    description: 'Import data from Comma-Separated Values files.',
    icon: FileText,
  },
  {
    id: 'json',
    name: 'JSON File',
    type: 'file',
    description: 'Connect and parse data from JSON files.',
    icon: FileText,
  },
  {
    id: 's3',
    name: 'Amazon S3',
    type: 'cloud',
    description: 'Connect to data stored in Amazon S3 buckets.',
    icon: Cloud,
  },
  {
    id: 'gcs',
    name: 'Google Cloud Storage',
    type: 'cloud',
    description: 'Access data from your Google Cloud Storage buckets.',
    icon: Cloud,
  },
   {
    id: 'other',
    name: 'Custom API',
    type: 'other',
    description: 'Connect to a custom API endpoint for data.',
    icon: PlusCircle,
  },
];

const DataSourcesListComponent = () => {
  const { getDatabaseSchema } = useApi();
  const [connectedSources, setConnectedSources] = useState<string[]>([]);
  const [loadingSchema, setLoadingSchema] = useState(false);
  const [schemaError, setSchemaError] = useState<string | null>(null);

  // This useEffect is an example. In a real app, you might fetch connected status or schema.
  useEffect(() => {
    const fetchInitialData = async () => {
      setLoadingSchema(true);
      try {
        // const schemaData = await getDatabaseSchema(); // Example API call
        // console.log("Schema:", schemaData);
        // Simulate fetching existing connections
        // setConnectedSources(['postgresql']); 
      } catch (err) {
        console.error('Failed to fetch initial data', err);
        setSchemaError('Could not load initial data sources configuration.');
      } finally {
        setLoadingSchema(false);
      }
    };
    fetchInitialData();
  }, [getDatabaseSchema]);

  const handleConnect = async (sourceId: string) => {
    if (connectedSources.includes(sourceId)) {
      // Optionally implement disconnect logic
      setConnectedSources(prev => prev.filter(id => id !== sourceId));
    } else {
      // Simulate connection process
      console.log(`Connecting to ${sourceId}...`);
      // In a real app: open modal for credentials, make API call to connect
      setConnectedSources(prev => [...prev, sourceId]);
    }
  };

  const renderDataSourceCard = (source: DataSource) => {
    const Icon = source.icon;
    const isConnected = connectedSources.includes(source.id);

    return (
      <Card key={source.id} className="shadow-md hover:shadow-lg transition-shadow duration-200 ">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 ">
          <CardTitle className="text-lg font-semibold text-white">{source.name}</CardTitle>
          <Icon className="h-6 w-6 text-white" />
        </CardHeader>
        <CardContent>
          <p className="text-sm text-white h-16 overflow-hidden ">{source.description}</p>
        </CardContent>
        <CardFooter>
          <Button 
            onClick={() => handleConnect(source.id)} 
            variant={isConnected ? "secondary" : "default"}
            className="w-full text-white border-white hover:bg-white/10 hover:text-white"
          >
            {isConnected ? <CheckCircle className="mr-2 h-4 w-4 text-white" /> : <PlusCircle className="mr-2 h-4 w-4 text-white" />}
            <span className="text-white">{isConnected ? 'Connected' : 'Connect'}</span>
          </Button>
        </CardFooter>
      </Card>
    );
  };

  const sections: { title: string; sources: DataSource[] }[] = [
    { title: 'Databases', sources: DATA_SOURCES.filter(s => s.type === 'database') },
    { title: 'File Uploads', sources: DATA_SOURCES.filter(s => s.type === 'file') },
    { title: 'Cloud Storage', sources: DATA_SOURCES.filter(s => s.type === 'cloud') },
    { title: 'Other Integrations', sources: DATA_SOURCES.filter(s => s.type === 'other') },
  ];

  if (loadingSchema) {
    return <div className="flex justify-center items-center h-full"><p>Loading data sources...</p></div>;
  }

  if (schemaError) {
    return <div className="flex justify-center items-center h-full text-red-500"><p>{schemaError}</p></div>;
  }

  return (
    <div className="p-4 md:p-8 space-y-8 bg-gray-50 dark:bg-gray-900 flex-grow">
      <h1 className="text-3xl font-bold text-white">Manage Data Sources</h1>
      <p className="text-white">
        Connect your various data sources to enable the AI agent to query and analyze your data.
      </p>

      {sections.map(section => (
        section.sources.length > 0 && (
          <div key={section.title}>
            <h2 className="text-2xl font-semibold text-white mb-4">{section.title}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {section.sources.map(renderDataSourceCard)}
            </div>
          </div>
        )
      ))}
    </div>
  );
};

export default DataSourcesListComponent;