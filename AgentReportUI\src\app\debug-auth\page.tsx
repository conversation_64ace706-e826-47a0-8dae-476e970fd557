"use client";

import React, { useState, useEffect } from 'react';
import { useApi } from '@/providers/ApiContext';
import { STORAGE_KEYS } from '@/lib/constants';

export default function DebugAuthPage() {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getUserProfile, completeOnboarding } = useApi();

  const fetchDebugInfo = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const info: any = {
        timestamp: new Date().toISOString(),
        localStorage: {},
        profileData: null,
        profileError: null,
        completionTest: null,
        completionError: null,
      };

      // Get localStorage data
      info.localStorage = {
        accessToken: localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN)?.substring(0, 20) + '...',
        refreshToken: localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)?.substring(0, 20) + '...',
        userId: localStorage.getItem(STORAGE_KEYS.USER_ID),
        expiresAt: localStorage.getItem(STORAGE_KEYS.EXPIRES_AT),
        tokenType: localStorage.getItem(STORAGE_KEYS.TOKEN_TYPE),
      };

      // Test getUserProfile
      try {
        console.log('🔍 Testing getUserProfile...');
        const profile = await getUserProfile();
        info.profileData = profile;
        console.log('✅ getUserProfile success:', profile);
      } catch (err: any) {
        info.profileError = {
          message: err.message,
          status: err.response?.status,
          data: err.response?.data,
        };
        console.error('❌ getUserProfile failed:', err);
      }

      // Test completion endpoint (but don't actually complete)
      try {
        console.log('🔍 Testing completion endpoint...');
        // We'll just test the endpoint without actually completing
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:8000'}/api/auth/onboarding/complete`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN)}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({}),
        });
        
        const responseData = await response.text();
        info.completionTest = {
          status: response.status,
          statusText: response.statusText,
          data: responseData,
        };
        console.log('✅ Completion endpoint test:', info.completionTest);
      } catch (err: any) {
        info.completionError = {
          message: err.message,
        };
        console.error('❌ Completion endpoint test failed:', err);
      }

      setDebugInfo(info);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDebugInfo();
  }, []);

  const handleRefresh = () => {
    fetchDebugInfo();
  };

  const handleTestCompletion = async () => {
    try {
      console.log('🚀 Testing actual completion...');
      await completeOnboarding();
      console.log('✅ Completion successful!');
      alert('Completion successful! Check console for details.');
    } catch (err: any) {
      console.error('❌ Completion failed:', err);
      alert(`Completion failed: ${err.message}`);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h1 className="text-2xl font-bold mb-4">Debug Authentication</h1>
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span>Loading debug information...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              🔍 Debug Authentication
            </h1>
            <div className="space-x-2">
              <button
                onClick={handleRefresh}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
              >
                Refresh
              </button>
              <button
                onClick={handleTestCompletion}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg"
              >
                Test Completion
              </button>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-red-800 mb-2">Error</h3>
              <p className="text-red-700">{error}</p>
            </div>
          )}

          <div className="space-y-6">
            {/* localStorage Info */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                📦 localStorage Data
              </h3>
              <pre className="text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto">
                {JSON.stringify(debugInfo.localStorage, null, 2)}
              </pre>
            </div>

            {/* Profile Data */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                👤 Profile Data (POST /auth/me)
              </h3>
              {debugInfo.profileData ? (
                <div>
                  <div className="mb-2">
                    <span className="font-medium">Status:</span>
                    <span className="ml-2 text-green-600">✅ Success</span>
                  </div>
                  <pre className="text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto">
                    {JSON.stringify(debugInfo.profileData, null, 2)}
                  </pre>
                </div>
              ) : (
                <div>
                  <div className="mb-2">
                    <span className="font-medium">Status:</span>
                    <span className="ml-2 text-red-600">❌ Failed</span>
                  </div>
                  <pre className="text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto">
                    {JSON.stringify(debugInfo.profileError, null, 2)}
                  </pre>
                </div>
              )}
            </div>

            {/* Completion Test */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                🎯 Completion Endpoint Test (POST /auth/onboarding/complete)
              </h3>
              {debugInfo.completionTest ? (
                <div>
                  <div className="mb-2">
                    <span className="font-medium">Status:</span>
                    <span className={`ml-2 ${debugInfo.completionTest.status === 200 ? 'text-green-600' : 'text-red-600'}`}>
                      {debugInfo.completionTest.status} {debugInfo.completionTest.statusText}
                    </span>
                  </div>
                  <pre className="text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto">
                    {JSON.stringify(debugInfo.completionTest, null, 2)}
                  </pre>
                </div>
              ) : (
                <div>
                  <div className="mb-2">
                    <span className="font-medium">Status:</span>
                    <span className="ml-2 text-red-600">❌ Failed</span>
                  </div>
                  <pre className="text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto">
                    {JSON.stringify(debugInfo.completionError, null, 2)}
                  </pre>
                </div>
              )}
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                🔍 What to Look For:
              </h3>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <li>• <strong>Profile Data:</strong> Check if `is_new_user` is true or false</li>
                <li>• <strong>Completion Test:</strong> Check the status code (200 = success, 400 = already completed, 401 = auth issue)</li>
                <li>• <strong>localStorage:</strong> Verify tokens are present and not expired</li>
                <li>• <strong>Console:</strong> Check browser console for detailed logs</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
