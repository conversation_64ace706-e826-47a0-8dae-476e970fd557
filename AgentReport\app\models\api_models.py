"""API Models

This module defines models used in API requests and responses.
"""

from typing import Dict, List, Any, Optional, Literal
from pydantic import BaseModel, Field

from app.models.database import DatabaseType

class DatabaseConnectionRequest(BaseModel):
    """Request to connect to a database."""
    name: str = Field(..., description="A friendly name for the database")
    description: Optional[str] = Field(None, description="Optional description")
    type: DatabaseType = Field(..., description="Type of database (postgresql, mysql, sqlserver, oracle, mongodb, supabase)")
    host: str = Field(..., description="Database host")
    port: int = Field(..., description="Database port")
    username: str = Field(..., description="Database username")
    password: str = Field(..., description="Database password")
    database: str = Field(..., description="Database name")
    db_schema: Optional[str] = Field(None, alias="schema", description="Schema name (for databases that support schemas)")
    ssl_enabled: bool = Field(False, description="Whether SSL is enabled")
    connection_string: Optional[str] = Field(None, description="Optional custom connection string")

class DatabaseConnectionResponse(BaseModel):
    """Response to a database connection request."""
    id: str = Field(..., description="The ID of the connected database")
    name: str = Field(..., description="The friendly name of the database")
    type: str = Field(..., description="The type of database")
    message: str = Field(..., description="A message about the connection")
    table_count: Optional[int] = Field(None, description="Number of tables in the database")

class QueryRequest(BaseModel):
    """A user question to the /api/query/ask endpoint."""
    query: str = Field(..., description="Natural-language question")
    output_format: str = Field("csv", description="csv | excel")
    session_id: Optional[str] = Field(
        None,
        description="Chat session ID.  Leave blank to start a new chat."
    )
    # optional targeting (unchanged)
    target_databases: Optional[List[str]] = None
    target_tables: Optional[Dict[str, List[str]]] = None
    target_columns: Optional[Dict[str, Dict[str, List[str]]]] = None
    target_datasets: Optional[List[str]] = Field(
        None, description="Optional list of dataset IDs to analyse"
    )

class QueryResponse(BaseModel):
    """What the UI gets back."""
    query: str
    session_id: str                         # always echo the session
    summary: Optional[str] = None
    message:  Optional[str] = None
    output_files: Optional[List[Dict[str, Any]]] = None
    errors: Optional[List[str]] = None
    sql_queries: Optional[Dict[str, str]] = None

class ReportRequest(BaseModel):
    """A user question to the /api/query/report endpoint."""
    query: str = Field(..., description="Natural-language question about reports")
    output_format: str = Field("csv", description="csv | excel")
    session_id: Optional[str] = Field(
        None,
        description="Chat session ID for reports. Leave blank to start a new chat."
    )
    # optional targeting for reports
    target_datasets: Optional[List[str]] = Field(
        None, description="Optional list of dataset IDs to analyse for reports"
    )

class ReportResponse(BaseModel):
    """What the UI gets back from report queries."""
    query: str
    session_id: str                         # always echo the session
    summary: Optional[str] = None
    message: Optional[str] = None
    output_files: Optional[List[Dict[str, Any]]] = None
    errors: Optional[List[str]] = None
    report_insights: Optional[Dict[str, Any]] = None

class ConversationMessage(BaseModel):
    """A message in a conversation."""
    role: str = Field(..., description="The role of the sender (user or assistant)")
    content: str = Field(..., description="The content of the message")

class ColumnSchema(BaseModel):
    """Schema information for a database column."""
    name: str = Field(..., description="Column name")

class TableSchema(BaseModel):
    """Schema information for a database table."""
    name: str = Field(..., description="Table name")
    columns: List[str] = Field([], description="List of column names in the table")

class DatabaseSchema(BaseModel):
    """Schema information for a database."""
    id: str = Field(..., description="Database ID")
    name: str = Field(..., description="Friendly name of the database")
    type: str = Field(..., description="Type of database")
    tables: List[TableSchema] = Field([], description="List of tables in the database")

class SchemaResponse(BaseModel):
    """Response for the database schema endpoint."""
    databases: List[DatabaseSchema] = Field([], description="List of connected databases and their schema information")

class DisconnectDatabaseRequest(BaseModel):
    """Request to disconnect from a database."""
    db_id: str = Field(..., description="The database ID to disconnect from")

class ListDatabasesRequest(BaseModel):
    """Request to list databases (can be empty for now, but allows for future filtering)."""
    pass

class DatabaseSchemaRequest(BaseModel):
    """Request to get database schema (can be empty for now, but allows for future filtering)."""
    pass 

# ──────────────────────────────────────────
# Reports API Models
# ──────────────────────────────────────────

class ListReportsRequest(BaseModel):
    """Request to list user reports."""
    max_reports: Optional[int] = Field(100, description="Maximum number of reports to return (default: 100, max: 500)")
    
    class Config:
        """Pydantic config."""
        json_schema_extra = {
            "example": {
                "max_reports": 50
            }
        }

class ReportInfo(BaseModel):
    """Information about a single report."""
    key: str = Field(..., description="S3 object key")
    session_id: str = Field(..., description="Session ID that generated this report")
    file_name: str = Field(..., description="Original filename")
    size: int = Field(..., description="File size in bytes")
    last_modified: str = Field(..., description="Last modified timestamp (ISO format)")
    download_url: str = Field(..., description="Pre-signed URL for downloading the report")
    content_type: str = Field(..., description="MIME type of the file")
    format: str = Field(..., description="File format (csv, xlsx, etc.)")
    file_type: Optional[str] = Field(None, description="File type category (reports, visualizations, dashboards, artifacts)")
    structure: Optional[str] = Field(None, description="Storage structure used (unified, legacy)")
    
    class Config:
        """Pydantic config."""
        json_schema_extra = {
            "example": {
                "key": "users/user123/sessions/sess_abc/reports/20241220_120000_analysis.csv",
                "session_id": "sess_abc",
                "file_name": "20241220_120000_analysis.csv",
                "size": 1024,
                "last_modified": "2024-01-15T10:30:00Z",
                "download_url": "https://s3.amazonaws.com/bucket/presigned-url",
                "content_type": "text/csv",
                "format": "csv",
                "file_type": "reports",
                "structure": "unified"
            }
        }

class ListReportsResponse(BaseModel):
    """Response for listing user reports."""
    reports: List[ReportInfo] = Field(..., description="List of user reports")
    total_count: int = Field(..., description="Total number of reports found")
    
    class Config:
        """Pydantic config."""
        json_schema_extra = {
            "example": {
                "reports": [
                    {
                        "key": "users/user123/sessions/sess_abc/reports/20241220_120000_analysis.csv",
                        "session_id": "sess_abc",
                        "file_name": "20241220_120000_analysis.csv",
                        "size": 1024,
                        "last_modified": "2024-01-15T10:30:00Z",
                        "download_url": "https://s3.amazonaws.com/bucket/presigned-url",
                        "content_type": "text/csv",
                        "format": "csv",
                        "file_type": "reports",
                        "structure": "unified"
                    }
                ],
                "total_count": 1
            }
        } 

class Section(BaseModel):
    """A logical section of markdown content."""
    title: str
    body: str

class RenderHints(BaseModel):
    """Optional hints that help the frontend decide how to render the content."""
    preferred_renderer: Optional[str] = Field(None, description="Recommended markdown renderer e.g. react-markdown")
    css_classes: Optional[List[str]] = Field(None, description="CSS classes suggested for wrapping container")

class Content(BaseModel):
    """Primary payload delivered to the UI (usually markdown)."""
    type: str = Field("markdown", description="Payload type: markdown | html | plaintext etc.")
    body: str = Field(..., description="Main markdown content")
    summary: Optional[str] = Field(None, description="Short summary used for list/previews")
    sections: Optional[List[Section]] = Field(None, description="Optional pre-split logical sections")
    fallback_plain: Optional[str] = Field(None, description="Plain-text fallback if markdown cannot be rendered")

class SuccessResponse(BaseModel):
    """Standard wrapper for successful responses sent to the UI."""
    status: str = Field("ok", description="Response state: ok | partial")
    request_id: str = Field(..., description="Server-generated request identifier for tracing")
    generated_at: str = Field(..., description="UTC ISO timestamp when the content was produced")
    content: Content = Field(..., description="Content payload (markdown by default)")
    render_hints: Optional[RenderHints] = Field(None, description="Optional frontend rendering hints")
    meta: Optional[Dict[str, Any]] = Field(None, description="Opaque metadata for debugging / analytics")

# Simplified response model for questions endpoint
class SimpleQuestionResponse(BaseModel):
    """Simplified response model for questions endpoint - easy frontend rendering."""
    success: bool = Field(True, description="Whether the request was successful")
    markdown: str = Field(..., description="Clean markdown content ready for frontend rendering")
    session_id: str = Field(..., description="Session ID for conversation tracking")
    query: str = Field(..., description="Original user query")
    has_data: bool = Field(True, description="Whether relevant data was found")
    error: Optional[str] = Field(None, description="Error message if success=false")

class ErrorResponse(BaseModel):
    """Standardised error shape understood by the UI."""
    status: Literal["error"] = "error"
    request_id: str = Field(...)
    generated_at: str = Field(...)
    error: Dict[str, Any] = Field(..., description="Error object including code/message/partial_content etc.")

try:
    __all__
except NameError:
    __all__ = []

__all__.extend([
    "Section",
    "RenderHints",
    "Content",
    "SuccessResponse",
    "ErrorResponse",
]) 
