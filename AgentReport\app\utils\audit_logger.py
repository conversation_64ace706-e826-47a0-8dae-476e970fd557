"""Security Audit Logging

This module provides structured logging for security-related events with correlation IDs.
"""

import logging
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum


class SecurityEventType(str, Enum):
    """Types of security events to log."""
    TOKEN_CREATED = "token_created"
    TOKEN_REFRESHED = "token_refreshed"
    TOKEN_REVOKED = "token_revoked"
    TOKEN_EXPIRED = "token_expired"
    TOKEN_BLACKLISTED = "token_blacklisted"
    TOKEN_CLEANUP = "token_cleanup"
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    LOGOUT = "logout"
    OAUTH_SUCCESS = "oauth_success"
    OAUTH_FAILED = "oauth_failed"
    USER_CREATED = "user_created"
    USER_UPDATED = "user_updated"
    CONCURRENT_TOKEN_USE = "concurrent_token_use"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"


class SecurityAuditLogger:
    """Structured security audit logger with correlation IDs."""
    
    def __init__(self, logger_name: str = "security_audit"):
        """Initialize the security audit logger.
        
        Args:
            logger_name: Name of the logger instance
        """
        self.logger = logging.getLogger(logger_name)
        
        # Configure structured logging format
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - '
                'correlation_id=%(correlation_id)s - '
                'event_type=%(event_type)s - '
                'user_id=%(user_id)s - '
                'ip_address=%(ip_address)s - '
                '%(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_security_event(
        self,
        event_type: SecurityEventType,
        user_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        level: int = logging.INFO
    ) -> str:
        """Log a security event with structured data.
        
        Args:
            event_type: Type of security event
            user_id: ID of the user involved (if applicable)
            correlation_id: Correlation ID for tracking related events
            ip_address: IP address of the client
            details: Additional event details
            level: Logging level
            
        Returns:
            str: The correlation ID used for this event
        """
        if correlation_id is None:
            correlation_id = str(uuid.uuid4())
        
        # Prepare extra data for structured logging
        extra = {
            'correlation_id': correlation_id,
            'event_type': event_type.value,
            'user_id': user_id or 'unknown',
            'ip_address': ip_address or 'unknown',
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Build message
        message_parts = [f"Security event: {event_type.value}"]
        if details:
            for key, value in details.items():
                message_parts.append(f"{key}={value}")
        
        message = " | ".join(message_parts)
        
        # Log the event
        self.logger.log(level, message, extra=extra)
        
        return correlation_id
    
    def log_token_created(
        self,
        user_id: str,
        token_type: str,
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> str:
        """Log token creation event."""
        return self.log_security_event(
            SecurityEventType.TOKEN_CREATED,
            user_id=user_id,
            correlation_id=correlation_id,
            ip_address=ip_address,
            details={"token_type": token_type}
        )
    
    def log_token_refreshed(
        self,
        user_id: str,
        old_token_id: Optional[str] = None,
        new_token_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> str:
        """Log token refresh event."""
        details = {}
        if old_token_id:
            details["old_token_id"] = old_token_id
        if new_token_id:
            details["new_token_id"] = new_token_id
            
        return self.log_security_event(
            SecurityEventType.TOKEN_REFRESHED,
            user_id=user_id,
            correlation_id=correlation_id,
            ip_address=ip_address,
            details=details
        )
    
    def log_token_revoked(
        self,
        user_id: str,
        token_id: Optional[str] = None,
        reason: str = "user_logout",
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> str:
        """Log token revocation event."""
        details = {"reason": reason}
        if token_id:
            details["token_id"] = token_id
            
        return self.log_security_event(
            SecurityEventType.TOKEN_REVOKED,
            user_id=user_id,
            correlation_id=correlation_id,
            ip_address=ip_address,
            details=details
        )
    
    def log_token_cleanup(
        self,
        cleanup_type: str,
        count: int,
        correlation_id: Optional[str] = None
    ) -> str:
        """Log token cleanup event."""
        return self.log_security_event(
            SecurityEventType.TOKEN_CLEANUP,
            correlation_id=correlation_id,
            details={
                "cleanup_type": cleanup_type,
                "tokens_cleaned": count
            }
        )
    
    def log_login_success(
        self,
        user_id: str,
        auth_method: str = "password",
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> str:
        """Log successful login event."""
        return self.log_security_event(
            SecurityEventType.LOGIN_SUCCESS,
            user_id=user_id,
            correlation_id=correlation_id,
            ip_address=ip_address,
            details={"auth_method": auth_method}
        )
    
    def log_login_failed(
        self,
        email: str,
        reason: str,
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> str:
        """Log failed login event."""
        return self.log_security_event(
            SecurityEventType.LOGIN_FAILED,
            correlation_id=correlation_id,
            ip_address=ip_address,
            details={
                "email": email,
                "reason": reason
            },
            level=logging.WARNING
        )
    
    def log_oauth_success(
        self,
        user_id: str,
        provider: str,
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> str:
        """Log successful OAuth authentication."""
        return self.log_security_event(
            SecurityEventType.OAUTH_SUCCESS,
            user_id=user_id,
            correlation_id=correlation_id,
            ip_address=ip_address,
            details={"provider": provider}
        )
    
    def log_oauth_failed(
        self,
        provider: str,
        reason: str,
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> str:
        """Log failed OAuth authentication."""
        return self.log_security_event(
            SecurityEventType.OAUTH_FAILED,
            correlation_id=correlation_id,
            ip_address=ip_address,
            details={
                "provider": provider,
                "reason": reason
            },
            level=logging.WARNING
        )
    
    def log_concurrent_token_use(
        self,
        user_id: str,
        token_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> str:
        """Log concurrent token usage detection."""
        details = {}
        if token_id:
            details["token_id"] = token_id
            
        return self.log_security_event(
            SecurityEventType.CONCURRENT_TOKEN_USE,
            user_id=user_id,
            correlation_id=correlation_id,
            ip_address=ip_address,
            details=details,
            level=logging.WARNING
        )


# Global audit logger instance
audit_logger = SecurityAuditLogger()
