(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[300],{747:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>P});var s=t(95155),l=t(12115),r=t(74677),d=t(58189),i=t(74045),n=t(87914),c=t(30285),o=t(66695),m=t(62523),u=t(85057),x=t(88539),h=t(26126),b=t(46102),g=t(71007),p=t(1243),f=t(53904),v=t(40646),j=t(19946);let N=(0,j.A)("pen-line",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]),k=(0,j.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var y=t(28883),w=t(54416),A=t(4229),_=t(75525),C=t(69074),M=t(14186),E=t(69803);let L=()=>{let{getUserProfile:e}=(0,d.g)(),{user:a}=(0,i.A)(),t=(0,l.useMemo)(()=>({title:"Profile",icon:g.A}),[]);(0,n.H)(t);let[r,j]=(0,l.useState)(null),[L,S]=(0,l.useState)(!0),[P,B]=(0,l.useState)(!1),[F,T]=(0,l.useState)(!1),[U,z]=(0,l.useState)(null),[I,R]=(0,l.useState)(null),[Z,O]=(0,l.useState)({full_name:"",bio:"",email:""}),[$,q]=(0,l.useState)({}),H=(0,l.useCallback)(async()=>{S(!0),z(null);try{let a=await e();j(a),O({full_name:a.full_name||"",bio:a.bio||"",email:a.email||""})}catch(e){console.error("Failed to fetch user profile:",e),z("Failed to load profile information. Please try again.")}finally{S(!1)}},[e]);(0,l.useEffect)(()=>{H()},[H]);let D=(0,l.useCallback)(()=>{let e={};return Z.full_name.trim()?Z.full_name.trim().length<2&&(e.full_name="Full name must be at least 2 characters"):e.full_name="Full name is required",Z.email&&!Z.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)&&(e.email="Please enter a valid email address"),Z.bio.length>500&&(e.bio="Bio must be less than 500 characters"),q(e),0===Object.keys(e).length},[Z]),J=(0,l.useCallback)((e,a)=>{O(t=>({...t,[e]:a})),$[e]&&q(a=>({...a,[e]:""}))},[$]),G=(0,l.useCallback)(()=>{B(!0),z(null),R(null)},[]),V=(0,l.useCallback)(()=>{B(!1),q({}),r&&O({full_name:r.full_name||"",bio:r.bio||"",email:r.email||""})},[r]),W=(0,l.useCallback)(async()=>{if(D()){T(!0),z(null);try{await new Promise(e=>setTimeout(e,1e3)),r&&j({...r,...Z}),B(!1),R("Profile updated successfully!"),setTimeout(()=>R(null),3e3)}catch(e){console.error("Failed to update profile:",e),z("Failed to update profile. Please try again.")}finally{T(!1)}}},[Z,D,r]),K=(0,l.useCallback)(e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),[]),Y=(0,l.useCallback)(e=>{switch(e){case"google_oauth":return{label:"Google OAuth",color:"bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300"};case"github_oauth":return{label:"GitHub OAuth",color:"bg-gray-100 dark:bg-gray-900 text-gray-700 dark:text-gray-300"};case"email":return{label:"Email & Password",color:"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300"};default:return{label:"Unknown",color:"bg-gray-100 dark:bg-gray-900 text-gray-700 dark:text-gray-300"}}},[]);return L?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,s.jsx)("div",{className:"container mx-auto p-4 sm:p-6 lg:p-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,s.jsx)("div",{className:"text-center space-y-4",children:(0,s.jsxs)("div",{className:"inline-flex items-center gap-3 p-4 bg-white dark:bg-slate-900 rounded-2xl shadow-sm border",children:[(0,s.jsx)("div",{className:"p-2 rounded-xl bg-slate-200 dark:bg-slate-700 w-12 h-12 animate-pulse"}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("div",{className:"h-8 bg-slate-200 dark:bg-slate-700 rounded w-32 mb-2 animate-pulse"}),(0,s.jsx)("div",{className:"h-5 bg-slate-200 dark:bg-slate-700 rounded w-48 animate-pulse"})]})]})}),(0,s.jsxs)(o.Zp,{className:"animate-pulse",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)("div",{className:"h-6 bg-slate-200 dark:bg-slate-700 rounded w-1/4 animate-pulse"}),(0,s.jsx)("div",{className:"h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2 animate-pulse"})]}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"h-4 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"}),(0,s.jsx)("div",{className:"h-4 bg-slate-200 dark:bg-slate-700 rounded w-3/4 animate-pulse"}),(0,s.jsx)("div",{className:"h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2 animate-pulse"})]})]})]})})}):U&&!r?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,s.jsx)("div",{className:"container mx-auto p-4 sm:p-6 lg:p-8",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsx)(o.Zp,{className:"border-red-200 dark:border-red-800",children:(0,s.jsxs)(o.Wu,{className:"p-6 text-center",children:[(0,s.jsx)(p.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-red-900 dark:text-red-100 mb-2",children:"Failed to Load Profile"}),(0,s.jsx)("p",{className:"text-red-700 dark:text-red-300 mb-4",children:U}),(0,s.jsxs)(c.$,{onClick:H,variant:"outline",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Try Again"]})]})})})})}):(0,s.jsx)(b.Bc,{children:(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,s.jsx)("div",{className:"container mx-auto p-4 sm:p-6 lg:p-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[I&&(0,s.jsx)("div",{className:"p-4 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(v.A,{className:"h-5 w-5 text-green-600 dark:text-green-400 flex-shrink-0"}),(0,s.jsxs)("div",{className:"text-green-800 dark:text-green-200",children:[(0,s.jsx)("div",{className:"font-medium",children:"Success"}),(0,s.jsx)("div",{className:"text-sm",children:I})]})]})}),U&&(0,s.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0"}),(0,s.jsxs)("div",{className:"text-red-800 dark:text-red-200",children:[(0,s.jsx)("div",{className:"font-medium",children:"Error"}),(0,s.jsx)("div",{className:"text-sm",children:U})]})]})}),(0,s.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(o.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100",children:"Profile Information"}),(0,s.jsx)(o.BT,{className:"text-slate-600 dark:text-slate-400",children:"Your personal details and account information"})]}),!P&&(0,s.jsxs)(b.m_,{children:[(0,s.jsx)(b.k$,{asChild:!0,children:(0,s.jsxs)(c.$,{onClick:G,variant:"outline",size:"sm",className:"flex items-center gap-2",children:[(0,s.jsx)(N,{className:"h-4 w-4"}),"Edit"]})}),(0,s.jsx)(b.ZI,{children:(0,s.jsx)("p",{children:"Edit your profile information"})})]})]}),(0,s.jsxs)(o.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white text-2xl font-bold shadow-lg",children:(null==r?void 0:r.profile_picture_url)?(0,s.jsx)("img",{src:r.profile_picture_url,alt:"Profile",className:"w-full h-full rounded-full object-cover"}):((null==r?void 0:r.full_name)||(null==r?void 0:r.username)||"U").charAt(0).toUpperCase()}),P&&(0,s.jsxs)(b.m_,{children:[(0,s.jsx)(b.k$,{asChild:!0,children:(0,s.jsx)(c.$,{size:"sm",variant:"outline",className:"absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0",children:(0,s.jsx)(k,{className:"h-4 w-4"})})}),(0,s.jsx)(b.ZI,{children:(0,s.jsx)("p",{children:"Change profile picture"})})]})]}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100",children:(null==r?void 0:r.full_name)||(null==r?void 0:r.username)||"Unknown User"}),(0,s.jsxs)("p",{className:"text-slate-600 dark:text-slate-400",children:["@",null==r?void 0:r.username]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,s.jsxs)(h.E,{variant:"outline",className:"text-xs",children:["User ID: ",null==r?void 0:r.id.slice(0,8),"..."]}),(null==r?void 0:r.is_active)&&(0,s.jsxs)(h.E,{className:"bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800 text-xs",children:[(0,s.jsx)(v.A,{className:"w-3 h-3 mr-1"}),"Active"]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"full_name",className:"text-sm font-medium",children:"Full Name"}),P?(0,s.jsxs)("div",{children:[(0,s.jsx)(m.p,{id:"full_name",value:Z.full_name,onChange:e=>J("full_name",e.target.value),placeholder:"Enter your full name",className:$.full_name?"border-red-300 dark:border-red-700":""}),$.full_name&&(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400 mt-1",children:$.full_name})]}):(0,s.jsx)("p",{className:"text-slate-900 dark:text-slate-100 py-2",children:(null==r?void 0:r.full_name)||"Not set"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"email",className:"text-sm font-medium",children:"Email Address"}),P?(0,s.jsxs)("div",{children:[(0,s.jsx)(m.p,{id:"email",type:"email",value:Z.email,onChange:e=>J("email",e.target.value),placeholder:"Enter your email",className:$.email?"border-red-300 dark:border-red-700":""}),$.email&&(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400 mt-1",children:$.email})]}):(0,s.jsxs)("div",{className:"flex items-center gap-2 py-2",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 text-slate-500"}),(0,s.jsx)("span",{className:"text-slate-900 dark:text-slate-100",children:(null==r?void 0:r.email)||"Not set"}),(null==r?void 0:r.email_verified)&&(0,s.jsxs)(b.m_,{children:[(0,s.jsx)(b.k$,{asChild:!0,children:(0,s.jsx)(v.A,{className:"h-4 w-4 text-green-500"})}),(0,s.jsx)(b.ZI,{children:(0,s.jsx)("p",{children:"Email verified"})})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,s.jsx)(u.J,{htmlFor:"bio",className:"text-sm font-medium",children:"Bio"}),P?(0,s.jsxs)("div",{children:[(0,s.jsx)(x.T,{id:"bio",value:Z.bio,onChange:e=>J("bio",e.target.value),placeholder:"Tell us about yourself...",rows:3,className:$.bio?"border-red-300 dark:border-red-700":""}),(0,s.jsxs)("div",{className:"flex justify-between items-center mt-1",children:[$.bio?(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:$.bio}):(0,s.jsx)("div",{}),(0,s.jsxs)("p",{className:"text-xs text-slate-500 dark:text-slate-400",children:[Z.bio.length,"/500"]})]})]}):(0,s.jsx)("p",{className:"text-slate-900 dark:text-slate-100 py-2",children:(null==r?void 0:r.bio)||"No bio available"})]})]}),P&&(0,s.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-slate-200 dark:border-slate-700",children:[(0,s.jsxs)(c.$,{onClick:V,variant:"outline",disabled:F,children:[(0,s.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Cancel"]}),(0,s.jsxs)(c.$,{onClick:W,disabled:F,className:"bg-blue-600 hover:bg-blue-700",children:[F?(0,s.jsx)(f.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,s.jsx)(A.A,{className:"h-4 w-4 mr-2"}),F?"Saving...":"Save Changes"]})]})]})]}),(0,s.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,s.jsx)(_.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Account Details"]}),(0,s.jsx)(o.BT,{className:"text-slate-600 dark:text-slate-400",children:"Information about your account and authentication"})]}),(0,s.jsx)(o.Wu,{className:"space-y-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Account Created"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-slate-900 dark:text-slate-100",children:[(0,s.jsx)(C.A,{className:"h-4 w-4 text-slate-500"}),(0,s.jsx)("span",{children:(null==r?void 0:r.created_at)?K(r.created_at):"Unknown"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Last Login"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-slate-900 dark:text-slate-100",children:[(0,s.jsx)(M.A,{className:"h-4 w-4 text-slate-500"}),(0,s.jsx)("span",{children:(null==r?void 0:r.last_login)?K(r.last_login):"Unknown"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Authentication Method"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(E.A,{className:"h-4 w-4 text-slate-500"}),(0,s.jsx)(h.E,{variant:"outline",className:Y(null==r?void 0:r.auth_method).color,children:Y(null==r?void 0:r.auth_method).label})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Account Status"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(_.A,{className:"h-4 w-4 text-slate-500"}),(0,s.jsx)(h.E,{variant:"outline",className:(null==r?void 0:r.is_active)?"bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800":"bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800",children:(null==r?void 0:r.is_active)?"Active":"Inactive"})]})]})]})})]})]})})})})};var S=t(30095);function P(){return(0,s.jsx)(S.Ay,{children:(0,s.jsx)(r.A,{children:(0,s.jsx)(L,{})})})}},1243:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4229:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},14186:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},26126:(e,a,t)=>{"use strict";t.d(a,{E:()=>i});var s=t(95155);t(12115);var l=t(74466),r=t(46486);let d=(0,l.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:a,variant:t,...l}=e;return(0,s.jsx)("div",{className:(0,r.cn)(d({variant:t}),a),...l})}},28883:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},30095:(e,a,t)=>{"use strict";t.d(a,{Ay:()=>n});var s=t(95155),l=t(12115),r=t(74045),d=t(35695),i=t(45786);function n(e){let{children:a,requireAuth:t=!0,redirectTo:n="/login"}=e,{isAuthenticated:c,isLoading:o,isNewUser:m,signIn:u}=(0,r.A)(),x=(0,d.useRouter)(),h=(0,d.usePathname)(),[b,g]=(0,l.useState)(!0),[p,f]=(0,l.useState)(!1);return((0,l.useEffect)(()=>{(async()=>{if(!t||[i.bw.HOME,i.bw.LOGIN,i.bw.REGISTER,i.bw.AUTH.CALLBACK,i.bw.OAUTH.CALLBACK,i.bw.ONBOARDING].includes(h))return g(!1);if(c){if(m&&"/onboarding"!==h){console.log("New user on protected route, redirecting to onboarding"),x.push("/onboarding");return}if(!m&&"/onboarding"===h){console.log("Existing user on onboarding, redirecting to dashboard"),x.push("/dashboard");return}g(!1);return}if(!c&&!p&&!o){f(!0),console.log("Attempting automatic authentication from stored tokens...");try{await u(void 0,!1)}catch(e){console.log("Auto-authentication failed, redirecting to login"),x.push(n)}return}if(!c&&p&&!o){console.log("Not authenticated, redirecting to login"),x.push(n);return}g(!1)})()},[c,o,m,h,t,n,x,u,p]),b||o)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Checking authentication..."})]})}):t&&!c?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Authentication Required"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please sign in to access this page."}),(0,s.jsx)("button",{onClick:()=>x.push(n),className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Sign In"})]})}):(0,s.jsx)(s.Fragment,{children:a})}},38223:(e,a,t)=>{Promise.resolve().then(t.bind(t,747))},40646:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},40968:(e,a,t)=>{"use strict";t.d(a,{b:()=>i});var s=t(12115),l=t(63655),r=t(95155),d=s.forwardRef((e,a)=>(0,r.jsx)(l.sG.label,{...e,ref:a,onMouseDown:a=>{var t;a.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));d.displayName="Label";var i=d},46102:(e,a,t)=>{"use strict";t.d(a,{Bc:()=>i,ZI:()=>o,k$:()=>c,m_:()=>n});var s=t(95155),l=t(12115),r=t(89613),d=t(46486);let i=r.Kq,n=r.bL,c=r.l9,o=l.forwardRef((e,a)=>{let{className:t,sideOffset:l=4,...i}=e;return(0,s.jsx)(r.UC,{ref:a,sideOffset:l,className:(0,d.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...i})});o.displayName=r.UC.displayName},53904:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},54416:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>r});var s=t(95155);t(12115);var l=t(46486);function r(e){let{className:a,type:t,...r}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...r})}},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>n,Wu:()=>c,ZB:()=>i,Zp:()=>r,aR:()=>d});var s=t(95155);t(12115);var l=t(46486);function r(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,l.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,l.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,l.cn)("leading-none font-semibold text-white",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,l.cn)("text-muted-foreground text-sm",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,l.cn)("px-6",a),...t})}},69074:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},69803:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},75525:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>d});var s=t(95155);t(12115);var l=t(40968),r=t(46486);function d(e){let{className:a,...t}=e;return(0,s.jsx)(l.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},88539:(e,a,t)=>{"use strict";t.d(a,{T:()=>d});var s=t(95155),l=t(12115),r=t(46486);let d=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...l})});d.displayName="Textarea"}},e=>{var a=a=>e(e.s=a);e.O(0,[464,817,874,786,826,613,189,45,179,30,441,684,358],()=>a(38223)),_N_E=e.O()}]);