// Enhanced error handling utilities for authentication and onboarding

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

export interface AuthError {
  code: string;
  message: string;
  retryable: boolean;
  userMessage: string;
  timestamp: string;
}

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffFactor: 2,
};

/**
 * Exponential backoff retry utility
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG,
  operationName: string = 'operation'
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      console.log(`Attempting ${operationName} (attempt ${attempt + 1}/${config.maxRetries + 1})`);
      const result = await operation();
      
      if (attempt > 0) {
        console.log(`${operationName} succeeded after ${attempt + 1} attempts`);
      }
      
      return result;
    } catch (error) {
      lastError = error as Error;
      console.warn(`${operationName} failed on attempt ${attempt + 1}:`, error);
      
      // Don't retry on the last attempt
      if (attempt === config.maxRetries) {
        break;
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(
        config.baseDelay * Math.pow(config.backoffFactor, attempt),
        config.maxDelay
      );
      
      console.log(`Retrying ${operationName} in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  console.error(`${operationName} failed after ${config.maxRetries + 1} attempts`);
  throw lastError!;
}

/**
 * Categorize authentication errors
 */
export function categorizeAuthError(error: any): AuthError {
  const timestamp = new Date().toISOString();
  
  // Network errors
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('network') || error.message?.includes('fetch')) {
    return {
      code: 'NETWORK_ERROR',
      message: error.message || 'Network error occurred',
      retryable: true,
      userMessage: 'Network connection issue. Please check your internet connection and try again.',
      timestamp,
    };
  }
  
  // Token errors
  if (error.response?.status === 401 || error.message?.includes('token') || error.message?.includes('unauthorized')) {
    return {
      code: 'TOKEN_ERROR',
      message: error.message || 'Authentication token invalid',
      retryable: false,
      userMessage: 'Your session has expired. Please log in again.',
      timestamp,
    };
  }
  
  // Server errors (5xx)
  if (error.response?.status >= 500) {
    return {
      code: 'SERVER_ERROR',
      message: error.message || 'Server error occurred',
      retryable: true,
      userMessage: 'Server is temporarily unavailable. Please try again in a moment.',
      timestamp,
    };
  }
  
  // Email already exists (409 Conflict)
  if (error.response?.status === 409) {
    const errorData = error.response?.data?.error;
    if (errorData?.code === 'EMAIL_ALREADY_EXISTS') {
      return {
        code: 'EMAIL_ALREADY_EXISTS',
        message: errorData.message || 'Email already exists',
        retryable: false,
        userMessage: errorData.message || 'An account with this email address already exists. Please use a different email or try logging in.',
        timestamp,
      };
    }

    // Generic 409 conflict
    return {
      code: 'CONFLICT_ERROR',
      message: error.response?.data?.error?.message || error.message || 'Conflict error occurred',
      retryable: false,
      userMessage: error.response?.data?.error?.message || 'There was a conflict with your request. Please try again.',
      timestamp,
    };
  }

  // Client errors (4xx)
  if (error.response?.status >= 400 && error.response?.status < 500) {
    // Check if there's a structured error response
    const errorData = error.response?.data?.error;
    if (errorData?.message) {
      return {
        code: errorData.code || 'CLIENT_ERROR',
        message: errorData.message,
        retryable: false,
        userMessage: errorData.message,
        timestamp,
      };
    }

    return {
      code: 'CLIENT_ERROR',
      message: error.message || 'Client error occurred',
      retryable: false,
      userMessage: 'There was an issue with your request. Please try again or contact support.',
      timestamp,
    };
  }
  
  // Onboarding specific errors
  if (error.message?.includes('onboarding') || error.message?.includes('is_new_user')) {
    return {
      code: 'ONBOARDING_ERROR',
      message: error.message || 'Onboarding error occurred',
      retryable: true,
      userMessage: 'There was an issue completing your setup. Please try again.',
      timestamp,
    };
  }
  
  // Generic error
  return {
    code: 'UNKNOWN_ERROR',
    message: error.message || 'An unknown error occurred',
    retryable: true,
    userMessage: 'An unexpected error occurred. Please try again.',
    timestamp,
  };
}

/**
 * Safe API call wrapper with error handling
 */
export async function safeApiCall<T>(
  apiCall: () => Promise<T>,
  operationName: string,
  retryConfig?: Partial<RetryConfig>
): Promise<{ data?: T; error?: AuthError }> {
  try {
    const data = await retryWithBackoff(
      apiCall,
      { ...DEFAULT_RETRY_CONFIG, ...retryConfig },
      operationName
    );
    return { data };
  } catch (error) {
    const authError = categorizeAuthError(error);
    console.error(`Safe API call failed for ${operationName}:`, authError);
    return { error: authError };
  }
}

/**
 * Validate authentication response structure
 */
export function validateAuthResponse(response: any): boolean {
  if (!response || typeof response !== 'object') {
    return false;
  }
  
  // Required fields
  const requiredFields = ['access_token', 'user_id'];
  for (const field of requiredFields) {
    if (!response[field] || typeof response[field] !== 'string') {
      return false;
    }
  }
  
  // Optional but validated fields
  if (response.is_new_user !== undefined && typeof response.is_new_user !== 'boolean') {
    return false;
  }
  
  return true;
}

/**
 * Normalize authentication response with defaults
 */
export function normalizeAuthResponse(response: any): any {
  if (!validateAuthResponse(response)) {
    throw new Error('Invalid authentication response structure');
  }
  
  return {
    ...response,
    // Default is_new_user to false if not provided (for legacy users)
    is_new_user: response.is_new_user ?? false,
    // Ensure token_type is set
    token_type: response.token_type || 'bearer',
  };
}

/**
 * Check if error indicates infinite redirect loop
 */
export function isRedirectLoopError(error: any, redirectHistory: string[]): boolean {
  // Check if we've been redirecting between the same routes repeatedly
  if (redirectHistory.length >= 6) {
    const recent = redirectHistory.slice(-6);
    const uniqueRoutes = new Set(recent);
    
    // If we only have 2-3 unique routes in recent history, it's likely a loop
    return uniqueRoutes.size <= 3;
  }
  
  return false;
}

/**
 * Log error for debugging (production-safe)
 */
export function logAuthError(error: AuthError, context: string): void {
  const logData = {
    context,
    code: error.code,
    message: error.message,
    timestamp: error.timestamp,
    retryable: error.retryable,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
    url: typeof window !== 'undefined' ? window.location.href : 'unknown',
  };
  
  // In production, you might want to send this to an error tracking service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to error tracking service
    // errorTrackingService.log(logData);
    console.error('Auth Error:', logData);
  } else {
    console.error('Auth Error:', logData);
  }
}
