"""Output Agent

This module provides functionality for formatting query results and creating output files.
"""

import logging
import json
import io
import os
import uuid
import importlib.util
import asyncio
import re
from typing import Dict, List, Any, Optional, Union, AsyncGenerator
from datetime import datetime

import pandas as pd

from app.agents.base import Agent, AgentResponse
from app.utils.bedrock_client import BedrockClient
from app.services.conversational_context_service import conversational_context_service
from app.services.natural_language_response_service import (
    natural_language_response_service,
    ResponseType,
    ResponseTone
)
from app.prompts.output import NO_RESULTS_SYSTEM_PROMPT, RESULTS_SUMMARY_SYSTEM_PROMPT, SIMPLE_RESULTS_SYSTEM_PROMPT, SIMPLE_NO_RESULTS_PROMPT
from app.config import settings
from app.utils.response_formatter import ResponseFormatter, QueryType
from app.utils.error_utils import friendly_agent_errors

logger = logging.getLogger(__name__)

# Check if openpyxl is available for Excel support
EXCEL_SUPPORT = importlib.util.find_spec('openpyxl') is not None
if not EXCEL_SUPPORT:
    logger.warning("openpyxl module not found. Excel output will fall back to CSV.")

class OutputAgent(Agent):
    """Agent responsible for formatting query results and preparing responses."""
    
    def __init__(
        self, 
        agent_id: Optional[str] = None,
        output_dir: str = "output"
    ):
        """Initialize the output agent.
        
        Args:
            agent_id: Optional ID for this agent
            output_dir: Directory where output files will be stored
        """
        self.agent_id = agent_id or "output_agent"
        self.bedrock_client = BedrockClient()
        self.output_dir = output_dir

        logger.info("Running in serverless environment - using in-memory storage")
        
    async def initialize(self) -> None:
        """Initialize the agent."""
        # No specific initialization needed
        pass
        
    @friendly_agent_errors("output-formatting")
    async def process(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process SQL agent results and prepare output.
        
        Args:
            message: The message containing query results
            
        Returns:
            An AgentResponse with formatted output
        """
        # Extract information
        sql_agent_response = message.get("sql_agent_response", {})
        output_format = message.get("output_format", "csv")
        user_query = message.get("query", "")
        session_id = message.get("session_id")
        user_id = message.get("user_id")
        # Phase-2: capture user-preferred tone (defaults to conversational)
        self._current_tone = message.get("tone", "conversational")
        
        if not sql_agent_response:
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error="No SQL agent response provided"
            ).to_dict()
            
        try:
            # Check if SQL agent found any relevant information
            if not sql_agent_response.get("has_relevant_info", False):
                # No results found, generate an enhanced helpful message
                response_message = await self._generate_enhanced_no_results_message(
                    user_query, session_id
                )

                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=False,
                    data={
                        "message": response_message,
                        "query": user_query
                    }
                ).to_dict()
            
            # Process results
            results = sql_agent_response.get("data", {}).get("results", [])
            query = sql_agent_response.get("data", {}).get("query", user_query)
            
            if not results:
                response_message = await self._generate_enhanced_no_results_message(
                    query, session_id
                )

                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=False,
                    data={
                        "message": response_message,
                        "query": query
                    }
                ).to_dict()
            
            # Determine if there are errors in the results
            errors = [r for r in results if "error" in r]
            successful_results = [r for r in results if "error" not in r]
            
            # Extract SQL queries from results for inclusion in response
            sql_queries = {}
            for result in results:
                db_name = result.get("database_name", "database")
                sql_query = result.get("query", "")
                if sql_query:
                    sql_queries[db_name] = sql_query.strip()  # Ensure clean query string
            
            # Generate files for successful results if needed
            output_files = []
            if successful_results:
                for result in successful_results:
                    db_name = result.get("database_name", "database")
                    
                    # Use the file information already generated by SQL agent
                    file_path = result.get("file_path", "")
                    file_format = result.get("format", "csv")
                    
                    if file_path:
                        # SQL agent already uploaded to S3, use that information
                        output_files.append({
                            "database_name": db_name,
                            "file_path": file_path,
                            "format": file_format
                        })
                    else:
                        # Fallback if no file was generated
                        output_files.append({
                            "database_name": db_name,
                            "file_path": "",
                            "format": "csv",
                            "note": "No file generated - data may be empty"
                        })
            
            # Update conversational context with query results (user_id may be absent)
            if session_id and successful_results:
                await self._update_conversational_context(
                    session_id, user_id, query, successful_results, sql_queries
                )

            # Extract Phase-3 analysis (statistical insights, business context, comparative)
            phase3_analysis = (
                sql_agent_response.get("phase3_analysis")
                or sql_agent_response.get("metadata", {}).get("phase3_analysis")
            )

            # Generate enhanced natural language summary (now with Phase-3 context)
            summary = await self._generate_enhanced_summary(
                query,
                successful_results,
                errors,
                output_format,
                session_id,
                user_id,
                phase3_analysis=phase3_analysis,
            )
            
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=True,
                data={
                    "summary": summary,
                    "query": query,
                    "output_files": output_files,
                    "errors": [e.get("error") for e in errors] if errors else [],
                    "sql_queries": sql_queries,  # Include the SQL queries in the response
                },
                # Add SQL queries to metadata as well for better visibility
                metadata={
                    "sql_queries": sql_queries
                }
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Error in output agent: {str(e)}")
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error=f"Error formatting results: {str(e)}"
            ).to_dict()

    @friendly_agent_errors("output-formatting")
    async def process_stream(self, message: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Process SQL agent results with streaming support for the final summary generation."""

        def create_event(event_type: str, agent: str, data: Any) -> Dict[str, Any]:
            return {
                "type": event_type,
                "agent": agent,
                "data": data,
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }

        # Extract parameters
        sql_agent_response = message.get("sql_agent_response", {})
        output_format = message.get("output_format", "csv")
        user_query = message.get("query", "")
        session_id = message.get("session_id")
        user_id = message.get("user_id")
        self._current_tone = message.get("tone", "conversational")

        # Phase-3 context (if any) – used later when generating summary
        phase3_analysis = (
            sql_agent_response.get("phase3_analysis")
            or sql_agent_response.get("metadata", {}).get("phase3_analysis")
        )

        if not sql_agent_response:
            yield create_event("error", self.agent_id, {"message": "No SQL agent response provided", "status": "failed"})
            return

        try:
            # Check if SQL agent found any relevant information
            if not sql_agent_response.get("has_relevant_info", False):
                # No results found, generate a helpful message with streaming
                yield create_event("agent_status", self.agent_id, {"message": "🔍 Analyzing query results...", "status": "processing"})

                # Stream the enhanced no-results message generation
                async for event in self._stream_enhanced_no_results_message(user_query, session_id):
                    yield event
                return

            # Process results
            results = sql_agent_response.get("data", {}).get("results", [])
            query = sql_agent_response.get("data", {}).get("query", user_query)

            if not results:
                yield create_event("agent_status", self.agent_id, {"message": "🔍 Analyzing query results...", "status": "processing"})

                async for event in self._stream_enhanced_no_results_message(query, session_id):
                    yield event
                return

            # Determine if there are errors in the results
            errors = [r for r in results if "error" in r]
            successful_results = [r for r in results if "error" not in r]

            # Extract SQL queries from results for inclusion in response
            sql_queries = {}
            for result in results:
                db_name = result.get("database_name", "database")
                sql_query = result.get("query", "")
                if sql_query:
                    sql_queries[db_name] = sql_query.strip()

            # Generate files for successful results if needed
            output_files = []
            if successful_results:
                for result in successful_results:
                    db_name = result.get("database_name", "database")

                    # Use the file information already generated by SQL agent
                    file_path = result.get("file_path", "")
                    file_format = result.get("format", "csv")

                    if file_path:
                        # SQL agent already uploaded to S3, use that information
                        output_files.append({
                            "database_name": db_name,
                            "file_path": file_path,
                            "format": file_format
                        })
                    else:
                        # Fallback if no file was generated
                        output_files.append({
                            "database_name": db_name,
                            "file_path": "",
                            "format": "csv",
                            "note": "No file generated - data may be empty"
                        })

            # Stream the summary generation
            yield create_event("agent_status", self.agent_id, {"message": "✨ Generating summary...", "status": "processing"})

            async for event in self._stream_results_summary(query, successful_results, errors, output_format, phase3_analysis):
                yield event

            # Send final result
            final_data = {
                "query": query,
                "output_files": output_files,
                "errors": [e.get("error") for e in errors] if errors else [],
                "sql_queries": sql_queries,
            }

            yield create_event("agent_result", self.agent_id, final_data)

        except Exception as e:
            logger.error(f"Error in output agent streaming: {str(e)}")
            yield create_event("error", self.agent_id, {"message": f"❌ Error formatting results: {str(e)}", "status": "failed"})

    async def _stream_no_results_message(self, query: str) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream the generation of a no-results message."""

        def create_event(event_type: str, agent: str, data: Any) -> Dict[str, Any]:
            return {
                "type": event_type,
                "agent": agent,
                "data": data,
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }

        system_prompt = NO_RESULTS_SYSTEM_PROMPT

        user_prompt = f"""The user asked this question about data in their databases:
"{query}"

No relevant results were found in any of their connected databases.
Create a brief, helpful message explaining possible reasons for the lack of results and suggesting alternatives."""

        try:
            # Accumulate complete response
            complete_response = ""

            # Step 1: Generate complete AI response first (non-streaming to avoid nested structures)
            complete_response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.7
            )

            # Step 2: Extract clean message from AI response
            clean_message = self._extract_clean_message(complete_response)

            # Step 3: Stream individual words as clean tokens
            async for token_event in self._stream_clean_tokens(clean_message, create_event):
                yield token_event

            # Step 4: Send completion event with clean message (single event)
            yield create_event("token_complete", self.agent_id, {
                "complete_response": clean_message,
                "conversational_answer": clean_message
            })

        except Exception as e:
            logger.error(f"Error streaming no-results message: {str(e)}")
            yield create_event("token_error", self.agent_id, {"message": f"Error generating message: {str(e)}", "status": "failed"})

    async def _stream_results_summary(
        self,
        query: str,
        successful_results: List[Dict[str, Any]],
        errors: List[Dict[str, Any]],
        output_format: str,
        phase3_analysis: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream the generation of a results summary."""

        def create_event(event_type: str, agent: str, data: Any) -> Dict[str, Any]:
            return {
                "type": event_type,
                "agent": agent,
                "data": data,
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }

        # Prepare data about successful results
        result_descriptions = []
        data_samples = []

        # Check if this is a list/ranking query
        is_list_query = self._is_list_query(query)

        for result in successful_results:
            db_name = result.get("database_name", "database")

            # Get data from SQL agent format
            columns = result.get("columns", [])
            preview = result.get("preview", [])

            # Add shape information
            if columns:
                col_count = len(columns)
                row_count = len(preview) if preview else "unknown"
                result_descriptions.append(f"{db_name}: {row_count} rows and {col_count} columns")

                # For list queries, show complete data; for others, show sample
                if preview and len(preview) > 0:
                    if is_list_query:
                        # Show all data for list queries (up to reasonable limit)
                        data_to_show = preview[:20] if len(preview) > 20 else preview
                        data_samples.append(f"{db_name} complete results: {json.dumps(data_to_show, indent=2)}")
                        if len(preview) > 20:
                            data_samples.append(f"({len(preview)} total rows available)")
                    else:
                        # Show sample for analytical queries
                        sample_row = preview[0]
                        data_samples.append(f"{db_name} sample: {json.dumps(sample_row)}")
            else:
                result_descriptions.append(f"{db_name}: No data structure available")

        # Prepare error descriptions
        error_descriptions = []
        for error_result in errors:
            db_name = error_result.get("database_name", "database")
            error_msg = error_result.get("error", "Unknown error")
            error_descriptions.append(f"{db_name}: {error_msg}")

        # Craft prompt for LLM
        system_prompt = RESULTS_SUMMARY_SYSTEM_PROMPT

        user_prompt = f"""User query: \"{query}\"\n\nResults from databases:\n{', '.join(result_descriptions) if result_descriptions else 'No successful results'}\n\nSample data:\n{', '.join(data_samples) if data_samples else 'No sample data available'}\n\nErrors:\n{', '.join(error_descriptions) if error_descriptions else 'No errors'}\n\nOutput format: {output_format}"""

        if phase3_analysis:
            # Append a concise summary of Phase-3 insights to guide the LLM
            import json as _json
            try:
                phase3_brief = _json.dumps(phase3_analysis.get("insights_summary", phase3_analysis)[:1000])
            except Exception:
                phase3_brief = str(phase3_analysis)[:1000]
            user_prompt += f"\n\nAdditional analytical context (Phase-3): {phase3_brief}"

        # Add specific instructions based on query type
        if is_list_query:
            user_prompt += "\n\nThis is a list/ranking query. Present the COMPLETE list of results with all items and their values using proper markdown formatting:\n- Use a clear header (## Title)\n- Use numbered list format (1. **Item** - Value) for rankings\n- For multi-column data, consider using markdown tables\n- Add key insights with bullet points (### Key Observations:)\n- Use **bold** for important numbers, names, and key findings\n- Ensure proper spacing and readability with line breaks\n- End with a brief summary or implication\nDo not just mention one example - show the full list that answers the user's question."
        else:
            user_prompt += "\n\nProvide a concise summary focusing on patterns and insights in the data that directly answers the user's question. Use markdown formatting for better readability:\n- Use headers (## or ###) for main sections\n- Use **bold** for key numbers and findings\n- Use bullet points for insights\n- Ensure proper spacing and structure"

        try:
            # Accumulate the complete response for final delivery
            complete_response = ""

            # Step 1: Generate complete AI response first (non-streaming to avoid nested structures)
            complete_response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.7
            )

            # Step 2: Extract clean message from AI response
            clean_message = self._extract_clean_message(complete_response)

            # Step 3: Stream individual words as clean tokens
            async for token_event in self._stream_clean_tokens(clean_message, create_event):
                yield token_event

            # Step 4: Send completion event with clean message (single event)
            yield create_event("token_complete", self.agent_id, {
                "complete_response": clean_message,
                "conversational_answer": clean_message
            })

        except Exception as e:
            logger.error(f"Error streaming results summary: {str(e)}")
            yield create_event("token_error", self.agent_id, {"message": f"Error generating summary: {str(e)}", "status": "failed"})
            
    async def _generate_no_results_message(self, query: str) -> str:
        """Generate a helpful message when no results are found.
        
        Args:
            query: The user's query
            
        Returns:
            A helpful message
        """
        system_prompt = NO_RESULTS_SYSTEM_PROMPT
        
        user_prompt = f"""The user asked this question about data in their databases:
"{query}"

No relevant results were found in any of their connected databases.
Create a brief, helpful message explaining possible reasons for the lack of results and suggesting alternatives."""
        
        # Call Bedrock
        response = await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.7
        )
        
        # Since we're no longer asking for JSON format, use the response directly
        if response and response.strip():
            return ResponseFormatter.no_results(query, response.strip(), tone=getattr(self, "_current_tone", "conversational"))
        else:
            logger.warning("Empty response from LLM for no results message")
            return ResponseFormatter.no_results(query, "No results found for your query.", tone=getattr(self, "_current_tone", "conversational"))
        
    async def _generate_results_summary(
        self, 
        query: str, 
        successful_results: List[Dict[str, Any]], 
        errors: List[Dict[str, Any]],
        output_format: str
    ) -> str:
        """Generate a summary of the query results.
        
        Args:
            query: The user's query
            successful_results: Results that were successfully retrieved
            errors: Errors that occurred during query execution
            output_format: The output format
            
        Returns:
            A summary of the results
        """
        # Prepare data about successful results
        result_descriptions = []
        data_samples = []
        
        for result in successful_results:
            db_name = result.get("database_name", "database")
            
            # Get data from SQL agent format
            columns = result.get("columns", [])
            preview = result.get("preview", [])
            size_bytes = result.get("size_bytes", 0)
            
            # Add shape information
            if columns:
                col_count = len(columns)
                row_count = len(preview) if preview else "unknown"
                result_descriptions.append(f"{db_name}: {row_count} rows and {col_count} columns")
                
                # Add sample data for better context
                if preview and len(preview) > 0:
                    sample_row = preview[0]
                    data_samples.append(f"{db_name} sample: {json.dumps(sample_row)}")
            else:
                result_descriptions.append(f"{db_name}: No data structure available")
        
        # Prepare error descriptions
        error_descriptions = []
        for error_result in errors:
            db_name = error_result.get("database_name", "database")
            error_msg = error_result.get("error", "Unknown error")
            error_descriptions.append(f"{db_name}: {error_msg}")
            
        # Craft prompt for LLM
        system_prompt = RESULTS_SUMMARY_SYSTEM_PROMPT
        
        user_prompt = f"""User query: \"{query}\"\n\nResults from databases:\n{', '.join(result_descriptions) if result_descriptions else 'No successful results'}\n\nSample data:\n{', '.join(data_samples) if data_samples else 'No sample data available'}\n\nErrors:\n{', '.join(error_descriptions) if error_descriptions else 'No errors'}\n\nOutput format: {output_format}\n\nProvide a concise summary focusing on patterns and insights in the data that directly answers the user's question."""
        
        # Call Bedrock
        response = await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.7
        )
        
        # Since we're no longer asking for JSON format, use the response directly
        if response and response.strip():
            # Detect query type for better formatting
            query_type = ResponseFormatter.detect_query_type(query)
            # Generate statistical summary from results
            statistical_summary = self._generate_statistical_summary(successful_results)
            return ResponseFormatter.format_data_response(
                summary_text=response.strip(),
                query_type=query_type,
                tone=getattr(self, "_current_tone", "conversational"),
                statistical_summary=statistical_summary
            )
        else:
            logger.warning("Empty response from LLM for results summary")
            return ResponseFormatter.format_data_response(
                summary_text="No summary available.",
                tone=getattr(self, "_current_tone", "conversational")
            )
        
    async def _create_excel_file(
        self, 
        data: Dict[str, Any], 
        db_name: str, 
        query: str
    ) -> str:
        """Create an Excel file from query results.
        
        Args:
            data: The query result data
            db_name: Name of the database
            query: The user's query
            
        Returns:
            Path to the created file
        """
            
        if not EXCEL_SUPPORT:
            logger.warning("Excel support not available (openpyxl missing). Skipping Excel file creation.")
            return ""
            
        if data.get("format") != "excel" and data.get("format") != "csv":
            logger.warning(f"Unexpected data format for Excel conversion: {data.get('format')}")
            return ""
            
        try:
            # Create DataFrame
            columns = data.get("columns", [])
            rows = data.get("data", [])
            df = pd.DataFrame(rows, columns=columns)
            
            # Generate file path
            filename = f"{db_name}_{uuid.uuid4().hex[:8]}.xlsx"
            file_path = os.path.join(self.output_dir, filename)
            
            # Write to Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name="Results", index=False)
                
                # Create a metadata sheet
                metadata = pd.DataFrame([
                    {"Key": "Query", "Value": query},
                    {"Key": "Database", "Value": db_name},
                    {"Key": "Row Count", "Value": len(rows)},
                    {"Key": "Column Count", "Value": len(columns)}
                ])
                metadata.to_excel(writer, sheet_name="Metadata", index=False)
                
            return file_path
            
        except (OSError, PermissionError) as e:
            logger.error(f"File system error creating Excel file (likely serverless environment): {str(e)}")
            return ""
        except Exception as e:
            logger.error(f"Error creating Excel file: {str(e)}")
            # Return empty string to trigger fallback to CSV
            return ""
            
    async def _create_csv_file(
        self, 
        data: Dict[str, Any], 
        db_name: str, 
        query: str
    ) -> str:
        """Create a CSV file from query results.
        
        Args:
            data: The query result data
            db_name: Name of the database
            query: The user's query
            
        Returns:
            Path to the created file
        """
            
        if data.get("format") not in ["csv", "excel"]:
            logger.warning(f"Unexpected data format for CSV conversion: {data.get('format')}")
            return ""
            
        try:
            # Create DataFrame
            columns = data.get("columns", [])
            rows = data.get("data", [])
            df = pd.DataFrame(rows, columns=columns)
            
            # Generate file path
            filename = f"{db_name}_{uuid.uuid4().hex[:8]}.csv"
            file_path = os.path.join(self.output_dir, filename)
            
            # Write to CSV
            df.to_csv(file_path, index=False)
                
            return file_path
            
        except (OSError, PermissionError) as e:
            logger.error(f"File system error creating CSV file (likely serverless environment): {str(e)}")
            return ""
        except Exception as e:
            logger.error(f"Error creating CSV file: {str(e)}")
            return ""

    async def _store_in_memory(
        self, 
        data: Dict[str, Any], 
        db_name: str, 
        query: str
    ) -> str:
        """        
        Args:
            data: The query result data
            db_name: Name of the database
            query: The user's query
            
        Returns:
            Unique ID for the stored data
        """
        # Generate unique ID
        data_id = f"{db_name}_{uuid.uuid4().hex[:8]}"
        
        # Store data with metadata
        self.in_memory_files[data_id] = {
            "data": data,
            "metadata": {
                "query": query,
                "database": db_name,
                "timestamp": str(uuid.uuid1())
            }
        }
        
        return data_id

    def _is_list_query(self, query: str) -> bool:
        """Detect if the query is asking for a list or ranking."""
        query_lower = query.lower()
        list_indicators = [
            'top ', 'bottom ', 'most ', 'least ', 'highest ', 'lowest ',
            'best ', 'worst ', 'first ', 'last ', 'all ', 'list of',
            'show me all', 'give me all', 'what are the', 'top 10', 'top 5',
            'top ten', 'top five'
        ]
        return any(indicator in query_lower for indicator in list_indicators)

    def _extract_clean_message(self, response: str) -> str:
        """Extract clean readable message from AI response.

        Args:
            response: Raw AI response that may contain JSON or plain text

        Returns:
            Clean readable message text
        """
        if not response or not response.strip():
            return "No response generated."

        clean_message = response.strip()

        # Handle nested JSON structures that contain token stream data
        if "{'type': 'token_stream'" in clean_message or '{"type": "token_stream"' in clean_message:
            # This is a nested token stream structure - extract the actual message
            # Look for patterns like "summary": "actual message" or similar

            # Try to find summary field in the nested structure
            summary_match = re.search(r'"summary":\s*"([^"]*)"', clean_message)
            if summary_match:
                clean_message = summary_match.group(1)
            else:
                # Try to find message field
                message_match = re.search(r'"message":\s*"([^"]*)"', clean_message)
                if message_match:
                    clean_message = message_match.group(1)
                else:
                    # Fallback: try to extract any readable text that's not JSON structure
                    # Remove the token stream structure and look for actual content
                    clean_parts = []
                    lines = clean_message.split('\n')
                    for line in lines:
                        line = line.strip()
                        if (line and
                            not line.startswith('{') and
                            not line.startswith('"type":') and
                            not line.startswith('"token":') and
                            not line.startswith('"accumulated_text":') and
                            not line.endswith(',') and
                            not line == '}'):
                            clean_parts.append(line)

                    if clean_parts:
                        clean_message = ' '.join(clean_parts)
                    else:
                        clean_message = "Query results processed successfully."

        # Try to parse as JSON if it looks like standard JSON
        elif clean_message.startswith('{') and clean_message.endswith('}'):
            try:
                import json as json_lib
                parsed = json_lib.loads(clean_message)
                if isinstance(parsed, dict):
                    # Look for common message fields
                    for field in ["summary", "message", "answer", "response", "content"]:
                        if field in parsed and parsed[field]:
                            clean_message = str(parsed[field])
                            break
                    else:
                        # If no standard field found, create a readable summary
                        clean_message = "Query results processed successfully."
            except (json.JSONDecodeError, ValueError):
                # If JSON parsing fails, use raw text
                pass

        # Clean up any remaining artifacts
        clean_message = clean_message.replace("\\n", " ").replace("\\t", " ")
        clean_message = " ".join(clean_message.split())  # Normalize whitespace

        # Remove any remaining JSON artifacts
        clean_message = re.sub(r'[{}"\[\]]', '', clean_message)
        clean_message = re.sub(r'\s+', ' ', clean_message).strip()

        return clean_message if clean_message else "Response generated successfully."

    async def generate_simple_markdown_response(
        self,
        query: str,
        successful_results: List[Dict[str, Any]],
        errors: List[Dict[str, Any]],
        session_id: Optional[str] = None
    ) -> str:
        """Generate a simple, clean markdown response for easy frontend rendering with follow-up awareness.

        Args:
            query: The user's query (may be enhanced for follow-ups)
            successful_results: Results that were successfully retrieved
            errors: Errors that occurred during query execution
            session_id: Session ID for conversational context

        Returns:
            Clean markdown string ready for frontend rendering
        """
        # Handle no results case
        if not successful_results:
            return await self._generate_simple_no_results_message(query)

        # Prepare data context for LLM
        data_context = self._prepare_simple_data_context(successful_results)

        # Detect if this is a follow-up question and get context
        follow_up_context = await self._get_follow_up_context(query, session_id)

        # Create enhanced user prompt with follow-up awareness
        user_prompt = self._create_follow_up_aware_prompt(query, data_context, follow_up_context)

        # Generate response using simple prompt
        try:
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=SIMPLE_RESULTS_SYSTEM_PROMPT,
                temperature=0.7
            )

            if response and response.strip():
                return response.strip()
            else:
                return "I found the data you requested, but couldn't generate a summary. Please try rephrasing your question."

        except Exception as e:
            logger.error(f"Error generating simple markdown response: {str(e)}")
            return f"I found some data related to your query about '{query}', but encountered an error while analyzing it. Please try again."

    async def _get_follow_up_context(self, query: str, session_id: Optional[str]) -> Dict[str, Any]:
        """Get conversational context for follow-up questions."""
        if not session_id:
            return {}

        try:
            # Import here to avoid circular imports
            from app.services.conversational_context_service import conversational_context_service

            # Get context data and references
            context_data = await conversational_context_service.extract_context_for_query(session_id, query)
            references = await conversational_context_service.identify_references_in_query(session_id, query)

            return {
                "is_follow_up": references.get("needs_context", False),
                "follow_up_type": references.get("follow_up_type"),
                "previous_query": references.get("previous_query_reference"),
                "recent_queries": context_data.get("recent_queries", [])
            }
        except Exception as e:
            logger.warning(f"Failed to get follow-up context: {e}")
            return {}

    def _create_follow_up_aware_prompt(
        self,
        query: str,
        data_context: str,
        follow_up_context: Dict[str, Any]
    ) -> str:
        """Create a prompt that's aware of follow-up question context."""

        base_prompt = f"""User asked: "{query}"

Data found:
{data_context}"""

        # Add follow-up context if this is a follow-up question
        if follow_up_context.get("is_follow_up"):
            follow_up_type = follow_up_context.get("follow_up_type")
            previous_query = follow_up_context.get("previous_query")

            if follow_up_type == "comparative" and previous_query:
                base_prompt += f"""

CONTEXT: This is a follow-up question comparing results to a previous query: "{previous_query.get('query_text', '')}"
The user is asking for a comparison between the current results and what was found previously."""

            elif follow_up_type == "result_analysis" and previous_query:
                base_prompt += f"""

CONTEXT: This is a follow-up question analyzing results from a previous query: "{previous_query.get('query_text', '')}"
The user wants to understand or analyze the data that was previously retrieved."""

            elif follow_up_type == "pronoun_reference":
                base_prompt += f"""

CONTEXT: This is a follow-up question referring to data from previous queries in this conversation.
The user is asking about specific data that was discussed earlier."""

        # Add instructions
        base_prompt += """

Please provide a complete, confident answer to their question using the data above.
- Present all the relevant data that answers their question
- Don't add disclaimers about "partial data" or "sample data" - this is the actual query result
- If they asked for "top 10" and you have 10 results, show all 10
- Focus on the key findings and insights from the data
- Be direct and helpful"""

        # Add follow-up specific instructions
        if follow_up_context.get("is_follow_up"):
            base_prompt += """
- Since this is a follow-up question, provide context about how this relates to the previous discussion
- Make clear connections between current results and what was discussed before
- Use conversational language that acknowledges the ongoing discussion"""

        return base_prompt

    async def _generate_simple_no_results_message(self, query: str) -> str:
        """Generate a simple no results message."""
        user_prompt = f"""The user asked: "{query}"

No data was found in their connected databases that matches this query."""

        try:
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=SIMPLE_NO_RESULTS_PROMPT,
                temperature=0.7
            )

            if response and response.strip():
                return response.strip()
            else:
                return f"I couldn't find any data matching your query about '{query}'. This might be because the data doesn't exist in your connected databases, or it might be stored under different terms. Try rephrasing your question or check if the relevant database is connected."

        except Exception as e:
            logger.error(f"Error generating simple no results message: {str(e)}")
            return f"I couldn't find any data matching your query about '{query}'. Please try rephrasing your question or check your database connections."

    def _prepare_simple_data_context(self, successful_results: List[Dict[str, Any]]) -> str:
        """Prepare a simple data context string for the LLM."""
        context_parts = []

        for result in successful_results:
            db_name = result.get("database_name", "database")
            columns = result.get("columns", [])
            preview = result.get("preview", [])

            if preview:
                # Show all available data (not just a sample)
                context_parts.append(f"From {db_name}:")
                context_parts.append(f"Columns: {', '.join(columns)}")
                context_parts.append(f"Query results ({len(preview)} rows):")

                # Reduce preview size to speed up downstream LLM calls and shrink payloads
                display_limit = min(10, len(preview))  # Show up to 10 rows which is usually enough context

                for i, row in enumerate(preview[:display_limit]):
                    row_str = ", ".join([f"{k}: {v}" for k, v in row.items()])
                    context_parts.append(f"  Row {i+1}: {row_str}")

                if len(preview) > display_limit:
                    context_parts.append(f"  ... and {len(preview) - display_limit} more rows")

                context_parts.append("")  # Empty line between databases

        return "\n".join(context_parts)

    async def _stream_clean_tokens(self, message: str, create_event_func) -> AsyncGenerator[Any, None]:
        """Stream individual words as clean tokens.

        Args:
            message: Clean message text to tokenize
            create_event_func: Function to create SSE events
        """
        if not message or not message.strip():
            # Send a default message if empty
            message = "Response completed."

        # Split into individual words
        words = message.split()

        # Stream each word as a clean token
        for i, word in enumerate(words):
            # Add space after word except for the last one
            token_to_send = word + (" " if i < len(words) - 1 else "")

            # Create clean token event - NO nested data structure
            yield create_event_func("token_stream", self.agent_id, {"token": token_to_send})

            # Add realistic delay between tokens
            await asyncio.sleep(0.03)  # 30ms delay for realistic streaming

    async def _update_conversational_context(
        self,
        session_id: str,
        user_id: Optional[str],
        query: str,
        successful_results: List[Dict[str, Any]],
        sql_queries: Dict[str, str]
    ) -> None:
        """Update conversational context with query results."""
        try:
            # Extract table information from results
            for result in successful_results:
                database_id = result.get("database_id", "unknown")
                database_name = result.get("database_name", "unknown")
                columns = result.get("columns", [])
                preview = result.get("preview", [])

                # Try to extract table name from SQL query
                table_name = "unknown"
                if database_name in sql_queries:
                    sql_query = sql_queries[database_name].lower()
                    # Simple extraction - look for "from table_name"
                    import re
                    match = re.search(r'from\s+(\w+)', sql_query)
                    if match:
                        table_name = match.group(1)

                # Update table context
                await conversational_context_service.update_table_context(
                    session_id=session_id,
                    table_name=table_name,
                    database_id=database_id,
                    columns_mentioned=columns,
                    sample_data=preview[0] if preview else None,
                    row_count=len(preview) if preview else None
                )

            # Add query context
            tables_involved = []
            key_findings = []

            # Extract tables from SQL queries
            for db_name, sql_query in sql_queries.items():
                import re
                matches = re.findall(r'from\s+(\w+)', sql_query.lower())
                tables_involved.extend(matches)

            # Generate key findings from preview data
            for result in successful_results:
                preview = result.get("preview", [])
                if preview:
                    key_findings.append(f"Found {len(preview)} records in {result.get('database_name', 'database')}")

            await conversational_context_service.add_query_context(
                session_id=session_id,
                query_text=query,
                sql_generated="; ".join(sql_queries.values()),
                result_summary=f"Retrieved data from {len(successful_results)} database(s)",
                tables_involved=list(set(tables_involved)),
                key_findings=key_findings
            )

        except Exception as e:
            logger.warning(f"Failed to update conversational context: {e}")

    def _generate_statistical_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate statistical summary from query results."""
        if not results:
            return {}

        stats = {}
        total_records = 0
        numeric_columns = []

        for result in results:
            preview = result.get("preview", [])
            columns = result.get("columns", [])
            total_records += len(preview)

            # Find numeric columns and calculate basic stats
            for col in columns:
                values = []
                for row in preview:
                    val = row.get(col)
                    if isinstance(val, (int, float)):
                        values.append(val)

                if values and len(values) > 1:
                    avg = sum(values) / len(values)
                    min_val = min(values)
                    max_val = max(values)
                    numeric_columns.append({
                        'column': col,
                        'average': round(avg, 2),
                        'min': min_val,
                        'max': max_val,
                        'range': f"{min_val}-{max_val}"
                    })

        if total_records > 0:
            stats['total_records'] = total_records

        if numeric_columns:
            # For the main numeric column (usually the first one), add to stats
            main_col = numeric_columns[0]
            stats['average'] = main_col['average']
            stats['range'] = main_col['range']

            # Add distribution info if we have multiple values
            if len(numeric_columns) > 1:
                stats['distribution'] = f"Values vary across {len(numeric_columns)} numeric attributes"

        return stats

    async def _generate_enhanced_summary(
        self,
        query: str,
        successful_results: List[Dict[str, Any]],
        errors: List[Dict[str, Any]],
        output_format: str,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        phase3_analysis: Optional[str] = None
    ) -> str:
        """Generate an enhanced natural language summary using the NL response service."""

        try:
            # Use the natural language response service for enhanced responses
            enhanced_response = await natural_language_response_service.generate_enhanced_response(
                query=query,
                results=successful_results,
                context={
                    "errors": errors,
                    "output_format": output_format,
                    "user_id": user_id,
                    "phase3_analysis": phase3_analysis
                },
                response_type=ResponseType.DATA_SUMMARY,
                tone=ResponseTone.CONVERSATIONAL,
                session_id=session_id
            )

            # Extract key insights and follow-up suggestions for enhanced formatting
            key_insights = []
            if enhanced_response.data_story and enhanced_response.data_story.key_insights:
                key_insights = enhanced_response.data_story.key_insights[:3]

            follow_up_suggestions = enhanced_response.follow_up_suggestions[:3] if enhanced_response.follow_up_suggestions else []

            # Detect query type for specialized formatting
            query_type = ResponseFormatter.detect_query_type(query)

            # Create executive summary if we have data story
            executive_summary = None
            if enhanced_response.data_story and enhanced_response.data_story.headline:
                executive_summary = enhanced_response.data_story.headline

            # Generate statistical summary from results
            statistical_summary = self._generate_statistical_summary(successful_results)

            # Use enhanced formatting
            return ResponseFormatter.format_data_response(
                summary_text=enhanced_response.main_response,
                query_type=query_type,
                tone=getattr(self, "_current_tone", "conversational"),
                executive_summary=executive_summary,
                key_insights=key_insights,
                follow_up_suggestions=follow_up_suggestions,
                statistical_summary=statistical_summary
            )

        except Exception as e:
            logger.warning(f"Enhanced summary generation failed, falling back to basic: {e}")
            # Fallback to original method
            return await self._generate_results_summary(query, successful_results, errors, output_format)

    async def _generate_enhanced_no_results_message(
        self,
        query: str,
        session_id: Optional[str] = None
    ) -> str:
        """Generate an enhanced no-results message using the NL response service."""

        try:
            # Use the natural language response service for enhanced no-results responses
            enhanced_response = await natural_language_response_service.generate_enhanced_response(
                query=query,
                results=[],
                context={},
                response_type=ResponseType.NO_RESULTS,
                tone=ResponseTone.CONVERSATIONAL,
                session_id=session_id
            )

            # Use enhanced no-results formatting
            return ResponseFormatter.no_results(
                query=query,
                explanation=enhanced_response.main_response,
                tone=getattr(self, "_current_tone", "conversational")
            )

        except Exception as e:
            logger.warning(f"Enhanced no-results message generation failed, falling back to basic: {e}")
            # Fallback to original method
            return await self._generate_no_results_message(query)

    async def _stream_enhanced_no_results_message(
        self,
        query: str,
        session_id: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream the generation of an enhanced no-results message."""

        def create_event(event_type: str, agent: str, data: Any) -> Dict[str, Any]:
            return {
                "type": event_type,
                "agent": agent,
                "data": data,
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }

        try:
            # Generate enhanced no-results message
            enhanced_message = await self._generate_enhanced_no_results_message(query, session_id)

            # Extract clean message
            clean_message = self._extract_clean_message(enhanced_message)

            # Stream individual words as clean tokens
            async for token_event in self._stream_clean_tokens(clean_message, create_event):
                yield token_event

            # Send completion event with clean message (single event)
            yield create_event("token_complete", self.agent_id, {
                "complete_response": clean_message,
                "conversational_answer": clean_message
            })

        except Exception as e:
            logger.error(f"Error streaming enhanced no-results message: {e}")
            # Fallback to original streaming method
            async for event in self._stream_no_results_message(query):
                yield event
