"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
  Dashboard as DashboardType,
  ChartWidget as ChartWidgetType,
  CHART_CONFIG,
  DashboardNavigationState,
  CreateDashboardRequest,
  UpdateDashboardRequest
} from '@/types';
import { useApi } from '@/providers/ApiContext';
import { usePageTitle } from '@/hooks/usePageTitle';
import { usePageHeader } from '@/providers/PageHeaderContext';
import { LayoutDashboard } from 'lucide-react';
import DashboardList from './DashboardList';
import DashboardView from './DashboardView';

const Dashboard: React.FC = () => {
  // Navigation state
  const [navigationState, setNavigationState] = useState<DashboardNavigationState>({
    currentView: 'list',
    selectedDashboard: null,
    breadcrumbs: [{ label: 'Dashboards' }],
  });

  // Dashboard data
  const [dashboards, setDashboards] = useState<DashboardType[]>([]);
  const [dashboardStats, setDashboardStats] = useState<Record<string, number>>({});
  const [isLoadingDashboards, setIsLoadingDashboards] = useState(false);

  // Current dashboard widgets
  const [widgets, setWidgets] = useState<ChartWidgetType[]>([]);
  const [nextWidgetId, setNextWidgetId] = useState(1);

  // Dashboard creation state
  const [isCreatingDashboard, setIsCreatingDashboard] = useState(false);

  // Chart configuration sidebar state - removed since we no longer use sidebar for database selection

  // API and utilities
  const {
    listDashboards,
    createDashboard,
    updateDashboard,
    deleteDashboard,
    queryChart
  } = useApi();
  const { setPageActions } = usePageHeader();
  // const { toast } = useToast();

  // Load dashboards on component mount
  useEffect(() => {
    loadDashboards();
  }, []);

  const loadDashboards = useCallback(async () => {
    setIsLoadingDashboards(true);
    try {
      const response = await listDashboards();
      if (response.success) {
        setDashboards(response.data);
        // Mock dashboard stats - in real app, this would come from API
        const stats: Record<string, number> = {};
        response.data.forEach((dashboard: DashboardType) => {
          stats[dashboard.id] = Math.floor(Math.random() * 8); // Random chart count for demo
        });
        setDashboardStats(stats);
      }
    } catch (error) {
      console.error('Failed to load dashboards:', error);
      // toast({
      //   title: "Error",
      //   description: "Failed to load dashboards. Please try again.",
      //   variant: "destructive",
      // });
    } finally {
      setIsLoadingDashboards(false);
    }
  }, [listDashboards]);

  // Navigation handlers
  const handleSelectDashboard = useCallback((dashboard: DashboardType) => {
    setNavigationState({
      currentView: 'dashboard',
      selectedDashboard: dashboard,
      breadcrumbs: [
        { label: 'Dashboards', onClick: () => handleNavigateBack() },
        { label: dashboard.name },
      ],
    });
    // Load dashboard widgets - for now, start with empty
    setWidgets([]);
    setNextWidgetId(1);
  }, []);

  const handleNavigateBack = useCallback(() => {
    setNavigationState({
      currentView: 'list',
      selectedDashboard: null,
      breadcrumbs: [{ label: 'Dashboards' }],
    });
    setWidgets([]);
  }, []);

  // Set page title dynamically based on navigation state - memoized to prevent re-renders
  const pageConfig = useMemo(() => ({
    title: navigationState.currentView === 'dashboard' && navigationState.selectedDashboard 
      ? navigationState.selectedDashboard.name
      : 'Dashboard',
    icon: LayoutDashboard,
    breadcrumbs: navigationState.currentView === 'dashboard' && navigationState.selectedDashboard
      ? [
          { label: 'Dashboard', onClick: () => handleNavigateBack() },
          { label: navigationState.selectedDashboard.name },
        ]
      : [{ label: 'Dashboard' }],
  }), [navigationState.currentView, navigationState.selectedDashboard, handleNavigateBack]);

  usePageTitle(pageConfig);

  // Dashboard CRUD handlers
  const handleCreateDashboard = useCallback(async () => {
    setIsCreatingDashboard(true);
    try {
      // Create dashboard with default name
      const defaultName = `Dashboard ${dashboards.length + 1}`;
      const response = await createDashboard({
        name: defaultName,
        description: '',
      });

      if (response.success) {
        const newDashboard = response.data;
        setDashboards(prev => [...prev, newDashboard]);
        setDashboardStats(prev => ({ ...prev, [newDashboard.id]: 0 }));

        // Immediately navigate to the new dashboard
        handleSelectDashboard(newDashboard);
      }
    } catch (error) {
      console.error('Failed to create dashboard:', error);
    } finally {
      setIsCreatingDashboard(false);
    }
  }, [dashboards.length, createDashboard, handleSelectDashboard]);

  const handleUpdateDashboard = useCallback(async (dashboardId: string, updates: UpdateDashboardRequest) => {
    try {
      const response = await updateDashboard(dashboardId, updates);
      if (response.success) {
        setDashboards(prev => prev.map(d =>
          d.id === dashboardId ? response.data : d
        ));

        // Update the selected dashboard if it's the one being edited
        if (navigationState.selectedDashboard?.id === dashboardId) {
          setNavigationState(prev => ({
            ...prev,
            selectedDashboard: response.data,
            breadcrumbs: [
              { label: 'Dashboards', onClick: () => handleNavigateBack() },
              { label: response.data.name },
            ],
          }));
        }
      }
    } catch (error) {
      console.error('Failed to update dashboard:', error);
    }
  }, [updateDashboard, navigationState.selectedDashboard, handleNavigateBack]);

  const handleDeleteDashboard = useCallback(async (dashboardId: string) => {
    if (!confirm('Are you sure you want to delete this dashboard? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await deleteDashboard(dashboardId);
      if (response.success) {
        setDashboards(prev => prev.filter(d => d.id !== dashboardId));
        setDashboardStats(prev => {
          const newStats = { ...prev };
          delete newStats[dashboardId];
          return newStats;
        });

        // If we're currently viewing the deleted dashboard, navigate back
        if (navigationState.selectedDashboard?.id === dashboardId) {
          handleNavigateBack();
        }

        // toast({
        //   title: "Success",
        //   description: "Dashboard deleted successfully.",
        // });
      }
    } catch (error) {
      console.error('Failed to delete dashboard:', error);
      // toast({
      //   title: "Error",
      //   description: "Failed to delete dashboard. Please try again.",
      //   variant: "destructive",
      // });
    }
  }, [deleteDashboard, navigationState.selectedDashboard, handleNavigateBack]);

  // Chart widget management
  const handleCreateChart = useCallback(() => {
    if (widgets.length >= CHART_CONFIG.MAX_WIDGETS) {
      // toast({
      //   title: "Limit Reached",
      //   description: `Maximum of ${CHART_CONFIG.MAX_WIDGETS} charts allowed per dashboard.`,
      //   variant: "destructive",
      // });
      return;
    }

    // Find next available position
    let x = 0;
    let y = 0;

    // Simple positioning logic - place widgets in rows
    const widgetsPerRow = Math.floor(12 / CHART_CONFIG.DEFAULT_WIDGET_SIZE.w);
    const currentRow = Math.floor(widgets.length / widgetsPerRow);
    const currentCol = widgets.length % widgetsPerRow;

    x = currentCol * CHART_CONFIG.DEFAULT_WIDGET_SIZE.w;
    y = currentRow * CHART_CONFIG.DEFAULT_WIDGET_SIZE.h;

    const newWidget: ChartWidgetType = {
      id: `widget-${nextWidgetId}`,
      title: '',
      chartData: null,
      isLoading: false,
      error: null,
      dashboard_id: navigationState.selectedDashboard?.id,
      layout: {
        x,
        y,
        w: CHART_CONFIG.DEFAULT_WIDGET_SIZE.w,
        h: CHART_CONFIG.DEFAULT_WIDGET_SIZE.h,
      },
    };

    setWidgets(prev => [...prev, newWidget]);
    setNextWidgetId(prev => prev + 1);
  }, [widgets, nextWidgetId, navigationState.selectedDashboard]);

  const handleDeleteWidget = useCallback((widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
  }, []);

  const handleUpdateWidget = useCallback((widgetId: string, updates: Partial<ChartWidgetType>) => {
    setWidgets(prev => prev.map(w =>
      w.id === widgetId ? { ...w, ...updates } : w
    ));
  }, []);

  // Sidebar handlers removed - database selection is now integrated into ChartWidget

  // Chart widget layout management with size constraints
  // Charts can be dragged anywhere on the grid and resized within bounds:
  // - Minimum size: 3x4 grid units (CHART_CONFIG.MIN_WIDGET_SIZE)
  // - Maximum size: 12x15 grid units (CHART_CONFIG.MAX_WIDGET_SIZE) 
  // - Maximum height constraint: 3x the default height (15 units = 3 × 5)
  // - Full width allowed up to grid boundary (12 units max)
  const handleLayoutChange = useCallback((layout: any[]) => {
    // Update widget positions based on grid layout changes with size constraints
    setWidgets(prev => prev.map(widget => {
      const layoutItem = layout.find(item => item.i === widget.id);
      if (layoutItem) {
        // Enforce size constraints
        const constrainedWidth = Math.max(
          CHART_CONFIG.MIN_WIDGET_SIZE.w,
          Math.min(layoutItem.w, CHART_CONFIG.MAX_WIDGET_SIZE.w)
        );
        const constrainedHeight = Math.max(
          CHART_CONFIG.MIN_WIDGET_SIZE.h,
          Math.min(layoutItem.h, CHART_CONFIG.MAX_WIDGET_SIZE.h)
        );

        return {
          ...widget,
          layout: {
            ...widget.layout,
            x: layoutItem.x,
            y: layoutItem.y,
            w: constrainedWidth,
            h: constrainedHeight,
          },
        };
      }
      return widget;
    }));
  }, []);

  // Update page actions when navigation state or widgets change
  useEffect(() => {
    const isListView = navigationState.currentView === 'list';
    const isDashboardView = navigationState.currentView === 'dashboard' && navigationState.selectedDashboard;
    
    setPageActions({
      // Show New Dashboard button only on list view
      onCreateDashboard: isListView ? handleCreateDashboard : undefined,
      
      // Show Create Chart button only on dashboard view
      onCreateChart: isDashboardView ? handleCreateChart : undefined,
      chartCount: widgets.length,
      maxCharts: CHART_CONFIG.MAX_WIDGETS,
    });
  }, [navigationState.currentView, navigationState.selectedDashboard, widgets.length, setPageActions, handleCreateDashboard, handleCreateChart]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {navigationState.currentView === 'list' ? (
        <DashboardList
          dashboards={dashboards}
          dashboardStats={dashboardStats}
          onSelectDashboard={handleSelectDashboard}
          onDeleteDashboard={handleDeleteDashboard}
        />
      ) : navigationState.selectedDashboard ? (
        <DashboardView
          dashboard={navigationState.selectedDashboard}
          widgets={widgets}
          onCreateChart={handleCreateChart}
          onDeleteWidget={handleDeleteWidget}
          onUpdateWidget={handleUpdateWidget}
          onLayoutChange={handleLayoutChange}
          onUpdateDashboard={handleUpdateDashboard}
        />
      ) : null}
    </div>
  );
};

export default Dashboard;