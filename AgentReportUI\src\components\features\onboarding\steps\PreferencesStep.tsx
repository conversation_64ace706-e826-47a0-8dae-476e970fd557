"use client";

import React, { useState } from 'react';
import { OnboardingStepProps } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, ArrowLeft, ArrowRight, Mail, Bell, Palette } from 'lucide-react';

interface PreferencesData {
  emailNotifications: boolean;
  weeklyDigest: boolean;
  securityAlerts: boolean;
  defaultOutputFormat: string;
  theme: string;
  timezone: string;
}

export default function PreferencesStep({ onNext, onPrevious, isLoading }: OnboardingStepProps) {
  const [preferences, setPreferences] = useState<PreferencesData>({
    emailNotifications: true,
    weeklyDigest: true,
    securityAlerts: true,
    defaultOutputFormat: 'excel',
    theme: 'system',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  });

  const handleSwitchChange = (field: keyof PreferencesData, value: boolean) => {
    setPreferences(prev => ({ ...prev, [field]: value }));
  };

  const handleSelectChange = (field: keyof PreferencesData, value: string) => {
    setPreferences(prev => ({ ...prev, [field]: value }));
  };

  const handleNext = () => {
    // Here you could save the preferences to the backend
    console.log('Preferences data:', preferences);
    onNext();
  };

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="text-center pb-6">
        <div className="mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit">
          <Settings className="h-8 w-8 text-blue-600 dark:text-blue-400" />
        </div>
        <CardTitle className="text-2xl font-bold text-slate-900 dark:text-slate-100">
          Configure Your Preferences
        </CardTitle>
        <CardDescription className="text-slate-600 dark:text-slate-400 mt-2">
          Customize your experience with Agent Platform. You can change these anytime.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-8">
        {/* Notification Preferences */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Mail className="h-5 w-5 text-slate-600 dark:text-slate-400" />
            <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
              Notifications
            </h3>
          </div>
          
          <div className="space-y-4 pl-7">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-sm font-medium">Email Notifications</Label>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  Receive updates about your queries and reports
                </p>
              </div>
              <Switch
                checked={preferences.emailNotifications}
                onCheckedChange={(value) => handleSwitchChange('emailNotifications', value)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-sm font-medium">Weekly Digest</Label>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  Get a summary of your activity and insights
                </p>
              </div>
              <Switch
                checked={preferences.weeklyDigest}
                onCheckedChange={(value) => handleSwitchChange('weeklyDigest', value)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-sm font-medium">Security Alerts</Label>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  Important security and account notifications
                </p>
              </div>
              <Switch
                checked={preferences.securityAlerts}
                onCheckedChange={(value) => handleSwitchChange('securityAlerts', value)}
              />
            </div>
          </div>
        </div>

        {/* Application Preferences */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Palette className="h-5 w-5 text-slate-600 dark:text-slate-400" />
            <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
              Application
            </h3>
          </div>
          
          <div className="space-y-4 pl-7">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Default Output Format</Label>
              <Select
                value={preferences.defaultOutputFormat}
                onValueChange={(value) => handleSelectChange('defaultOutputFormat', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                  <SelectItem value="json">JSON</SelectItem>
                  <SelectItem value="table">Table View</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">Theme</Label>
              <Select
                value={preferences.theme}
                onValueChange={(value) => handleSelectChange('theme', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="system">System</SelectItem>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={onPrevious}
            disabled={isLoading}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Previous</span>
          </Button>

          <Button
            onClick={handleNext}
            disabled={isLoading}
            className="flex items-center space-x-2"
          >
            <span>Continue</span>
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="text-center text-sm text-slate-500 dark:text-slate-400">
          <p>This step is optional - you can skip and configure these later</p>
        </div>
      </CardContent>
    </Card>
  );
}
