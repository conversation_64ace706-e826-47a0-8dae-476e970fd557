{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/cn.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/date.ts"], "sourcesContent": ["// Date utility functions\n\nexport const formatDate = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n};\n\nexport const formatDateTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n};\n\nexport const formatRelativeTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n\n  return formatDate(d);\n};\n\nexport const isTokenExpired = (expiresAt: string): boolean => {\n  const expirationTime = new Date(expiresAt).getTime();\n  const currentTime = new Date().getTime();\n  const bufferTime = 5 * 60 * 1000; // 5 minutes buffer\n  \n  return currentTime >= (expirationTime - bufferTime);\n};\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;AAElB,MAAM,aAAa,CAAC;IACzB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,cAAc,CAAC,SAAS;QAC/B,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;IACrE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;IAC5D;IAEA,OAAO,WAAW;AACpB;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,iBAAiB,IAAI,KAAK,WAAW,OAAO;IAClD,MAAM,cAAc,IAAI,OAAO,OAAO;IACtC,MAAM,aAAa,IAAI,KAAK,MAAM,mBAAmB;IAErD,OAAO,eAAgB,iBAAiB;AAC1C", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/format.ts"], "sourcesContent": ["// Formatting utility functions\n\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\nexport const formatNumber = (num: number): string => {\n  return new Intl.NumberFormat('en-US').format(num);\n};\n\nexport const formatCurrency = (amount: number, currency = 'USD'): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n};\n\nexport const truncateText = (text: string, maxLength: number): string => {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n};\n\nexport const capitalizeFirst = (str: string): string => {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n};\n\nexport const camelToTitle = (str: string): string => {\n  return str\n    .replace(/([A-Z])/g, ' $1')\n    .replace(/^./, (str) => str.toUpperCase())\n    .trim();\n};\n\nexport const generateSessionId = (): string => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;AAExB,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,MAAM,iBAAiB,CAAC,QAAgB,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,eAAe,CAAC,MAAc;IACzC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,YAAY,OACpB,OAAO,CAAC,MAAM,CAAC,MAAQ,IAAI,WAAW,IACtC,IAAI;AACT;AAEO,MAAM,oBAAoB;IAC/B,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/validation.ts"], "sourcesContent": ["// Validation utility functions\n\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const isValidPassword = (password: string): boolean => {\n  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number\n  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;\n  return passwordRegex.test(password);\n};\n\nexport const isValidUrl = (url: string): boolean => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const isValidPort = (port: number): boolean => {\n  return port >= 1 && port <= 65535;\n};\n\nexport const isValidDatabaseName = (name: string): boolean => {\n  // Basic validation for database names\n  const dbNameRegex = /^[a-zA-Z][a-zA-Z0-9_-]*$/;\n  return dbNameRegex.test(name) && name.length >= 2 && name.length <= 50;\n};\n\nexport const sanitizeInput = (input: string): string => {\n  return input.trim().replace(/[<>]/g, '');\n};\n\nexport const validateRequired = (value: any): boolean => {\n  if (typeof value === 'string') {\n    return value.trim().length > 0;\n  }\n  return value !== null && value !== undefined;\n};\n\nexport const validateMinLength = (value: string, minLength: number): boolean => {\n  return value.length >= minLength;\n};\n\nexport const validateMaxLength = (value: string, maxLength: number): boolean => {\n  return value.length <= maxLength;\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;AAExB,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,kBAAkB,CAAC;IAC9B,4DAA4D;IAC5D,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,QAAQ,KAAK,QAAQ;AAC9B;AAEO,MAAM,sBAAsB,CAAC;IAClC,sCAAsC;IACtC,MAAM,cAAc;IACpB,OAAO,YAAY,IAAI,CAAC,SAAS,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI;AACtE;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,MAAM,IAAI,GAAG,MAAM,GAAG;IAC/B;IACA,OAAO,UAAU,QAAQ,UAAU;AACrC;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/index.ts"], "sourcesContent": ["// Main utility exports\n\nexport * from './cn';\nexport * from './date';\nexport * from './format';\nexport * from './validation';\n\n// Re-export the original cn function for backward compatibility\nexport { cn } from './cn';\n"], "names": [], "mappings": "AAAA,uBAAuB;;AAEvB;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAAA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { ChevronRight } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<\"nav\"> & {\n    separator?: React.ComponentType<{ className?: string }>;\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label=\"breadcrumb\" {...props} />);\nBreadcrumb.displayName = \"Breadcrumb\";\n\nconst BreadcrumbList = React.forwardRef<\n  HTMLOListElement,\n  React.ComponentPropsWithoutRef<\"ol\">\n>(({ className, ...props }, ref) => (\n  <ol\n    ref={ref}\n    className={cn(\n      \"flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5\",\n      className\n    )}\n    {...props}\n  />\n));\nBreadcrumbList.displayName = \"BreadcrumbList\";\n\nconst BreadcrumbItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentPropsWithoutRef<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    className={cn(\"inline-flex items-center gap-1.5\", className)}\n    {...props}\n  />\n));\nBreadcrumbItem.displayName = \"BreadcrumbItem\";\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<\"a\"> & {\n    asChild?: boolean;\n  }\n>(({ asChild, className, ...props }, ref) => {\n  if (asChild) {\n    return <React.Fragment {...props} />;\n  }\n\n  return (\n    <a\n      ref={ref}\n      className={cn(\"transition-colors hover:text-foreground\", className)}\n      {...props}\n    />\n  );\n});\nBreadcrumbLink.displayName = \"BreadcrumbLink\";\n\nconst BreadcrumbPage = React.forwardRef<\n  HTMLSpanElement,\n  React.ComponentPropsWithoutRef<\"span\">\n>(({ className, ...props }, ref) => (\n  <span\n    ref={ref}\n    role=\"link\"\n    aria-disabled=\"true\"\n    aria-current=\"page\"\n    className={cn(\"font-normal text-foreground\", className)}\n    {...props}\n  />\n));\nBreadcrumbPage.displayName = \"BreadcrumbPage\";\n\nconst BreadcrumbSeparator = ({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) => (\n  <li\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"[&>svg]:size-3.5\", className)}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n);\nBreadcrumbSeparator.displayName = \"BreadcrumbSeparator\";\n\nconst BreadcrumbEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <svg\n      width=\"15\"\n      height=\"15\"\n      viewBox=\"0 0 15 15\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        d=\"M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z\"\n        fill=\"currentColor\"\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n      />\n    </svg>\n    <span className=\"sr-only\">More</span>\n  </span>\n);\nBreadcrumbEllipsis.displayName = \"BreadcrumbEllipsis\";\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAKhC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,6LAAC;QAAI,KAAK;QAAK,cAAW;QAAc,GAAG,KAAK;;;;;;;AACzE,WAAW,WAAW,GAAG;AAEzB,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKpC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACnC,IAAI,SAAS;QACX,qBAAO,6LAAC,6JAAA,CAAA,WAAc;YAAE,GAAG,KAAK;;;;;;IAClC;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB,iBAC3B,6LAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;OAXxB;AAcN,oBAAoB,WAAW,GAAG;AAElC,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OAC0B,iBAC7B,6LAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;0BAEN,cAAA,6LAAC;oBACC,GAAE;oBACF,MAAK;oBACL,UAAS;oBACT,UAAS;;;;;;;;;;;0BAGb,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;OAxBxB;AA2BN,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\r\nimport { Menu, LayoutDashboard, ChevronRight, Plus, Brain, Download, MessageSquare } from 'lucide-react';\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { useTheme } from \"@/providers/theme-provider\";\r\nimport { ThemeToggle } from '@/components/ui/theme-toggle';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\nimport {\r\n  Breadcrumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from '@/components/ui/breadcrumb';\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated } = useAuth();\r\n  const { theme, setTheme } = useTheme();\r\n  const { pageInfo, actions } = usePageHeader();\r\n  const pathname = usePathname();\r\n  \r\n  // Check if we have breadcrumbs to show\r\n  const hasBreadcrumbs = pageInfo.breadcrumbs && pageInfo.breadcrumbs.length > 1;\r\n  \r\n  // Get chart-related state from context actions\r\n  const { \r\n    onCreateChart, \r\n    chartCount = 0, \r\n    maxCharts = 12, \r\n    onCreateAnalysis, \r\n    isCreatingAnalysis = false,\r\n    onCreateDashboard,\r\n    onExport,\r\n    onToggleChat,\r\n    isChatOpen = false\r\n  } = actions;\r\n  const canCreateChart = chartCount < maxCharts;\r\n  \r\n  // Show Create Chart Button on dashboard pages when the callback is provided\r\n  const isDashboardPage = pathname === '/dashboard';\r\n  const isAIWorkflowsPage = pathname === '/ai-workflows';\r\n  const showCreateChartButton = isDashboardPage && onCreateChart;\r\n  const showCreateDashboardButton = isDashboardPage && onCreateDashboard;\r\n  const showCreateAnalysisButton = isAIWorkflowsPage && onCreateAnalysis;\r\n  const showProjectActions = isAIWorkflowsPage && (onExport || onToggleChat);\r\n\r\n  // Get the appropriate icon for breadcrumbs\r\n  const getBreadcrumbIcon = () => {\r\n    if (pathname === '/dashboard') return LayoutDashboard;\r\n    if (pathname === '/ai-workflows') return Brain;\r\n    return pageInfo.icon || LayoutDashboard;\r\n  };\r\n\r\n  const BreadcrumbIcon = getBreadcrumbIcon();\r\n\r\n  return (\r\n    <header className=\"flex items-center justify-between whitespace-nowrap border-b border-solid border-sidebar-border bg-sidebar-bg px-4 py-2 h-12\">\r\n      <div className=\"flex items-center gap-3 text-sidebar-text-primary\">\r\n        {isAuthenticated && (\r\n          <div className=\"lg:hidden\">\r\n            <Sheet>\r\n              <SheetTrigger asChild>\r\n                <Button variant=\"outline\" size=\"icon\" className=\"h-7 w-7\">\r\n                  <Menu className=\"h-4 w-4\" />\r\n                </Button>\r\n              </SheetTrigger>\r\n            </Sheet>\r\n          </div>\r\n        )}\r\n        \r\n        {/* Dynamic Breadcrumb Navigation or Page Title */}\r\n        {hasBreadcrumbs ? (\r\n          <Breadcrumb>\r\n            <BreadcrumbList>\r\n              {pageInfo.breadcrumbs!.map((breadcrumb, index) => (\r\n                <React.Fragment key={breadcrumb.label}>\r\n                  <BreadcrumbItem>\r\n                    {index === 0 ? (\r\n                      // First breadcrumb item (with icon)\r\n                      breadcrumb.onClick || breadcrumb.href ? (\r\n                        <BreadcrumbLink \r\n                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}\r\n                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}\r\n                          className=\"flex items-center space-x-1.5 cursor-pointer hover:text-primary text-sm font-medium\"\r\n                        >\r\n                          <BreadcrumbIcon className=\"h-4 w-4\" />\r\n                          <span>{breadcrumb.label}</span>\r\n                        </BreadcrumbLink>\r\n                      ) : (\r\n                        <div className=\"flex items-center space-x-1.5 text-sm font-medium\">\r\n                          <BreadcrumbIcon className=\"h-4 w-4\" />\r\n                          <span>{breadcrumb.label}</span>\r\n                        </div>\r\n                      )\r\n                    ) : (\r\n                      // Subsequent breadcrumb items\r\n                      index === pageInfo.breadcrumbs!.length - 1 ? (\r\n                        <BreadcrumbPage className=\"font-medium text-sm\">\r\n                          {breadcrumb.label}\r\n                        </BreadcrumbPage>\r\n                      ) : (\r\n                        <BreadcrumbLink \r\n                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}\r\n                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}\r\n                          className=\"cursor-pointer hover:text-primary text-sm font-medium\"\r\n                        >\r\n                          {breadcrumb.label}\r\n                        </BreadcrumbLink>\r\n                      )\r\n                    )}\r\n                  </BreadcrumbItem>\r\n                  \r\n                  {/* Separator */}\r\n                  {index < pageInfo.breadcrumbs!.length - 1 && (\r\n                    <BreadcrumbSeparator>\r\n                      <ChevronRight className=\"h-3 w-3\" />\r\n                    </BreadcrumbSeparator>\r\n                  )}\r\n                </React.Fragment>\r\n              ))}\r\n            </BreadcrumbList>\r\n          </Breadcrumb>\r\n        ) : (\r\n          <h1 className=\"text-sm font-medium leading-tight tracking-[-0.015em] text-sidebar-text-primary\">\r\n            {pageInfo.title}\r\n          </h1>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right side actions */}\r\n      <div className=\"flex items-center gap-2\">\r\n        {showCreateChartButton && (\r\n          <Button\r\n            onClick={onCreateChart}\r\n            disabled={!canCreateChart}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            title={!canCreateChart ? `Maximum of ${maxCharts} charts allowed` : undefined}\r\n            onMouseEnter={(e) => {\r\n              if (canCreateChart) {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (canCreateChart) {\r\n                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n              }\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            Chart\r\n          </Button>\r\n        )}\r\n        {showCreateDashboardButton && (\r\n          <Button\r\n            onClick={onCreateDashboard}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            onMouseEnter={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            Dashboard\r\n          </Button>\r\n        )}\r\n        {showCreateAnalysisButton && (\r\n          <Button\r\n            onClick={onCreateAnalysis}\r\n            disabled={isCreatingAnalysis}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            title={isCreatingAnalysis ? \"Analysis is being created\" : undefined}\r\n            onMouseEnter={(e) => {\r\n              if (!isCreatingAnalysis) {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (!isCreatingAnalysis) {\r\n                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n              }\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            {isCreatingAnalysis ? 'Creating...' : 'Analysis'}\r\n          </Button>\r\n        )}\r\n        {showProjectActions && (\r\n          <>\r\n            {onExport && (\r\n              <Button\r\n                onClick={onExport}\r\n                className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n                style={{\r\n                  backgroundColor: 'var(--surface-selected)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n                }}\r\n              >\r\n                <Download className=\"h-3 w-3 mr-1.5\" />\r\n                Export\r\n              </Button>\r\n            )}\r\n            {onToggleChat && (\r\n              <Button\r\n                onClick={onToggleChat}\r\n                className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n                style={{\r\n                  backgroundColor: isChatOpen ? 'var(--interactive-bg-secondary-hover)' : 'var(--surface-selected)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = isChatOpen ? 'var(--interactive-bg-secondary-hover)' : 'var(--surface-selected)';\r\n                }}\r\n              >\r\n                <MessageSquare className=\"h-3 w-3 mr-1.5\" />\r\n                Chat\r\n              </Button>\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;;;AAXA;;;;;;;;;;AAoBA,MAAM,SAAS;;IACb,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAC1C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,uCAAuC;IACvC,MAAM,iBAAiB,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG;IAE7E,+CAA+C;IAC/C,MAAM,EACJ,aAAa,EACb,aAAa,CAAC,EACd,YAAY,EAAE,EACd,gBAAgB,EAChB,qBAAqB,KAAK,EAC1B,iBAAiB,EACjB,QAAQ,EACR,YAAY,EACZ,aAAa,KAAK,EACnB,GAAG;IACJ,MAAM,iBAAiB,aAAa;IAEpC,4EAA4E;IAC5E,MAAM,kBAAkB,aAAa;IACrC,MAAM,oBAAoB,aAAa;IACvC,MAAM,wBAAwB,mBAAmB;IACjD,MAAM,4BAA4B,mBAAmB;IACrD,MAAM,2BAA2B,qBAAqB;IACtD,MAAM,qBAAqB,qBAAqB,CAAC,YAAY,YAAY;IAEzE,2CAA2C;IAC3C,MAAM,oBAAoB;QACxB,IAAI,aAAa,cAAc,OAAO,+NAAA,CAAA,kBAAe;QACrD,IAAI,aAAa,iBAAiB,OAAO,uMAAA,CAAA,QAAK;QAC9C,OAAO,SAAS,IAAI,IAAI,+NAAA,CAAA,kBAAe;IACzC;IAEA,MAAM,iBAAiB;IAEvB,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;oBACZ,iCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;sCACJ,cAAA,6LAAC,oIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAO,WAAU;8CAC9C,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQzB,+BACC,6LAAC,yIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,yIAAA,CAAA,iBAAc;sCACZ,SAAS,WAAW,CAAE,GAAG,CAAC,CAAC,YAAY,sBACtC,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;sDACb,6LAAC,yIAAA,CAAA,iBAAc;sDACZ,UAAU,IACT,oCAAoC;4CACpC,WAAW,OAAO,IAAI,WAAW,IAAI,iBACnC,6LAAC,yIAAA,CAAA,iBAAc;gDACZ,GAAI,WAAW,IAAI,GAAG;oDAAE,MAAM,WAAW,IAAI;gDAAC,IAAI,CAAC,CAAC;gDACpD,GAAI,WAAW,OAAO,GAAG;oDAAE,SAAS,WAAW,OAAO;gDAAC,IAAI,CAAC,CAAC;gDAC9D,WAAU;;kEAEV,6LAAC;wDAAe,WAAU;;;;;;kEAC1B,6LAAC;kEAAM,WAAW,KAAK;;;;;;;;;;;qEAGzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAe,WAAU;;;;;;kEAC1B,6LAAC;kEAAM,WAAW,KAAK;;;;;;;;;;;uDAI3B,8BAA8B;4CAC9B,UAAU,SAAS,WAAW,CAAE,MAAM,GAAG,kBACvC,6LAAC,yIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,WAAW,KAAK;;;;;qEAGnB,6LAAC,yIAAA,CAAA,iBAAc;gDACZ,GAAI,WAAW,IAAI,GAAG;oDAAE,MAAM,WAAW,IAAI;gDAAC,IAAI,CAAC,CAAC;gDACpD,GAAI,WAAW,OAAO,GAAG;oDAAE,SAAS,WAAW,OAAO;gDAAC,IAAI,CAAC,CAAC;gDAC9D,WAAU;0DAET,WAAW,KAAK;;;;;;;;;;;wCAOxB,QAAQ,SAAS,WAAW,CAAE,MAAM,GAAG,mBACtC,6LAAC,yIAAA,CAAA,sBAAmB;sDAClB,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;mCAxCT,WAAW,KAAK;;;;;;;;;;;;;;6CAgD3C,6LAAC;wBAAG,WAAU;kCACX,SAAS,KAAK;;;;;;;;;;;;0BAMrB,6LAAC;gBAAI,WAAU;;oBACZ,uCACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,OAAO,CAAC,iBAAiB,CAAC,WAAW,EAAE,UAAU,eAAe,CAAC,GAAG;wBACpE,cAAc,CAAC;4BACb,IAAI,gBAAgB;gCAClB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,gBAAgB;gCAClB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;;0CAEA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAmB;;;;;;;oBAItC,2CACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC1C;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC1C;;0CAEA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAmB;;;;;;;oBAItC,0CACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,OAAO,qBAAqB,8BAA8B;wBAC1D,cAAc,CAAC;4BACb,IAAI,CAAC,oBAAoB;gCACvB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,oBAAoB;gCACvB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;;0CAEA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,qBAAqB,gBAAgB;;;;;;;oBAGzC,oCACC;;4BACG,0BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,OAAO;gCACT;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;;kDAEA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAmB;;;;;;;4BAI1C,8BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,iBAAiB,aAAa,0CAA0C;oCACxE,OAAO;gCACT;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,aAAa,0CAA0C;gCACjG;;kDAEA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAmB;;;;;;;;;;;;;;;;;;;;;AAS5D;GAtOM;;QACwB,mIAAA,CAAA,UAAO;QACP,yIAAA,CAAA,WAAQ;QACN,yIAAA,CAAA,gBAAa;QAC1B,qIAAA,CAAA,cAAW;;;KAJxB;uCAwOS", "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground text-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+nBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Sidebar.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport Link from 'next/link';\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { MoreVertical, MessageCircle, Database, Plus, BarChart3, MessageCirclePlus, LayoutDashboard, Brain, User, Settings, LogOut } from 'lucide-react';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useChatHistory } from \"@/providers/ChatHistoryContext\";\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface SidebarProps {\r\n  onNewChat: () => void;\r\n  onToggleCollapse?: (collapsed: boolean) => void;\r\n  isCreatingNewChat?: boolean;\r\n}\r\n\r\nconst Sidebar: React.FC<SidebarProps> = ({ onNewChat, onToggleCollapse, isCreatingNewChat = false }) => {\r\n  const { \r\n    chatHistory, \r\n    isLoadingChats, \r\n    deleteChat,\r\n    renameChat, \r\n  } = useChatHistory();\r\n  const { logout, user } = useAuth();\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const [menuOpenId, setMenuOpenId] = useState<string | null>(null);\r\n  const [renameId, setRenameId] = useState<string | null>(null);\r\n  const [renameValue, setRenameValue] = useState<string>(\"\");\r\n  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);\r\n\r\n  console.log('Sidebar component rendered. Current pathname:', pathname);\r\n\r\n  // Extract chatId from pathname (e.g., /chat/123)\r\n  const currentChatId = pathname?.split('/').includes('chat') ? pathname.split('/').pop() : null;\r\n  console.log('Current chatId extracted from pathname:', currentChatId);\r\n\r\n  const handleRename = (id: string, currentTitle: string) => {\r\n    console.log('Renaming chat with id:', id, 'and current title:', currentTitle);\r\n    setRenameId(id);\r\n    setRenameValue(currentTitle);\r\n    setMenuOpenId(null);\r\n  };\r\n\r\n  const handleRenameSubmit = (id: string) => {\r\n    console.log('Submitting rename for chat with id:', id, 'and new title:', renameValue);\r\n    if (renameValue.trim()) {\r\n      renameChat(id, renameValue.trim());\r\n    }\r\n    setRenameId(null);\r\n    setRenameValue(\"\");\r\n  };\r\n\r\n  const handleDelete = async (chatId: string) => {\r\n    try {\r\n      await deleteChat(chatId);\r\n      setMenuOpenId(null);\r\n\r\n      // 🚚 After successful deletion, handle navigation if the deleted chat was active\r\n      if (currentChatId === chatId) {\r\n        // Get the updated chat list (the state will have been updated by deleteChat)\r\n        const remainingChats = chatHistory.filter(chat => chat.id !== chatId);\r\n\r\n        if (remainingChats.length > 0) {\r\n          // Navigate to the most recently updated chat (first in list)\r\n          router.push(`/chat/${remainingChats[0].id}`);\r\n        } else {\r\n          // No chats left – start a fresh chat\r\n          onNewChat();\r\n          // Fallback navigate to generic chat route to trigger new-chat UI\r\n          router.push('/chat');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete chat:', error);\r\n      // You could add a toast notification here if you have one\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (timestamp: Date) => {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - timestamp.getTime();\r\n    const diffHours = diffMs / (1000 * 60 * 60);\r\n    const diffDays = diffMs / (1000 * 60 * 60 * 24);\r\n\r\n    if (diffHours < 1) {\r\n      return 'Just now';\r\n    } else if (diffHours < 24) {\r\n      return `${Math.floor(diffHours)}h ago`;\r\n    } else if (diffDays < 7) {\r\n      return `${Math.floor(diffDays)}d ago`;\r\n    } else {\r\n      return timestamp.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  const handleToggleCollapse = () => {\r\n    const newCollapsedState = !isCollapsed;\r\n    setIsCollapsed(newCollapsedState);\r\n    onToggleCollapse?.(newCollapsedState);\r\n  };\r\n\r\n  return (\r\n    <aside \r\n      className={`hidden lg:flex flex-col ${isCollapsed ? 'w-16' : 'w-sidebar'} h-full min-h-0 transition-all duration-300 flex-shrink-0 relative overflow-y-auto`}\r\n      style={{\r\n        backgroundColor: 'var(--sidebar-bg)',\r\n        borderRight: '1px solid var(--sidebar-border)',\r\n        scrollbarColor: 'var(--sidebar-surface-tertiary) transparent'\r\n      }}\r\n    >\r\n      {/* Toggle Button */}\r\n      <div className={`sticky top-0 z-10 p-2 transition-all duration-300 ease-in-out ${isCollapsed ? 'flex justify-center' : 'flex justify-end'}`}\r\n        style={{ backgroundColor: 'var(--sidebar-bg)' }}\r\n      >\r\n        <Button \r\n          variant=\"ghost\" \r\n          size=\"icon\"\r\n          className=\"w-10 h-10 flex items-center justify-center rounded-lg border-0 transition-all duration-300 ease-in-out transform-gpu\"\r\n          style={{ \r\n            color: 'var(--sidebar-icon) !important',\r\n            backgroundColor: 'transparent !important'\r\n          }}\r\n          onMouseEnter={(e) => {\r\n            e.currentTarget.style.setProperty('background-color', 'var(--interactive-bg-secondary-hover)', 'important');\r\n            e.currentTarget.style.transform = 'scale(1.05)';\r\n          }}\r\n          onMouseLeave={(e) => {\r\n            e.currentTarget.style.setProperty('background-color', 'transparent', 'important');\r\n            e.currentTarget.style.transform = 'scale(1)';\r\n          }}\r\n          onClick={handleToggleCollapse}\r\n        >\r\n          <svg \r\n            className=\"h-5 w-5 transition-transform duration-300 ease-in-out\" \r\n            viewBox=\"0 0 20 20\" \r\n            fill=\"currentColor\" \r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            style={{\r\n              transform: isCollapsed ? 'scaleX(-1)' : 'scaleX(1)'\r\n            }}\r\n          >\r\n            <path d=\"M6.83496 3.99992C6.38353 4.00411 6.01421 4.0122 5.69824 4.03801C5.31232 4.06954 5.03904 4.12266 4.82227 4.20012L4.62207 4.28606C4.18264 4.50996 3.81498 4.85035 3.55859 5.26848L3.45605 5.45207C3.33013 5.69922 3.25006 6.01354 3.20801 6.52824C3.16533 7.05065 3.16504 7.71885 3.16504 8.66301V11.3271C3.16504 12.2712 3.16533 12.9394 3.20801 13.4618C3.25006 13.9766 3.33013 14.2909 3.45605 14.538L3.55859 14.7216C3.81498 15.1397 4.18266 15.4801 4.62207 15.704L4.82227 15.79C5.03904 15.8674 5.31234 15.9205 5.69824 15.9521C6.01398 15.9779 6.383 15.986 6.83398 15.9902L6.83496 3.99992ZM18.165 11.3271C18.165 12.2493 18.1653 12.9811 18.1172 13.5702C18.0745 14.0924 17.9916 14.5472 17.8125 14.9648L17.7295 15.1415C17.394 15.8 16.8834 16.3511 16.2568 16.7353L15.9814 16.8896C15.5157 17.1268 15.0069 17.2285 14.4102 17.2773C13.821 17.3254 13.0893 17.3251 12.167 17.3251H7.83301C6.91071 17.3251 6.17898 17.3254 5.58984 17.2773C5.06757 17.2346 4.61294 17.1508 4.19531 16.9716L4.01855 16.8896C3.36014 16.5541 2.80898 16.0434 2.4248 15.4169L2.27051 15.1415C2.03328 14.6758 1.93158 14.167 1.88281 13.5702C1.83468 12.9811 1.83496 12.2493 1.83496 11.3271V8.66301C1.83496 7.74072 1.83468 7.00898 1.88281 6.41985C1.93157 5.82309 2.03329 5.31432 2.27051 4.84856L2.4248 4.57317C2.80898 3.94666 3.36012 3.436 4.01855 3.10051L4.19531 3.0175C4.61285 2.83843 5.06771 2.75548 5.58984 2.71281C6.17898 2.66468 6.91071 2.66496 7.83301 2.66496H12.17C13.0893 2.66496 13.821 2.66468 14.4102 2.71281C15.0069 2.76157 15.5157 2.86329 15.9814 3.10051L16.2568 3.25481C16.8833 3.63898 17.394 4.19012 17.7295 4.84856L17.8125 5.02531C17.9916 5.44285 18.0745 5.89771 18.1172 6.41985C18.1653 7.00898 18.165 7.74072 18.165 8.66301V11.3271ZM8.16406 15.995H12.167C13.1112 15.995 13.7794 15.9947 14.3018 15.9521C14.8164 15.91 15.1308 15.8299 15.3779 15.704L15.5615 15.6015C15.9797 15.3451 16.32 14.9774 16.5439 14.538L16.6299 14.3378C16.7074 14.121 16.7605 13.8478 16.792 13.4618C16.8347 12.9394 16.835 12.2712 16.835 11.3271V8.66301C16.835 7.71885 16.8347 7.05065 16.792 6.52824C16.7605 6.14232 16.7073 5.86904 16.6299 5.65227L16.5439 5.45207C16.32 5.01264 15.9796 4.64498 15.5615 4.3886L15.3779 4.28606C15.1308 4.16013 14.8165 4.08006 14.3018 4.03801C13.7794 3.99533 13.1112 3.99504 12.167 3.99504H8.16406C8.16407 3.99667 8.16504 3.99829 8.16504 3.99992L8.16406 15.995Z\"></path>\r\n          </svg>\r\n        </Button>\r\n      </div>\r\n      \r\n      {!isCollapsed && (\r\n        <div className=\"flex flex-col h-full px-3\">\r\n          {/* Navigation Links */}\r\n          <div className=\"space-y-1 mb-4\">\r\n            <Link href=\"/dashboard\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/dashboard' ? '' : ''}`}\r\n                style={{\r\n                  color: pathname === '/dashboard' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/dashboard' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/dashboard') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/dashboard') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <LayoutDashboard className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Dashboard\r\n              </Button>\r\n            </Link>\r\n            \r\n            <Link href=\"/reports\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/reports' ? '' : ''}`}\r\n                style={{\r\n                  color: pathname === '/reports' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/reports' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/reports') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/reports') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <BarChart3 className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Reports\r\n              </Button>\r\n            </Link>\r\n            \r\n            <Link href=\"/datasources\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10`}\r\n                style={{\r\n                  color: pathname === '/datasources' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/datasources' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/datasources') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/datasources') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <Database className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Data Sources\r\n              </Button>\r\n            </Link>\r\n\r\n            <Link href=\"/ai-workflows\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10`}\r\n                style={{\r\n                  color: pathname === '/ai-workflows' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/ai-workflows' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/ai-workflows') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/ai-workflows') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <Brain className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                AI Workflows\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n          \r\n          {/* New Chat Button */}\r\n          <div className=\"mb-4\">\r\n            <Button \r\n              variant=\"ghost\" \r\n              className=\"w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10\" \r\n              onClick={onNewChat}\r\n              disabled={isCreatingNewChat}\r\n              style={{\r\n                color: 'var(--sidebar-text-secondary)',\r\n                backgroundColor: 'transparent'\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                if (!isCreatingNewChat) {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                if (!isCreatingNewChat) {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }\r\n              }}\r\n            >\r\n              <Plus className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n              {isCreatingNewChat ? 'Creating...' : 'New Chat'}\r\n            </Button>\r\n          </div>\r\n          \r\n          {/* Chat History Section */}\r\n          <div className=\"flex flex-col gap-1 overflow-y-auto flex-1 pb-4\">\r\n            <h3 \r\n              className=\"text-xs font-medium uppercase tracking-wider px-2 py-2 sticky top-0 z-10\"\r\n              style={{ \r\n                color: 'var(--sidebar-text-tertiary)',\r\n                backgroundColor: 'var(--sidebar-bg)'\r\n              }}\r\n            >\r\n              Chat History\r\n            </h3>\r\n            \r\n            {isLoadingChats && (\r\n              <div className=\"flex items-center justify-center py-8\">\r\n                <div className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  Loading chats...\r\n                </div>\r\n              </div>\r\n            )}\r\n            \r\n            {!isLoadingChats && chatHistory.map((chat) => {\r\n              const isActive = chat.id === currentChatId;\r\n              const isRenaming = renameId === chat.id;\r\n              return (\r\n                <div\r\n                  key={chat.id}\r\n                  className={`group flex items-center justify-between py-2 px-2 rounded-lg cursor-pointer transition-all duration-200 ${isActive ? '' : ''}`}\r\n                  style={{\r\n                    backgroundColor: isActive ? 'var(--surface-selected)' : 'transparent'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'transparent';\r\n                    }\r\n                  }}\r\n                  onClick={() => router.push(`/chat/${chat.id}`)}\r\n                >\r\n                  {isRenaming ? (\r\n                    <form\r\n                      onSubmit={e => { e.preventDefault(); handleRenameSubmit(chat.id); }}\r\n                      className=\"flex items-center gap-2 w-full\"\r\n                    >\r\n                      <input\r\n                        autoFocus\r\n                        className=\"truncate max-w-[120px] text-sm font-normal bg-transparent border-b border-blue-400 outline-none px-1\"\r\n                        style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        value={renameValue}\r\n                        onChange={e => setRenameValue(e.target.value)}\r\n                        onKeyDown={e => { if (e.key === 'Escape') setRenameId(null); }}\r\n                      />\r\n                      <Button \r\n                        type=\"submit\" \r\n                        size=\"sm\" \r\n                        variant=\"ghost\" \r\n                        className=\"text-blue-500 px-2 text-xs rounded border-0\"\r\n                      >\r\n                        Save\r\n                      </Button>\r\n                    </form>\r\n                  ) : (\r\n                    <>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <span \r\n                          className=\"truncate text-sm font-normal block\"\r\n                          style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        >\r\n                          {chat.title}\r\n                        </span>\r\n                      </div>\r\n                      \r\n                      <div className=\"relative ml-2 flex-shrink-0\" onClick={e => e.stopPropagation()}>\r\n                        <DropdownMenu>\r\n                          <DropdownMenuTrigger asChild>\r\n                            <Button\r\n                              type=\"button\"\r\n                              size=\"icon\"\r\n                              variant=\"ghost\"\r\n                              className=\"w-7 h-7 p-0 opacity-0 group-hover:opacity-100 transition-all duration-200 rounded border-0\"\r\n                              style={{ color: 'var(--sidebar-icon)' }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                              aria-label=\"More actions\"\r\n                            >\r\n                              <MoreVertical className=\"w-4 h-4\" />\r\n                            </Button>\r\n                          </DropdownMenuTrigger>\r\n                          <DropdownMenuContent \r\n                            align=\"start\" \r\n                            side=\"bottom\" \r\n                            sideOffset={8} \r\n                            className=\"border-none shadow-xl rounded-xl p-2\"\r\n                            style={{\r\n                              backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                              color: 'var(--sidebar-text-primary)'\r\n                            }}\r\n                          >\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleRename(chat.id, chat.title)}\r\n                              className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                              style={{ \r\n                                color: 'var(--sidebar-text-primary)',\r\n                                backgroundColor: 'transparent'\r\n                              }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                            >\r\n                              Rename\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleDelete(chat.id)}\r\n                              className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                              style={{ \r\n                                color: '#ff8583',\r\n                                backgroundColor: 'transparent'\r\n                              }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                            >\r\n                              Delete\r\n                            </DropdownMenuItem>\r\n                          </DropdownMenuContent>\r\n                        </DropdownMenu>\r\n                      </div>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              );\r\n            })}\r\n            \r\n            {!isLoadingChats && chatHistory.length === 0 && (\r\n              <div className=\"text-center py-8\">\r\n                <MessageCirclePlus \r\n                  className=\"h-12 w-12 mx-auto mb-3\" \r\n                  style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                />\r\n                <p className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  No chats yet\r\n                </p>\r\n                <p className=\"text-xs mt-1\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  Start a new conversation\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          {/* Profile Section */}\r\n          <div className=\"mt-auto pt-2 pb-4 border-t\" style={{ borderColor: 'var(--sidebar-border)' }}>\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <div className=\"flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-all duration-200 hover:bg-opacity-80\" \r\n                     style={{ backgroundColor: 'transparent' }}\r\n                     onMouseEnter={(e) => {\r\n                       e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                     }}\r\n                     onMouseLeave={(e) => {\r\n                       e.currentTarget.style.backgroundColor = 'transparent';\r\n                     }}>\r\n                  <div\r\n                    className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600\"\r\n                    style={{backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y\")'}}\r\n                  />\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <span className=\"text-sm font-medium block truncate\" style={{ color: 'var(--sidebar-text-primary)' }}>\r\n                      {'User'}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent \r\n                align=\"start\" \r\n                side=\"top\" \r\n                sideOffset={8} \r\n                className=\"border-none shadow-xl rounded-xl p-2\"\r\n                style={{\r\n                  backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n              >\r\n                <DropdownMenuItem asChild>\r\n                  <Link href=\"/profile\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                        style={{ \r\n                          color: 'var(--sidebar-text-primary)',\r\n                          backgroundColor: 'transparent'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'transparent';\r\n                        }}>\r\n                    <User className=\"w-4 h-4\" />\r\n                    Profile\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem asChild>\r\n                  <Link href=\"/settings\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                        style={{ \r\n                          color: 'var(--sidebar-text-primary)',\r\n                          backgroundColor: 'transparent'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'transparent';\r\n                        }}>\r\n                    <Settings className=\"w-4 h-4\" />\r\n                    Settings\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem \r\n                  onClick={logout} \r\n                  className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                  style={{ \r\n                    color: '#ff8583',\r\n                    backgroundColor: 'transparent'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }}\r\n                >\r\n                  <LogOut className=\"w-4 h-4\" />\r\n                  Logout\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      {/* Profile Section for Collapsed State */}\r\n      {isCollapsed && (\r\n        <div className=\"mt-auto p-2 pb-4 border-t\" style={{ borderColor: 'var(--sidebar-border)' }}>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <div className=\"flex justify-center\">\r\n                <div\r\n                  className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600 cursor-pointer transition-all duration-200 hover:scale-105\"\r\n                  style={{backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y\")'}}\r\n                />\r\n              </div>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent \r\n              align=\"start\" \r\n              side=\"right\" \r\n              sideOffset={8} \r\n              className=\"border-none shadow-xl rounded-xl p-2\"\r\n              style={{\r\n                backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                color: 'var(--sidebar-text-primary)'\r\n              }}\r\n            >\r\n              <DropdownMenuItem asChild>\r\n                <Link href=\"/profile\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                      style={{ \r\n                        color: 'var(--sidebar-text-primary)',\r\n                        backgroundColor: 'transparent'\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}>\r\n                  <User className=\"w-4 h-4\" />\r\n                  Profile\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem asChild>\r\n                <Link href=\"/settings\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                      style={{ \r\n                        color: 'var(--sidebar-text-primary)',\r\n                        backgroundColor: 'transparent'\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}>\r\n                  <Settings className=\"w-4 h-4\" />\r\n                  Settings\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem \r\n                onClick={logout} \r\n                className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                style={{ \r\n                  color: '#ff8583',\r\n                  backgroundColor: 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }}\r\n              >\r\n                <LogOut className=\"w-4 h-4\" />\r\n                Logout\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      )}\r\n    </aside>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAgBA,MAAM,UAAkC,CAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,oBAAoB,KAAK,EAAE;;IACjG,MAAM,EACJ,WAAW,EACX,cAAc,EACd,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,0IAAA,CAAA,iBAAc,AAAD;IACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAExD,QAAQ,GAAG,CAAC,iDAAiD;IAE7D,iDAAiD;IACjD,MAAM,gBAAgB,UAAU,MAAM,KAAK,SAAS,UAAU,SAAS,KAAK,CAAC,KAAK,GAAG,KAAK;IAC1F,QAAQ,GAAG,CAAC,2CAA2C;IAEvD,MAAM,eAAe,CAAC,IAAY;QAChC,QAAQ,GAAG,CAAC,0BAA0B,IAAI,sBAAsB;QAChE,YAAY;QACZ,eAAe;QACf,cAAc;IAChB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,uCAAuC,IAAI,kBAAkB;QACzE,IAAI,YAAY,IAAI,IAAI;YACtB,WAAW,IAAI,YAAY,IAAI;QACjC;QACA,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,cAAc;YAEd,iFAAiF;YACjF,IAAI,kBAAkB,QAAQ;gBAC5B,6EAA6E;gBAC7E,MAAM,iBAAiB,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAE9D,IAAI,eAAe,MAAM,GAAG,GAAG;oBAC7B,6DAA6D;oBAC7D,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC7C,OAAO;oBACL,qCAAqC;oBACrC;oBACA,iEAAiE;oBACjE,OAAO,IAAI,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,0DAA0D;QAC5D;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,UAAU,OAAO;QAChD,MAAM,YAAY,SAAS,CAAC,OAAO,KAAK,EAAE;QAC1C,MAAM,WAAW,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAE9C,IAAI,YAAY,GAAG;YACjB,OAAO;QACT,OAAO,IAAI,YAAY,IAAI;YACzB,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,KAAK,CAAC;QACxC,OAAO,IAAI,WAAW,GAAG;YACvB,OAAO,GAAG,KAAK,KAAK,CAAC,UAAU,KAAK,CAAC;QACvC,OAAO;YACL,OAAO,UAAU,kBAAkB;QACrC;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,oBAAoB,CAAC;QAC3B,eAAe;QACf,mBAAmB;IACrB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,wBAAwB,EAAE,cAAc,SAAS,YAAY,kFAAkF,CAAC;QAC5J,OAAO;YACL,iBAAiB;YACjB,aAAa;YACb,gBAAgB;QAClB;;0BAGA,6LAAC;gBAAI,WAAW,CAAC,8DAA8D,EAAE,cAAc,wBAAwB,oBAAoB;gBACzI,OAAO;oBAAE,iBAAiB;gBAAoB;0BAE9C,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,OAAO;wBACL,OAAO;wBACP,iBAAiB;oBACnB;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,yCAAyC;wBAC/F,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oBACpC;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,eAAe;wBACrE,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oBACpC;oBACA,SAAS;8BAET,cAAA,6LAAC;wBACC,WAAU;wBACV,SAAQ;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO;4BACL,WAAW,cAAc,eAAe;wBAC1C;kCAEA,cAAA,6LAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;;;;;YAKb,CAAC,6BACA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,oGAAoG,EAAE,aAAa,eAAe,KAAK,IAAI;oCACvJ,OAAO;wCACL,OAAO,aAAa,eAAe,gCAAgC;wCACnE,iBAAiB,aAAa,eAAe,4BAA4B;oCAC3E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,cAAc;4CAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,cAAc;4CAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,+NAAA,CAAA,kBAAe;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAKpF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,oGAAoG,EAAE,aAAa,aAAa,KAAK,IAAI;oCACrJ,OAAO;wCACL,OAAO,aAAa,aAAa,gCAAgC;wCACjE,iBAAiB,aAAa,aAAa,4BAA4B;oCACzE;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,YAAY;4CAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,YAAY;4CAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAK9E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,mGAAmG,CAAC;oCAChH,OAAO;wCACL,OAAO,aAAa,iBAAiB,gCAAgC;wCACrE,iBAAiB,aAAa,iBAAiB,4BAA4B;oCAC7E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,gBAAgB;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,gBAAgB;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAK7E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,mGAAmG,CAAC;oCAChH,OAAO;wCACL,OAAO,aAAa,kBAAkB,gCAAgC;wCACtE,iBAAiB,aAAa,kBAAkB,4BAA4B;oCAC9E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,iBAAiB;4CAChC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,iBAAiB;4CAChC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;;;;;;;kCAO5E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;4BACT,UAAU;4BACV,OAAO;gCACL,OAAO;gCACP,iBAAiB;4BACnB;4BACA,cAAc,CAAC;gCACb,IAAI,CAAC,mBAAmB;oCACtB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;4BACF;4BACA,cAAc,CAAC;gCACb,IAAI,CAAC,mBAAmB;oCACtB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;4BACF;;8CAEA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAU,OAAO;wCAAE,OAAO;oCAAsB;;;;;;gCAC/D,oBAAoB,gBAAgB;;;;;;;;;;;;kCAKzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;0CACD;;;;;;4BAIA,gCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAU,OAAO;wCAAE,OAAO;oCAA+B;8CAAG;;;;;;;;;;;4BAM9E,CAAC,kBAAkB,YAAY,GAAG,CAAC,CAAC;gCACnC,MAAM,WAAW,KAAK,EAAE,KAAK;gCAC7B,MAAM,aAAa,aAAa,KAAK,EAAE;gCACvC,qBACE,6LAAC;oCAEC,WAAW,CAAC,wGAAwG,EAAE,WAAW,KAAK,IAAI;oCAC1I,OAAO;wCACL,iBAAiB,WAAW,4BAA4B;oCAC1D;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;8CAE5C,2BACC,6LAAC;wCACC,UAAU,CAAA;4CAAO,EAAE,cAAc;4CAAI,mBAAmB,KAAK,EAAE;wCAAG;wCAClE,WAAU;;0DAEV,6LAAC;gDACC,SAAS;gDACT,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAA8B;gDAC9C,OAAO;gDACP,UAAU,CAAA,IAAK,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAW,CAAA;oDAAO,IAAI,EAAE,GAAG,KAAK,UAAU,YAAY;gDAAO;;;;;;0DAE/D,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;6DAKH;;0DACE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAA8B;8DAE7C,KAAK,KAAK;;;;;;;;;;;0DAIf,6LAAC;gDAAI,WAAU;gDAA8B,SAAS,CAAA,IAAK,EAAE,eAAe;0DAC1E,cAAA,6LAAC,+IAAA,CAAA,eAAY;;sEACX,6LAAC,+IAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,MAAK;gEACL,SAAQ;gEACR,WAAU;gEACV,OAAO;oEAAE,OAAO;gEAAsB;gEACtC,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEAC1C;gEACA,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEAC1C;gEACA,cAAW;0EAEX,cAAA,6LAAC,6NAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG5B,6LAAC,+IAAA,CAAA,sBAAmB;4DAClB,OAAM;4DACN,MAAK;4DACL,YAAY;4DACZ,WAAU;4DACV,OAAO;gEACL,iBAAiB;gEACjB,OAAO;4DACT;;8EAEA,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,aAAa,KAAK,EAAE,EAAE,KAAK,KAAK;oEAC/C,WAAU;oEACV,OAAO;wEACL,OAAO;wEACP,iBAAiB;oEACnB;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;8EACD;;;;;;8EAGD,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,aAAa,KAAK,EAAE;oEACnC,WAAU;oEACV,OAAO;wEACL,OAAO;wEACP,iBAAiB;oEACnB;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;8EACD;;;;;;;;;;;;;;;;;;;;;;;;;mCA7GN,KAAK,EAAE;;;;;4BAuHlB;4BAEC,CAAC,kBAAkB,YAAY,MAAM,KAAK,mBACzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uOAAA,CAAA,oBAAiB;wCAChB,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAA+B;;;;;;kDAEjD,6LAAC;wCAAE,WAAU;wCAAU,OAAO;4CAAE,OAAO;wCAA+B;kDAAG;;;;;;kDAGzE,6LAAC;wCAAE,WAAU;wCAAe,OAAO;4CAAE,OAAO;wCAA+B;kDAAG;;;;;;;;;;;;;;;;;;kCAQpF,6LAAC;wBAAI,WAAU;wBAA6B,OAAO;4BAAE,aAAa;wBAAwB;kCACxF,cAAA,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC;wCAAI,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAc;wCACxC,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACH,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAC,iBAAiB;gDAA0U;;;;;;0DAErW,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;oDAAqC,OAAO;wDAAE,OAAO;oDAA8B;8DAChG;;;;;;;;;;;;;;;;;;;;;;8CAKT,6LAAC,+IAAA,CAAA,sBAAmB;oCAClB,OAAM;oCACN,MAAK;oCACL,YAAY;oCACZ,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,OAAO;oCACT;;sDAEA,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;gDAC1B,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;;kEACJ,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIhC,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;gDAC3B,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;;kEACJ,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIpC,6LAAC,+IAAA,CAAA,mBAAgB;4CACf,SAAS;4CACT,WAAU;4CACV,OAAO;gDACL,OAAO;gDACP,iBAAiB;4CACnB;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;;8DAEA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUzC,6BACC,6LAAC;gBAAI,WAAU;gBAA4B,OAAO;oBAAE,aAAa;gBAAwB;0BACvF,cAAA,6LAAC,+IAAA,CAAA,eAAY;;sCACX,6LAAC,+IAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAC,iBAAiB;oCAA0U;;;;;;;;;;;;;;;;sCAIzW,6LAAC,+IAAA,CAAA,sBAAmB;4BAClB,OAAM;4BACN,MAAK;4BACL,YAAY;4BACZ,WAAU;4BACV,OAAO;gCACL,iBAAiB;gCACjB,OAAO;4BACT;;8CAEA,6LAAC,+IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;wCAC1B,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACJ,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIhC,6LAAC,+IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;wCAC3B,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACJ,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIpC,6LAAC,+IAAA,CAAA,mBAAgB;oCACf,SAAS;oCACT,WAAU;oCACV,OAAO;wCACL,OAAO;wCACP,iBAAiB;oCACnB;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;;sDAEA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;GA1kBM;;QAMA,0IAAA,CAAA,iBAAc;QACO,mIAAA,CAAA,UAAO;QACf,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;;;KATpB;uCA4kBS", "debugId": null}}, {"offset": {"line": 2419, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Layout.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { ReactNode, useState, useRef, useCallback } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useChatHistory } from \"@/providers/ChatHistoryContext\";\r\nimport { PageHeaderProvider } from '@/providers/PageHeaderContext';\r\nimport Header from './Header';\r\nimport Sidebar from './Sidebar';\r\n\r\ninterface LayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\r\n  const { setActiveChat } = useChatHistory();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);\r\n  const [isCreatingNewChat, setIsCreatingNewChat] = useState<boolean>(false);\r\n  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  const handleNewChat = useCallback(async () => {\r\n    // Prevent multiple simultaneous new chat creations\r\n    if (isCreatingNewChat) {\r\n      console.log('New chat creation already in progress, ignoring click');\r\n      return;\r\n    }\r\n\r\n    // Clear any existing debounce timeout\r\n    if (debounceTimeoutRef.current) {\r\n      clearTimeout(debounceTimeoutRef.current);\r\n    }\r\n\r\n    // Set debounce timeout to prevent rapid clicking\r\n    debounceTimeoutRef.current = setTimeout(async () => {\r\n      setIsCreatingNewChat(true);\r\n      \r\n      try {\r\n        // Clear the active chat to show the welcome message\r\n        await setActiveChat(null);\r\n        \r\n        // Navigate to the base chat route without a specific chat ID\r\n        router.push('/chat');\r\n      } catch (error) {\r\n        console.error('Error creating new chat:', error);\r\n      } finally {\r\n        setIsCreatingNewChat(false);\r\n      }\r\n    }, 300); // 300ms debounce to prevent rapid clicking\r\n  }, [isCreatingNewChat, setActiveChat, router]);\r\n\r\n  const handleToggleCollapse = (collapsed: boolean) => {\r\n    setSidebarCollapsed(collapsed);\r\n  };\r\n\r\n  // Cleanup timeout on unmount\r\n  React.useEffect(() => {\r\n    return () => {\r\n      if (debounceTimeoutRef.current) {\r\n        clearTimeout(debounceTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <PageHeaderProvider>\r\n      <div className=\"flex h-screen bg-sidebar-bg\" style={{ fontFamily: 'var(--font-inter), var(--font-noto-sans), sans-serif' }}>\r\n      <Sidebar \r\n        onNewChat={handleNewChat} \r\n        onToggleCollapse={handleToggleCollapse}\r\n        isCreatingNewChat={isCreatingNewChat}\r\n      />\r\n      <main className=\"flex flex-col flex-1 min-w-0 bg-sidebar-bg overflow-y-auto\">\r\n        <Header />\r\n        <div className=\"flex-1\">\r\n          {children}\r\n        </div>\r\n      </main>\r\n    </div>\r\n    </PageHeaderProvider>\r\n  );\r\n};\r\n\r\nexport default Layout;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAYA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEzD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YAChC,mDAAmD;YACnD,IAAI,mBAAmB;gBACrB,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,sCAAsC;YACtC,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;YAEA,iDAAiD;YACjD,mBAAmB,OAAO,GAAG;qDAAW;oBACtC,qBAAqB;oBAErB,IAAI;wBACF,oDAAoD;wBACpD,MAAM,cAAc;wBAEpB,6DAA6D;wBAC7D,OAAO,IAAI,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C,SAAU;wBACR,qBAAqB;oBACvB;gBACF;oDAAG,MAAM,2CAA2C;QACtD;4CAAG;QAAC;QAAmB;QAAe;KAAO;IAE7C,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,6BAA6B;IAC7B,6JAAA,CAAA,UAAK,CAAC,SAAS;4BAAC;YACd;oCAAO;oBACL,IAAI,mBAAmB,OAAO,EAAE;wBAC9B,aAAa,mBAAmB,OAAO;oBACzC;gBACF;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC,yIAAA,CAAA,qBAAkB;kBACjB,cAAA,6LAAC;YAAI,WAAU;YAA8B,OAAO;gBAAE,YAAY;YAAuD;;8BACzH,6LAAC,0IAAA,CAAA,UAAO;oBACN,WAAW;oBACX,kBAAkB;oBAClB,mBAAmB;;;;;;8BAErB,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,yIAAA,CAAA,UAAM;;;;;sCACP,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMX;GApEM;;QACsB,0IAAA,CAAA,iBAAc;QACzB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHxB;uCAsES", "debugId": null}}, {"offset": {"line": 2564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/onboarding.ts"], "sourcesContent": ["// Onboarding utility functions\r\n\r\nimport { ROUTES } from '@/lib/constants';\r\n\r\n/**\r\n * Determines the appropriate redirect path based on user authentication and onboarding status\r\n */\r\nexport function getRedirectPath(\r\n  isAuthenticated: boolean,\r\n  isNewUser: boolean,\r\n  currentPath: string\r\n): string | null {\r\n  // Not authenticated - redirect to login (except for public routes)\r\n  if (!isAuthenticated) {\r\n    const publicRoutes = [\r\n      ROUTES.HOME,\r\n      ROUTES.LOGIN,\r\n      ROUTES.REGISTER,\r\n      ROUTES.AUTH.CALLBACK,\r\n      ROUTES.OAUTH.CALLBACK,\r\n      ROUTES.ONBOARDING,\r\n    ];\r\n\r\n    if (!publicRoutes.includes(currentPath as any)) {\r\n      return ROUTES.LOGIN;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // Authenticated user scenarios\r\n  if (isAuthenticated) {\r\n    // New user not on onboarding page - redirect to onboarding\r\n    if (isNewUser && currentPath !== ROUTES.ONBOARDING) {\r\n      const publicRoutes = [\r\n        ROUTES.HOME,\r\n        ROUTES.LOGIN,\r\n        ROUTES.REGISTER,\r\n        ROUTES.AUTH.CALLBACK,\r\n        ROUTES.OAUTH.CALLBACK,\r\n      ];\r\n\r\n      // Don't redirect if on public routes (except login)\r\n      if (!publicRoutes.includes(currentPath as any) || currentPath === ROUTES.LOGIN) {\r\n        return ROUTES.ONBOARDING;\r\n      }\r\n    }\r\n    \r\n    // Existing user on onboarding page - redirect to dashboard\r\n    if (!isNewUser && currentPath === ROUTES.ONBOARDING) {\r\n      return ROUTES.DASHBOARD;\r\n    }\r\n    \r\n    // Authenticated user on login or register page - redirect based on onboarding status\r\n    if (currentPath === ROUTES.LOGIN || currentPath === ROUTES.REGISTER) {\r\n      return isNewUser ? ROUTES.ONBOARDING : ROUTES.DASHBOARD;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Checks if a route requires authentication\r\n */\r\nexport function isProtectedRoute(path: string): boolean {\r\n  const publicRoutes = [\r\n    ROUTES.HOME,\r\n    ROUTES.LOGIN,\r\n    ROUTES.REGISTER,\r\n    ROUTES.AUTH.CALLBACK,\r\n    ROUTES.OAUTH.CALLBACK,\r\n    ROUTES.ONBOARDING,\r\n  ];\r\n\r\n  return !publicRoutes.includes(path as any);\r\n}\r\n\r\n/**\r\n * Checks if a route is accessible to new users\r\n */\r\nexport function isNewUserAccessibleRoute(path: string): boolean {\r\n  const newUserRoutes = [\r\n    ROUTES.HOME,\r\n    ROUTES.LOGIN,\r\n    ROUTES.REGISTER,\r\n    ROUTES.AUTH.CALLBACK,\r\n    ROUTES.OAUTH.CALLBACK,\r\n    ROUTES.ONBOARDING,\r\n  ];\r\n\r\n  return newUserRoutes.includes(path as any);\r\n}\r\n\r\n/**\r\n * Gets the next step in the onboarding flow after completion\r\n */\r\nexport function getPostOnboardingRedirect(): string {\r\n  return ROUTES.DASHBOARD;\r\n}\r\n\r\n/**\r\n * Validates if onboarding completion is allowed from the current state\r\n */\r\nexport function canCompleteOnboarding(\r\n  isAuthenticated: boolean,\r\n  isNewUser: boolean,\r\n  currentPath: string\r\n): boolean {\r\n  return isAuthenticated && isNewUser && currentPath === ROUTES.ONBOARDING;\r\n}\r\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;AAE/B;AAAA;;AAKO,SAAS,gBACd,eAAwB,EACxB,SAAkB,EAClB,WAAmB;IAEnB,mEAAmE;IACnE,IAAI,CAAC,iBAAiB;QACpB,MAAM,eAAe;YACnB,oIAAA,CAAA,SAAM,CAAC,IAAI;YACX,oIAAA,CAAA,SAAM,CAAC,KAAK;YACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;YACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;YACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YACrB,oIAAA,CAAA,SAAM,CAAC,UAAU;SAClB;QAED,IAAI,CAAC,aAAa,QAAQ,CAAC,cAAqB;YAC9C,OAAO,oIAAA,CAAA,SAAM,CAAC,KAAK;QACrB;QACA,OAAO;IACT;IAEA,+BAA+B;IAC/B,IAAI,iBAAiB;QACnB,2DAA2D;QAC3D,IAAI,aAAa,gBAAgB,oIAAA,CAAA,SAAM,CAAC,UAAU,EAAE;YAClD,MAAM,eAAe;gBACnB,oIAAA,CAAA,SAAM,CAAC,IAAI;gBACX,oIAAA,CAAA,SAAM,CAAC,KAAK;gBACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;gBACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;gBACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;aACtB;YAED,oDAAoD;YACpD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAuB,gBAAgB,oIAAA,CAAA,SAAM,CAAC,KAAK,EAAE;gBAC9E,OAAO,oIAAA,CAAA,SAAM,CAAC,UAAU;YAC1B;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAAC,aAAa,gBAAgB,oIAAA,CAAA,SAAM,CAAC,UAAU,EAAE;YACnD,OAAO,oIAAA,CAAA,SAAM,CAAC,SAAS;QACzB;QAEA,qFAAqF;QACrF,IAAI,gBAAgB,oIAAA,CAAA,SAAM,CAAC,KAAK,IAAI,gBAAgB,oIAAA,CAAA,SAAM,CAAC,QAAQ,EAAE;YACnE,OAAO,YAAY,oIAAA,CAAA,SAAM,CAAC,UAAU,GAAG,oIAAA,CAAA,SAAM,CAAC,SAAS;QACzD;IACF;IAEA,OAAO;AACT;AAKO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,eAAe;QACnB,oIAAA,CAAA,SAAM,CAAC,IAAI;QACX,oIAAA,CAAA,SAAM,CAAC,KAAK;QACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;QACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;QACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QACrB,oIAAA,CAAA,SAAM,CAAC,UAAU;KAClB;IAED,OAAO,CAAC,aAAa,QAAQ,CAAC;AAChC;AAKO,SAAS,yBAAyB,IAAY;IACnD,MAAM,gBAAgB;QACpB,oIAAA,CAAA,SAAM,CAAC,IAAI;QACX,oIAAA,CAAA,SAAM,CAAC,KAAK;QACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;QACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;QACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QACrB,oIAAA,CAAA,SAAM,CAAC,UAAU;KAClB;IAED,OAAO,cAAc,QAAQ,CAAC;AAChC;AAKO,SAAS;IACd,OAAO,oIAAA,CAAA,SAAM,CAAC,SAAS;AACzB;AAKO,SAAS,sBACd,eAAwB,EACxB,SAAkB,EAClB,WAAmB;IAEnB,OAAO,mBAAmB,aAAa,gBAAgB,oIAAA,CAAA,SAAM,CAAC,UAAU;AAC1E", "debugId": null}}, {"offset": {"line": 2655, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useAuth } from '@/providers/AuthContext';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { isProtectedRoute } from '@/lib/utils/onboarding';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requireAuth?: boolean;\n  redirectTo?: string;\n}\n\nexport default function ProtectedRoute({ \n  children, \n  requireAuth = true, \n  redirectTo = '/login' \n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading, isNewUser, signIn } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isCheckingAuth, setIsCheckingAuth] = useState(true);\n  const [hasTriedAutoAuth, setHasTriedAutoAuth] = useState(false);\n\n  useEffect(() => {\n    const checkAuthentication = async () => {\n      // Skip auth check for public routes\n      if (!requireAuth || !isProtectedRoute(pathname)) {\n        setIsCheckingAuth(false);\n        return;\n      }\n\n      // If already authenticated, handle onboarding redirect\n      if (isAuthenticated) {\n        if (isNewUser && pathname !== '/onboarding') {\n          console.log('New user on protected route, redirecting to onboarding');\n          router.push('/onboarding');\n          return;\n        }\n        if (!isNewUser && pathname === '/onboarding') {\n          console.log('Existing user on onboarding, redirecting to dashboard');\n          router.push('/dashboard');\n          return;\n        }\n        setIsCheckingAuth(false);\n        return;\n      }\n\n      // If not authenticated and haven't tried auto-auth yet, try it once\n      if (!isAuthenticated && !hasTriedAutoAuth && !isLoading) {\n        setHasTriedAutoAuth(true);\n        console.log('Attempting automatic authentication from stored tokens...');\n\n        try {\n          await signIn(undefined, false); // Don't redirect, just authenticate\n          // If successful, the useEffect will re-run due to isAuthenticated change\n        } catch (error) {\n          console.log('Auto-authentication failed, redirecting to login');\n          router.push(redirectTo);\n        }\n        return;\n      }\n\n      // If not authenticated and already tried auto-auth, redirect to login\n      if (!isAuthenticated && hasTriedAutoAuth && !isLoading) {\n        console.log('Not authenticated, redirecting to login');\n        router.push(redirectTo);\n        return;\n      }\n\n      setIsCheckingAuth(false);\n    };\n\n    checkAuthentication();\n  }, [isAuthenticated, isLoading, isNewUser, pathname, requireAuth, redirectTo, router, signIn, hasTriedAutoAuth]);\n\n  // Show loading state while checking authentication\n  if (isCheckingAuth || isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">Checking authentication...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // For protected routes, only render children if authenticated\n  if (requireAuth && !isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold mb-4\">Authentication Required</h2>\n          <p className=\"text-muted-foreground mb-4\">Please sign in to access this page.</p>\n          <button\n            onClick={() => router.push(redirectTo)}\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n          >\n            Sign In\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return <>{children}</>;\n}\n\n// Higher-order component for easy wrapping\nexport function withProtectedRoute<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: Omit<ProtectedRouteProps, 'children'>\n) {\n  return function ProtectedComponent(props: P) {\n    return (\n      <ProtectedRoute {...options}>\n        <Component {...props} />\n      </ProtectedRoute>\n    );\n  };\n}\n\n// Hook for manual authentication checks\nexport function useRequireAuth(redirectTo: string = '/login') {\n  const { isAuthenticated, isLoading, signIn } = useAuth();\n  const router = useRouter();\n  const [isChecking, setIsChecking] = useState(true);\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (isLoading) return;\n\n      if (!isAuthenticated) {\n        try {\n          await signIn(undefined, false); // Don't redirect, just authenticate\n        } catch {\n          router.push(redirectTo);\n        }\n      }\n      setIsChecking(false);\n    };\n\n    checkAuth();\n  }, [isAuthenticated, isLoading, signIn, router, redirectTo]);\n\n  return { isAuthenticated, isLoading: isLoading || isChecking };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAae,SAAS,eAAe,EACrC,QAAQ,EACR,cAAc,IAAI,EAClB,aAAa,QAAQ,EACD;;IACpB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAChE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;gEAAsB;oBAC1B,oCAAoC;oBACpC,IAAI,CAAC,eAAe,CAAC,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;wBAC/C,kBAAkB;wBAClB;oBACF;oBAEA,uDAAuD;oBACvD,IAAI,iBAAiB;wBACnB,IAAI,aAAa,aAAa,eAAe;4BAC3C,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;4BACZ;wBACF;wBACA,IAAI,CAAC,aAAa,aAAa,eAAe;4BAC5C,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;4BACZ;wBACF;wBACA,kBAAkB;wBAClB;oBACF;oBAEA,oEAAoE;oBACpE,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,WAAW;wBACvD,oBAAoB;wBACpB,QAAQ,GAAG,CAAC;wBAEZ,IAAI;4BACF,MAAM,OAAO,WAAW,QAAQ,oCAAoC;wBACpE,yEAAyE;wBAC3E,EAAE,OAAO,OAAO;4BACd,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;wBACd;wBACA;oBACF;oBAEA,sEAAsE;oBACtE,IAAI,CAAC,mBAAmB,oBAAoB,CAAC,WAAW;wBACtD,QAAQ,GAAG,CAAC;wBACZ,OAAO,IAAI,CAAC;wBACZ;oBACF;oBAEA,kBAAkB;gBACpB;;YAEA;QACF;mCAAG;QAAC;QAAiB;QAAW;QAAW;QAAU;QAAa;QAAY;QAAQ;QAAQ;KAAiB;IAE/G,mDAAmD;IACnD,IAAI,kBAAkB,WAAW;QAC/B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,8DAA8D;IAC9D,IAAI,eAAe,CAAC,iBAAiB;QACnC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBAAO;kBAAG;;AACZ;GA9FwB;;QAKoC,mIAAA,CAAA,UAAO;QAClD,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAPN;AAiGjB,SAAS,mBACd,SAAiC,EACjC,OAA+C;IAE/C,OAAO,SAAS,mBAAmB,KAAQ;QACzC,qBACE,6LAAC;YAAgB,GAAG,OAAO;sBACzB,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,aAAqB,QAAQ;;IAC1D,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACrD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;sDAAY;oBAChB,IAAI,WAAW;oBAEf,IAAI,CAAC,iBAAiB;wBACpB,IAAI;4BACF,MAAM,OAAO,WAAW,QAAQ,oCAAoC;wBACtE,EAAE,OAAM;4BACN,OAAO,IAAI,CAAC;wBACd;oBACF;oBACA,cAAc;gBAChB;;YAEA;QACF;mCAAG;QAAC;QAAiB;QAAW;QAAQ;QAAQ;KAAW;IAE3D,OAAO;QAAE;QAAiB,WAAW,aAAa;IAAW;AAC/D;IAvBgB;;QACiC,mIAAA,CAAA,UAAO;QACvC,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 2897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/hooks/usePageTitle.ts"], "sourcesContent": ["import { useEffect, useMemo } from 'react';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\n\r\ninterface UsePageTitleOptions {\r\n  title: string;\r\n  subtitle?: string;\r\n  icon?: React.ComponentType<{ className?: string }>;\r\n}\r\n\r\n/**\r\n * Custom hook for setting page-specific header information\r\n * \r\n * Industry standard approach for managing page titles and metadata.\r\n * Automatically handles cleanup when component unmounts.\r\n * \r\n * @param options - Page header configuration\r\n */\r\nexport const usePageTitle = (options: UsePageTitleOptions) => {\r\n  const { setPageHeader, resetPageHeader } = usePageHeader();\r\n\r\n  useEffect(() => {\r\n    // Set the page header when component mounts or options change\r\n    setPageHeader(options);\r\n\r\n    // Cleanup: reset to default when component unmounts\r\n    return () => {\r\n      resetPageHeader();\r\n    };\r\n  }, [\r\n    options.title, \r\n    options.subtitle, \r\n    options.icon, \r\n    setPageHeader, \r\n    resetPageHeader\r\n  ]);\r\n};\r\n\r\n/**\r\n * Simplified hook for just setting a page title\r\n * \r\n * @param title - Page title\r\n * @param icon - Optional icon component\r\n */\r\nexport const useSimplePageTitle = (title: string, icon?: React.ComponentType<{ className?: string }>) => {\r\n  usePageTitle({ title, icon });\r\n}; "], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAgBO,MAAM,eAAe,CAAC;;IAC3B,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,8DAA8D;YAC9D,cAAc;YAEd,oDAAoD;YACpD;0CAAO;oBACL;gBACF;;QACF;iCAAG;QACD,QAAQ,KAAK;QACb,QAAQ,QAAQ;QAChB,QAAQ,IAAI;QACZ;QACA;KACD;AACH;GAlBa;;QACgC,yIAAA,CAAA,gBAAa;;;AAyBnD,MAAM,qBAAqB,CAAC,OAAe;;IAChD,aAAa;QAAE;QAAO;IAAK;AAC7B;IAFa;;QACX", "debugId": null}}, {"offset": {"line": 2954, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils/index\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold text-white\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6 text-white\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAAA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 3070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n} "], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;AAAA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants } "], "names": [], "mappings": ";;;;;AACA;AAEA;AAAA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 3258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/app/%28dashboard%29/reports/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Layout from '@/components/layout/Layout';\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport Link from 'next/link';\r\nimport { useApi } from '@/providers/ApiContext';\r\nimport ProtectedRoute from '@/components/auth/ProtectedRoute';\r\nimport { ReportInfo } from '@/types';\r\nimport { usePageTitle } from '@/hooks/usePageTitle';\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Download, Eye, FileText, RefreshCw } from 'lucide-react';\r\n\r\nfunction ReportsPageContent() {\r\n  const { isAuthenticated } = useAuth();\r\n  const { listUserReports } = useApi();\r\n  const [reports, setReports] = useState<ReportInfo[]>([]);\r\n  const [isLoadingReports, setIsLoadingReports] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const loadReports = useCallback(async () => {\r\n    setIsLoadingReports(true);\r\n    setError(null);\r\n    try {\r\n      const response = await listUserReports({ max_reports: 100 });\r\n      setReports(response.reports);\r\n    } catch (error) {\r\n      console.error('Error loading reports:', error);\r\n      setError('Failed to load reports. Please try again.');\r\n    } finally {\r\n      setIsLoadingReports(false);\r\n    }\r\n  }, [listUserReports]);\r\n\r\n  useEffect(() => {\r\n    if (isAuthenticated) {\r\n      loadReports();\r\n    } else {\r\n      // Clear reports when user is not authenticated\r\n      setReports([]);\r\n      setError(null);\r\n    }\r\n  }, [isAuthenticated, loadReports]);\r\n\r\n  // Add retry functionality for failed API calls\r\n  const retryLoadReports = async () => {\r\n    setError(null);\r\n    await loadReports();\r\n  };\r\n\r\n  const formatFileSize = (bytes: number): string => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  const formatDate = (dateString: string): string => {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n  };\r\n\r\n  const getFileIcon = () => {\r\n    return <FileText className=\"h-4 w-4\" />;\r\n  };\r\n\r\n  const getFormatBadgeColor = (format: string): string => {\r\n    switch (format.toLowerCase()) {\r\n      case 'csv':\r\n        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'xlsx':\r\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'json':\r\n        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';\r\n      default:\r\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  };\r\n\r\n  const handleViewReport = (report: ReportInfo) => {\r\n    // For CSV files, we can view them directly in the browser\r\n    if (report.format === 'csv') {\r\n      window.open(report.download_url, '_blank');\r\n    } else {\r\n      // For Excel files, download them as they can't be viewed directly in browser\r\n      handleDownloadReport(report);\r\n    }\r\n  };\r\n\r\n  const handleDownloadReport = (report: ReportInfo) => {\r\n    const link = document.createElement('a');\r\n    link.href = report.download_url;\r\n    link.download = report.file_name;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  };\r\n\r\n  return (\r\n      <div className=\"flex flex-col h-full p-10\">\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4\">\r\n          <div className=\"flex-1\">\r\n            {/* Removed hardcoded header - now using centralized header system */}\r\n          </div>\r\n          <Button\r\n            onClick={loadReports}\r\n            disabled={isLoadingReports}\r\n            variant=\"outline\"\r\n            className=\"flex items-center gap-2 w-full sm:w-auto\"\r\n          >\r\n            <RefreshCw className={`h-4 w-4 ${isLoadingReports ? 'animate-spin' : ''}`} />\r\n            Refresh\r\n          </Button>\r\n        </div>\r\n\r\n        {error && (\r\n          <Card className=\"mb-6 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20\">\r\n            <CardContent className=\"pt-6\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <p className=\"text-red-800 dark:text-red-200\">{error}</p>\r\n                <Button\r\n                  onClick={retryLoadReports}\r\n                  disabled={isLoadingReports}\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"ml-4\"\r\n                >\r\n                  Try Again\r\n                </Button>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        )}\r\n\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2 text-gray-900 dark:text-white\">\r\n              <FileText className=\"h-5 w-5 text-gray-600 dark:text-white\" />\r\n              Your Reports ({reports.length})\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            {isLoadingReports ? (\r\n              <div className=\"flex items-center justify-center py-8\">\r\n                <div className=\"flex items-center gap-2 text-gray-600 dark:text-black\">\r\n                  <RefreshCw className=\"h-4 w-4 animate-spin\" />\r\n                  Loading reports...\r\n                </div>\r\n              </div>\r\n            ) : reports.length === 0 ? (\r\n              <div className=\"text-center py-8\">\r\n                <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-3\" />\r\n                <p className=\"text-gray-600 dark:text-gray-400\">No reports found</p>\r\n                <p className=\"text-sm text-gray-500 dark:text-black mt-1\">\r\n                  Reports will appear here after you generate them from your chats\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"overflow-x-auto\">\r\n                <Table>\r\n                  <TableHeader>\r\n                    <TableRow>\r\n                      <TableHead>File</TableHead>\r\n                      <TableHead>Format</TableHead>\r\n                      <TableHead>Size</TableHead>\r\n                      <TableHead>Session</TableHead>\r\n                      <TableHead>Created</TableHead>\r\n                      <TableHead className=\"text-right\">Actions</TableHead>\r\n                    </TableRow>\r\n                  </TableHeader>\r\n                  <TableBody>\r\n                    {reports.map((report) => (\r\n                      <TableRow key={report.key}>\r\n                        <TableCell>\r\n                          <div className=\"flex items-center gap-2\">\r\n                            {getFileIcon()}\r\n                            <span className=\"font-medium text-sm\">{report.file_name}</span>\r\n                          </div>\r\n                        </TableCell>\r\n                        <TableCell>\r\n                          <Badge className={`${getFormatBadgeColor(report.format)} text-xs`}>\r\n                            {report.format.toUpperCase()}\r\n                          </Badge>\r\n                        </TableCell>\r\n                        <TableCell className=\"text-sm text-gray-600 dark:text-white\">\r\n                          {formatFileSize(report.size)}\r\n                        </TableCell>\r\n                        <TableCell className=\"text-sm\">\r\n                          <Link\r\n                            href={`/chat/chat_sess${report.session_id}`}\r\n                            className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:underline\"\r\n                          >\r\n                            {report.session_id}\r\n                          </Link>\r\n                        </TableCell>\r\n                        <TableCell className=\"text-sm text-gray-600 dark:text-white\">\r\n                          {formatDate(report.last_modified)}\r\n                        </TableCell>\r\n                        <TableCell className=\"text-right\">\r\n                          <div className=\"flex items-center justify-end gap-2\">\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              size=\"sm\"\r\n                              onClick={() => handleViewReport(report)}\r\n                              className=\"h-8 w-8 p-0\"\r\n                              title={report.format === 'csv' ? 'View in browser' : 'Download file'}\r\n                            >\r\n                              {report.format === 'csv' ? (\r\n                                <Eye className=\"h-4 w-4\" />\r\n                              ) : (\r\n                                <Download className=\"h-4 w-4\" />\r\n                              )}\r\n                            </Button>\r\n                          </div>\r\n                        </TableCell>\r\n                      </TableRow>\r\n                    ))}\r\n                  </TableBody>\r\n                </Table>\r\n              </div>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n  );\r\n}\r\n\r\nfunction ReportsPageWithTitle() {\r\n  // Set page title - memoized to prevent re-renders\r\n  const pageConfig = useMemo(() => ({\r\n    title: 'Reports',\r\n    icon: FileText\r\n  }), []);\r\n\r\n  usePageTitle(pageConfig);\r\n\r\n  return <ReportsPageContent />;\r\n}\r\n\r\nexport default function ReportsPage() {\r\n  return (\r\n    <ProtectedRoute>\r\n      <Layout>\r\n        <ReportsPageWithTitle />\r\n      </Layout>\r\n    </ProtectedRoute>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;;;;;AAeA,SAAS;;IACP,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,SAAM,AAAD;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC9B,oBAAoB;YACpB,SAAS;YACT,IAAI;gBACF,MAAM,WAAW,MAAM,gBAAgB;oBAAE,aAAa;gBAAI;gBAC1D,WAAW,SAAS,OAAO;YAC7B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,SAAS;YACX,SAAU;gBACR,oBAAoB;YACtB;QACF;sDAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,iBAAiB;gBACnB;YACF,OAAO;gBACL,+CAA+C;gBAC/C,WAAW,EAAE;gBACb,SAAS;YACX;QACF;uCAAG;QAAC;QAAiB;KAAY;IAEjC,+CAA+C;IAC/C,MAAM,mBAAmB;QACvB,SAAS;QACT,MAAM;IACR;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,KAAK,MAAM,KAAK,kBAAkB,CAAC,EAAE,EAAE;YAAE,MAAM;YAAW,QAAQ;QAAU;IAC5G;IAEA,MAAM,cAAc;QAClB,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,0DAA0D;QAC1D,IAAI,OAAO,MAAM,KAAK,OAAO;YAC3B,OAAO,IAAI,CAAC,OAAO,YAAY,EAAE;QACnC,OAAO;YACL,6EAA6E;YAC7E,qBAAqB;QACvB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,OAAO,YAAY;QAC/B,KAAK,QAAQ,GAAG,OAAO,SAAS;QAChC,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU;wBACV,SAAQ;wBACR,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAW,CAAC,QAAQ,EAAE,mBAAmB,iBAAiB,IAAI;;;;;;4BAAI;;;;;;;;;;;;;YAKhF,uBACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAkC;;;;;;0CAC/C,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU;gCACV,SAAQ;gCACR,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQT,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA0C;gCAC/C,QAAQ,MAAM;gCAAC;;;;;;;;;;;;kCAGlC,6LAAC,mIAAA,CAAA,cAAW;kCACT,iCACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;;;;;mCAIhD,QAAQ,MAAM,KAAK,kBACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,6LAAC;oCAAE,WAAU;8CAA6C;;;;;;;;;;;iDAK5D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kDACJ,6LAAC,oIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8DACP,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAGtC,6LAAC,oIAAA,CAAA,YAAS;kDACP,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,oIAAA,CAAA,WAAQ;;kEACP,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;gEACZ;8EACD,6LAAC;oEAAK,WAAU;8EAAuB,OAAO,SAAS;;;;;;;;;;;;;;;;;kEAG3D,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAW,GAAG,oBAAoB,OAAO,MAAM,EAAE,QAAQ,CAAC;sEAC9D,OAAO,MAAM,CAAC,WAAW;;;;;;;;;;;kEAG9B,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,eAAe,OAAO,IAAI;;;;;;kEAE7B,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,eAAe,EAAE,OAAO,UAAU,EAAE;4DAC3C,WAAU;sEAET,OAAO,UAAU;;;;;;;;;;;kEAGtB,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,WAAW,OAAO,aAAa;;;;;;kEAElC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,iBAAiB;gEAChC,WAAU;gEACV,OAAO,OAAO,MAAM,KAAK,QAAQ,oBAAoB;0EAEpD,OAAO,MAAM,KAAK,sBACjB,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;yFAEf,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;+CAtCf,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqD/C;GArNS;;QACqB,mIAAA,CAAA,UAAO;QACP,kIAAA,CAAA,SAAM;;;KAF3B;AAuNT,SAAS;;IACP,kDAAkD;IAClD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE,IAAM,CAAC;gBAChC,OAAO;gBACP,MAAM,iNAAA,CAAA,WAAQ;YAChB,CAAC;mDAAG,EAAE;IAEN,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE;IAEb,qBAAO,6LAAC;;;;;AACV;IAVS;;QAOP,+HAAA,CAAA,eAAY;;;MAPL;AAYM,SAAS;IACtB,qBACE,6LAAC,+IAAA,CAAA,UAAc;kBACb,cAAA,6LAAC,yIAAA,CAAA,UAAM;sBACL,cAAA,6LAAC;;;;;;;;;;;;;;;AAIT;MARwB", "debugId": null}}]}