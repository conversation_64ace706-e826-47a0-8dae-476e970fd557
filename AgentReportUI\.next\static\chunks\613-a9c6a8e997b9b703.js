"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[613],{2564:(e,t,r)=>{r.d(t,{Qg:()=>i,bL:()=>s});var n=r(12115),o=r(63655),l=r(95155),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=n.forwardRef((e,t)=>(0,l.jsx)(o.sG.span,{...e,ref:t,style:{...i,...e.style}}));a.displayName="VisuallyHidden";var s=a},89613:(e,t,r)=>{r.d(t,{Kq:()=>U,UC:()=>Z,ZL:()=>Y,bL:()=>W,i3:()=>K,l9:()=>X});var n=r(12115),o=r(85185),l=r(6101),i=r(46081),a=r(19178),s=r(61285),u=r(35152),c=r(34378),d=r(28905),p=r(63655),f=r(99708),h=r(5845),x=r(2564),v=r(95155),[g,y]=(0,i.A)("Tooltip",[u.Bk]),b=(0,u.Bk)(),m="TooltipProvider",w="tooltip.open",[C,T]=g(m),E=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:i}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(C,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:l,children:i})};E.displayName=m;var k="Tooltip",[L,j]=g(k),R=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:l,onOpenChange:i,disableHoverableContent:a,delayDuration:c}=e,d=T(k,e.__scopeTooltip),p=b(t),[f,x]=n.useState(null),g=(0,s.B)(),y=n.useRef(0),m=null!=a?a:d.disableHoverableContent,C=null!=c?c:d.delayDuration,E=n.useRef(!1),[j,R]=(0,h.i)({prop:o,defaultProp:null!=l&&l,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(w))):d.onClose(),null==i||i(e)},caller:k}),_=n.useMemo(()=>j?E.current?"delayed-open":"instant-open":"closed",[j]),P=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,E.current=!1,R(!0)},[R]),M=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,R(!1)},[R]),D=n.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{E.current=!0,R(!0),y.current=0},C)},[C,R]);return n.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,v.jsx)(u.bL,{...p,children:(0,v.jsx)(L,{scope:t,contentId:g,open:j,stateAttribute:_,trigger:f,onTriggerChange:x,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?D():P()},[d.isOpenDelayedRef,D,P]),onTriggerLeave:n.useCallback(()=>{m?M():(window.clearTimeout(y.current),y.current=0)},[M,m]),onOpen:P,onClose:M,disableHoverableContent:m,children:r})})};R.displayName=k;var _="TooltipTrigger",P=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,a=j(_,r),s=T(_,r),c=b(r),d=n.useRef(null),f=(0,l.s)(t,d,a.onTriggerChange),h=n.useRef(!1),x=n.useRef(!1),g=n.useCallback(()=>h.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,v.jsx)(u.Mz,{asChild:!0,...c,children:(0,v.jsx)(p.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...i,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(x.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),x.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),x.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),h.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});P.displayName=_;var M="TooltipPortal",[D,N]=g(M,{forceMount:void 0}),O=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,l=j(M,t);return(0,v.jsx)(D,{scope:t,forceMount:r,children:(0,v.jsx)(d.C,{present:r||l.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};O.displayName=M;var B="TooltipContent",I=n.forwardRef((e,t)=>{let r=N(B,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...l}=e,i=j(B,e.__scopeTooltip);return(0,v.jsx)(d.C,{present:n||i.open,children:i.disableHoverableContent?(0,v.jsx)(q,{side:o,...l,ref:t}):(0,v.jsx)(A,{side:o,...l,ref:t})})}),A=n.forwardRef((e,t)=>{let r=j(B,e.__scopeTooltip),o=T(B,e.__scopeTooltip),i=n.useRef(null),a=(0,l.s)(t,i),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,p=i.current,{onPointerInTransitChange:f}=o,h=n.useCallback(()=>{u(null),f(!1)},[f]),x=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(r,n,o,l)){case l:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(c&&p){let e=e=>x(e,p),t=e=>x(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,x,h]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],s=i.x,u=i.y,c=a.x,d=a.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,h]),(0,v.jsx)(q,{...e,ref:a})}),[H,F]=g(k,{isInside:!1}),S=(0,f.Dc)("TooltipContent"),q=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":l,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=e,d=j(B,r),p=b(r),{onClose:f}=d;return n.useEffect(()=>(document.addEventListener(w,f),()=>document.removeEventListener(w,f)),[f]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,v.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,v.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(S,{children:o}),(0,v.jsx)(H,{scope:r,isInside:!0,children:(0,v.jsx)(x.bL,{id:d.contentId,role:"tooltip",children:l||o})})]})})});I.displayName=B;var z="TooltipArrow",G=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=b(r);return F(z,r).isInside?null:(0,v.jsx)(u.i3,{...o,...n,ref:t})});G.displayName=z;var U=E,W=R,X=P,Y=O,Z=I,K=G}}]);