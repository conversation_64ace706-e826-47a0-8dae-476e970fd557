"""Query Understanding Agent

This module provides enhanced query understanding capabilities including:
- Intent classification and analysis
- Semantic query parsing
- Context-aware query enhancement
- Clarification question generation
- Domain-specific query templates
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from enum import Enum

from app.agents.base import Agent, AgentResponse
from app.utils.bedrock_client import Bedrock<PERSON><PERSON>
from app.utils.constants import FOLLOW_UP_INDICATORS
from app.services.conversational_context_service import conversational_context_service
from app.prompts.query_understanding import (
    INTENT_ANALYSIS_SYSTEM_PROMPT,
    ENTITY_EXTRACTION_SYSTEM_PROMPT,
    QUERY_ENHANCEMENT_SYSTEM_PROMPT as ENHANCED_QUERY_ENHANCEMENT_SYSTEM_PROMPT,
    CLARIFICATION_GENERATION_SYSTEM_PROMPT
)

logger = logging.getLogger(__name__)


class QueryIntent(Enum):
    """Classification of query intents."""
    ANALYTICAL = "analytical"  # Aggregations, trends, patterns
    OPERATIONAL = "operational"  # CRUD operations, specific records
    EXPLORATORY = "exploratory"  # Discovery, browsing, understanding data
    COMPARATIVE = "comparative"  # Comparisons between entities/time periods
    DIAGNOSTIC = "diagnostic"  # Troubleshooting, error analysis
    REPORTING = "reporting"  # Formatted reports, summaries
    UNCLEAR = "unclear"  # Ambiguous or incomplete queries


class QueryComplexity(Enum):
    """Classification of query complexity."""
    SIMPLE = "simple"  # Single table, basic filters
    MODERATE = "moderate"  # Multiple tables, joins, basic aggregations
    COMPLEX = "complex"  # Complex joins, subqueries, advanced analytics
    VERY_COMPLEX = "very_complex"  # Multi-step analysis, complex business logic


class QueryUnderstandingAgent(Agent):
    """Enhanced query understanding agent with intent recognition and semantic analysis."""
    
    def __init__(self, agent_id: str = None):
        self.agent_id = agent_id or "query_understanding_agent"
        self.bedrock_client = BedrockClient()
        self.initialized = False
        
        # Query analysis cache for performance
        self._analysis_cache = {}
        
    async def initialize(self) -> None:
        """Initialize the agent."""
        if self.initialized:
            return
        self.initialized = True
        logger.info("✅ Query Understanding Agent initialized")
    
    async def process(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a query for enhanced understanding.
        
        Expected message format:
        {
            "query": "user's natural language question",
            "conversation_history": [...],
            "user_context": {...},  # Optional user preferences/context
            "database_schemas": [...],  # Optional available schemas
            "session_id": "session_identifier",  # For conversational context
            "user_id": "user_identifier"  # For conversational context
        }
        
        Returns:
        {
            "enhanced_query": "improved standalone query",
            "intent": "analytical|operational|exploratory|...",
            "complexity": "simple|moderate|complex|very_complex",
            "entities": [...],  # Identified entities
            "requirements": [...],  # Data requirements
            "clarifications": [...],  # Questions if query is unclear
            "confidence": 0.85,
            "analysis_metadata": {...}
        }
        """
        if not self.initialized:
            await self.initialize()
            
        query = message.get("query", "")
        if query is None:
            query = ""
        query = query.strip()

        conversation_history = message.get("conversation_history", [])
        user_context = message.get("user_context", {})
        database_schemas = message.get("database_schemas", [])
        session_id = message.get("session_id")
        user_id = message.get("user_id")

        if not query:
            return AgentResponse(
                self.agent_id,
                False,
                error="No query provided for analysis"
            ).to_dict()

        try:
            # Step 0: Get conversational context if session info is available
            conversational_context = {}
            if session_id and user_id:
                conversational_context = await conversational_context_service.extract_context_for_query(
                    session_id, query
                )

                # Check for references that need context resolution
                references = await conversational_context_service.identify_references_in_query(
                    session_id, query
                )
                conversational_context["references"] = references

            # Step 1: Analyze query intent and complexity (enhanced with context)
            intent_analysis = await self._analyze_query_intent_with_context(
                query, conversation_history, conversational_context
            )

            # Step 2: Extract entities and requirements (enhanced with context)
            entity_analysis = await self._extract_entities_and_requirements_with_context(
                query, conversation_history, database_schemas, conversational_context
            )

            # Step 3: Enhance query with conversational context
            enhanced_query = await self._enhance_query_with_conversational_context(
                query, conversation_history, intent_analysis, entity_analysis,
                conversational_context, session_id
            )
            
            # Step 4: Generate clarifications if needed (with context awareness)
            clarifications = await self._generate_clarifications_with_context(
                query, enhanced_query, intent_analysis, entity_analysis, conversational_context
            )

            # Step 5: Calculate confidence score (enhanced with context)
            confidence = self._calculate_confidence_with_context(
                intent_analysis, entity_analysis, clarifications, conversational_context
            )

            # Step 6: Detect table discussion intent
            is_table_discussion = self._detect_table_discussion_intent(query)
            
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=True,
                data={
                    "enhanced_query": enhanced_query,
                    "intent": intent_analysis["intent"],
                    "complexity": intent_analysis["complexity"],
                    "entities": entity_analysis["entities"],
                    "requirements": entity_analysis["requirements"],
                    "clarifications": clarifications,
                    "confidence": confidence,
                    "analysis_metadata": {
                        "original_query": query,
                        "processing_time": datetime.utcnow().isoformat(),
                        "intent_details": intent_analysis,
                        "entity_details": entity_analysis,
                        "is_table_discussion": is_table_discussion
                    },
                    "conversational_context": conversational_context
                }
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Error in query understanding: {str(e)}")
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error=f"Query analysis failed: {str(e)}"
            ).to_dict()
    
    async def _analyze_query_intent(
        self, 
        query: str, 
        conversation_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze the intent and complexity of a query."""
        
        # Create context from conversation history
        context_text = self._format_conversation_context(conversation_history)
        
        system_prompt = INTENT_ANALYSIS_SYSTEM_PROMPT

        user_prompt = f"""Query to analyze: "{query}"

{f"Conversation context: {context_text}" if context_text else "No previous conversation context."}

Analyze this query and provide your classification."""

        try:
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.2
            )
            
            # Clean and parse JSON response
            cleaned_response = self._clean_json_response(response)
            analysis = json.loads(cleaned_response)
            
            # Validate and set defaults
            analysis.setdefault("intent", "unclear")
            analysis.setdefault("complexity", "simple")
            analysis.setdefault("reasoning", "")
            analysis.setdefault("key_indicators", [])
            analysis.setdefault("business_context", "")
            
            return analysis
            
        except Exception as e:
            logger.warning(f"Intent analysis failed, using fallback: {e}")
            return self._fallback_intent_analysis(query)
    
    def _fallback_intent_analysis(self, query: str) -> Dict[str, Any]:
        """Fallback intent analysis using heuristics."""
        query_lower = query.lower()
        
        # Simple heuristic-based classification
        if any(word in query_lower for word in ["count", "sum", "average", "total", "trend", "pattern"]):
            intent = "analytical"
            complexity = "moderate"
        elif any(word in query_lower for word in ["show", "list", "find", "get", "what"]):
            intent = "operational"
            complexity = "simple"
        elif any(word in query_lower for word in ["compare", "versus", "vs", "difference"]):
            intent = "comparative"
            complexity = "moderate"
        elif any(word in query_lower for word in ["explore", "browse", "available", "tables"]):
            intent = "exploratory"
            complexity = "simple"
        else:
            intent = "unclear"
            complexity = "simple"
            
        return {
            "intent": intent,
            "complexity": complexity,
            "reasoning": "Fallback heuristic analysis",
            "key_indicators": [],
            "business_context": ""
        }

    async def _extract_entities_and_requirements(
        self,
        query: str,
        conversation_history: List[Dict[str, Any]],
        database_schemas: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Extract entities and data requirements from the query."""

        context_text = self._format_conversation_context(conversation_history)
        schema_text = self._format_schema_context(database_schemas)

        system_prompt = ENTITY_EXTRACTION_SYSTEM_PROMPT

        user_prompt = f"""Query to analyze: "{query}"

{f"Conversation context: {context_text}" if context_text else ""}
{f"Available database schemas: {schema_text}" if schema_text else ""}

Extract entities and requirements from this query."""

        try:
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.2
            )

            cleaned_response = self._clean_json_response(response)
            analysis = json.loads(cleaned_response)

            # Validate and set defaults
            analysis.setdefault("entities", [])
            analysis.setdefault("attributes", [])
            analysis.setdefault("filters", [])
            analysis.setdefault("relationships", [])
            analysis.setdefault("operations", [])
            analysis.setdefault("requirements", {})

            return analysis

        except Exception as e:
            logger.warning(f"Entity extraction failed, using fallback: {e}")
            return self._fallback_entity_extraction(query)

    def _fallback_entity_extraction(self, query: str) -> Dict[str, Any]:
        """Fallback entity extraction using simple heuristics."""
        query_lower = query.lower()

        # Simple keyword-based extraction
        entities = []
        attributes = []
        filters = []
        operations = []

        # Common business entities
        entity_keywords = ["customer", "product", "order", "sale", "user", "account", "transaction"]
        for keyword in entity_keywords:
            if keyword in query_lower:
                entities.append(keyword)

        # Common attributes
        attribute_keywords = ["name", "id", "date", "amount", "price", "status", "count"]
        for keyword in attribute_keywords:
            if keyword in query_lower:
                attributes.append(keyword)

        # Common operations
        if any(word in query_lower for word in ["count", "sum", "total"]):
            operations.append("aggregation")
        if any(word in query_lower for word in ["sort", "order"]):
            operations.append("sorting")
        if any(word in query_lower for word in ["where", "filter", "only"]):
            operations.append("filtering")

        return {
            "entities": entities,
            "attributes": attributes,
            "filters": filters,
            "relationships": [],
            "operations": operations,
            "requirements": {
                "tables_needed": entities,
                "join_requirements": [],
                "aggregation_needs": operations,
                "temporal_aspects": []
            }
        }

    async def _enhance_query_with_context(
        self,
        query: str,
        conversation_history: List[Dict[str, Any]],
        intent_analysis: Dict[str, Any],
        entity_analysis: Dict[str, Any]
    ) -> str:
        """Enhance the query with context and make it standalone."""

        # Check if enhancement is needed
        if not conversation_history or not self._needs_enhancement(query, conversation_history):
            return query

        context_text = self._format_conversation_context(conversation_history)

        system_prompt = ENHANCED_QUERY_ENHANCEMENT_SYSTEM_PROMPT

        user_prompt = f"""Original query: "{query}"

Conversation history:
{context_text}

Intent: {intent_analysis.get('intent', 'unclear')}
Key entities: {', '.join(entity_analysis.get('entities', []))}

Enhance this query to be complete and standalone if needed, otherwise return it unchanged."""

        try:
            enhanced_query = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.3
            )

            enhanced_query = enhanced_query.strip()

            # Validate enhancement
            if len(enhanced_query) < 5 or enhanced_query.lower() == query.lower():
                return query

            return enhanced_query

        except Exception as e:
            logger.warning(f"Query enhancement failed: {e}")
            return query

    async def _generate_clarifications(
        self,
        original_query: str,
        enhanced_query: str,
        intent_analysis: Dict[str, Any],
        entity_analysis: Dict[str, Any]
    ) -> List[str]:
        """Generate clarification questions if the query is unclear or incomplete."""

        clarifications = []

        # Check if query is unclear based on intent analysis
        if intent_analysis.get("intent") == "unclear":
            clarifications.append("Could you provide more details about what specific information you're looking for?")

        # Check for missing time context in analytical queries
        if (intent_analysis.get("intent") == "analytical" and
            not any(word in enhanced_query.lower() for word in ["date", "time", "when", "period", "month", "year"])):
            clarifications.append("What time period would you like to analyze?")

        # Check for missing comparison context in comparative queries
        if (intent_analysis.get("intent") == "comparative" and
            len(entity_analysis.get("entities", [])) < 2):
            clarifications.append("What would you like to compare this against?")

        # Check for vague entities - but only if context enhancement didn't resolve them
        vague_terms = ["it", "they", "these", "those", "that", "this"]
        if any(term in enhanced_query.lower().split() for term in vague_terms):
            # Only ask for clarification if the enhanced query still contains vague terms
            # and the original query was the same (meaning context didn't help)
            if enhanced_query.lower() == original_query.lower():
                clarifications.append("Could you specify what exactly you're referring to?")

        # Limit to most important clarifications
        return clarifications[:3]

    async def _generate_clarifications_with_context(
        self,
        original_query: str,
        enhanced_query: str,
        intent_analysis: Dict[str, Any],
        entity_analysis: Dict[str, Any],
        conversational_context: Dict[str, Any]
    ) -> List[str]:
        """Generate clarification questions with enhanced conversational context awareness."""

        clarifications = []

        # Check if query is unclear based on intent analysis
        if intent_analysis.get("intent") == "unclear":
            # But don't ask for clarification if this appears to be a follow-up question
            is_follow_up = self._is_likely_follow_up(original_query, conversational_context)
            if not is_follow_up:
                clarifications.append("Could you provide more details about what specific information you're looking for?")

        # Check for missing time context in analytical queries
        if (intent_analysis.get("intent") == "analytical" and
            not any(word in enhanced_query.lower() for word in ["date", "time", "when", "period", "month", "year"])):
            # Don't ask for time context if we have recent queries that might provide context
            needs_time_clarification = True
            if conversational_context:
                recent_queries = conversational_context.get("recent_queries", [])
                if recent_queries:
                    # If recent queries had time context, we might be continuing that analysis
                    last_query = recent_queries[-1]
                    if any(word in last_query.get("query_text", "").lower()
                           for word in ["date", "time", "when", "period", "month", "year"]):
                        needs_time_clarification = False

            if needs_time_clarification:
                clarifications.append("What time period would you like to analyze?")

        # Enhanced comparative query handling
        if intent_analysis.get("intent") == "comparative":
            has_context_for_comparison = self._has_comparison_context(
                entity_analysis, conversational_context, enhanced_query
            )

            if not has_context_for_comparison:
                clarifications.append("What would you like to compare this against?")

        # Enhanced vague entity handling with better context resolution
        vague_terms = ["it", "they", "these", "those", "that", "this"]
        if any(term in enhanced_query.lower().split() for term in vague_terms):
            context_resolved = self._is_context_resolved(
                original_query, enhanced_query, conversational_context
            )

            # Only ask for clarification if context didn't resolve the vague terms
            if not context_resolved:
                clarifications.append("Could you specify what exactly you're referring to?")

        # Limit to most important clarifications
        return clarifications[:2]  # Reduced to 2 to be less intrusive

    def _is_likely_follow_up(self, query: str, conversational_context: Dict[str, Any]) -> bool:
        """Determine if a query is likely a follow-up question."""
        if not conversational_context:
            return False

        query_lower = query.lower()

        # Check for follow-up indicators
        follow_up_indicators = [
            "these", "those", "it", "they", "them", "compare", "versus", "vs",
            "how about", "what about", "also", "too", "as well", "similarly"
        ]

        has_follow_up_indicators = any(indicator in query_lower for indicator in follow_up_indicators)
        has_recent_queries = len(conversational_context.get("recent_queries", [])) > 0

        return has_follow_up_indicators and has_recent_queries

    def _has_comparison_context(
        self,
        entity_analysis: Dict[str, Any],
        conversational_context: Dict[str, Any],
        enhanced_query: str
    ) -> bool:
        """Check if we have sufficient context for a comparative query."""

        # If we have enough entities, we're good
        if len(entity_analysis.get("entities", [])) >= 2:
            return True

        # Check if we have conversational context that provides comparison
        if conversational_context:
            recent_queries = conversational_context.get("recent_queries", [])
            references = conversational_context.get("references", {})

            # If we have recent queries and this is a follow-up comparison
            if recent_queries and references.get("follow_up_type") == "comparative":
                return True

            # If the enhanced query is significantly different, context helped
            if "previous query" in enhanced_query.lower():
                return True

        return False

    def _is_context_resolved(
        self,
        original_query: str,
        enhanced_query: str,
        conversational_context: Dict[str, Any]
    ) -> bool:
        """Check if conversational context successfully resolved vague references."""

        if not conversational_context:
            return False

        references = conversational_context.get("references", {})

        # If we have strong reference resolution
        if (references.get("table_references") or
            references.get("result_references") or
            references.get("previous_query_reference")):
            return True

        # If the enhanced query is meaningfully different from original
        if len(enhanced_query) > len(original_query) * 1.2:  # 20% longer suggests context was added
            return True

        # If we have a clear follow-up type identified
        if references.get("follow_up_type") in ["comparative", "result_analysis"]:
            return True

        return False

    def _calculate_confidence(
        self,
        intent_analysis: Dict[str, Any],
        entity_analysis: Dict[str, Any],
        clarifications: List[str]
    ) -> float:
        """Calculate confidence score for the query analysis."""

        confidence = 1.0

        # Reduce confidence for unclear intent
        if intent_analysis.get("intent") == "unclear":
            confidence -= 0.3

        # Reduce confidence if many clarifications needed
        confidence -= len(clarifications) * 0.1

        # Reduce confidence if no entities identified
        if not entity_analysis.get("entities"):
            confidence -= 0.2

        # Ensure confidence is between 0 and 1
        return max(0.0, min(1.0, confidence))

    def _needs_enhancement(self, query: str, conversation_history: List[Dict[str, Any]]) -> bool:
        """Determine if a query needs enhancement based on follow-up indicators."""

        if not conversation_history:
            return False

        query_lower = query.lower()

        # Check for follow-up indicators
        for indicator in FOLLOW_UP_INDICATORS:
            if indicator in query_lower.split():
                return True

        # Check for very short queries (likely follow-ups)
        if len(query_lower.split()) <= 3:
            return True

        return False

    def _format_conversation_context(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Format conversation history for prompts."""
        if not conversation_history:
            return ""

        context_lines = []
        for msg in conversation_history[-5:]:  # Last 5 messages
            role = msg.get("role", "user")
            content = msg.get("content", "")
            context_lines.append(f"{role}: {content}")

        return "\n".join(context_lines)

    def _format_schema_context(self, database_schemas: List[Dict[str, Any]]) -> str:
        """Format database schema information for prompts."""
        if not database_schemas:
            return ""

        schema_lines = []
        for schema in database_schemas[:3]:  # Limit to avoid prompt overflow
            db_name = schema.get("database_name", "Unknown")
            tables = schema.get("tables", [])
            if tables:
                table_names = [t.get("name", "") for t in tables[:5]]  # First 5 tables
                schema_lines.append(f"{db_name}: {', '.join(table_names)}")

        return "\n".join(schema_lines)

    def _clean_json_response(self, response: str) -> str:
        """Clean and extract JSON from LLM response."""
        response = response.strip()

        # Remove markdown code blocks
        if response.startswith("```json"):
            response = response[7:]
        elif response.startswith("```"):
            response = response[3:]

        if response.endswith("```"):
            response = response[:-3]

        # Find JSON object boundaries
        start_idx = response.find("{")
        end_idx = response.rfind("}") + 1

        if start_idx >= 0 and end_idx > start_idx:
            return response[start_idx:end_idx]

        return response.strip()

    # ========================================================================
    # Enhanced Conversational Context Methods
    # ========================================================================

    async def _analyze_query_intent_with_context(
        self,
        query: str,
        conversation_history: List[Dict[str, Any]],
        conversational_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze query intent with enhanced conversational context."""

        # Start with basic intent analysis
        basic_analysis = await self._analyze_query_intent(query, conversation_history)

        # Enhance with conversational context
        if conversational_context:
            # Check if this is a follow-up to a specific table discussion
            current_focus = conversational_context.get("current_focus")
            if current_focus and conversational_context.get("references", {}).get("needs_context"):
                basic_analysis["context_enhanced"] = True
                basic_analysis["table_focus"] = current_focus

                # Adjust intent based on context
                if basic_analysis["intent"] == "unclear":
                    # Try to infer intent from recent queries
                    recent_queries = conversational_context.get("recent_queries", [])
                    if recent_queries:
                        last_query = recent_queries[-1]
                        if "analytical" in last_query.get("query_text", "").lower():
                            basic_analysis["intent"] = "analytical"
                            basic_analysis["reasoning"] += " (inferred from conversation context)"

        return basic_analysis

    async def _extract_entities_and_requirements_with_context(
        self,
        query: str,
        conversation_history: List[Dict[str, Any]],
        database_schemas: List[Dict[str, Any]],
        conversational_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract entities with enhanced conversational context."""

        # Start with basic entity extraction
        basic_analysis = await self._extract_entities_and_requirements(
            query, conversation_history, database_schemas
        )

        # Enhance with conversational context
        if conversational_context:
            tables_discussed = conversational_context.get("tables_discussed", {})

            # Add entities from discussed tables
            for table_key, table_info in tables_discussed.items():
                table_name = table_info.get("table_name", "")
                if table_name and table_name not in basic_analysis["entities"]:
                    basic_analysis["entities"].append(table_name)

                # Add columns that were mentioned
                columns = table_info.get("columns_mentioned", [])
                for column in columns:
                    if column not in basic_analysis["attributes"]:
                        basic_analysis["attributes"].append(column)

            # Add business entities from context
            business_entities = conversational_context.get("business_entities", [])
            for entity in business_entities:
                if entity not in basic_analysis["entities"]:
                    basic_analysis["entities"].append(entity)

        return basic_analysis

    async def _enhance_query_with_conversational_context(
        self,
        query: str,
        conversation_history: List[Dict[str, Any]],
        intent_analysis: Dict[str, Any],
        entity_analysis: Dict[str, Any],
        conversational_context: Dict[str, Any],
        session_id: Optional[str] = None
    ) -> str:
        """Enhance query with conversational context using the context service."""

        # First, try the context service enhancement if session_id is available
        if session_id:
            context_enhanced_query = await conversational_context_service.generate_context_enhanced_query(
                session_id, query
            )
            if context_enhanced_query != query:
                return context_enhanced_query

        # Fallback to original enhancement method
        return await self._enhance_query_with_context(
            query, conversation_history, intent_analysis, entity_analysis
        )

    def _calculate_confidence_with_context(
        self,
        intent_analysis: Dict[str, Any],
        entity_analysis: Dict[str, Any],
        clarifications: List[str],
        conversational_context: Dict[str, Any]
    ) -> float:
        """Calculate confidence score enhanced with conversational context and follow-up detection."""

        # Start with basic confidence calculation
        confidence = self._calculate_confidence(intent_analysis, entity_analysis, clarifications)

        # Enhanced confidence boosting for conversational context
        if conversational_context:
            references = conversational_context.get("references", {})
            recent_queries = conversational_context.get("recent_queries", [])

            # Major boost for successfully resolved follow-up questions
            if references.get("needs_context"):
                follow_up_type = references.get("follow_up_type")

                if follow_up_type == "comparative" and references.get("previous_query_reference"):
                    confidence += 0.25  # High confidence for comparative follow-ups with context
                elif follow_up_type == "result_analysis" and references.get("result_references"):
                    confidence += 0.2   # High confidence for result analysis follow-ups
                elif follow_up_type == "pronoun_reference" and references.get("table_references"):
                    confidence += 0.15  # Good confidence for resolved pronoun references

                # Additional boost if we have strong previous query context
                if references.get("previous_query_reference"):
                    confidence += 0.1

            # Boost if we have a clear current focus
            if conversational_context.get("current_focus"):
                confidence += 0.1

            # Boost if we have recent relevant queries (indicates ongoing conversation)
            if recent_queries:
                confidence += 0.05

                # Extra boost if recent queries are related to current query
                if len(recent_queries) > 0:
                    last_query = recent_queries[-1]
                    if self._queries_are_related(intent_analysis, last_query):
                        confidence += 0.1

            # Boost if references were successfully resolved
            if (references.get("table_references") or
                references.get("column_references") or
                references.get("result_references")):
                confidence += 0.1

            # Penalty reduction for follow-up questions (they're naturally more ambiguous but still valid)
            if self._is_likely_follow_up("", conversational_context):
                # Reduce the penalty for having clarifications in follow-up questions
                if len(clarifications) > 0:
                    confidence += 0.1  # Offset some of the clarification penalty

        # Ensure confidence is between 0 and 1
        return max(0.0, min(1.0, confidence))

    def _queries_are_related(self, current_intent: Dict[str, Any], previous_query: Dict[str, Any]) -> bool:
        """Check if current query is related to a previous query."""

        current_intent_type = current_intent.get("intent", "")
        current_entities = set(current_intent.get("entities", []))

        # Check if they share similar intent patterns
        if current_intent_type in ["comparative", "analytical"]:
            # These intents often build on previous queries
            return True

        # Check if they share entities or tables
        previous_tables = set(previous_query.get("tables_involved", []))
        if current_entities.intersection(previous_tables):
            return True

        # Check for keyword overlap
        current_keywords = set(current_intent.get("key_indicators", []))
        previous_text = previous_query.get("query_text", "").lower()

        if any(keyword.lower() in previous_text for keyword in current_keywords):
            return True

        return False

    def _detect_table_discussion_intent(self, query: str) -> bool:
        """Detect if the query is about table structure/metadata discussion."""

        table_discussion_keywords = [
            # Structure keywords
            "table structure", "table schema", "columns", "fields", "attributes",
            "data types", "column types", "table layout", "table design",

            # Relationship keywords
            "table relationships", "foreign keys", "references", "connections",
            "linked tables", "related tables", "table connections",

            # Metadata keywords
            "table information", "table details", "table metadata", "table properties",
            "table size", "row count", "table statistics",

            # Comparison keywords
            "compare tables", "table differences", "similar tables", "table comparison",

            # Exploration keywords
            "explore table", "describe table", "explain table", "show table",
            "table overview", "table summary"
        ]

        query_lower = query.lower()

        # Direct keyword matching
        if any(keyword in query_lower for keyword in table_discussion_keywords):
            return True

        # Pattern matching for table-specific queries
        table_patterns = [
            r'\bwhat.*table\b',
            r'\btable.*structure\b',
            r'\btable.*columns\b',
            r'\bcolumns.*in.*table\b',
            r'\btable.*relationships\b',
            r'\bshow.*table\b',
            r'\bdescribe.*table\b',
            r'\bexplain.*table\b'
        ]

        import re
        for pattern in table_patterns:
            if re.search(pattern, query_lower):
                return True

        return False
