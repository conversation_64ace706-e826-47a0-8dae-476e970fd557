"use client";

import React, { useState } from 'react';
import { OnboardingStepProps } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { User, ArrowLeft, ArrowRight } from 'lucide-react';

interface ProfileData {
  fullName: string;
  jobTitle: string;
  company: string;
  bio: string;
}

export default function ProfileSetupStep({ onNext, onPrevious, isLoading }: OnboardingStepProps) {
  const [profileData, setProfileData] = useState<ProfileData>({
    fullName: '',
    jobTitle: '',
    company: '',
    bio: '',
  });

  const [errors, setErrors] = useState<Partial<ProfileData>>({});

  const handleInputChange = (field: keyof ProfileData, value: string) => {
    setProfileData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ProfileData> = {};

    if (!profileData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      // Here you could save the profile data to the backend
      console.log('Profile data:', profileData);
      onNext();
    }
  };

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="text-center pb-6">
        <div className="mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit">
          <User className="h-8 w-8 text-blue-600 dark:text-blue-400" />
        </div>
        <CardTitle className="text-2xl font-bold text-slate-900 dark:text-slate-100">
          Set Up Your Profile
        </CardTitle>
        <CardDescription className="text-slate-600 dark:text-slate-400 mt-2">
          Tell us a bit about yourself to personalize your experience.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="grid gap-4">
          {/* Full Name */}
          <div className="space-y-2">
            <Label htmlFor="fullName" className="text-sm font-medium">
              Full Name *
            </Label>
            <Input
              id="fullName"
              type="text"
              placeholder="Enter your full name"
              value={profileData.fullName}
              onChange={(e) => handleInputChange('fullName', e.target.value)}
              className={errors.fullName ? 'border-red-500' : ''}
            />
            {errors.fullName && (
              <p className="text-sm text-red-600 dark:text-red-400">{errors.fullName}</p>
            )}
          </div>

          {/* Job Title */}
          <div className="space-y-2">
            <Label htmlFor="jobTitle" className="text-sm font-medium">
              Job Title
            </Label>
            <Input
              id="jobTitle"
              type="text"
              placeholder="e.g., Data Analyst, Business Intelligence Manager"
              value={profileData.jobTitle}
              onChange={(e) => handleInputChange('jobTitle', e.target.value)}
            />
          </div>

          {/* Company */}
          <div className="space-y-2">
            <Label htmlFor="company" className="text-sm font-medium">
              Company
            </Label>
            <Input
              id="company"
              type="text"
              placeholder="Enter your company name"
              value={profileData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
            />
          </div>

          {/* Bio */}
          <div className="space-y-2">
            <Label htmlFor="bio" className="text-sm font-medium">
              Bio (Optional)
            </Label>
            <Textarea
              id="bio"
              placeholder="Tell us about your role and what you hope to achieve with Agent Platform..."
              value={profileData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              rows={3}
            />
          </div>
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={onPrevious}
            disabled={isLoading}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Previous</span>
          </Button>

          <Button
            onClick={handleNext}
            disabled={isLoading}
            className="flex items-center space-x-2"
          >
            <span>Continue</span>
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="text-center text-sm text-slate-500 dark:text-slate-400">
          <p>* Required fields</p>
        </div>
      </CardContent>
    </Card>
  );
}
