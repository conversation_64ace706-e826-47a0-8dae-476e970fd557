/* [project]/node_modules/react-grid-layout/css/styles.css [app-client] (css) */
.react-grid-layout {
  transition: height .2s;
  position: relative;
}

.react-grid-item {
  transition: left .2s, top .2s, width .2s, height .2s;
}

.react-grid-item img {
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
}

.react-grid-item.cssTransforms {
  transition-property: transform, width, height;
}

.react-grid-item.resizing {
  z-index: 1;
  will-change: width, height;
  transition: none;
}

.react-grid-item.react-draggable-dragging {
  z-index: 3;
  will-change: transform;
  transition: none;
}

.react-grid-item.dropping {
  visibility: hidden;
}

.react-grid-item.react-grid-placeholder {
  opacity: .2;
  z-index: 2;
  -webkit-user-select: none;
  user-select: none;
  -o-user-select: none;
  background: red;
  transition-duration: .1s;
}

.react-grid-item.react-grid-placeholder.placeholder-resizing {
  transition: none;
}

.react-grid-item > .react-resizable-handle {
  width: 20px;
  height: 20px;
  position: absolute;
}

.react-grid-item > .react-resizable-handle:after {
  content: "";
  border-bottom: 2px solid #0006;
  border-right: 2px solid #0006;
  width: 5px;
  height: 5px;
  position: absolute;
  bottom: 3px;
  right: 3px;
}

.react-resizable-hide > .react-resizable-handle {
  display: none;
}

.react-grid-item > .react-resizable-handle.react-resizable-handle-sw {
  cursor: sw-resize;
  bottom: 0;
  left: 0;
  transform: rotate(90deg);
}

.react-grid-item > .react-resizable-handle.react-resizable-handle-se {
  cursor: se-resize;
  bottom: 0;
  right: 0;
}

.react-grid-item > .react-resizable-handle.react-resizable-handle-nw {
  cursor: nw-resize;
  top: 0;
  left: 0;
  transform: rotate(180deg);
}

.react-grid-item > .react-resizable-handle.react-resizable-handle-ne {
  cursor: ne-resize;
  top: 0;
  right: 0;
  transform: rotate(270deg);
}

.react-grid-item > .react-resizable-handle.react-resizable-handle-w, .react-grid-item > .react-resizable-handle.react-resizable-handle-e {
  cursor: ew-resize;
  margin-top: -10px;
  top: 50%;
}

.react-grid-item > .react-resizable-handle.react-resizable-handle-w {
  left: 0;
  transform: rotate(135deg);
}

.react-grid-item > .react-resizable-handle.react-resizable-handle-e {
  right: 0;
  transform: rotate(315deg);
}

.react-grid-item > .react-resizable-handle.react-resizable-handle-n, .react-grid-item > .react-resizable-handle.react-resizable-handle-s {
  cursor: ns-resize;
  margin-left: -10px;
  left: 50%;
}

.react-grid-item > .react-resizable-handle.react-resizable-handle-n {
  top: 0;
  transform: rotate(225deg);
}

.react-grid-item > .react-resizable-handle.react-resizable-handle-s {
  bottom: 0;
  transform: rotate(45deg);
}

/*# sourceMappingURL=node_modules_react-grid-layout_css_styles_css_f9ee138c._.single.css.map*/