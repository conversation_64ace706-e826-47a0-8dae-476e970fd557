"""Security Utilities

This module provides security-related utilities for the application.
"""

import hashlib
from datetime import datetime, timedelta
from typing import Dict, Optional, Union, <PERSON><PERSON>

from fastapi import Depends, HTTPException, status
from fastapi.security import O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JW<PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy.orm import Session

from app.models.user import TokenD<PERSON>, User
from app.models.auth_db import UserDB, RefreshTokenDB, TokenBlacklistDB
from app.utils.db import get_db
from app.utils.exceptions import (
    TokenExpiredError,
    TokenRevokedError,
    TokenBlacklistedError,
    TokenNotFoundError,
    UserNotFoundError,
    UserInactiveError
)
from app.config.settings import (
    JWT_SECRET_KEY as SECRET_KEY,
    JWT_REFRESH_SECRET_KEY as REFRESH_SECRET_KEY,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    REFRESH_TOKEN_EXPIRE_DAYS
)

# Algorithm used for JWT encoding/decoding
ALGORITHM = "HS256"

# Configure password context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Configure OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/auth/token")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hash.
    
    Args:
        plain_password: The plain text password
        hashed_password: The hashed password
        
    Returns:
        bool: True if the password matches the hash, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate a hash from a password.
    
    Args:
        password: The password to hash
        
    Returns:
        str: The hashed password
    """
    return pwd_context.hash(password)

def create_access_token(data: Dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token.
    
    Args:
        data: The data to encode in the token
        expires_delta: The time delta after which the token will expire
        
    Returns:
        str: The encoded JWT token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def create_refresh_token(data: Dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT refresh token.
    
    Args:
        data: The data to encode in the token
        expires_delta: The time delta after which the token will expire
        
    Returns:
        str: The encoded JWT token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, REFRESH_SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def decode_token(token: str, secret_key: str = SECRET_KEY) -> Dict:
    """Decode a JWT token.
    
    Args:
        token: The token to decode
        secret_key: The secret key used to decode the token
        
    Returns:
        Dict: The decoded token data
        
    Raises:
        JWTError: If the token is invalid
    """
    return jwt.decode(token, secret_key, algorithms=[ALGORITHM])

def store_refresh_token(db: Session, user_id: str, token: str, expires_at: datetime) -> RefreshTokenDB:
    """Store a refresh token in the database.
    
    Args:
        db: The database session
        user_id: The ID of the user
        token: The refresh token
        expires_at: The expiration datetime
        
    Returns:
        RefreshTokenDB: The created refresh token
    """
    db_token = RefreshTokenDB(
        user_id=user_id,
        token=token,
        expires_at=expires_at
    )
    db.add(db_token)
    db.commit()
    db.refresh(db_token)
    return db_token

def revoke_refresh_token(
    db: Session,
    token: str,
    reason: str = "logout",
    correlation_id: Optional[str] = None
) -> bool:
    """Revoke a refresh token.

    Args:
        db: The database session
        token: The refresh token
        reason: Reason for revocation
        correlation_id: Request correlation ID for logging

    Returns:
        bool: True if the token was revoked, False otherwise
    """
    db_token = db.query(RefreshTokenDB).filter(RefreshTokenDB.token == token).first()
    if db_token is None:
        return False

    if db_token:
        db_token.revoked = True
        db_token.revoked_at = datetime.utcnow()
        db_token.revoked_reason = reason
        db.commit()
        return True

    return False

def get_token_data(token: str) -> TokenData:
    """Get data from a JWT token.
    
    Args:
        token: The token to decode
        
    Returns:
        TokenData: The token data
        
    Raises:
        HTTPException: If the token is invalid
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        email: Optional[str] = payload.get("email")
        role: Optional[str] = payload.get("role")
        exp: Optional[int] = payload.get("exp")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        token_data = TokenData(user_id=user_id, email=email, role=role, exp=exp)
        return token_data
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_user(
    token: str = Depends(oauth2_scheme), 
    db: Session = Depends(get_db)
) -> User:
    """Get the current user from a JWT token.
    
    Args:
        token: The JWT token
        db: The database session
        
    Returns:
        User: The current user
        
    Raises:
        HTTPException: If the token is invalid or the user doesn't exist
    """
    token_data = get_token_data(token)
    user = db.query(UserDB).filter(UserDB.id == token_data.user_id).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user",
            headers={"WWW-Authenticate": "Bearer"},
        )
    # Convert to Pydantic model
    user_model = User(
        id=user.id,
        email=user.email,
        hashed_password=user.hashed_password,
        full_name=user.full_name,
        is_active=user.is_active,
        is_email_verified=user.is_email_verified,
        role=user.role,
        auth_provider=user.auth_provider,
        auth_provider_id=user.auth_provider_id,
        created_at=user.created_at,
        updated_at=user.updated_at,
        last_login=user.last_login,
        profile_picture=user.profile_picture,
        settings={}  # Deserialize JSON if needed
    )
    return user_model

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get the current active user.

    Args:
        current_user: The current user

    Returns:
        User: The current active user

    Raises:
        HTTPException: If the user is inactive
    """
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


def hash_token(token: str) -> str:
    """Create a SHA-256 hash of a token.

    Args:
        token: The token to hash

    Returns:
        str: The SHA-256 hash of the token
    """
    return hashlib.sha256(token.encode()).hexdigest()


def is_token_blacklisted(db: Session, token: str, token_type: str) -> bool:
    """Check if a token is blacklisted.

    Args:
        db: Database session
        token: The token to check
        token_type: Type of token (access/refresh)

    Returns:
        bool: True if the token is blacklisted
    """
    token_hash = hash_token(token)
    blacklisted = db.query(TokenBlacklistDB).filter(
        TokenBlacklistDB.token_hash == token_hash,
        TokenBlacklistDB.token_type == token_type,
        TokenBlacklistDB.expires_at > datetime.utcnow()  # Only check non-expired blacklist entries
    ).first()

    return blacklisted is not None


def blacklist_token(
    db: Session,
    token: str,
    token_type: str,
    user_id: str,
    expires_at: datetime,
    reason: str = "security",
    correlation_id: Optional[str] = None
) -> TokenBlacklistDB:
    """Add a token to the blacklist.

    Args:
        db: Database session
        token: The token to blacklist
        token_type: Type of token (access/refresh)
        user_id: User ID associated with token
        expires_at: Token expiration time
        reason: Reason for blacklisting
        correlation_id: Request correlation ID for logging

    Returns:
        TokenBlacklistDB: The blacklist entry that was created
    """
    token_hash = hash_token(token)

    # Check if already blacklisted
    existing = db.query(TokenBlacklistDB).filter(
        TokenBlacklistDB.token_hash == token_hash
    ).first()

    if existing:
        return existing  # Already blacklisted

    blacklist_entry = TokenBlacklistDB(
        token_hash=token_hash,
        token_type=token_type,
        user_id=user_id,
        expires_at=expires_at,
        reason=reason
    )

    db.add(blacklist_entry)
    db.commit()
    return blacklist_entry


def validate_refresh_token(
    db: Session,
    token: str,
    ip_address: Optional[str] = None,
    correlation_id: Optional[str] = None
) -> Tuple[RefreshTokenDB, UserDB]:
    """Validate a refresh token with comprehensive security checks.

    Args:
        db: Database session
        token: The refresh token to validate
        ip_address: IP address of the client
        correlation_id: Request correlation ID for logging

    Returns:
        Tuple[RefreshTokenDB, UserDB]: The token and user objects

    Raises:
        TokenBlacklistedError: If the token is blacklisted
        TokenNotFoundError: If the token is not found
        TokenExpiredError: If the token has expired
        TokenRevokedError: If the token has been revoked
        UserNotFoundError: If the user is not found
        UserInactiveError: If the user account is inactive
    """
    # Check if token is blacklisted first
    if is_token_blacklisted(db, token, "refresh"):
        raise TokenBlacklistedError("Token has been blacklisted")

    # Find the token in database
    db_token = db.query(RefreshTokenDB).filter(RefreshTokenDB.token == token).first()
    if not db_token:
        raise TokenNotFoundError("Refresh token not found")

    # Check expiration
    if datetime.utcnow() > db_token.expires_at:
        raise TokenExpiredError("Refresh token has expired")

    # Check if token is revoked
    if db_token.revoked:
        raise TokenRevokedError("Refresh token has been revoked")

    # Get user
    user = db.query(UserDB).filter(UserDB.id == db_token.user_id).first()
    if not user:
        raise UserNotFoundError("User not found")

    if not user.is_active:
        raise UserInactiveError("User account is inactive")

    # Update last used timestamp
    db_token.last_used_at = datetime.utcnow()
    if ip_address:
        db_token.ip_address = ip_address

    # Increment use count
    db_token.use_count = str(int(db_token.use_count or "0") + 1)
    db.commit()

    return db_token, user
