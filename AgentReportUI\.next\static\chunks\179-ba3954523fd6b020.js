"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[179],{10071:(e,t,s)=>{s.d(t,{_:()=>i,s:()=>d});var o=s(95155),a=s(12115);let r={title:"Agent Platform",subtitle:"AI-Powered Data Analytics"},n={onCreateChart:void 0,chartCount:0,maxCharts:12},l=(0,a.createContext)(void 0),i=e=>{let{children:t}=e,[s,i]=(0,a.useState)(r),[d,c]=(0,a.useState)(n),u=(0,a.useCallback)(e=>{i(e)},[]),h=(0,a.useCallback)(e=>{c(e)},[]),g=(0,a.useCallback)(()=>{i(r),c(n)},[]);return(0,o.jsx)(l.Provider,{value:{pageInfo:s,actions:d,setPageHeader:u,setPageActions:h,resetPageHeader:g},children:t})},d=()=>{let e=(0,a.useContext)(l);if(void 0===e)throw Error("usePageHeader must be used within a PageHeaderProvider");return e}},10871:(e,t,s)=>{s.d(t,{ChatHistoryProvider:()=>d,m:()=>c});var o=s(95155),a=s(12115),r=s(23464),n=s(74045),l=s(58189);let i=(0,a.createContext)(void 0),d=e=>{let{children:t}=e,{user:s,isAuthenticated:d,logout:c,isLoading:u}=(0,n.A)(),{listUserChats:h,getChatHistory:g,deleteChat:f}=(0,l.g)(),[_,C]=(0,a.useState)([]),[p,m]=(0,a.useState)(null),[v,w]=(0,a.useState)({}),[y,k]=(0,a.useState)(!1),[b,A]=(0,a.useState)(!1),[x,E]=(0,a.useState)(null),[S,D]=(0,a.useState)(!1),P=(0,a.useRef)(new Set),T=(0,a.useRef)(!1),H=(0,a.useRef)(!1),L=(0,a.useCallback)(()=>{let e="sess_";for(let t=0;t<8;t++)e+="0123456789abcdef"[Math.floor(16*Math.random())];return e},[]),N=(0,a.useCallback)(e=>{let t=new Date(e.last_seen);return{id:"chat_".concat(e.session_id),session_id:e.session_id,title:e.title||"Untitled Chat",created_at:t,last_updated:t,message_count:e.message_count||0}},[]),R=(0,a.useCallback)(e=>({role:"assistant"===e.role?"agent":e.role,content:e.content,timestamp:new Date}),[]),U=(0,a.useCallback)(async e=>{if(d&&s){if(P.current.has(e))return void console.log("Load for session ".concat(e," already in progress, skipping."));A(!0),P.current.add(e);try{let t=(await g(e)).map(R);w(s=>({...s,[e]:t})),C(s=>s.map(s=>s.session_id===e?{...s,message_count:t.length,last_updated:new Date}:s))}catch(s){var t,o;if(console.error("Error loading chat history from backend:",s),r.A.isAxiosError(s)&&(null==(t=s.response)?void 0:t.status)===401){console.log("Authentication token invalid while loading chat history, logging out..."),c();return}r.A.isAxiosError(s)&&(null==(o=s.response)?void 0:o.status)===404?(console.log("Chat history not found (404) - this is normal for new chats"),w(t=>({...t,[e]:[]})),C(t=>t.map(t=>t.session_id===e?{...t,message_count:0,last_updated:new Date}:t))):(console.error("Unexpected error loading chat history:",s),w(t=>({...t,[e]:[]})),C(t=>t.map(t=>t.session_id===e?{...t,message_count:0,last_updated:new Date}:t)))}finally{A(!1),P.current.delete(e)}}},[d,s,c,g,R]),j=(0,a.useCallback)(async e=>{if(console.log("setActiveChat called with:",e),H.current)return void console.log("setActiveChat already in progress, ignoring call");if(null===e)return void m(null);if(p&&p.id===e.id)return void console.log("Chat is already active, skipping to prevent duplicate calls");H.current=!0;try{m(e);let t=(v[e.session_id]||[]).length>0;b||t||!e.session_id?console.log("Skipping history load - either already loaded or new chat (no session_id yet)"):(console.log("Loading chat history for session:",e.session_id),await U(e.session_id))}finally{H.current=!1}},[U,v,p,b]),I=(0,a.useCallback)(e=>{if(S){console.log("Chat creation already in progress, returning existing or throwing error");let e=_[0];if(e)return e;throw Error("Chat creation already in progress")}D(!0);try{let t=L(),s="chat_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),o=new Date,a={id:s,session_id:t,title:e||"New Chat",created_at:o,last_updated:o,message_count:0};return C(e=>[a,...e]),a}finally{setTimeout(()=>{D(!1)},500)}},[L,S,_]),M=(0,a.useCallback)((e,t)=>{let s,o=null;return C(a=>a.map(a=>a.id===e?(s=a.session_id,o={...a,session_id:t}):a)),s&&s!==t&&w(e=>{let o=e[s]||[],a={...e};return delete a[s],a[t]=o,a}),o},[]),q=(0,a.useCallback)(e=>{C(t=>{let s=t.find(t=>t.id===e);return s?(w(e=>{let t={...e};return delete t[s.session_id],t}),m(t=>(null==t?void 0:t.id)===e?null:t),t.filter(t=>t.id!==e)):t})},[]),z=(0,a.useCallback)(async e=>{if(!d||!s)return;let t=_.find(t=>t.id===e);if(!t)return void console.warn("Chat not found for deletion:",e);try{await f(t.session_id),console.log("Chat deleted successfully from backend:",t.session_id),q(e)}catch(t){var o,a;if(console.error("Error deleting chat from backend:",t),r.A.isAxiosError(t)&&(null==(o=t.response)?void 0:o.status)===401){console.log("Authentication token invalid while deleting chat, logging out..."),c();return}if(r.A.isAxiosError(t)&&(null==(a=t.response)?void 0:a.status)===404){console.log("Chat not found on backend (404) - removing locally"),q(e);return}throw t}},[d,s,_,f,q,c]),B=(0,a.useCallback)((e,t)=>{C(s=>s.map(s=>s.id===e?{...s,title:t,last_updated:new Date}:s))},[]),F=(0,a.useCallback)(e=>{let t=e.trim().substring(0,50);return t=(t=t.replace(/^(what|how|when|where|why|who|can|could|would|should|is|are|do|does|did)\s+/i,"")).charAt(0).toUpperCase()+t.slice(1),e.length>50&&(t+="..."),t||"New Chat"},[]),G=(0,a.useCallback)((e,t)=>{let s={...t,timestamp:t.timestamp||new Date};w(t=>({...t,[e]:[...t[e]||[],s]})),C(t=>t.map(t=>t.session_id===e?{...t,last_updated:new Date,message_count:t.message_count+1}:t)),"user"===t.role&&C(s=>s.map(s=>{if(s.session_id===e&&("New Chat"===s.title||"Untitled Chat"===s.title)){let e=F(t.content);return{...s,title:e}}return s}))},[F]);(0,a.useEffect)(()=>{if(console.log("ChatHistoryContext useEffect triggered:",{authLoading:u,isAuthenticated:d,hasAccessToken:!!(null==s?void 0:s.access_token),userId:null==s?void 0:s.user_id,hasLoadedChats:T.current}),u||!d||!(null==s?void 0:s.access_token)){console.log("Skipping chat load - auth loading or not authenticated"),u||d||(C([]),w({}),m(null),T.current=!1);return}if(T.current)return void console.log("Chats already loaded for this session, skipping...");console.log("Loading chats for authenticated user..."),T.current=!0,k(!0),h().then(e=>{console.log("Successfully loaded chats from backend:",e.length);let t=e.map(N);t.sort((e,t)=>t.last_updated.getTime()-e.last_updated.getTime()),C(t)}).catch(e=>{var t;if(console.error("Error loading chats from backend:",e),T.current=!1,r.A.isAxiosError(e)&&(null==(t=e.response)?void 0:t.status)===401){console.log("401 error while loading chats, logging out..."),c();return}C([])}).finally(()=>{k(!1)})},[u,d,null==s?void 0:s.access_token,null==s?void 0:s.user_id]);let J=(0,a.useCallback)(async()=>{if(d&&(null==s?void 0:s.access_token))try{console.log("Refreshing chat list from backend...");let e=(await h()).map(N);e.sort((e,t)=>t.last_updated.getTime()-e.last_updated.getTime()),C(e),console.log("Chat list refreshed successfully")}catch(t){var e;console.error("Error refreshing chat list:",t),r.A.isAxiosError(t)&&(null==(e=t.response)?void 0:e.status)===401&&c()}},[d,null==s?void 0:s.access_token]),K=(0,a.useCallback)(async e=>{console.log("Loading chat by ID:",e);let t=_.find(t=>t.id===e);return t?(console.log("Chat found in existing history:",t),await j(t),t):(y?console.log("Chats still loading, waiting..."):console.log("Chat not found in loaded chat history - chat may not exist:",e),null)},[_,y,j]);return(0,o.jsx)(i.Provider,{value:{chatHistory:_,activeChat:p,chatMessages:v,isLoadingChats:y,isLoadingHistory:b,pendingFirstMessage:x,setPendingFirstMessage:E,addChat:I,updateChatSessionId:M,deleteChat:z,renameChat:B,setActiveChat:j,loadChatById:K,addMessageToChat:G,loadChatHistory:U,refreshChatList:J,generateSessionId:L},children:t})},c=()=>{let e=(0,a.useContext)(i);if(!e)throw Error("useChatHistory must be used within a ChatHistoryProvider");return e}},56543:(e,t,s)=>{s.d(t,{D:()=>l,ThemeProvider:()=>n});var o=s(95155),a=s(12115),r=s(51362);function n(e){let{children:t,...s}=e;return(0,o.jsx)(r.N,{...s,children:t})}let l=()=>{let[e,t]=a.useState(!1),{theme:s,setTheme:o,resolvedTheme:n}=(0,r.D)();return a.useEffect(()=>t(!0),[]),{theme:e?s:void 0,setTheme:o,resolvedTheme:e?n:void 0,mounted:e}}}}]);