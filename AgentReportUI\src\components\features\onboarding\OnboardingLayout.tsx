"use client";

import React from 'react';
import { OnboardingStep } from '@/types/auth';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, Circle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OnboardingLayoutProps {
  steps: OnboardingStep[];
  currentStepIndex: number;
  isLoading: boolean;
  children: React.ReactNode;
}

export default function OnboardingLayout({
  steps,
  currentStepIndex,
  isLoading,
  children,
}: OnboardingLayoutProps) {
  const progressPercentage = ((currentStepIndex + 1) / steps.length) * 100;

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header with progress */}
      <div className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700 px-6 py-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                Welcome to Agent Platform
              </h1>
              <p className="text-slate-600 dark:text-slate-400 mt-1">
                Let's get you set up in just a few steps
              </p>
            </div>
            <div className="text-sm text-slate-500 dark:text-slate-400">
              Step {currentStepIndex + 1} of {steps.length}
            </div>
          </div>

          {/* Progress bar */}
          <div className="mb-4">
            <Progress 
              value={progressPercentage} 
              className="h-2"
            />
          </div>

          {/* Step indicators */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={cn(
                  "flex items-center space-x-2",
                  index < steps.length - 1 && "flex-1"
                )}
              >
                <div className="flex items-center space-x-2">
                  {step.isCompleted ? (
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                  ) : (
                    <Circle 
                      className={cn(
                        "h-5 w-5",
                        index === currentStepIndex 
                          ? "text-blue-600 dark:text-blue-400" 
                          : "text-slate-300 dark:text-slate-600"
                      )} 
                    />
                  )}
                  <span
                    className={cn(
                      "text-sm font-medium",
                      step.isCompleted 
                        ? "text-green-600 dark:text-green-400"
                        : index === currentStepIndex
                        ? "text-blue-600 dark:text-blue-400"
                        : "text-slate-400 dark:text-slate-500"
                    )}
                  >
                    {step.title}
                  </span>
                  {step.isOptional && (
                    <span className="text-xs text-slate-400 dark:text-slate-500">
                      (Optional)
                    </span>
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div className="flex-1 h-px bg-slate-200 dark:bg-slate-700 mx-4" />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-2xl">
          {children}
        </div>
      </div>
    </div>
  );
}
