["tests/agents/test_planner_agent.py::TestPlannerAgent::test_fallback_action", "tests/agents/test_planner_agent.py::TestPlannerAgent::test_get_plan_success", "tests/agents/test_planner_agent.py::TestPlannerAgent::test_get_plan_validation_error_with_retry", "tests/agents/test_planner_agent.py::TestPlannerAgent::test_init", "tests/agents/test_report_orchestrator_infinite_loop_fix.py::TestReportOrchestratorInfiniteLoopFix::test_analysis_plan_infinite_loop_fix", "tests/agents/test_report_orchestrator_infinite_loop_fix.py::TestReportOrchestratorInfiniteLoopFix::test_analysis_plan_tool_handles_failure", "tests/agents/test_report_orchestrator_infinite_loop_fix.py::TestReportOrchestratorInfiniteLoopFix::test_analysis_plan_tool_marks_creation_attempted", "tests/agents/test_report_orchestrator_infinite_loop_fix.py::TestReportOrchestratorInfiniteLoopFix::test_dashboard_handles_no_execution_results", "tests/agents/test_report_orchestrator_infinite_loop_fix.py::TestReportOrchestratorInfiniteLoopFix::test_decide_next_action_no_infinite_loop", "tests/agents/test_report_orchestrator_infinite_loop_fix.py::TestReportOrchestratorInfiniteLoopFix::test_select_datasets_tool_marks_selection_attempted", "tests/agents/test_report_orchestrator_infinite_loop_fix.py::TestReportOrchestratorInfiniteLoopFix::test_select_datasets_with_mock_agents", "tests/agents/test_report_orchestrator_infinite_loop_fix.py::TestReportOrchestratorInfiniteLoopFix::test_workflow_progression_without_datasets", "tests/test_auth_security.py::TestRefreshTokenSecurity::test_cleanup_expired_refresh_tokens", "tests/test_auth_security.py::TestRefreshTokenSecurity::test_cleanup_expired_tokens_comprehensive", "tests/test_auth_security.py::TestRefreshTokenSecurity::test_refresh_token_with_expired_token", "tests/test_auth_security.py::TestRefreshTokenSecurity::test_verify_refresh_token_comprehensive_validation", "tests/test_auth_security.py::TestRefreshTokenSecurity::test_verify_refresh_token_expired_token_cleanup", "tests/test_auth_security.py::TestRefreshTokenSecurity::test_verify_refresh_token_in_db_expired_token", "tests/test_auth_security.py::TestRefreshTokenSecurity::test_verify_refresh_token_in_db_revoked_token", "tests/test_auth_security.py::TestRefreshTokenSecurity::test_verify_refresh_token_in_db_valid_token", "tests/test_auth_security.py::TestRefreshTokenSecurity::test_verify_refresh_token_invalid_jwt", "tests/test_auth_security.py::TestSecurityIntegration::test_refresh_token_includes_expiration_in_jwt", "tests/test_auth_security.py::TestSecurityIntegration::test_token_storage_includes_expiration", "tests/test_chart_controller.py::TestChartControllerIntegration::test_chart_endpoints_require_authentication", "tests/test_chart_controller.py::TestChartControllerIntegration::test_chart_query_end_to_end", "tests/test_chart_controller.py::TestChartControllerIntegration::test_chart_service_health", "tests/test_chart_controller.py::TestChartControllerIntegration::test_get_supported_chart_types", "tests/test_chart_controller.py::TestChartControllerIntegration::test_query_chart_empty_prompt", "tests/test_chart_controller.py::TestChartControllerIntegration::test_query_chart_invalid_request", "tests/test_chart_controller.py::TestChartControllerIntegration::test_query_chart_prompt_too_long", "tests/test_chart_controller.py::TestChartControllerIntegration::test_query_chart_service_error", "tests/test_chart_controller.py::TestChartControllerIntegration::test_query_chart_success", "tests/test_chart_controller.py::TestChartControllerIntegration::test_query_chart_without_user_id", "tests/test_chart_controller.py::TestChartControllerIntegration::test_validate_chart_query_empty_prompt", "tests/test_chart_controller.py::TestChartControllerIntegration::test_validate_chart_query_service_error", "tests/test_chart_controller.py::TestChartControllerIntegration::test_validate_chart_query_too_long", "tests/test_chart_controller.py::TestChartControllerIntegration::test_validate_chart_query_valid", "tests/test_chart_service.py::TestChartSelectionService::test_create_chart_metadata", "tests/test_chart_service.py::TestChartSelectionService::test_fallback_chart_type_recommendation_bar", "tests/test_chart_service.py::TestChartSelectionService::test_fallback_chart_type_recommendation_default", "tests/test_chart_service.py::TestChartSelectionService::test_fallback_chart_type_recommendation_line", "tests/test_chart_service.py::TestChartSelectionService::test_fallback_chart_type_recommendation_pie", "tests/test_chart_service.py::TestChartSelectionService::test_generate_axis_label_pie_chart", "tests/test_chart_service.py::TestChartSelectionService::test_generate_axis_label_time_x", "tests/test_chart_service.py::TestChartSelectionService::test_generate_axis_label_value_y", "tests/test_chart_service.py::TestChartSelectionService::test_generate_bar_chart_data", "tests/test_chart_service.py::TestChartSelectionService::test_generate_chart_title_fallback", "tests/test_chart_service.py::TestChartSelectionService::test_generate_chart_title_success", "tests/test_chart_service.py::TestChartSelectionService::test_generate_funnel_chart_data", "tests/test_chart_service.py::TestChartSelectionService::test_generate_line_chart_data", "tests/test_chart_service.py::TestChartSelectionService::test_generate_number_data", "tests/test_chart_service.py::TestChartSelectionService::test_generate_pie_chart_data", "tests/test_chart_service.py::TestChartSelectionService::test_get_chart_type_recommendation_invalid_json", "tests/test_chart_service.py::TestChartSelectionService::test_get_chart_type_recommendation_success", "tests/test_chart_service.py::TestChartSelectionService::test_get_relevant_categories_departments", "tests/test_chart_service.py::TestChartSelectionService::test_get_relevant_categories_products", "tests/test_chart_service.py::TestChartSelectionService::test_get_time_labels_monthly", "tests/test_chart_service.py::TestChartSelectionService::test_get_time_labels_yearly", "tests/test_chart_service.py::TestChartSelectionService::test_process_chart_query_llm_failure", "tests/test_chart_service.py::TestChartSelectionService::test_process_chart_query_success", "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_complex_join_queries", "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_connection_recovery", "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_data_type_handling", "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_database_connection", "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_error_handling_with_real_database", "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_performance_with_large_datasets", "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_schema_discovery", "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_sql_query_execution", "tests/test_database_integration.py::TestSupabaseDatabaseIntegration::test_table_schema_retrieval", "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_create_user_with_duplicate_email", "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_create_user_with_unique_email", "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_cross_provider_email_validation", "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_email_validation_database_error_handling", "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_email_validation_security_logging", "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_registration_endpoint_duplicate_email", "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_registration_endpoint_unique_email", "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_validate_email_uniqueness_case_insensitive", "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_validate_email_uniqueness_existing_email", "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_validate_email_uniqueness_new_email", "tests/test_email_uniqueness_validation.py::TestEmailUniquenessValidation::test_validate_email_uniqueness_with_whitespace", "tests/test_email_uniqueness_validation.py::TestEmailValidationEdgeCases::test_email_validation_with_special_characters", "tests/test_email_uniqueness_validation.py::TestEmailValidationEdgeCases::test_email_validation_with_unicode_domains", "tests/test_email_uniqueness_validation.py::TestEmailValidationIntegration::test_error_response_format_consistency", "tests/test_email_uniqueness_validation.py::TestEmailValidationIntegration::test_full_registration_flow_with_validation", "tests/test_email_uniqueness_validation.py::TestGoogleOAuthEmailHandling::test_google_oauth_links_existing_local_account", "tests/test_email_uniqueness_validation.py::TestGoogleOAuthEmailHandling::test_traditional_registration_blocks_google_email", "tests/test_error_handling.py::TestAgentProcessingErrors::test_agent_communication_failure", "tests/test_error_handling.py::TestAgentProcessingErrors::test_agent_initialization_failure", "tests/test_error_handling.py::TestAgentProcessingErrors::test_bedrock_api_failure", "tests/test_error_handling.py::TestAgentProcessingErrors::test_malformed_agent_response", "tests/test_error_handling.py::TestDatabaseConnectionErrors::test_database_connection_failure", "tests/test_error_handling.py::TestDatabaseConnectionErrors::test_database_permission_errors", "tests/test_error_handling.py::TestDatabaseConnectionErrors::test_database_timeout", "tests/test_error_handling.py::TestDatabaseConnectionErrors::test_invalid_sql_generation", "tests/test_error_handling.py::TestGracefulDegradation::test_fallback_to_simple_responses", "tests/test_error_handling.py::TestGracefulDegradation::test_partial_service_failure", "tests/test_error_handling.py::TestQueryValidationErrors::test_empty_query_handling", "tests/test_error_handling.py::TestQueryValidationErrors::test_empty_query_handling_setup", "tests/test_error_handling.py::TestQueryValidationErrors::test_extremely_long_query", "tests/test_error_handling.py::TestQueryValidationErrors::test_special_characters_in_query", "tests/test_error_handling.py::TestQueryValidationErrors::test_sql_injection_attempts", "tests/test_error_handling.py::TestQueryValidationErrors::test_whitespace_only_query", "tests/test_error_handling.py::TestResourceLimitErrors::test_concurrent_request_limits", "tests/test_error_handling.py::TestResourceLimitErrors::test_large_result_set_handling", "tests/test_error_handling.py::TestResourceLimitErrors::test_memory_intensive_query", "tests/test_frontend_integration.py::TestClientTypeDetection::test_api_client_detection", "tests/test_frontend_integration.py::TestClientTypeDetection::test_browser_request_detection", "tests/test_frontend_integration.py::TestClientTypeDetection::test_mobile_request_detection", "tests/test_frontend_integration.py::TestClientTypeDetection::test_spa_request_detection", "tests/test_frontend_integration.py::TestOAuthStateManagement::test_generate_oauth_state_with_client_type", "tests/test_optimization_endpoints.py::test_bottlenecks_endpoint", "tests/test_optimization_endpoints.py::test_cache_stats_endpoint", "tests/test_optimization_endpoints.py::test_performance_report_json", "tests/test_optimization_endpoints.py::test_performance_report_markdown", "tests/test_question_endpoint_integration.py::TestAgentWorkflowIntegration::test_error_propagation_and_recovery", "tests/test_question_endpoint_integration.py::TestAgentWorkflowIntegration::test_full_agent_pipeline_coordination", "tests/test_question_endpoint_integration.py::TestAgentWorkflowIntegration::test_query_understanding_to_sql_pipeline", "tests/test_question_endpoint_integration.py::TestAgentWorkflowIntegration::test_query_understanding_to_sql_pipeline_setup", "tests/test_question_endpoint_integration.py::TestAgentWorkflowIntegration::test_sql_to_output_pipeline", "tests/test_question_endpoint_integration.py::TestAgentWorkflowIntegration::test_streaming_workflow_integration", "tests/test_question_endpoint_integration.py::TestConversationalFlowIntegration::test_conversation_context_preservation", "tests/test_question_endpoint_integration.py::TestConversationalFlowIntegration::test_insight_quality_validation", "tests/test_question_endpoint_integration.py::TestConversationalFlowIntegration::test_multi_table_query_coordination", "tests/test_question_endpoint_integration.py::TestConversationalFlowIntegration::test_query_complexity_adaptation", "tests/test_question_endpoint_unit.py::TestOrchestratorAgent::test_agent_initialization", "tests/test_question_endpoint_unit.py::TestOrchestratorAgent::test_orchestrator_setup", "tests/test_question_endpoint_unit.py::TestOutputAgent::test_agent_initialization", "tests/test_question_endpoint_unit.py::TestOutputAgent::test_empty_results_handling", "tests/test_question_endpoint_unit.py::TestOutputAgent::test_json_output_formatting", "tests/test_question_endpoint_unit.py::TestOutputAgent::test_json_output_formatting_setup", "tests/test_question_endpoint_unit.py::TestOutputAgent::test_meaningful_insights_generation", "tests/test_question_endpoint_unit.py::TestQueryUnderstandingAgent::test_agent_initialization", "tests/test_question_endpoint_unit.py::TestQueryUnderstandingAgent::test_complex_query_analysis", "tests/test_question_endpoint_unit.py::TestQueryUnderstandingAgent::test_conversation_context_integration", "tests/test_question_endpoint_unit.py::TestQueryUnderstandingAgent::test_invalid_query_handling", "tests/test_question_endpoint_unit.py::TestQueryUnderstandingAgent::test_simple_query_analysis", "tests/test_question_endpoint_unit.py::TestSQLAgent::test_agent_initialization", "tests/test_question_endpoint_unit.py::TestSQLAgent::test_complex_sql_generation", "tests/test_question_endpoint_unit.py::TestSQLAgent::test_database_error_handling", "tests/test_question_endpoint_unit.py::TestSQLAgent::test_empty_database_infos", "tests/test_question_endpoint_unit.py::TestSQLAgent::test_simple_sql_generation", "tests/test_question_endpoint_unit.py::TestSQLAgent::test_simple_sql_generation_setup", "tests/test_session_management.py::TestConversationHistoryMaintenance::test_context_preservation_across_queries", "tests/test_session_management.py::TestConversationHistoryMaintenance::test_conversation_history_accumulation", "tests/test_session_management.py::TestConversationHistoryMaintenance::test_conversation_history_limits", "tests/test_session_management.py::TestMemoryManagementAndCleanup::test_inactive_session_handling", "tests/test_session_management.py::TestMemoryManagementAndCleanup::test_session_cleanup_on_completion", "tests/test_session_management.py::TestMemoryManagementAndCleanup::test_session_memory_management", "tests/test_session_management.py::TestSessionCreationAndPersistence::test_existing_session_continuation", "tests/test_session_management.py::TestSessionCreationAndPersistence::test_new_session_creation", "tests/test_session_management.py::TestSessionCreationAndPersistence::test_session_id_validation", "tests/test_session_management.py::TestSessionIsolation::test_concurrent_sessions", "tests/test_session_management.py::TestSessionIsolation::test_multiple_sessions_per_user", "tests/test_session_management.py::TestSessionIsolation::test_user_session_isolation", "tests/test_supabase_integration.py::TestAuthServiceSupabaseIntegration::test_authenticate_supabase_user_existing_user", "tests/test_supabase_integration.py::TestAuthServiceSupabaseIntegration::test_authenticate_supabase_user_invalid_token", "tests/test_supabase_integration.py::TestAuthServiceSupabaseIntegration::test_authenticate_supabase_user_new_user", "tests/test_supabase_integration.py::TestAuthServiceSupabaseIntegration::test_authenticate_supabase_user_not_configured", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_check_user_exists_found", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_check_user_exists_not_found", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_check_user_exists_with_provider_filter", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_create_enhanced_auth_response", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_mark_onboarding_complete_database_error", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_mark_onboarding_complete_success", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_mark_onboarding_complete_user_not_found", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_user_state_detection_corrupted_settings", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_user_state_detection_existing_user", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_user_state_detection_local_unverified", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_user_state_detection_new_user_no_login", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_user_state_detection_oauth_incomplete_profile", "tests/test_supabase_integration.py::TestSupabaseDatabaseIntegration::test_user_state_detection_onboarding_incomplete", "tests/test_supabase_integration.py::TestSupabaseSchemaCompatibility::test_oauth_account_model_compatibility", "tests/test_supabase_integration.py::TestSupabaseSchemaCompatibility::test_settings_json_serialization", "tests/test_supabase_integration.py::TestSupabaseSchemaCompatibility::test_user_model_matches_database_schema", "tests/test_supabase_integration.py::TestSupabaseService::test_extract_user_info_email_auth", "tests/test_supabase_integration.py::TestSupabaseService::test_extract_user_info_google_oauth", "tests/test_supabase_integration.py::TestSupabaseService::test_get_supabase_user_not_found", "tests/test_supabase_integration.py::TestSupabaseService::test_get_supabase_user_success", "tests/test_supabase_integration.py::TestSupabaseService::test_is_configured_false", "tests/test_supabase_integration.py::TestSupabaseService::test_is_configured_true", "tests/test_supabase_integration.py::TestSupabaseService::test_validate_supabase_token_expired", "tests/test_supabase_integration.py::TestSupabaseService::test_validate_supabase_token_invalid_signature", "tests/test_supabase_integration.py::TestSupabaseService::test_validate_supabase_token_not_configured", "tests/test_supabase_integration.py::TestSupabaseService::test_validate_supabase_token_success", "tests/test_token_security.py::TestRefreshTokenValidation::test_validate_blacklisted_token_raises_error", "tests/test_token_security.py::TestRefreshTokenValidation::test_validate_expired_token_raises_error", "tests/test_token_security.py::TestRefreshTokenValidation::test_validate_nonexistent_token_raises_error", "tests/test_token_security.py::TestRefreshTokenValidation::test_validate_refresh_token_success", "tests/test_token_security.py::TestRefreshTokenValidation::test_validate_revoked_token_raises_error", "tests/test_token_security.py::TestTokenBlacklisting::test_blacklist_expired_token_not_found", "tests/test_token_security.py::TestTokenBlacklisting::test_blacklist_token", "tests/test_token_security.py::TestTokenBlacklisting::test_hash_token", "tests/test_token_security.py::TestTokenBlacklisting::test_is_token_blacklisted", "tests/test_token_security.py::TestTokenRotation::test_refresh_token_rotation_success", "tests/test_token_security_integration.py::TestGoogleOAuthSecurity::test_google_oauth_callback_with_security_logging", "tests/test_token_security_integration.py::TestTokenSecurityIntegration::test_concurrent_token_usage_detection", "tests/test_token_security_integration.py::TestTokenSecurityIntegration::test_expired_token_cleanup_during_validation", "tests/test_token_security_integration.py::TestTokenSecurityIntegration::test_ip_address_tracking_in_refresh", "tests/test_token_security_integration.py::TestTokenSecurityIntegration::test_login_and_refresh_token_rotation", "tests/test_token_security_integration.py::TestTokenSecurityIntegration::test_logout_blacklists_token", "tests/test_token_security_integration.py::TestTokenSecurityIntegration::test_token_cleanup_endpoint_admin_only", "tests/test_token_security_integration.py::TestTokenSecurityIntegration::test_token_statistics_endpoint_admin_only", "tests/test_user_state_detection.py::TestAuthenticationEndpoints::test_google_callback_user_state", "tests/test_user_state_detection.py::TestAuthenticationEndpoints::test_login_endpoint_user_state", "tests/test_user_state_detection.py::TestAuthenticationEndpoints::test_register_endpoint_user_state", "tests/test_user_state_detection.py::TestSupabaseIntegration::test_auth_service_supabase_integration", "tests/test_user_state_detection.py::TestSupabaseIntegration::test_enhance_user_state_detection_existing_user", "tests/test_user_state_detection.py::TestSupabaseIntegration::test_enhance_user_state_detection_new_user", "tests/test_user_state_detection.py::TestSupabaseIntegration::test_log_user_state_event", "tests/test_user_state_detection.py::TestSupabaseIntegration::test_optimize_user_lookup_query", "tests/test_user_state_detection.py::TestSupabaseIntegration::test_supabase_connection_validation", "tests/test_user_state_detection.py::TestUserStateDetection::test_check_user_exists_existing_user", "tests/test_user_state_detection.py::TestUserStateDetection::test_check_user_exists_new_user", "tests/test_user_state_detection.py::TestUserStateDetection::test_create_enhanced_auth_response", "tests/test_user_state_detection.py::TestUserStateDetection::test_determine_user_state_existing_user", "tests/test_user_state_detection.py::TestUserStateDetection::test_determine_user_state_first_login", "tests/test_user_state_detection.py::TestUserStateDetection::test_determine_user_state_local_unverified", "tests/test_user_state_detection.py::TestUserStateDetection::test_determine_user_state_new_user_no_record", "tests/test_user_state_detection.py::TestUserStateDetection::test_determine_user_state_oauth_incomplete_profile", "tests/test_user_state_detection.py::TestUserStateIntegration::test_oauth_existing_user_flow", "tests/test_user_state_detection.py::TestUserStateIntegration::test_oauth_new_user_flow"]