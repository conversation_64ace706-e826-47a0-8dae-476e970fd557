// Authentication-related type definitions

import { LoginRequest, RegisterRequest, TokenResponse } from './api';

export interface OAuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  user_id: string;
  token_type?: string;
}

export interface AuthContextType {
  isAuthenticated: boolean;
  user: TokenResponse | null;
  isLoading: boolean;
  isNewUser: boolean;
  isCompletingOnboarding: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  signIn: (credentials?: LoginRequest, shouldRedirect?: boolean) => Promise<void>;
  initializeAuthFromStorage: () => Promise<boolean>;
  register: (credentials: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  setUser: (user: TokenResponse | null) => void;
  refreshUserToken: () => Promise<void>;
  handleOAuthCallback: (tokens: OAuthTokens) => Promise<void>;
  completeOnboarding: () => Promise<void>;
}

export interface User {
  id: string;
  username: string;
  email?: string;
  is_new_user?: boolean;
  // Add other user properties as needed
}

// Onboarding-related types
export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  component: React.ComponentType<OnboardingStepProps>;
  isCompleted: boolean;
  isOptional?: boolean;
}

export interface OnboardingStepProps {
  onNext: () => void;
  onPrevious: () => void;
  onComplete: () => Promise<void>;
  isLoading: boolean;
}

export interface OnboardingData {
  welcome_completed?: boolean;
  profile_setup_completed?: boolean;
  preferences_completed?: boolean;
  tutorial_completed?: boolean;
}

export interface OnboardingProgress {
  currentStep: string;
  completedSteps: string[];
  lastUpdated: string;
  canResume: boolean;
  totalSteps: number;
}

export interface OnboardingState {
  isNewUser: boolean;
  progress?: OnboardingProgress;
  isCompleting: boolean;
  hasError: boolean;
  errorMessage?: string;
  canRetry: boolean;
}
