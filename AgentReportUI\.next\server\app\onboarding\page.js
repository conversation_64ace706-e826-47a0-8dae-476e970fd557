(()=>{var e={};e.id=566,e.ids=[566],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7766:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var a=s(49384),r=s(82348);function l(...e){return(0,r.QP)((0,a.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>d,yv:()=>c});var a=s(60687);s(43210);var r=s(72951),l=s(78272),n=s(13964),i=s(3589),o=s(7766);function d({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...n}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:t,position:s="popper",...l}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...l,children:[(0,a.jsx)(p,{}),(0,a.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(h,{})]})})}function m({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function p({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}function h({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}},18902:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\NXTHUMANS\\\\internal-solutions-nxtwebsite\\\\AgentReport 2\\\\AgentReportUI\\\\src\\\\app\\\\onboarding\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\onboarding\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},21860:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=s(65239),r=s(48088),l=s(88170),n=s.n(l),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["onboarding",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,18902)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\onboarding\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\onboarding\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/onboarding/page",pathname:"/onboarding",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var a=s(60687),r=s(43210),l=s(8730),n=s(24224),i=s(7766);let o=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef(({className:e,variant:t,size:s,asChild:r=!1,...n},d)=>{let c=r?l.DX:"button";return(0,a.jsx)(c,{className:(0,i.cn)(o({variant:t,size:s,className:e})),ref:d,...n})});d.displayName="Button"},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var a=s(60687),r=s(43210),l=s(7766);let n=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));n.displayName="Textarea"},35675:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ed});var a=s(60687),r=s(43210),l=s(91010),n=s(16189),i=s(44346),o=s(46657),d=s(5336),c=s(62688);let u=(0,c.A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var x=s(7766);function m({steps:e,currentStepIndex:t,isLoading:s,children:r}){let l=(t+1)/e.length*100;return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700 px-6 py-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-slate-900 dark:text-slate-100",children:"Welcome to Agent Platform"}),(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-400 mt-1",children:"Let's get you set up in just a few steps"})]}),(0,a.jsxs)("div",{className:"text-sm text-slate-500 dark:text-slate-400",children:["Step ",t+1," of ",e.length]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)(o.k,{value:l,className:"h-2"})}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:e.map((s,r)=>(0,a.jsxs)("div",{className:(0,x.cn)("flex items-center space-x-2",r<e.length-1&&"flex-1"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[s.isCompleted?(0,a.jsx)(d.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"}):(0,a.jsx)(u,{className:(0,x.cn)("h-5 w-5",r===t?"text-blue-600 dark:text-blue-400":"text-slate-300 dark:text-slate-600")}),(0,a.jsx)("span",{className:(0,x.cn)("text-sm font-medium",s.isCompleted?"text-green-600 dark:text-green-400":r===t?"text-blue-600 dark:text-blue-400":"text-slate-400 dark:text-slate-500"),children:s.title}),s.isOptional&&(0,a.jsx)("span",{className:"text-xs text-slate-400 dark:text-slate-500",children:"(Optional)"})]}),r<e.length-1&&(0,a.jsx)("div",{className:"flex-1 h-px bg-slate-200 dark:bg-slate-700 mx-4"})]},s.id))})]})}),(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center p-6",children:(0,a.jsx)("div",{className:"w-full max-w-2xl",children:r})})]})}var p=s(29523),h=s(44493),f=s(96834),g=s(48730),b=s(97840);let j=(0,c.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);function v({isVisible:e,progress:t,onResume:s,onStartOver:r,onDismiss:l}){if(!e)return null;let n=Math.round(t.completedSteps.length/t.totalSteps*100),i=new Date(t.lastUpdated),c=Math.round((Date.now()-i.getTime())/6e4);return(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm",children:(0,a.jsxs)(h.Zp,{className:"w-full max-w-md mx-4 shadow-2xl",children:[(0,a.jsxs)(h.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit",children:(0,a.jsx)(g.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)(h.ZB,{className:"text-xl font-bold text-slate-900 dark:text-slate-100",children:"Resume Your Setup?"}),(0,a.jsx)(h.BT,{className:"text-slate-600 dark:text-slate-400",children:"We found your previous onboarding progress"})]}),(0,a.jsxs)(h.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Progress"}),(0,a.jsxs)(f.E,{variant:"outline",className:"text-xs",children:[t.completedSteps.length," of ",t.totalSteps," steps"]})]}),(0,a.jsx)(o.k,{value:n,className:"h-2"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("span",{className:"text-lg font-semibold text-blue-600 dark:text-blue-400",children:[n,"%"]}),(0,a.jsx)("span",{className:"text-sm text-slate-500 dark:text-slate-400 ml-1",children:"complete"})]})]}),(0,a.jsxs)("div",{className:"p-4 bg-slate-50 dark:bg-slate-800 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),(0,a.jsx)("span",{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Next Step"})]}),(0,a.jsx)("p",{className:"text-slate-900 dark:text-slate-100 font-medium capitalize",children:t.currentStep.replace(/([A-Z])/g," $1").trim()})]}),t.completedSteps.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Completed Steps"}),(0,a.jsx)("div",{className:"space-y-1",children:t.completedSteps.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),(0,a.jsx)("span",{className:"text-sm text-slate-600 dark:text-slate-400 capitalize",children:e.replace(/([A-Z])/g," $1").trim()})]},e))})]}),(0,a.jsxs)("div",{className:"text-center text-xs text-slate-500 dark:text-slate-400",children:["Last updated ",(e=>{if(e<1)return"just now";if(e<60)return`${e} minute${1===e?"":"s"} ago`;let t=Math.round(e/60);if(t<24)return`${t} hour${1===t?"":"s"} ago`;let s=Math.round(t/24);return`${s} day${1===s?"":"s"} ago`})(c)]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(p.$,{onClick:s,className:"w-full flex items-center justify-center space-x-2",size:"lg",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Continue Setup"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)(p.$,{variant:"outline",onClick:r,className:"flex-1 flex items-center justify-center space-x-2",children:[(0,a.jsx)(j,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Start Over"})]}),(0,a.jsx)(p.$,{variant:"ghost",onClick:l,className:"flex-1",children:"Skip for Now"})]})]}),(0,a.jsx)("div",{className:"text-center text-xs text-slate-500 dark:text-slate-400",children:(0,a.jsx)("p",{children:"You can always complete your setup later from your profile settings."})})]})]})})}var y=s(61611),N=s(58887),k=s(53411);let w=(0,c.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),C=[{icon:y.A,title:"Connect Your Data",description:"Easily connect to multiple databases and data sources"},{icon:N.A,title:"AI-Powered Chat",description:"Ask questions about your data in natural language"},{icon:k.A,title:"Generate Reports",description:"Create beautiful reports and visualizations automatically"}];var A=s(89667),S=s(54300),P=s(34729),q=s(58869);let R=(0,c.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),z=(0,c.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var M=s(70569),T=s(98599),D=s(11273),$=s(65551),O=s(83721),E=s(18853),F=s(14163),L="Switch",[_,I]=(0,D.A)(L),[Z,U]=_(L),B=r.forwardRef((e,t)=>{let{__scopeSwitch:s,name:l,checked:n,defaultChecked:i,required:o,disabled:d,value:c="on",onCheckedChange:u,form:x,...m}=e,[p,h]=r.useState(null),f=(0,T.s)(t,e=>h(e)),g=r.useRef(!1),b=!p||x||!!p.closest("form"),[j,v]=(0,$.i)({prop:n,defaultProp:i??!1,onChange:u,caller:L});return(0,a.jsxs)(Z,{scope:s,checked:j,disabled:d,children:[(0,a.jsx)(F.sG.button,{type:"button",role:"switch","aria-checked":j,"aria-required":o,"data-state":H(j),"data-disabled":d?"":void 0,disabled:d,value:c,...m,ref:f,onClick:(0,M.m)(e.onClick,e=>{v(e=>!e),b&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),b&&(0,a.jsx)(G,{control:p,bubbles:!g.current,name:l,value:c,checked:j,required:o,disabled:d,form:x,style:{transform:"translateX(-100%)"}})]})});B.displayName=L;var W="SwitchThumb",Y=r.forwardRef((e,t)=>{let{__scopeSwitch:s,...r}=e,l=U(W,s);return(0,a.jsx)(F.sG.span,{"data-state":H(l.checked),"data-disabled":l.disabled?"":void 0,...r,ref:t})});Y.displayName=W;var G=r.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:l=!0,...n},i)=>{let o=r.useRef(null),d=(0,T.s)(o,i),c=(0,O.Z)(s),u=(0,E.X)(t);return r.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==s&&t){let a=new Event("click",{bubbles:l});t.call(e,s),e.dispatchEvent(a)}},[c,s,l]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...n,tabIndex:-1,ref:d,style:{...n.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function H(e){return e?"checked":"unchecked"}G.displayName="SwitchBubbleInput";let V=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(B,{className:(0,x.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 focus-visible:ring-offset-white disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-slate-900 data-[state=unchecked]:bg-slate-200 dark:focus-visible:ring-slate-300 dark:focus-visible:ring-offset-slate-950 dark:data-[state=checked]:bg-slate-50 dark:data-[state=unchecked]:bg-slate-800",e),...t,ref:s,children:(0,a.jsx)(Y,{className:(0,x.cn)("pointer-events-none block h-4 w-4 rounded-full bg-white shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0 dark:bg-slate-950")})}));V.displayName=B.displayName;var J=s(15079),X=s(84027),Q=s(41550);let K=(0,c.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]);var ee=s(41862);function et({isVisible:e,message:t="Setting up your account...",submessage:s="This will only take a moment",type:r="loading"}){if(!e)return null;let l=(()=>{switch(r){case"success":return{bg:"bg-green-50 dark:bg-green-900/20",border:"border-green-200 dark:border-green-800",text:"text-green-900 dark:text-green-100",subtext:"text-green-700 dark:text-green-300"};case"completing":return{bg:"bg-blue-50 dark:bg-blue-900/20",border:"border-blue-200 dark:border-blue-800",text:"text-blue-900 dark:text-blue-100",subtext:"text-blue-700 dark:text-blue-300"};default:return{bg:"bg-white dark:bg-slate-900",border:"border-slate-200 dark:border-slate-700",text:"text-slate-900 dark:text-slate-100",subtext:"text-slate-600 dark:text-slate-400"}}})();return(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm",children:(0,a.jsx)(h.Zp,{className:(0,x.cn)("w-full max-w-md mx-4 shadow-2xl",l.bg,l.border),children:(0,a.jsxs)(h.Wu,{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"mb-6 flex justify-center",children:(()=>{switch(r){case"success":return(0,a.jsx)(d.A,{className:"h-12 w-12 text-green-600 dark:text-green-400"});case"completing":return(0,a.jsx)(w,{className:"h-12 w-12 text-blue-600 dark:text-blue-400 animate-pulse"});default:return(0,a.jsx)(ee.A,{className:"h-12 w-12 text-blue-600 dark:text-blue-400 animate-spin"})}})()}),(0,a.jsx)("h3",{className:(0,x.cn)("text-xl font-semibold mb-2",l.text),children:t}),(0,a.jsx)("p",{className:(0,x.cn)("text-sm",l.subtext),children:s}),"loading"===r&&(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("div",{className:"flex justify-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]})}),"completing"===r&&(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"w-8 h-1 bg-blue-200 dark:bg-blue-800 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"w-full h-full bg-blue-600 dark:bg-blue-400 rounded-full animate-pulse"})})})})]})})})}let es=[{icon:y.A,title:"Connect Your First Database",description:"Start by connecting to your data sources",action:"Go to Data Sources"},{icon:N.A,title:"Ask Your First Question",description:"Try asking questions about your data in natural language",action:"Start Chatting"},{icon:k.A,title:"Explore Reports",description:"View and create reports from your data",action:"View Reports"}],ea=[{id:"welcome",title:"Welcome",description:"Welcome to Agent Platform",component:function({onNext:e,isLoading:t}){return(0,a.jsxs)(h.Zp,{className:"border-0 shadow-lg",children:[(0,a.jsxs)(h.aR,{className:"text-center pb-6",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit",children:(0,a.jsx)(w,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)(h.ZB,{className:"text-3xl font-bold text-slate-900 dark:text-slate-100",children:"Welcome to Agent Platform!"}),(0,a.jsx)(h.BT,{className:"text-lg text-slate-600 dark:text-slate-400 mt-2",children:"Your intelligent data analysis companion is ready to help you unlock insights from your data."})]}),(0,a.jsxs)(h.Wu,{className:"space-y-6",children:[(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-3",children:C.map((e,t)=>(0,a.jsxs)("div",{className:"text-center p-4 rounded-lg bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700",children:[(0,a.jsx)("div",{className:"mx-auto mb-3 p-2 bg-white dark:bg-slate-700 rounded-lg w-fit",children:(0,a.jsx)(e.icon,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)("h3",{className:"font-semibold text-slate-900 dark:text-slate-100 mb-1",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:e.description})]},t))}),(0,a.jsxs)("div",{className:"text-center py-6",children:[(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-400 mb-6",children:"Let's get you set up with a quick tour and personalize your experience. This will only take a few minutes!"}),(0,a.jsx)(p.$,{onClick:e,disabled:t,size:"lg",className:"px-8 py-3 text-lg",children:"Get Started"})]}),(0,a.jsx)("div",{className:"text-center text-sm text-slate-500 dark:text-slate-400",children:(0,a.jsx)("p",{children:"You can always change these settings later in your profile."})})]})]})},isCompleted:!1},{id:"profile",title:"Profile Setup",description:"Set up your profile information",component:function({onNext:e,onPrevious:t,isLoading:s}){let[l,n]=(0,r.useState)({fullName:"",jobTitle:"",company:"",bio:""}),[i,o]=(0,r.useState)({}),d=(e,t)=>{n(s=>({...s,[e]:t})),i[e]&&o(t=>({...t,[e]:void 0}))},c=()=>{let e={};return l.fullName.trim()||(e.fullName="Full name is required"),o(e),0===Object.keys(e).length};return(0,a.jsxs)(h.Zp,{className:"border-0 shadow-lg",children:[(0,a.jsxs)(h.aR,{className:"text-center pb-6",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit",children:(0,a.jsx)(q.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)(h.ZB,{className:"text-2xl font-bold text-slate-900 dark:text-slate-100",children:"Set Up Your Profile"}),(0,a.jsx)(h.BT,{className:"text-slate-600 dark:text-slate-400 mt-2",children:"Tell us a bit about yourself to personalize your experience."})]}),(0,a.jsxs)(h.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.J,{htmlFor:"fullName",className:"text-sm font-medium",children:"Full Name *"}),(0,a.jsx)(A.p,{id:"fullName",type:"text",placeholder:"Enter your full name",value:l.fullName,onChange:e=>d("fullName",e.target.value),className:i.fullName?"border-red-500":""}),i.fullName&&(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:i.fullName})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.J,{htmlFor:"jobTitle",className:"text-sm font-medium",children:"Job Title"}),(0,a.jsx)(A.p,{id:"jobTitle",type:"text",placeholder:"e.g., Data Analyst, Business Intelligence Manager",value:l.jobTitle,onChange:e=>d("jobTitle",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.J,{htmlFor:"company",className:"text-sm font-medium",children:"Company"}),(0,a.jsx)(A.p,{id:"company",type:"text",placeholder:"Enter your company name",value:l.company,onChange:e=>d("company",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.J,{htmlFor:"bio",className:"text-sm font-medium",children:"Bio (Optional)"}),(0,a.jsx)(P.T,{id:"bio",placeholder:"Tell us about your role and what you hope to achieve with Agent Platform...",value:l.bio,onChange:e=>d("bio",e.target.value),rows:3})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,a.jsxs)(p.$,{variant:"outline",onClick:t,disabled:s,className:"flex items-center space-x-2",children:[(0,a.jsx)(R,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Previous"})]}),(0,a.jsxs)(p.$,{onClick:()=>{c()&&(console.log("Profile data:",l),e())},disabled:s,className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"Continue"}),(0,a.jsx)(z,{className:"h-4 w-4"})]})]}),(0,a.jsx)("div",{className:"text-center text-sm text-slate-500 dark:text-slate-400",children:(0,a.jsx)("p",{children:"* Required fields"})})]})]})},isCompleted:!1},{id:"preferences",title:"Preferences",description:"Configure your preferences",component:function({onNext:e,onPrevious:t,isLoading:s}){let[l,n]=(0,r.useState)({emailNotifications:!0,weeklyDigest:!0,securityAlerts:!0,defaultOutputFormat:"excel",theme:"system",timezone:Intl.DateTimeFormat().resolvedOptions().timeZone}),i=(e,t)=>{n(s=>({...s,[e]:t}))},o=(e,t)=>{n(s=>({...s,[e]:t}))};return(0,a.jsxs)(h.Zp,{className:"border-0 shadow-lg",children:[(0,a.jsxs)(h.aR,{className:"text-center pb-6",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit",children:(0,a.jsx)(X.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)(h.ZB,{className:"text-2xl font-bold text-slate-900 dark:text-slate-100",children:"Configure Your Preferences"}),(0,a.jsx)(h.BT,{className:"text-slate-600 dark:text-slate-400 mt-2",children:"Customize your experience with Agent Platform. You can change these anytime."})]}),(0,a.jsxs)(h.Wu,{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(Q.A,{className:"h-5 w-5 text-slate-600 dark:text-slate-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100",children:"Notifications"})]}),(0,a.jsxs)("div",{className:"space-y-4 pl-7",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Receive updates about your queries and reports"})]}),(0,a.jsx)(V,{checked:l.emailNotifications,onCheckedChange:e=>i("emailNotifications",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Weekly Digest"}),(0,a.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Get a summary of your activity and insights"})]}),(0,a.jsx)(V,{checked:l.weeklyDigest,onCheckedChange:e=>i("weeklyDigest",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Security Alerts"}),(0,a.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Important security and account notifications"})]}),(0,a.jsx)(V,{checked:l.securityAlerts,onCheckedChange:e=>i("securityAlerts",e)})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(K,{className:"h-5 w-5 text-slate-600 dark:text-slate-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100",children:"Application"})]}),(0,a.jsxs)("div",{className:"space-y-4 pl-7",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Default Output Format"}),(0,a.jsxs)(J.l6,{value:l.defaultOutputFormat,onValueChange:e=>o("defaultOutputFormat",e),children:[(0,a.jsx)(J.bq,{children:(0,a.jsx)(J.yv,{})}),(0,a.jsxs)(J.gC,{children:[(0,a.jsx)(J.eb,{value:"excel",children:"Excel (.xlsx)"}),(0,a.jsx)(J.eb,{value:"csv",children:"CSV"}),(0,a.jsx)(J.eb,{value:"json",children:"JSON"}),(0,a.jsx)(J.eb,{value:"table",children:"Table View"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Theme"}),(0,a.jsxs)(J.l6,{value:l.theme,onValueChange:e=>o("theme",e),children:[(0,a.jsx)(J.bq,{children:(0,a.jsx)(J.yv,{})}),(0,a.jsxs)(J.gC,{children:[(0,a.jsx)(J.eb,{value:"system",children:"System"}),(0,a.jsx)(J.eb,{value:"light",children:"Light"}),(0,a.jsx)(J.eb,{value:"dark",children:"Dark"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,a.jsxs)(p.$,{variant:"outline",onClick:t,disabled:s,className:"flex items-center space-x-2",children:[(0,a.jsx)(R,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Previous"})]}),(0,a.jsxs)(p.$,{onClick:()=>{console.log("Preferences data:",l),e()},disabled:s,className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"Continue"}),(0,a.jsx)(z,{className:"h-4 w-4"})]})]}),(0,a.jsx)("div",{className:"text-center text-sm text-slate-500 dark:text-slate-400",children:(0,a.jsx)("p",{children:"This step is optional - you can skip and configure these later"})})]})]})},isCompleted:!1,isOptional:!0},{id:"completion",title:"Complete",description:"Finish your setup",component:function({onPrevious:e,onComplete:t,isLoading:s}){let[l,n]=(0,r.useState)(!1),[i,o]=(0,r.useState)(null),c=async()=>{n(!0),o(null);try{await t()}catch(t){console.error("Failed to complete onboarding:",t);let e="Failed to complete setup. Please try again.";t.message?.includes("required steps")?e=t.message:t.message?.includes("network")||t.message?.includes("fetch")?e="Network error. Please check your connection and try again.":t.message?.includes("server")||t.message?.includes("500")?e="Server error. Please try again in a moment.":(t.message?.includes("unauthorized")||t.message?.includes("401"))&&(e="Session expired. Please log in again."),o(e),n(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(et,{isVisible:l,message:"Completing your setup...",submessage:"Finalizing your Agent Platform account",type:"completing"}),(0,a.jsxs)(h.Zp,{className:"border-0 shadow-lg",children:[(0,a.jsxs)(h.aR,{className:"text-center pb-6",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-green-100 dark:bg-green-900 rounded-full w-fit",children:(0,a.jsx)(d.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,a.jsx)(h.ZB,{className:"text-2xl font-bold text-slate-900 dark:text-slate-100",children:"You're All Set!"}),(0,a.jsx)(h.BT,{className:"text-slate-600 dark:text-slate-400 mt-2",children:"Welcome to Agent Platform! Your account is ready and you can start exploring your data."})]}),(0,a.jsxs)(h.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg w-fit",children:(0,a.jsx)(w,{className:"h-12 w-12 text-blue-600 dark:text-blue-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2",children:"Setup Complete!"}),(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-400",children:"Your Agent Platform account is now configured and ready to use."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100 text-center",children:"What's Next?"}),(0,a.jsx)("div",{className:"grid gap-4",children:es.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 rounded-lg bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700",children:[(0,a.jsx)("div",{className:"p-2 bg-white dark:bg-slate-700 rounded-lg",children:(0,a.jsx)(e.icon,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h5",{className:"font-semibold text-slate-900 dark:text-slate-100",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:e.description})]}),(0,a.jsx)("div",{className:"text-sm text-blue-600 dark:text-blue-400 font-medium",children:e.action})]},t))})]}),i&&(0,a.jsxs)("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,a.jsx)("div",{className:"flex items-start space-x-3",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400 font-medium mb-1",children:"Setup Error"}),(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:i})]})}),(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsx)(p.$,{onClick:c,variant:"outline",size:"sm",className:"text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-700 dark:hover:bg-red-900/20",children:"Try Again"})})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,a.jsxs)(p.$,{variant:"outline",onClick:e,disabled:s||l,className:"flex items-center space-x-2",children:[(0,a.jsx)(R,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Previous"})]}),(0,a.jsx)(p.$,{onClick:c,disabled:s||l,size:"lg",className:"px-8 flex items-center space-x-2",children:l?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ee.A,{className:"h-4 w-4 animate-spin"}),(0,a.jsx)("span",{children:"Completing Setup..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:"Complete Setup"}),(0,a.jsx)(d.A,{className:"h-4 w-4"})]})})]}),(0,a.jsx)("div",{className:"text-center text-sm text-slate-500 dark:text-slate-400",children:(0,a.jsx)("p",{children:"You can always access these features from your dashboard or the navigation menu."})})]})]})]})},isCompleted:!1}];function er(){let{currentStepIndex:e,steps:t,isLoading:s,progress:n,hasResumableProgress:o,goToNext:d,goToPrevious:c,completeOnboarding:u,resumeOnboarding:x,startOver:p}=function(e){let[t,s]=(0,r.useState)(0),[a,n]=(0,r.useState)(e),[o,d]=(0,r.useState)(!1),[c,u]=(0,r.useState)(null),[x,m]=(0,r.useState)(!1),{completeOnboarding:p}=(0,l.A)(),h=t<a.length-1,f=t>0,g=(0,r.useCallback)(()=>{if(h){let e=t+1;n(s=>{let a=s.map((e,s)=>s===t?{...e,isCompleted:!0}:e),r=(0,i.YL)(a,e);return u(r),(0,i.Wr)(r),a}),s(e)}},[t,h]),b=(0,r.useCallback)(()=>{f&&s(e=>e-1)},[f]),j=(0,r.useCallback)(e=>{n(t=>t.map((t,s)=>s===e?{...t,isCompleted:!0}:t))},[]),v=(0,r.useCallback)(async()=>{console.log("\uD83D\uDE80 Starting onboarding completion..."),console.log("Current step index:",t),console.log("Current steps:",a.map(e=>({id:e.id,title:e.title,completed:e.isCompleted,optional:e.isOptional}))),d(!0);try{let e=a.map((e,t)=>e.isOptional?e:{...e,isCompleted:!0});console.log("Updated steps for completion:",e.map(e=>({id:e.id,title:e.title,completed:e.isCompleted,optional:e.isOptional}))),n(e);let t=(0,i.uE)(e);console.log("Validation result:",t),t.canComplete||console.warn("⚠️ Validation failed, but proceeding with completion since user reached completion step"),console.log("✅ Proceeding with backend call..."),await p(),console.log("✅ Backend call successful, cleaning up..."),(0,i.qy)(),u(null),m(!1),console.log("\uD83C\uDF89 Onboarding completion successful!")}catch(e){throw console.error("❌ Failed to complete onboarding:",e),d(!1),e}},[t,p,a]),y=(0,r.useCallback)(()=>{s(0),n(e.map(e=>({...e,isCompleted:!1}))),d(!1),(0,i.qy)(),u((0,i.YL)(e,0)),m(!1)},[e]);return{currentStepIndex:t,steps:a,isLoading:o,canGoNext:h,canGoPrevious:f,progress:c,hasResumableProgress:x,goToNext:g,goToPrevious:b,completeOnboarding:v,markStepCompleted:j,resetOnboarding:y,resumeOnboarding:(0,r.useCallback)(()=>{let t=(0,i.Hg)();if(t){let{steps:a,currentStepIndex:r}=(0,i.ZS)(e,t);n(a),s(r),u(t),m(!1),console.log("Resumed onboarding from step:",t.currentStep)}},[e]),startOver:(0,r.useCallback)(()=>{(0,i.qy)(),s(0),n(e.map(e=>({...e,isCompleted:!1}))),u((0,i.YL)(e,0)),m(!1),d(!1),console.log("Started onboarding over from the beginning")},[e])}}(ea),h=t[e].component;return(0,a.jsxs)(a.Fragment,{children:[o&&n&&(0,a.jsx)(v,{isVisible:o,progress:n,onResume:x,onStartOver:p,onDismiss:()=>{}}),(0,a.jsx)(m,{steps:t,currentStepIndex:e,isLoading:s,children:(0,a.jsx)(h,{onNext:d,onPrevious:c,onComplete:u,isLoading:s})})]})}var el=s(43649),en=s(78122);let ei=(0,c.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);class eo extends r.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})},this.handleGoHome=()=>{window.location.href="/"},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Onboarding Error Boundary caught an error:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-6",children:(0,a.jsxs)(h.Zp,{className:"w-full max-w-md border-red-200 dark:border-red-800",children:[(0,a.jsxs)(h.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 p-3 bg-red-100 dark:bg-red-900 rounded-full w-fit",children:(0,a.jsx)(el.A,{className:"h-8 w-8 text-red-600 dark:text-red-400"})}),(0,a.jsx)(h.ZB,{className:"text-xl font-bold text-red-900 dark:text-red-100",children:"Onboarding Error"}),(0,a.jsx)(h.BT,{className:"text-red-700 dark:text-red-300",children:"Something went wrong during the setup process"})]}),(0,a.jsxs)(h.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400 mb-4",children:"We encountered an unexpected error while setting up your account. You can try again or return to the home page."}),!1]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsxs)(p.$,{onClick:this.handleRetry,className:"flex-1 flex items-center justify-center space-x-2",children:[(0,a.jsx)(en.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Try Again"})]}),(0,a.jsxs)(p.$,{variant:"outline",onClick:this.handleGoHome,className:"flex-1 flex items-center justify-center space-x-2",children:[(0,a.jsx)(ei,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Go Home"})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-xs text-slate-500 dark:text-slate-400",children:"If this problem persists, please contact support."})})]})]})}):this.props.children}}function ed(){let{isAuthenticated:e,isLoading:t,isNewUser:s,isCompletingOnboarding:r}=(0,l.A)();return((0,n.useRouter)(),t)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-400",children:"Loading..."})]})}):e&&s?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,a.jsx)(eo,{children:(0,a.jsx)(er,{})})}):null}},41550:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},43649:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44346:(e,t,s)=>{"use strict";function a(e){}function r(){return null}function l(){}function n(e,t){let s=e.slice(0,t).filter(e=>e.isCompleted).map(e=>e.id);return{currentStep:e[t]?.id||"welcome",completedSteps:s,lastUpdated:new Date().toISOString(),canResume:s.length>0,totalSteps:e.length}}s.d(t,{Hg:()=>r,Wr:()=>a,YL:()=>n,ZS:()=>i,qy:()=>l,uE:()=>o});function i(e,t){var s;if(!t||!((s=t)&&"object"==typeof s&&"string"==typeof s.currentStep&&Array.isArray(s.completedSteps)&&"string"==typeof s.lastUpdated&&"boolean"==typeof s.canResume&&"number"==typeof s.totalSteps))return{steps:e,currentStepIndex:0};let a=e.map(e=>({...e,isCompleted:t.completedSteps.includes(e.id)})),r=function(e,t){let s=e.findIndex(e=>e.id===t);return s>=0?s:0}(a,t.currentStep);return{steps:a,currentStepIndex:r}}function o(e){let t=[],s=[],a=["completion","complete","finish","final"],r=e.filter(e=>!e.isOptional&&!a.includes(e.id.toLowerCase()));for(let t of(console.log("\uD83D\uDD0D Validating completion requirements:"),console.log("All steps:",e.map(e=>({id:e.id,title:e.title,completed:e.isCompleted,optional:e.isOptional}))),console.log("Required steps:",r.map(e=>({id:e.id,title:e.title,completed:e.isCompleted}))),r))t.isCompleted||s.push(t.title);console.log("Missing steps:",s),s.length>0&&t.push(`Please complete the following required steps: ${s.join(", ")}`);let l={canComplete:0===t.length,missingSteps:s,errors:t};return console.log("Validation result:",l),l}},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>i,Zp:()=>l,aR:()=>n});var a=s(60687);s(43210);var r=s(7766);function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold text-white",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}},46657:(e,t,s)=>{"use strict";s.d(t,{k:()=>y});var a=s(60687),r=s(43210),l=s(11273),n=s(14163),i="Progress",[o,d]=(0,l.A)(i),[c,u]=o(i),x=r.forwardRef((e,t)=>{var s,r;let{__scopeProgress:l,value:i=null,max:o,getValueLabel:d=h,...u}=e;(o||0===o)&&!b(o)&&console.error((s=`${o}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let x=b(o)?o:100;null===i||j(i,x)||console.error((r=`${i}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=j(i,x)?i:null,p=g(m)?d(m,x):void 0;return(0,a.jsx)(c,{scope:l,value:m,max:x,children:(0,a.jsx)(n.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":g(m)?m:void 0,"aria-valuetext":p,role:"progressbar","data-state":f(m,x),"data-value":m??void 0,"data-max":x,...u,ref:t})})});x.displayName=i;var m="ProgressIndicator",p=r.forwardRef((e,t)=>{let{__scopeProgress:s,...r}=e,l=u(m,s);return(0,a.jsx)(n.sG.div,{"data-state":f(l.value,l.max),"data-value":l.value??void 0,"data-max":l.max,...r,ref:t})});function h(e,t){return`${Math.round(e/t*100)}%`}function f(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function b(e){return g(e)&&!isNaN(e)&&e>0}function j(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}p.displayName=m;var v=s(7766);let y=r.forwardRef(({className:e,value:t,...s},r)=>(0,a.jsx)(x,{ref:r,className:(0,v.cn)("relative h-2 w-full overflow-hidden rounded-full bg-slate-100 dark:bg-slate-800",e),...s,children:(0,a.jsx)(p,{className:"h-full w-full flex-1 bg-slate-900 transition-all dark:bg-slate-50",style:{transform:`translateX(-${100-(t||0)}%)`}})}));y.displayName=x.displayName},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>o});var a=s(60687),r=s(43210),l=s(14163),n=r.forwardRef((e,t)=>(0,a.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=s(7766);function o({className:e,...t}){return(0,a.jsx)(n,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63644:(e,t,s)=>{Promise.resolve().then(s.bind(s,35675))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>l});var a=s(60687);s(43210);var r=s(7766);function l({className:e,type:t,...s}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},93564:(e,t,s)=>{Promise.resolve().then(s.bind(s,18902))},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(60687);s(43210);var r=s(24224),l=s(7766);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...s}){return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),e),...s})}},97840:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,255,658,365,292,668,695],()=>s(21860));module.exports=a})();