(()=>{var e={};e.id=678,e.ids=[678],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4794:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>x,tree:()=>l});var a=t(65239),s=t(48088),o=t(88170),i=t.n(o),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let l={children:["",{children:["(auth)",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,99308)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(auth)\\register\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(auth)\\register\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(auth)/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},5353:(e,r,t)=>{Promise.resolve().then(t.bind(t,78777))},7766:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o});var a=t(49384),s=t(82348);function o(...e){return(0,s.QP)((0,a.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var a=t(60687),s=t(43210),o=t(8730),i=t(24224),n=t(7766);let d=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef(({className:e,variant:r,size:t,asChild:s=!1,...i},l)=>{let u=s?o.DX:"button";return(0,a.jsx)(u,{className:(0,n.cn)(d({variant:r,size:t,className:e})),ref:l,...i})});l.displayName="Button"},33873:e=>{"use strict";e.exports=require("path")},41550:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63497:(e,r,t)=>{Promise.resolve().then(t.bind(t,99308))},64021:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var a=t(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78777:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var a=t(60687),s=t(43210),o=t(89667),i=t(29523),n=t(91010),d=t(58869),l=t(41550),u=t(64021),c=t(12597),x=t(13861),b=t(85814),h=t.n(b);let g=()=>{let[e,r]=(0,s.useState)(""),[t,b]=(0,s.useState)(""),[g,p]=(0,s.useState)(""),[m,f]=(0,s.useState)(""),[y,v]=(0,s.useState)(!1),[k,w]=(0,s.useState)(!1),[j,N]=(0,s.useState)(null),[A,P]=(0,s.useState)(null),[q,R]=(0,s.useState)(!1),{register:E}=(0,n.A)(),S=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),_=e=>/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(e),C=()=>g.trim()?e.trim()?S(e)?t?_(t)?t===m||(N("Passwords do not match"),!1):(N("Password must be at least 8 characters with uppercase, lowercase, number, and special character"),!1):(N("Password is required"),!1):(N("Please enter a valid email address"),!1):(N("Email is required"),!1):(N("Full name is required"),!1),M=async r=>{if(r.preventDefault(),N(null),P(null),C()){R(!0);try{await E({email:e.trim(),password:t,full_name:g.trim()})}catch(e){if(console.error("Registration failed",e),e.response?.data?.error){let r=e.response.data.error;"EMAIL_ALREADY_EXISTS"===r.code?(N(r.message||"An account with this email address already exists. Please use a different email or try logging in."),P("EMAIL_ALREADY_EXISTS"),setTimeout(()=>{let e=document.getElementById("email");e&&(e.focus(),e.select())},100)):(N(r.message||"Registration failed. Please try again."),P("GENERAL_ERROR"))}else e.message?N(e.message):N("Registration failed. Please try again."),P("GENERAL_ERROR")}R(!1)}};return(0,a.jsxs)("form",{onSubmit:M,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-5",children:[(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10",children:(0,a.jsx)(d.A,{className:"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200"})}),(0,a.jsx)(o.p,{id:"fullName",type:"text",value:g,onChange:e=>p(e.target.value),placeholder:"Full name",className:"w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500",required:!0,disabled:q})]}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10",children:(0,a.jsx)(l.A,{className:"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200"})}),(0,a.jsx)(o.p,{id:"email",type:"email",value:e,onChange:e=>r(e.target.value),placeholder:"Email address",className:"w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500",required:!0,disabled:q})]}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10",children:(0,a.jsx)(u.A,{className:"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200"})}),(0,a.jsx)(o.p,{id:"password",type:y?"text":"password",value:t,onChange:e=>b(e.target.value),placeholder:"Password",className:"w-full pl-12 pr-14 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500",required:!0,disabled:q}),(0,a.jsx)("button",{type:"button",onClick:()=>v(!y),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100 transition-colors duration-200 z-10 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700",disabled:q,children:y?(0,a.jsx)(c.A,{className:"h-5 w-5"}):(0,a.jsx)(x.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10",children:(0,a.jsx)(u.A,{className:"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200"})}),(0,a.jsx)(o.p,{id:"confirmPassword",type:k?"text":"password",value:m,onChange:e=>f(e.target.value),placeholder:"Confirm password",className:"w-full pl-12 pr-14 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500",required:!0,disabled:q}),(0,a.jsx)("button",{type:"button",onClick:()=>w(!k),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100 transition-colors duration-200 z-10 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700",disabled:q,children:k?(0,a.jsx)(c.A,{className:"h-5 w-5"}):(0,a.jsx)(x.A,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/30 border-2 border-blue-200 dark:border-blue-500/50 rounded-2xl p-4 shadow-sm",children:[(0,a.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300 font-medium mb-2",children:"Password requirements:"}),(0,a.jsxs)("ul",{className:"text-xs text-blue-600 dark:text-blue-400 space-y-1",children:[(0,a.jsx)("li",{children:"• At least 8 characters long"}),(0,a.jsx)("li",{children:"• Contains uppercase and lowercase letters"}),(0,a.jsx)("li",{children:"• Contains at least one number"}),(0,a.jsx)("li",{children:"• Contains at least one special character (@$!%*?&)"})]})]}),j&&(0,a.jsxs)("div",{className:`border-2 rounded-2xl p-4 animate-in slide-in-from-top-2 duration-300 shadow-sm ${"EMAIL_ALREADY_EXISTS"===A?"bg-amber-50 dark:bg-amber-900/30 border-amber-200 dark:border-amber-500/50":"bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-500/50"}`,children:[(0,a.jsx)("p",{className:`text-sm text-center font-medium ${"EMAIL_ALREADY_EXISTS"===A?"text-amber-700 dark:text-amber-300":"text-red-700 dark:text-red-300"}`,children:j}),"EMAIL_ALREADY_EXISTS"===A&&(0,a.jsx)("div",{className:"mt-3 text-center",children:(0,a.jsx)(h(),{href:"/login",className:"inline-flex items-center px-4 py-2 text-sm font-medium text-amber-700 dark:text-amber-300 bg-amber-100 dark:bg-amber-800/50 border border-amber-300 dark:border-amber-600 rounded-lg hover:bg-amber-200 dark:hover:bg-amber-800/70 transition-colors duration-200",children:"Go to Login Page"})})]}),(0,a.jsx)(i.$,{type:"submit",className:"w-full bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-500 dark:to-blue-600 hover:from-blue-700 hover:to-blue-800 dark:hover:from-blue-600 dark:hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg shadow-blue-500/30 dark:shadow-blue-400/20 hover:shadow-blue-500/50 dark:hover:shadow-blue-400/30 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:shadow-blue-500/25 text-base border-0",disabled:q,children:q?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-3"}),"Creating account..."]}):"Create account"}),(0,a.jsx)("div",{className:"text-center pt-4",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Already have an account?"," ",(0,a.jsx)(h(),{href:"/login",className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 font-semibold hover:underline",children:"Sign in"})]})})]})},p=()=>(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("div",{className:"absolute top-0 left-0 p-6 z-20",children:(0,a.jsxs)(h(),{href:"/",className:"flex items-center space-x-2 group",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-blue-500 group-hover:bg-blue-600 transition-colors duration-200 shadow-sm",children:(0,a.jsx)("svg",{viewBox:"0 0 24 24",fill:"none",className:"w-5 h-5 text-white",children:(0,a.jsx)("path",{d:"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,a.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-500 transition-colors duration-200",children:"Agent"})]})}),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center p-6",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 rounded-3xl border border-gray-200/50 dark:border-gray-700/50 shadow-xl",children:[(0,a.jsxs)("div",{className:"p-8 pb-0 text-center",children:[(0,a.jsx)("h1",{className:"text-3xl font-light text-gray-900 dark:text-white mb-2 tracking-tight",children:"Create your account"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400 font-light",children:"Join us and start your journey"})]}),(0,a.jsx)("div",{className:"p-8",children:(0,a.jsx)(g,{})})]}),(0,a.jsx)("div",{className:"text-center mt-8",children:(0,a.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["By creating an account, you agree to our"," ",(0,a.jsx)("a",{href:"#",className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 hover:underline",children:"Terms of Service"})," ","and"," ",(0,a.jsx)("a",{href:"#",className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 hover:underline",children:"Privacy Policy"})]})})]})})]})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var a=t(60687);t(43210);var s=t(7766);function o({className:e,type:r,...t}){return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},94735:e=>{"use strict";e.exports=require("events")},99308:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\NXTHUMANS\\\\internal-solutions-nxtwebsite\\\\AgentReport 2\\\\AgentReportUI\\\\src\\\\app\\\\(auth)\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(auth)\\register\\page.tsx","default")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,255,658,365,814,695],()=>t(4794));module.exports=a})();