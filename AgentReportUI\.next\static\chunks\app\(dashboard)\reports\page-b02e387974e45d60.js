(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[670],{16144:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var a=r(95155),s=r(12115),n=r(74677),l=r(74045),d=r(6874),i=r.n(d),o=r(58189),c=r(30095),u=r(87914),x=r(30285),m=r(66695),h=r(85127),f=r(26126),g=r(57434),b=r(53904),p=r(92657),j=r(91788);function y(){let{isAuthenticated:e}=(0,l.A)(),{listUserReports:t}=(0,o.g)(),[r,n]=(0,s.useState)([]),[d,c]=(0,s.useState)(!1),[u,y]=(0,s.useState)(null),N=(0,s.useCallback)(async()=>{c(!0),y(null);try{let e=await t({max_reports:100});n(e.reports)}catch(e){console.error("Error loading reports:",e),y("Failed to load reports. Please try again.")}finally{c(!1)}},[t]);(0,s.useEffect)(()=>{e?N():(n([]),y(null))},[e,N]);let v=async()=>{y(null),await N()},w=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},k=e=>{let t=new Date(e);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},A=()=>(0,a.jsx)(g.A,{className:"h-4 w-4"}),R=e=>{switch(e.toLowerCase()){case"csv":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"xlsx":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"json":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},_=e=>{"csv"===e.format?window.open(e.download_url,"_blank"):C(e)},C=e=>{let t=document.createElement("a");t.href=e.download_url,t.download=e.file_name,document.body.appendChild(t),t.click(),document.body.removeChild(t)};return(0,a.jsxs)("div",{className:"flex flex-col h-full p-10",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4",children:[(0,a.jsx)("div",{className:"flex-1"}),(0,a.jsxs)(x.$,{onClick:N,disabled:d,variant:"outline",className:"flex items-center gap-2 w-full sm:w-auto",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 ".concat(d?"animate-spin":"")}),"Refresh"]})]}),u&&(0,a.jsx)(m.Zp,{className:"mb-6 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20",children:(0,a.jsx)(m.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-red-800 dark:text-red-200",children:u}),(0,a.jsx)(x.$,{onClick:v,disabled:d,variant:"outline",size:"sm",className:"ml-4",children:"Try Again"})]})})}),(0,a.jsxs)(m.Zp,{children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2 text-gray-900 dark:text-white",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-gray-600 dark:text-white"}),"Your Reports (",r.length,")"]})}),(0,a.jsx)(m.Wu,{children:d?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 text-gray-600 dark:text-black",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 animate-spin"}),"Loading reports..."]})}):0===r.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-3"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"No reports found"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-black mt-1",children:"Reports will appear here after you generate them from your chats"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)(h.XI,{children:[(0,a.jsx)(h.A0,{children:(0,a.jsxs)(h.Hj,{children:[(0,a.jsx)(h.nd,{children:"File"}),(0,a.jsx)(h.nd,{children:"Format"}),(0,a.jsx)(h.nd,{children:"Size"}),(0,a.jsx)(h.nd,{children:"Session"}),(0,a.jsx)(h.nd,{children:"Created"}),(0,a.jsx)(h.nd,{className:"text-right",children:"Actions"})]})}),(0,a.jsx)(h.BF,{children:r.map(e=>(0,a.jsxs)(h.Hj,{children:[(0,a.jsx)(h.nA,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[A(),(0,a.jsx)("span",{className:"font-medium text-sm",children:e.file_name})]})}),(0,a.jsx)(h.nA,{children:(0,a.jsx)(f.E,{className:"".concat(R(e.format)," text-xs"),children:e.format.toUpperCase()})}),(0,a.jsx)(h.nA,{className:"text-sm text-gray-600 dark:text-white",children:w(e.size)}),(0,a.jsx)(h.nA,{className:"text-sm",children:(0,a.jsx)(i(),{href:"/chat/chat_sess".concat(e.session_id),className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:underline",children:e.session_id})}),(0,a.jsx)(h.nA,{className:"text-sm text-gray-600 dark:text-white",children:k(e.last_modified)}),(0,a.jsx)(h.nA,{className:"text-right",children:(0,a.jsx)("div",{className:"flex items-center justify-end gap-2",children:(0,a.jsx)(x.$,{variant:"ghost",size:"sm",onClick:()=>_(e),className:"h-8 w-8 p-0",title:"csv"===e.format?"View in browser":"Download file",children:"csv"===e.format?(0,a.jsx)(p.A,{className:"h-4 w-4"}):(0,a.jsx)(j.A,{className:"h-4 w-4"})})})})]},e.key))})]})})})]})]})}function N(){let e=(0,s.useMemo)(()=>({title:"Reports",icon:g.A}),[]);return(0,u.H)(e),(0,a.jsx)(y,{})}function v(){return(0,a.jsx)(c.Ay,{children:(0,a.jsx)(n.A,{children:(0,a.jsx)(N,{})})})}},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var a=r(95155);r(12115);var s=r(74466),n=r(46486);let l=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,n.cn)(l({variant:r}),t),...s})}},30095:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i});var a=r(95155),s=r(12115),n=r(74045),l=r(35695),d=r(45786);function i(e){let{children:t,requireAuth:r=!0,redirectTo:i="/login"}=e,{isAuthenticated:o,isLoading:c,isNewUser:u,signIn:x}=(0,n.A)(),m=(0,l.useRouter)(),h=(0,l.usePathname)(),[f,g]=(0,s.useState)(!0),[b,p]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{(async()=>{if(!r||[d.bw.HOME,d.bw.LOGIN,d.bw.REGISTER,d.bw.AUTH.CALLBACK,d.bw.OAUTH.CALLBACK,d.bw.ONBOARDING].includes(h))return g(!1);if(o){if(u&&"/onboarding"!==h){console.log("New user on protected route, redirecting to onboarding"),m.push("/onboarding");return}if(!u&&"/onboarding"===h){console.log("Existing user on onboarding, redirecting to dashboard"),m.push("/dashboard");return}g(!1);return}if(!o&&!b&&!c){p(!0),console.log("Attempting automatic authentication from stored tokens...");try{await x(void 0,!1)}catch(e){console.log("Auto-authentication failed, redirecting to login"),m.push(i)}return}if(!o&&b&&!c){console.log("Not authenticated, redirecting to login"),m.push(i);return}g(!1)})()},[o,c,u,h,r,i,m,x,b]),f||c)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Checking authentication..."})]})}):r&&!o?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Authentication Required"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please sign in to access this page."}),(0,a.jsx)("button",{onClick:()=>m.push(i),className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Sign In"})]})}):(0,a.jsx)(a.Fragment,{children:t})}},36393:(e,t,r)=>{Promise.resolve().then(r.bind(r,16144))},53904:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},57434:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>l});var a=r(95155);r(12115);var s=r(46486);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold text-white",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}},85127:(e,t,r)=>{"use strict";r.d(t,{A0:()=>d,BF:()=>i,Hj:()=>o,XI:()=>l,nA:()=>u,nd:()=>c,r6:()=>x});var a=r(95155),s=r(12115),n=r(46486);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",r),...s})})});l.displayName="Table";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",r),...s})});d.displayName="TableHeader";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",r),...s})});i.displayName="TableBody",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...s})}).displayName="TableFooter";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...s})});o.displayName="TableRow";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...s})});c.displayName="TableHead";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...s})});u.displayName="TableCell";let x=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",r),...s})});x.displayName="TableCaption"},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[464,817,874,786,826,189,45,179,30,441,684,358],()=>t(36393)),_N_E=e.O()}]);