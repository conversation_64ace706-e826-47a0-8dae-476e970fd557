"""Database Manager Service

This module provides high-level services for managing database connections and queries.
It acts as a coordinator between controllers and the lower-level database service.
"""

from __future__ import annotations

import logging, uuid, time, json, asyncio
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime

from fastapi import HTTPException, status
from fastapi.responses import StreamingResponse

from app.models.database import Database, DatabaseType, DatabaseCredentials
from app.services.database_service import DatabaseService
from app.services.connection_pool_manager import get_connection_pool_manager
from app.agents.database_query.orchestrator_agent import OrchestratorAgent
from app.services.conversation_service import ConversationService
from app.services.credential_service import CredentialService
from app.utils.standard_response_builder import build_success_response, build_simple_question_response

logger = logging.getLogger(__name__)

class DatabaseManagerService:
    """Service for managing database connections and queries.
    
    This service coordinates between controllers and the lower-level database operations.
    Database credentials are securely stored using encryption, not kept in memory.
    """
    
    def __init__(self):
        self.database_service = DatabaseService()
        self.connection_pool_manager = get_connection_pool_manager()
        self.conversation_service = ConversationService()
        self.credential_service = CredentialService()
        # Cache for temporary orchestrator instances (no credentials stored)
        self._orchestrator_cache: Dict[str, OrchestratorAgent] = {}

    def cleanup_user_resources(self, user_id: str, reason: str = "user_logout"):
        """Clean up all user resources including connection pools and caches.

        Args:
            user_id: The user ID to clean up
            reason: Reason for cleanup (for logging)
        """
        logger.info(f"Cleaning up resources for user {user_id} ({reason})")

        # Clean up connection pools
        self.connection_pool_manager.cleanup_user_session(user_id, reason)

        # Clear orchestrator cache for this user
        if user_id in self._orchestrator_cache:
            del self._orchestrator_cache[user_id]
            logger.debug(f"Cleared orchestrator cache for user {user_id}")

        logger.info(f"Completed resource cleanup for user {user_id}")

    def get_user_resource_stats(self, user_id: str) -> Dict[str, Any]:
        """Get resource usage statistics for a user.

        Args:
            user_id: The user ID

        Returns:
            Dictionary with resource statistics
        """
        return {
            "user_id": user_id,
            "has_cached_orchestrator": user_id in self._orchestrator_cache,
            "connection_pools": self.connection_pool_manager.get_session_stats(),
            "database_pools": self.database_service.get_user_pool_stats(user_id)
        }

    async def list_user_chats(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Wrapper over ConversationService.list_sessions()
        """
        return await self.conversation_service.list_sessions(user_id)

    async def get_user_chat_history(self, user_id: str, session_id: str) -> List[Dict[str, str]]:
        """
        Get chat history for a specific session with error handling.
        
        Args:
            user_id: The user ID
            session_id: The session ID to get history for
            
        Returns:
            List[Dict[str, str]]: Chat history messages (empty list if no history exists)
        """
        history = await self.conversation_service.get_history(
            user_id, session_id, limit=100
        )
        # Return empty list instead of 404 - consistent with query_databases behavior
        # and better UX for new chats
        return history

    async def delete_user_chat(self, user_id: str, session_id: str) -> Dict[str, str]:
        """
        Delete a chat session and all its history.
        
        Args:
            user_id: The user ID
            session_id: The session ID to delete
            
        Returns:
            Dict[str, str]: Success message
            
        Raises:
            HTTPException: If the chat session is not found or deletion fails
        """
        try:
            success = await self.conversation_service.delete_session(user_id, session_id)
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Chat session '{session_id}' not found or already deleted",
                )
            
            return {"message": f"Chat session '{session_id}' deleted successfully"}
            
        except HTTPException:
            # Re-raise HTTPExceptions as-is
            raise
        except Exception as e:
            logger.error(f"Error deleting chat session {session_id} for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting chat session: {str(e)}",
            )
        
    async def rename_user_chat(self, user_id: str, session_id: str, title: str) -> Dict[str, str]:
        """
        Rename a chat session.
        
        Args:
            user_id: The user ID
            session_id: The session ID to rename
            title: New title for the chat session
            
        Returns:
            Dict[str, str]: Success message
            
        Raises:
            HTTPException: If the chat session is not found or renaming fails
        """
        try:
            success = await self.conversation_service.rename_session(user_id, session_id, title)
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Chat session '{session_id}' not found",
                )
            
            return {"message": f"Chat session '{session_id}' renamed to '{title}' successfully"}
            
        except HTTPException:
            # Re-raise HTTPExceptions as-is
            raise
        except Exception as e:
            logger.error(f"Error renaming chat session {session_id} for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error renaming chat session: {str(e)}",
            )
    
    async def connect_database(self, 
                        user_id: str,
                        name: str,
                        description: Optional[str],
                        db_type: DatabaseType,
                        host: str,
                        port: int,
                        username: str,
                        password: str,
                        database: str,
                        db_schema: Optional[str],
                        ssl_enabled: bool,
                        connection_string: Optional[str]) -> Dict[str, Any]:
        """Connect to a database and store credentials securely.
        
        Args:
            user_id: The user ID who owns this database connection
            name: A friendly name for the database
            description: Optional description
            db_type: Type of database
            host: Database host
            port: Database port
            username: Database username
            password: Database password
            database: Database name
            db_schema: Schema name (for databases that support schemas)
            ssl_enabled: Whether SSL is enabled
            connection_string: Optional custom connection string
            
        Returns:
            Dict: Database connection information
            
        Raises:
            HTTPException: If connection fails
        """
        try:
            # Create credentials
            credentials = DatabaseCredentials(
                host=host,
                port=port,
                username=username,
                password=password,
                database=database,
                db_schema=db_schema,
                ssl_enabled=ssl_enabled
            )
            
            # Create database object for testing
            db_id = f"db_{uuid.uuid4().hex[:8]}"
            db = Database(
                id=db_id,
                user_id=user_id,
                name=name,
                description=description,
                db_type=db_type,
                credentials=credentials,
                connection_string=connection_string
            )
            
            # Test the connection
            success, error = await self.database_service.connect_database(db)
            
            if not success:
                raise HTTPException(status_code=400, detail=f"Failed to connect to database: {error}")
                
            # Count tables
            table_count = len(await self.database_service.list_tables(db_id, schema=db_schema))
            
            # Disconnect test connection
            self.database_service.disconnect_database(db_id)
            
            # Store credentials securely (encrypted)
            await self.credential_service.store_credentials(
                user_id=user_id,
                database_id=db_id,
                name=name,
                description=description,
                db_type=db_type,
                credentials=credentials,
                connection_string=connection_string
            )
            
            # Clear any cached orchestrators since database list changed
            self._orchestrator_cache.clear()
            
            return {
                "id": db_id,
                "name": name,
                "type": db_type.value,
                "message": "Successfully connected to database",
                "table_count": table_count
            }
            
        except Exception as e:
            logger.error(f"Error connecting to database: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error connecting to database: {str(e)}")
    
    async def list_databases(self, user_id: str) -> List[Dict[str, Any]]:
        """List connected databases for a user.
        
        Args:
            user_id: The user ID
            
        Returns:
            List[Dict]: List of user's databases (without credentials)
        """
        return await self.credential_service.list_user_databases(user_id)
    
    async def disconnect_database(self, user_id: str, db_id: str) -> Dict[str, str]:
        """Disconnect from a database and remove stored credentials.
        
        Args:
            user_id: The user ID
            db_id: The ID of the database to disconnect
            
        Returns:
            Dict: A message
            
        Raises:
            HTTPException: If the database is not found
        """
        try:
            # Delete the stored credentials
            success = await self.credential_service.delete_credentials(user_id, db_id)
            
            if not success:
                raise HTTPException(status_code=404, detail="Database not found")
            
            # Clear orchestrator cache since database list changed
            self._orchestrator_cache.clear()
            
            return {"message": "Database disconnected successfully"}
            
        except Exception as e:
            logger.error(f"Error disconnecting database: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error disconnecting database: {str(e)}")
    
    async def _get_user_databases(self, user_id: str) -> List[Database]:
        """Get all databases for a user from secure storage."""
        database_list = await self.credential_service.list_user_databases(user_id)
        databases = []
        
        for db_info in database_list:
            db = await self.credential_service.get_credentials(user_id, db_info["id"])
            if db:
                databases.append(db)
        
        return databases
    
    async def _get_orchestrator(self, user_id: str) -> OrchestratorAgent:
        """Get orchestrator for user's databases with connection validation."""
        # Check cache first
        if user_id in self._orchestrator_cache:
            orchestrator = self._orchestrator_cache[user_id]
            # Validate that the orchestrator's database connections are still valid
            if await self._validate_orchestrator_connections(orchestrator):
                return orchestrator
            else:
                # Remove invalid orchestrator from cache
                del self._orchestrator_cache[user_id]

        # Get user's databases
        databases = await self._get_user_databases(user_id)

        if not databases:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No databases connected",
            )

        # Create and initialize orchestrator
        orchestrator = OrchestratorAgent(databases)
        await orchestrator.initialize()

        # Cache it (but not the credentials - those are only in the Database objects temporarily)
        self._orchestrator_cache[user_id] = orchestrator

        return orchestrator

    async def _validate_orchestrator_connections(self, orchestrator: OrchestratorAgent) -> bool:
        """Validate that all database connections in the orchestrator are still valid."""
        try:
            # Check if orchestrator has database service
            if hasattr(orchestrator, 'database_service'):
                database_service = orchestrator.database_service
                # Get all connected database IDs
                connected_db_ids = list(database_service.connections.keys())

                # Validate each connection
                for db_id in connected_db_ids:
                    if not database_service.is_connection_valid(db_id):
                        logger.warning(f"Invalid connection found for database {db_id}")
                        return False

                return True
            else:
                # If no database service, assume invalid
                return False
        except Exception as e:
            logger.error(f"Error validating orchestrator connections: {e}")
            return False

    async def ensure_database_connections(self, user_id: str, database_ids: List[str]) -> None:
        """Ensure database connections are valid, reconnecting if necessary."""
        databases = await self._get_user_databases(user_id)
        db_dict = {db.id: db for db in databases}

        for db_id in database_ids:
            if db_id in db_dict:
                database = db_dict[db_id]
                success = await self.database_service.ensure_connection(database)
                if not success:
                    logger.error(f"Failed to ensure connection for database {db_id}")
                    # Clear orchestrator cache to force recreation
                    if user_id in self._orchestrator_cache:
                        del self._orchestrator_cache[user_id]

    # ────────────────────────────────────────────────────────────
    # PUBLIC  ▸  main entry from /api/query/ask
    # ────────────────────────────────────────────────────────────
    async def query_databases(
        self,
        *,
        query: str,
        output_format: str,
        user_id: str,
        session_id: str | None,
        target_databases: Optional[List[str]],
        target_tables: Optional[Dict[str, List[str]]],
        target_columns: Optional[Dict[str, Dict[str, List[str]]]],
    ) -> Dict[str, Any]:

        # 1 ▸ create session_id **only if the user actually asked something**     
        history = []
        if not session_id:
            session_id = f"report_sess_{uuid.uuid4().hex[:8]}"
            logger.info("Started new report chat %s for user %s", session_id, user_id)
        else:
            history = await self.conversation_service.get_history(user_id, session_id, limit=20)

        # 3 ▸ run orchestrator
        orchestrator = await self._get_orchestrator(user_id)

        if target_databases:
            # Get user's database list to check against
            user_dbs = await self.credential_service.list_user_databases(user_id)
            user_db_ids = [db["id"] for db in user_dbs]
            unknown = [d for d in target_databases if d not in user_db_ids]
            if unknown:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unknown DB id(s): {', '.join(unknown)}",
                )

        orches_msg = {
            "query": query,
            "output_format": output_format,
            "conversation_history": history,
            "target_databases": target_databases,
            "target_tables": target_tables,
            "target_columns": target_columns,
            "user_id": user_id,
            "session_id": session_id,
        }

        try:
            orch_resp = await orchestrator.process(orches_msg)
        except Exception as exc:
            logger.exception("Orchestrator failed")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Orchestrator error: {exc}",
            ) from exc

        data       = orch_resp.get("data", {})
        has_info   = orch_resp.get("has_relevant_info", False)
        assistant_msg = (
            data.get("summary")
            or data.get("message")
            or "Done."
        )

        # 4 ▸ persist the (Q, A) pair
        await self.conversation_service.append_pair(
            user_id=user_id,
            session_id=session_id,
            user_msg=query,
            assistant_msg=assistant_msg,
        )

        # 5 ▸ build simple frontend-friendly response
        if not has_info:
            return build_simple_question_response(
                markdown=data.get("message", "I couldn't find any relevant data for your query. This might be because the data doesn't exist in your connected databases, or it might be stored under different terms. Try rephrasing your question or check if the relevant database is connected."),
                session_id=session_id,
                query=query,
                has_data=False,
                success=True,
            )

        return build_simple_question_response(
            markdown=assistant_msg,
            session_id=session_id,
            query=query,
            has_data=True,
            success=True,
        )

    # ────────────────────────────────────────────────────────────
    # STREAMING  ▸  main entry for streaming question responses
    # ────────────────────────────────────────────────────────────
    async def query_databases_streaming(
        self,
        *,
        query: str,
        output_format: str,
        user_id: str,
        session_id: str | None,
        target_databases: Optional[List[str]],
        target_tables: Optional[Dict[str, List[str]]],
        target_columns: Optional[Dict[str, Dict[str, List[str]]]],
    ) -> StreamingResponse:
        """
        Streaming version of query_databases that returns Server-Sent Events (SSE).
        Provides real-time token streaming for the final answer generation.
        """
        return StreamingResponse(
            self._stream_question_response(
                query=query,
                output_format=output_format,
                user_id=user_id,
                session_id=session_id,
                target_databases=target_databases,
                target_tables=target_tables,
                target_columns=target_columns,
            ),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",  # Disable nginx buffering
            }
        )

    async def _stream_question_response(
        self,
        *,
        query: str,
        output_format: str,
        user_id: str,
        session_id: str | None,
        target_databases: Optional[List[str]],
        target_tables: Optional[Dict[str, List[str]]],
        target_columns: Optional[Dict[str, Dict[str, List[str]]]],
    ) -> AsyncGenerator[str, None]:
        """
        Internal streaming generator for question responses.
        """
        def create_event(event_type: str, agent: str, data: Any) -> str:
            event = {
                "type": event_type,
                "agent": agent,
                "data": data,
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }
            return f"data: {json.dumps(event)}\n\n"

        try:
            # 1 ▸ session bookkeeping
            history = []
            if not session_id:
                session_id = f"question_sess_{uuid.uuid4().hex[:8]}"
                logger.info("Started new question chat %s for user %s", session_id, user_id)
            else:
                history = await self.conversation_service.get_history(user_id, session_id, limit=20)

            # 2 ▸ get orchestrator
            yield create_event("agent_status", "orchestrator", {"message": "🎯 Initializing question processing...", "status": "starting"})

            orchestrator = await self._get_orchestrator(user_id)

            if target_databases:
                # Get user's database list to check against
                user_dbs = await self.credential_service.list_user_databases(user_id)
                user_db_ids = [db["id"] for db in user_dbs]
                unknown = [d for d in target_databases if d not in user_db_ids]
                if unknown:
                    error_msg = f"Unknown DB id(s): {', '.join(unknown)}"
                    yield create_event("error", "orchestrator", {"message": error_msg, "status": "failed"})
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=error_msg,
                    )

            # 3 ▸ prepare orchestrator message
            orches_msg = {
                "query": query,
                "output_format": output_format,
                "conversation_history": history,
                "target_databases": target_databases,
                "target_tables": target_tables,
                "target_columns": target_columns,
                "user_id": user_id,
                "session_id": session_id,
                "enable_token_streaming": True,  # Enable streaming for final response
            }

            # 4 ▸ process with streaming
            yield create_event("agent_status", "orchestrator", {"message": "🔍 Processing your question...", "status": "processing"})

            try:
                # Check if orchestrator supports streaming
                if hasattr(orchestrator, 'process_stream'):
                    # Stream the orchestrator response and capture final answer
                    final_answer = ""
                    session_id_final = session_id

                    async for event in orchestrator.process_stream(orches_msg):
                        # Handle token stream events with clean token extraction
                        if event.get("type") == "token_stream":
                            # Extract clean token from the event data
                            token_data = event.get("data", {})
                            clean_token = token_data.get("token", "")

                            # Create simplified token event for frontend
                            simplified_event = {
                                "type": "token_stream",
                                "token": clean_token,  # Just the clean token string
                                "timestamp": event.get("timestamp", datetime.utcnow().isoformat() + "Z")
                            }
                            yield f"data: {json.dumps(simplified_event)}\n\n"
                            # Note: Delay is handled in the output agent
                        else:
                            # Send other events as-is
                            yield f"data: {json.dumps(event)}\n\n"

                        # Capture the conversational answer for persistence
                        if event.get("type") == "conversation_complete":
                            # Extract clean message from conversation_complete event
                            final_answer = event.get("data", {}).get("message", "")
                            if not final_answer:
                                # Fallback to response data
                                response_data = event.get("data", {}).get("response", {})
                                final_answer = response_data.get("answer", "")
                        elif event.get("type") == "token_complete":
                            complete_response = event.get("data", {}).get("complete_response", "")
                            if complete_response and not final_answer:
                                # Try to parse JSON and extract summary
                                try:
                                    import json as json_lib
                                    parsed = json_lib.loads(complete_response)
                                    if isinstance(parsed, dict) and "summary" in parsed:
                                        final_answer = parsed["summary"]
                                    else:
                                        final_answer = complete_response
                                except:
                                    final_answer = complete_response

                    # Persist the conversation
                    if final_answer:
                        await self.conversation_service.append_pair(
                            user_id=user_id,
                            session_id=session_id_final,
                            user_msg=query,
                            assistant_msg=final_answer,
                        )
                else:
                    # Fallback to non-streaming with final result
                    orch_resp = await orchestrator.process(orches_msg)

                    data = orch_resp.get("data", {})
                    has_info = orch_resp.get("has_relevant_info", False)
                    assistant_msg = (
                        data.get("summary")
                        or data.get("message")
                        or "Done."
                    )

                    # Send final result as streaming event
                    if has_info:
                        yield create_event("agent_result", "orchestrator", {
                            "query": data.get("query", query),
                            "session_id": session_id,
                            "summary": data.get("summary"),
                            "output_files": data.get("output_files"),
                            "errors": data.get("errors"),
                            "sql_queries": data.get("sql_queries"),
                        })
                    else:
                        yield create_event("agent_result", "orchestrator", {
                            "query": query,
                            "session_id": session_id,
                            "message": data.get("message", "No relevant data found"),
                        })

                    # 5 ▸ persist the (Q, A) pair
                    await self.conversation_service.append_pair(
                        user_id=user_id,
                        session_id=session_id,
                        user_msg=query,
                        assistant_msg=assistant_msg,
                    )

            except HTTPException:
                raise
            except Exception as exc:
                logger.exception("Orchestrator failed during streaming")
                yield create_event("error", "orchestrator", {"message": f"❌ Processing error: {exc}", "status": "failed"})
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Orchestrator error: {exc}",
                ) from exc

        except Exception as e:
            # Send error event in SSE format
            error_event = {
                "type": "error",
                "agent": "system",
                "data": {"message": f"❌ Unexpected error: {str(e)}", "status": "failed"},
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }
            yield f"data: {json.dumps(error_event)}\n\n"
            raise

    async def get_database_schema(self, user_id: str) -> Dict[str, Any]:
        """Get schema information for all connected databases for a user.
        
        Args:
            user_id: The user ID
        
        Returns:
            Dict: A nested dictionary containing databases, tables, and columns
            
        Raises:
            HTTPException: If an error occurs during schema retrieval
        """
        user_databases = await self._get_user_databases(user_id)
        
        if not user_databases:
            return {"databases": []}
            
        try:
            result = {"databases": []}
            
            for db in user_databases:
                db_info = {
                    "id": db.id,
                    "name": db.name,
                    "type": db.db_type.value,
                    "tables": []
                }
                
                # Ensure database is connected before listing tables
                try:
                    # Reconnect to the database
                    success, error = await self.database_service.connect_database(db)
                    if not success:
                        logger.warning(f"Could not connect to database {db.id}: {error}")
                        result["databases"].append(db_info)  # Add database with empty tables
                        continue
                        
                    # List tables
                    tables = await self.database_service.list_tables(db.id, schema=db.credentials.db_schema)
                    logger.info(f"Found {len(tables)} tables in database {db.id}")
                    
                    # Get columns for each table
                    for table_name in tables:
                        try:
                            table_metadata = await self.database_service.get_table_metadata(
                                db.id, 
                                table_name, 
                                schema=db.credentials.db_schema
                            )
                            
                            # Only collect column names
                            column_names = [column.name for column in table_metadata.columns]
                            
                            # Create simplified table info
                            table_info = {
                                "name": table_name,
                                "columns": column_names
                            }
                                
                            db_info["tables"].append(table_info)
                        except Exception as table_error:
                            logger.warning(f"Error getting metadata for table {table_name} in {db.id}: {str(table_error)}")
                except Exception as tables_error:
                    logger.warning(f"Error listing tables for {db.id}: {str(tables_error)}")
                finally:
                    # Always disconnect after we're done
                    try:
                        self.database_service.disconnect_database(db.id)
                    except Exception as disconnect_error:
                        logger.warning(f"Error disconnecting from database {db.id}: {str(disconnect_error)}")
                    
                result["databases"].append(db_info)
                
            return result
            
        except Exception as e:
            logger.error(f"Error retrieving database schema: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error retrieving database schema: {str(e)}")

    async def connect_database_with_error_handling(
        self,
        user_id: str,
        name: str,
        description: Optional[str],
        db_type: DatabaseType,
        host: str,
        port: int,
        username: str,
        password: str,
        database: str,
        db_schema: Optional[str],
        ssl_enabled: bool,
        connection_string: Optional[str]
    ) -> Dict[str, Any]:
        """Connect to a database and store credentials securely with full error handling.
        
        Returns:
            Dict: Database connection information
            
        Raises:
            HTTPException: For any connection or storage errors
        """
        try:
            result = await self.connect_database(
                user_id=user_id,
                name=name,
                description=description,
                db_type=db_type,
                host=host,
                port=port,
                username=username,
                password=password,
                database=database,
                db_schema=db_schema,
                ssl_enabled=ssl_enabled,
                connection_string=connection_string
            )
            return result
        except HTTPException:
            # Re-raise HTTPExceptions as-is
            raise
        except Exception as e:
            logger.error(f"Unexpected error in connect_database: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    async def list_databases_with_error_handling(self, user_id: str) -> List[Dict[str, Any]]:
        """List all connected databases for a user with error handling.
        
        Returns:
            List[Dict]: List of user's databases
            
        Raises:
            HTTPException: For any database access errors
        """
        try:
            databases = await self.list_databases(user_id)
            return databases
        except Exception as e:
            logger.error(f"Error listing databases: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    async def disconnect_database_with_error_handling(self, user_id: str, db_id: str) -> Dict[str, str]:
        """Disconnect from a database with error handling.
        
        Returns:
            Dict: Success message
            
        Raises:
            HTTPException: For any disconnection errors
        """
        try:
            result = await self.disconnect_database(user_id, db_id)
            return result
        except HTTPException:
            # Re-raise HTTPExceptions as-is
            raise
        except Exception as e:
            logger.error(f"Unexpected error in disconnect_database: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    async def get_database_schema_with_error_handling(self, user_id: str) -> Dict[str, Any]:
        """Get schema information for all connected databases with error handling.
        
        Returns:
            Dict: Database schema information
            
        Raises:
            HTTPException: For any schema retrieval errors
        """
        try:
            schema = await self.get_database_schema(user_id)
            return schema
        except HTTPException:
            # Re-raise HTTPExceptions as-is
            raise
        except Exception as e:
            logger.error(f"Unexpected error in get_database_schema: {str(e)}")
            raise HTTPException(status_code=500, detail="Internal server error") 
