(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{24265:(e,t,a)=>{"use strict";a.d(t,{default:()=>u});var s=a(95155);a(12115);var n=a(72922),r=a(26715),c=a(58189),l=a(74045),o=a(10071),i=a(84783);let d=new n.E,u=e=>{let{children:t}=e;return(0,s.jsx)(r.Ht,{client:d,children:(0,s.jsx)(c.K,{children:(0,s.jsx)(l.O,{children:(0,s.jsx)(i.M,{children:(0,s.jsx)(o._,{children:t})})})})})}},30347:()=>{},65511:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,98575,23)),Promise.resolve().then(a.t.bind(a,37367,23)),Promise.resolve().then(a.t.bind(a,30347,23)),Promise.resolve().then(a.bind(a,24265)),Promise.resolve().then(a.bind(a,10871)),Promise.resolve().then(a.bind(a,56543))},84783:(e,t,a)=>{"use strict";a.d(t,{G:()=>l,M:()=>o});var s=a(95155),n=a(12115),r=a(58189);let c=(0,n.createContext)(void 0),l=()=>{let e=(0,n.useContext)(c);if(!e)throw Error("useDataSources must be used within a DataSourcesProvider");return e},o=e=>{let{children:t}=e,{listDatabases:a,disconnectExistingDatabase:l}=(0,r.g)(),[o,i]=(0,n.useState)([]),[d,u]=(0,n.useState)(!1),[h,b]=(0,n.useState)(null),[m,v]=(0,n.useState)(!1),f=(0,n.useCallback)(e=>({id:e.id,name:e.name,type:"database",description:e.description||"".concat(e.type," database"),isConnected:!0}),[]),C=(0,n.useCallback)(async()=>{u(!0),b(null);try{console.log("Fetching connected databases...");let e=(await a()).map(f);i(e),v(!0)}catch(e){console.error("Failed to fetch connected databases:",e),b(e instanceof Error?e.message:"Failed to fetch connected databases"),i([]),v(!0)}finally{u(!1)}},[a,f]);(0,n.useEffect)(()=>{m||C()},[C,m]);let p=o.filter(e=>e.isConnected),k=(0,n.useCallback)(async e=>{console.log("Connect data source not implemented - use the Data Sources page"),b("Please use the Data Sources page to connect new databases")},[]),w=(0,n.useCallback)(async e=>{u(!0),b(null);try{await l(e),await C()}catch(e){b(e instanceof Error?e.message:"Failed to disconnect data source"),u(!1)}},[l,C]),y=(0,n.useCallback)(e=>o.find(t=>t.id===e),[o]),P=(0,n.useCallback)(async()=>{await C()},[C]);return(0,s.jsx)(c.Provider,{value:{dataSources:o,connectedDataSources:p,isLoading:d,error:h,connectDataSource:k,disconnectDataSource:w,getDataSource:y,refreshDataSources:P},children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[585,690,464,231,189,45,179,441,684,358],()=>t(65511)),_N_E=e.O()}]);