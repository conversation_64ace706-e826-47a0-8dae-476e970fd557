"""
Connection Pool Manager Service

This service manages user-session-aware database connection pools,
integrating with JWT token validation and providing automatic cleanup
for expired or revoked tokens.
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass

from app.services.database_service import DatabaseService
from app.models.database import Database
from app.utils.security import get_token_data, JWTError
from app.utils.db import get_db
from app.models.user import User as UserDB

logger = logging.getLogger(__name__)


@dataclass
class UserSession:
    """Information about a user session."""
    user_id: str
    token_hash: str
    expires_at: datetime
    last_activity: datetime
    database_pools: Set[str]  # Set of database IDs with active pools
    
    def is_expired(self) -> bool:
        """Check if the session is expired."""
        return datetime.utcnow() > self.expires_at
    
    def update_activity(self):
        """Update last activity timestamp."""
        self.last_activity = datetime.utcnow()


class ConnectionPoolManager:
    """Manages user-session-aware database connection pools."""
    
    def __init__(self, database_service: DatabaseService):
        """Initialize the connection pool manager.
        
        Args:
            database_service: The database service to manage pools for
        """
        self.database_service = database_service
        self.active_sessions: Dict[str, UserSession] = {}  # token_hash -> session
        self.user_tokens: Dict[str, Set[str]] = {}  # user_id -> set of token_hashes
        self.cleanup_lock = threading.Lock()
        
        # Configuration
        self.SESSION_CLEANUP_INTERVAL = timedelta(minutes=5)
        self.TOKEN_VALIDATION_INTERVAL = timedelta(minutes=10)
        
        # Start background tasks
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """Start background cleanup and validation tasks."""
        def session_cleanup_worker():
            while True:
                try:
                    time.sleep(300)  # Check every 5 minutes
                    self._cleanup_expired_sessions()
                except Exception as e:
                    logger.error(f"Error in session cleanup: {e}")
        
        def token_validation_worker():
            while True:
                try:
                    time.sleep(600)  # Check every 10 minutes
                    asyncio.run(self._validate_active_tokens())
                except Exception as e:
                    logger.error(f"Error in token validation: {e}")
        
        # Start daemon threads
        cleanup_thread = threading.Thread(target=session_cleanup_worker, daemon=True)
        cleanup_thread.start()
        
        validation_thread = threading.Thread(target=token_validation_worker, daemon=True)
        validation_thread.start()
        
        logger.info("Started connection pool manager background tasks")
    
    async def get_user_database_connection(
        self, 
        token: str, 
        database: Database
    ) -> tuple[bool, Optional[str], Optional[Any]]:
        """Get a database connection for an authenticated user.
        
        Args:
            token: JWT token for authentication
            database: Database to connect to
            
        Returns:
            Tuple of (success, error_message, connection_pool)
        """
        try:
            # Validate token and get user info
            token_data = get_token_data(token)
            user_id = token_data.user_id
            
            # Create token hash for session tracking
            import hashlib
            token_hash = hashlib.sha256(token.encode()).hexdigest()
            
            # Update or create session
            await self._update_user_session(token, token_hash, user_id, database.id)
            
            # Get connection pool from database service
            success, error, pool = await self.database_service.get_user_connection_pool(
                user_id, database
            )
            
            if success:
                logger.debug(f"Provided database connection for user {user_id}, database {database.id}")
                return True, None, pool
            else:
                logger.error(f"Failed to get connection pool for user {user_id}, database {database.id}: {error}")
                return False, error, None
                
        except JWTError as e:
            logger.warning(f"Invalid JWT token: {e}")
            return False, "Invalid authentication token", None
        except Exception as e:
            logger.error(f"Error getting user database connection: {e}")
            return False, str(e), None
    
    async def _update_user_session(
        self, 
        token: str, 
        token_hash: str, 
        user_id: str, 
        database_id: str
    ):
        """Update or create a user session.
        
        Args:
            token: JWT token
            token_hash: Hash of the token
            user_id: User ID
            database_id: Database ID being accessed
        """
        with self.cleanup_lock:
            # Get token expiration
            token_data = get_token_data(token)
            expires_at = datetime.utcfromtimestamp(token_data.exp) if token_data.exp else datetime.utcnow() + timedelta(hours=1)
            
            if token_hash in self.active_sessions:
                # Update existing session
                session = self.active_sessions[token_hash]
                session.update_activity()
                session.database_pools.add(database_id)
            else:
                # Create new session
                session = UserSession(
                    user_id=user_id,
                    token_hash=token_hash,
                    expires_at=expires_at,
                    last_activity=datetime.utcnow(),
                    database_pools={database_id}
                )
                self.active_sessions[token_hash] = session
                
                # Track user tokens
                if user_id not in self.user_tokens:
                    self.user_tokens[user_id] = set()
                self.user_tokens[user_id].add(token_hash)
                
                logger.debug(f"Created new session for user {user_id}")
    
    def _cleanup_expired_sessions(self):
        """Clean up expired user sessions and their connection pools."""
        current_time = datetime.utcnow()
        expired_sessions = []
        
        with self.cleanup_lock:
            # Find expired sessions
            for token_hash, session in self.active_sessions.items():
                if session.is_expired():
                    expired_sessions.append((token_hash, session))
            
            # Clean up expired sessions
            for token_hash, session in expired_sessions:
                logger.info(f"Cleaning up expired session for user {session.user_id}")
                
                # Remove from active sessions
                del self.active_sessions[token_hash]
                
                # Remove from user tokens
                if session.user_id in self.user_tokens:
                    self.user_tokens[session.user_id].discard(token_hash)
                    if not self.user_tokens[session.user_id]:
                        del self.user_tokens[session.user_id]
                
                # Clean up database connection pools
                self.database_service.cleanup_user_pools(session.user_id, "session_expired")
    
    async def _validate_active_tokens(self):
        """Validate active tokens against the database."""
        try:
            db = next(get_db())
            invalid_sessions = []
            
            with self.cleanup_lock:
                for token_hash, session in self.active_sessions.items():
                    try:
                        # Check if user still exists and is active
                        user = db.query(UserDB).filter(UserDB.id == session.user_id).first()
                        if not user or not user.is_active:
                            invalid_sessions.append((token_hash, session))
                            continue
                            
                        # Additional token validation could be added here
                        # (e.g., check token blacklist, refresh token validity, etc.)
                        
                    except Exception as e:
                        logger.error(f"Error validating session for user {session.user_id}: {e}")
                        invalid_sessions.append((token_hash, session))
            
            # Clean up invalid sessions
            for token_hash, session in invalid_sessions:
                logger.info(f"Cleaning up invalid session for user {session.user_id}")
                
                with self.cleanup_lock:
                    # Remove from active sessions
                    self.active_sessions.pop(token_hash, None)
                    
                    # Remove from user tokens
                    if session.user_id in self.user_tokens:
                        self.user_tokens[session.user_id].discard(token_hash)
                        if not self.user_tokens[session.user_id]:
                            del self.user_tokens[session.user_id]
                
                # Clean up database connection pools
                self.database_service.cleanup_user_pools(session.user_id, "invalid_session")
                
        except Exception as e:
            logger.error(f"Error in token validation: {e}")
    
    def cleanup_user_session(self, user_id: str, reason: str = "manual_cleanup"):
        """Manually clean up all sessions for a specific user.
        
        Args:
            user_id: User ID to clean up
            reason: Reason for cleanup
        """
        with self.cleanup_lock:
            if user_id in self.user_tokens:
                token_hashes = list(self.user_tokens[user_id])
                
                logger.info(f"Cleaning up {len(token_hashes)} sessions for user {user_id} ({reason})")
                
                # Remove all sessions for this user
                for token_hash in token_hashes:
                    self.active_sessions.pop(token_hash, None)
                
                # Remove user from tracking
                del self.user_tokens[user_id]
        
        # Clean up database connection pools
        self.database_service.cleanup_user_pools(user_id, reason)
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get statistics about active sessions.
        
        Returns:
            Dictionary with session statistics
        """
        with self.cleanup_lock:
            total_sessions = len(self.active_sessions)
            total_users = len(self.user_tokens)
            
            # Calculate session distribution
            user_session_counts = {
                user_id: len(token_hashes) 
                for user_id, token_hashes in self.user_tokens.items()
            }
            
            return {
                "total_sessions": total_sessions,
                "total_users": total_users,
                "user_session_counts": user_session_counts,
                "database_pool_stats": self.database_service.get_user_pool_stats()
            }


# Global instance
_connection_pool_manager: Optional[ConnectionPoolManager] = None


def get_connection_pool_manager() -> ConnectionPoolManager:
    """Get the global connection pool manager instance."""
    global _connection_pool_manager
    if _connection_pool_manager is None:
        from app.services.database_service import DatabaseService
        database_service = DatabaseService()
        _connection_pool_manager = ConnectionPoolManager(database_service)
    return _connection_pool_manager
