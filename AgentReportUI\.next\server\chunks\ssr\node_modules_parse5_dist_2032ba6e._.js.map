{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/common/unicode.js"], "sourcesContent": ["const UNDEFINED_CODE_POINTS = new Set([\n    65534, 65535, 131070, 131071, 196606, 196607, 262142, 262143, 327678, 327679, 393214,\n    393215, 458750, 458751, 524286, 524287, 589822, 589823, 655358, 655359, 720894,\n    720895, 786430, 786431, 851966, 851967, 917502, 917503, 983038, 983039, 1048574,\n    1048575, 1114110, 1114111,\n]);\nexport const REPLACEMENT_CHARACTER = '\\uFFFD';\nexport var CODE_POINTS;\n(function (CODE_POINTS) {\n    CODE_POINTS[CODE_POINTS[\"EOF\"] = -1] = \"EOF\";\n    CODE_POINTS[CODE_POINTS[\"NULL\"] = 0] = \"NULL\";\n    CODE_POINTS[CODE_POINTS[\"TABULATION\"] = 9] = \"TABULATION\";\n    CODE_POINTS[CODE_POINTS[\"CARRIAGE_RETURN\"] = 13] = \"CARRIAGE_RETURN\";\n    CODE_POINTS[CODE_POINTS[\"LINE_FEED\"] = 10] = \"LINE_FEED\";\n    CODE_POINTS[CODE_POINTS[\"FORM_FEED\"] = 12] = \"FORM_FEED\";\n    CODE_POINTS[CODE_POINTS[\"SPACE\"] = 32] = \"SPACE\";\n    CODE_POINTS[CODE_POINTS[\"EXCLAMATION_MARK\"] = 33] = \"EXCLAMATION_MARK\";\n    CODE_POINTS[CODE_POINTS[\"QUOTATION_MARK\"] = 34] = \"QUOTATION_MARK\";\n    CODE_POINTS[CODE_POINTS[\"AMPERSAND\"] = 38] = \"AMPERSAND\";\n    CODE_POINTS[CODE_POINTS[\"APOSTROPHE\"] = 39] = \"APOSTROPHE\";\n    CODE_POINTS[CODE_POINTS[\"HYPHEN_MINUS\"] = 45] = \"HYPHEN_MINUS\";\n    CODE_POINTS[CODE_POINTS[\"SOLIDUS\"] = 47] = \"SOLIDUS\";\n    CODE_POINTS[CODE_POINTS[\"DIGIT_0\"] = 48] = \"DIGIT_0\";\n    CODE_POINTS[CODE_POINTS[\"DIGIT_9\"] = 57] = \"DIGIT_9\";\n    CODE_POINTS[CODE_POINTS[\"SEMICOLON\"] = 59] = \"SEMICOLON\";\n    CODE_POINTS[CODE_POINTS[\"LESS_THAN_SIGN\"] = 60] = \"LESS_THAN_SIGN\";\n    CODE_POINTS[CODE_POINTS[\"EQUALS_SIGN\"] = 61] = \"EQUALS_SIGN\";\n    CODE_POINTS[CODE_POINTS[\"GREATER_THAN_SIGN\"] = 62] = \"GREATER_THAN_SIGN\";\n    CODE_POINTS[CODE_POINTS[\"QUESTION_MARK\"] = 63] = \"QUESTION_MARK\";\n    CODE_POINTS[CODE_POINTS[\"LATIN_CAPITAL_A\"] = 65] = \"LATIN_CAPITAL_A\";\n    CODE_POINTS[CODE_POINTS[\"LATIN_CAPITAL_Z\"] = 90] = \"LATIN_CAPITAL_Z\";\n    CODE_POINTS[CODE_POINTS[\"RIGHT_SQUARE_BRACKET\"] = 93] = \"RIGHT_SQUARE_BRACKET\";\n    CODE_POINTS[CODE_POINTS[\"GRAVE_ACCENT\"] = 96] = \"GRAVE_ACCENT\";\n    CODE_POINTS[CODE_POINTS[\"LATIN_SMALL_A\"] = 97] = \"LATIN_SMALL_A\";\n    CODE_POINTS[CODE_POINTS[\"LATIN_SMALL_Z\"] = 122] = \"LATIN_SMALL_Z\";\n})(CODE_POINTS || (CODE_POINTS = {}));\nexport const SEQUENCES = {\n    DASH_DASH: '--',\n    CDATA_START: '[CDATA[',\n    DOCTYPE: 'doctype',\n    SCRIPT: 'script',\n    PUBLIC: 'public',\n    SYSTEM: 'system',\n};\n//Surrogates\nexport function isSurrogate(cp) {\n    return cp >= 55296 && cp <= 57343;\n}\nexport function isSurrogatePair(cp) {\n    return cp >= 56320 && cp <= 57343;\n}\nexport function getSurrogatePairCodePoint(cp1, cp2) {\n    return (cp1 - 55296) * 1024 + 9216 + cp2;\n}\n//NOTE: excluding NULL and ASCII whitespace\nexport function isControlCodePoint(cp) {\n    return ((cp !== 0x20 && cp !== 0x0a && cp !== 0x0d && cp !== 0x09 && cp !== 0x0c && cp >= 0x01 && cp <= 0x1f) ||\n        (cp >= 0x7f && cp <= 0x9f));\n}\nexport function isUndefinedCodePoint(cp) {\n    return (cp >= 64976 && cp <= 65007) || UNDEFINED_CODE_POINTS.has(cp);\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA,MAAM,wBAAwB,IAAI,IAAI;IAClC;IAAO;IAAO;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAC9E;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxE;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxE;IAAS;IAAS;CACrB;AACM,MAAM,wBAAwB;AAC9B,IAAI;AACX,CAAC,SAAU,WAAW;IAClB,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG;IACvC,WAAW,CAAC,WAAW,CAAC,OAAO,GAAG,EAAE,GAAG;IACvC,WAAW,CAAC,WAAW,CAAC,aAAa,GAAG,EAAE,GAAG;IAC7C,WAAW,CAAC,WAAW,CAAC,kBAAkB,GAAG,GAAG,GAAG;IACnD,WAAW,CAAC,WAAW,CAAC,YAAY,GAAG,GAAG,GAAG;IAC7C,WAAW,CAAC,WAAW,CAAC,YAAY,GAAG,GAAG,GAAG;IAC7C,WAAW,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG,GAAG;IACzC,WAAW,CAAC,WAAW,CAAC,mBAAmB,GAAG,GAAG,GAAG;IACpD,WAAW,CAAC,WAAW,CAAC,iBAAiB,GAAG,GAAG,GAAG;IAClD,WAAW,CAAC,WAAW,CAAC,YAAY,GAAG,GAAG,GAAG;IAC7C,WAAW,CAAC,WAAW,CAAC,aAAa,GAAG,GAAG,GAAG;IAC9C,WAAW,CAAC,WAAW,CAAC,eAAe,GAAG,GAAG,GAAG;IAChD,WAAW,CAAC,WAAW,CAAC,UAAU,GAAG,GAAG,GAAG;IAC3C,WAAW,CAAC,WAAW,CAAC,UAAU,GAAG,GAAG,GAAG;IAC3C,WAAW,CAAC,WAAW,CAAC,UAAU,GAAG,GAAG,GAAG;IAC3C,WAAW,CAAC,WAAW,CAAC,YAAY,GAAG,GAAG,GAAG;IAC7C,WAAW,CAAC,WAAW,CAAC,iBAAiB,GAAG,GAAG,GAAG;IAClD,WAAW,CAAC,WAAW,CAAC,cAAc,GAAG,GAAG,GAAG;IAC/C,WAAW,CAAC,WAAW,CAAC,oBAAoB,GAAG,GAAG,GAAG;IACrD,WAAW,CAAC,WAAW,CAAC,gBAAgB,GAAG,GAAG,GAAG;IACjD,WAAW,CAAC,WAAW,CAAC,kBAAkB,GAAG,GAAG,GAAG;IACnD,WAAW,CAAC,WAAW,CAAC,kBAAkB,GAAG,GAAG,GAAG;IACnD,WAAW,CAAC,WAAW,CAAC,uBAAuB,GAAG,GAAG,GAAG;IACxD,WAAW,CAAC,WAAW,CAAC,eAAe,GAAG,GAAG,GAAG;IAChD,WAAW,CAAC,WAAW,CAAC,gBAAgB,GAAG,GAAG,GAAG;IACjD,WAAW,CAAC,WAAW,CAAC,gBAAgB,GAAG,IAAI,GAAG;AACtD,CAAC,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;AAC5B,MAAM,YAAY;IACrB,WAAW;IACX,aAAa;IACb,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,QAAQ;AACZ;AAEO,SAAS,YAAY,EAAE;IAC1B,OAAO,MAAM,SAAS,MAAM;AAChC;AACO,SAAS,gBAAgB,EAAE;IAC9B,OAAO,MAAM,SAAS,MAAM;AAChC;AACO,SAAS,0BAA0B,GAAG,EAAE,GAAG;IAC9C,OAAO,CAAC,MAAM,KAAK,IAAI,OAAO,OAAO;AACzC;AAEO,SAAS,mBAAmB,EAAE;IACjC,OAAQ,AAAC,OAAO,QAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,QAAQ,MAAM,QAAQ,MAAM,QACnG,MAAM,QAAQ,MAAM;AAC7B;AACO,SAAS,qBAAqB,EAAE;IACnC,OAAO,AAAC,MAAM,SAAS,MAAM,SAAU,sBAAsB,GAAG,CAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/common/error-codes.js"], "sourcesContent": ["export var ERR;\n(function (ERR) {\n    ERR[\"controlCharacterInInputStream\"] = \"control-character-in-input-stream\";\n    ERR[\"noncharacterInInputStream\"] = \"noncharacter-in-input-stream\";\n    ERR[\"surrogateInInputStream\"] = \"surrogate-in-input-stream\";\n    ERR[\"nonVoidHtmlElementStartTagWithTrailingSolidus\"] = \"non-void-html-element-start-tag-with-trailing-solidus\";\n    ERR[\"endTagWithAttributes\"] = \"end-tag-with-attributes\";\n    ERR[\"endTagWithTrailingSolidus\"] = \"end-tag-with-trailing-solidus\";\n    ERR[\"unexpectedSolidusInTag\"] = \"unexpected-solidus-in-tag\";\n    ERR[\"unexpectedNullCharacter\"] = \"unexpected-null-character\";\n    ERR[\"unexpectedQuestionMarkInsteadOfTagName\"] = \"unexpected-question-mark-instead-of-tag-name\";\n    ERR[\"invalidFirstCharacterOfTagName\"] = \"invalid-first-character-of-tag-name\";\n    ERR[\"unexpectedEqualsSignBeforeAttributeName\"] = \"unexpected-equals-sign-before-attribute-name\";\n    ERR[\"missingEndTagName\"] = \"missing-end-tag-name\";\n    ERR[\"unexpectedCharacterInAttributeName\"] = \"unexpected-character-in-attribute-name\";\n    ERR[\"unknownNamedCharacterReference\"] = \"unknown-named-character-reference\";\n    ERR[\"missingSemicolonAfterCharacterReference\"] = \"missing-semicolon-after-character-reference\";\n    ERR[\"unexpectedCharacterAfterDoctypeSystemIdentifier\"] = \"unexpected-character-after-doctype-system-identifier\";\n    ERR[\"unexpectedCharacterInUnquotedAttributeValue\"] = \"unexpected-character-in-unquoted-attribute-value\";\n    ERR[\"eofBeforeTagName\"] = \"eof-before-tag-name\";\n    ERR[\"eofInTag\"] = \"eof-in-tag\";\n    ERR[\"missingAttributeValue\"] = \"missing-attribute-value\";\n    ERR[\"missingWhitespaceBetweenAttributes\"] = \"missing-whitespace-between-attributes\";\n    ERR[\"missingWhitespaceAfterDoctypePublicKeyword\"] = \"missing-whitespace-after-doctype-public-keyword\";\n    ERR[\"missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers\"] = \"missing-whitespace-between-doctype-public-and-system-identifiers\";\n    ERR[\"missingWhitespaceAfterDoctypeSystemKeyword\"] = \"missing-whitespace-after-doctype-system-keyword\";\n    ERR[\"missingQuoteBeforeDoctypePublicIdentifier\"] = \"missing-quote-before-doctype-public-identifier\";\n    ERR[\"missingQuoteBeforeDoctypeSystemIdentifier\"] = \"missing-quote-before-doctype-system-identifier\";\n    ERR[\"missingDoctypePublicIdentifier\"] = \"missing-doctype-public-identifier\";\n    ERR[\"missingDoctypeSystemIdentifier\"] = \"missing-doctype-system-identifier\";\n    ERR[\"abruptDoctypePublicIdentifier\"] = \"abrupt-doctype-public-identifier\";\n    ERR[\"abruptDoctypeSystemIdentifier\"] = \"abrupt-doctype-system-identifier\";\n    ERR[\"cdataInHtmlContent\"] = \"cdata-in-html-content\";\n    ERR[\"incorrectlyOpenedComment\"] = \"incorrectly-opened-comment\";\n    ERR[\"eofInScriptHtmlCommentLikeText\"] = \"eof-in-script-html-comment-like-text\";\n    ERR[\"eofInDoctype\"] = \"eof-in-doctype\";\n    ERR[\"nestedComment\"] = \"nested-comment\";\n    ERR[\"abruptClosingOfEmptyComment\"] = \"abrupt-closing-of-empty-comment\";\n    ERR[\"eofInComment\"] = \"eof-in-comment\";\n    ERR[\"incorrectlyClosedComment\"] = \"incorrectly-closed-comment\";\n    ERR[\"eofInCdata\"] = \"eof-in-cdata\";\n    ERR[\"absenceOfDigitsInNumericCharacterReference\"] = \"absence-of-digits-in-numeric-character-reference\";\n    ERR[\"nullCharacterReference\"] = \"null-character-reference\";\n    ERR[\"surrogateCharacterReference\"] = \"surrogate-character-reference\";\n    ERR[\"characterReferenceOutsideUnicodeRange\"] = \"character-reference-outside-unicode-range\";\n    ERR[\"controlCharacterReference\"] = \"control-character-reference\";\n    ERR[\"noncharacterCharacterReference\"] = \"noncharacter-character-reference\";\n    ERR[\"missingWhitespaceBeforeDoctypeName\"] = \"missing-whitespace-before-doctype-name\";\n    ERR[\"missingDoctypeName\"] = \"missing-doctype-name\";\n    ERR[\"invalidCharacterSequenceAfterDoctypeName\"] = \"invalid-character-sequence-after-doctype-name\";\n    ERR[\"duplicateAttribute\"] = \"duplicate-attribute\";\n    ERR[\"nonConformingDoctype\"] = \"non-conforming-doctype\";\n    ERR[\"missingDoctype\"] = \"missing-doctype\";\n    ERR[\"misplacedDoctype\"] = \"misplaced-doctype\";\n    ERR[\"endTagWithoutMatchingOpenElement\"] = \"end-tag-without-matching-open-element\";\n    ERR[\"closingOfElementWithOpenChildElements\"] = \"closing-of-element-with-open-child-elements\";\n    ERR[\"disallowedContentInNoscriptInHead\"] = \"disallowed-content-in-noscript-in-head\";\n    ERR[\"openElementsLeftAfterEof\"] = \"open-elements-left-after-eof\";\n    ERR[\"abandonedHeadElementChild\"] = \"abandoned-head-element-child\";\n    ERR[\"misplacedStartTagForHeadElement\"] = \"misplaced-start-tag-for-head-element\";\n    ERR[\"nestedNoscriptInHead\"] = \"nested-noscript-in-head\";\n    ERR[\"eofInElementThatCanContainOnlyText\"] = \"eof-in-element-that-can-contain-only-text\";\n})(ERR || (ERR = {}));\n"], "names": [], "mappings": ";;;AAAO,IAAI;AACX,CAAC,SAAU,GAAG;IACV,GAAG,CAAC,gCAAgC,GAAG;IACvC,GAAG,CAAC,4BAA4B,GAAG;IACnC,GAAG,CAAC,yBAAyB,GAAG;IAChC,GAAG,CAAC,gDAAgD,GAAG;IACvD,GAAG,CAAC,uBAAuB,GAAG;IAC9B,GAAG,CAAC,4BAA4B,GAAG;IACnC,GAAG,CAAC,yBAAyB,GAAG;IAChC,GAAG,CAAC,0BAA0B,GAAG;IACjC,GAAG,CAAC,yCAAyC,GAAG;IAChD,GAAG,CAAC,iCAAiC,GAAG;IACxC,GAAG,CAAC,0CAA0C,GAAG;IACjD,GAAG,CAAC,oBAAoB,GAAG;IAC3B,GAAG,CAAC,qCAAqC,GAAG;IAC5C,GAAG,CAAC,iCAAiC,GAAG;IACxC,GAAG,CAAC,0CAA0C,GAAG;IACjD,GAAG,CAAC,kDAAkD,GAAG;IACzD,GAAG,CAAC,8CAA8C,GAAG;IACrD,GAAG,CAAC,mBAAmB,GAAG;IAC1B,GAAG,CAAC,WAAW,GAAG;IAClB,GAAG,CAAC,wBAAwB,GAAG;IAC/B,GAAG,CAAC,qCAAqC,GAAG;IAC5C,GAAG,CAAC,6CAA6C,GAAG;IACpD,GAAG,CAAC,4DAA4D,GAAG;IACnE,GAAG,CAAC,6CAA6C,GAAG;IACpD,GAAG,CAAC,4CAA4C,GAAG;IACnD,GAAG,CAAC,4CAA4C,GAAG;IACnD,GAAG,CAAC,iCAAiC,GAAG;IACxC,GAAG,CAAC,iCAAiC,GAAG;IACxC,GAAG,CAAC,gCAAgC,GAAG;IACvC,GAAG,CAAC,gCAAgC,GAAG;IACvC,GAAG,CAAC,qBAAqB,GAAG;IAC5B,GAAG,CAAC,2BAA2B,GAAG;IAClC,GAAG,CAAC,iCAAiC,GAAG;IACxC,GAAG,CAAC,eAAe,GAAG;IACtB,GAAG,CAAC,gBAAgB,GAAG;IACvB,GAAG,CAAC,8BAA8B,GAAG;IACrC,GAAG,CAAC,eAAe,GAAG;IACtB,GAAG,CAAC,2BAA2B,GAAG;IAClC,GAAG,CAAC,aAAa,GAAG;IACpB,GAAG,CAAC,6CAA6C,GAAG;IACpD,GAAG,CAAC,yBAAyB,GAAG;IAChC,GAAG,CAAC,8BAA8B,GAAG;IACrC,GAAG,CAAC,wCAAwC,GAAG;IAC/C,GAAG,CAAC,4BAA4B,GAAG;IACnC,GAAG,CAAC,iCAAiC,GAAG;IACxC,GAAG,CAAC,qCAAqC,GAAG;IAC5C,GAAG,CAAC,qBAAqB,GAAG;IAC5B,GAAG,CAAC,2CAA2C,GAAG;IAClD,GAAG,CAAC,qBAAqB,GAAG;IAC5B,GAAG,CAAC,uBAAuB,GAAG;IAC9B,GAAG,CAAC,iBAAiB,GAAG;IACxB,GAAG,CAAC,mBAAmB,GAAG;IAC1B,GAAG,CAAC,mCAAmC,GAAG;IAC1C,GAAG,CAAC,wCAAwC,GAAG;IAC/C,GAAG,CAAC,oCAAoC,GAAG;IAC3C,GAAG,CAAC,2BAA2B,GAAG;IAClC,GAAG,CAAC,4BAA4B,GAAG;IACnC,GAAG,CAAC,kCAAkC,GAAG;IACzC,GAAG,CAAC,uBAAuB,GAAG;IAC9B,GAAG,CAAC,qCAAqC,GAAG;AAChD,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/tokenizer/preprocessor.js"], "sourcesContent": ["import { CODE_POINTS as $, getSurrogatePairCodePoint, isControlCodePoint, isSurrogate, isSurrogatePair, isUndefinedCodePoint, } from '../common/unicode.js';\nimport { ERR } from '../common/error-codes.js';\n//Const\nconst DEFAULT_BUFFER_WATERLINE = 1 << 16;\n//Preprocessor\n//NOTE: HTML input preprocessing\n//(see: http://www.whatwg.org/specs/web-apps/current-work/multipage/parsing.html#preprocessing-the-input-stream)\nexport class Preprocessor {\n    constructor(handler) {\n        this.handler = handler;\n        this.html = '';\n        this.pos = -1;\n        // NOTE: Initial `lastGapPos` is -2, to ensure `col` on initialisation is 0\n        this.lastGapPos = -2;\n        this.gapStack = [];\n        this.skipNextNewLine = false;\n        this.lastChunkWritten = false;\n        this.endOfChunkHit = false;\n        this.bufferWaterline = DEFAULT_BUFFER_WATERLINE;\n        this.isEol = false;\n        this.lineStartPos = 0;\n        this.droppedBufferSize = 0;\n        this.line = 1;\n        //NOTE: avoid reporting errors twice on advance/retreat\n        this.lastErrOffset = -1;\n    }\n    /** The column on the current line. If we just saw a gap (eg. a surrogate pair), return the index before. */\n    get col() {\n        return this.pos - this.lineStartPos + Number(this.lastGapPos !== this.pos);\n    }\n    get offset() {\n        return this.droppedBufferSize + this.pos;\n    }\n    getError(code, cpOffset) {\n        const { line, col, offset } = this;\n        const startCol = col + cpOffset;\n        const startOffset = offset + cpOffset;\n        return {\n            code,\n            startLine: line,\n            endLine: line,\n            startCol,\n            endCol: startCol,\n            startOffset,\n            endOffset: startOffset,\n        };\n    }\n    _err(code) {\n        if (this.handler.onParseError && this.lastErrOffset !== this.offset) {\n            this.lastErrOffset = this.offset;\n            this.handler.onParseError(this.getError(code, 0));\n        }\n    }\n    _addGap() {\n        this.gapStack.push(this.lastGapPos);\n        this.lastGapPos = this.pos;\n    }\n    _processSurrogate(cp) {\n        //NOTE: try to peek a surrogate pair\n        if (this.pos !== this.html.length - 1) {\n            const nextCp = this.html.charCodeAt(this.pos + 1);\n            if (isSurrogatePair(nextCp)) {\n                //NOTE: we have a surrogate pair. Peek pair character and recalculate code point.\n                this.pos++;\n                //NOTE: add a gap that should be avoided during retreat\n                this._addGap();\n                return getSurrogatePairCodePoint(cp, nextCp);\n            }\n        }\n        //NOTE: we are at the end of a chunk, therefore we can't infer the surrogate pair yet.\n        else if (!this.lastChunkWritten) {\n            this.endOfChunkHit = true;\n            return $.EOF;\n        }\n        //NOTE: isolated surrogate\n        this._err(ERR.surrogateInInputStream);\n        return cp;\n    }\n    willDropParsedChunk() {\n        return this.pos > this.bufferWaterline;\n    }\n    dropParsedChunk() {\n        if (this.willDropParsedChunk()) {\n            this.html = this.html.substring(this.pos);\n            this.lineStartPos -= this.pos;\n            this.droppedBufferSize += this.pos;\n            this.pos = 0;\n            this.lastGapPos = -2;\n            this.gapStack.length = 0;\n        }\n    }\n    write(chunk, isLastChunk) {\n        if (this.html.length > 0) {\n            this.html += chunk;\n        }\n        else {\n            this.html = chunk;\n        }\n        this.endOfChunkHit = false;\n        this.lastChunkWritten = isLastChunk;\n    }\n    insertHtmlAtCurrentPos(chunk) {\n        this.html = this.html.substring(0, this.pos + 1) + chunk + this.html.substring(this.pos + 1);\n        this.endOfChunkHit = false;\n    }\n    startsWith(pattern, caseSensitive) {\n        // Check if our buffer has enough characters\n        if (this.pos + pattern.length > this.html.length) {\n            this.endOfChunkHit = !this.lastChunkWritten;\n            return false;\n        }\n        if (caseSensitive) {\n            return this.html.startsWith(pattern, this.pos);\n        }\n        for (let i = 0; i < pattern.length; i++) {\n            const cp = this.html.charCodeAt(this.pos + i) | 0x20;\n            if (cp !== pattern.charCodeAt(i)) {\n                return false;\n            }\n        }\n        return true;\n    }\n    peek(offset) {\n        const pos = this.pos + offset;\n        if (pos >= this.html.length) {\n            this.endOfChunkHit = !this.lastChunkWritten;\n            return $.EOF;\n        }\n        const code = this.html.charCodeAt(pos);\n        return code === $.CARRIAGE_RETURN ? $.LINE_FEED : code;\n    }\n    advance() {\n        this.pos++;\n        //NOTE: LF should be in the last column of the line\n        if (this.isEol) {\n            this.isEol = false;\n            this.line++;\n            this.lineStartPos = this.pos;\n        }\n        if (this.pos >= this.html.length) {\n            this.endOfChunkHit = !this.lastChunkWritten;\n            return $.EOF;\n        }\n        let cp = this.html.charCodeAt(this.pos);\n        //NOTE: all U+000D CARRIAGE RETURN (CR) characters must be converted to U+000A LINE FEED (LF) characters\n        if (cp === $.CARRIAGE_RETURN) {\n            this.isEol = true;\n            this.skipNextNewLine = true;\n            return $.LINE_FEED;\n        }\n        //NOTE: any U+000A LINE FEED (LF) characters that immediately follow a U+000D CARRIAGE RETURN (CR) character\n        //must be ignored.\n        if (cp === $.LINE_FEED) {\n            this.isEol = true;\n            if (this.skipNextNewLine) {\n                // `line` will be bumped again in the recursive call.\n                this.line--;\n                this.skipNextNewLine = false;\n                this._addGap();\n                return this.advance();\n            }\n        }\n        this.skipNextNewLine = false;\n        if (isSurrogate(cp)) {\n            cp = this._processSurrogate(cp);\n        }\n        //OPTIMIZATION: first check if code point is in the common allowed\n        //range (ASCII alphanumeric, whitespaces, big chunk of BMP)\n        //before going into detailed performance cost validation.\n        const isCommonValidRange = this.handler.onParseError === null ||\n            (cp > 0x1f && cp < 0x7f) ||\n            cp === $.LINE_FEED ||\n            cp === $.CARRIAGE_RETURN ||\n            (cp > 0x9f && cp < 64976);\n        if (!isCommonValidRange) {\n            this._checkForProblematicCharacters(cp);\n        }\n        return cp;\n    }\n    _checkForProblematicCharacters(cp) {\n        if (isControlCodePoint(cp)) {\n            this._err(ERR.controlCharacterInInputStream);\n        }\n        else if (isUndefinedCodePoint(cp)) {\n            this._err(ERR.noncharacterInInputStream);\n        }\n    }\n    retreat(count) {\n        this.pos -= count;\n        while (this.pos < this.lastGapPos) {\n            this.lastGapPos = this.gapStack.pop();\n            this.pos--;\n        }\n        this.isEol = false;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,OAAO;AACP,MAAM,2BAA2B,KAAK;AAI/B,MAAM;IACT,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,GAAG,GAAG,CAAC;QACZ,2EAA2E;QAC3E,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,IAAI,GAAG;QACZ,uDAAuD;QACvD,IAAI,CAAC,aAAa,GAAG,CAAC;IAC1B;IACA,0GAA0G,GAC1G,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,GAAG;IAC7E;IACA,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG;IAC5C;IACA,SAAS,IAAI,EAAE,QAAQ,EAAE;QACrB,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,IAAI;QAClC,MAAM,WAAW,MAAM;QACvB,MAAM,cAAc,SAAS;QAC7B,OAAO;YACH;YACA,WAAW;YACX,SAAS;YACT;YACA,QAAQ;YACR;YACA,WAAW;QACf;IACJ;IACA,KAAK,IAAI,EAAE;QACP,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,MAAM,EAAE;YACjE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM;YAChC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM;QAClD;IACJ;IACA,UAAU;QACN,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG;IAC9B;IACA,kBAAkB,EAAE,EAAE;QAClB,oCAAoC;QACpC,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;YACnC,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG;YAC/C,IAAI,CAAA,GAAA,mJAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;gBACzB,iFAAiF;gBACjF,IAAI,CAAC,GAAG;gBACR,uDAAuD;gBACvD,IAAI,CAAC,OAAO;gBACZ,OAAO,CAAA,GAAA,mJAAA,CAAA,4BAAyB,AAAD,EAAE,IAAI;YACzC;QACJ,OAEK,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC7B,IAAI,CAAC,aAAa,GAAG;YACrB,OAAO,mJAAA,CAAA,cAAC,CAAC,GAAG;QAChB;QACA,0BAA0B;QAC1B,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,sBAAsB;QACpC,OAAO;IACX;IACA,sBAAsB;QAClB,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe;IAC1C;IACA,kBAAkB;QACd,IAAI,IAAI,CAAC,mBAAmB,IAAI;YAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;YACxC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,GAAG;YAC7B,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,GAAG;YAClC,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,UAAU,GAAG,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;QAC3B;IACJ;IACA,MAAM,KAAK,EAAE,WAAW,EAAE;QACtB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;YACtB,IAAI,CAAC,IAAI,IAAI;QACjB,OACK;YACD,IAAI,CAAC,IAAI,GAAG;QAChB;QACA,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,gBAAgB,GAAG;IAC5B;IACA,uBAAuB,KAAK,EAAE;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG;QAC1F,IAAI,CAAC,aAAa,GAAG;IACzB;IACA,WAAW,OAAO,EAAE,aAAa,EAAE;QAC/B,4CAA4C;QAC5C,IAAI,IAAI,CAAC,GAAG,GAAG,QAAQ,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9C,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,gBAAgB;YAC3C,OAAO;QACX;QACA,IAAI,eAAe;YACf,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,GAAG;QACjD;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK;YAChD,IAAI,OAAO,QAAQ,UAAU,CAAC,IAAI;gBAC9B,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,KAAK,MAAM,EAAE;QACT,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG;QACvB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACzB,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,gBAAgB;YAC3C,OAAO,mJAAA,CAAA,cAAC,CAAC,GAAG;QAChB;QACA,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO,SAAS,mJAAA,CAAA,cAAC,CAAC,eAAe,GAAG,mJAAA,CAAA,cAAC,CAAC,SAAS,GAAG;IACtD;IACA,UAAU;QACN,IAAI,CAAC,GAAG;QACR,mDAAmD;QACnD,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG;QAChC;QACA,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,gBAAgB;YAC3C,OAAO,mJAAA,CAAA,cAAC,CAAC,GAAG;QAChB;QACA,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG;QACtC,wGAAwG;QACxG,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,eAAe,EAAE;YAC1B,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,eAAe,GAAG;YACvB,OAAO,mJAAA,CAAA,cAAC,CAAC,SAAS;QACtB;QACA,4GAA4G;QAC5G,kBAAkB;QAClB,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,SAAS,EAAE;YACpB,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,qDAAqD;gBACrD,IAAI,CAAC,IAAI;gBACT,IAAI,CAAC,eAAe,GAAG;gBACvB,IAAI,CAAC,OAAO;gBACZ,OAAO,IAAI,CAAC,OAAO;YACvB;QACJ;QACA,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAA,GAAA,mJAAA,CAAA,cAAW,AAAD,EAAE,KAAK;YACjB,KAAK,IAAI,CAAC,iBAAiB,CAAC;QAChC;QACA,kEAAkE;QAClE,2DAA2D;QAC3D,yDAAyD;QACzD,MAAM,qBAAqB,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,QACpD,KAAK,QAAQ,KAAK,QACnB,OAAO,mJAAA,CAAA,cAAC,CAAC,SAAS,IAClB,OAAO,mJAAA,CAAA,cAAC,CAAC,eAAe,IACvB,KAAK,QAAQ,KAAK;QACvB,IAAI,CAAC,oBAAoB;YACrB,IAAI,CAAC,8BAA8B,CAAC;QACxC;QACA,OAAO;IACX;IACA,+BAA+B,EAAE,EAAE;QAC/B,IAAI,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK;YACxB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,6BAA6B;QAC/C,OACK,IAAI,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK;YAC/B,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,yBAAyB;QAC3C;IACJ;IACA,QAAQ,KAAK,EAAE;QACX,IAAI,CAAC,GAAG,IAAI;QACZ,MAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,CAAE;YAC/B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG;YACnC,IAAI,CAAC,GAAG;QACZ;QACA,IAAI,CAAC,KAAK,GAAG;IACjB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/common/token.js"], "sourcesContent": ["export var TokenType;\n(function (TokenType) {\n    TokenType[TokenType[\"CHARACTER\"] = 0] = \"CHARACTER\";\n    TokenType[TokenType[\"NULL_CHARACTER\"] = 1] = \"NULL_CHARACTER\";\n    TokenType[TokenType[\"WHITESPACE_CHARACTER\"] = 2] = \"WHITESPACE_CHARACTER\";\n    TokenType[TokenType[\"START_TAG\"] = 3] = \"START_TAG\";\n    TokenType[TokenType[\"END_TAG\"] = 4] = \"END_TAG\";\n    TokenType[TokenType[\"COMMENT\"] = 5] = \"COMMENT\";\n    TokenType[TokenType[\"DOCTYPE\"] = 6] = \"DOCTYPE\";\n    TokenType[TokenType[\"EOF\"] = 7] = \"EOF\";\n    TokenType[TokenType[\"HIBERNATION\"] = 8] = \"HIBERNATION\";\n})(TokenType || (TokenType = {}));\nexport function getTokenAttr(token, attrName) {\n    for (let i = token.attrs.length - 1; i >= 0; i--) {\n        if (token.attrs[i].name === attrName) {\n            return token.attrs[i].value;\n        }\n    }\n    return null;\n}\n"], "names": [], "mappings": ";;;;AAAO,IAAI;AACX,CAAC,SAAU,SAAS;IAChB,SAAS,CAAC,SAAS,CAAC,YAAY,GAAG,EAAE,GAAG;IACxC,SAAS,CAAC,SAAS,CAAC,iBAAiB,GAAG,EAAE,GAAG;IAC7C,SAAS,CAAC,SAAS,CAAC,uBAAuB,GAAG,EAAE,GAAG;IACnD,SAAS,CAAC,SAAS,CAAC,YAAY,GAAG,EAAE,GAAG;IACxC,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,GAAG;IACtC,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,GAAG;IACtC,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,GAAG;IACtC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,GAAG;IAClC,SAAS,CAAC,SAAS,CAAC,cAAc,GAAG,EAAE,GAAG;AAC9C,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AACxB,SAAS,aAAa,KAAK,EAAE,QAAQ;IACxC,IAAK,IAAI,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC9C,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,UAAU;YAClC,OAAO,MAAM,KAAK,CAAC,EAAE,CAAC,KAAK;QAC/B;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/common/html.js"], "sourcesContent": ["/** All valid namespaces in HTML. */\nexport var NS;\n(function (NS) {\n    NS[\"HTML\"] = \"http://www.w3.org/1999/xhtml\";\n    NS[\"MATHML\"] = \"http://www.w3.org/1998/Math/MathML\";\n    NS[\"SVG\"] = \"http://www.w3.org/2000/svg\";\n    NS[\"XLINK\"] = \"http://www.w3.org/1999/xlink\";\n    NS[\"XML\"] = \"http://www.w3.org/XML/1998/namespace\";\n    NS[\"XMLNS\"] = \"http://www.w3.org/2000/xmlns/\";\n})(NS || (NS = {}));\nexport var ATTRS;\n(function (ATTRS) {\n    ATTRS[\"TYPE\"] = \"type\";\n    ATTRS[\"ACTION\"] = \"action\";\n    ATTRS[\"ENCODING\"] = \"encoding\";\n    ATTRS[\"PROMPT\"] = \"prompt\";\n    ATTRS[\"NAME\"] = \"name\";\n    ATTRS[\"COLOR\"] = \"color\";\n    ATTRS[\"FACE\"] = \"face\";\n    ATTRS[\"SIZE\"] = \"size\";\n})(ATTRS || (ATTRS = {}));\n/**\n * The mode of the document.\n *\n * @see {@link https://dom.spec.whatwg.org/#concept-document-limited-quirks}\n */\nexport var DOCUMENT_MODE;\n(function (DOCUMENT_MODE) {\n    DOCUMENT_MODE[\"NO_QUIRKS\"] = \"no-quirks\";\n    DOCUMENT_MODE[\"QUIRKS\"] = \"quirks\";\n    DOCUMENT_MODE[\"LIMITED_QUIRKS\"] = \"limited-quirks\";\n})(DOCUMENT_MODE || (DOCUMENT_MODE = {}));\nexport var TAG_NAMES;\n(function (TAG_NAMES) {\n    TAG_NAMES[\"A\"] = \"a\";\n    TAG_NAMES[\"ADDRESS\"] = \"address\";\n    TAG_NAMES[\"ANNOTATION_XML\"] = \"annotation-xml\";\n    TAG_NAMES[\"APPLET\"] = \"applet\";\n    TAG_NAMES[\"AREA\"] = \"area\";\n    TAG_NAMES[\"ARTICLE\"] = \"article\";\n    TAG_NAMES[\"ASIDE\"] = \"aside\";\n    TAG_NAMES[\"B\"] = \"b\";\n    TAG_NAMES[\"BASE\"] = \"base\";\n    TAG_NAMES[\"BASEFONT\"] = \"basefont\";\n    TAG_NAMES[\"BGSOUND\"] = \"bgsound\";\n    TAG_NAMES[\"BIG\"] = \"big\";\n    TAG_NAMES[\"BLOCKQUOTE\"] = \"blockquote\";\n    TAG_NAMES[\"BODY\"] = \"body\";\n    TAG_NAMES[\"BR\"] = \"br\";\n    TAG_NAMES[\"BUTTON\"] = \"button\";\n    TAG_NAMES[\"CAPTION\"] = \"caption\";\n    TAG_NAMES[\"CENTER\"] = \"center\";\n    TAG_NAMES[\"CODE\"] = \"code\";\n    TAG_NAMES[\"COL\"] = \"col\";\n    TAG_NAMES[\"COLGROUP\"] = \"colgroup\";\n    TAG_NAMES[\"DD\"] = \"dd\";\n    TAG_NAMES[\"DESC\"] = \"desc\";\n    TAG_NAMES[\"DETAILS\"] = \"details\";\n    TAG_NAMES[\"DIALOG\"] = \"dialog\";\n    TAG_NAMES[\"DIR\"] = \"dir\";\n    TAG_NAMES[\"DIV\"] = \"div\";\n    TAG_NAMES[\"DL\"] = \"dl\";\n    TAG_NAMES[\"DT\"] = \"dt\";\n    TAG_NAMES[\"EM\"] = \"em\";\n    TAG_NAMES[\"EMBED\"] = \"embed\";\n    TAG_NAMES[\"FIELDSET\"] = \"fieldset\";\n    TAG_NAMES[\"FIGCAPTION\"] = \"figcaption\";\n    TAG_NAMES[\"FIGURE\"] = \"figure\";\n    TAG_NAMES[\"FONT\"] = \"font\";\n    TAG_NAMES[\"FOOTER\"] = \"footer\";\n    TAG_NAMES[\"FOREIGN_OBJECT\"] = \"foreignObject\";\n    TAG_NAMES[\"FORM\"] = \"form\";\n    TAG_NAMES[\"FRAME\"] = \"frame\";\n    TAG_NAMES[\"FRAMESET\"] = \"frameset\";\n    TAG_NAMES[\"H1\"] = \"h1\";\n    TAG_NAMES[\"H2\"] = \"h2\";\n    TAG_NAMES[\"H3\"] = \"h3\";\n    TAG_NAMES[\"H4\"] = \"h4\";\n    TAG_NAMES[\"H5\"] = \"h5\";\n    TAG_NAMES[\"H6\"] = \"h6\";\n    TAG_NAMES[\"HEAD\"] = \"head\";\n    TAG_NAMES[\"HEADER\"] = \"header\";\n    TAG_NAMES[\"HGROUP\"] = \"hgroup\";\n    TAG_NAMES[\"HR\"] = \"hr\";\n    TAG_NAMES[\"HTML\"] = \"html\";\n    TAG_NAMES[\"I\"] = \"i\";\n    TAG_NAMES[\"IMG\"] = \"img\";\n    TAG_NAMES[\"IMAGE\"] = \"image\";\n    TAG_NAMES[\"INPUT\"] = \"input\";\n    TAG_NAMES[\"IFRAME\"] = \"iframe\";\n    TAG_NAMES[\"KEYGEN\"] = \"keygen\";\n    TAG_NAMES[\"LABEL\"] = \"label\";\n    TAG_NAMES[\"LI\"] = \"li\";\n    TAG_NAMES[\"LINK\"] = \"link\";\n    TAG_NAMES[\"LISTING\"] = \"listing\";\n    TAG_NAMES[\"MAIN\"] = \"main\";\n    TAG_NAMES[\"MALIGNMARK\"] = \"malignmark\";\n    TAG_NAMES[\"MARQUEE\"] = \"marquee\";\n    TAG_NAMES[\"MATH\"] = \"math\";\n    TAG_NAMES[\"MENU\"] = \"menu\";\n    TAG_NAMES[\"META\"] = \"meta\";\n    TAG_NAMES[\"MGLYPH\"] = \"mglyph\";\n    TAG_NAMES[\"MI\"] = \"mi\";\n    TAG_NAMES[\"MO\"] = \"mo\";\n    TAG_NAMES[\"MN\"] = \"mn\";\n    TAG_NAMES[\"MS\"] = \"ms\";\n    TAG_NAMES[\"MTEXT\"] = \"mtext\";\n    TAG_NAMES[\"NAV\"] = \"nav\";\n    TAG_NAMES[\"NOBR\"] = \"nobr\";\n    TAG_NAMES[\"NOFRAMES\"] = \"noframes\";\n    TAG_NAMES[\"NOEMBED\"] = \"noembed\";\n    TAG_NAMES[\"NOSCRIPT\"] = \"noscript\";\n    TAG_NAMES[\"OBJECT\"] = \"object\";\n    TAG_NAMES[\"OL\"] = \"ol\";\n    TAG_NAMES[\"OPTGROUP\"] = \"optgroup\";\n    TAG_NAMES[\"OPTION\"] = \"option\";\n    TAG_NAMES[\"P\"] = \"p\";\n    TAG_NAMES[\"PARAM\"] = \"param\";\n    TAG_NAMES[\"PLAINTEXT\"] = \"plaintext\";\n    TAG_NAMES[\"PRE\"] = \"pre\";\n    TAG_NAMES[\"RB\"] = \"rb\";\n    TAG_NAMES[\"RP\"] = \"rp\";\n    TAG_NAMES[\"RT\"] = \"rt\";\n    TAG_NAMES[\"RTC\"] = \"rtc\";\n    TAG_NAMES[\"RUBY\"] = \"ruby\";\n    TAG_NAMES[\"S\"] = \"s\";\n    TAG_NAMES[\"SCRIPT\"] = \"script\";\n    TAG_NAMES[\"SEARCH\"] = \"search\";\n    TAG_NAMES[\"SECTION\"] = \"section\";\n    TAG_NAMES[\"SELECT\"] = \"select\";\n    TAG_NAMES[\"SOURCE\"] = \"source\";\n    TAG_NAMES[\"SMALL\"] = \"small\";\n    TAG_NAMES[\"SPAN\"] = \"span\";\n    TAG_NAMES[\"STRIKE\"] = \"strike\";\n    TAG_NAMES[\"STRONG\"] = \"strong\";\n    TAG_NAMES[\"STYLE\"] = \"style\";\n    TAG_NAMES[\"SUB\"] = \"sub\";\n    TAG_NAMES[\"SUMMARY\"] = \"summary\";\n    TAG_NAMES[\"SUP\"] = \"sup\";\n    TAG_NAMES[\"TABLE\"] = \"table\";\n    TAG_NAMES[\"TBODY\"] = \"tbody\";\n    TAG_NAMES[\"TEMPLATE\"] = \"template\";\n    TAG_NAMES[\"TEXTAREA\"] = \"textarea\";\n    TAG_NAMES[\"TFOOT\"] = \"tfoot\";\n    TAG_NAMES[\"TD\"] = \"td\";\n    TAG_NAMES[\"TH\"] = \"th\";\n    TAG_NAMES[\"THEAD\"] = \"thead\";\n    TAG_NAMES[\"TITLE\"] = \"title\";\n    TAG_NAMES[\"TR\"] = \"tr\";\n    TAG_NAMES[\"TRACK\"] = \"track\";\n    TAG_NAMES[\"TT\"] = \"tt\";\n    TAG_NAMES[\"U\"] = \"u\";\n    TAG_NAMES[\"UL\"] = \"ul\";\n    TAG_NAMES[\"SVG\"] = \"svg\";\n    TAG_NAMES[\"VAR\"] = \"var\";\n    TAG_NAMES[\"WBR\"] = \"wbr\";\n    TAG_NAMES[\"XMP\"] = \"xmp\";\n})(TAG_NAMES || (TAG_NAMES = {}));\n/**\n * Tag IDs are numeric IDs for known tag names.\n *\n * We use tag IDs to improve the performance of tag name comparisons.\n */\nexport var TAG_ID;\n(function (TAG_ID) {\n    TAG_ID[TAG_ID[\"UNKNOWN\"] = 0] = \"UNKNOWN\";\n    TAG_ID[TAG_ID[\"A\"] = 1] = \"A\";\n    TAG_ID[TAG_ID[\"ADDRESS\"] = 2] = \"ADDRESS\";\n    TAG_ID[TAG_ID[\"ANNOTATION_XML\"] = 3] = \"ANNOTATION_XML\";\n    TAG_ID[TAG_ID[\"APPLET\"] = 4] = \"APPLET\";\n    TAG_ID[TAG_ID[\"AREA\"] = 5] = \"AREA\";\n    TAG_ID[TAG_ID[\"ARTICLE\"] = 6] = \"ARTICLE\";\n    TAG_ID[TAG_ID[\"ASIDE\"] = 7] = \"ASIDE\";\n    TAG_ID[TAG_ID[\"B\"] = 8] = \"B\";\n    TAG_ID[TAG_ID[\"BASE\"] = 9] = \"BASE\";\n    TAG_ID[TAG_ID[\"BASEFONT\"] = 10] = \"BASEFONT\";\n    TAG_ID[TAG_ID[\"BGSOUND\"] = 11] = \"BGSOUND\";\n    TAG_ID[TAG_ID[\"BIG\"] = 12] = \"BIG\";\n    TAG_ID[TAG_ID[\"BLOCKQUOTE\"] = 13] = \"BLOCKQUOTE\";\n    TAG_ID[TAG_ID[\"BODY\"] = 14] = \"BODY\";\n    TAG_ID[TAG_ID[\"BR\"] = 15] = \"BR\";\n    TAG_ID[TAG_ID[\"BUTTON\"] = 16] = \"BUTTON\";\n    TAG_ID[TAG_ID[\"CAPTION\"] = 17] = \"CAPTION\";\n    TAG_ID[TAG_ID[\"CENTER\"] = 18] = \"CENTER\";\n    TAG_ID[TAG_ID[\"CODE\"] = 19] = \"CODE\";\n    TAG_ID[TAG_ID[\"COL\"] = 20] = \"COL\";\n    TAG_ID[TAG_ID[\"COLGROUP\"] = 21] = \"COLGROUP\";\n    TAG_ID[TAG_ID[\"DD\"] = 22] = \"DD\";\n    TAG_ID[TAG_ID[\"DESC\"] = 23] = \"DESC\";\n    TAG_ID[TAG_ID[\"DETAILS\"] = 24] = \"DETAILS\";\n    TAG_ID[TAG_ID[\"DIALOG\"] = 25] = \"DIALOG\";\n    TAG_ID[TAG_ID[\"DIR\"] = 26] = \"DIR\";\n    TAG_ID[TAG_ID[\"DIV\"] = 27] = \"DIV\";\n    TAG_ID[TAG_ID[\"DL\"] = 28] = \"DL\";\n    TAG_ID[TAG_ID[\"DT\"] = 29] = \"DT\";\n    TAG_ID[TAG_ID[\"EM\"] = 30] = \"EM\";\n    TAG_ID[TAG_ID[\"EMBED\"] = 31] = \"EMBED\";\n    TAG_ID[TAG_ID[\"FIELDSET\"] = 32] = \"FIELDSET\";\n    TAG_ID[TAG_ID[\"FIGCAPTION\"] = 33] = \"FIGCAPTION\";\n    TAG_ID[TAG_ID[\"FIGURE\"] = 34] = \"FIGURE\";\n    TAG_ID[TAG_ID[\"FONT\"] = 35] = \"FONT\";\n    TAG_ID[TAG_ID[\"FOOTER\"] = 36] = \"FOOTER\";\n    TAG_ID[TAG_ID[\"FOREIGN_OBJECT\"] = 37] = \"FOREIGN_OBJECT\";\n    TAG_ID[TAG_ID[\"FORM\"] = 38] = \"FORM\";\n    TAG_ID[TAG_ID[\"FRAME\"] = 39] = \"FRAME\";\n    TAG_ID[TAG_ID[\"FRAMESET\"] = 40] = \"FRAMESET\";\n    TAG_ID[TAG_ID[\"H1\"] = 41] = \"H1\";\n    TAG_ID[TAG_ID[\"H2\"] = 42] = \"H2\";\n    TAG_ID[TAG_ID[\"H3\"] = 43] = \"H3\";\n    TAG_ID[TAG_ID[\"H4\"] = 44] = \"H4\";\n    TAG_ID[TAG_ID[\"H5\"] = 45] = \"H5\";\n    TAG_ID[TAG_ID[\"H6\"] = 46] = \"H6\";\n    TAG_ID[TAG_ID[\"HEAD\"] = 47] = \"HEAD\";\n    TAG_ID[TAG_ID[\"HEADER\"] = 48] = \"HEADER\";\n    TAG_ID[TAG_ID[\"HGROUP\"] = 49] = \"HGROUP\";\n    TAG_ID[TAG_ID[\"HR\"] = 50] = \"HR\";\n    TAG_ID[TAG_ID[\"HTML\"] = 51] = \"HTML\";\n    TAG_ID[TAG_ID[\"I\"] = 52] = \"I\";\n    TAG_ID[TAG_ID[\"IMG\"] = 53] = \"IMG\";\n    TAG_ID[TAG_ID[\"IMAGE\"] = 54] = \"IMAGE\";\n    TAG_ID[TAG_ID[\"INPUT\"] = 55] = \"INPUT\";\n    TAG_ID[TAG_ID[\"IFRAME\"] = 56] = \"IFRAME\";\n    TAG_ID[TAG_ID[\"KEYGEN\"] = 57] = \"KEYGEN\";\n    TAG_ID[TAG_ID[\"LABEL\"] = 58] = \"LABEL\";\n    TAG_ID[TAG_ID[\"LI\"] = 59] = \"LI\";\n    TAG_ID[TAG_ID[\"LINK\"] = 60] = \"LINK\";\n    TAG_ID[TAG_ID[\"LISTING\"] = 61] = \"LISTING\";\n    TAG_ID[TAG_ID[\"MAIN\"] = 62] = \"MAIN\";\n    TAG_ID[TAG_ID[\"MALIGNMARK\"] = 63] = \"MALIGNMARK\";\n    TAG_ID[TAG_ID[\"MARQUEE\"] = 64] = \"MARQUEE\";\n    TAG_ID[TAG_ID[\"MATH\"] = 65] = \"MATH\";\n    TAG_ID[TAG_ID[\"MENU\"] = 66] = \"MENU\";\n    TAG_ID[TAG_ID[\"META\"] = 67] = \"META\";\n    TAG_ID[TAG_ID[\"MGLYPH\"] = 68] = \"MGLYPH\";\n    TAG_ID[TAG_ID[\"MI\"] = 69] = \"MI\";\n    TAG_ID[TAG_ID[\"MO\"] = 70] = \"MO\";\n    TAG_ID[TAG_ID[\"MN\"] = 71] = \"MN\";\n    TAG_ID[TAG_ID[\"MS\"] = 72] = \"MS\";\n    TAG_ID[TAG_ID[\"MTEXT\"] = 73] = \"MTEXT\";\n    TAG_ID[TAG_ID[\"NAV\"] = 74] = \"NAV\";\n    TAG_ID[TAG_ID[\"NOBR\"] = 75] = \"NOBR\";\n    TAG_ID[TAG_ID[\"NOFRAMES\"] = 76] = \"NOFRAMES\";\n    TAG_ID[TAG_ID[\"NOEMBED\"] = 77] = \"NOEMBED\";\n    TAG_ID[TAG_ID[\"NOSCRIPT\"] = 78] = \"NOSCRIPT\";\n    TAG_ID[TAG_ID[\"OBJECT\"] = 79] = \"OBJECT\";\n    TAG_ID[TAG_ID[\"OL\"] = 80] = \"OL\";\n    TAG_ID[TAG_ID[\"OPTGROUP\"] = 81] = \"OPTGROUP\";\n    TAG_ID[TAG_ID[\"OPTION\"] = 82] = \"OPTION\";\n    TAG_ID[TAG_ID[\"P\"] = 83] = \"P\";\n    TAG_ID[TAG_ID[\"PARAM\"] = 84] = \"PARAM\";\n    TAG_ID[TAG_ID[\"PLAINTEXT\"] = 85] = \"PLAINTEXT\";\n    TAG_ID[TAG_ID[\"PRE\"] = 86] = \"PRE\";\n    TAG_ID[TAG_ID[\"RB\"] = 87] = \"RB\";\n    TAG_ID[TAG_ID[\"RP\"] = 88] = \"RP\";\n    TAG_ID[TAG_ID[\"RT\"] = 89] = \"RT\";\n    TAG_ID[TAG_ID[\"RTC\"] = 90] = \"RTC\";\n    TAG_ID[TAG_ID[\"RUBY\"] = 91] = \"RUBY\";\n    TAG_ID[TAG_ID[\"S\"] = 92] = \"S\";\n    TAG_ID[TAG_ID[\"SCRIPT\"] = 93] = \"SCRIPT\";\n    TAG_ID[TAG_ID[\"SEARCH\"] = 94] = \"SEARCH\";\n    TAG_ID[TAG_ID[\"SECTION\"] = 95] = \"SECTION\";\n    TAG_ID[TAG_ID[\"SELECT\"] = 96] = \"SELECT\";\n    TAG_ID[TAG_ID[\"SOURCE\"] = 97] = \"SOURCE\";\n    TAG_ID[TAG_ID[\"SMALL\"] = 98] = \"SMALL\";\n    TAG_ID[TAG_ID[\"SPAN\"] = 99] = \"SPAN\";\n    TAG_ID[TAG_ID[\"STRIKE\"] = 100] = \"STRIKE\";\n    TAG_ID[TAG_ID[\"STRONG\"] = 101] = \"STRONG\";\n    TAG_ID[TAG_ID[\"STYLE\"] = 102] = \"STYLE\";\n    TAG_ID[TAG_ID[\"SUB\"] = 103] = \"SUB\";\n    TAG_ID[TAG_ID[\"SUMMARY\"] = 104] = \"SUMMARY\";\n    TAG_ID[TAG_ID[\"SUP\"] = 105] = \"SUP\";\n    TAG_ID[TAG_ID[\"TABLE\"] = 106] = \"TABLE\";\n    TAG_ID[TAG_ID[\"TBODY\"] = 107] = \"TBODY\";\n    TAG_ID[TAG_ID[\"TEMPLATE\"] = 108] = \"TEMPLATE\";\n    TAG_ID[TAG_ID[\"TEXTAREA\"] = 109] = \"TEXTAREA\";\n    TAG_ID[TAG_ID[\"TFOOT\"] = 110] = \"TFOOT\";\n    TAG_ID[TAG_ID[\"TD\"] = 111] = \"TD\";\n    TAG_ID[TAG_ID[\"TH\"] = 112] = \"TH\";\n    TAG_ID[TAG_ID[\"THEAD\"] = 113] = \"THEAD\";\n    TAG_ID[TAG_ID[\"TITLE\"] = 114] = \"TITLE\";\n    TAG_ID[TAG_ID[\"TR\"] = 115] = \"TR\";\n    TAG_ID[TAG_ID[\"TRACK\"] = 116] = \"TRACK\";\n    TAG_ID[TAG_ID[\"TT\"] = 117] = \"TT\";\n    TAG_ID[TAG_ID[\"U\"] = 118] = \"U\";\n    TAG_ID[TAG_ID[\"UL\"] = 119] = \"UL\";\n    TAG_ID[TAG_ID[\"SVG\"] = 120] = \"SVG\";\n    TAG_ID[TAG_ID[\"VAR\"] = 121] = \"VAR\";\n    TAG_ID[TAG_ID[\"WBR\"] = 122] = \"WBR\";\n    TAG_ID[TAG_ID[\"XMP\"] = 123] = \"XMP\";\n})(TAG_ID || (TAG_ID = {}));\nconst TAG_NAME_TO_ID = new Map([\n    [TAG_NAMES.A, TAG_ID.A],\n    [TAG_NAMES.ADDRESS, TAG_ID.ADDRESS],\n    [TAG_NAMES.ANNOTATION_XML, TAG_ID.ANNOTATION_XML],\n    [TAG_NAMES.APPLET, TAG_ID.APPLET],\n    [TAG_NAMES.AREA, TAG_ID.AREA],\n    [TAG_NAMES.ARTICLE, TAG_ID.ARTICLE],\n    [TAG_NAMES.ASIDE, TAG_ID.ASIDE],\n    [TAG_NAMES.B, TAG_ID.B],\n    [TAG_NAMES.BASE, TAG_ID.BASE],\n    [TAG_NAMES.BASEFONT, TAG_ID.BASEFONT],\n    [TAG_NAMES.BGSOUND, TAG_ID.BGSOUND],\n    [TAG_NAMES.BIG, TAG_ID.BIG],\n    [TAG_NAMES.BLOCKQUOTE, TAG_ID.BLOCKQUOTE],\n    [TAG_NAMES.BODY, TAG_ID.BODY],\n    [TAG_NAMES.BR, TAG_ID.BR],\n    [TAG_NAMES.BUTTON, TAG_ID.BUTTON],\n    [TAG_NAMES.CAPTION, TAG_ID.CAPTION],\n    [TAG_NAMES.CENTER, TAG_ID.CENTER],\n    [TAG_NAMES.CODE, TAG_ID.CODE],\n    [TAG_NAMES.COL, TAG_ID.COL],\n    [TAG_NAMES.COLGROUP, TAG_ID.COLGROUP],\n    [TAG_NAMES.DD, TAG_ID.DD],\n    [TAG_NAMES.DESC, TAG_ID.DESC],\n    [TAG_NAMES.DETAILS, TAG_ID.DETAILS],\n    [TAG_NAMES.DIALOG, TAG_ID.DIALOG],\n    [TAG_NAMES.DIR, TAG_ID.DIR],\n    [TAG_NAMES.DIV, TAG_ID.DIV],\n    [TAG_NAMES.DL, TAG_ID.DL],\n    [TAG_NAMES.DT, TAG_ID.DT],\n    [TAG_NAMES.EM, TAG_ID.EM],\n    [TAG_NAMES.EMBED, TAG_ID.EMBED],\n    [TAG_NAMES.FIELDSET, TAG_ID.FIELDSET],\n    [TAG_NAMES.FIGCAPTION, TAG_ID.FIGCAPTION],\n    [TAG_NAMES.FIGURE, TAG_ID.FIGURE],\n    [TAG_NAMES.FONT, TAG_ID.FONT],\n    [TAG_NAMES.FOOTER, TAG_ID.FOOTER],\n    [TAG_NAMES.FOREIGN_OBJECT, TAG_ID.FOREIGN_OBJECT],\n    [TAG_NAMES.FORM, TAG_ID.FORM],\n    [TAG_NAMES.FRAME, TAG_ID.FRAME],\n    [TAG_NAMES.FRAMESET, TAG_ID.FRAMESET],\n    [TAG_NAMES.H1, TAG_ID.H1],\n    [TAG_NAMES.H2, TAG_ID.H2],\n    [TAG_NAMES.H3, TAG_ID.H3],\n    [TAG_NAMES.H4, TAG_ID.H4],\n    [TAG_NAMES.H5, TAG_ID.H5],\n    [TAG_NAMES.H6, TAG_ID.H6],\n    [TAG_NAMES.HEAD, TAG_ID.HEAD],\n    [TAG_NAMES.HEADER, TAG_ID.HEADER],\n    [TAG_NAMES.HGROUP, TAG_ID.HGROUP],\n    [TAG_NAMES.HR, TAG_ID.HR],\n    [TAG_NAMES.HTML, TAG_ID.HTML],\n    [TAG_NAMES.I, TAG_ID.I],\n    [TAG_NAMES.IMG, TAG_ID.IMG],\n    [TAG_NAMES.IMAGE, TAG_ID.IMAGE],\n    [TAG_NAMES.INPUT, TAG_ID.INPUT],\n    [TAG_NAMES.IFRAME, TAG_ID.IFRAME],\n    [TAG_NAMES.KEYGEN, TAG_ID.KEYGEN],\n    [TAG_NAMES.LABEL, TAG_ID.LABEL],\n    [TAG_NAMES.LI, TAG_ID.LI],\n    [TAG_NAMES.LINK, TAG_ID.LINK],\n    [TAG_NAMES.LISTING, TAG_ID.LISTING],\n    [TAG_NAMES.MAIN, TAG_ID.MAIN],\n    [TAG_NAMES.MALIGNMARK, TAG_ID.MALIGNMARK],\n    [TAG_NAMES.MARQUEE, TAG_ID.MARQUEE],\n    [TAG_NAMES.MATH, TAG_ID.MATH],\n    [TAG_NAMES.MENU, TAG_ID.MENU],\n    [TAG_NAMES.META, TAG_ID.META],\n    [TAG_NAMES.MGLYPH, TAG_ID.MGLYPH],\n    [TAG_NAMES.MI, TAG_ID.MI],\n    [TAG_NAMES.MO, TAG_ID.MO],\n    [TAG_NAMES.MN, TAG_ID.MN],\n    [TAG_NAMES.MS, TAG_ID.MS],\n    [TAG_NAMES.MTEXT, TAG_ID.MTEXT],\n    [TAG_NAMES.NAV, TAG_ID.NAV],\n    [TAG_NAMES.NOBR, TAG_ID.NOBR],\n    [TAG_NAMES.NOFRAMES, TAG_ID.NOFRAMES],\n    [TAG_NAMES.NOEMBED, TAG_ID.NOEMBED],\n    [TAG_NAMES.NOSCRIPT, TAG_ID.NOSCRIPT],\n    [TAG_NAMES.OBJECT, TAG_ID.OBJECT],\n    [TAG_NAMES.OL, TAG_ID.OL],\n    [TAG_NAMES.OPTGROUP, TAG_ID.OPTGROUP],\n    [TAG_NAMES.OPTION, TAG_ID.OPTION],\n    [TAG_NAMES.P, TAG_ID.P],\n    [TAG_NAMES.PARAM, TAG_ID.PARAM],\n    [TAG_NAMES.PLAINTEXT, TAG_ID.PLAINTEXT],\n    [TAG_NAMES.PRE, TAG_ID.PRE],\n    [TAG_NAMES.RB, TAG_ID.RB],\n    [TAG_NAMES.RP, TAG_ID.RP],\n    [TAG_NAMES.RT, TAG_ID.RT],\n    [TAG_NAMES.RTC, TAG_ID.RTC],\n    [TAG_NAMES.RUBY, TAG_ID.RUBY],\n    [TAG_NAMES.S, TAG_ID.S],\n    [TAG_NAMES.SCRIPT, TAG_ID.SCRIPT],\n    [TAG_NAMES.SEARCH, TAG_ID.SEARCH],\n    [TAG_NAMES.SECTION, TAG_ID.SECTION],\n    [TAG_NAMES.SELECT, TAG_ID.SELECT],\n    [TAG_NAMES.SOURCE, TAG_ID.SOURCE],\n    [TAG_NAMES.SMALL, TAG_ID.SMALL],\n    [TAG_NAMES.SPAN, TAG_ID.SPAN],\n    [TAG_NAMES.STRIKE, TAG_ID.STRIKE],\n    [TAG_NAMES.STRONG, TAG_ID.STRONG],\n    [TAG_NAMES.STYLE, TAG_ID.STYLE],\n    [TAG_NAMES.SUB, TAG_ID.SUB],\n    [TAG_NAMES.SUMMARY, TAG_ID.SUMMARY],\n    [TAG_NAMES.SUP, TAG_ID.SUP],\n    [TAG_NAMES.TABLE, TAG_ID.TABLE],\n    [TAG_NAMES.TBODY, TAG_ID.TBODY],\n    [TAG_NAMES.TEMPLATE, TAG_ID.TEMPLATE],\n    [TAG_NAMES.TEXTAREA, TAG_ID.TEXTAREA],\n    [TAG_NAMES.TFOOT, TAG_ID.TFOOT],\n    [TAG_NAMES.TD, TAG_ID.TD],\n    [TAG_NAMES.TH, TAG_ID.TH],\n    [TAG_NAMES.THEAD, TAG_ID.THEAD],\n    [TAG_NAMES.TITLE, TAG_ID.TITLE],\n    [TAG_NAMES.TR, TAG_ID.TR],\n    [TAG_NAMES.TRACK, TAG_ID.TRACK],\n    [TAG_NAMES.TT, TAG_ID.TT],\n    [TAG_NAMES.U, TAG_ID.U],\n    [TAG_NAMES.UL, TAG_ID.UL],\n    [TAG_NAMES.SVG, TAG_ID.SVG],\n    [TAG_NAMES.VAR, TAG_ID.VAR],\n    [TAG_NAMES.WBR, TAG_ID.WBR],\n    [TAG_NAMES.XMP, TAG_ID.XMP],\n]);\nexport function getTagID(tagName) {\n    var _a;\n    return (_a = TAG_NAME_TO_ID.get(tagName)) !== null && _a !== void 0 ? _a : TAG_ID.UNKNOWN;\n}\nconst $ = TAG_ID;\nexport const SPECIAL_ELEMENTS = {\n    [NS.HTML]: new Set([\n        $.ADDRESS,\n        $.APPLET,\n        $.AREA,\n        $.ARTICLE,\n        $.ASIDE,\n        $.BASE,\n        $.BASEFONT,\n        $.BGSOUND,\n        $.BLOCKQUOTE,\n        $.BODY,\n        $.BR,\n        $.BUTTON,\n        $.CAPTION,\n        $.CENTER,\n        $.COL,\n        $.COLGROUP,\n        $.DD,\n        $.DETAILS,\n        $.DIR,\n        $.DIV,\n        $.DL,\n        $.DT,\n        $.EMBED,\n        $.FIELDSET,\n        $.FIGCAPTION,\n        $.FIGURE,\n        $.FOOTER,\n        $.FORM,\n        $.FRAME,\n        $.FRAMESET,\n        $.H1,\n        $.H2,\n        $.H3,\n        $.H4,\n        $.H5,\n        $.H6,\n        $.HEAD,\n        $.HEADER,\n        $.HGROUP,\n        $.HR,\n        $.HTML,\n        $.IFRAME,\n        $.IMG,\n        $.INPUT,\n        $.LI,\n        $.LINK,\n        $.LISTING,\n        $.MAIN,\n        $.MARQUEE,\n        $.MENU,\n        $.META,\n        $.NAV,\n        $.NOEMBED,\n        $.NOFRAMES,\n        $.NOSCRIPT,\n        $.OBJECT,\n        $.OL,\n        $.P,\n        $.PARAM,\n        $.PLAINTEXT,\n        $.PRE,\n        $.SCRIPT,\n        $.SECTION,\n        $.SELECT,\n        $.SOURCE,\n        $.STYLE,\n        $.SUMMARY,\n        $.TABLE,\n        $.TBODY,\n        $.TD,\n        $.TEMPLATE,\n        $.TEXTAREA,\n        $.TFOOT,\n        $.TH,\n        $.THEAD,\n        $.TITLE,\n        $.TR,\n        $.TRACK,\n        $.UL,\n        $.WBR,\n        $.XMP,\n    ]),\n    [NS.MATHML]: new Set([$.MI, $.MO, $.MN, $.MS, $.MTEXT, $.ANNOTATION_XML]),\n    [NS.SVG]: new Set([$.TITLE, $.FOREIGN_OBJECT, $.DESC]),\n    [NS.XLINK]: new Set(),\n    [NS.XML]: new Set(),\n    [NS.XMLNS]: new Set(),\n};\nexport const NUMBERED_HEADERS = new Set([$.H1, $.H2, $.H3, $.H4, $.H5, $.H6]);\nconst UNESCAPED_TEXT = new Set([\n    TAG_NAMES.STYLE,\n    TAG_NAMES.SCRIPT,\n    TAG_NAMES.XMP,\n    TAG_NAMES.IFRAME,\n    TAG_NAMES.NOEMBED,\n    TAG_NAMES.NOFRAMES,\n    TAG_NAMES.PLAINTEXT,\n]);\nexport function hasUnescapedText(tn, scriptingEnabled) {\n    return UNESCAPED_TEXT.has(tn) || (scriptingEnabled && tn === TAG_NAMES.NOSCRIPT);\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;;;;;;;AAC3B,IAAI;AACX,CAAC,SAAU,EAAE;IACT,EAAE,CAAC,OAAO,GAAG;IACb,EAAE,CAAC,SAAS,GAAG;IACf,EAAE,CAAC,MAAM,GAAG;IACZ,EAAE,CAAC,QAAQ,GAAG;IACd,EAAE,CAAC,MAAM,GAAG;IACZ,EAAE,CAAC,QAAQ,GAAG;AAClB,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;AACV,IAAI;AACX,CAAC,SAAU,KAAK;IACZ,KAAK,CAAC,OAAO,GAAG;IAChB,KAAK,CAAC,SAAS,GAAG;IAClB,KAAK,CAAC,WAAW,GAAG;IACpB,KAAK,CAAC,SAAS,GAAG;IAClB,KAAK,CAAC,OAAO,GAAG;IAChB,KAAK,CAAC,QAAQ,GAAG;IACjB,KAAK,CAAC,OAAO,GAAG;IAChB,KAAK,CAAC,OAAO,GAAG;AACpB,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;AAMhB,IAAI;AACX,CAAC,SAAU,aAAa;IACpB,aAAa,CAAC,YAAY,GAAG;IAC7B,aAAa,CAAC,SAAS,GAAG;IAC1B,aAAa,CAAC,iBAAiB,GAAG;AACtC,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;AAChC,IAAI;AACX,CAAC,SAAU,SAAS;IAChB,SAAS,CAAC,IAAI,GAAG;IACjB,SAAS,CAAC,UAAU,GAAG;IACvB,SAAS,CAAC,iBAAiB,GAAG;IAC9B,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,UAAU,GAAG;IACvB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,IAAI,GAAG;IACjB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,WAAW,GAAG;IACxB,SAAS,CAAC,UAAU,GAAG;IACvB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,aAAa,GAAG;IAC1B,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,UAAU,GAAG;IACvB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,WAAW,GAAG;IACxB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,UAAU,GAAG;IACvB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,WAAW,GAAG;IACxB,SAAS,CAAC,aAAa,GAAG;IAC1B,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,iBAAiB,GAAG;IAC9B,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,WAAW,GAAG;IACxB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,IAAI,GAAG;IACjB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,UAAU,GAAG;IACvB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,aAAa,GAAG;IAC1B,SAAS,CAAC,UAAU,GAAG;IACvB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,WAAW,GAAG;IACxB,SAAS,CAAC,UAAU,GAAG;IACvB,SAAS,CAAC,WAAW,GAAG;IACxB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,WAAW,GAAG;IACxB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,IAAI,GAAG;IACjB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,YAAY,GAAG;IACzB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,IAAI,GAAG;IACjB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,UAAU,GAAG;IACvB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,OAAO,GAAG;IACpB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,SAAS,GAAG;IACtB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,UAAU,GAAG;IACvB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,WAAW,GAAG;IACxB,SAAS,CAAC,WAAW,GAAG;IACxB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,IAAI,GAAG;IACjB,SAAS,CAAC,KAAK,GAAG;IAClB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,MAAM,GAAG;IACnB,SAAS,CAAC,MAAM,GAAG;AACvB,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAMxB,IAAI;AACX,CAAC,SAAU,MAAM;IACb,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,EAAE,GAAG;IAC1B,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,iBAAiB,GAAG,EAAE,GAAG;IACvC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,EAAE,GAAG;IAC/B,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,EAAE,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,EAAE,GAAG;IAC1B,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,EAAE,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,GAAG,GAAG;IAClC,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG;IACjC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,aAAa,GAAG,GAAG,GAAG;IACpC,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG;IACjC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,GAAG,GAAG;IAClC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG;IACjC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG;IAC/B,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,GAAG,GAAG;IAClC,MAAM,CAAC,MAAM,CAAC,aAAa,GAAG,GAAG,GAAG;IACpC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,iBAAiB,GAAG,GAAG,GAAG;IACxC,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG;IAC/B,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,GAAG,GAAG;IAClC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,GAAG,GAAG;IAC3B,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG;IAC/B,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG;IAC/B,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG;IAC/B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG;IACjC,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,aAAa,GAAG,GAAG,GAAG;IACpC,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG;IACjC,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG;IAC/B,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,GAAG,GAAG;IAClC,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG;IACjC,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,GAAG,GAAG;IAClC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,GAAG,GAAG;IAClC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,GAAG,GAAG;IAC3B,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG;IAC/B,MAAM,CAAC,MAAM,CAAC,YAAY,GAAG,GAAG,GAAG;IACnC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,GAAG,GAAG;IAC3B,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG;IACjC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG;IAC/B,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,GAAG;IACjC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,GAAG;IACjC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,GAAG;IAClC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,GAAG;IACnC,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,GAAG;IACnC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG;IAChC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,GAAG;IAC5B,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG;IAC7B,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,GAAG;IAC9B,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,GAAG;AAClC,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;AACzB,MAAM,iBAAiB,IAAI,IAAI;IAC3B;QAAC,UAAU,CAAC;QAAE,OAAO,CAAC;KAAC;IACvB;QAAC,UAAU,OAAO;QAAE,OAAO,OAAO;KAAC;IACnC;QAAC,UAAU,cAAc;QAAE,OAAO,cAAc;KAAC;IACjD;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,OAAO;QAAE,OAAO,OAAO;KAAC;IACnC;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,CAAC;QAAE,OAAO,CAAC;KAAC;IACvB;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,QAAQ;QAAE,OAAO,QAAQ;KAAC;IACrC;QAAC,UAAU,OAAO;QAAE,OAAO,OAAO;KAAC;IACnC;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,UAAU;QAAE,OAAO,UAAU;KAAC;IACzC;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,OAAO;QAAE,OAAO,OAAO;KAAC;IACnC;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,QAAQ;QAAE,OAAO,QAAQ;KAAC;IACrC;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,OAAO;QAAE,OAAO,OAAO;KAAC;IACnC;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,QAAQ;QAAE,OAAO,QAAQ;KAAC;IACrC;QAAC,UAAU,UAAU;QAAE,OAAO,UAAU;KAAC;IACzC;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,cAAc;QAAE,OAAO,cAAc;KAAC;IACjD;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,QAAQ;QAAE,OAAO,QAAQ;KAAC;IACrC;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,CAAC;QAAE,OAAO,CAAC;KAAC;IACvB;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,OAAO;QAAE,OAAO,OAAO;KAAC;IACnC;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,UAAU;QAAE,OAAO,UAAU;KAAC;IACzC;QAAC,UAAU,OAAO;QAAE,OAAO,OAAO;KAAC;IACnC;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,QAAQ;QAAE,OAAO,QAAQ;KAAC;IACrC;QAAC,UAAU,OAAO;QAAE,OAAO,OAAO;KAAC;IACnC;QAAC,UAAU,QAAQ;QAAE,OAAO,QAAQ;KAAC;IACrC;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,QAAQ;QAAE,OAAO,QAAQ;KAAC;IACrC;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,CAAC;QAAE,OAAO,CAAC;KAAC;IACvB;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,SAAS;QAAE,OAAO,SAAS;KAAC;IACvC;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,CAAC;QAAE,OAAO,CAAC;KAAC;IACvB;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,OAAO;QAAE,OAAO,OAAO;KAAC;IACnC;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,IAAI;QAAE,OAAO,IAAI;KAAC;IAC7B;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,MAAM;QAAE,OAAO,MAAM;KAAC;IACjC;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,OAAO;QAAE,OAAO,OAAO;KAAC;IACnC;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,QAAQ;QAAE,OAAO,QAAQ;KAAC;IACrC;QAAC,UAAU,QAAQ;QAAE,OAAO,QAAQ;KAAC;IACrC;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,KAAK;QAAE,OAAO,KAAK;KAAC;IAC/B;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,CAAC;QAAE,OAAO,CAAC;KAAC;IACvB;QAAC,UAAU,EAAE;QAAE,OAAO,EAAE;KAAC;IACzB;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;IAC3B;QAAC,UAAU,GAAG;QAAE,OAAO,GAAG;KAAC;CAC9B;AACM,SAAS,SAAS,OAAO;IAC5B,IAAI;IACJ,OAAO,CAAC,KAAK,eAAe,GAAG,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,OAAO;AAC7F;AACA,MAAM,IAAI;AACH,MAAM,mBAAmB;IAC5B,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,IAAI;QACf,EAAE,OAAO;QACT,EAAE,MAAM;QACR,EAAE,IAAI;QACN,EAAE,OAAO;QACT,EAAE,KAAK;QACP,EAAE,IAAI;QACN,EAAE,QAAQ;QACV,EAAE,OAAO;QACT,EAAE,UAAU;QACZ,EAAE,IAAI;QACN,EAAE,EAAE;QACJ,EAAE,MAAM;QACR,EAAE,OAAO;QACT,EAAE,MAAM;QACR,EAAE,GAAG;QACL,EAAE,QAAQ;QACV,EAAE,EAAE;QACJ,EAAE,OAAO;QACT,EAAE,GAAG;QACL,EAAE,GAAG;QACL,EAAE,EAAE;QACJ,EAAE,EAAE;QACJ,EAAE,KAAK;QACP,EAAE,QAAQ;QACV,EAAE,UAAU;QACZ,EAAE,MAAM;QACR,EAAE,MAAM;QACR,EAAE,IAAI;QACN,EAAE,KAAK;QACP,EAAE,QAAQ;QACV,EAAE,EAAE;QACJ,EAAE,EAAE;QACJ,EAAE,EAAE;QACJ,EAAE,EAAE;QACJ,EAAE,EAAE;QACJ,EAAE,EAAE;QACJ,EAAE,IAAI;QACN,EAAE,MAAM;QACR,EAAE,MAAM;QACR,EAAE,EAAE;QACJ,EAAE,IAAI;QACN,EAAE,MAAM;QACR,EAAE,GAAG;QACL,EAAE,KAAK;QACP,EAAE,EAAE;QACJ,EAAE,IAAI;QACN,EAAE,OAAO;QACT,EAAE,IAAI;QACN,EAAE,OAAO;QACT,EAAE,IAAI;QACN,EAAE,IAAI;QACN,EAAE,GAAG;QACL,EAAE,OAAO;QACT,EAAE,QAAQ;QACV,EAAE,QAAQ;QACV,EAAE,MAAM;QACR,EAAE,EAAE;QACJ,EAAE,CAAC;QACH,EAAE,KAAK;QACP,EAAE,SAAS;QACX,EAAE,GAAG;QACL,EAAE,MAAM;QACR,EAAE,OAAO;QACT,EAAE,MAAM;QACR,EAAE,MAAM;QACR,EAAE,KAAK;QACP,EAAE,OAAO;QACT,EAAE,KAAK;QACP,EAAE,KAAK;QACP,EAAE,EAAE;QACJ,EAAE,QAAQ;QACV,EAAE,QAAQ;QACV,EAAE,KAAK;QACP,EAAE,EAAE;QACJ,EAAE,KAAK;QACP,EAAE,KAAK;QACP,EAAE,EAAE;QACJ,EAAE,KAAK;QACP,EAAE,EAAE;QACJ,EAAE,GAAG;QACL,EAAE,GAAG;KACR;IACD,CAAC,GAAG,MAAM,CAAC,EAAE,IAAI,IAAI;QAAC,EAAE,EAAE;QAAE,EAAE,EAAE;QAAE,EAAE,EAAE;QAAE,EAAE,EAAE;QAAE,EAAE,KAAK;QAAE,EAAE,cAAc;KAAC;IACxE,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,IAAI;QAAC,EAAE,KAAK;QAAE,EAAE,cAAc;QAAE,EAAE,IAAI;KAAC;IACrD,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI;IAChB,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI;IACd,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI;AACpB;AACO,MAAM,mBAAmB,IAAI,IAAI;IAAC,EAAE,EAAE;IAAE,EAAE,EAAE;IAAE,EAAE,EAAE;IAAE,EAAE,EAAE;IAAE,EAAE,EAAE;IAAE,EAAE,EAAE;CAAC;AAC5E,MAAM,iBAAiB,IAAI,IAAI;IAC3B,UAAU,KAAK;IACf,UAAU,MAAM;IAChB,UAAU,GAAG;IACb,UAAU,MAAM;IAChB,UAAU,OAAO;IACjB,UAAU,QAAQ;IAClB,UAAU,SAAS;CACtB;AACM,SAAS,iBAAiB,EAAE,EAAE,gBAAgB;IACjD,OAAO,eAAe,GAAG,CAAC,OAAQ,oBAAoB,OAAO,UAAU,QAAQ;AACnF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/tokenizer/index.js"], "sourcesContent": ["import { Preprocessor } from './preprocessor.js';\nimport { CODE_POINTS as $, SEQUENCES as $$, REPLACEMENT_CHARACTER, isSurrogate, isUndefinedCodePoint, isControlCodePoint, } from '../common/unicode.js';\nimport { TokenType, getTokenAttr, } from '../common/token.js';\nimport { htmlDecodeTree, EntityDecoder, DecodingMode } from 'entities/decode';\nimport { ERR } from '../common/error-codes.js';\nimport { TAG_ID, getTagID } from '../common/html.js';\n//States\nvar State;\n(function (State) {\n    State[State[\"DATA\"] = 0] = \"DATA\";\n    State[State[\"RCDATA\"] = 1] = \"RCDATA\";\n    State[State[\"RAWTEXT\"] = 2] = \"RAWTEXT\";\n    State[State[\"SCRIPT_DATA\"] = 3] = \"SCRIPT_DATA\";\n    State[State[\"PLAINTEXT\"] = 4] = \"PLAINTEXT\";\n    State[State[\"TAG_OPEN\"] = 5] = \"TAG_OPEN\";\n    State[State[\"END_TAG_OPEN\"] = 6] = \"END_TAG_OPEN\";\n    State[State[\"TAG_NAME\"] = 7] = \"TAG_NAME\";\n    State[State[\"RCDATA_LESS_THAN_SIGN\"] = 8] = \"RCDATA_LESS_THAN_SIGN\";\n    State[State[\"RCDATA_END_TAG_OPEN\"] = 9] = \"RCDATA_END_TAG_OPEN\";\n    State[State[\"RCDATA_END_TAG_NAME\"] = 10] = \"RCDATA_END_TAG_NAME\";\n    State[State[\"RAWTEXT_LESS_THAN_SIGN\"] = 11] = \"RAWTEXT_LESS_THAN_SIGN\";\n    State[State[\"RAWTEXT_END_TAG_OPEN\"] = 12] = \"RAWTEXT_END_TAG_OPEN\";\n    State[State[\"RAWTEXT_END_TAG_NAME\"] = 13] = \"RAWTEXT_END_TAG_NAME\";\n    State[State[\"SCRIPT_DATA_LESS_THAN_SIGN\"] = 14] = \"SCRIPT_DATA_LESS_THAN_SIGN\";\n    State[State[\"SCRIPT_DATA_END_TAG_OPEN\"] = 15] = \"SCRIPT_DATA_END_TAG_OPEN\";\n    State[State[\"SCRIPT_DATA_END_TAG_NAME\"] = 16] = \"SCRIPT_DATA_END_TAG_NAME\";\n    State[State[\"SCRIPT_DATA_ESCAPE_START\"] = 17] = \"SCRIPT_DATA_ESCAPE_START\";\n    State[State[\"SCRIPT_DATA_ESCAPE_START_DASH\"] = 18] = \"SCRIPT_DATA_ESCAPE_START_DASH\";\n    State[State[\"SCRIPT_DATA_ESCAPED\"] = 19] = \"SCRIPT_DATA_ESCAPED\";\n    State[State[\"SCRIPT_DATA_ESCAPED_DASH\"] = 20] = \"SCRIPT_DATA_ESCAPED_DASH\";\n    State[State[\"SCRIPT_DATA_ESCAPED_DASH_DASH\"] = 21] = \"SCRIPT_DATA_ESCAPED_DASH_DASH\";\n    State[State[\"SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN\"] = 22] = \"SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN\";\n    State[State[\"SCRIPT_DATA_ESCAPED_END_TAG_OPEN\"] = 23] = \"SCRIPT_DATA_ESCAPED_END_TAG_OPEN\";\n    State[State[\"SCRIPT_DATA_ESCAPED_END_TAG_NAME\"] = 24] = \"SCRIPT_DATA_ESCAPED_END_TAG_NAME\";\n    State[State[\"SCRIPT_DATA_DOUBLE_ESCAPE_START\"] = 25] = \"SCRIPT_DATA_DOUBLE_ESCAPE_START\";\n    State[State[\"SCRIPT_DATA_DOUBLE_ESCAPED\"] = 26] = \"SCRIPT_DATA_DOUBLE_ESCAPED\";\n    State[State[\"SCRIPT_DATA_DOUBLE_ESCAPED_DASH\"] = 27] = \"SCRIPT_DATA_DOUBLE_ESCAPED_DASH\";\n    State[State[\"SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH\"] = 28] = \"SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH\";\n    State[State[\"SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN\"] = 29] = \"SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN\";\n    State[State[\"SCRIPT_DATA_DOUBLE_ESCAPE_END\"] = 30] = \"SCRIPT_DATA_DOUBLE_ESCAPE_END\";\n    State[State[\"BEFORE_ATTRIBUTE_NAME\"] = 31] = \"BEFORE_ATTRIBUTE_NAME\";\n    State[State[\"ATTRIBUTE_NAME\"] = 32] = \"ATTRIBUTE_NAME\";\n    State[State[\"AFTER_ATTRIBUTE_NAME\"] = 33] = \"AFTER_ATTRIBUTE_NAME\";\n    State[State[\"BEFORE_ATTRIBUTE_VALUE\"] = 34] = \"BEFORE_ATTRIBUTE_VALUE\";\n    State[State[\"ATTRIBUTE_VALUE_DOUBLE_QUOTED\"] = 35] = \"ATTRIBUTE_VALUE_DOUBLE_QUOTED\";\n    State[State[\"ATTRIBUTE_VALUE_SINGLE_QUOTED\"] = 36] = \"ATTRIBUTE_VALUE_SINGLE_QUOTED\";\n    State[State[\"ATTRIBUTE_VALUE_UNQUOTED\"] = 37] = \"ATTRIBUTE_VALUE_UNQUOTED\";\n    State[State[\"AFTER_ATTRIBUTE_VALUE_QUOTED\"] = 38] = \"AFTER_ATTRIBUTE_VALUE_QUOTED\";\n    State[State[\"SELF_CLOSING_START_TAG\"] = 39] = \"SELF_CLOSING_START_TAG\";\n    State[State[\"BOGUS_COMMENT\"] = 40] = \"BOGUS_COMMENT\";\n    State[State[\"MARKUP_DECLARATION_OPEN\"] = 41] = \"MARKUP_DECLARATION_OPEN\";\n    State[State[\"COMMENT_START\"] = 42] = \"COMMENT_START\";\n    State[State[\"COMMENT_START_DASH\"] = 43] = \"COMMENT_START_DASH\";\n    State[State[\"COMMENT\"] = 44] = \"COMMENT\";\n    State[State[\"COMMENT_LESS_THAN_SIGN\"] = 45] = \"COMMENT_LESS_THAN_SIGN\";\n    State[State[\"COMMENT_LESS_THAN_SIGN_BANG\"] = 46] = \"COMMENT_LESS_THAN_SIGN_BANG\";\n    State[State[\"COMMENT_LESS_THAN_SIGN_BANG_DASH\"] = 47] = \"COMMENT_LESS_THAN_SIGN_BANG_DASH\";\n    State[State[\"COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH\"] = 48] = \"COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH\";\n    State[State[\"COMMENT_END_DASH\"] = 49] = \"COMMENT_END_DASH\";\n    State[State[\"COMMENT_END\"] = 50] = \"COMMENT_END\";\n    State[State[\"COMMENT_END_BANG\"] = 51] = \"COMMENT_END_BANG\";\n    State[State[\"DOCTYPE\"] = 52] = \"DOCTYPE\";\n    State[State[\"BEFORE_DOCTYPE_NAME\"] = 53] = \"BEFORE_DOCTYPE_NAME\";\n    State[State[\"DOCTYPE_NAME\"] = 54] = \"DOCTYPE_NAME\";\n    State[State[\"AFTER_DOCTYPE_NAME\"] = 55] = \"AFTER_DOCTYPE_NAME\";\n    State[State[\"AFTER_DOCTYPE_PUBLIC_KEYWORD\"] = 56] = \"AFTER_DOCTYPE_PUBLIC_KEYWORD\";\n    State[State[\"BEFORE_DOCTYPE_PUBLIC_IDENTIFIER\"] = 57] = \"BEFORE_DOCTYPE_PUBLIC_IDENTIFIER\";\n    State[State[\"DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED\"] = 58] = \"DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED\";\n    State[State[\"DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED\"] = 59] = \"DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED\";\n    State[State[\"AFTER_DOCTYPE_PUBLIC_IDENTIFIER\"] = 60] = \"AFTER_DOCTYPE_PUBLIC_IDENTIFIER\";\n    State[State[\"BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS\"] = 61] = \"BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS\";\n    State[State[\"AFTER_DOCTYPE_SYSTEM_KEYWORD\"] = 62] = \"AFTER_DOCTYPE_SYSTEM_KEYWORD\";\n    State[State[\"BEFORE_DOCTYPE_SYSTEM_IDENTIFIER\"] = 63] = \"BEFORE_DOCTYPE_SYSTEM_IDENTIFIER\";\n    State[State[\"DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED\"] = 64] = \"DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED\";\n    State[State[\"DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED\"] = 65] = \"DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED\";\n    State[State[\"AFTER_DOCTYPE_SYSTEM_IDENTIFIER\"] = 66] = \"AFTER_DOCTYPE_SYSTEM_IDENTIFIER\";\n    State[State[\"BOGUS_DOCTYPE\"] = 67] = \"BOGUS_DOCTYPE\";\n    State[State[\"CDATA_SECTION\"] = 68] = \"CDATA_SECTION\";\n    State[State[\"CDATA_SECTION_BRACKET\"] = 69] = \"CDATA_SECTION_BRACKET\";\n    State[State[\"CDATA_SECTION_END\"] = 70] = \"CDATA_SECTION_END\";\n    State[State[\"CHARACTER_REFERENCE\"] = 71] = \"CHARACTER_REFERENCE\";\n    State[State[\"AMBIGUOUS_AMPERSAND\"] = 72] = \"AMBIGUOUS_AMPERSAND\";\n})(State || (State = {}));\n//Tokenizer initial states for different modes\nexport const TokenizerMode = {\n    DATA: State.DATA,\n    RCDATA: State.RCDATA,\n    RAWTEXT: State.RAWTEXT,\n    SCRIPT_DATA: State.SCRIPT_DATA,\n    PLAINTEXT: State.PLAINTEXT,\n    CDATA_SECTION: State.CDATA_SECTION,\n};\n//Utils\n//OPTIMIZATION: these utility functions should not be moved out of this module. V8 Crankshaft will not inline\n//this functions if they will be situated in another module due to context switch.\n//Always perform inlining check before modifying this functions ('node --trace-inlining').\nfunction isAsciiDigit(cp) {\n    return cp >= $.DIGIT_0 && cp <= $.DIGIT_9;\n}\nfunction isAsciiUpper(cp) {\n    return cp >= $.LATIN_CAPITAL_A && cp <= $.LATIN_CAPITAL_Z;\n}\nfunction isAsciiLower(cp) {\n    return cp >= $.LATIN_SMALL_A && cp <= $.LATIN_SMALL_Z;\n}\nfunction isAsciiLetter(cp) {\n    return isAsciiLower(cp) || isAsciiUpper(cp);\n}\nfunction isAsciiAlphaNumeric(cp) {\n    return isAsciiLetter(cp) || isAsciiDigit(cp);\n}\nfunction toAsciiLower(cp) {\n    return cp + 32;\n}\nfunction isWhitespace(cp) {\n    return cp === $.SPACE || cp === $.LINE_FEED || cp === $.TABULATION || cp === $.FORM_FEED;\n}\nfunction isScriptDataDoubleEscapeSequenceEnd(cp) {\n    return isWhitespace(cp) || cp === $.SOLIDUS || cp === $.GREATER_THAN_SIGN;\n}\nfunction getErrorForNumericCharacterReference(code) {\n    if (code === $.NULL) {\n        return ERR.nullCharacterReference;\n    }\n    else if (code > 1114111) {\n        return ERR.characterReferenceOutsideUnicodeRange;\n    }\n    else if (isSurrogate(code)) {\n        return ERR.surrogateCharacterReference;\n    }\n    else if (isUndefinedCodePoint(code)) {\n        return ERR.noncharacterCharacterReference;\n    }\n    else if (isControlCodePoint(code) || code === $.CARRIAGE_RETURN) {\n        return ERR.controlCharacterReference;\n    }\n    return null;\n}\n//Tokenizer\nexport class Tokenizer {\n    constructor(options, handler) {\n        this.options = options;\n        this.handler = handler;\n        this.paused = false;\n        /** Ensures that the parsing loop isn't run multiple times at once. */\n        this.inLoop = false;\n        /**\n         * Indicates that the current adjusted node exists, is not an element in the HTML namespace,\n         * and that it is not an integration point for either MathML or HTML.\n         *\n         * @see {@link https://html.spec.whatwg.org/multipage/parsing.html#tree-construction}\n         */\n        this.inForeignNode = false;\n        this.lastStartTagName = '';\n        this.active = false;\n        this.state = State.DATA;\n        this.returnState = State.DATA;\n        this.entityStartPos = 0;\n        this.consumedAfterSnapshot = -1;\n        this.currentCharacterToken = null;\n        this.currentToken = null;\n        this.currentAttr = { name: '', value: '' };\n        this.preprocessor = new Preprocessor(handler);\n        this.currentLocation = this.getCurrentLocation(-1);\n        this.entityDecoder = new EntityDecoder(htmlDecodeTree, (cp, consumed) => {\n            // Note: Set `pos` _before_ flushing, as flushing might drop\n            // the current chunk and invalidate `entityStartPos`.\n            this.preprocessor.pos = this.entityStartPos + consumed - 1;\n            this._flushCodePointConsumedAsCharacterReference(cp);\n        }, handler.onParseError\n            ? {\n                missingSemicolonAfterCharacterReference: () => {\n                    this._err(ERR.missingSemicolonAfterCharacterReference, 1);\n                },\n                absenceOfDigitsInNumericCharacterReference: (consumed) => {\n                    this._err(ERR.absenceOfDigitsInNumericCharacterReference, this.entityStartPos - this.preprocessor.pos + consumed);\n                },\n                validateNumericCharacterReference: (code) => {\n                    const error = getErrorForNumericCharacterReference(code);\n                    if (error)\n                        this._err(error, 1);\n                },\n            }\n            : undefined);\n    }\n    //Errors\n    _err(code, cpOffset = 0) {\n        var _a, _b;\n        (_b = (_a = this.handler).onParseError) === null || _b === void 0 ? void 0 : _b.call(_a, this.preprocessor.getError(code, cpOffset));\n    }\n    // NOTE: `offset` may never run across line boundaries.\n    getCurrentLocation(offset) {\n        if (!this.options.sourceCodeLocationInfo) {\n            return null;\n        }\n        return {\n            startLine: this.preprocessor.line,\n            startCol: this.preprocessor.col - offset,\n            startOffset: this.preprocessor.offset - offset,\n            endLine: -1,\n            endCol: -1,\n            endOffset: -1,\n        };\n    }\n    _runParsingLoop() {\n        if (this.inLoop)\n            return;\n        this.inLoop = true;\n        while (this.active && !this.paused) {\n            this.consumedAfterSnapshot = 0;\n            const cp = this._consume();\n            if (!this._ensureHibernation()) {\n                this._callState(cp);\n            }\n        }\n        this.inLoop = false;\n    }\n    //API\n    pause() {\n        this.paused = true;\n    }\n    resume(writeCallback) {\n        if (!this.paused) {\n            throw new Error('Parser was already resumed');\n        }\n        this.paused = false;\n        // Necessary for synchronous resume.\n        if (this.inLoop)\n            return;\n        this._runParsingLoop();\n        if (!this.paused) {\n            writeCallback === null || writeCallback === void 0 ? void 0 : writeCallback();\n        }\n    }\n    write(chunk, isLastChunk, writeCallback) {\n        this.active = true;\n        this.preprocessor.write(chunk, isLastChunk);\n        this._runParsingLoop();\n        if (!this.paused) {\n            writeCallback === null || writeCallback === void 0 ? void 0 : writeCallback();\n        }\n    }\n    insertHtmlAtCurrentPos(chunk) {\n        this.active = true;\n        this.preprocessor.insertHtmlAtCurrentPos(chunk);\n        this._runParsingLoop();\n    }\n    //Hibernation\n    _ensureHibernation() {\n        if (this.preprocessor.endOfChunkHit) {\n            this.preprocessor.retreat(this.consumedAfterSnapshot);\n            this.consumedAfterSnapshot = 0;\n            this.active = false;\n            return true;\n        }\n        return false;\n    }\n    //Consumption\n    _consume() {\n        this.consumedAfterSnapshot++;\n        return this.preprocessor.advance();\n    }\n    _advanceBy(count) {\n        this.consumedAfterSnapshot += count;\n        for (let i = 0; i < count; i++) {\n            this.preprocessor.advance();\n        }\n    }\n    _consumeSequenceIfMatch(pattern, caseSensitive) {\n        if (this.preprocessor.startsWith(pattern, caseSensitive)) {\n            // We will already have consumed one character before calling this method.\n            this._advanceBy(pattern.length - 1);\n            return true;\n        }\n        return false;\n    }\n    //Token creation\n    _createStartTagToken() {\n        this.currentToken = {\n            type: TokenType.START_TAG,\n            tagName: '',\n            tagID: TAG_ID.UNKNOWN,\n            selfClosing: false,\n            ackSelfClosing: false,\n            attrs: [],\n            location: this.getCurrentLocation(1),\n        };\n    }\n    _createEndTagToken() {\n        this.currentToken = {\n            type: TokenType.END_TAG,\n            tagName: '',\n            tagID: TAG_ID.UNKNOWN,\n            selfClosing: false,\n            ackSelfClosing: false,\n            attrs: [],\n            location: this.getCurrentLocation(2),\n        };\n    }\n    _createCommentToken(offset) {\n        this.currentToken = {\n            type: TokenType.COMMENT,\n            data: '',\n            location: this.getCurrentLocation(offset),\n        };\n    }\n    _createDoctypeToken(initialName) {\n        this.currentToken = {\n            type: TokenType.DOCTYPE,\n            name: initialName,\n            forceQuirks: false,\n            publicId: null,\n            systemId: null,\n            location: this.currentLocation,\n        };\n    }\n    _createCharacterToken(type, chars) {\n        this.currentCharacterToken = {\n            type,\n            chars,\n            location: this.currentLocation,\n        };\n    }\n    //Tag attributes\n    _createAttr(attrNameFirstCh) {\n        this.currentAttr = {\n            name: attrNameFirstCh,\n            value: '',\n        };\n        this.currentLocation = this.getCurrentLocation(0);\n    }\n    _leaveAttrName() {\n        var _a;\n        var _b;\n        const token = this.currentToken;\n        if (getTokenAttr(token, this.currentAttr.name) === null) {\n            token.attrs.push(this.currentAttr);\n            if (token.location && this.currentLocation) {\n                const attrLocations = ((_a = (_b = token.location).attrs) !== null && _a !== void 0 ? _a : (_b.attrs = Object.create(null)));\n                attrLocations[this.currentAttr.name] = this.currentLocation;\n                // Set end location\n                this._leaveAttrValue();\n            }\n        }\n        else {\n            this._err(ERR.duplicateAttribute);\n        }\n    }\n    _leaveAttrValue() {\n        if (this.currentLocation) {\n            this.currentLocation.endLine = this.preprocessor.line;\n            this.currentLocation.endCol = this.preprocessor.col;\n            this.currentLocation.endOffset = this.preprocessor.offset;\n        }\n    }\n    //Token emission\n    prepareToken(ct) {\n        this._emitCurrentCharacterToken(ct.location);\n        this.currentToken = null;\n        if (ct.location) {\n            ct.location.endLine = this.preprocessor.line;\n            ct.location.endCol = this.preprocessor.col + 1;\n            ct.location.endOffset = this.preprocessor.offset + 1;\n        }\n        this.currentLocation = this.getCurrentLocation(-1);\n    }\n    emitCurrentTagToken() {\n        const ct = this.currentToken;\n        this.prepareToken(ct);\n        ct.tagID = getTagID(ct.tagName);\n        if (ct.type === TokenType.START_TAG) {\n            this.lastStartTagName = ct.tagName;\n            this.handler.onStartTag(ct);\n        }\n        else {\n            if (ct.attrs.length > 0) {\n                this._err(ERR.endTagWithAttributes);\n            }\n            if (ct.selfClosing) {\n                this._err(ERR.endTagWithTrailingSolidus);\n            }\n            this.handler.onEndTag(ct);\n        }\n        this.preprocessor.dropParsedChunk();\n    }\n    emitCurrentComment(ct) {\n        this.prepareToken(ct);\n        this.handler.onComment(ct);\n        this.preprocessor.dropParsedChunk();\n    }\n    emitCurrentDoctype(ct) {\n        this.prepareToken(ct);\n        this.handler.onDoctype(ct);\n        this.preprocessor.dropParsedChunk();\n    }\n    _emitCurrentCharacterToken(nextLocation) {\n        if (this.currentCharacterToken) {\n            //NOTE: if we have a pending character token, make it's end location equal to the\n            //current token's start location.\n            if (nextLocation && this.currentCharacterToken.location) {\n                this.currentCharacterToken.location.endLine = nextLocation.startLine;\n                this.currentCharacterToken.location.endCol = nextLocation.startCol;\n                this.currentCharacterToken.location.endOffset = nextLocation.startOffset;\n            }\n            switch (this.currentCharacterToken.type) {\n                case TokenType.CHARACTER: {\n                    this.handler.onCharacter(this.currentCharacterToken);\n                    break;\n                }\n                case TokenType.NULL_CHARACTER: {\n                    this.handler.onNullCharacter(this.currentCharacterToken);\n                    break;\n                }\n                case TokenType.WHITESPACE_CHARACTER: {\n                    this.handler.onWhitespaceCharacter(this.currentCharacterToken);\n                    break;\n                }\n            }\n            this.currentCharacterToken = null;\n        }\n    }\n    _emitEOFToken() {\n        const location = this.getCurrentLocation(0);\n        if (location) {\n            location.endLine = location.startLine;\n            location.endCol = location.startCol;\n            location.endOffset = location.startOffset;\n        }\n        this._emitCurrentCharacterToken(location);\n        this.handler.onEof({ type: TokenType.EOF, location });\n        this.active = false;\n    }\n    //Characters emission\n    //OPTIMIZATION: The specification uses only one type of character token (one token per character).\n    //This causes a huge memory overhead and a lot of unnecessary parser loops. parse5 uses 3 groups of characters.\n    //If we have a sequence of characters that belong to the same group, the parser can process it\n    //as a single solid character token.\n    //So, there are 3 types of character tokens in parse5:\n    //1)TokenType.NULL_CHARACTER - \\u0000-character sequences (e.g. '\\u0000\\u0000\\u0000')\n    //2)TokenType.WHITESPACE_CHARACTER - any whitespace/new-line character sequences (e.g. '\\n  \\r\\t   \\f')\n    //3)TokenType.CHARACTER - any character sequence which don't belong to groups 1 and 2 (e.g. 'abcdef1234@@#$%^')\n    _appendCharToCurrentCharacterToken(type, ch) {\n        if (this.currentCharacterToken) {\n            if (this.currentCharacterToken.type === type) {\n                this.currentCharacterToken.chars += ch;\n                return;\n            }\n            else {\n                this.currentLocation = this.getCurrentLocation(0);\n                this._emitCurrentCharacterToken(this.currentLocation);\n                this.preprocessor.dropParsedChunk();\n            }\n        }\n        this._createCharacterToken(type, ch);\n    }\n    _emitCodePoint(cp) {\n        const type = isWhitespace(cp)\n            ? TokenType.WHITESPACE_CHARACTER\n            : cp === $.NULL\n                ? TokenType.NULL_CHARACTER\n                : TokenType.CHARACTER;\n        this._appendCharToCurrentCharacterToken(type, String.fromCodePoint(cp));\n    }\n    //NOTE: used when we emit characters explicitly.\n    //This is always for non-whitespace and non-null characters, which allows us to avoid additional checks.\n    _emitChars(ch) {\n        this._appendCharToCurrentCharacterToken(TokenType.CHARACTER, ch);\n    }\n    // Character reference helpers\n    _startCharacterReference() {\n        this.returnState = this.state;\n        this.state = State.CHARACTER_REFERENCE;\n        this.entityStartPos = this.preprocessor.pos;\n        this.entityDecoder.startEntity(this._isCharacterReferenceInAttribute() ? DecodingMode.Attribute : DecodingMode.Legacy);\n    }\n    _isCharacterReferenceInAttribute() {\n        return (this.returnState === State.ATTRIBUTE_VALUE_DOUBLE_QUOTED ||\n            this.returnState === State.ATTRIBUTE_VALUE_SINGLE_QUOTED ||\n            this.returnState === State.ATTRIBUTE_VALUE_UNQUOTED);\n    }\n    _flushCodePointConsumedAsCharacterReference(cp) {\n        if (this._isCharacterReferenceInAttribute()) {\n            this.currentAttr.value += String.fromCodePoint(cp);\n        }\n        else {\n            this._emitCodePoint(cp);\n        }\n    }\n    // Calling states this way turns out to be much faster than any other approach.\n    _callState(cp) {\n        switch (this.state) {\n            case State.DATA: {\n                this._stateData(cp);\n                break;\n            }\n            case State.RCDATA: {\n                this._stateRcdata(cp);\n                break;\n            }\n            case State.RAWTEXT: {\n                this._stateRawtext(cp);\n                break;\n            }\n            case State.SCRIPT_DATA: {\n                this._stateScriptData(cp);\n                break;\n            }\n            case State.PLAINTEXT: {\n                this._statePlaintext(cp);\n                break;\n            }\n            case State.TAG_OPEN: {\n                this._stateTagOpen(cp);\n                break;\n            }\n            case State.END_TAG_OPEN: {\n                this._stateEndTagOpen(cp);\n                break;\n            }\n            case State.TAG_NAME: {\n                this._stateTagName(cp);\n                break;\n            }\n            case State.RCDATA_LESS_THAN_SIGN: {\n                this._stateRcdataLessThanSign(cp);\n                break;\n            }\n            case State.RCDATA_END_TAG_OPEN: {\n                this._stateRcdataEndTagOpen(cp);\n                break;\n            }\n            case State.RCDATA_END_TAG_NAME: {\n                this._stateRcdataEndTagName(cp);\n                break;\n            }\n            case State.RAWTEXT_LESS_THAN_SIGN: {\n                this._stateRawtextLessThanSign(cp);\n                break;\n            }\n            case State.RAWTEXT_END_TAG_OPEN: {\n                this._stateRawtextEndTagOpen(cp);\n                break;\n            }\n            case State.RAWTEXT_END_TAG_NAME: {\n                this._stateRawtextEndTagName(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_LESS_THAN_SIGN: {\n                this._stateScriptDataLessThanSign(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_END_TAG_OPEN: {\n                this._stateScriptDataEndTagOpen(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_END_TAG_NAME: {\n                this._stateScriptDataEndTagName(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_ESCAPE_START: {\n                this._stateScriptDataEscapeStart(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_ESCAPE_START_DASH: {\n                this._stateScriptDataEscapeStartDash(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_ESCAPED: {\n                this._stateScriptDataEscaped(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_ESCAPED_DASH: {\n                this._stateScriptDataEscapedDash(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_ESCAPED_DASH_DASH: {\n                this._stateScriptDataEscapedDashDash(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN: {\n                this._stateScriptDataEscapedLessThanSign(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_ESCAPED_END_TAG_OPEN: {\n                this._stateScriptDataEscapedEndTagOpen(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_ESCAPED_END_TAG_NAME: {\n                this._stateScriptDataEscapedEndTagName(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_DOUBLE_ESCAPE_START: {\n                this._stateScriptDataDoubleEscapeStart(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_DOUBLE_ESCAPED: {\n                this._stateScriptDataDoubleEscaped(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_DOUBLE_ESCAPED_DASH: {\n                this._stateScriptDataDoubleEscapedDash(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH: {\n                this._stateScriptDataDoubleEscapedDashDash(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN: {\n                this._stateScriptDataDoubleEscapedLessThanSign(cp);\n                break;\n            }\n            case State.SCRIPT_DATA_DOUBLE_ESCAPE_END: {\n                this._stateScriptDataDoubleEscapeEnd(cp);\n                break;\n            }\n            case State.BEFORE_ATTRIBUTE_NAME: {\n                this._stateBeforeAttributeName(cp);\n                break;\n            }\n            case State.ATTRIBUTE_NAME: {\n                this._stateAttributeName(cp);\n                break;\n            }\n            case State.AFTER_ATTRIBUTE_NAME: {\n                this._stateAfterAttributeName(cp);\n                break;\n            }\n            case State.BEFORE_ATTRIBUTE_VALUE: {\n                this._stateBeforeAttributeValue(cp);\n                break;\n            }\n            case State.ATTRIBUTE_VALUE_DOUBLE_QUOTED: {\n                this._stateAttributeValueDoubleQuoted(cp);\n                break;\n            }\n            case State.ATTRIBUTE_VALUE_SINGLE_QUOTED: {\n                this._stateAttributeValueSingleQuoted(cp);\n                break;\n            }\n            case State.ATTRIBUTE_VALUE_UNQUOTED: {\n                this._stateAttributeValueUnquoted(cp);\n                break;\n            }\n            case State.AFTER_ATTRIBUTE_VALUE_QUOTED: {\n                this._stateAfterAttributeValueQuoted(cp);\n                break;\n            }\n            case State.SELF_CLOSING_START_TAG: {\n                this._stateSelfClosingStartTag(cp);\n                break;\n            }\n            case State.BOGUS_COMMENT: {\n                this._stateBogusComment(cp);\n                break;\n            }\n            case State.MARKUP_DECLARATION_OPEN: {\n                this._stateMarkupDeclarationOpen(cp);\n                break;\n            }\n            case State.COMMENT_START: {\n                this._stateCommentStart(cp);\n                break;\n            }\n            case State.COMMENT_START_DASH: {\n                this._stateCommentStartDash(cp);\n                break;\n            }\n            case State.COMMENT: {\n                this._stateComment(cp);\n                break;\n            }\n            case State.COMMENT_LESS_THAN_SIGN: {\n                this._stateCommentLessThanSign(cp);\n                break;\n            }\n            case State.COMMENT_LESS_THAN_SIGN_BANG: {\n                this._stateCommentLessThanSignBang(cp);\n                break;\n            }\n            case State.COMMENT_LESS_THAN_SIGN_BANG_DASH: {\n                this._stateCommentLessThanSignBangDash(cp);\n                break;\n            }\n            case State.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH: {\n                this._stateCommentLessThanSignBangDashDash(cp);\n                break;\n            }\n            case State.COMMENT_END_DASH: {\n                this._stateCommentEndDash(cp);\n                break;\n            }\n            case State.COMMENT_END: {\n                this._stateCommentEnd(cp);\n                break;\n            }\n            case State.COMMENT_END_BANG: {\n                this._stateCommentEndBang(cp);\n                break;\n            }\n            case State.DOCTYPE: {\n                this._stateDoctype(cp);\n                break;\n            }\n            case State.BEFORE_DOCTYPE_NAME: {\n                this._stateBeforeDoctypeName(cp);\n                break;\n            }\n            case State.DOCTYPE_NAME: {\n                this._stateDoctypeName(cp);\n                break;\n            }\n            case State.AFTER_DOCTYPE_NAME: {\n                this._stateAfterDoctypeName(cp);\n                break;\n            }\n            case State.AFTER_DOCTYPE_PUBLIC_KEYWORD: {\n                this._stateAfterDoctypePublicKeyword(cp);\n                break;\n            }\n            case State.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER: {\n                this._stateBeforeDoctypePublicIdentifier(cp);\n                break;\n            }\n            case State.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED: {\n                this._stateDoctypePublicIdentifierDoubleQuoted(cp);\n                break;\n            }\n            case State.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED: {\n                this._stateDoctypePublicIdentifierSingleQuoted(cp);\n                break;\n            }\n            case State.AFTER_DOCTYPE_PUBLIC_IDENTIFIER: {\n                this._stateAfterDoctypePublicIdentifier(cp);\n                break;\n            }\n            case State.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS: {\n                this._stateBetweenDoctypePublicAndSystemIdentifiers(cp);\n                break;\n            }\n            case State.AFTER_DOCTYPE_SYSTEM_KEYWORD: {\n                this._stateAfterDoctypeSystemKeyword(cp);\n                break;\n            }\n            case State.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER: {\n                this._stateBeforeDoctypeSystemIdentifier(cp);\n                break;\n            }\n            case State.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED: {\n                this._stateDoctypeSystemIdentifierDoubleQuoted(cp);\n                break;\n            }\n            case State.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED: {\n                this._stateDoctypeSystemIdentifierSingleQuoted(cp);\n                break;\n            }\n            case State.AFTER_DOCTYPE_SYSTEM_IDENTIFIER: {\n                this._stateAfterDoctypeSystemIdentifier(cp);\n                break;\n            }\n            case State.BOGUS_DOCTYPE: {\n                this._stateBogusDoctype(cp);\n                break;\n            }\n            case State.CDATA_SECTION: {\n                this._stateCdataSection(cp);\n                break;\n            }\n            case State.CDATA_SECTION_BRACKET: {\n                this._stateCdataSectionBracket(cp);\n                break;\n            }\n            case State.CDATA_SECTION_END: {\n                this._stateCdataSectionEnd(cp);\n                break;\n            }\n            case State.CHARACTER_REFERENCE: {\n                this._stateCharacterReference();\n                break;\n            }\n            case State.AMBIGUOUS_AMPERSAND: {\n                this._stateAmbiguousAmpersand(cp);\n                break;\n            }\n            default: {\n                throw new Error('Unknown state');\n            }\n        }\n    }\n    // State machine\n    // Data state\n    //------------------------------------------------------------------\n    _stateData(cp) {\n        switch (cp) {\n            case $.LESS_THAN_SIGN: {\n                this.state = State.TAG_OPEN;\n                break;\n            }\n            case $.AMPERSAND: {\n                this._startCharacterReference();\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this._emitCodePoint(cp);\n                break;\n            }\n            case $.EOF: {\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    //  RCDATA state\n    //------------------------------------------------------------------\n    _stateRcdata(cp) {\n        switch (cp) {\n            case $.AMPERSAND: {\n                this._startCharacterReference();\n                break;\n            }\n            case $.LESS_THAN_SIGN: {\n                this.state = State.RCDATA_LESS_THAN_SIGN;\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this._emitChars(REPLACEMENT_CHARACTER);\n                break;\n            }\n            case $.EOF: {\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    // RAWTEXT state\n    //------------------------------------------------------------------\n    _stateRawtext(cp) {\n        switch (cp) {\n            case $.LESS_THAN_SIGN: {\n                this.state = State.RAWTEXT_LESS_THAN_SIGN;\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this._emitChars(REPLACEMENT_CHARACTER);\n                break;\n            }\n            case $.EOF: {\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    // Script data state\n    //------------------------------------------------------------------\n    _stateScriptData(cp) {\n        switch (cp) {\n            case $.LESS_THAN_SIGN: {\n                this.state = State.SCRIPT_DATA_LESS_THAN_SIGN;\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this._emitChars(REPLACEMENT_CHARACTER);\n                break;\n            }\n            case $.EOF: {\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    // PLAINTEXT state\n    //------------------------------------------------------------------\n    _statePlaintext(cp) {\n        switch (cp) {\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this._emitChars(REPLACEMENT_CHARACTER);\n                break;\n            }\n            case $.EOF: {\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    // Tag open state\n    //------------------------------------------------------------------\n    _stateTagOpen(cp) {\n        if (isAsciiLetter(cp)) {\n            this._createStartTagToken();\n            this.state = State.TAG_NAME;\n            this._stateTagName(cp);\n        }\n        else\n            switch (cp) {\n                case $.EXCLAMATION_MARK: {\n                    this.state = State.MARKUP_DECLARATION_OPEN;\n                    break;\n                }\n                case $.SOLIDUS: {\n                    this.state = State.END_TAG_OPEN;\n                    break;\n                }\n                case $.QUESTION_MARK: {\n                    this._err(ERR.unexpectedQuestionMarkInsteadOfTagName);\n                    this._createCommentToken(1);\n                    this.state = State.BOGUS_COMMENT;\n                    this._stateBogusComment(cp);\n                    break;\n                }\n                case $.EOF: {\n                    this._err(ERR.eofBeforeTagName);\n                    this._emitChars('<');\n                    this._emitEOFToken();\n                    break;\n                }\n                default: {\n                    this._err(ERR.invalidFirstCharacterOfTagName);\n                    this._emitChars('<');\n                    this.state = State.DATA;\n                    this._stateData(cp);\n                }\n            }\n    }\n    // End tag open state\n    //------------------------------------------------------------------\n    _stateEndTagOpen(cp) {\n        if (isAsciiLetter(cp)) {\n            this._createEndTagToken();\n            this.state = State.TAG_NAME;\n            this._stateTagName(cp);\n        }\n        else\n            switch (cp) {\n                case $.GREATER_THAN_SIGN: {\n                    this._err(ERR.missingEndTagName);\n                    this.state = State.DATA;\n                    break;\n                }\n                case $.EOF: {\n                    this._err(ERR.eofBeforeTagName);\n                    this._emitChars('</');\n                    this._emitEOFToken();\n                    break;\n                }\n                default: {\n                    this._err(ERR.invalidFirstCharacterOfTagName);\n                    this._createCommentToken(2);\n                    this.state = State.BOGUS_COMMENT;\n                    this._stateBogusComment(cp);\n                }\n            }\n    }\n    // Tag name state\n    //------------------------------------------------------------------\n    _stateTagName(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                this.state = State.BEFORE_ATTRIBUTE_NAME;\n                break;\n            }\n            case $.SOLIDUS: {\n                this.state = State.SELF_CLOSING_START_TAG;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this.state = State.DATA;\n                this.emitCurrentTagToken();\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                token.tagName += REPLACEMENT_CHARACTER;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInTag);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                token.tagName += String.fromCodePoint(isAsciiUpper(cp) ? toAsciiLower(cp) : cp);\n            }\n        }\n    }\n    // RCDATA less-than sign state\n    //------------------------------------------------------------------\n    _stateRcdataLessThanSign(cp) {\n        if (cp === $.SOLIDUS) {\n            this.state = State.RCDATA_END_TAG_OPEN;\n        }\n        else {\n            this._emitChars('<');\n            this.state = State.RCDATA;\n            this._stateRcdata(cp);\n        }\n    }\n    // RCDATA end tag open state\n    //------------------------------------------------------------------\n    _stateRcdataEndTagOpen(cp) {\n        if (isAsciiLetter(cp)) {\n            this.state = State.RCDATA_END_TAG_NAME;\n            this._stateRcdataEndTagName(cp);\n        }\n        else {\n            this._emitChars('</');\n            this.state = State.RCDATA;\n            this._stateRcdata(cp);\n        }\n    }\n    handleSpecialEndTag(_cp) {\n        if (!this.preprocessor.startsWith(this.lastStartTagName, false)) {\n            return !this._ensureHibernation();\n        }\n        this._createEndTagToken();\n        const token = this.currentToken;\n        token.tagName = this.lastStartTagName;\n        const cp = this.preprocessor.peek(this.lastStartTagName.length);\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                this._advanceBy(this.lastStartTagName.length);\n                this.state = State.BEFORE_ATTRIBUTE_NAME;\n                return false;\n            }\n            case $.SOLIDUS: {\n                this._advanceBy(this.lastStartTagName.length);\n                this.state = State.SELF_CLOSING_START_TAG;\n                return false;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._advanceBy(this.lastStartTagName.length);\n                this.emitCurrentTagToken();\n                this.state = State.DATA;\n                return false;\n            }\n            default: {\n                return !this._ensureHibernation();\n            }\n        }\n    }\n    // RCDATA end tag name state\n    //------------------------------------------------------------------\n    _stateRcdataEndTagName(cp) {\n        if (this.handleSpecialEndTag(cp)) {\n            this._emitChars('</');\n            this.state = State.RCDATA;\n            this._stateRcdata(cp);\n        }\n    }\n    // RAWTEXT less-than sign state\n    //------------------------------------------------------------------\n    _stateRawtextLessThanSign(cp) {\n        if (cp === $.SOLIDUS) {\n            this.state = State.RAWTEXT_END_TAG_OPEN;\n        }\n        else {\n            this._emitChars('<');\n            this.state = State.RAWTEXT;\n            this._stateRawtext(cp);\n        }\n    }\n    // RAWTEXT end tag open state\n    //------------------------------------------------------------------\n    _stateRawtextEndTagOpen(cp) {\n        if (isAsciiLetter(cp)) {\n            this.state = State.RAWTEXT_END_TAG_NAME;\n            this._stateRawtextEndTagName(cp);\n        }\n        else {\n            this._emitChars('</');\n            this.state = State.RAWTEXT;\n            this._stateRawtext(cp);\n        }\n    }\n    // RAWTEXT end tag name state\n    //------------------------------------------------------------------\n    _stateRawtextEndTagName(cp) {\n        if (this.handleSpecialEndTag(cp)) {\n            this._emitChars('</');\n            this.state = State.RAWTEXT;\n            this._stateRawtext(cp);\n        }\n    }\n    // Script data less-than sign state\n    //------------------------------------------------------------------\n    _stateScriptDataLessThanSign(cp) {\n        switch (cp) {\n            case $.SOLIDUS: {\n                this.state = State.SCRIPT_DATA_END_TAG_OPEN;\n                break;\n            }\n            case $.EXCLAMATION_MARK: {\n                this.state = State.SCRIPT_DATA_ESCAPE_START;\n                this._emitChars('<!');\n                break;\n            }\n            default: {\n                this._emitChars('<');\n                this.state = State.SCRIPT_DATA;\n                this._stateScriptData(cp);\n            }\n        }\n    }\n    // Script data end tag open state\n    //------------------------------------------------------------------\n    _stateScriptDataEndTagOpen(cp) {\n        if (isAsciiLetter(cp)) {\n            this.state = State.SCRIPT_DATA_END_TAG_NAME;\n            this._stateScriptDataEndTagName(cp);\n        }\n        else {\n            this._emitChars('</');\n            this.state = State.SCRIPT_DATA;\n            this._stateScriptData(cp);\n        }\n    }\n    // Script data end tag name state\n    //------------------------------------------------------------------\n    _stateScriptDataEndTagName(cp) {\n        if (this.handleSpecialEndTag(cp)) {\n            this._emitChars('</');\n            this.state = State.SCRIPT_DATA;\n            this._stateScriptData(cp);\n        }\n    }\n    // Script data escape start state\n    //------------------------------------------------------------------\n    _stateScriptDataEscapeStart(cp) {\n        if (cp === $.HYPHEN_MINUS) {\n            this.state = State.SCRIPT_DATA_ESCAPE_START_DASH;\n            this._emitChars('-');\n        }\n        else {\n            this.state = State.SCRIPT_DATA;\n            this._stateScriptData(cp);\n        }\n    }\n    // Script data escape start dash state\n    //------------------------------------------------------------------\n    _stateScriptDataEscapeStartDash(cp) {\n        if (cp === $.HYPHEN_MINUS) {\n            this.state = State.SCRIPT_DATA_ESCAPED_DASH_DASH;\n            this._emitChars('-');\n        }\n        else {\n            this.state = State.SCRIPT_DATA;\n            this._stateScriptData(cp);\n        }\n    }\n    // Script data escaped state\n    //------------------------------------------------------------------\n    _stateScriptDataEscaped(cp) {\n        switch (cp) {\n            case $.HYPHEN_MINUS: {\n                this.state = State.SCRIPT_DATA_ESCAPED_DASH;\n                this._emitChars('-');\n                break;\n            }\n            case $.LESS_THAN_SIGN: {\n                this.state = State.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this._emitChars(REPLACEMENT_CHARACTER);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInScriptHtmlCommentLikeText);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    // Script data escaped dash state\n    //------------------------------------------------------------------\n    _stateScriptDataEscapedDash(cp) {\n        switch (cp) {\n            case $.HYPHEN_MINUS: {\n                this.state = State.SCRIPT_DATA_ESCAPED_DASH_DASH;\n                this._emitChars('-');\n                break;\n            }\n            case $.LESS_THAN_SIGN: {\n                this.state = State.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this.state = State.SCRIPT_DATA_ESCAPED;\n                this._emitChars(REPLACEMENT_CHARACTER);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInScriptHtmlCommentLikeText);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this.state = State.SCRIPT_DATA_ESCAPED;\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    // Script data escaped dash dash state\n    //------------------------------------------------------------------\n    _stateScriptDataEscapedDashDash(cp) {\n        switch (cp) {\n            case $.HYPHEN_MINUS: {\n                this._emitChars('-');\n                break;\n            }\n            case $.LESS_THAN_SIGN: {\n                this.state = State.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this.state = State.SCRIPT_DATA;\n                this._emitChars('>');\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this.state = State.SCRIPT_DATA_ESCAPED;\n                this._emitChars(REPLACEMENT_CHARACTER);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInScriptHtmlCommentLikeText);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this.state = State.SCRIPT_DATA_ESCAPED;\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    // Script data escaped less-than sign state\n    //------------------------------------------------------------------\n    _stateScriptDataEscapedLessThanSign(cp) {\n        if (cp === $.SOLIDUS) {\n            this.state = State.SCRIPT_DATA_ESCAPED_END_TAG_OPEN;\n        }\n        else if (isAsciiLetter(cp)) {\n            this._emitChars('<');\n            this.state = State.SCRIPT_DATA_DOUBLE_ESCAPE_START;\n            this._stateScriptDataDoubleEscapeStart(cp);\n        }\n        else {\n            this._emitChars('<');\n            this.state = State.SCRIPT_DATA_ESCAPED;\n            this._stateScriptDataEscaped(cp);\n        }\n    }\n    // Script data escaped end tag open state\n    //------------------------------------------------------------------\n    _stateScriptDataEscapedEndTagOpen(cp) {\n        if (isAsciiLetter(cp)) {\n            this.state = State.SCRIPT_DATA_ESCAPED_END_TAG_NAME;\n            this._stateScriptDataEscapedEndTagName(cp);\n        }\n        else {\n            this._emitChars('</');\n            this.state = State.SCRIPT_DATA_ESCAPED;\n            this._stateScriptDataEscaped(cp);\n        }\n    }\n    // Script data escaped end tag name state\n    //------------------------------------------------------------------\n    _stateScriptDataEscapedEndTagName(cp) {\n        if (this.handleSpecialEndTag(cp)) {\n            this._emitChars('</');\n            this.state = State.SCRIPT_DATA_ESCAPED;\n            this._stateScriptDataEscaped(cp);\n        }\n    }\n    // Script data double escape start state\n    //------------------------------------------------------------------\n    _stateScriptDataDoubleEscapeStart(cp) {\n        if (this.preprocessor.startsWith($$.SCRIPT, false) &&\n            isScriptDataDoubleEscapeSequenceEnd(this.preprocessor.peek($$.SCRIPT.length))) {\n            this._emitCodePoint(cp);\n            for (let i = 0; i < $$.SCRIPT.length; i++) {\n                this._emitCodePoint(this._consume());\n            }\n            this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED;\n        }\n        else if (!this._ensureHibernation()) {\n            this.state = State.SCRIPT_DATA_ESCAPED;\n            this._stateScriptDataEscaped(cp);\n        }\n    }\n    // Script data double escaped state\n    //------------------------------------------------------------------\n    _stateScriptDataDoubleEscaped(cp) {\n        switch (cp) {\n            case $.HYPHEN_MINUS: {\n                this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED_DASH;\n                this._emitChars('-');\n                break;\n            }\n            case $.LESS_THAN_SIGN: {\n                this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN;\n                this._emitChars('<');\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this._emitChars(REPLACEMENT_CHARACTER);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInScriptHtmlCommentLikeText);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    // Script data double escaped dash state\n    //------------------------------------------------------------------\n    _stateScriptDataDoubleEscapedDash(cp) {\n        switch (cp) {\n            case $.HYPHEN_MINUS: {\n                this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH;\n                this._emitChars('-');\n                break;\n            }\n            case $.LESS_THAN_SIGN: {\n                this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN;\n                this._emitChars('<');\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED;\n                this._emitChars(REPLACEMENT_CHARACTER);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInScriptHtmlCommentLikeText);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED;\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    // Script data double escaped dash dash state\n    //------------------------------------------------------------------\n    _stateScriptDataDoubleEscapedDashDash(cp) {\n        switch (cp) {\n            case $.HYPHEN_MINUS: {\n                this._emitChars('-');\n                break;\n            }\n            case $.LESS_THAN_SIGN: {\n                this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN;\n                this._emitChars('<');\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this.state = State.SCRIPT_DATA;\n                this._emitChars('>');\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED;\n                this._emitChars(REPLACEMENT_CHARACTER);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInScriptHtmlCommentLikeText);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED;\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    // Script data double escaped less-than sign state\n    //------------------------------------------------------------------\n    _stateScriptDataDoubleEscapedLessThanSign(cp) {\n        if (cp === $.SOLIDUS) {\n            this.state = State.SCRIPT_DATA_DOUBLE_ESCAPE_END;\n            this._emitChars('/');\n        }\n        else {\n            this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED;\n            this._stateScriptDataDoubleEscaped(cp);\n        }\n    }\n    // Script data double escape end state\n    //------------------------------------------------------------------\n    _stateScriptDataDoubleEscapeEnd(cp) {\n        if (this.preprocessor.startsWith($$.SCRIPT, false) &&\n            isScriptDataDoubleEscapeSequenceEnd(this.preprocessor.peek($$.SCRIPT.length))) {\n            this._emitCodePoint(cp);\n            for (let i = 0; i < $$.SCRIPT.length; i++) {\n                this._emitCodePoint(this._consume());\n            }\n            this.state = State.SCRIPT_DATA_ESCAPED;\n        }\n        else if (!this._ensureHibernation()) {\n            this.state = State.SCRIPT_DATA_DOUBLE_ESCAPED;\n            this._stateScriptDataDoubleEscaped(cp);\n        }\n    }\n    // Before attribute name state\n    //------------------------------------------------------------------\n    _stateBeforeAttributeName(cp) {\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                // Ignore whitespace\n                break;\n            }\n            case $.SOLIDUS:\n            case $.GREATER_THAN_SIGN:\n            case $.EOF: {\n                this.state = State.AFTER_ATTRIBUTE_NAME;\n                this._stateAfterAttributeName(cp);\n                break;\n            }\n            case $.EQUALS_SIGN: {\n                this._err(ERR.unexpectedEqualsSignBeforeAttributeName);\n                this._createAttr('=');\n                this.state = State.ATTRIBUTE_NAME;\n                break;\n            }\n            default: {\n                this._createAttr('');\n                this.state = State.ATTRIBUTE_NAME;\n                this._stateAttributeName(cp);\n            }\n        }\n    }\n    // Attribute name state\n    //------------------------------------------------------------------\n    _stateAttributeName(cp) {\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED:\n            case $.SOLIDUS:\n            case $.GREATER_THAN_SIGN:\n            case $.EOF: {\n                this._leaveAttrName();\n                this.state = State.AFTER_ATTRIBUTE_NAME;\n                this._stateAfterAttributeName(cp);\n                break;\n            }\n            case $.EQUALS_SIGN: {\n                this._leaveAttrName();\n                this.state = State.BEFORE_ATTRIBUTE_VALUE;\n                break;\n            }\n            case $.QUOTATION_MARK:\n            case $.APOSTROPHE:\n            case $.LESS_THAN_SIGN: {\n                this._err(ERR.unexpectedCharacterInAttributeName);\n                this.currentAttr.name += String.fromCodePoint(cp);\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this.currentAttr.name += REPLACEMENT_CHARACTER;\n                break;\n            }\n            default: {\n                this.currentAttr.name += String.fromCodePoint(isAsciiUpper(cp) ? toAsciiLower(cp) : cp);\n            }\n        }\n    }\n    // After attribute name state\n    //------------------------------------------------------------------\n    _stateAfterAttributeName(cp) {\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                // Ignore whitespace\n                break;\n            }\n            case $.SOLIDUS: {\n                this.state = State.SELF_CLOSING_START_TAG;\n                break;\n            }\n            case $.EQUALS_SIGN: {\n                this.state = State.BEFORE_ATTRIBUTE_VALUE;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this.state = State.DATA;\n                this.emitCurrentTagToken();\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInTag);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._createAttr('');\n                this.state = State.ATTRIBUTE_NAME;\n                this._stateAttributeName(cp);\n            }\n        }\n    }\n    // Before attribute value state\n    //------------------------------------------------------------------\n    _stateBeforeAttributeValue(cp) {\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                // Ignore whitespace\n                break;\n            }\n            case $.QUOTATION_MARK: {\n                this.state = State.ATTRIBUTE_VALUE_DOUBLE_QUOTED;\n                break;\n            }\n            case $.APOSTROPHE: {\n                this.state = State.ATTRIBUTE_VALUE_SINGLE_QUOTED;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.missingAttributeValue);\n                this.state = State.DATA;\n                this.emitCurrentTagToken();\n                break;\n            }\n            default: {\n                this.state = State.ATTRIBUTE_VALUE_UNQUOTED;\n                this._stateAttributeValueUnquoted(cp);\n            }\n        }\n    }\n    // Attribute value (double-quoted) state\n    //------------------------------------------------------------------\n    _stateAttributeValueDoubleQuoted(cp) {\n        switch (cp) {\n            case $.QUOTATION_MARK: {\n                this.state = State.AFTER_ATTRIBUTE_VALUE_QUOTED;\n                break;\n            }\n            case $.AMPERSAND: {\n                this._startCharacterReference();\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this.currentAttr.value += REPLACEMENT_CHARACTER;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInTag);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this.currentAttr.value += String.fromCodePoint(cp);\n            }\n        }\n    }\n    // Attribute value (single-quoted) state\n    //------------------------------------------------------------------\n    _stateAttributeValueSingleQuoted(cp) {\n        switch (cp) {\n            case $.APOSTROPHE: {\n                this.state = State.AFTER_ATTRIBUTE_VALUE_QUOTED;\n                break;\n            }\n            case $.AMPERSAND: {\n                this._startCharacterReference();\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this.currentAttr.value += REPLACEMENT_CHARACTER;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInTag);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this.currentAttr.value += String.fromCodePoint(cp);\n            }\n        }\n    }\n    // Attribute value (unquoted) state\n    //------------------------------------------------------------------\n    _stateAttributeValueUnquoted(cp) {\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                this._leaveAttrValue();\n                this.state = State.BEFORE_ATTRIBUTE_NAME;\n                break;\n            }\n            case $.AMPERSAND: {\n                this._startCharacterReference();\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._leaveAttrValue();\n                this.state = State.DATA;\n                this.emitCurrentTagToken();\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                this.currentAttr.value += REPLACEMENT_CHARACTER;\n                break;\n            }\n            case $.QUOTATION_MARK:\n            case $.APOSTROPHE:\n            case $.LESS_THAN_SIGN:\n            case $.EQUALS_SIGN:\n            case $.GRAVE_ACCENT: {\n                this._err(ERR.unexpectedCharacterInUnquotedAttributeValue);\n                this.currentAttr.value += String.fromCodePoint(cp);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInTag);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this.currentAttr.value += String.fromCodePoint(cp);\n            }\n        }\n    }\n    // After attribute value (quoted) state\n    //------------------------------------------------------------------\n    _stateAfterAttributeValueQuoted(cp) {\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                this._leaveAttrValue();\n                this.state = State.BEFORE_ATTRIBUTE_NAME;\n                break;\n            }\n            case $.SOLIDUS: {\n                this._leaveAttrValue();\n                this.state = State.SELF_CLOSING_START_TAG;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._leaveAttrValue();\n                this.state = State.DATA;\n                this.emitCurrentTagToken();\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInTag);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._err(ERR.missingWhitespaceBetweenAttributes);\n                this.state = State.BEFORE_ATTRIBUTE_NAME;\n                this._stateBeforeAttributeName(cp);\n            }\n        }\n    }\n    // Self-closing start tag state\n    //------------------------------------------------------------------\n    _stateSelfClosingStartTag(cp) {\n        switch (cp) {\n            case $.GREATER_THAN_SIGN: {\n                const token = this.currentToken;\n                token.selfClosing = true;\n                this.state = State.DATA;\n                this.emitCurrentTagToken();\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInTag);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._err(ERR.unexpectedSolidusInTag);\n                this.state = State.BEFORE_ATTRIBUTE_NAME;\n                this._stateBeforeAttributeName(cp);\n            }\n        }\n    }\n    // Bogus comment state\n    //------------------------------------------------------------------\n    _stateBogusComment(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.GREATER_THAN_SIGN: {\n                this.state = State.DATA;\n                this.emitCurrentComment(token);\n                break;\n            }\n            case $.EOF: {\n                this.emitCurrentComment(token);\n                this._emitEOFToken();\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                token.data += REPLACEMENT_CHARACTER;\n                break;\n            }\n            default: {\n                token.data += String.fromCodePoint(cp);\n            }\n        }\n    }\n    // Markup declaration open state\n    //------------------------------------------------------------------\n    _stateMarkupDeclarationOpen(cp) {\n        if (this._consumeSequenceIfMatch($$.DASH_DASH, true)) {\n            this._createCommentToken($$.DASH_DASH.length + 1);\n            this.state = State.COMMENT_START;\n        }\n        else if (this._consumeSequenceIfMatch($$.DOCTYPE, false)) {\n            // NOTE: Doctypes tokens are created without fixed offsets. We keep track of the moment a doctype *might* start here.\n            this.currentLocation = this.getCurrentLocation($$.DOCTYPE.length + 1);\n            this.state = State.DOCTYPE;\n        }\n        else if (this._consumeSequenceIfMatch($$.CDATA_START, true)) {\n            if (this.inForeignNode) {\n                this.state = State.CDATA_SECTION;\n            }\n            else {\n                this._err(ERR.cdataInHtmlContent);\n                this._createCommentToken($$.CDATA_START.length + 1);\n                this.currentToken.data = '[CDATA[';\n                this.state = State.BOGUS_COMMENT;\n            }\n        }\n        //NOTE: Sequence lookups can be abrupted by hibernation. In that case, lookup\n        //results are no longer valid and we will need to start over.\n        else if (!this._ensureHibernation()) {\n            this._err(ERR.incorrectlyOpenedComment);\n            this._createCommentToken(2);\n            this.state = State.BOGUS_COMMENT;\n            this._stateBogusComment(cp);\n        }\n    }\n    // Comment start state\n    //------------------------------------------------------------------\n    _stateCommentStart(cp) {\n        switch (cp) {\n            case $.HYPHEN_MINUS: {\n                this.state = State.COMMENT_START_DASH;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.abruptClosingOfEmptyComment);\n                this.state = State.DATA;\n                const token = this.currentToken;\n                this.emitCurrentComment(token);\n                break;\n            }\n            default: {\n                this.state = State.COMMENT;\n                this._stateComment(cp);\n            }\n        }\n    }\n    // Comment start dash state\n    //------------------------------------------------------------------\n    _stateCommentStartDash(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.HYPHEN_MINUS: {\n                this.state = State.COMMENT_END;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.abruptClosingOfEmptyComment);\n                this.state = State.DATA;\n                this.emitCurrentComment(token);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInComment);\n                this.emitCurrentComment(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                token.data += '-';\n                this.state = State.COMMENT;\n                this._stateComment(cp);\n            }\n        }\n    }\n    // Comment state\n    //------------------------------------------------------------------\n    _stateComment(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.HYPHEN_MINUS: {\n                this.state = State.COMMENT_END_DASH;\n                break;\n            }\n            case $.LESS_THAN_SIGN: {\n                token.data += '<';\n                this.state = State.COMMENT_LESS_THAN_SIGN;\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                token.data += REPLACEMENT_CHARACTER;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInComment);\n                this.emitCurrentComment(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                token.data += String.fromCodePoint(cp);\n            }\n        }\n    }\n    // Comment less-than sign state\n    //------------------------------------------------------------------\n    _stateCommentLessThanSign(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.EXCLAMATION_MARK: {\n                token.data += '!';\n                this.state = State.COMMENT_LESS_THAN_SIGN_BANG;\n                break;\n            }\n            case $.LESS_THAN_SIGN: {\n                token.data += '<';\n                break;\n            }\n            default: {\n                this.state = State.COMMENT;\n                this._stateComment(cp);\n            }\n        }\n    }\n    // Comment less-than sign bang state\n    //------------------------------------------------------------------\n    _stateCommentLessThanSignBang(cp) {\n        if (cp === $.HYPHEN_MINUS) {\n            this.state = State.COMMENT_LESS_THAN_SIGN_BANG_DASH;\n        }\n        else {\n            this.state = State.COMMENT;\n            this._stateComment(cp);\n        }\n    }\n    // Comment less-than sign bang dash state\n    //------------------------------------------------------------------\n    _stateCommentLessThanSignBangDash(cp) {\n        if (cp === $.HYPHEN_MINUS) {\n            this.state = State.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH;\n        }\n        else {\n            this.state = State.COMMENT_END_DASH;\n            this._stateCommentEndDash(cp);\n        }\n    }\n    // Comment less-than sign bang dash dash state\n    //------------------------------------------------------------------\n    _stateCommentLessThanSignBangDashDash(cp) {\n        if (cp !== $.GREATER_THAN_SIGN && cp !== $.EOF) {\n            this._err(ERR.nestedComment);\n        }\n        this.state = State.COMMENT_END;\n        this._stateCommentEnd(cp);\n    }\n    // Comment end dash state\n    //------------------------------------------------------------------\n    _stateCommentEndDash(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.HYPHEN_MINUS: {\n                this.state = State.COMMENT_END;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInComment);\n                this.emitCurrentComment(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                token.data += '-';\n                this.state = State.COMMENT;\n                this._stateComment(cp);\n            }\n        }\n    }\n    // Comment end state\n    //------------------------------------------------------------------\n    _stateCommentEnd(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.GREATER_THAN_SIGN: {\n                this.state = State.DATA;\n                this.emitCurrentComment(token);\n                break;\n            }\n            case $.EXCLAMATION_MARK: {\n                this.state = State.COMMENT_END_BANG;\n                break;\n            }\n            case $.HYPHEN_MINUS: {\n                token.data += '-';\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInComment);\n                this.emitCurrentComment(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                token.data += '--';\n                this.state = State.COMMENT;\n                this._stateComment(cp);\n            }\n        }\n    }\n    // Comment end bang state\n    //------------------------------------------------------------------\n    _stateCommentEndBang(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.HYPHEN_MINUS: {\n                token.data += '--!';\n                this.state = State.COMMENT_END_DASH;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.incorrectlyClosedComment);\n                this.state = State.DATA;\n                this.emitCurrentComment(token);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInComment);\n                this.emitCurrentComment(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                token.data += '--!';\n                this.state = State.COMMENT;\n                this._stateComment(cp);\n            }\n        }\n    }\n    // DOCTYPE state\n    //------------------------------------------------------------------\n    _stateDoctype(cp) {\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                this.state = State.BEFORE_DOCTYPE_NAME;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this.state = State.BEFORE_DOCTYPE_NAME;\n                this._stateBeforeDoctypeName(cp);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                this._createDoctypeToken(null);\n                const token = this.currentToken;\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._err(ERR.missingWhitespaceBeforeDoctypeName);\n                this.state = State.BEFORE_DOCTYPE_NAME;\n                this._stateBeforeDoctypeName(cp);\n            }\n        }\n    }\n    // Before DOCTYPE name state\n    //------------------------------------------------------------------\n    _stateBeforeDoctypeName(cp) {\n        if (isAsciiUpper(cp)) {\n            this._createDoctypeToken(String.fromCharCode(toAsciiLower(cp)));\n            this.state = State.DOCTYPE_NAME;\n        }\n        else\n            switch (cp) {\n                case $.SPACE:\n                case $.LINE_FEED:\n                case $.TABULATION:\n                case $.FORM_FEED: {\n                    // Ignore whitespace\n                    break;\n                }\n                case $.NULL: {\n                    this._err(ERR.unexpectedNullCharacter);\n                    this._createDoctypeToken(REPLACEMENT_CHARACTER);\n                    this.state = State.DOCTYPE_NAME;\n                    break;\n                }\n                case $.GREATER_THAN_SIGN: {\n                    this._err(ERR.missingDoctypeName);\n                    this._createDoctypeToken(null);\n                    const token = this.currentToken;\n                    token.forceQuirks = true;\n                    this.emitCurrentDoctype(token);\n                    this.state = State.DATA;\n                    break;\n                }\n                case $.EOF: {\n                    this._err(ERR.eofInDoctype);\n                    this._createDoctypeToken(null);\n                    const token = this.currentToken;\n                    token.forceQuirks = true;\n                    this.emitCurrentDoctype(token);\n                    this._emitEOFToken();\n                    break;\n                }\n                default: {\n                    this._createDoctypeToken(String.fromCodePoint(cp));\n                    this.state = State.DOCTYPE_NAME;\n                }\n            }\n    }\n    // DOCTYPE name state\n    //------------------------------------------------------------------\n    _stateDoctypeName(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                this.state = State.AFTER_DOCTYPE_NAME;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this.state = State.DATA;\n                this.emitCurrentDoctype(token);\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                token.name += REPLACEMENT_CHARACTER;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                token.name += String.fromCodePoint(isAsciiUpper(cp) ? toAsciiLower(cp) : cp);\n            }\n        }\n    }\n    // After DOCTYPE name state\n    //------------------------------------------------------------------\n    _stateAfterDoctypeName(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                // Ignore whitespace\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this.state = State.DATA;\n                this.emitCurrentDoctype(token);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                if (this._consumeSequenceIfMatch($$.PUBLIC, false)) {\n                    this.state = State.AFTER_DOCTYPE_PUBLIC_KEYWORD;\n                }\n                else if (this._consumeSequenceIfMatch($$.SYSTEM, false)) {\n                    this.state = State.AFTER_DOCTYPE_SYSTEM_KEYWORD;\n                }\n                //NOTE: sequence lookup can be abrupted by hibernation. In that case lookup\n                //results are no longer valid and we will need to start over.\n                else if (!this._ensureHibernation()) {\n                    this._err(ERR.invalidCharacterSequenceAfterDoctypeName);\n                    token.forceQuirks = true;\n                    this.state = State.BOGUS_DOCTYPE;\n                    this._stateBogusDoctype(cp);\n                }\n            }\n        }\n    }\n    // After DOCTYPE public keyword state\n    //------------------------------------------------------------------\n    _stateAfterDoctypePublicKeyword(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                this.state = State.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER;\n                break;\n            }\n            case $.QUOTATION_MARK: {\n                this._err(ERR.missingWhitespaceAfterDoctypePublicKeyword);\n                token.publicId = '';\n                this.state = State.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;\n                break;\n            }\n            case $.APOSTROPHE: {\n                this._err(ERR.missingWhitespaceAfterDoctypePublicKeyword);\n                token.publicId = '';\n                this.state = State.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.missingDoctypePublicIdentifier);\n                token.forceQuirks = true;\n                this.state = State.DATA;\n                this.emitCurrentDoctype(token);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._err(ERR.missingQuoteBeforeDoctypePublicIdentifier);\n                token.forceQuirks = true;\n                this.state = State.BOGUS_DOCTYPE;\n                this._stateBogusDoctype(cp);\n            }\n        }\n    }\n    // Before DOCTYPE public identifier state\n    //------------------------------------------------------------------\n    _stateBeforeDoctypePublicIdentifier(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                // Ignore whitespace\n                break;\n            }\n            case $.QUOTATION_MARK: {\n                token.publicId = '';\n                this.state = State.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;\n                break;\n            }\n            case $.APOSTROPHE: {\n                token.publicId = '';\n                this.state = State.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.missingDoctypePublicIdentifier);\n                token.forceQuirks = true;\n                this.state = State.DATA;\n                this.emitCurrentDoctype(token);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._err(ERR.missingQuoteBeforeDoctypePublicIdentifier);\n                token.forceQuirks = true;\n                this.state = State.BOGUS_DOCTYPE;\n                this._stateBogusDoctype(cp);\n            }\n        }\n    }\n    // DOCTYPE public identifier (double-quoted) state\n    //------------------------------------------------------------------\n    _stateDoctypePublicIdentifierDoubleQuoted(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.QUOTATION_MARK: {\n                this.state = State.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                token.publicId += REPLACEMENT_CHARACTER;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.abruptDoctypePublicIdentifier);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this.state = State.DATA;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                token.publicId += String.fromCodePoint(cp);\n            }\n        }\n    }\n    // DOCTYPE public identifier (single-quoted) state\n    //------------------------------------------------------------------\n    _stateDoctypePublicIdentifierSingleQuoted(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.APOSTROPHE: {\n                this.state = State.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                token.publicId += REPLACEMENT_CHARACTER;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.abruptDoctypePublicIdentifier);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this.state = State.DATA;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                token.publicId += String.fromCodePoint(cp);\n            }\n        }\n    }\n    // After DOCTYPE public identifier state\n    //------------------------------------------------------------------\n    _stateAfterDoctypePublicIdentifier(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                this.state = State.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this.state = State.DATA;\n                this.emitCurrentDoctype(token);\n                break;\n            }\n            case $.QUOTATION_MARK: {\n                this._err(ERR.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers);\n                token.systemId = '';\n                this.state = State.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;\n                break;\n            }\n            case $.APOSTROPHE: {\n                this._err(ERR.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers);\n                token.systemId = '';\n                this.state = State.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._err(ERR.missingQuoteBeforeDoctypeSystemIdentifier);\n                token.forceQuirks = true;\n                this.state = State.BOGUS_DOCTYPE;\n                this._stateBogusDoctype(cp);\n            }\n        }\n    }\n    // Between DOCTYPE public and system identifiers state\n    //------------------------------------------------------------------\n    _stateBetweenDoctypePublicAndSystemIdentifiers(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                // Ignore whitespace\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this.emitCurrentDoctype(token);\n                this.state = State.DATA;\n                break;\n            }\n            case $.QUOTATION_MARK: {\n                token.systemId = '';\n                this.state = State.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;\n                break;\n            }\n            case $.APOSTROPHE: {\n                token.systemId = '';\n                this.state = State.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._err(ERR.missingQuoteBeforeDoctypeSystemIdentifier);\n                token.forceQuirks = true;\n                this.state = State.BOGUS_DOCTYPE;\n                this._stateBogusDoctype(cp);\n            }\n        }\n    }\n    // After DOCTYPE system keyword state\n    //------------------------------------------------------------------\n    _stateAfterDoctypeSystemKeyword(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                this.state = State.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER;\n                break;\n            }\n            case $.QUOTATION_MARK: {\n                this._err(ERR.missingWhitespaceAfterDoctypeSystemKeyword);\n                token.systemId = '';\n                this.state = State.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;\n                break;\n            }\n            case $.APOSTROPHE: {\n                this._err(ERR.missingWhitespaceAfterDoctypeSystemKeyword);\n                token.systemId = '';\n                this.state = State.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.missingDoctypeSystemIdentifier);\n                token.forceQuirks = true;\n                this.state = State.DATA;\n                this.emitCurrentDoctype(token);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._err(ERR.missingQuoteBeforeDoctypeSystemIdentifier);\n                token.forceQuirks = true;\n                this.state = State.BOGUS_DOCTYPE;\n                this._stateBogusDoctype(cp);\n            }\n        }\n    }\n    // Before DOCTYPE system identifier state\n    //------------------------------------------------------------------\n    _stateBeforeDoctypeSystemIdentifier(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                // Ignore whitespace\n                break;\n            }\n            case $.QUOTATION_MARK: {\n                token.systemId = '';\n                this.state = State.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;\n                break;\n            }\n            case $.APOSTROPHE: {\n                token.systemId = '';\n                this.state = State.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.missingDoctypeSystemIdentifier);\n                token.forceQuirks = true;\n                this.state = State.DATA;\n                this.emitCurrentDoctype(token);\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._err(ERR.missingQuoteBeforeDoctypeSystemIdentifier);\n                token.forceQuirks = true;\n                this.state = State.BOGUS_DOCTYPE;\n                this._stateBogusDoctype(cp);\n            }\n        }\n    }\n    // DOCTYPE system identifier (double-quoted) state\n    //------------------------------------------------------------------\n    _stateDoctypeSystemIdentifierDoubleQuoted(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.QUOTATION_MARK: {\n                this.state = State.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                token.systemId += REPLACEMENT_CHARACTER;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.abruptDoctypeSystemIdentifier);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this.state = State.DATA;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                token.systemId += String.fromCodePoint(cp);\n            }\n        }\n    }\n    // DOCTYPE system identifier (single-quoted) state\n    //------------------------------------------------------------------\n    _stateDoctypeSystemIdentifierSingleQuoted(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.APOSTROPHE: {\n                this.state = State.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                token.systemId += REPLACEMENT_CHARACTER;\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this._err(ERR.abruptDoctypeSystemIdentifier);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this.state = State.DATA;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                token.systemId += String.fromCodePoint(cp);\n            }\n        }\n    }\n    // After DOCTYPE system identifier state\n    //------------------------------------------------------------------\n    _stateAfterDoctypeSystemIdentifier(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.SPACE:\n            case $.LINE_FEED:\n            case $.TABULATION:\n            case $.FORM_FEED: {\n                // Ignore whitespace\n                break;\n            }\n            case $.GREATER_THAN_SIGN: {\n                this.emitCurrentDoctype(token);\n                this.state = State.DATA;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInDoctype);\n                token.forceQuirks = true;\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._err(ERR.unexpectedCharacterAfterDoctypeSystemIdentifier);\n                this.state = State.BOGUS_DOCTYPE;\n                this._stateBogusDoctype(cp);\n            }\n        }\n    }\n    // Bogus DOCTYPE state\n    //------------------------------------------------------------------\n    _stateBogusDoctype(cp) {\n        const token = this.currentToken;\n        switch (cp) {\n            case $.GREATER_THAN_SIGN: {\n                this.emitCurrentDoctype(token);\n                this.state = State.DATA;\n                break;\n            }\n            case $.NULL: {\n                this._err(ERR.unexpectedNullCharacter);\n                break;\n            }\n            case $.EOF: {\n                this.emitCurrentDoctype(token);\n                this._emitEOFToken();\n                break;\n            }\n            default:\n            // Do nothing\n        }\n    }\n    // CDATA section state\n    //------------------------------------------------------------------\n    _stateCdataSection(cp) {\n        switch (cp) {\n            case $.RIGHT_SQUARE_BRACKET: {\n                this.state = State.CDATA_SECTION_BRACKET;\n                break;\n            }\n            case $.EOF: {\n                this._err(ERR.eofInCdata);\n                this._emitEOFToken();\n                break;\n            }\n            default: {\n                this._emitCodePoint(cp);\n            }\n        }\n    }\n    // CDATA section bracket state\n    //------------------------------------------------------------------\n    _stateCdataSectionBracket(cp) {\n        if (cp === $.RIGHT_SQUARE_BRACKET) {\n            this.state = State.CDATA_SECTION_END;\n        }\n        else {\n            this._emitChars(']');\n            this.state = State.CDATA_SECTION;\n            this._stateCdataSection(cp);\n        }\n    }\n    // CDATA section end state\n    //------------------------------------------------------------------\n    _stateCdataSectionEnd(cp) {\n        switch (cp) {\n            case $.GREATER_THAN_SIGN: {\n                this.state = State.DATA;\n                break;\n            }\n            case $.RIGHT_SQUARE_BRACKET: {\n                this._emitChars(']');\n                break;\n            }\n            default: {\n                this._emitChars(']]');\n                this.state = State.CDATA_SECTION;\n                this._stateCdataSection(cp);\n            }\n        }\n    }\n    // Character reference state\n    //------------------------------------------------------------------\n    _stateCharacterReference() {\n        let length = this.entityDecoder.write(this.preprocessor.html, this.preprocessor.pos);\n        if (length < 0) {\n            if (this.preprocessor.lastChunkWritten) {\n                length = this.entityDecoder.end();\n            }\n            else {\n                // Wait for the rest of the entity.\n                this.active = false;\n                // Mark the entire buffer as read.\n                this.preprocessor.pos = this.preprocessor.html.length - 1;\n                this.consumedAfterSnapshot = 0;\n                this.preprocessor.endOfChunkHit = true;\n                return;\n            }\n        }\n        if (length === 0) {\n            // This was not a valid entity. Go back to the beginning, and\n            // figure out what to do.\n            this.preprocessor.pos = this.entityStartPos;\n            this._flushCodePointConsumedAsCharacterReference($.AMPERSAND);\n            this.state =\n                !this._isCharacterReferenceInAttribute() && isAsciiAlphaNumeric(this.preprocessor.peek(1))\n                    ? State.AMBIGUOUS_AMPERSAND\n                    : this.returnState;\n        }\n        else {\n            // We successfully parsed an entity. Switch to the return state.\n            this.state = this.returnState;\n        }\n    }\n    // Ambiguos ampersand state\n    //------------------------------------------------------------------\n    _stateAmbiguousAmpersand(cp) {\n        if (isAsciiAlphaNumeric(cp)) {\n            this._flushCodePointConsumedAsCharacterReference(cp);\n        }\n        else {\n            if (cp === $.SEMICOLON) {\n                this._err(ERR.unknownNamedCharacterReference);\n            }\n            this.state = this.returnState;\n            this._callState(cp);\n        }\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;AACA,QAAQ;AACR,IAAI;AACJ,CAAC,SAAU,KAAK;IACZ,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,GAAG;IAC3B,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,GAAG;IAC7B,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,GAAG;IAC9B,KAAK,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE,GAAG;IAClC,KAAK,CAAC,KAAK,CAAC,YAAY,GAAG,EAAE,GAAG;IAChC,KAAK,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,GAAG;IAC/B,KAAK,CAAC,KAAK,CAAC,eAAe,GAAG,EAAE,GAAG;IACnC,KAAK,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,GAAG;IAC/B,KAAK,CAAC,KAAK,CAAC,wBAAwB,GAAG,EAAE,GAAG;IAC5C,KAAK,CAAC,KAAK,CAAC,sBAAsB,GAAG,EAAE,GAAG;IAC1C,KAAK,CAAC,KAAK,CAAC,sBAAsB,GAAG,GAAG,GAAG;IAC3C,KAAK,CAAC,KAAK,CAAC,yBAAyB,GAAG,GAAG,GAAG;IAC9C,KAAK,CAAC,KAAK,CAAC,uBAAuB,GAAG,GAAG,GAAG;IAC5C,KAAK,CAAC,KAAK,CAAC,uBAAuB,GAAG,GAAG,GAAG;IAC5C,KAAK,CAAC,KAAK,CAAC,6BAA6B,GAAG,GAAG,GAAG;IAClD,KAAK,CAAC,KAAK,CAAC,2BAA2B,GAAG,GAAG,GAAG;IAChD,KAAK,CAAC,KAAK,CAAC,2BAA2B,GAAG,GAAG,GAAG;IAChD,KAAK,CAAC,KAAK,CAAC,2BAA2B,GAAG,GAAG,GAAG;IAChD,KAAK,CAAC,KAAK,CAAC,gCAAgC,GAAG,GAAG,GAAG;IACrD,KAAK,CAAC,KAAK,CAAC,sBAAsB,GAAG,GAAG,GAAG;IAC3C,KAAK,CAAC,KAAK,CAAC,2BAA2B,GAAG,GAAG,GAAG;IAChD,KAAK,CAAC,KAAK,CAAC,gCAAgC,GAAG,GAAG,GAAG;IACrD,KAAK,CAAC,KAAK,CAAC,qCAAqC,GAAG,GAAG,GAAG;IAC1D,KAAK,CAAC,KAAK,CAAC,mCAAmC,GAAG,GAAG,GAAG;IACxD,KAAK,CAAC,KAAK,CAAC,mCAAmC,GAAG,GAAG,GAAG;IACxD,KAAK,CAAC,KAAK,CAAC,kCAAkC,GAAG,GAAG,GAAG;IACvD,KAAK,CAAC,KAAK,CAAC,6BAA6B,GAAG,GAAG,GAAG;IAClD,KAAK,CAAC,KAAK,CAAC,kCAAkC,GAAG,GAAG,GAAG;IACvD,KAAK,CAAC,KAAK,CAAC,uCAAuC,GAAG,GAAG,GAAG;IAC5D,KAAK,CAAC,KAAK,CAAC,4CAA4C,GAAG,GAAG,GAAG;IACjE,KAAK,CAAC,KAAK,CAAC,gCAAgC,GAAG,GAAG,GAAG;IACrD,KAAK,CAAC,KAAK,CAAC,wBAAwB,GAAG,GAAG,GAAG;IAC7C,KAAK,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,GAAG;IACtC,KAAK,CAAC,KAAK,CAAC,uBAAuB,GAAG,GAAG,GAAG;IAC5C,KAAK,CAAC,KAAK,CAAC,yBAAyB,GAAG,GAAG,GAAG;IAC9C,KAAK,CAAC,KAAK,CAAC,gCAAgC,GAAG,GAAG,GAAG;IACrD,KAAK,CAAC,KAAK,CAAC,gCAAgC,GAAG,GAAG,GAAG;IACrD,KAAK,CAAC,KAAK,CAAC,2BAA2B,GAAG,GAAG,GAAG;IAChD,KAAK,CAAC,KAAK,CAAC,+BAA+B,GAAG,GAAG,GAAG;IACpD,KAAK,CAAC,KAAK,CAAC,yBAAyB,GAAG,GAAG,GAAG;IAC9C,KAAK,CAAC,KAAK,CAAC,gBAAgB,GAAG,GAAG,GAAG;IACrC,KAAK,CAAC,KAAK,CAAC,0BAA0B,GAAG,GAAG,GAAG;IAC/C,KAAK,CAAC,KAAK,CAAC,gBAAgB,GAAG,GAAG,GAAG;IACrC,KAAK,CAAC,KAAK,CAAC,qBAAqB,GAAG,GAAG,GAAG;IAC1C,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,GAAG;IAC/B,KAAK,CAAC,KAAK,CAAC,yBAAyB,GAAG,GAAG,GAAG;IAC9C,KAAK,CAAC,KAAK,CAAC,8BAA8B,GAAG,GAAG,GAAG;IACnD,KAAK,CAAC,KAAK,CAAC,mCAAmC,GAAG,GAAG,GAAG;IACxD,KAAK,CAAC,KAAK,CAAC,wCAAwC,GAAG,GAAG,GAAG;IAC7D,KAAK,CAAC,KAAK,CAAC,mBAAmB,GAAG,GAAG,GAAG;IACxC,KAAK,CAAC,KAAK,CAAC,cAAc,GAAG,GAAG,GAAG;IACnC,KAAK,CAAC,KAAK,CAAC,mBAAmB,GAAG,GAAG,GAAG;IACxC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,GAAG;IAC/B,KAAK,CAAC,KAAK,CAAC,sBAAsB,GAAG,GAAG,GAAG;IAC3C,KAAK,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,GAAG;IACpC,KAAK,CAAC,KAAK,CAAC,qBAAqB,GAAG,GAAG,GAAG;IAC1C,KAAK,CAAC,KAAK,CAAC,+BAA+B,GAAG,GAAG,GAAG;IACpD,KAAK,CAAC,KAAK,CAAC,mCAAmC,GAAG,GAAG,GAAG;IACxD,KAAK,CAAC,KAAK,CAAC,0CAA0C,GAAG,GAAG,GAAG;IAC/D,KAAK,CAAC,KAAK,CAAC,0CAA0C,GAAG,GAAG,GAAG;IAC/D,KAAK,CAAC,KAAK,CAAC,kCAAkC,GAAG,GAAG,GAAG;IACvD,KAAK,CAAC,KAAK,CAAC,gDAAgD,GAAG,GAAG,GAAG;IACrE,KAAK,CAAC,KAAK,CAAC,+BAA+B,GAAG,GAAG,GAAG;IACpD,KAAK,CAAC,KAAK,CAAC,mCAAmC,GAAG,GAAG,GAAG;IACxD,KAAK,CAAC,KAAK,CAAC,0CAA0C,GAAG,GAAG,GAAG;IAC/D,KAAK,CAAC,KAAK,CAAC,0CAA0C,GAAG,GAAG,GAAG;IAC/D,KAAK,CAAC,KAAK,CAAC,kCAAkC,GAAG,GAAG,GAAG;IACvD,KAAK,CAAC,KAAK,CAAC,gBAAgB,GAAG,GAAG,GAAG;IACrC,KAAK,CAAC,KAAK,CAAC,gBAAgB,GAAG,GAAG,GAAG;IACrC,KAAK,CAAC,KAAK,CAAC,wBAAwB,GAAG,GAAG,GAAG;IAC7C,KAAK,CAAC,KAAK,CAAC,oBAAoB,GAAG,GAAG,GAAG;IACzC,KAAK,CAAC,KAAK,CAAC,sBAAsB,GAAG,GAAG,GAAG;IAC3C,KAAK,CAAC,KAAK,CAAC,sBAAsB,GAAG,GAAG,GAAG;AAC/C,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;AAEhB,MAAM,gBAAgB;IACzB,MAAM,MAAM,IAAI;IAChB,QAAQ,MAAM,MAAM;IACpB,SAAS,MAAM,OAAO;IACtB,aAAa,MAAM,WAAW;IAC9B,WAAW,MAAM,SAAS;IAC1B,eAAe,MAAM,aAAa;AACtC;AACA,OAAO;AACP,6GAA6G;AAC7G,kFAAkF;AAClF,0FAA0F;AAC1F,SAAS,aAAa,EAAE;IACpB,OAAO,MAAM,mJAAA,CAAA,cAAC,CAAC,OAAO,IAAI,MAAM,mJAAA,CAAA,cAAC,CAAC,OAAO;AAC7C;AACA,SAAS,aAAa,EAAE;IACpB,OAAO,MAAM,mJAAA,CAAA,cAAC,CAAC,eAAe,IAAI,MAAM,mJAAA,CAAA,cAAC,CAAC,eAAe;AAC7D;AACA,SAAS,aAAa,EAAE;IACpB,OAAO,MAAM,mJAAA,CAAA,cAAC,CAAC,aAAa,IAAI,MAAM,mJAAA,CAAA,cAAC,CAAC,aAAa;AACzD;AACA,SAAS,cAAc,EAAE;IACrB,OAAO,aAAa,OAAO,aAAa;AAC5C;AACA,SAAS,oBAAoB,EAAE;IAC3B,OAAO,cAAc,OAAO,aAAa;AAC7C;AACA,SAAS,aAAa,EAAE;IACpB,OAAO,KAAK;AAChB;AACA,SAAS,aAAa,EAAE;IACpB,OAAO,OAAO,mJAAA,CAAA,cAAC,CAAC,KAAK,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,SAAS,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,UAAU,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,SAAS;AAC5F;AACA,SAAS,oCAAoC,EAAE;IAC3C,OAAO,aAAa,OAAO,OAAO,mJAAA,CAAA,cAAC,CAAC,OAAO,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,iBAAiB;AAC7E;AACA,SAAS,qCAAqC,IAAI;IAC9C,IAAI,SAAS,mJAAA,CAAA,cAAC,CAAC,IAAI,EAAE;QACjB,OAAO,0JAAA,CAAA,MAAG,CAAC,sBAAsB;IACrC,OACK,IAAI,OAAO,SAAS;QACrB,OAAO,0JAAA,CAAA,MAAG,CAAC,qCAAqC;IACpD,OACK,IAAI,CAAA,GAAA,mJAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxB,OAAO,0JAAA,CAAA,MAAG,CAAC,2BAA2B;IAC1C,OACK,IAAI,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO;QACjC,OAAO,0JAAA,CAAA,MAAG,CAAC,8BAA8B;IAC7C,OACK,IAAI,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,SAAS,mJAAA,CAAA,cAAC,CAAC,eAAe,EAAE;QAC7D,OAAO,0JAAA,CAAA,MAAG,CAAC,yBAAyB;IACxC;IACA,OAAO;AACX;AAEO,MAAM;IACT,YAAY,OAAO,EAAE,OAAO,CAAE;QAC1B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG;QACd,oEAAoE,GACpE,IAAI,CAAC,MAAM,GAAG;QACd;;;;;SAKC,GACD,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;QACvB,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI;QAC7B,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,qBAAqB,GAAG,CAAC;QAC9B,IAAI,CAAC,qBAAqB,GAAG;QAC7B,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,WAAW,GAAG;YAAE,MAAM;YAAI,OAAO;QAAG;QACzC,IAAI,CAAC,YAAY,GAAG,IAAI,2JAAA,CAAA,eAAY,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAChD,IAAI,CAAC,aAAa,GAAG,IAAI,iKAAA,CAAA,gBAAa,CAAC,8KAAA,CAAA,iBAAc,EAAE,CAAC,IAAI;YACxD,4DAA4D;YAC5D,qDAAqD;YACrD,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,GAAG,WAAW;YACzD,IAAI,CAAC,2CAA2C,CAAC;QACrD,GAAG,QAAQ,YAAY,GACjB;YACE,yCAAyC;gBACrC,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uCAAuC,EAAE;YAC3D;YACA,4CAA4C,CAAC;gBACzC,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,0CAA0C,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG;YAC5G;YACA,mCAAmC,CAAC;gBAChC,MAAM,QAAQ,qCAAqC;gBACnD,IAAI,OACA,IAAI,CAAC,IAAI,CAAC,OAAO;YACzB;QACJ,IACE;IACV;IACA,QAAQ;IACR,KAAK,IAAI,EAAE,WAAW,CAAC,EAAE;QACrB,IAAI,IAAI;QACR,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,EAAE,YAAY,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM;IAC9H;IACA,uDAAuD;IACvD,mBAAmB,MAAM,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YACtC,OAAO;QACX;QACA,OAAO;YACH,WAAW,IAAI,CAAC,YAAY,CAAC,IAAI;YACjC,UAAU,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG;YAClC,aAAa,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;YACxC,SAAS,CAAC;YACV,QAAQ,CAAC;YACT,WAAW,CAAC;QAChB;IACJ;IACA,kBAAkB;QACd,IAAI,IAAI,CAAC,MAAM,EACX;QACJ,IAAI,CAAC,MAAM,GAAG;QACd,MAAO,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAE;YAChC,IAAI,CAAC,qBAAqB,GAAG;YAC7B,MAAM,KAAK,IAAI,CAAC,QAAQ;YACxB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI;gBAC5B,IAAI,CAAC,UAAU,CAAC;YACpB;QACJ;QACA,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,KAAK;IACL,QAAQ;QACJ,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,OAAO,aAAa,EAAE;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,MAAM,GAAG;QACd,oCAAoC;QACpC,IAAI,IAAI,CAAC,MAAM,EACX;QACJ,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,KAAK,IAAI;QAClE;IACJ;IACA,MAAM,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE;QACrC,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO;QAC/B,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,KAAK,IAAI;QAClE;IACJ;IACA,uBAAuB,KAAK,EAAE;QAC1B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC;QACzC,IAAI,CAAC,eAAe;IACxB;IACA,aAAa;IACb,qBAAqB;QACjB,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE;YACjC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB;YACpD,IAAI,CAAC,qBAAqB,GAAG;YAC7B,IAAI,CAAC,MAAM,GAAG;YACd,OAAO;QACX;QACA,OAAO;IACX;IACA,aAAa;IACb,WAAW;QACP,IAAI,CAAC,qBAAqB;QAC1B,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO;IACpC;IACA,WAAW,KAAK,EAAE;QACd,IAAI,CAAC,qBAAqB,IAAI;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC5B,IAAI,CAAC,YAAY,CAAC,OAAO;QAC7B;IACJ;IACA,wBAAwB,OAAO,EAAE,aAAa,EAAE;QAC5C,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,SAAS,gBAAgB;YACtD,0EAA0E;YAC1E,IAAI,CAAC,UAAU,CAAC,QAAQ,MAAM,GAAG;YACjC,OAAO;QACX;QACA,OAAO;IACX;IACA,gBAAgB;IAChB,uBAAuB;QACnB,IAAI,CAAC,YAAY,GAAG;YAChB,MAAM,iJAAA,CAAA,YAAS,CAAC,SAAS;YACzB,SAAS;YACT,OAAO,gJAAA,CAAA,SAAM,CAAC,OAAO;YACrB,aAAa;YACb,gBAAgB;YAChB,OAAO,EAAE;YACT,UAAU,IAAI,CAAC,kBAAkB,CAAC;QACtC;IACJ;IACA,qBAAqB;QACjB,IAAI,CAAC,YAAY,GAAG;YAChB,MAAM,iJAAA,CAAA,YAAS,CAAC,OAAO;YACvB,SAAS;YACT,OAAO,gJAAA,CAAA,SAAM,CAAC,OAAO;YACrB,aAAa;YACb,gBAAgB;YAChB,OAAO,EAAE;YACT,UAAU,IAAI,CAAC,kBAAkB,CAAC;QACtC;IACJ;IACA,oBAAoB,MAAM,EAAE;QACxB,IAAI,CAAC,YAAY,GAAG;YAChB,MAAM,iJAAA,CAAA,YAAS,CAAC,OAAO;YACvB,MAAM;YACN,UAAU,IAAI,CAAC,kBAAkB,CAAC;QACtC;IACJ;IACA,oBAAoB,WAAW,EAAE;QAC7B,IAAI,CAAC,YAAY,GAAG;YAChB,MAAM,iJAAA,CAAA,YAAS,CAAC,OAAO;YACvB,MAAM;YACN,aAAa;YACb,UAAU;YACV,UAAU;YACV,UAAU,IAAI,CAAC,eAAe;QAClC;IACJ;IACA,sBAAsB,IAAI,EAAE,KAAK,EAAE;QAC/B,IAAI,CAAC,qBAAqB,GAAG;YACzB;YACA;YACA,UAAU,IAAI,CAAC,eAAe;QAClC;IACJ;IACA,gBAAgB;IAChB,YAAY,eAAe,EAAE;QACzB,IAAI,CAAC,WAAW,GAAG;YACf,MAAM;YACN,OAAO;QACX;QACA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC;IACnD;IACA,iBAAiB;QACb,IAAI;QACJ,IAAI;QACJ,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,IAAI,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,MAAM,MAAM;YACrD,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;YACjC,IAAI,MAAM,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE;gBACxC,MAAM,gBAAiB,CAAC,KAAK,CAAC,KAAK,MAAM,QAAQ,EAAE,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAM,GAAG,KAAK,GAAG,OAAO,MAAM,CAAC;gBACrH,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe;gBAC3D,mBAAmB;gBACnB,IAAI,CAAC,eAAe;YACxB;QACJ,OACK;YACD,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,kBAAkB;QACpC;IACJ;IACA,kBAAkB;QACd,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI;YACrD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG;YACnD,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM;QAC7D;IACJ;IACA,gBAAgB;IAChB,aAAa,EAAE,EAAE;QACb,IAAI,CAAC,0BAA0B,CAAC,GAAG,QAAQ;QAC3C,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,GAAG,QAAQ,EAAE;YACb,GAAG,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI;YAC5C,GAAG,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG;YAC7C,GAAG,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;QACvD;QACA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACpD;IACA,sBAAsB;QAClB,MAAM,KAAK,IAAI,CAAC,YAAY;QAC5B,IAAI,CAAC,YAAY,CAAC;QAClB,GAAG,KAAK,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,OAAO;QAC9B,IAAI,GAAG,IAAI,KAAK,iJAAA,CAAA,YAAS,CAAC,SAAS,EAAE;YACjC,IAAI,CAAC,gBAAgB,GAAG,GAAG,OAAO;YAClC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;QAC5B,OACK;YACD,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;gBACrB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,oBAAoB;YACtC;YACA,IAAI,GAAG,WAAW,EAAE;gBAChB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,yBAAyB;YAC3C;YACA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC1B;QACA,IAAI,CAAC,YAAY,CAAC,eAAe;IACrC;IACA,mBAAmB,EAAE,EAAE;QACnB,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,eAAe;IACrC;IACA,mBAAmB,EAAE,EAAE;QACnB,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,eAAe;IACrC;IACA,2BAA2B,YAAY,EAAE;QACrC,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,iFAAiF;YACjF,iCAAiC;YACjC,IAAI,gBAAgB,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE;gBACrD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,OAAO,GAAG,aAAa,SAAS;gBACpE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,MAAM,GAAG,aAAa,QAAQ;gBAClE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,SAAS,GAAG,aAAa,WAAW;YAC5E;YACA,OAAQ,IAAI,CAAC,qBAAqB,CAAC,IAAI;gBACnC,KAAK,iJAAA,CAAA,YAAS,CAAC,SAAS;oBAAE;wBACtB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB;wBACnD;oBACJ;gBACA,KAAK,iJAAA,CAAA,YAAS,CAAC,cAAc;oBAAE;wBAC3B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB;wBACvD;oBACJ;gBACA,KAAK,iJAAA,CAAA,YAAS,CAAC,oBAAoB;oBAAE;wBACjC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,qBAAqB;wBAC7D;oBACJ;YACJ;YACA,IAAI,CAAC,qBAAqB,GAAG;QACjC;IACJ;IACA,gBAAgB;QACZ,MAAM,WAAW,IAAI,CAAC,kBAAkB,CAAC;QACzC,IAAI,UAAU;YACV,SAAS,OAAO,GAAG,SAAS,SAAS;YACrC,SAAS,MAAM,GAAG,SAAS,QAAQ;YACnC,SAAS,SAAS,GAAG,SAAS,WAAW;QAC7C;QACA,IAAI,CAAC,0BAA0B,CAAC;QAChC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YAAE,MAAM,iJAAA,CAAA,YAAS,CAAC,GAAG;YAAE;QAAS;QACnD,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,qBAAqB;IACrB,kGAAkG;IAClG,+GAA+G;IAC/G,8FAA8F;IAC9F,oCAAoC;IACpC,sDAAsD;IACtD,qFAAqF;IACrF,uGAAuG;IACvG,+GAA+G;IAC/G,mCAAmC,IAAI,EAAE,EAAE,EAAE;QACzC,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,KAAK,MAAM;gBAC1C,IAAI,CAAC,qBAAqB,CAAC,KAAK,IAAI;gBACpC;YACJ,OACK;gBACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAC/C,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,eAAe;gBACpD,IAAI,CAAC,YAAY,CAAC,eAAe;YACrC;QACJ;QACA,IAAI,CAAC,qBAAqB,CAAC,MAAM;IACrC;IACA,eAAe,EAAE,EAAE;QACf,MAAM,OAAO,aAAa,MACpB,iJAAA,CAAA,YAAS,CAAC,oBAAoB,GAC9B,OAAO,mJAAA,CAAA,cAAC,CAAC,IAAI,GACT,iJAAA,CAAA,YAAS,CAAC,cAAc,GACxB,iJAAA,CAAA,YAAS,CAAC,SAAS;QAC7B,IAAI,CAAC,kCAAkC,CAAC,MAAM,OAAO,aAAa,CAAC;IACvE;IACA,gDAAgD;IAChD,wGAAwG;IACxG,WAAW,EAAE,EAAE;QACX,IAAI,CAAC,kCAAkC,CAAC,iJAAA,CAAA,YAAS,CAAC,SAAS,EAAE;IACjE;IACA,8BAA8B;IAC9B,2BAA2B;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK;QAC7B,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;QACtC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG;QAC3C,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,gCAAgC,KAAK,iKAAA,CAAA,eAAY,CAAC,SAAS,GAAG,iKAAA,CAAA,eAAY,CAAC,MAAM;IACzH;IACA,mCAAmC;QAC/B,OAAQ,IAAI,CAAC,WAAW,KAAK,MAAM,6BAA6B,IAC5D,IAAI,CAAC,WAAW,KAAK,MAAM,6BAA6B,IACxD,IAAI,CAAC,WAAW,KAAK,MAAM,wBAAwB;IAC3D;IACA,4CAA4C,EAAE,EAAE;QAC5C,IAAI,IAAI,CAAC,gCAAgC,IAAI;YACzC,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,OAAO,aAAa,CAAC;QACnD,OACK;YACD,IAAI,CAAC,cAAc,CAAC;QACxB;IACJ;IACA,+EAA+E;IAC/E,WAAW,EAAE,EAAE;QACX,OAAQ,IAAI,CAAC,KAAK;YACd,KAAK,MAAM,IAAI;gBAAE;oBACb,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,MAAM,MAAM;gBAAE;oBACf,IAAI,CAAC,YAAY,CAAC;oBAClB;gBACJ;YACA,KAAK,MAAM,OAAO;gBAAE;oBAChB,IAAI,CAAC,aAAa,CAAC;oBACnB;gBACJ;YACA,KAAK,MAAM,WAAW;gBAAE;oBACpB,IAAI,CAAC,gBAAgB,CAAC;oBACtB;gBACJ;YACA,KAAK,MAAM,SAAS;gBAAE;oBAClB,IAAI,CAAC,eAAe,CAAC;oBACrB;gBACJ;YACA,KAAK,MAAM,QAAQ;gBAAE;oBACjB,IAAI,CAAC,aAAa,CAAC;oBACnB;gBACJ;YACA,KAAK,MAAM,YAAY;gBAAE;oBACrB,IAAI,CAAC,gBAAgB,CAAC;oBACtB;gBACJ;YACA,KAAK,MAAM,QAAQ;gBAAE;oBACjB,IAAI,CAAC,aAAa,CAAC;oBACnB;gBACJ;YACA,KAAK,MAAM,qBAAqB;gBAAE;oBAC9B,IAAI,CAAC,wBAAwB,CAAC;oBAC9B;gBACJ;YACA,KAAK,MAAM,mBAAmB;gBAAE;oBAC5B,IAAI,CAAC,sBAAsB,CAAC;oBAC5B;gBACJ;YACA,KAAK,MAAM,mBAAmB;gBAAE;oBAC5B,IAAI,CAAC,sBAAsB,CAAC;oBAC5B;gBACJ;YACA,KAAK,MAAM,sBAAsB;gBAAE;oBAC/B,IAAI,CAAC,yBAAyB,CAAC;oBAC/B;gBACJ;YACA,KAAK,MAAM,oBAAoB;gBAAE;oBAC7B,IAAI,CAAC,uBAAuB,CAAC;oBAC7B;gBACJ;YACA,KAAK,MAAM,oBAAoB;gBAAE;oBAC7B,IAAI,CAAC,uBAAuB,CAAC;oBAC7B;gBACJ;YACA,KAAK,MAAM,0BAA0B;gBAAE;oBACnC,IAAI,CAAC,4BAA4B,CAAC;oBAClC;gBACJ;YACA,KAAK,MAAM,wBAAwB;gBAAE;oBACjC,IAAI,CAAC,0BAA0B,CAAC;oBAChC;gBACJ;YACA,KAAK,MAAM,wBAAwB;gBAAE;oBACjC,IAAI,CAAC,0BAA0B,CAAC;oBAChC;gBACJ;YACA,KAAK,MAAM,wBAAwB;gBAAE;oBACjC,IAAI,CAAC,2BAA2B,CAAC;oBACjC;gBACJ;YACA,KAAK,MAAM,6BAA6B;gBAAE;oBACtC,IAAI,CAAC,+BAA+B,CAAC;oBACrC;gBACJ;YACA,KAAK,MAAM,mBAAmB;gBAAE;oBAC5B,IAAI,CAAC,uBAAuB,CAAC;oBAC7B;gBACJ;YACA,KAAK,MAAM,wBAAwB;gBAAE;oBACjC,IAAI,CAAC,2BAA2B,CAAC;oBACjC;gBACJ;YACA,KAAK,MAAM,6BAA6B;gBAAE;oBACtC,IAAI,CAAC,+BAA+B,CAAC;oBACrC;gBACJ;YACA,KAAK,MAAM,kCAAkC;gBAAE;oBAC3C,IAAI,CAAC,mCAAmC,CAAC;oBACzC;gBACJ;YACA,KAAK,MAAM,gCAAgC;gBAAE;oBACzC,IAAI,CAAC,iCAAiC,CAAC;oBACvC;gBACJ;YACA,KAAK,MAAM,gCAAgC;gBAAE;oBACzC,IAAI,CAAC,iCAAiC,CAAC;oBACvC;gBACJ;YACA,KAAK,MAAM,+BAA+B;gBAAE;oBACxC,IAAI,CAAC,iCAAiC,CAAC;oBACvC;gBACJ;YACA,KAAK,MAAM,0BAA0B;gBAAE;oBACnC,IAAI,CAAC,6BAA6B,CAAC;oBACnC;gBACJ;YACA,KAAK,MAAM,+BAA+B;gBAAE;oBACxC,IAAI,CAAC,iCAAiC,CAAC;oBACvC;gBACJ;YACA,KAAK,MAAM,oCAAoC;gBAAE;oBAC7C,IAAI,CAAC,qCAAqC,CAAC;oBAC3C;gBACJ;YACA,KAAK,MAAM,yCAAyC;gBAAE;oBAClD,IAAI,CAAC,yCAAyC,CAAC;oBAC/C;gBACJ;YACA,KAAK,MAAM,6BAA6B;gBAAE;oBACtC,IAAI,CAAC,+BAA+B,CAAC;oBACrC;gBACJ;YACA,KAAK,MAAM,qBAAqB;gBAAE;oBAC9B,IAAI,CAAC,yBAAyB,CAAC;oBAC/B;gBACJ;YACA,KAAK,MAAM,cAAc;gBAAE;oBACvB,IAAI,CAAC,mBAAmB,CAAC;oBACzB;gBACJ;YACA,KAAK,MAAM,oBAAoB;gBAAE;oBAC7B,IAAI,CAAC,wBAAwB,CAAC;oBAC9B;gBACJ;YACA,KAAK,MAAM,sBAAsB;gBAAE;oBAC/B,IAAI,CAAC,0BAA0B,CAAC;oBAChC;gBACJ;YACA,KAAK,MAAM,6BAA6B;gBAAE;oBACtC,IAAI,CAAC,gCAAgC,CAAC;oBACtC;gBACJ;YACA,KAAK,MAAM,6BAA6B;gBAAE;oBACtC,IAAI,CAAC,gCAAgC,CAAC;oBACtC;gBACJ;YACA,KAAK,MAAM,wBAAwB;gBAAE;oBACjC,IAAI,CAAC,4BAA4B,CAAC;oBAClC;gBACJ;YACA,KAAK,MAAM,4BAA4B;gBAAE;oBACrC,IAAI,CAAC,+BAA+B,CAAC;oBACrC;gBACJ;YACA,KAAK,MAAM,sBAAsB;gBAAE;oBAC/B,IAAI,CAAC,yBAAyB,CAAC;oBAC/B;gBACJ;YACA,KAAK,MAAM,aAAa;gBAAE;oBACtB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,MAAM,uBAAuB;gBAAE;oBAChC,IAAI,CAAC,2BAA2B,CAAC;oBACjC;gBACJ;YACA,KAAK,MAAM,aAAa;gBAAE;oBACtB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,MAAM,kBAAkB;gBAAE;oBAC3B,IAAI,CAAC,sBAAsB,CAAC;oBAC5B;gBACJ;YACA,KAAK,MAAM,OAAO;gBAAE;oBAChB,IAAI,CAAC,aAAa,CAAC;oBACnB;gBACJ;YACA,KAAK,MAAM,sBAAsB;gBAAE;oBAC/B,IAAI,CAAC,yBAAyB,CAAC;oBAC/B;gBACJ;YACA,KAAK,MAAM,2BAA2B;gBAAE;oBACpC,IAAI,CAAC,6BAA6B,CAAC;oBACnC;gBACJ;YACA,KAAK,MAAM,gCAAgC;gBAAE;oBACzC,IAAI,CAAC,iCAAiC,CAAC;oBACvC;gBACJ;YACA,KAAK,MAAM,qCAAqC;gBAAE;oBAC9C,IAAI,CAAC,qCAAqC,CAAC;oBAC3C;gBACJ;YACA,KAAK,MAAM,gBAAgB;gBAAE;oBACzB,IAAI,CAAC,oBAAoB,CAAC;oBAC1B;gBACJ;YACA,KAAK,MAAM,WAAW;gBAAE;oBACpB,IAAI,CAAC,gBAAgB,CAAC;oBACtB;gBACJ;YACA,KAAK,MAAM,gBAAgB;gBAAE;oBACzB,IAAI,CAAC,oBAAoB,CAAC;oBAC1B;gBACJ;YACA,KAAK,MAAM,OAAO;gBAAE;oBAChB,IAAI,CAAC,aAAa,CAAC;oBACnB;gBACJ;YACA,KAAK,MAAM,mBAAmB;gBAAE;oBAC5B,IAAI,CAAC,uBAAuB,CAAC;oBAC7B;gBACJ;YACA,KAAK,MAAM,YAAY;gBAAE;oBACrB,IAAI,CAAC,iBAAiB,CAAC;oBACvB;gBACJ;YACA,KAAK,MAAM,kBAAkB;gBAAE;oBAC3B,IAAI,CAAC,sBAAsB,CAAC;oBAC5B;gBACJ;YACA,KAAK,MAAM,4BAA4B;gBAAE;oBACrC,IAAI,CAAC,+BAA+B,CAAC;oBACrC;gBACJ;YACA,KAAK,MAAM,gCAAgC;gBAAE;oBACzC,IAAI,CAAC,mCAAmC,CAAC;oBACzC;gBACJ;YACA,KAAK,MAAM,uCAAuC;gBAAE;oBAChD,IAAI,CAAC,yCAAyC,CAAC;oBAC/C;gBACJ;YACA,KAAK,MAAM,uCAAuC;gBAAE;oBAChD,IAAI,CAAC,yCAAyC,CAAC;oBAC/C;gBACJ;YACA,KAAK,MAAM,+BAA+B;gBAAE;oBACxC,IAAI,CAAC,kCAAkC,CAAC;oBACxC;gBACJ;YACA,KAAK,MAAM,6CAA6C;gBAAE;oBACtD,IAAI,CAAC,8CAA8C,CAAC;oBACpD;gBACJ;YACA,KAAK,MAAM,4BAA4B;gBAAE;oBACrC,IAAI,CAAC,+BAA+B,CAAC;oBACrC;gBACJ;YACA,KAAK,MAAM,gCAAgC;gBAAE;oBACzC,IAAI,CAAC,mCAAmC,CAAC;oBACzC;gBACJ;YACA,KAAK,MAAM,uCAAuC;gBAAE;oBAChD,IAAI,CAAC,yCAAyC,CAAC;oBAC/C;gBACJ;YACA,KAAK,MAAM,uCAAuC;gBAAE;oBAChD,IAAI,CAAC,yCAAyC,CAAC;oBAC/C;gBACJ;YACA,KAAK,MAAM,+BAA+B;gBAAE;oBACxC,IAAI,CAAC,kCAAkC,CAAC;oBACxC;gBACJ;YACA,KAAK,MAAM,aAAa;gBAAE;oBACtB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,MAAM,aAAa;gBAAE;oBACtB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,MAAM,qBAAqB;gBAAE;oBAC9B,IAAI,CAAC,yBAAyB,CAAC;oBAC/B;gBACJ;YACA,KAAK,MAAM,iBAAiB;gBAAE;oBAC1B,IAAI,CAAC,qBAAqB,CAAC;oBAC3B;gBACJ;YACA,KAAK,MAAM,mBAAmB;gBAAE;oBAC5B,IAAI,CAAC,wBAAwB;oBAC7B;gBACJ;YACA,KAAK,MAAM,mBAAmB;gBAAE;oBAC5B,IAAI,CAAC,wBAAwB,CAAC;oBAC9B;gBACJ;YACA;gBAAS;oBACL,MAAM,IAAI,MAAM;gBACpB;QACJ;IACJ;IACA,gBAAgB;IAChB,aAAa;IACb,oEAAoE;IACpE,WAAW,EAAE,EAAE;QACX,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,QAAQ;oBAC3B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,wBAAwB;oBAC7B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,cAAc,CAAC;oBACpB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,gBAAgB;IAChB,oEAAoE;IACpE,aAAa,EAAE,EAAE;QACb,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,wBAAwB;oBAC7B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,qBAAqB;oBACxC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,UAAU,CAAC,mJAAA,CAAA,wBAAqB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,gBAAgB;IAChB,oEAAoE;IACpE,cAAc,EAAE,EAAE;QACd,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,sBAAsB;oBACzC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,UAAU,CAAC,mJAAA,CAAA,wBAAqB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,oBAAoB;IACpB,oEAAoE;IACpE,iBAAiB,EAAE,EAAE;QACjB,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,0BAA0B;oBAC7C;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,UAAU,CAAC,mJAAA,CAAA,wBAAqB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,kBAAkB;IAClB,oEAAoE;IACpE,gBAAgB,EAAE,EAAE;QAChB,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,UAAU,CAAC,mJAAA,CAAA,wBAAqB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,iBAAiB;IACjB,oEAAoE;IACpE,cAAc,EAAE,EAAE;QACd,IAAI,cAAc,KAAK;YACnB,IAAI,CAAC,oBAAoB;YACzB,IAAI,CAAC,KAAK,GAAG,MAAM,QAAQ;YAC3B,IAAI,CAAC,aAAa,CAAC;QACvB,OAEI,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,gBAAgB;gBAAE;oBACrB,IAAI,CAAC,KAAK,GAAG,MAAM,uBAAuB;oBAC1C;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,OAAO;gBAAE;oBACZ,IAAI,CAAC,KAAK,GAAG,MAAM,YAAY;oBAC/B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,aAAa;gBAAE;oBAClB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,sCAAsC;oBACpD,IAAI,CAAC,mBAAmB,CAAC;oBACzB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;oBAChC,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,gBAAgB;oBAC9B,IAAI,CAAC,UAAU,CAAC;oBAChB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,IAAI,CAAC,UAAU,CAAC;oBAChB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,UAAU,CAAC;gBACpB;QACJ;IACR;IACA,qBAAqB;IACrB,oEAAoE;IACpE,iBAAiB,EAAE,EAAE;QACjB,IAAI,cAAc,KAAK;YACnB,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,KAAK,GAAG,MAAM,QAAQ;YAC3B,IAAI,CAAC,aAAa,CAAC;QACvB,OAEI,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,iBAAiB;oBAC/B,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,gBAAgB;oBAC9B,IAAI,CAAC,UAAU,CAAC;oBAChB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,IAAI,CAAC,mBAAmB,CAAC;oBACzB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;oBAChC,IAAI,CAAC,kBAAkB,CAAC;gBAC5B;QACJ;IACR;IACA,iBAAiB;IACjB,oEAAoE;IACpE,cAAc,EAAE,EAAE;QACd,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,qBAAqB;oBACxC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,OAAO;gBAAE;oBACZ,IAAI,CAAC,KAAK,GAAG,MAAM,sBAAsB;oBACzC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,mBAAmB;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,MAAM,OAAO,IAAI,mJAAA,CAAA,wBAAqB;oBACtC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,QAAQ;oBACtB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,MAAM,OAAO,IAAI,OAAO,aAAa,CAAC,aAAa,MAAM,aAAa,MAAM;gBAChF;QACJ;IACJ;IACA,8BAA8B;IAC9B,oEAAoE;IACpE,yBAAyB,EAAE,EAAE;QACzB,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,OAAO,EAAE;YAClB,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;QAC1C,OACK;YACD,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,MAAM;YACzB,IAAI,CAAC,YAAY,CAAC;QACtB;IACJ;IACA,4BAA4B;IAC5B,oEAAoE;IACpE,uBAAuB,EAAE,EAAE;QACvB,IAAI,cAAc,KAAK;YACnB,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;YACtC,IAAI,CAAC,sBAAsB,CAAC;QAChC,OACK;YACD,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,MAAM;YACzB,IAAI,CAAC,YAAY,CAAC;QACtB;IACJ;IACA,oBAAoB,GAAG,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,QAAQ;YAC7D,OAAO,CAAC,IAAI,CAAC,kBAAkB;QACnC;QACA,IAAI,CAAC,kBAAkB;QACvB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB;QACrC,MAAM,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM;QAC9D,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM;oBAC5C,IAAI,CAAC,KAAK,GAAG,MAAM,qBAAqB;oBACxC,OAAO;gBACX;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,OAAO;gBAAE;oBACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM;oBAC5C,IAAI,CAAC,KAAK,GAAG,MAAM,sBAAsB;oBACzC,OAAO;gBACX;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM;oBAC5C,IAAI,CAAC,mBAAmB;oBACxB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,OAAO;gBACX;YACA;gBAAS;oBACL,OAAO,CAAC,IAAI,CAAC,kBAAkB;gBACnC;QACJ;IACJ;IACA,4BAA4B;IAC5B,oEAAoE;IACpE,uBAAuB,EAAE,EAAE;QACvB,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK;YAC9B,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,MAAM;YACzB,IAAI,CAAC,YAAY,CAAC;QACtB;IACJ;IACA,+BAA+B;IAC/B,oEAAoE;IACpE,0BAA0B,EAAE,EAAE;QAC1B,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,OAAO,EAAE;YAClB,IAAI,CAAC,KAAK,GAAG,MAAM,oBAAoB;QAC3C,OACK;YACD,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;YAC1B,IAAI,CAAC,aAAa,CAAC;QACvB;IACJ;IACA,6BAA6B;IAC7B,oEAAoE;IACpE,wBAAwB,EAAE,EAAE;QACxB,IAAI,cAAc,KAAK;YACnB,IAAI,CAAC,KAAK,GAAG,MAAM,oBAAoB;YACvC,IAAI,CAAC,uBAAuB,CAAC;QACjC,OACK;YACD,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;YAC1B,IAAI,CAAC,aAAa,CAAC;QACvB;IACJ;IACA,6BAA6B;IAC7B,oEAAoE;IACpE,wBAAwB,EAAE,EAAE;QACxB,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK;YAC9B,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;YAC1B,IAAI,CAAC,aAAa,CAAC;QACvB;IACJ;IACA,mCAAmC;IACnC,oEAAoE;IACpE,6BAA6B,EAAE,EAAE;QAC7B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,OAAO;gBAAE;oBACZ,IAAI,CAAC,KAAK,GAAG,MAAM,wBAAwB;oBAC3C;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,gBAAgB;gBAAE;oBACrB,IAAI,CAAC,KAAK,GAAG,MAAM,wBAAwB;oBAC3C,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,UAAU,CAAC;oBAChB,IAAI,CAAC,KAAK,GAAG,MAAM,WAAW;oBAC9B,IAAI,CAAC,gBAAgB,CAAC;gBAC1B;QACJ;IACJ;IACA,iCAAiC;IACjC,oEAAoE;IACpE,2BAA2B,EAAE,EAAE;QAC3B,IAAI,cAAc,KAAK;YACnB,IAAI,CAAC,KAAK,GAAG,MAAM,wBAAwB;YAC3C,IAAI,CAAC,0BAA0B,CAAC;QACpC,OACK;YACD,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,WAAW;YAC9B,IAAI,CAAC,gBAAgB,CAAC;QAC1B;IACJ;IACA,iCAAiC;IACjC,oEAAoE;IACpE,2BAA2B,EAAE,EAAE;QAC3B,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK;YAC9B,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,WAAW;YAC9B,IAAI,CAAC,gBAAgB,CAAC;QAC1B;IACJ;IACA,iCAAiC;IACjC,oEAAoE;IACpE,4BAA4B,EAAE,EAAE;QAC5B,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,YAAY,EAAE;YACvB,IAAI,CAAC,KAAK,GAAG,MAAM,6BAA6B;YAChD,IAAI,CAAC,UAAU,CAAC;QACpB,OACK;YACD,IAAI,CAAC,KAAK,GAAG,MAAM,WAAW;YAC9B,IAAI,CAAC,gBAAgB,CAAC;QAC1B;IACJ;IACA,sCAAsC;IACtC,oEAAoE;IACpE,gCAAgC,EAAE,EAAE;QAChC,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,YAAY,EAAE;YACvB,IAAI,CAAC,KAAK,GAAG,MAAM,6BAA6B;YAChD,IAAI,CAAC,UAAU,CAAC;QACpB,OACK;YACD,IAAI,CAAC,KAAK,GAAG,MAAM,WAAW;YAC9B,IAAI,CAAC,gBAAgB,CAAC;QAC1B;IACJ;IACA,4BAA4B;IAC5B,oEAAoE;IACpE,wBAAwB,EAAE,EAAE;QACxB,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,wBAAwB;oBAC3C,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,kCAAkC;oBACrD;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,UAAU,CAAC,mJAAA,CAAA,wBAAqB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,iCAAiC;IACjC,oEAAoE;IACpE,4BAA4B,EAAE,EAAE;QAC5B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,6BAA6B;oBAChD,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,kCAAkC;oBACrD;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;oBACtC,IAAI,CAAC,UAAU,CAAC,mJAAA,CAAA,wBAAqB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;oBACtC,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,sCAAsC;IACtC,oEAAoE;IACpE,gCAAgC,EAAE,EAAE;QAChC,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,kCAAkC;oBACrD;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,KAAK,GAAG,MAAM,WAAW;oBAC9B,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;oBACtC,IAAI,CAAC,UAAU,CAAC,mJAAA,CAAA,wBAAqB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;oBACtC,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,2CAA2C;IAC3C,oEAAoE;IACpE,oCAAoC,EAAE,EAAE;QACpC,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,OAAO,EAAE;YAClB,IAAI,CAAC,KAAK,GAAG,MAAM,gCAAgC;QACvD,OACK,IAAI,cAAc,KAAK;YACxB,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,+BAA+B;YAClD,IAAI,CAAC,iCAAiC,CAAC;QAC3C,OACK;YACD,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;YACtC,IAAI,CAAC,uBAAuB,CAAC;QACjC;IACJ;IACA,yCAAyC;IACzC,oEAAoE;IACpE,kCAAkC,EAAE,EAAE;QAClC,IAAI,cAAc,KAAK;YACnB,IAAI,CAAC,KAAK,GAAG,MAAM,gCAAgC;YACnD,IAAI,CAAC,iCAAiC,CAAC;QAC3C,OACK;YACD,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;YACtC,IAAI,CAAC,uBAAuB,CAAC;QACjC;IACJ;IACA,yCAAyC;IACzC,oEAAoE;IACpE,kCAAkC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK;YAC9B,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;YACtC,IAAI,CAAC,uBAAuB,CAAC;QACjC;IACJ;IACA,wCAAwC;IACxC,oEAAoE;IACpE,kCAAkC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,mJAAA,CAAA,YAAE,CAAC,MAAM,EAAE,UACxC,oCAAoC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mJAAA,CAAA,YAAE,CAAC,MAAM,CAAC,MAAM,IAAI;YAC/E,IAAI,CAAC,cAAc,CAAC;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,mJAAA,CAAA,YAAE,CAAC,MAAM,CAAC,MAAM,EAAE,IAAK;gBACvC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ;YACrC;YACA,IAAI,CAAC,KAAK,GAAG,MAAM,0BAA0B;QACjD,OACK,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI;YACjC,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;YACtC,IAAI,CAAC,uBAAuB,CAAC;QACjC;IACJ;IACA,mCAAmC;IACnC,oEAAoE;IACpE,8BAA8B,EAAE,EAAE;QAC9B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,+BAA+B;oBAClD,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,yCAAyC;oBAC5D,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,UAAU,CAAC,mJAAA,CAAA,wBAAqB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,wCAAwC;IACxC,oEAAoE;IACpE,kCAAkC,EAAE,EAAE;QAClC,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,oCAAoC;oBACvD,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,yCAAyC;oBAC5D,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,KAAK,GAAG,MAAM,0BAA0B;oBAC7C,IAAI,CAAC,UAAU,CAAC,mJAAA,CAAA,wBAAqB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,KAAK,GAAG,MAAM,0BAA0B;oBAC7C,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,6CAA6C;IAC7C,oEAAoE;IACpE,sCAAsC,EAAE,EAAE;QACtC,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,yCAAyC;oBAC5D,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,KAAK,GAAG,MAAM,WAAW;oBAC9B,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,KAAK,GAAG,MAAM,0BAA0B;oBAC7C,IAAI,CAAC,UAAU,CAAC,mJAAA,CAAA,wBAAqB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,KAAK,GAAG,MAAM,0BAA0B;oBAC7C,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,kDAAkD;IAClD,oEAAoE;IACpE,0CAA0C,EAAE,EAAE;QAC1C,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,OAAO,EAAE;YAClB,IAAI,CAAC,KAAK,GAAG,MAAM,6BAA6B;YAChD,IAAI,CAAC,UAAU,CAAC;QACpB,OACK;YACD,IAAI,CAAC,KAAK,GAAG,MAAM,0BAA0B;YAC7C,IAAI,CAAC,6BAA6B,CAAC;QACvC;IACJ;IACA,sCAAsC;IACtC,oEAAoE;IACpE,gCAAgC,EAAE,EAAE;QAChC,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,mJAAA,CAAA,YAAE,CAAC,MAAM,EAAE,UACxC,oCAAoC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mJAAA,CAAA,YAAE,CAAC,MAAM,CAAC,MAAM,IAAI;YAC/E,IAAI,CAAC,cAAc,CAAC;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,mJAAA,CAAA,YAAE,CAAC,MAAM,CAAC,MAAM,EAAE,IAAK;gBACvC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ;YACrC;YACA,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;QAC1C,OACK,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI;YACjC,IAAI,CAAC,KAAK,GAAG,MAAM,0BAA0B;YAC7C,IAAI,CAAC,6BAA6B,CAAC;QACvC;IACJ;IACA,8BAA8B;IAC9B,oEAAoE;IACpE,0BAA0B,EAAE,EAAE;QAC1B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBAEd;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,OAAO;YACd,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;YACxB,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,KAAK,GAAG,MAAM,oBAAoB;oBACvC,IAAI,CAAC,wBAAwB,CAAC;oBAC9B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,WAAW;gBAAE;oBAChB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uCAAuC;oBACrD,IAAI,CAAC,WAAW,CAAC;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,cAAc;oBACjC;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,WAAW,CAAC;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,cAAc;oBACjC,IAAI,CAAC,mBAAmB,CAAC;gBAC7B;QACJ;IACJ;IACA,uBAAuB;IACvB,oEAAoE;IACpE,oBAAoB,EAAE,EAAE;QACpB,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,OAAO;YACd,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;YACxB,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,cAAc;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,oBAAoB;oBACvC,IAAI,CAAC,wBAAwB,CAAC;oBAC9B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,WAAW;gBAAE;oBAChB,IAAI,CAAC,cAAc;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,sBAAsB;oBACzC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;YACrB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,kCAAkC;oBAChD,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,OAAO,aAAa,CAAC;oBAC9C;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,mJAAA,CAAA,wBAAqB;oBAC9C;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,OAAO,aAAa,CAAC,aAAa,MAAM,aAAa,MAAM;gBACxF;QACJ;IACJ;IACA,6BAA6B;IAC7B,oEAAoE;IACpE,yBAAyB,EAAE,EAAE;QACzB,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBAEd;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,OAAO;gBAAE;oBACZ,IAAI,CAAC,KAAK,GAAG,MAAM,sBAAsB;oBACzC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,WAAW;gBAAE;oBAChB,IAAI,CAAC,KAAK,GAAG,MAAM,sBAAsB;oBACzC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,mBAAmB;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,QAAQ;oBACtB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,WAAW,CAAC;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,cAAc;oBACjC,IAAI,CAAC,mBAAmB,CAAC;gBAC7B;QACJ;IACJ;IACA,+BAA+B;IAC/B,oEAAoE;IACpE,2BAA2B,EAAE,EAAE;QAC3B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBAEd;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,6BAA6B;oBAChD;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;gBAAE;oBACf,IAAI,CAAC,KAAK,GAAG,MAAM,6BAA6B;oBAChD;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,qBAAqB;oBACnC,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,mBAAmB;oBACxB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,KAAK,GAAG,MAAM,wBAAwB;oBAC3C,IAAI,CAAC,4BAA4B,CAAC;gBACtC;QACJ;IACJ;IACA,wCAAwC;IACxC,oEAAoE;IACpE,iCAAiC,EAAE,EAAE;QACjC,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,4BAA4B;oBAC/C;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,wBAAwB;oBAC7B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,mJAAA,CAAA,wBAAqB;oBAC/C;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,QAAQ;oBACtB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,OAAO,aAAa,CAAC;gBACnD;QACJ;IACJ;IACA,wCAAwC;IACxC,oEAAoE;IACpE,iCAAiC,EAAE,EAAE;QACjC,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;gBAAE;oBACf,IAAI,CAAC,KAAK,GAAG,MAAM,4BAA4B;oBAC/C;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,wBAAwB;oBAC7B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,mJAAA,CAAA,wBAAqB;oBAC/C;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,QAAQ;oBACtB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,OAAO,aAAa,CAAC;gBACnD;QACJ;IACJ;IACA,mCAAmC;IACnC,oEAAoE;IACpE,6BAA6B,EAAE,EAAE;QAC7B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,eAAe;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,qBAAqB;oBACxC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,wBAAwB;oBAC7B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,eAAe;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,mBAAmB;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,mJAAA,CAAA,wBAAqB;oBAC/C;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;YACrB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;YACrB,KAAK,mJAAA,CAAA,cAAC,CAAC,WAAW;YAClB,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,2CAA2C;oBACzD,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,OAAO,aAAa,CAAC;oBAC/C;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,QAAQ;oBACtB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,OAAO,aAAa,CAAC;gBACnD;QACJ;IACJ;IACA,uCAAuC;IACvC,oEAAoE;IACpE,gCAAgC,EAAE,EAAE;QAChC,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,eAAe;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,qBAAqB;oBACxC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,OAAO;gBAAE;oBACZ,IAAI,CAAC,eAAe;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,sBAAsB;oBACzC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,eAAe;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,mBAAmB;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,QAAQ;oBACtB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,kCAAkC;oBAChD,IAAI,CAAC,KAAK,GAAG,MAAM,qBAAqB;oBACxC,IAAI,CAAC,yBAAyB,CAAC;gBACnC;QACJ;IACJ;IACA,+BAA+B;IAC/B,oEAAoE;IACpE,0BAA0B,EAAE,EAAE;QAC1B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,MAAM,QAAQ,IAAI,CAAC,YAAY;oBAC/B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,mBAAmB;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,QAAQ;oBACtB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,sBAAsB;oBACpC,IAAI,CAAC,KAAK,GAAG,MAAM,qBAAqB;oBACxC,IAAI,CAAC,yBAAyB,CAAC;gBACnC;QACJ;IACJ;IACA,sBAAsB;IACtB,oEAAoE;IACpE,mBAAmB,EAAE,EAAE;QACnB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,MAAM,IAAI,IAAI,mJAAA,CAAA,wBAAqB;oBACnC;gBACJ;YACA;gBAAS;oBACL,MAAM,IAAI,IAAI,OAAO,aAAa,CAAC;gBACvC;QACJ;IACJ;IACA,gCAAgC;IAChC,oEAAoE;IACpE,4BAA4B,EAAE,EAAE;QAC5B,IAAI,IAAI,CAAC,uBAAuB,CAAC,mJAAA,CAAA,YAAE,CAAC,SAAS,EAAE,OAAO;YAClD,IAAI,CAAC,mBAAmB,CAAC,mJAAA,CAAA,YAAE,CAAC,SAAS,CAAC,MAAM,GAAG;YAC/C,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;QACpC,OACK,IAAI,IAAI,CAAC,uBAAuB,CAAC,mJAAA,CAAA,YAAE,CAAC,OAAO,EAAE,QAAQ;YACtD,qHAAqH;YACrH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,mJAAA,CAAA,YAAE,CAAC,OAAO,CAAC,MAAM,GAAG;YACnE,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;QAC9B,OACK,IAAI,IAAI,CAAC,uBAAuB,CAAC,mJAAA,CAAA,YAAE,CAAC,WAAW,EAAE,OAAO;YACzD,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;YACpC,OACK;gBACD,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,kBAAkB;gBAChC,IAAI,CAAC,mBAAmB,CAAC,mJAAA,CAAA,YAAE,CAAC,WAAW,CAAC,MAAM,GAAG;gBACjD,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG;gBACzB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;YACpC;QACJ,OAGK,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI;YACjC,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,wBAAwB;YACtC,IAAI,CAAC,mBAAmB,CAAC;YACzB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;YAChC,IAAI,CAAC,kBAAkB,CAAC;QAC5B;IACJ;IACA,sBAAsB;IACtB,oEAAoE;IACpE,mBAAmB,EAAE,EAAE;QACnB,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,kBAAkB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,2BAA2B;oBACzC,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,MAAM,QAAQ,IAAI,CAAC,YAAY;oBAC/B,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;oBAC1B,IAAI,CAAC,aAAa,CAAC;gBACvB;QACJ;IACJ;IACA,2BAA2B;IAC3B,oEAAoE;IACpE,uBAAuB,EAAE,EAAE;QACvB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,WAAW;oBAC9B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,2BAA2B;oBACzC,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,MAAM,IAAI,IAAI;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;oBAC1B,IAAI,CAAC,aAAa,CAAC;gBACvB;QACJ;IACJ;IACA,gBAAgB;IAChB,oEAAoE;IACpE,cAAc,EAAE,EAAE;QACd,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,gBAAgB;oBACnC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,MAAM,IAAI,IAAI;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,sBAAsB;oBACzC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,MAAM,IAAI,IAAI,mJAAA,CAAA,wBAAqB;oBACnC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,MAAM,IAAI,IAAI,OAAO,aAAa,CAAC;gBACvC;QACJ;IACJ;IACA,+BAA+B;IAC/B,oEAAoE;IACpE,0BAA0B,EAAE,EAAE;QAC1B,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,gBAAgB;gBAAE;oBACrB,MAAM,IAAI,IAAI;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,2BAA2B;oBAC9C;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,MAAM,IAAI,IAAI;oBACd;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;oBAC1B,IAAI,CAAC,aAAa,CAAC;gBACvB;QACJ;IACJ;IACA,oCAAoC;IACpC,oEAAoE;IACpE,8BAA8B,EAAE,EAAE;QAC9B,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,YAAY,EAAE;YACvB,IAAI,CAAC,KAAK,GAAG,MAAM,gCAAgC;QACvD,OACK;YACD,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;YAC1B,IAAI,CAAC,aAAa,CAAC;QACvB;IACJ;IACA,yCAAyC;IACzC,oEAAoE;IACpE,kCAAkC,EAAE,EAAE;QAClC,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,YAAY,EAAE;YACvB,IAAI,CAAC,KAAK,GAAG,MAAM,qCAAqC;QAC5D,OACK;YACD,IAAI,CAAC,KAAK,GAAG,MAAM,gBAAgB;YACnC,IAAI,CAAC,oBAAoB,CAAC;QAC9B;IACJ;IACA,8CAA8C;IAC9C,oEAAoE;IACpE,sCAAsC,EAAE,EAAE;QACtC,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,iBAAiB,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,aAAa;QAC/B;QACA,IAAI,CAAC,KAAK,GAAG,MAAM,WAAW;QAC9B,IAAI,CAAC,gBAAgB,CAAC;IAC1B;IACA,yBAAyB;IACzB,oEAAoE;IACpE,qBAAqB,EAAE,EAAE;QACrB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,WAAW;oBAC9B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,MAAM,IAAI,IAAI;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;oBAC1B,IAAI,CAAC,aAAa,CAAC;gBACvB;QACJ;IACJ;IACA,oBAAoB;IACpB,oEAAoE;IACpE,iBAAiB,EAAE,EAAE;QACjB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,gBAAgB;gBAAE;oBACrB,IAAI,CAAC,KAAK,GAAG,MAAM,gBAAgB;oBACnC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,MAAM,IAAI,IAAI;oBACd;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,MAAM,IAAI,IAAI;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;oBAC1B,IAAI,CAAC,aAAa,CAAC;gBACvB;QACJ;IACJ;IACA,yBAAyB;IACzB,oEAAoE;IACpE,qBAAqB,EAAE,EAAE;QACrB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,YAAY;gBAAE;oBACjB,MAAM,IAAI,IAAI;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,gBAAgB;oBACnC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,wBAAwB;oBACtC,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,MAAM,IAAI,IAAI;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;oBAC1B,IAAI,CAAC,aAAa,CAAC;gBACvB;QACJ;IACJ;IACA,gBAAgB;IAChB,oEAAoE;IACpE,cAAc,EAAE,EAAE;QACd,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;oBACtC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;oBACtC,IAAI,CAAC,uBAAuB,CAAC;oBAC7B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,IAAI,CAAC,mBAAmB,CAAC;oBACzB,MAAM,QAAQ,IAAI,CAAC,YAAY;oBAC/B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,kCAAkC;oBAChD,IAAI,CAAC,KAAK,GAAG,MAAM,mBAAmB;oBACtC,IAAI,CAAC,uBAAuB,CAAC;gBACjC;QACJ;IACJ;IACA,4BAA4B;IAC5B,oEAAoE;IACpE,wBAAwB,EAAE,EAAE;QACxB,IAAI,aAAa,KAAK;YAClB,IAAI,CAAC,mBAAmB,CAAC,OAAO,YAAY,CAAC,aAAa;YAC1D,IAAI,CAAC,KAAK,GAAG,MAAM,YAAY;QACnC,OAEI,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBAEd;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,IAAI,CAAC,mBAAmB,CAAC,mJAAA,CAAA,wBAAqB;oBAC9C,IAAI,CAAC,KAAK,GAAG,MAAM,YAAY;oBAC/B;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,kBAAkB;oBAChC,IAAI,CAAC,mBAAmB,CAAC;oBACzB,MAAM,QAAQ,IAAI,CAAC,YAAY;oBAC/B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,IAAI,CAAC,mBAAmB,CAAC;oBACzB,MAAM,QAAQ,IAAI,CAAC,YAAY;oBAC/B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,mBAAmB,CAAC,OAAO,aAAa,CAAC;oBAC9C,IAAI,CAAC,KAAK,GAAG,MAAM,YAAY;gBACnC;QACJ;IACR;IACA,qBAAqB;IACrB,oEAAoE;IACpE,kBAAkB,EAAE,EAAE;QAClB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,kBAAkB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,MAAM,IAAI,IAAI,mJAAA,CAAA,wBAAqB;oBACnC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,MAAM,IAAI,IAAI,OAAO,aAAa,CAAC,aAAa,MAAM,aAAa,MAAM;gBAC7E;QACJ;IACJ;IACA,2BAA2B;IAC3B,oEAAoE;IACpE,uBAAuB,EAAE,EAAE;QACvB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBAEd;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,IAAI,CAAC,uBAAuB,CAAC,mJAAA,CAAA,YAAE,CAAC,MAAM,EAAE,QAAQ;wBAChD,IAAI,CAAC,KAAK,GAAG,MAAM,4BAA4B;oBACnD,OACK,IAAI,IAAI,CAAC,uBAAuB,CAAC,mJAAA,CAAA,YAAE,CAAC,MAAM,EAAE,QAAQ;wBACrD,IAAI,CAAC,KAAK,GAAG,MAAM,4BAA4B;oBACnD,OAGK,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI;wBACjC,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,wCAAwC;wBACtD,MAAM,WAAW,GAAG;wBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;wBAChC,IAAI,CAAC,kBAAkB,CAAC;oBAC5B;gBACJ;QACJ;IACJ;IACA,qCAAqC;IACrC,oEAAoE;IACpE,gCAAgC,EAAE,EAAE;QAChC,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,gCAAgC;oBACnD;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,0CAA0C;oBACxD,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;gBAAE;oBACf,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,0CAA0C;oBACxD,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,yCAAyC;oBACvD,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;oBAChC,IAAI,CAAC,kBAAkB,CAAC;gBAC5B;QACJ;IACJ;IACA,yCAAyC;IACzC,oEAAoE;IACpE,oCAAoC,EAAE,EAAE;QACpC,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBAEd;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;gBAAE;oBACf,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,yCAAyC;oBACvD,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;oBAChC,IAAI,CAAC,kBAAkB,CAAC;gBAC5B;QACJ;IACJ;IACA,kDAAkD;IAClD,oEAAoE;IACpE,0CAA0C,EAAE,EAAE;QAC1C,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,+BAA+B;oBAClD;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,MAAM,QAAQ,IAAI,mJAAA,CAAA,wBAAqB;oBACvC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,6BAA6B;oBAC3C,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,MAAM,QAAQ,IAAI,OAAO,aAAa,CAAC;gBAC3C;QACJ;IACJ;IACA,kDAAkD;IAClD,oEAAoE;IACpE,0CAA0C,EAAE,EAAE;QAC1C,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;gBAAE;oBACf,IAAI,CAAC,KAAK,GAAG,MAAM,+BAA+B;oBAClD;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,MAAM,QAAQ,IAAI,mJAAA,CAAA,wBAAqB;oBACvC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,6BAA6B;oBAC3C,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,MAAM,QAAQ,IAAI,OAAO,aAAa,CAAC;gBAC3C;QACJ;IACJ;IACA,wCAAwC;IACxC,oEAAoE;IACpE,mCAAmC,EAAE,EAAE;QACnC,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,6CAA6C;oBAChE;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,yDAAyD;oBACvE,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;gBAAE;oBACf,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,yDAAyD;oBACvE,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,yCAAyC;oBACvD,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;oBAChC,IAAI,CAAC,kBAAkB,CAAC;gBAC5B;QACJ;IACJ;IACA,sDAAsD;IACtD,oEAAoE;IACpE,+CAA+C,EAAE,EAAE;QAC/C,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBAEd;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;gBAAE;oBACf,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,yCAAyC;oBACvD,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;oBAChC,IAAI,CAAC,kBAAkB,CAAC;gBAC5B;QACJ;IACJ;IACA,qCAAqC;IACrC,oEAAoE;IACpE,gCAAgC,EAAE,EAAE;QAChC,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,KAAK,GAAG,MAAM,gCAAgC;oBACnD;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,0CAA0C;oBACxD,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;gBAAE;oBACf,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,0CAA0C;oBACxD,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,yCAAyC;oBACvD,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;oBAChC,IAAI,CAAC,kBAAkB,CAAC;gBAC5B;QACJ;IACJ;IACA,yCAAyC;IACzC,oEAAoE;IACpE,oCAAoC,EAAE,EAAE;QACpC,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBAEd;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;gBAAE;oBACf,MAAM,QAAQ,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG,MAAM,uCAAuC;oBAC1D;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;oBAC5C,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,yCAAyC;oBACvD,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;oBAChC,IAAI,CAAC,kBAAkB,CAAC;gBAC5B;QACJ;IACJ;IACA,kDAAkD;IAClD,oEAAoE;IACpE,0CAA0C,EAAE,EAAE;QAC1C,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,cAAc;gBAAE;oBACnB,IAAI,CAAC,KAAK,GAAG,MAAM,+BAA+B;oBAClD;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,MAAM,QAAQ,IAAI,mJAAA,CAAA,wBAAqB;oBACvC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,6BAA6B;oBAC3C,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,MAAM,QAAQ,IAAI,OAAO,aAAa,CAAC;gBAC3C;QACJ;IACJ;IACA,kDAAkD;IAClD,oEAAoE;IACpE,0CAA0C,EAAE,EAAE;QAC1C,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;gBAAE;oBACf,IAAI,CAAC,KAAK,GAAG,MAAM,+BAA+B;oBAClD;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC,MAAM,QAAQ,IAAI,mJAAA,CAAA,wBAAqB;oBACvC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,6BAA6B;oBAC3C,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,MAAM,QAAQ,IAAI,OAAO,aAAa,CAAC;gBAC3C;QACJ;IACJ;IACA,wCAAwC;IACxC,oEAAoE;IACpE,mCAAmC,EAAE,EAAE;QACnC,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,KAAK;YACZ,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;YAChB,KAAK,mJAAA,CAAA,cAAC,CAAC,UAAU;YACjB,KAAK,mJAAA,CAAA,cAAC,CAAC,SAAS;gBAAE;oBAEd;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,YAAY;oBAC1B,MAAM,WAAW,GAAG;oBACpB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,+CAA+C;oBAC7D,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;oBAChC,IAAI,CAAC,kBAAkB,CAAC;gBAC5B;QACJ;IACJ;IACA,sBAAsB;IACtB,oEAAoE;IACpE,mBAAmB,EAAE,EAAE;QACnB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,IAAI;gBAAE;oBACT,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,uBAAuB;oBACrC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,kBAAkB,CAAC;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;QAEJ;IACJ;IACA,sBAAsB;IACtB,oEAAoE;IACpE,mBAAmB,EAAE,EAAE;QACnB,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,oBAAoB;gBAAE;oBACzB,IAAI,CAAC,KAAK,GAAG,MAAM,qBAAqB;oBACxC;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,GAAG;gBAAE;oBACR,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,UAAU;oBACxB,IAAI,CAAC,aAAa;oBAClB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,cAAc,CAAC;gBACxB;QACJ;IACJ;IACA,8BAA8B;IAC9B,oEAAoE;IACpE,0BAA0B,EAAE,EAAE;QAC1B,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,oBAAoB,EAAE;YAC/B,IAAI,CAAC,KAAK,GAAG,MAAM,iBAAiB;QACxC,OACK;YACD,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;YAChC,IAAI,CAAC,kBAAkB,CAAC;QAC5B;IACJ;IACA,0BAA0B;IAC1B,oEAAoE;IACpE,sBAAsB,EAAE,EAAE;QACtB,OAAQ;YACJ,KAAK,mJAAA,CAAA,cAAC,CAAC,iBAAiB;gBAAE;oBACtB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;oBACvB;gBACJ;YACA,KAAK,mJAAA,CAAA,cAAC,CAAC,oBAAoB;gBAAE;oBACzB,IAAI,CAAC,UAAU,CAAC;oBAChB;gBACJ;YACA;gBAAS;oBACL,IAAI,CAAC,UAAU,CAAC;oBAChB,IAAI,CAAC,KAAK,GAAG,MAAM,aAAa;oBAChC,IAAI,CAAC,kBAAkB,CAAC;gBAC5B;QACJ;IACJ;IACA,4BAA4B;IAC5B,oEAAoE;IACpE,2BAA2B;QACvB,IAAI,SAAS,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG;QACnF,IAAI,SAAS,GAAG;YACZ,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;gBACpC,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG;YACnC,OACK;gBACD,mCAAmC;gBACnC,IAAI,CAAC,MAAM,GAAG;gBACd,kCAAkC;gBAClC,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG;gBACxD,IAAI,CAAC,qBAAqB,GAAG;gBAC7B,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG;gBAClC;YACJ;QACJ;QACA,IAAI,WAAW,GAAG;YACd,6DAA6D;YAC7D,yBAAyB;YACzB,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc;YAC3C,IAAI,CAAC,2CAA2C,CAAC,mJAAA,CAAA,cAAC,CAAC,SAAS;YAC5D,IAAI,CAAC,KAAK,GACN,CAAC,IAAI,CAAC,gCAAgC,MAAM,oBAAoB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MACjF,MAAM,mBAAmB,GACzB,IAAI,CAAC,WAAW;QAC9B,OACK;YACD,gEAAgE;YAChE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW;QACjC;IACJ;IACA,2BAA2B;IAC3B,oEAAoE;IACpE,yBAAyB,EAAE,EAAE;QACzB,IAAI,oBAAoB,KAAK;YACzB,IAAI,CAAC,2CAA2C,CAAC;QACrD,OACK;YACD,IAAI,OAAO,mJAAA,CAAA,cAAC,CAAC,SAAS,EAAE;gBACpB,IAAI,CAAC,IAAI,CAAC,0JAAA,CAAA,MAAG,CAAC,8BAA8B;YAChD;YACA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW;YAC7B,IAAI,CAAC,UAAU,CAAC;QACpB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/parser/open-element-stack.js"], "sourcesContent": ["import { TAG_ID as $, NS, NUMBERED_HEADERS } from '../common/html.js';\n//Element utils\nconst IMPLICIT_END_TAG_REQUIRED = new Set([$.DD, $.DT, $.LI, $.OPTGROUP, $.OPTION, $.P, $.RB, $.RP, $.RT, $.RTC]);\nconst IMPLICIT_END_TAG_REQUIRED_THOROUGHLY = new Set([\n    ...IMPLICIT_END_TAG_REQUIRED,\n    $.CAPTION,\n    $.COLGROUP,\n    $.TBODY,\n    $.TD,\n    $.TFOOT,\n    $.TH,\n    $.THEAD,\n    $.TR,\n]);\nconst SCOPING_ELEMENTS_HTML = new Set([\n    $.APPLET,\n    $.CAPTION,\n    $.HTML,\n    $.MARQUEE,\n    $.OBJECT,\n    $.TABLE,\n    $.TD,\n    $.TEMPLATE,\n    $.TH,\n]);\nconst SCOPING_ELEMENTS_HTML_LIST = new Set([...SCOPING_ELEMENTS_HTML, $.OL, $.UL]);\nconst SCOPING_ELEMENTS_HTML_BUTTON = new Set([...SCOPING_ELEMENTS_HTML, $.BUTTON]);\nconst SCOPING_ELEMENTS_MATHML = new Set([$.ANNOTATION_XML, $.MI, $.MN, $.MO, $.MS, $.MTEXT]);\nconst SCOPING_ELEMENTS_SVG = new Set([$.DESC, $.FOREIGN_OBJECT, $.TITLE]);\nconst TABLE_ROW_CONTEXT = new Set([$.TR, $.TEMPLATE, $.HTML]);\nconst TABLE_BODY_CONTEXT = new Set([$.TBODY, $.TFOOT, $.THEAD, $.TEMPLATE, $.HTML]);\nconst TABLE_CONTEXT = new Set([$.TABLE, $.TEMPLATE, $.HTML]);\nconst TABLE_CELLS = new Set([$.TD, $.TH]);\n//Stack of open elements\nexport class OpenElementStack {\n    get currentTmplContentOrNode() {\n        return this._isInTemplate() ? this.treeAdapter.getTemplateContent(this.current) : this.current;\n    }\n    constructor(document, treeAdapter, handler) {\n        this.treeAdapter = treeAdapter;\n        this.handler = handler;\n        this.items = [];\n        this.tagIDs = [];\n        this.stackTop = -1;\n        this.tmplCount = 0;\n        this.currentTagId = $.UNKNOWN;\n        this.current = document;\n    }\n    //Index of element\n    _indexOf(element) {\n        return this.items.lastIndexOf(element, this.stackTop);\n    }\n    //Update current element\n    _isInTemplate() {\n        return this.currentTagId === $.TEMPLATE && this.treeAdapter.getNamespaceURI(this.current) === NS.HTML;\n    }\n    _updateCurrentElement() {\n        this.current = this.items[this.stackTop];\n        this.currentTagId = this.tagIDs[this.stackTop];\n    }\n    //Mutations\n    push(element, tagID) {\n        this.stackTop++;\n        this.items[this.stackTop] = element;\n        this.current = element;\n        this.tagIDs[this.stackTop] = tagID;\n        this.currentTagId = tagID;\n        if (this._isInTemplate()) {\n            this.tmplCount++;\n        }\n        this.handler.onItemPush(element, tagID, true);\n    }\n    pop() {\n        const popped = this.current;\n        if (this.tmplCount > 0 && this._isInTemplate()) {\n            this.tmplCount--;\n        }\n        this.stackTop--;\n        this._updateCurrentElement();\n        this.handler.onItemPop(popped, true);\n    }\n    replace(oldElement, newElement) {\n        const idx = this._indexOf(oldElement);\n        this.items[idx] = newElement;\n        if (idx === this.stackTop) {\n            this.current = newElement;\n        }\n    }\n    insertAfter(referenceElement, newElement, newElementID) {\n        const insertionIdx = this._indexOf(referenceElement) + 1;\n        this.items.splice(insertionIdx, 0, newElement);\n        this.tagIDs.splice(insertionIdx, 0, newElementID);\n        this.stackTop++;\n        if (insertionIdx === this.stackTop) {\n            this._updateCurrentElement();\n        }\n        if (this.current && this.currentTagId !== undefined) {\n            this.handler.onItemPush(this.current, this.currentTagId, insertionIdx === this.stackTop);\n        }\n    }\n    popUntilTagNamePopped(tagName) {\n        let targetIdx = this.stackTop + 1;\n        do {\n            targetIdx = this.tagIDs.lastIndexOf(tagName, targetIdx - 1);\n        } while (targetIdx > 0 && this.treeAdapter.getNamespaceURI(this.items[targetIdx]) !== NS.HTML);\n        this.shortenToLength(Math.max(targetIdx, 0));\n    }\n    shortenToLength(idx) {\n        while (this.stackTop >= idx) {\n            const popped = this.current;\n            if (this.tmplCount > 0 && this._isInTemplate()) {\n                this.tmplCount -= 1;\n            }\n            this.stackTop--;\n            this._updateCurrentElement();\n            this.handler.onItemPop(popped, this.stackTop < idx);\n        }\n    }\n    popUntilElementPopped(element) {\n        const idx = this._indexOf(element);\n        this.shortenToLength(Math.max(idx, 0));\n    }\n    popUntilPopped(tagNames, targetNS) {\n        const idx = this._indexOfTagNames(tagNames, targetNS);\n        this.shortenToLength(Math.max(idx, 0));\n    }\n    popUntilNumberedHeaderPopped() {\n        this.popUntilPopped(NUMBERED_HEADERS, NS.HTML);\n    }\n    popUntilTableCellPopped() {\n        this.popUntilPopped(TABLE_CELLS, NS.HTML);\n    }\n    popAllUpToHtmlElement() {\n        //NOTE: here we assume that the root <html> element is always first in the open element stack, so\n        //we perform this fast stack clean up.\n        this.tmplCount = 0;\n        this.shortenToLength(1);\n    }\n    _indexOfTagNames(tagNames, namespace) {\n        for (let i = this.stackTop; i >= 0; i--) {\n            if (tagNames.has(this.tagIDs[i]) && this.treeAdapter.getNamespaceURI(this.items[i]) === namespace) {\n                return i;\n            }\n        }\n        return -1;\n    }\n    clearBackTo(tagNames, targetNS) {\n        const idx = this._indexOfTagNames(tagNames, targetNS);\n        this.shortenToLength(idx + 1);\n    }\n    clearBackToTableContext() {\n        this.clearBackTo(TABLE_CONTEXT, NS.HTML);\n    }\n    clearBackToTableBodyContext() {\n        this.clearBackTo(TABLE_BODY_CONTEXT, NS.HTML);\n    }\n    clearBackToTableRowContext() {\n        this.clearBackTo(TABLE_ROW_CONTEXT, NS.HTML);\n    }\n    remove(element) {\n        const idx = this._indexOf(element);\n        if (idx >= 0) {\n            if (idx === this.stackTop) {\n                this.pop();\n            }\n            else {\n                this.items.splice(idx, 1);\n                this.tagIDs.splice(idx, 1);\n                this.stackTop--;\n                this._updateCurrentElement();\n                this.handler.onItemPop(element, false);\n            }\n        }\n    }\n    //Search\n    tryPeekProperlyNestedBodyElement() {\n        //Properly nested <body> element (should be second element in stack).\n        return this.stackTop >= 1 && this.tagIDs[1] === $.BODY ? this.items[1] : null;\n    }\n    contains(element) {\n        return this._indexOf(element) > -1;\n    }\n    getCommonAncestor(element) {\n        const elementIdx = this._indexOf(element) - 1;\n        return elementIdx >= 0 ? this.items[elementIdx] : null;\n    }\n    isRootHtmlElementCurrent() {\n        return this.stackTop === 0 && this.tagIDs[0] === $.HTML;\n    }\n    //Element in scope\n    hasInDynamicScope(tagName, htmlScope) {\n        for (let i = this.stackTop; i >= 0; i--) {\n            const tn = this.tagIDs[i];\n            switch (this.treeAdapter.getNamespaceURI(this.items[i])) {\n                case NS.HTML: {\n                    if (tn === tagName)\n                        return true;\n                    if (htmlScope.has(tn))\n                        return false;\n                    break;\n                }\n                case NS.SVG: {\n                    if (SCOPING_ELEMENTS_SVG.has(tn))\n                        return false;\n                    break;\n                }\n                case NS.MATHML: {\n                    if (SCOPING_ELEMENTS_MATHML.has(tn))\n                        return false;\n                    break;\n                }\n            }\n        }\n        return true;\n    }\n    hasInScope(tagName) {\n        return this.hasInDynamicScope(tagName, SCOPING_ELEMENTS_HTML);\n    }\n    hasInListItemScope(tagName) {\n        return this.hasInDynamicScope(tagName, SCOPING_ELEMENTS_HTML_LIST);\n    }\n    hasInButtonScope(tagName) {\n        return this.hasInDynamicScope(tagName, SCOPING_ELEMENTS_HTML_BUTTON);\n    }\n    hasNumberedHeaderInScope() {\n        for (let i = this.stackTop; i >= 0; i--) {\n            const tn = this.tagIDs[i];\n            switch (this.treeAdapter.getNamespaceURI(this.items[i])) {\n                case NS.HTML: {\n                    if (NUMBERED_HEADERS.has(tn))\n                        return true;\n                    if (SCOPING_ELEMENTS_HTML.has(tn))\n                        return false;\n                    break;\n                }\n                case NS.SVG: {\n                    if (SCOPING_ELEMENTS_SVG.has(tn))\n                        return false;\n                    break;\n                }\n                case NS.MATHML: {\n                    if (SCOPING_ELEMENTS_MATHML.has(tn))\n                        return false;\n                    break;\n                }\n            }\n        }\n        return true;\n    }\n    hasInTableScope(tagName) {\n        for (let i = this.stackTop; i >= 0; i--) {\n            if (this.treeAdapter.getNamespaceURI(this.items[i]) !== NS.HTML) {\n                continue;\n            }\n            switch (this.tagIDs[i]) {\n                case tagName: {\n                    return true;\n                }\n                case $.TABLE:\n                case $.HTML: {\n                    return false;\n                }\n            }\n        }\n        return true;\n    }\n    hasTableBodyContextInTableScope() {\n        for (let i = this.stackTop; i >= 0; i--) {\n            if (this.treeAdapter.getNamespaceURI(this.items[i]) !== NS.HTML) {\n                continue;\n            }\n            switch (this.tagIDs[i]) {\n                case $.TBODY:\n                case $.THEAD:\n                case $.TFOOT: {\n                    return true;\n                }\n                case $.TABLE:\n                case $.HTML: {\n                    return false;\n                }\n            }\n        }\n        return true;\n    }\n    hasInSelectScope(tagName) {\n        for (let i = this.stackTop; i >= 0; i--) {\n            if (this.treeAdapter.getNamespaceURI(this.items[i]) !== NS.HTML) {\n                continue;\n            }\n            switch (this.tagIDs[i]) {\n                case tagName: {\n                    return true;\n                }\n                case $.OPTION:\n                case $.OPTGROUP: {\n                    break;\n                }\n                default: {\n                    return false;\n                }\n            }\n        }\n        return true;\n    }\n    //Implied end tags\n    generateImpliedEndTags() {\n        while (this.currentTagId !== undefined && IMPLICIT_END_TAG_REQUIRED.has(this.currentTagId)) {\n            this.pop();\n        }\n    }\n    generateImpliedEndTagsThoroughly() {\n        while (this.currentTagId !== undefined && IMPLICIT_END_TAG_REQUIRED_THOROUGHLY.has(this.currentTagId)) {\n            this.pop();\n        }\n    }\n    generateImpliedEndTagsWithExclusion(exclusionId) {\n        while (this.currentTagId !== undefined &&\n            this.currentTagId !== exclusionId &&\n            IMPLICIT_END_TAG_REQUIRED_THOROUGHLY.has(this.currentTagId)) {\n            this.pop();\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,eAAe;AACf,MAAM,4BAA4B,IAAI,IAAI;IAAC,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,QAAQ;IAAE,gJAAA,CAAA,SAAC,CAAC,MAAM;IAAE,gJAAA,CAAA,SAAC,CAAC,CAAC;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,GAAG;CAAC;AAChH,MAAM,uCAAuC,IAAI,IAAI;OAC9C;IACH,gJAAA,CAAA,SAAC,CAAC,OAAO;IACT,gJAAA,CAAA,SAAC,CAAC,QAAQ;IACV,gJAAA,CAAA,SAAC,CAAC,KAAK;IACP,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,KAAK;IACP,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,KAAK;IACP,gJAAA,CAAA,SAAC,CAAC,EAAE;CACP;AACD,MAAM,wBAAwB,IAAI,IAAI;IAClC,gJAAA,CAAA,SAAC,CAAC,MAAM;IACR,gJAAA,CAAA,SAAC,CAAC,OAAO;IACT,gJAAA,CAAA,SAAC,CAAC,IAAI;IACN,gJAAA,CAAA,SAAC,CAAC,OAAO;IACT,gJAAA,CAAA,SAAC,CAAC,MAAM;IACR,gJAAA,CAAA,SAAC,CAAC,KAAK;IACP,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,QAAQ;IACV,gJAAA,CAAA,SAAC,CAAC,EAAE;CACP;AACD,MAAM,6BAA6B,IAAI,IAAI;OAAI;IAAuB,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;CAAC;AACjF,MAAM,+BAA+B,IAAI,IAAI;OAAI;IAAuB,gJAAA,CAAA,SAAC,CAAC,MAAM;CAAC;AACjF,MAAM,0BAA0B,IAAI,IAAI;IAAC,gJAAA,CAAA,SAAC,CAAC,cAAc;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,KAAK;CAAC;AAC3F,MAAM,uBAAuB,IAAI,IAAI;IAAC,gJAAA,CAAA,SAAC,CAAC,IAAI;IAAE,gJAAA,CAAA,SAAC,CAAC,cAAc;IAAE,gJAAA,CAAA,SAAC,CAAC,KAAK;CAAC;AACxE,MAAM,oBAAoB,IAAI,IAAI;IAAC,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,QAAQ;IAAE,gJAAA,CAAA,SAAC,CAAC,IAAI;CAAC;AAC5D,MAAM,qBAAqB,IAAI,IAAI;IAAC,gJAAA,CAAA,SAAC,CAAC,KAAK;IAAE,gJAAA,CAAA,SAAC,CAAC,KAAK;IAAE,gJAAA,CAAA,SAAC,CAAC,KAAK;IAAE,gJAAA,CAAA,SAAC,CAAC,QAAQ;IAAE,gJAAA,CAAA,SAAC,CAAC,IAAI;CAAC;AAClF,MAAM,gBAAgB,IAAI,IAAI;IAAC,gJAAA,CAAA,SAAC,CAAC,KAAK;IAAE,gJAAA,CAAA,SAAC,CAAC,QAAQ;IAAE,gJAAA,CAAA,SAAC,CAAC,IAAI;CAAC;AAC3D,MAAM,cAAc,IAAI,IAAI;IAAC,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;CAAC;AAEjC,MAAM;IACT,IAAI,2BAA2B;QAC3B,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO;IAClG;IACA,YAAY,QAAQ,EAAE,WAAW,EAAE,OAAO,CAAE;QACxC,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,YAAY,GAAG,gJAAA,CAAA,SAAC,CAAC,OAAO;QAC7B,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,kBAAkB;IAClB,SAAS,OAAO,EAAE;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,IAAI,CAAC,QAAQ;IACxD;IACA,wBAAwB;IACxB,gBAAgB;QACZ,OAAO,IAAI,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,MAAM,gJAAA,CAAA,KAAE,CAAC,IAAI;IACzG;IACA,wBAAwB;QACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;IAClD;IACA,WAAW;IACX,KAAK,OAAO,EAAE,KAAK,EAAE;QACjB,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG;QAC5B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG;QAC7B,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,IAAI,CAAC,aAAa,IAAI;YACtB,IAAI,CAAC,SAAS;QAClB;QACA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,OAAO;IAC5C;IACA,MAAM;QACF,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,IAAI,CAAC,aAAa,IAAI;YAC5C,IAAI,CAAC,SAAS;QAClB;QACA,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ;IACnC;IACA,QAAQ,UAAU,EAAE,UAAU,EAAE;QAC5B,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC;QAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;QAClB,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACvB,IAAI,CAAC,OAAO,GAAG;QACnB;IACJ;IACA,YAAY,gBAAgB,EAAE,UAAU,EAAE,YAAY,EAAE;QACpD,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,oBAAoB;QACvD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,GAAG;QACnC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,GAAG;QACpC,IAAI,CAAC,QAAQ;QACb,IAAI,iBAAiB,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,CAAC,qBAAqB;QAC9B;QACA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,KAAK,WAAW;YACjD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,iBAAiB,IAAI,CAAC,QAAQ;QAC3F;IACJ;IACA,sBAAsB,OAAO,EAAE;QAC3B,IAAI,YAAY,IAAI,CAAC,QAAQ,GAAG;QAChC,GAAG;YACC,YAAY,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,YAAY;QAC7D,QAAS,YAAY,KAAK,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,MAAM,gJAAA,CAAA,KAAE,CAAC,IAAI,CAAE;QAC/F,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,CAAC,WAAW;IAC7C;IACA,gBAAgB,GAAG,EAAE;QACjB,MAAO,IAAI,CAAC,QAAQ,IAAI,IAAK;YACzB,MAAM,SAAS,IAAI,CAAC,OAAO;YAC3B,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,IAAI,CAAC,aAAa,IAAI;gBAC5C,IAAI,CAAC,SAAS,IAAI;YACtB;YACA,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,QAAQ,GAAG;QACnD;IACJ;IACA,sBAAsB,OAAO,EAAE;QAC3B,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC;QAC1B,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,CAAC,KAAK;IACvC;IACA,eAAe,QAAQ,EAAE,QAAQ,EAAE;QAC/B,MAAM,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU;QAC5C,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,CAAC,KAAK;IACvC;IACA,+BAA+B;QAC3B,IAAI,CAAC,cAAc,CAAC,gJAAA,CAAA,mBAAgB,EAAE,gJAAA,CAAA,KAAE,CAAC,IAAI;IACjD;IACA,0BAA0B;QACtB,IAAI,CAAC,cAAc,CAAC,aAAa,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC5C;IACA,wBAAwB;QACpB,iGAAiG;QACjG,sCAAsC;QACtC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,eAAe,CAAC;IACzB;IACA,iBAAiB,QAAQ,EAAE,SAAS,EAAE;QAClC,IAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAK;YACrC,IAAI,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,WAAW;gBAC/F,OAAO;YACX;QACJ;QACA,OAAO,CAAC;IACZ;IACA,YAAY,QAAQ,EAAE,QAAQ,EAAE;QAC5B,MAAM,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU;QAC5C,IAAI,CAAC,eAAe,CAAC,MAAM;IAC/B;IACA,0BAA0B;QACtB,IAAI,CAAC,WAAW,CAAC,eAAe,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC3C;IACA,8BAA8B;QAC1B,IAAI,CAAC,WAAW,CAAC,oBAAoB,gJAAA,CAAA,KAAE,CAAC,IAAI;IAChD;IACA,6BAA6B;QACzB,IAAI,CAAC,WAAW,CAAC,mBAAmB,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/C;IACA,OAAO,OAAO,EAAE;QACZ,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC;QAC1B,IAAI,OAAO,GAAG;YACV,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBACvB,IAAI,CAAC,GAAG;YACZ,OACK;gBACD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK;gBACvB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK;gBACxB,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,qBAAqB;gBAC1B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS;YACpC;QACJ;IACJ;IACA,QAAQ;IACR,mCAAmC;QAC/B,qEAAqE;QACrE,OAAO,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG;IAC7E;IACA,SAAS,OAAO,EAAE;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;IACrC;IACA,kBAAkB,OAAO,EAAE;QACvB,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC,WAAW;QAC5C,OAAO,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG;IACtD;IACA,2BAA2B;QACvB,OAAO,IAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;IAC3D;IACA,kBAAkB;IAClB,kBAAkB,OAAO,EAAE,SAAS,EAAE;QAClC,IAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAK;YACrC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;YACzB,OAAQ,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAClD,KAAK,gJAAA,CAAA,KAAE,CAAC,IAAI;oBAAE;wBACV,IAAI,OAAO,SACP,OAAO;wBACX,IAAI,UAAU,GAAG,CAAC,KACd,OAAO;wBACX;oBACJ;gBACA,KAAK,gJAAA,CAAA,KAAE,CAAC,GAAG;oBAAE;wBACT,IAAI,qBAAqB,GAAG,CAAC,KACzB,OAAO;wBACX;oBACJ;gBACA,KAAK,gJAAA,CAAA,KAAE,CAAC,MAAM;oBAAE;wBACZ,IAAI,wBAAwB,GAAG,CAAC,KAC5B,OAAO;wBACX;oBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,WAAW,OAAO,EAAE;QAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS;IAC3C;IACA,mBAAmB,OAAO,EAAE;QACxB,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS;IAC3C;IACA,iBAAiB,OAAO,EAAE;QACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS;IAC3C;IACA,2BAA2B;QACvB,IAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAK;YACrC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;YACzB,OAAQ,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAClD,KAAK,gJAAA,CAAA,KAAE,CAAC,IAAI;oBAAE;wBACV,IAAI,gJAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,KACrB,OAAO;wBACX,IAAI,sBAAsB,GAAG,CAAC,KAC1B,OAAO;wBACX;oBACJ;gBACA,KAAK,gJAAA,CAAA,KAAE,CAAC,GAAG;oBAAE;wBACT,IAAI,qBAAqB,GAAG,CAAC,KACzB,OAAO;wBACX;oBACJ;gBACA,KAAK,gJAAA,CAAA,KAAE,CAAC,MAAM;oBAAE;wBACZ,IAAI,wBAAwB,GAAG,CAAC,KAC5B,OAAO;wBACX;oBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,gBAAgB,OAAO,EAAE;QACrB,IAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAK;YACrC,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE;gBAC7D;YACJ;YACA,OAAQ,IAAI,CAAC,MAAM,CAAC,EAAE;gBAClB,KAAK;oBAAS;wBACV,OAAO;oBACX;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;gBACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;oBAAE;wBACT,OAAO;oBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,kCAAkC;QAC9B,IAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAK;YACrC,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE;gBAC7D;YACJ;YACA,OAAQ,IAAI,CAAC,MAAM,CAAC,EAAE;gBAClB,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;gBACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;gBACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;oBAAE;wBACV,OAAO;oBACX;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;gBACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;oBAAE;wBACT,OAAO;oBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,iBAAiB,OAAO,EAAE;QACtB,IAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAK;YACrC,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE;gBAC7D;YACJ;YACA,OAAQ,IAAI,CAAC,MAAM,CAAC,EAAE;gBAClB,KAAK;oBAAS;wBACV,OAAO;oBACX;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;gBACb,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;oBAAE;wBACb;oBACJ;gBACA;oBAAS;wBACL,OAAO;oBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,kBAAkB;IAClB,yBAAyB;QACrB,MAAO,IAAI,CAAC,YAAY,KAAK,aAAa,0BAA0B,GAAG,CAAC,IAAI,CAAC,YAAY,EAAG;YACxF,IAAI,CAAC,GAAG;QACZ;IACJ;IACA,mCAAmC;QAC/B,MAAO,IAAI,CAAC,YAAY,KAAK,aAAa,qCAAqC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAG;YACnG,IAAI,CAAC,GAAG;QACZ;IACJ;IACA,oCAAoC,WAAW,EAAE;QAC7C,MAAO,IAAI,CAAC,YAAY,KAAK,aACzB,IAAI,CAAC,YAAY,KAAK,eACtB,qCAAqC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAG;YAC7D,IAAI,CAAC,GAAG;QACZ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/parser/formatting-element-list.js"], "sourcesContent": ["//Const\nconst NOAH_ARK_CAPACITY = 3;\nexport var EntryType;\n(function (EntryType) {\n    EntryType[EntryType[\"Marker\"] = 0] = \"Marker\";\n    EntryType[EntryType[\"Element\"] = 1] = \"Element\";\n})(EntryType || (EntryType = {}));\nconst MARKER = { type: EntryType.Marker };\n//List of formatting elements\nexport class FormattingElementList {\n    constructor(treeAdapter) {\n        this.treeAdapter = treeAdapter;\n        this.entries = [];\n        this.bookmark = null;\n    }\n    //<PERSON> Ark's condition\n    //OPTIMIZATION: at first we try to find possible candidates for exclusion using\n    //lightweight heuristics without thorough attributes check.\n    _getNoahArkConditionCandidates(newElement, neAttrs) {\n        const candidates = [];\n        const neAttrsLength = neAttrs.length;\n        const neTagName = this.treeAdapter.getTagName(newElement);\n        const neNamespaceURI = this.treeAdapter.getNamespaceURI(newElement);\n        for (let i = 0; i < this.entries.length; i++) {\n            const entry = this.entries[i];\n            if (entry.type === EntryType.Marker) {\n                break;\n            }\n            const { element } = entry;\n            if (this.treeAdapter.getTagName(element) === neTagName &&\n                this.treeAdapter.getNamespaceURI(element) === neNamespaceURI) {\n                const elementAttrs = this.treeAdapter.getAttrList(element);\n                if (elementAttrs.length === neAttrsLength) {\n                    candidates.push({ idx: i, attrs: elementAttrs });\n                }\n            }\n        }\n        return candidates;\n    }\n    _ensureNoahArkCondition(newElement) {\n        if (this.entries.length < NOAH_ARK_CAPACITY)\n            return;\n        const neAttrs = this.treeAdapter.getAttrList(newElement);\n        const candidates = this._getNoahArkConditionCandidates(newElement, neAttrs);\n        if (candidates.length < NOAH_ARK_CAPACITY)\n            return;\n        //NOTE: build attrs map for the new element, so we can perform fast lookups\n        const neAttrsMap = new Map(neAttrs.map((neAttr) => [neAttr.name, neAttr.value]));\n        let validCandidates = 0;\n        //NOTE: remove bottommost candidates, until Noah's Ark condition will not be met\n        for (let i = 0; i < candidates.length; i++) {\n            const candidate = candidates[i];\n            // We know that `candidate.attrs.length === neAttrs.length`\n            if (candidate.attrs.every((cAttr) => neAttrsMap.get(cAttr.name) === cAttr.value)) {\n                validCandidates += 1;\n                if (validCandidates >= NOAH_ARK_CAPACITY) {\n                    this.entries.splice(candidate.idx, 1);\n                }\n            }\n        }\n    }\n    //Mutations\n    insertMarker() {\n        this.entries.unshift(MARKER);\n    }\n    pushElement(element, token) {\n        this._ensureNoahArkCondition(element);\n        this.entries.unshift({\n            type: EntryType.Element,\n            element,\n            token,\n        });\n    }\n    insertElementAfterBookmark(element, token) {\n        const bookmarkIdx = this.entries.indexOf(this.bookmark);\n        this.entries.splice(bookmarkIdx, 0, {\n            type: EntryType.Element,\n            element,\n            token,\n        });\n    }\n    removeEntry(entry) {\n        const entryIndex = this.entries.indexOf(entry);\n        if (entryIndex !== -1) {\n            this.entries.splice(entryIndex, 1);\n        }\n    }\n    /**\n     * Clears the list of formatting elements up to the last marker.\n     *\n     * @see https://html.spec.whatwg.org/multipage/parsing.html#clear-the-list-of-active-formatting-elements-up-to-the-last-marker\n     */\n    clearToLastMarker() {\n        const markerIdx = this.entries.indexOf(MARKER);\n        if (markerIdx === -1) {\n            this.entries.length = 0;\n        }\n        else {\n            this.entries.splice(0, markerIdx + 1);\n        }\n    }\n    //Search\n    getElementEntryInScopeWithTagName(tagName) {\n        const entry = this.entries.find((entry) => entry.type === EntryType.Marker || this.treeAdapter.getTagName(entry.element) === tagName);\n        return entry && entry.type === EntryType.Element ? entry : null;\n    }\n    getElementEntry(element) {\n        return this.entries.find((entry) => entry.type === EntryType.Element && entry.element === element);\n    }\n}\n"], "names": [], "mappings": "AAAA,OAAO;;;;;AACP,MAAM,oBAAoB;AACnB,IAAI;AACX,CAAC,SAAU,SAAS;IAChB,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,GAAG;IACrC,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,GAAG;AAC1C,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,MAAM,SAAS;IAAE,MAAM,UAAU,MAAM;AAAC;AAEjC,MAAM;IACT,YAAY,WAAW,CAAE;QACrB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,sBAAsB;IACtB,+EAA+E;IAC/E,2DAA2D;IAC3D,+BAA+B,UAAU,EAAE,OAAO,EAAE;QAChD,MAAM,aAAa,EAAE;QACrB,MAAM,gBAAgB,QAAQ,MAAM;QACpC,MAAM,YAAY,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;QAC9C,MAAM,iBAAiB,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;QACxD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAK;YAC1C,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,EAAE;YAC7B,IAAI,MAAM,IAAI,KAAK,UAAU,MAAM,EAAE;gBACjC;YACJ;YACA,MAAM,EAAE,OAAO,EAAE,GAAG;YACpB,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,aAAa,aACzC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,aAAa,gBAAgB;gBAC9D,MAAM,eAAe,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;gBAClD,IAAI,aAAa,MAAM,KAAK,eAAe;oBACvC,WAAW,IAAI,CAAC;wBAAE,KAAK;wBAAG,OAAO;oBAAa;gBAClD;YACJ;QACJ;QACA,OAAO;IACX;IACA,wBAAwB,UAAU,EAAE;QAChC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,mBACtB;QACJ,MAAM,UAAU,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;QAC7C,MAAM,aAAa,IAAI,CAAC,8BAA8B,CAAC,YAAY;QACnE,IAAI,WAAW,MAAM,GAAG,mBACpB;QACJ,2EAA2E;QAC3E,MAAM,aAAa,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAC,SAAW;gBAAC,OAAO,IAAI;gBAAE,OAAO,KAAK;aAAC;QAC9E,IAAI,kBAAkB;QACtB,gFAAgF;QAChF,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YACxC,MAAM,YAAY,UAAU,CAAC,EAAE;YAC/B,2DAA2D;YAC3D,IAAI,UAAU,KAAK,CAAC,KAAK,CAAC,CAAC,QAAU,WAAW,GAAG,CAAC,MAAM,IAAI,MAAM,MAAM,KAAK,GAAG;gBAC9E,mBAAmB;gBACnB,IAAI,mBAAmB,mBAAmB;oBACtC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE;gBACvC;YACJ;QACJ;IACJ;IACA,WAAW;IACX,eAAe;QACX,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;IACzB;IACA,YAAY,OAAO,EAAE,KAAK,EAAE;QACxB,IAAI,CAAC,uBAAuB,CAAC;QAC7B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;YACjB,MAAM,UAAU,OAAO;YACvB;YACA;QACJ;IACJ;IACA,2BAA2B,OAAO,EAAE,KAAK,EAAE;QACvC,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ;QACtD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,GAAG;YAChC,MAAM,UAAU,OAAO;YACvB;YACA;QACJ;IACJ;IACA,YAAY,KAAK,EAAE;QACf,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACxC,IAAI,eAAe,CAAC,GAAG;YACnB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY;QACpC;IACJ;IACA;;;;KAIC,GACD,oBAAoB;QAChB,MAAM,YAAY,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACvC,IAAI,cAAc,CAAC,GAAG;YAClB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;QAC1B,OACK;YACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,YAAY;QACvC;IACJ;IACA,QAAQ;IACR,kCAAkC,OAAO,EAAE;QACvC,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAU,MAAM,IAAI,KAAK,UAAU,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,OAAO,MAAM;QAC7H,OAAO,SAAS,MAAM,IAAI,KAAK,UAAU,OAAO,GAAG,QAAQ;IAC/D;IACA,gBAAgB,OAAO,EAAE;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAU,MAAM,IAAI,KAAK,UAAU,OAAO,IAAI,MAAM,OAAO,KAAK;IAC9F;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4803, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/tree-adapters/default.js"], "sourcesContent": ["import { DOCUMENT_MODE } from '../common/html.js';\nexport const defaultTreeAdapter = {\n    //Node construction\n    createDocument() {\n        return {\n            nodeName: '#document',\n            mode: DOCUMENT_MODE.NO_QUIRKS,\n            childNodes: [],\n        };\n    },\n    createDocumentFragment() {\n        return {\n            nodeName: '#document-fragment',\n            childNodes: [],\n        };\n    },\n    createElement(tagName, namespaceURI, attrs) {\n        return {\n            nodeName: tagName,\n            tagName,\n            attrs,\n            namespaceURI,\n            childNodes: [],\n            parentNode: null,\n        };\n    },\n    createCommentNode(data) {\n        return {\n            nodeName: '#comment',\n            data,\n            parentNode: null,\n        };\n    },\n    createTextNode(value) {\n        return {\n            nodeName: '#text',\n            value,\n            parentNode: null,\n        };\n    },\n    //Tree mutation\n    appendChild(parentNode, newNode) {\n        parentNode.childNodes.push(newNode);\n        newNode.parentNode = parentNode;\n    },\n    insertBefore(parentNode, newNode, referenceNode) {\n        const insertionIdx = parentNode.childNodes.indexOf(referenceNode);\n        parentNode.childNodes.splice(insertionIdx, 0, newNode);\n        newNode.parentNode = parentNode;\n    },\n    setTemplateContent(templateElement, contentElement) {\n        templateElement.content = contentElement;\n    },\n    getTemplateContent(templateElement) {\n        return templateElement.content;\n    },\n    setDocumentType(document, name, publicId, systemId) {\n        const doctypeNode = document.childNodes.find((node) => node.nodeName === '#documentType');\n        if (doctypeNode) {\n            doctypeNode.name = name;\n            doctypeNode.publicId = publicId;\n            doctypeNode.systemId = systemId;\n        }\n        else {\n            const node = {\n                nodeName: '#documentType',\n                name,\n                publicId,\n                systemId,\n                parentNode: null,\n            };\n            defaultTreeAdapter.appendChild(document, node);\n        }\n    },\n    setDocumentMode(document, mode) {\n        document.mode = mode;\n    },\n    getDocumentMode(document) {\n        return document.mode;\n    },\n    detachNode(node) {\n        if (node.parentNode) {\n            const idx = node.parentNode.childNodes.indexOf(node);\n            node.parentNode.childNodes.splice(idx, 1);\n            node.parentNode = null;\n        }\n    },\n    insertText(parentNode, text) {\n        if (parentNode.childNodes.length > 0) {\n            const prevNode = parentNode.childNodes[parentNode.childNodes.length - 1];\n            if (defaultTreeAdapter.isTextNode(prevNode)) {\n                prevNode.value += text;\n                return;\n            }\n        }\n        defaultTreeAdapter.appendChild(parentNode, defaultTreeAdapter.createTextNode(text));\n    },\n    insertTextBefore(parentNode, text, referenceNode) {\n        const prevNode = parentNode.childNodes[parentNode.childNodes.indexOf(referenceNode) - 1];\n        if (prevNode && defaultTreeAdapter.isTextNode(prevNode)) {\n            prevNode.value += text;\n        }\n        else {\n            defaultTreeAdapter.insertBefore(parentNode, defaultTreeAdapter.createTextNode(text), referenceNode);\n        }\n    },\n    adoptAttributes(recipient, attrs) {\n        const recipientAttrsMap = new Set(recipient.attrs.map((attr) => attr.name));\n        for (let j = 0; j < attrs.length; j++) {\n            if (!recipientAttrsMap.has(attrs[j].name)) {\n                recipient.attrs.push(attrs[j]);\n            }\n        }\n    },\n    //Tree traversing\n    getFirstChild(node) {\n        return node.childNodes[0];\n    },\n    getChildNodes(node) {\n        return node.childNodes;\n    },\n    getParentNode(node) {\n        return node.parentNode;\n    },\n    getAttrList(element) {\n        return element.attrs;\n    },\n    //Node data\n    getTagName(element) {\n        return element.tagName;\n    },\n    getNamespaceURI(element) {\n        return element.namespaceURI;\n    },\n    getTextNodeContent(textNode) {\n        return textNode.value;\n    },\n    getCommentNodeContent(commentNode) {\n        return commentNode.data;\n    },\n    getDocumentTypeNodeName(doctypeNode) {\n        return doctypeNode.name;\n    },\n    getDocumentTypeNodePublicId(doctypeNode) {\n        return doctypeNode.publicId;\n    },\n    getDocumentTypeNodeSystemId(doctypeNode) {\n        return doctypeNode.systemId;\n    },\n    //Node types\n    isTextNode(node) {\n        return node.nodeName === '#text';\n    },\n    isCommentNode(node) {\n        return node.nodeName === '#comment';\n    },\n    isDocumentTypeNode(node) {\n        return node.nodeName === '#documentType';\n    },\n    isElementNode(node) {\n        return Object.prototype.hasOwnProperty.call(node, 'tagName');\n    },\n    // Source code location\n    setNodeSourceCodeLocation(node, location) {\n        node.sourceCodeLocation = location;\n    },\n    getNodeSourceCodeLocation(node) {\n        return node.sourceCodeLocation;\n    },\n    updateNodeSourceCodeLocation(node, endLocation) {\n        node.sourceCodeLocation = { ...node.sourceCodeLocation, ...endLocation };\n    },\n};\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB;IAC9B,mBAAmB;IACnB;QACI,OAAO;YACH,UAAU;YACV,MAAM,gJAAA,CAAA,gBAAa,CAAC,SAAS;YAC7B,YAAY,EAAE;QAClB;IACJ;IACA;QACI,OAAO;YACH,UAAU;YACV,YAAY,EAAE;QAClB;IACJ;IACA,eAAc,OAAO,EAAE,YAAY,EAAE,KAAK;QACtC,OAAO;YACH,UAAU;YACV;YACA;YACA;YACA,YAAY,EAAE;YACd,YAAY;QAChB;IACJ;IACA,mBAAkB,IAAI;QAClB,OAAO;YACH,UAAU;YACV;YACA,YAAY;QAChB;IACJ;IACA,gBAAe,KAAK;QAChB,OAAO;YACH,UAAU;YACV;YACA,YAAY;QAChB;IACJ;IACA,eAAe;IACf,aAAY,UAAU,EAAE,OAAO;QAC3B,WAAW,UAAU,CAAC,IAAI,CAAC;QAC3B,QAAQ,UAAU,GAAG;IACzB;IACA,cAAa,UAAU,EAAE,OAAO,EAAE,aAAa;QAC3C,MAAM,eAAe,WAAW,UAAU,CAAC,OAAO,CAAC;QACnD,WAAW,UAAU,CAAC,MAAM,CAAC,cAAc,GAAG;QAC9C,QAAQ,UAAU,GAAG;IACzB;IACA,oBAAmB,eAAe,EAAE,cAAc;QAC9C,gBAAgB,OAAO,GAAG;IAC9B;IACA,oBAAmB,eAAe;QAC9B,OAAO,gBAAgB,OAAO;IAClC;IACA,iBAAgB,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ;QAC9C,MAAM,cAAc,SAAS,UAAU,CAAC,IAAI,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK;QACzE,IAAI,aAAa;YACb,YAAY,IAAI,GAAG;YACnB,YAAY,QAAQ,GAAG;YACvB,YAAY,QAAQ,GAAG;QAC3B,OACK;YACD,MAAM,OAAO;gBACT,UAAU;gBACV;gBACA;gBACA;gBACA,YAAY;YAChB;YACA,mBAAmB,WAAW,CAAC,UAAU;QAC7C;IACJ;IACA,iBAAgB,QAAQ,EAAE,IAAI;QAC1B,SAAS,IAAI,GAAG;IACpB;IACA,iBAAgB,QAAQ;QACpB,OAAO,SAAS,IAAI;IACxB;IACA,YAAW,IAAI;QACX,IAAI,KAAK,UAAU,EAAE;YACjB,MAAM,MAAM,KAAK,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC;YAC/C,KAAK,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK;YACvC,KAAK,UAAU,GAAG;QACtB;IACJ;IACA,YAAW,UAAU,EAAE,IAAI;QACvB,IAAI,WAAW,UAAU,CAAC,MAAM,GAAG,GAAG;YAClC,MAAM,WAAW,WAAW,UAAU,CAAC,WAAW,UAAU,CAAC,MAAM,GAAG,EAAE;YACxE,IAAI,mBAAmB,UAAU,CAAC,WAAW;gBACzC,SAAS,KAAK,IAAI;gBAClB;YACJ;QACJ;QACA,mBAAmB,WAAW,CAAC,YAAY,mBAAmB,cAAc,CAAC;IACjF;IACA,kBAAiB,UAAU,EAAE,IAAI,EAAE,aAAa;QAC5C,MAAM,WAAW,WAAW,UAAU,CAAC,WAAW,UAAU,CAAC,OAAO,CAAC,iBAAiB,EAAE;QACxF,IAAI,YAAY,mBAAmB,UAAU,CAAC,WAAW;YACrD,SAAS,KAAK,IAAI;QACtB,OACK;YACD,mBAAmB,YAAY,CAAC,YAAY,mBAAmB,cAAc,CAAC,OAAO;QACzF;IACJ;IACA,iBAAgB,SAAS,EAAE,KAAK;QAC5B,MAAM,oBAAoB,IAAI,IAAI,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI;QACzE,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,IAAI,CAAC,kBAAkB,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG;gBACvC,UAAU,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACjC;QACJ;IACJ;IACA,iBAAiB;IACjB,eAAc,IAAI;QACd,OAAO,KAAK,UAAU,CAAC,EAAE;IAC7B;IACA,eAAc,IAAI;QACd,OAAO,KAAK,UAAU;IAC1B;IACA,eAAc,IAAI;QACd,OAAO,KAAK,UAAU;IAC1B;IACA,aAAY,OAAO;QACf,OAAO,QAAQ,KAAK;IACxB;IACA,WAAW;IACX,YAAW,OAAO;QACd,OAAO,QAAQ,OAAO;IAC1B;IACA,iBAAgB,OAAO;QACnB,OAAO,QAAQ,YAAY;IAC/B;IACA,oBAAmB,QAAQ;QACvB,OAAO,SAAS,KAAK;IACzB;IACA,uBAAsB,WAAW;QAC7B,OAAO,YAAY,IAAI;IAC3B;IACA,yBAAwB,WAAW;QAC/B,OAAO,YAAY,IAAI;IAC3B;IACA,6BAA4B,WAAW;QACnC,OAAO,YAAY,QAAQ;IAC/B;IACA,6BAA4B,WAAW;QACnC,OAAO,YAAY,QAAQ;IAC/B;IACA,YAAY;IACZ,YAAW,IAAI;QACX,OAAO,KAAK,QAAQ,KAAK;IAC7B;IACA,eAAc,IAAI;QACd,OAAO,KAAK,QAAQ,KAAK;IAC7B;IACA,oBAAmB,IAAI;QACnB,OAAO,KAAK,QAAQ,KAAK;IAC7B;IACA,eAAc,IAAI;QACd,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM;IACtD;IACA,uBAAuB;IACvB,2BAA0B,IAAI,EAAE,QAAQ;QACpC,KAAK,kBAAkB,GAAG;IAC9B;IACA,2BAA0B,IAAI;QAC1B,OAAO,KAAK,kBAAkB;IAClC;IACA,8BAA6B,IAAI,EAAE,WAAW;QAC1C,KAAK,kBAAkB,GAAG;YAAE,GAAG,KAAK,kBAAkB;YAAE,GAAG,WAAW;QAAC;IAC3E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4987, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/common/doctype.js"], "sourcesContent": ["import { DOCUMENT_MODE } from './html.js';\n//Const\nconst VALID_DOCTYPE_NAME = 'html';\nconst VALID_SYSTEM_ID = 'about:legacy-compat';\nconst QUIRKS_MODE_SYSTEM_ID = 'http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd';\nconst QUIRKS_MODE_PUBLIC_ID_PREFIXES = [\n    '+//silmaril//dtd html pro v0r11 19970101//',\n    '-//as//dtd html 3.0 aswedit + extensions//',\n    '-//advasoft ltd//dtd html 3.0 aswedit + extensions//',\n    '-//ietf//dtd html 2.0 level 1//',\n    '-//ietf//dtd html 2.0 level 2//',\n    '-//ietf//dtd html 2.0 strict level 1//',\n    '-//ietf//dtd html 2.0 strict level 2//',\n    '-//ietf//dtd html 2.0 strict//',\n    '-//ietf//dtd html 2.0//',\n    '-//ietf//dtd html 2.1e//',\n    '-//ietf//dtd html 3.0//',\n    '-//ietf//dtd html 3.2 final//',\n    '-//ietf//dtd html 3.2//',\n    '-//ietf//dtd html 3//',\n    '-//ietf//dtd html level 0//',\n    '-//ietf//dtd html level 1//',\n    '-//ietf//dtd html level 2//',\n    '-//ietf//dtd html level 3//',\n    '-//ietf//dtd html strict level 0//',\n    '-//ietf//dtd html strict level 1//',\n    '-//ietf//dtd html strict level 2//',\n    '-//ietf//dtd html strict level 3//',\n    '-//ietf//dtd html strict//',\n    '-//ietf//dtd html//',\n    '-//metrius//dtd metrius presentational//',\n    '-//microsoft//dtd internet explorer 2.0 html strict//',\n    '-//microsoft//dtd internet explorer 2.0 html//',\n    '-//microsoft//dtd internet explorer 2.0 tables//',\n    '-//microsoft//dtd internet explorer 3.0 html strict//',\n    '-//microsoft//dtd internet explorer 3.0 html//',\n    '-//microsoft//dtd internet explorer 3.0 tables//',\n    '-//netscape comm. corp.//dtd html//',\n    '-//netscape comm. corp.//dtd strict html//',\n    \"-//o'reilly and associates//dtd html 2.0//\",\n    \"-//o'reilly and associates//dtd html extended 1.0//\",\n    \"-//o'reilly and associates//dtd html extended relaxed 1.0//\",\n    '-//sq//dtd html 2.0 hotmetal + extensions//',\n    '-//softquad software//dtd hotmetal pro 6.0::19990601::extensions to html 4.0//',\n    '-//softquad//dtd hotmetal pro 4.0::19971010::extensions to html 4.0//',\n    '-//spyglass//dtd html 2.0 extended//',\n    '-//sun microsystems corp.//dtd hotjava html//',\n    '-//sun microsystems corp.//dtd hotjava strict html//',\n    '-//w3c//dtd html 3 1995-03-24//',\n    '-//w3c//dtd html 3.2 draft//',\n    '-//w3c//dtd html 3.2 final//',\n    '-//w3c//dtd html 3.2//',\n    '-//w3c//dtd html 3.2s draft//',\n    '-//w3c//dtd html 4.0 frameset//',\n    '-//w3c//dtd html 4.0 transitional//',\n    '-//w3c//dtd html experimental 19960712//',\n    '-//w3c//dtd html experimental 970421//',\n    '-//w3c//dtd w3 html//',\n    '-//w3o//dtd w3 html 3.0//',\n    '-//webtechs//dtd mozilla html 2.0//',\n    '-//webtechs//dtd mozilla html//',\n];\nconst QUIRKS_MODE_NO_SYSTEM_ID_PUBLIC_ID_PREFIXES = [\n    ...QUIRKS_MODE_PUBLIC_ID_PREFIXES,\n    '-//w3c//dtd html 4.01 frameset//',\n    '-//w3c//dtd html 4.01 transitional//',\n];\nconst QUIRKS_MODE_PUBLIC_IDS = new Set([\n    '-//w3o//dtd w3 html strict 3.0//en//',\n    '-/w3c/dtd html 4.0 transitional/en',\n    'html',\n]);\nconst LIMITED_QUIRKS_PUBLIC_ID_PREFIXES = ['-//w3c//dtd xhtml 1.0 frameset//', '-//w3c//dtd xhtml 1.0 transitional//'];\nconst LIMITED_QUIRKS_WITH_SYSTEM_ID_PUBLIC_ID_PREFIXES = [\n    ...LIMITED_QUIRKS_PUBLIC_ID_PREFIXES,\n    '-//w3c//dtd html 4.01 frameset//',\n    '-//w3c//dtd html 4.01 transitional//',\n];\n//Utils\nfunction hasPrefix(publicId, prefixes) {\n    return prefixes.some((prefix) => publicId.startsWith(prefix));\n}\n//API\nexport function isConforming(token) {\n    return (token.name === VALID_DOCTYPE_NAME &&\n        token.publicId === null &&\n        (token.systemId === null || token.systemId === VALID_SYSTEM_ID));\n}\nexport function getDocumentMode(token) {\n    if (token.name !== VALID_DOCTYPE_NAME) {\n        return DOCUMENT_MODE.QUIRKS;\n    }\n    const { systemId } = token;\n    if (systemId && systemId.toLowerCase() === QUIRKS_MODE_SYSTEM_ID) {\n        return DOCUMENT_MODE.QUIRKS;\n    }\n    let { publicId } = token;\n    if (publicId !== null) {\n        publicId = publicId.toLowerCase();\n        if (QUIRKS_MODE_PUBLIC_IDS.has(publicId)) {\n            return DOCUMENT_MODE.QUIRKS;\n        }\n        let prefixes = systemId === null ? QUIRKS_MODE_NO_SYSTEM_ID_PUBLIC_ID_PREFIXES : QUIRKS_MODE_PUBLIC_ID_PREFIXES;\n        if (hasPrefix(publicId, prefixes)) {\n            return DOCUMENT_MODE.QUIRKS;\n        }\n        prefixes =\n            systemId === null ? LIMITED_QUIRKS_PUBLIC_ID_PREFIXES : LIMITED_QUIRKS_WITH_SYSTEM_ID_PUBLIC_ID_PREFIXES;\n        if (hasPrefix(publicId, prefixes)) {\n            return DOCUMENT_MODE.LIMITED_QUIRKS;\n        }\n    }\n    return DOCUMENT_MODE.NO_QUIRKS;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACA,OAAO;AACP,MAAM,qBAAqB;AAC3B,MAAM,kBAAkB;AACxB,MAAM,wBAAwB;AAC9B,MAAM,iCAAiC;IACnC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,MAAM,8CAA8C;OAC7C;IACH;IACA;CACH;AACD,MAAM,yBAAyB,IAAI,IAAI;IACnC;IACA;IACA;CACH;AACD,MAAM,oCAAoC;IAAC;IAAoC;CAAuC;AACtH,MAAM,mDAAmD;OAClD;IACH;IACA;CACH;AACD,OAAO;AACP,SAAS,UAAU,QAAQ,EAAE,QAAQ;IACjC,OAAO,SAAS,IAAI,CAAC,CAAC,SAAW,SAAS,UAAU,CAAC;AACzD;AAEO,SAAS,aAAa,KAAK;IAC9B,OAAQ,MAAM,IAAI,KAAK,sBACnB,MAAM,QAAQ,KAAK,QACnB,CAAC,MAAM,QAAQ,KAAK,QAAQ,MAAM,QAAQ,KAAK,eAAe;AACtE;AACO,SAAS,gBAAgB,KAAK;IACjC,IAAI,MAAM,IAAI,KAAK,oBAAoB;QACnC,OAAO,gJAAA,CAAA,gBAAa,CAAC,MAAM;IAC/B;IACA,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,IAAI,YAAY,SAAS,WAAW,OAAO,uBAAuB;QAC9D,OAAO,gJAAA,CAAA,gBAAa,CAAC,MAAM;IAC/B;IACA,IAAI,EAAE,QAAQ,EAAE,GAAG;IACnB,IAAI,aAAa,MAAM;QACnB,WAAW,SAAS,WAAW;QAC/B,IAAI,uBAAuB,GAAG,CAAC,WAAW;YACtC,OAAO,gJAAA,CAAA,gBAAa,CAAC,MAAM;QAC/B;QACA,IAAI,WAAW,aAAa,OAAO,8CAA8C;QACjF,IAAI,UAAU,UAAU,WAAW;YAC/B,OAAO,gJAAA,CAAA,gBAAa,CAAC,MAAM;QAC/B;QACA,WACI,aAAa,OAAO,oCAAoC;QAC5D,IAAI,UAAU,UAAU,WAAW;YAC/B,OAAO,gJAAA,CAAA,gBAAa,CAAC,cAAc;QACvC;IACJ;IACA,OAAO,gJAAA,CAAA,gBAAa,CAAC,SAAS;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/common/foreign-content.js"], "sourcesContent": ["import { TAG_ID as $, NS, ATTRS, getTagID } from './html.js';\n//MIME types\nconst MIME_TYPES = {\n    TEXT_HTML: 'text/html',\n    APPLICATION_XML: 'application/xhtml+xml',\n};\n//Attributes\nconst DEFINITION_URL_ATTR = 'definitionurl';\nconst ADJUSTED_DEFINITION_URL_ATTR = 'definitionURL';\nconst SVG_ATTRS_ADJUSTMENT_MAP = new Map([\n    'attributeName',\n    'attributeType',\n    'baseFrequency',\n    'baseProfile',\n    'calcMode',\n    'clipPathUnits',\n    'diffuseConstant',\n    'edgeMode',\n    'filterUnits',\n    'glyphRef',\n    'gradientTransform',\n    'gradientUnits',\n    'kernelMatrix',\n    'kernelUnitLength',\n    'keyPoints',\n    'keySplines',\n    'keyTimes',\n    'lengthAdjust',\n    'limitingConeAngle',\n    'markerHeight',\n    'markerUnits',\n    'markerWidth',\n    'maskContentUnits',\n    'maskUnits',\n    'numOctaves',\n    'pathLength',\n    'patternContentUnits',\n    'patternTransform',\n    'patternUnits',\n    'pointsAtX',\n    'pointsAtY',\n    'pointsAtZ',\n    'preserveAlpha',\n    'preserveAspectRatio',\n    'primitiveUnits',\n    'refX',\n    'refY',\n    'repeatCount',\n    'repeatDur',\n    'requiredExtensions',\n    'requiredFeatures',\n    'specularConstant',\n    'specularExponent',\n    'spreadMethod',\n    'startOffset',\n    'stdDeviation',\n    'stitchTiles',\n    'surfaceScale',\n    'systemLanguage',\n    'tableValues',\n    'targetX',\n    'targetY',\n    'textLength',\n    'viewBox',\n    'viewTarget',\n    'xChannelSelector',\n    'yChannelSelector',\n    'zoomAndPan',\n].map((attr) => [attr.toLowerCase(), attr]));\nconst XML_ATTRS_ADJUSTMENT_MAP = new Map([\n    ['xlink:actuate', { prefix: 'xlink', name: 'actuate', namespace: NS.XLINK }],\n    ['xlink:arcrole', { prefix: 'xlink', name: 'arcrole', namespace: NS.XLINK }],\n    ['xlink:href', { prefix: 'xlink', name: 'href', namespace: NS.XLINK }],\n    ['xlink:role', { prefix: 'xlink', name: 'role', namespace: NS.XLINK }],\n    ['xlink:show', { prefix: 'xlink', name: 'show', namespace: NS.XLINK }],\n    ['xlink:title', { prefix: 'xlink', name: 'title', namespace: NS.XLINK }],\n    ['xlink:type', { prefix: 'xlink', name: 'type', namespace: NS.XLINK }],\n    ['xml:lang', { prefix: 'xml', name: 'lang', namespace: NS.XML }],\n    ['xml:space', { prefix: 'xml', name: 'space', namespace: NS.XML }],\n    ['xmlns', { prefix: '', name: 'xmlns', namespace: NS.XMLNS }],\n    ['xmlns:xlink', { prefix: 'xmlns', name: 'xlink', namespace: NS.XMLNS }],\n]);\n//SVG tag names adjustment map\nexport const SVG_TAG_NAMES_ADJUSTMENT_MAP = new Map([\n    'altGlyph',\n    'altGlyphDef',\n    'altGlyphItem',\n    'animateColor',\n    'animateMotion',\n    'animateTransform',\n    'clipPath',\n    'feBlend',\n    'feColorMatrix',\n    'feComponentTransfer',\n    'feComposite',\n    'feConvolveMatrix',\n    'feDiffuseLighting',\n    'feDisplacementMap',\n    'feDistantLight',\n    'feFlood',\n    'feFuncA',\n    'feFuncB',\n    'feFuncG',\n    'feFuncR',\n    'feGaussianBlur',\n    'feImage',\n    'feMerge',\n    'feMergeNode',\n    'feMorphology',\n    'feOffset',\n    'fePointLight',\n    'feSpecularLighting',\n    'feSpotLight',\n    'feTile',\n    'feTurbulence',\n    'foreignObject',\n    'glyphRef',\n    'linearGradient',\n    'radialGradient',\n    'textPath',\n].map((tn) => [tn.toLowerCase(), tn]));\n//Tags that causes exit from foreign content\nconst EXITS_FOREIGN_CONTENT = new Set([\n    $.B,\n    $.BIG,\n    $.BLOCKQUOTE,\n    $.BODY,\n    $.BR,\n    $.CENTER,\n    $.CODE,\n    $.DD,\n    $.DIV,\n    $.DL,\n    $.DT,\n    $.EM,\n    $.EMBED,\n    $.H1,\n    $.H2,\n    $.H3,\n    $.H4,\n    $.H5,\n    $.H6,\n    $.HEAD,\n    $.HR,\n    $.I,\n    $.IMG,\n    $.LI,\n    $.LISTING,\n    $.MENU,\n    $.META,\n    $.NOBR,\n    $.OL,\n    $.P,\n    $.PRE,\n    $.RUBY,\n    $.S,\n    $.SMALL,\n    $.SPAN,\n    $.STRONG,\n    $.STRIKE,\n    $.SUB,\n    $.SUP,\n    $.TABLE,\n    $.TT,\n    $.U,\n    $.UL,\n    $.VAR,\n]);\n//Check exit from foreign content\nexport function causesExit(startTagToken) {\n    const tn = startTagToken.tagID;\n    const isFontWithAttrs = tn === $.FONT &&\n        startTagToken.attrs.some(({ name }) => name === ATTRS.COLOR || name === ATTRS.SIZE || name === ATTRS.FACE);\n    return isFontWithAttrs || EXITS_FOREIGN_CONTENT.has(tn);\n}\n//Token adjustments\nexport function adjustTokenMathMLAttrs(token) {\n    for (let i = 0; i < token.attrs.length; i++) {\n        if (token.attrs[i].name === DEFINITION_URL_ATTR) {\n            token.attrs[i].name = ADJUSTED_DEFINITION_URL_ATTR;\n            break;\n        }\n    }\n}\nexport function adjustTokenSVGAttrs(token) {\n    for (let i = 0; i < token.attrs.length; i++) {\n        const adjustedAttrName = SVG_ATTRS_ADJUSTMENT_MAP.get(token.attrs[i].name);\n        if (adjustedAttrName != null) {\n            token.attrs[i].name = adjustedAttrName;\n        }\n    }\n}\nexport function adjustTokenXMLAttrs(token) {\n    for (let i = 0; i < token.attrs.length; i++) {\n        const adjustedAttrEntry = XML_ATTRS_ADJUSTMENT_MAP.get(token.attrs[i].name);\n        if (adjustedAttrEntry) {\n            token.attrs[i].prefix = adjustedAttrEntry.prefix;\n            token.attrs[i].name = adjustedAttrEntry.name;\n            token.attrs[i].namespace = adjustedAttrEntry.namespace;\n        }\n    }\n}\nexport function adjustTokenSVGTagName(token) {\n    const adjustedTagName = SVG_TAG_NAMES_ADJUSTMENT_MAP.get(token.tagName);\n    if (adjustedTagName != null) {\n        token.tagName = adjustedTagName;\n        token.tagID = getTagID(token.tagName);\n    }\n}\n//Integration points\nfunction isMathMLTextIntegrationPoint(tn, ns) {\n    return ns === NS.MATHML && (tn === $.MI || tn === $.MO || tn === $.MN || tn === $.MS || tn === $.MTEXT);\n}\nfunction isHtmlIntegrationPoint(tn, ns, attrs) {\n    if (ns === NS.MATHML && tn === $.ANNOTATION_XML) {\n        for (let i = 0; i < attrs.length; i++) {\n            if (attrs[i].name === ATTRS.ENCODING) {\n                const value = attrs[i].value.toLowerCase();\n                return value === MIME_TYPES.TEXT_HTML || value === MIME_TYPES.APPLICATION_XML;\n            }\n        }\n    }\n    return ns === NS.SVG && (tn === $.FOREIGN_OBJECT || tn === $.DESC || tn === $.TITLE);\n}\nexport function isIntegrationPoint(tn, ns, attrs, foreignNS) {\n    return (((!foreignNS || foreignNS === NS.HTML) && isHtmlIntegrationPoint(tn, ns, attrs)) ||\n        ((!foreignNS || foreignNS === NS.MATHML) && isMathMLTextIntegrationPoint(tn, ns)));\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AACA,YAAY;AACZ,MAAM,aAAa;IACf,WAAW;IACX,iBAAiB;AACrB;AACA,YAAY;AACZ,MAAM,sBAAsB;AAC5B,MAAM,+BAA+B;AACrC,MAAM,2BAA2B,IAAI,IAAI;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH,CAAC,GAAG,CAAC,CAAC,OAAS;QAAC,KAAK,WAAW;QAAI;KAAK;AAC1C,MAAM,2BAA2B,IAAI,IAAI;IACrC;QAAC;QAAiB;YAAE,QAAQ;YAAS,MAAM;YAAW,WAAW,gJAAA,CAAA,KAAE,CAAC,KAAK;QAAC;KAAE;IAC5E;QAAC;QAAiB;YAAE,QAAQ;YAAS,MAAM;YAAW,WAAW,gJAAA,CAAA,KAAE,CAAC,KAAK;QAAC;KAAE;IAC5E;QAAC;QAAc;YAAE,QAAQ;YAAS,MAAM;YAAQ,WAAW,gJAAA,CAAA,KAAE,CAAC,KAAK;QAAC;KAAE;IACtE;QAAC;QAAc;YAAE,QAAQ;YAAS,MAAM;YAAQ,WAAW,gJAAA,CAAA,KAAE,CAAC,KAAK;QAAC;KAAE;IACtE;QAAC;QAAc;YAAE,QAAQ;YAAS,MAAM;YAAQ,WAAW,gJAAA,CAAA,KAAE,CAAC,KAAK;QAAC;KAAE;IACtE;QAAC;QAAe;YAAE,QAAQ;YAAS,MAAM;YAAS,WAAW,gJAAA,CAAA,KAAE,CAAC,KAAK;QAAC;KAAE;IACxE;QAAC;QAAc;YAAE,QAAQ;YAAS,MAAM;YAAQ,WAAW,gJAAA,CAAA,KAAE,CAAC,KAAK;QAAC;KAAE;IACtE;QAAC;QAAY;YAAE,QAAQ;YAAO,MAAM;YAAQ,WAAW,gJAAA,CAAA,KAAE,CAAC,GAAG;QAAC;KAAE;IAChE;QAAC;QAAa;YAAE,QAAQ;YAAO,MAAM;YAAS,WAAW,gJAAA,CAAA,KAAE,CAAC,GAAG;QAAC;KAAE;IAClE;QAAC;QAAS;YAAE,QAAQ;YAAI,MAAM;YAAS,WAAW,gJAAA,CAAA,KAAE,CAAC,KAAK;QAAC;KAAE;IAC7D;QAAC;QAAe;YAAE,QAAQ;YAAS,MAAM;YAAS,WAAW,gJAAA,CAAA,KAAE,CAAC,KAAK;QAAC;KAAE;CAC3E;AAEM,MAAM,+BAA+B,IAAI,IAAI;IAChD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH,CAAC,GAAG,CAAC,CAAC,KAAO;QAAC,GAAG,WAAW;QAAI;KAAG;AACpC,4CAA4C;AAC5C,MAAM,wBAAwB,IAAI,IAAI;IAClC,gJAAA,CAAA,SAAC,CAAC,CAAC;IACH,gJAAA,CAAA,SAAC,CAAC,GAAG;IACL,gJAAA,CAAA,SAAC,CAAC,UAAU;IACZ,gJAAA,CAAA,SAAC,CAAC,IAAI;IACN,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,MAAM;IACR,gJAAA,CAAA,SAAC,CAAC,IAAI;IACN,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,GAAG;IACL,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,KAAK;IACP,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,IAAI;IACN,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,CAAC;IACH,gJAAA,CAAA,SAAC,CAAC,GAAG;IACL,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,OAAO;IACT,gJAAA,CAAA,SAAC,CAAC,IAAI;IACN,gJAAA,CAAA,SAAC,CAAC,IAAI;IACN,gJAAA,CAAA,SAAC,CAAC,IAAI;IACN,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,CAAC;IACH,gJAAA,CAAA,SAAC,CAAC,GAAG;IACL,gJAAA,CAAA,SAAC,CAAC,IAAI;IACN,gJAAA,CAAA,SAAC,CAAC,CAAC;IACH,gJAAA,CAAA,SAAC,CAAC,KAAK;IACP,gJAAA,CAAA,SAAC,CAAC,IAAI;IACN,gJAAA,CAAA,SAAC,CAAC,MAAM;IACR,gJAAA,CAAA,SAAC,CAAC,MAAM;IACR,gJAAA,CAAA,SAAC,CAAC,GAAG;IACL,gJAAA,CAAA,SAAC,CAAC,GAAG;IACL,gJAAA,CAAA,SAAC,CAAC,KAAK;IACP,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,CAAC;IACH,gJAAA,CAAA,SAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,SAAC,CAAC,GAAG;CACR;AAEM,SAAS,WAAW,aAAa;IACpC,MAAM,KAAK,cAAc,KAAK;IAC9B,MAAM,kBAAkB,OAAO,gJAAA,CAAA,SAAC,CAAC,IAAI,IACjC,cAAc,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,GAAK,SAAS,gJAAA,CAAA,QAAK,CAAC,KAAK,IAAI,SAAS,gJAAA,CAAA,QAAK,CAAC,IAAI,IAAI,SAAS,gJAAA,CAAA,QAAK,CAAC,IAAI;IAC7G,OAAO,mBAAmB,sBAAsB,GAAG,CAAC;AACxD;AAEO,SAAS,uBAAuB,KAAK;IACxC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE,IAAK;QACzC,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,qBAAqB;YAC7C,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG;YACtB;QACJ;IACJ;AACJ;AACO,SAAS,oBAAoB,KAAK;IACrC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE,IAAK;QACzC,MAAM,mBAAmB,yBAAyB,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI;QACzE,IAAI,oBAAoB,MAAM;YAC1B,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG;QAC1B;IACJ;AACJ;AACO,SAAS,oBAAoB,KAAK;IACrC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE,IAAK;QACzC,MAAM,oBAAoB,yBAAyB,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI;QAC1E,IAAI,mBAAmB;YACnB,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,kBAAkB,MAAM;YAChD,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,kBAAkB,IAAI;YAC5C,MAAM,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG,kBAAkB,SAAS;QAC1D;IACJ;AACJ;AACO,SAAS,sBAAsB,KAAK;IACvC,MAAM,kBAAkB,6BAA6B,GAAG,CAAC,MAAM,OAAO;IACtE,IAAI,mBAAmB,MAAM;QACzB,MAAM,OAAO,GAAG;QAChB,MAAM,KAAK,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,OAAO;IACxC;AACJ;AACA,oBAAoB;AACpB,SAAS,6BAA6B,EAAE,EAAE,EAAE;IACxC,OAAO,OAAO,gJAAA,CAAA,KAAE,CAAC,MAAM,IAAI,CAAC,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK;AAC1G;AACA,SAAS,uBAAuB,EAAE,EAAE,EAAE,EAAE,KAAK;IACzC,IAAI,OAAO,gJAAA,CAAA,KAAE,CAAC,MAAM,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,cAAc,EAAE;QAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,gJAAA,CAAA,QAAK,CAAC,QAAQ,EAAE;gBAClC,MAAM,QAAQ,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW;gBACxC,OAAO,UAAU,WAAW,SAAS,IAAI,UAAU,WAAW,eAAe;YACjF;QACJ;IACJ;IACA,OAAO,OAAO,gJAAA,CAAA,KAAE,CAAC,GAAG,IAAI,CAAC,OAAO,gJAAA,CAAA,SAAC,CAAC,cAAc,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,IAAI,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK;AACvF;AACO,SAAS,mBAAmB,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS;IACvD,OAAQ,AAAC,CAAC,CAAC,aAAa,cAAc,gJAAA,CAAA,KAAE,CAAC,IAAI,KAAK,uBAAuB,IAAI,IAAI,UAC5E,CAAC,CAAC,aAAa,cAAc,gJAAA,CAAA,KAAE,CAAC,MAAM,KAAK,6BAA6B,IAAI;AACrF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5433, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/parser/index.js"], "sourcesContent": ["import { Tokenizer, TokenizerMode } from '../tokenizer/index.js';\nimport { OpenElementStack } from './open-element-stack.js';\nimport { FormattingElementList, EntryType } from './formatting-element-list.js';\nimport { defaultTreeAdapter } from '../tree-adapters/default.js';\nimport * as doctype from '../common/doctype.js';\nimport * as foreignContent from '../common/foreign-content.js';\nimport { ERR } from '../common/error-codes.js';\nimport * as unicode from '../common/unicode.js';\nimport { TAG_ID as $, TAG_NAMES as TN, NS, ATTRS, SPECIAL_ELEMENTS, DOCUMENT_MODE, NUMBERED_HEADERS, getTagID, } from '../common/html.js';\nimport { TokenType, getTokenAttr, } from '../common/token.js';\n//Misc constants\nconst HIDDEN_INPUT_TYPE = 'hidden';\n//Adoption agency loops iteration count\nconst AA_OUTER_LOOP_ITER = 8;\nconst AA_INNER_LOOP_ITER = 3;\n//Insertion modes\nvar InsertionMode;\n(function (InsertionMode) {\n    InsertionMode[InsertionMode[\"INITIAL\"] = 0] = \"INITIAL\";\n    InsertionMode[InsertionMode[\"BEFORE_HTML\"] = 1] = \"BEFORE_HTML\";\n    InsertionMode[InsertionMode[\"BEFORE_HEAD\"] = 2] = \"BEFORE_HEAD\";\n    InsertionMode[InsertionMode[\"IN_HEAD\"] = 3] = \"IN_HEAD\";\n    InsertionMode[InsertionMode[\"IN_HEAD_NO_SCRIPT\"] = 4] = \"IN_HEAD_NO_SCRIPT\";\n    InsertionMode[InsertionMode[\"AFTER_HEAD\"] = 5] = \"AFTER_HEAD\";\n    InsertionMode[InsertionMode[\"IN_BODY\"] = 6] = \"IN_BODY\";\n    InsertionMode[InsertionMode[\"TEXT\"] = 7] = \"TEXT\";\n    InsertionMode[InsertionMode[\"IN_TABLE\"] = 8] = \"IN_TABLE\";\n    InsertionMode[InsertionMode[\"IN_TABLE_TEXT\"] = 9] = \"IN_TABLE_TEXT\";\n    InsertionMode[InsertionMode[\"IN_CAPTION\"] = 10] = \"IN_CAPTION\";\n    InsertionMode[InsertionMode[\"IN_COLUMN_GROUP\"] = 11] = \"IN_COLUMN_GROUP\";\n    InsertionMode[InsertionMode[\"IN_TABLE_BODY\"] = 12] = \"IN_TABLE_BODY\";\n    InsertionMode[InsertionMode[\"IN_ROW\"] = 13] = \"IN_ROW\";\n    InsertionMode[InsertionMode[\"IN_CELL\"] = 14] = \"IN_CELL\";\n    InsertionMode[InsertionMode[\"IN_SELECT\"] = 15] = \"IN_SELECT\";\n    InsertionMode[InsertionMode[\"IN_SELECT_IN_TABLE\"] = 16] = \"IN_SELECT_IN_TABLE\";\n    InsertionMode[InsertionMode[\"IN_TEMPLATE\"] = 17] = \"IN_TEMPLATE\";\n    InsertionMode[InsertionMode[\"AFTER_BODY\"] = 18] = \"AFTER_BODY\";\n    InsertionMode[InsertionMode[\"IN_FRAMESET\"] = 19] = \"IN_FRAMESET\";\n    InsertionMode[InsertionMode[\"AFTER_FRAMESET\"] = 20] = \"AFTER_FRAMESET\";\n    InsertionMode[InsertionMode[\"AFTER_AFTER_BODY\"] = 21] = \"AFTER_AFTER_BODY\";\n    InsertionMode[InsertionMode[\"AFTER_AFTER_FRAMESET\"] = 22] = \"AFTER_AFTER_FRAMESET\";\n})(InsertionMode || (InsertionMode = {}));\nconst BASE_LOC = {\n    startLine: -1,\n    startCol: -1,\n    startOffset: -1,\n    endLine: -1,\n    endCol: -1,\n    endOffset: -1,\n};\nconst TABLE_STRUCTURE_TAGS = new Set([$.TABLE, $.TBODY, $.TFOOT, $.THEAD, $.TR]);\nconst defaultParserOptions = {\n    scriptingEnabled: true,\n    sourceCodeLocationInfo: false,\n    treeAdapter: defaultTreeAdapter,\n    onParseError: null,\n};\n//Parser\nexport class Parser {\n    constructor(options, document, \n    /** @internal */\n    fragmentContext = null, \n    /** @internal */\n    scriptHandler = null) {\n        this.fragmentContext = fragmentContext;\n        this.scriptHandler = scriptHandler;\n        this.currentToken = null;\n        this.stopped = false;\n        /** @internal */\n        this.insertionMode = InsertionMode.INITIAL;\n        /** @internal */\n        this.originalInsertionMode = InsertionMode.INITIAL;\n        /** @internal */\n        this.headElement = null;\n        /** @internal */\n        this.formElement = null;\n        /** Indicates that the current node is not an element in the HTML namespace */\n        this.currentNotInHTML = false;\n        /**\n         * The template insertion mode stack is maintained from the left.\n         * Ie. the topmost element will always have index 0.\n         *\n         * @internal\n         */\n        this.tmplInsertionModeStack = [];\n        /** @internal */\n        this.pendingCharacterTokens = [];\n        /** @internal */\n        this.hasNonWhitespacePendingCharacterToken = false;\n        /** @internal */\n        this.framesetOk = true;\n        /** @internal */\n        this.skipNextNewLine = false;\n        /** @internal */\n        this.fosterParentingEnabled = false;\n        this.options = {\n            ...defaultParserOptions,\n            ...options,\n        };\n        this.treeAdapter = this.options.treeAdapter;\n        this.onParseError = this.options.onParseError;\n        // Always enable location info if we report parse errors.\n        if (this.onParseError) {\n            this.options.sourceCodeLocationInfo = true;\n        }\n        this.document = document !== null && document !== void 0 ? document : this.treeAdapter.createDocument();\n        this.tokenizer = new Tokenizer(this.options, this);\n        this.activeFormattingElements = new FormattingElementList(this.treeAdapter);\n        this.fragmentContextID = fragmentContext ? getTagID(this.treeAdapter.getTagName(fragmentContext)) : $.UNKNOWN;\n        this._setContextModes(fragmentContext !== null && fragmentContext !== void 0 ? fragmentContext : this.document, this.fragmentContextID);\n        this.openElements = new OpenElementStack(this.document, this.treeAdapter, this);\n    }\n    // API\n    static parse(html, options) {\n        const parser = new this(options);\n        parser.tokenizer.write(html, true);\n        return parser.document;\n    }\n    static getFragmentParser(fragmentContext, options) {\n        const opts = {\n            ...defaultParserOptions,\n            ...options,\n        };\n        //NOTE: use a <template> element as the fragment context if no context element was provided,\n        //so we will parse in a \"forgiving\" manner\n        fragmentContext !== null && fragmentContext !== void 0 ? fragmentContext : (fragmentContext = opts.treeAdapter.createElement(TN.TEMPLATE, NS.HTML, []));\n        //NOTE: create a fake element which will be used as the `document` for fragment parsing.\n        //This is important for jsdom, where a new `document` cannot be created. This led to\n        //fragment parsing messing with the main `document`.\n        const documentMock = opts.treeAdapter.createElement('documentmock', NS.HTML, []);\n        const parser = new this(opts, documentMock, fragmentContext);\n        if (parser.fragmentContextID === $.TEMPLATE) {\n            parser.tmplInsertionModeStack.unshift(InsertionMode.IN_TEMPLATE);\n        }\n        parser._initTokenizerForFragmentParsing();\n        parser._insertFakeRootElement();\n        parser._resetInsertionMode();\n        parser._findFormInFragmentContext();\n        return parser;\n    }\n    getFragment() {\n        const rootElement = this.treeAdapter.getFirstChild(this.document);\n        const fragment = this.treeAdapter.createDocumentFragment();\n        this._adoptNodes(rootElement, fragment);\n        return fragment;\n    }\n    //Errors\n    /** @internal */\n    _err(token, code, beforeToken) {\n        var _a;\n        if (!this.onParseError)\n            return;\n        const loc = (_a = token.location) !== null && _a !== void 0 ? _a : BASE_LOC;\n        const err = {\n            code,\n            startLine: loc.startLine,\n            startCol: loc.startCol,\n            startOffset: loc.startOffset,\n            endLine: beforeToken ? loc.startLine : loc.endLine,\n            endCol: beforeToken ? loc.startCol : loc.endCol,\n            endOffset: beforeToken ? loc.startOffset : loc.endOffset,\n        };\n        this.onParseError(err);\n    }\n    //Stack events\n    /** @internal */\n    onItemPush(node, tid, isTop) {\n        var _a, _b;\n        (_b = (_a = this.treeAdapter).onItemPush) === null || _b === void 0 ? void 0 : _b.call(_a, node);\n        if (isTop && this.openElements.stackTop > 0)\n            this._setContextModes(node, tid);\n    }\n    /** @internal */\n    onItemPop(node, isTop) {\n        var _a, _b;\n        if (this.options.sourceCodeLocationInfo) {\n            this._setEndLocation(node, this.currentToken);\n        }\n        (_b = (_a = this.treeAdapter).onItemPop) === null || _b === void 0 ? void 0 : _b.call(_a, node, this.openElements.current);\n        if (isTop) {\n            let current;\n            let currentTagId;\n            if (this.openElements.stackTop === 0 && this.fragmentContext) {\n                current = this.fragmentContext;\n                currentTagId = this.fragmentContextID;\n            }\n            else {\n                ({ current, currentTagId } = this.openElements);\n            }\n            this._setContextModes(current, currentTagId);\n        }\n    }\n    _setContextModes(current, tid) {\n        const isHTML = current === this.document || (current && this.treeAdapter.getNamespaceURI(current) === NS.HTML);\n        this.currentNotInHTML = !isHTML;\n        this.tokenizer.inForeignNode =\n            !isHTML && current !== undefined && tid !== undefined && !this._isIntegrationPoint(tid, current);\n    }\n    /** @protected */\n    _switchToTextParsing(currentToken, nextTokenizerState) {\n        this._insertElement(currentToken, NS.HTML);\n        this.tokenizer.state = nextTokenizerState;\n        this.originalInsertionMode = this.insertionMode;\n        this.insertionMode = InsertionMode.TEXT;\n    }\n    switchToPlaintextParsing() {\n        this.insertionMode = InsertionMode.TEXT;\n        this.originalInsertionMode = InsertionMode.IN_BODY;\n        this.tokenizer.state = TokenizerMode.PLAINTEXT;\n    }\n    //Fragment parsing\n    /** @protected */\n    _getAdjustedCurrentElement() {\n        return this.openElements.stackTop === 0 && this.fragmentContext\n            ? this.fragmentContext\n            : this.openElements.current;\n    }\n    /** @protected */\n    _findFormInFragmentContext() {\n        let node = this.fragmentContext;\n        while (node) {\n            if (this.treeAdapter.getTagName(node) === TN.FORM) {\n                this.formElement = node;\n                break;\n            }\n            node = this.treeAdapter.getParentNode(node);\n        }\n    }\n    _initTokenizerForFragmentParsing() {\n        if (!this.fragmentContext || this.treeAdapter.getNamespaceURI(this.fragmentContext) !== NS.HTML) {\n            return;\n        }\n        switch (this.fragmentContextID) {\n            case $.TITLE:\n            case $.TEXTAREA: {\n                this.tokenizer.state = TokenizerMode.RCDATA;\n                break;\n            }\n            case $.STYLE:\n            case $.XMP:\n            case $.IFRAME:\n            case $.NOEMBED:\n            case $.NOFRAMES:\n            case $.NOSCRIPT: {\n                this.tokenizer.state = TokenizerMode.RAWTEXT;\n                break;\n            }\n            case $.SCRIPT: {\n                this.tokenizer.state = TokenizerMode.SCRIPT_DATA;\n                break;\n            }\n            case $.PLAINTEXT: {\n                this.tokenizer.state = TokenizerMode.PLAINTEXT;\n                break;\n            }\n            default:\n            // Do nothing\n        }\n    }\n    //Tree mutation\n    /** @protected */\n    _setDocumentType(token) {\n        const name = token.name || '';\n        const publicId = token.publicId || '';\n        const systemId = token.systemId || '';\n        this.treeAdapter.setDocumentType(this.document, name, publicId, systemId);\n        if (token.location) {\n            const documentChildren = this.treeAdapter.getChildNodes(this.document);\n            const docTypeNode = documentChildren.find((node) => this.treeAdapter.isDocumentTypeNode(node));\n            if (docTypeNode) {\n                this.treeAdapter.setNodeSourceCodeLocation(docTypeNode, token.location);\n            }\n        }\n    }\n    /** @protected */\n    _attachElementToTree(element, location) {\n        if (this.options.sourceCodeLocationInfo) {\n            const loc = location && {\n                ...location,\n                startTag: location,\n            };\n            this.treeAdapter.setNodeSourceCodeLocation(element, loc);\n        }\n        if (this._shouldFosterParentOnInsertion()) {\n            this._fosterParentElement(element);\n        }\n        else {\n            const parent = this.openElements.currentTmplContentOrNode;\n            this.treeAdapter.appendChild(parent !== null && parent !== void 0 ? parent : this.document, element);\n        }\n    }\n    /**\n     * For self-closing tags. Add an element to the tree, but skip adding it\n     * to the stack.\n     */\n    /** @protected */\n    _appendElement(token, namespaceURI) {\n        const element = this.treeAdapter.createElement(token.tagName, namespaceURI, token.attrs);\n        this._attachElementToTree(element, token.location);\n    }\n    /** @protected */\n    _insertElement(token, namespaceURI) {\n        const element = this.treeAdapter.createElement(token.tagName, namespaceURI, token.attrs);\n        this._attachElementToTree(element, token.location);\n        this.openElements.push(element, token.tagID);\n    }\n    /** @protected */\n    _insertFakeElement(tagName, tagID) {\n        const element = this.treeAdapter.createElement(tagName, NS.HTML, []);\n        this._attachElementToTree(element, null);\n        this.openElements.push(element, tagID);\n    }\n    /** @protected */\n    _insertTemplate(token) {\n        const tmpl = this.treeAdapter.createElement(token.tagName, NS.HTML, token.attrs);\n        const content = this.treeAdapter.createDocumentFragment();\n        this.treeAdapter.setTemplateContent(tmpl, content);\n        this._attachElementToTree(tmpl, token.location);\n        this.openElements.push(tmpl, token.tagID);\n        if (this.options.sourceCodeLocationInfo)\n            this.treeAdapter.setNodeSourceCodeLocation(content, null);\n    }\n    /** @protected */\n    _insertFakeRootElement() {\n        const element = this.treeAdapter.createElement(TN.HTML, NS.HTML, []);\n        if (this.options.sourceCodeLocationInfo)\n            this.treeAdapter.setNodeSourceCodeLocation(element, null);\n        this.treeAdapter.appendChild(this.openElements.current, element);\n        this.openElements.push(element, $.HTML);\n    }\n    /** @protected */\n    _appendCommentNode(token, parent) {\n        const commentNode = this.treeAdapter.createCommentNode(token.data);\n        this.treeAdapter.appendChild(parent, commentNode);\n        if (this.options.sourceCodeLocationInfo) {\n            this.treeAdapter.setNodeSourceCodeLocation(commentNode, token.location);\n        }\n    }\n    /** @protected */\n    _insertCharacters(token) {\n        let parent;\n        let beforeElement;\n        if (this._shouldFosterParentOnInsertion()) {\n            ({ parent, beforeElement } = this._findFosterParentingLocation());\n            if (beforeElement) {\n                this.treeAdapter.insertTextBefore(parent, token.chars, beforeElement);\n            }\n            else {\n                this.treeAdapter.insertText(parent, token.chars);\n            }\n        }\n        else {\n            parent = this.openElements.currentTmplContentOrNode;\n            this.treeAdapter.insertText(parent, token.chars);\n        }\n        if (!token.location)\n            return;\n        const siblings = this.treeAdapter.getChildNodes(parent);\n        const textNodeIdx = beforeElement ? siblings.lastIndexOf(beforeElement) : siblings.length;\n        const textNode = siblings[textNodeIdx - 1];\n        //NOTE: if we have a location assigned by another token, then just update the end position\n        const tnLoc = this.treeAdapter.getNodeSourceCodeLocation(textNode);\n        if (tnLoc) {\n            const { endLine, endCol, endOffset } = token.location;\n            this.treeAdapter.updateNodeSourceCodeLocation(textNode, { endLine, endCol, endOffset });\n        }\n        else if (this.options.sourceCodeLocationInfo) {\n            this.treeAdapter.setNodeSourceCodeLocation(textNode, token.location);\n        }\n    }\n    /** @protected */\n    _adoptNodes(donor, recipient) {\n        for (let child = this.treeAdapter.getFirstChild(donor); child; child = this.treeAdapter.getFirstChild(donor)) {\n            this.treeAdapter.detachNode(child);\n            this.treeAdapter.appendChild(recipient, child);\n        }\n    }\n    /** @protected */\n    _setEndLocation(element, closingToken) {\n        if (this.treeAdapter.getNodeSourceCodeLocation(element) && closingToken.location) {\n            const ctLoc = closingToken.location;\n            const tn = this.treeAdapter.getTagName(element);\n            const endLoc = \n            // NOTE: For cases like <p> <p> </p> - First 'p' closes without a closing\n            // tag and for cases like <td> <p> </td> - 'p' closes without a closing tag.\n            closingToken.type === TokenType.END_TAG && tn === closingToken.tagName\n                ? {\n                    endTag: { ...ctLoc },\n                    endLine: ctLoc.endLine,\n                    endCol: ctLoc.endCol,\n                    endOffset: ctLoc.endOffset,\n                }\n                : {\n                    endLine: ctLoc.startLine,\n                    endCol: ctLoc.startCol,\n                    endOffset: ctLoc.startOffset,\n                };\n            this.treeAdapter.updateNodeSourceCodeLocation(element, endLoc);\n        }\n    }\n    //Token processing\n    shouldProcessStartTagTokenInForeignContent(token) {\n        // Check that neither current === document, or ns === NS.HTML\n        if (!this.currentNotInHTML)\n            return false;\n        let current;\n        let currentTagId;\n        if (this.openElements.stackTop === 0 && this.fragmentContext) {\n            current = this.fragmentContext;\n            currentTagId = this.fragmentContextID;\n        }\n        else {\n            ({ current, currentTagId } = this.openElements);\n        }\n        if (token.tagID === $.SVG &&\n            this.treeAdapter.getTagName(current) === TN.ANNOTATION_XML &&\n            this.treeAdapter.getNamespaceURI(current) === NS.MATHML) {\n            return false;\n        }\n        return (\n        // Check that `current` is not an integration point for HTML or MathML elements.\n        this.tokenizer.inForeignNode ||\n            // If it _is_ an integration point, then we might have to check that it is not an HTML\n            // integration point.\n            ((token.tagID === $.MGLYPH || token.tagID === $.MALIGNMARK) &&\n                currentTagId !== undefined &&\n                !this._isIntegrationPoint(currentTagId, current, NS.HTML)));\n    }\n    /** @protected */\n    _processToken(token) {\n        switch (token.type) {\n            case TokenType.CHARACTER: {\n                this.onCharacter(token);\n                break;\n            }\n            case TokenType.NULL_CHARACTER: {\n                this.onNullCharacter(token);\n                break;\n            }\n            case TokenType.COMMENT: {\n                this.onComment(token);\n                break;\n            }\n            case TokenType.DOCTYPE: {\n                this.onDoctype(token);\n                break;\n            }\n            case TokenType.START_TAG: {\n                this._processStartTag(token);\n                break;\n            }\n            case TokenType.END_TAG: {\n                this.onEndTag(token);\n                break;\n            }\n            case TokenType.EOF: {\n                this.onEof(token);\n                break;\n            }\n            case TokenType.WHITESPACE_CHARACTER: {\n                this.onWhitespaceCharacter(token);\n                break;\n            }\n        }\n    }\n    //Integration points\n    /** @protected */\n    _isIntegrationPoint(tid, element, foreignNS) {\n        const ns = this.treeAdapter.getNamespaceURI(element);\n        const attrs = this.treeAdapter.getAttrList(element);\n        return foreignContent.isIntegrationPoint(tid, ns, attrs, foreignNS);\n    }\n    //Active formatting elements reconstruction\n    /** @protected */\n    _reconstructActiveFormattingElements() {\n        const listLength = this.activeFormattingElements.entries.length;\n        if (listLength) {\n            const endIndex = this.activeFormattingElements.entries.findIndex((entry) => entry.type === EntryType.Marker || this.openElements.contains(entry.element));\n            const unopenIdx = endIndex === -1 ? listLength - 1 : endIndex - 1;\n            for (let i = unopenIdx; i >= 0; i--) {\n                const entry = this.activeFormattingElements.entries[i];\n                this._insertElement(entry.token, this.treeAdapter.getNamespaceURI(entry.element));\n                entry.element = this.openElements.current;\n            }\n        }\n    }\n    //Close elements\n    /** @protected */\n    _closeTableCell() {\n        this.openElements.generateImpliedEndTags();\n        this.openElements.popUntilTableCellPopped();\n        this.activeFormattingElements.clearToLastMarker();\n        this.insertionMode = InsertionMode.IN_ROW;\n    }\n    /** @protected */\n    _closePElement() {\n        this.openElements.generateImpliedEndTagsWithExclusion($.P);\n        this.openElements.popUntilTagNamePopped($.P);\n    }\n    //Insertion modes\n    /** @protected */\n    _resetInsertionMode() {\n        for (let i = this.openElements.stackTop; i >= 0; i--) {\n            //Insertion mode reset map\n            switch (i === 0 && this.fragmentContext ? this.fragmentContextID : this.openElements.tagIDs[i]) {\n                case $.TR: {\n                    this.insertionMode = InsertionMode.IN_ROW;\n                    return;\n                }\n                case $.TBODY:\n                case $.THEAD:\n                case $.TFOOT: {\n                    this.insertionMode = InsertionMode.IN_TABLE_BODY;\n                    return;\n                }\n                case $.CAPTION: {\n                    this.insertionMode = InsertionMode.IN_CAPTION;\n                    return;\n                }\n                case $.COLGROUP: {\n                    this.insertionMode = InsertionMode.IN_COLUMN_GROUP;\n                    return;\n                }\n                case $.TABLE: {\n                    this.insertionMode = InsertionMode.IN_TABLE;\n                    return;\n                }\n                case $.BODY: {\n                    this.insertionMode = InsertionMode.IN_BODY;\n                    return;\n                }\n                case $.FRAMESET: {\n                    this.insertionMode = InsertionMode.IN_FRAMESET;\n                    return;\n                }\n                case $.SELECT: {\n                    this._resetInsertionModeForSelect(i);\n                    return;\n                }\n                case $.TEMPLATE: {\n                    this.insertionMode = this.tmplInsertionModeStack[0];\n                    return;\n                }\n                case $.HTML: {\n                    this.insertionMode = this.headElement ? InsertionMode.AFTER_HEAD : InsertionMode.BEFORE_HEAD;\n                    return;\n                }\n                case $.TD:\n                case $.TH: {\n                    if (i > 0) {\n                        this.insertionMode = InsertionMode.IN_CELL;\n                        return;\n                    }\n                    break;\n                }\n                case $.HEAD: {\n                    if (i > 0) {\n                        this.insertionMode = InsertionMode.IN_HEAD;\n                        return;\n                    }\n                    break;\n                }\n            }\n        }\n        this.insertionMode = InsertionMode.IN_BODY;\n    }\n    /** @protected */\n    _resetInsertionModeForSelect(selectIdx) {\n        if (selectIdx > 0) {\n            for (let i = selectIdx - 1; i > 0; i--) {\n                const tn = this.openElements.tagIDs[i];\n                if (tn === $.TEMPLATE) {\n                    break;\n                }\n                else if (tn === $.TABLE) {\n                    this.insertionMode = InsertionMode.IN_SELECT_IN_TABLE;\n                    return;\n                }\n            }\n        }\n        this.insertionMode = InsertionMode.IN_SELECT;\n    }\n    //Foster parenting\n    /** @protected */\n    _isElementCausesFosterParenting(tn) {\n        return TABLE_STRUCTURE_TAGS.has(tn);\n    }\n    /** @protected */\n    _shouldFosterParentOnInsertion() {\n        return (this.fosterParentingEnabled &&\n            this.openElements.currentTagId !== undefined &&\n            this._isElementCausesFosterParenting(this.openElements.currentTagId));\n    }\n    /** @protected */\n    _findFosterParentingLocation() {\n        for (let i = this.openElements.stackTop; i >= 0; i--) {\n            const openElement = this.openElements.items[i];\n            switch (this.openElements.tagIDs[i]) {\n                case $.TEMPLATE: {\n                    if (this.treeAdapter.getNamespaceURI(openElement) === NS.HTML) {\n                        return { parent: this.treeAdapter.getTemplateContent(openElement), beforeElement: null };\n                    }\n                    break;\n                }\n                case $.TABLE: {\n                    const parent = this.treeAdapter.getParentNode(openElement);\n                    if (parent) {\n                        return { parent, beforeElement: openElement };\n                    }\n                    return { parent: this.openElements.items[i - 1], beforeElement: null };\n                }\n                default:\n                // Do nothing\n            }\n        }\n        return { parent: this.openElements.items[0], beforeElement: null };\n    }\n    /** @protected */\n    _fosterParentElement(element) {\n        const location = this._findFosterParentingLocation();\n        if (location.beforeElement) {\n            this.treeAdapter.insertBefore(location.parent, element, location.beforeElement);\n        }\n        else {\n            this.treeAdapter.appendChild(location.parent, element);\n        }\n    }\n    //Special elements\n    /** @protected */\n    _isSpecialElement(element, id) {\n        const ns = this.treeAdapter.getNamespaceURI(element);\n        return SPECIAL_ELEMENTS[ns].has(id);\n    }\n    /** @internal */\n    onCharacter(token) {\n        this.skipNextNewLine = false;\n        if (this.tokenizer.inForeignNode) {\n            characterInForeignContent(this, token);\n            return;\n        }\n        switch (this.insertionMode) {\n            case InsertionMode.INITIAL: {\n                tokenInInitialMode(this, token);\n                break;\n            }\n            case InsertionMode.BEFORE_HTML: {\n                tokenBeforeHtml(this, token);\n                break;\n            }\n            case InsertionMode.BEFORE_HEAD: {\n                tokenBeforeHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_HEAD: {\n                tokenInHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_HEAD_NO_SCRIPT: {\n                tokenInHeadNoScript(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_HEAD: {\n                tokenAfterHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_BODY:\n            case InsertionMode.IN_CAPTION:\n            case InsertionMode.IN_CELL:\n            case InsertionMode.IN_TEMPLATE: {\n                characterInBody(this, token);\n                break;\n            }\n            case InsertionMode.TEXT:\n            case InsertionMode.IN_SELECT:\n            case InsertionMode.IN_SELECT_IN_TABLE: {\n                this._insertCharacters(token);\n                break;\n            }\n            case InsertionMode.IN_TABLE:\n            case InsertionMode.IN_TABLE_BODY:\n            case InsertionMode.IN_ROW: {\n                characterInTable(this, token);\n                break;\n            }\n            case InsertionMode.IN_TABLE_TEXT: {\n                characterInTableText(this, token);\n                break;\n            }\n            case InsertionMode.IN_COLUMN_GROUP: {\n                tokenInColumnGroup(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_BODY: {\n                tokenAfterBody(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_AFTER_BODY: {\n                tokenAfterAfterBody(this, token);\n                break;\n            }\n            default:\n            // Do nothing\n        }\n    }\n    /** @internal */\n    onNullCharacter(token) {\n        this.skipNextNewLine = false;\n        if (this.tokenizer.inForeignNode) {\n            nullCharacterInForeignContent(this, token);\n            return;\n        }\n        switch (this.insertionMode) {\n            case InsertionMode.INITIAL: {\n                tokenInInitialMode(this, token);\n                break;\n            }\n            case InsertionMode.BEFORE_HTML: {\n                tokenBeforeHtml(this, token);\n                break;\n            }\n            case InsertionMode.BEFORE_HEAD: {\n                tokenBeforeHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_HEAD: {\n                tokenInHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_HEAD_NO_SCRIPT: {\n                tokenInHeadNoScript(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_HEAD: {\n                tokenAfterHead(this, token);\n                break;\n            }\n            case InsertionMode.TEXT: {\n                this._insertCharacters(token);\n                break;\n            }\n            case InsertionMode.IN_TABLE:\n            case InsertionMode.IN_TABLE_BODY:\n            case InsertionMode.IN_ROW: {\n                characterInTable(this, token);\n                break;\n            }\n            case InsertionMode.IN_COLUMN_GROUP: {\n                tokenInColumnGroup(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_BODY: {\n                tokenAfterBody(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_AFTER_BODY: {\n                tokenAfterAfterBody(this, token);\n                break;\n            }\n            default:\n            // Do nothing\n        }\n    }\n    /** @internal */\n    onComment(token) {\n        this.skipNextNewLine = false;\n        if (this.currentNotInHTML) {\n            appendComment(this, token);\n            return;\n        }\n        switch (this.insertionMode) {\n            case InsertionMode.INITIAL:\n            case InsertionMode.BEFORE_HTML:\n            case InsertionMode.BEFORE_HEAD:\n            case InsertionMode.IN_HEAD:\n            case InsertionMode.IN_HEAD_NO_SCRIPT:\n            case InsertionMode.AFTER_HEAD:\n            case InsertionMode.IN_BODY:\n            case InsertionMode.IN_TABLE:\n            case InsertionMode.IN_CAPTION:\n            case InsertionMode.IN_COLUMN_GROUP:\n            case InsertionMode.IN_TABLE_BODY:\n            case InsertionMode.IN_ROW:\n            case InsertionMode.IN_CELL:\n            case InsertionMode.IN_SELECT:\n            case InsertionMode.IN_SELECT_IN_TABLE:\n            case InsertionMode.IN_TEMPLATE:\n            case InsertionMode.IN_FRAMESET:\n            case InsertionMode.AFTER_FRAMESET: {\n                appendComment(this, token);\n                break;\n            }\n            case InsertionMode.IN_TABLE_TEXT: {\n                tokenInTableText(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_BODY: {\n                appendCommentToRootHtmlElement(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_AFTER_BODY:\n            case InsertionMode.AFTER_AFTER_FRAMESET: {\n                appendCommentToDocument(this, token);\n                break;\n            }\n            default:\n            // Do nothing\n        }\n    }\n    /** @internal */\n    onDoctype(token) {\n        this.skipNextNewLine = false;\n        switch (this.insertionMode) {\n            case InsertionMode.INITIAL: {\n                doctypeInInitialMode(this, token);\n                break;\n            }\n            case InsertionMode.BEFORE_HEAD:\n            case InsertionMode.IN_HEAD:\n            case InsertionMode.IN_HEAD_NO_SCRIPT:\n            case InsertionMode.AFTER_HEAD: {\n                this._err(token, ERR.misplacedDoctype);\n                break;\n            }\n            case InsertionMode.IN_TABLE_TEXT: {\n                tokenInTableText(this, token);\n                break;\n            }\n            default:\n            // Do nothing\n        }\n    }\n    /** @internal */\n    onStartTag(token) {\n        this.skipNextNewLine = false;\n        this.currentToken = token;\n        this._processStartTag(token);\n        if (token.selfClosing && !token.ackSelfClosing) {\n            this._err(token, ERR.nonVoidHtmlElementStartTagWithTrailingSolidus);\n        }\n    }\n    /**\n     * Processes a given start tag.\n     *\n     * `onStartTag` checks if a self-closing tag was recognized. When a token\n     * is moved inbetween multiple insertion modes, this check for self-closing\n     * could lead to false positives. To avoid this, `_processStartTag` is used\n     * for nested calls.\n     *\n     * @param token The token to process.\n     * @protected\n     */\n    _processStartTag(token) {\n        if (this.shouldProcessStartTagTokenInForeignContent(token)) {\n            startTagInForeignContent(this, token);\n        }\n        else {\n            this._startTagOutsideForeignContent(token);\n        }\n    }\n    /** @protected */\n    _startTagOutsideForeignContent(token) {\n        switch (this.insertionMode) {\n            case InsertionMode.INITIAL: {\n                tokenInInitialMode(this, token);\n                break;\n            }\n            case InsertionMode.BEFORE_HTML: {\n                startTagBeforeHtml(this, token);\n                break;\n            }\n            case InsertionMode.BEFORE_HEAD: {\n                startTagBeforeHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_HEAD: {\n                startTagInHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_HEAD_NO_SCRIPT: {\n                startTagInHeadNoScript(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_HEAD: {\n                startTagAfterHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_BODY: {\n                startTagInBody(this, token);\n                break;\n            }\n            case InsertionMode.IN_TABLE: {\n                startTagInTable(this, token);\n                break;\n            }\n            case InsertionMode.IN_TABLE_TEXT: {\n                tokenInTableText(this, token);\n                break;\n            }\n            case InsertionMode.IN_CAPTION: {\n                startTagInCaption(this, token);\n                break;\n            }\n            case InsertionMode.IN_COLUMN_GROUP: {\n                startTagInColumnGroup(this, token);\n                break;\n            }\n            case InsertionMode.IN_TABLE_BODY: {\n                startTagInTableBody(this, token);\n                break;\n            }\n            case InsertionMode.IN_ROW: {\n                startTagInRow(this, token);\n                break;\n            }\n            case InsertionMode.IN_CELL: {\n                startTagInCell(this, token);\n                break;\n            }\n            case InsertionMode.IN_SELECT: {\n                startTagInSelect(this, token);\n                break;\n            }\n            case InsertionMode.IN_SELECT_IN_TABLE: {\n                startTagInSelectInTable(this, token);\n                break;\n            }\n            case InsertionMode.IN_TEMPLATE: {\n                startTagInTemplate(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_BODY: {\n                startTagAfterBody(this, token);\n                break;\n            }\n            case InsertionMode.IN_FRAMESET: {\n                startTagInFrameset(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_FRAMESET: {\n                startTagAfterFrameset(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_AFTER_BODY: {\n                startTagAfterAfterBody(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_AFTER_FRAMESET: {\n                startTagAfterAfterFrameset(this, token);\n                break;\n            }\n            default:\n            // Do nothing\n        }\n    }\n    /** @internal */\n    onEndTag(token) {\n        this.skipNextNewLine = false;\n        this.currentToken = token;\n        if (this.currentNotInHTML) {\n            endTagInForeignContent(this, token);\n        }\n        else {\n            this._endTagOutsideForeignContent(token);\n        }\n    }\n    /** @protected */\n    _endTagOutsideForeignContent(token) {\n        switch (this.insertionMode) {\n            case InsertionMode.INITIAL: {\n                tokenInInitialMode(this, token);\n                break;\n            }\n            case InsertionMode.BEFORE_HTML: {\n                endTagBeforeHtml(this, token);\n                break;\n            }\n            case InsertionMode.BEFORE_HEAD: {\n                endTagBeforeHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_HEAD: {\n                endTagInHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_HEAD_NO_SCRIPT: {\n                endTagInHeadNoScript(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_HEAD: {\n                endTagAfterHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_BODY: {\n                endTagInBody(this, token);\n                break;\n            }\n            case InsertionMode.TEXT: {\n                endTagInText(this, token);\n                break;\n            }\n            case InsertionMode.IN_TABLE: {\n                endTagInTable(this, token);\n                break;\n            }\n            case InsertionMode.IN_TABLE_TEXT: {\n                tokenInTableText(this, token);\n                break;\n            }\n            case InsertionMode.IN_CAPTION: {\n                endTagInCaption(this, token);\n                break;\n            }\n            case InsertionMode.IN_COLUMN_GROUP: {\n                endTagInColumnGroup(this, token);\n                break;\n            }\n            case InsertionMode.IN_TABLE_BODY: {\n                endTagInTableBody(this, token);\n                break;\n            }\n            case InsertionMode.IN_ROW: {\n                endTagInRow(this, token);\n                break;\n            }\n            case InsertionMode.IN_CELL: {\n                endTagInCell(this, token);\n                break;\n            }\n            case InsertionMode.IN_SELECT: {\n                endTagInSelect(this, token);\n                break;\n            }\n            case InsertionMode.IN_SELECT_IN_TABLE: {\n                endTagInSelectInTable(this, token);\n                break;\n            }\n            case InsertionMode.IN_TEMPLATE: {\n                endTagInTemplate(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_BODY: {\n                endTagAfterBody(this, token);\n                break;\n            }\n            case InsertionMode.IN_FRAMESET: {\n                endTagInFrameset(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_FRAMESET: {\n                endTagAfterFrameset(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_AFTER_BODY: {\n                tokenAfterAfterBody(this, token);\n                break;\n            }\n            default:\n            // Do nothing\n        }\n    }\n    /** @internal */\n    onEof(token) {\n        switch (this.insertionMode) {\n            case InsertionMode.INITIAL: {\n                tokenInInitialMode(this, token);\n                break;\n            }\n            case InsertionMode.BEFORE_HTML: {\n                tokenBeforeHtml(this, token);\n                break;\n            }\n            case InsertionMode.BEFORE_HEAD: {\n                tokenBeforeHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_HEAD: {\n                tokenInHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_HEAD_NO_SCRIPT: {\n                tokenInHeadNoScript(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_HEAD: {\n                tokenAfterHead(this, token);\n                break;\n            }\n            case InsertionMode.IN_BODY:\n            case InsertionMode.IN_TABLE:\n            case InsertionMode.IN_CAPTION:\n            case InsertionMode.IN_COLUMN_GROUP:\n            case InsertionMode.IN_TABLE_BODY:\n            case InsertionMode.IN_ROW:\n            case InsertionMode.IN_CELL:\n            case InsertionMode.IN_SELECT:\n            case InsertionMode.IN_SELECT_IN_TABLE: {\n                eofInBody(this, token);\n                break;\n            }\n            case InsertionMode.TEXT: {\n                eofInText(this, token);\n                break;\n            }\n            case InsertionMode.IN_TABLE_TEXT: {\n                tokenInTableText(this, token);\n                break;\n            }\n            case InsertionMode.IN_TEMPLATE: {\n                eofInTemplate(this, token);\n                break;\n            }\n            case InsertionMode.AFTER_BODY:\n            case InsertionMode.IN_FRAMESET:\n            case InsertionMode.AFTER_FRAMESET:\n            case InsertionMode.AFTER_AFTER_BODY:\n            case InsertionMode.AFTER_AFTER_FRAMESET: {\n                stopParsing(this, token);\n                break;\n            }\n            default:\n            // Do nothing\n        }\n    }\n    /** @internal */\n    onWhitespaceCharacter(token) {\n        if (this.skipNextNewLine) {\n            this.skipNextNewLine = false;\n            if (token.chars.charCodeAt(0) === unicode.CODE_POINTS.LINE_FEED) {\n                if (token.chars.length === 1) {\n                    return;\n                }\n                token.chars = token.chars.substr(1);\n            }\n        }\n        if (this.tokenizer.inForeignNode) {\n            this._insertCharacters(token);\n            return;\n        }\n        switch (this.insertionMode) {\n            case InsertionMode.IN_HEAD:\n            case InsertionMode.IN_HEAD_NO_SCRIPT:\n            case InsertionMode.AFTER_HEAD:\n            case InsertionMode.TEXT:\n            case InsertionMode.IN_COLUMN_GROUP:\n            case InsertionMode.IN_SELECT:\n            case InsertionMode.IN_SELECT_IN_TABLE:\n            case InsertionMode.IN_FRAMESET:\n            case InsertionMode.AFTER_FRAMESET: {\n                this._insertCharacters(token);\n                break;\n            }\n            case InsertionMode.IN_BODY:\n            case InsertionMode.IN_CAPTION:\n            case InsertionMode.IN_CELL:\n            case InsertionMode.IN_TEMPLATE:\n            case InsertionMode.AFTER_BODY:\n            case InsertionMode.AFTER_AFTER_BODY:\n            case InsertionMode.AFTER_AFTER_FRAMESET: {\n                whitespaceCharacterInBody(this, token);\n                break;\n            }\n            case InsertionMode.IN_TABLE:\n            case InsertionMode.IN_TABLE_BODY:\n            case InsertionMode.IN_ROW: {\n                characterInTable(this, token);\n                break;\n            }\n            case InsertionMode.IN_TABLE_TEXT: {\n                whitespaceCharacterInTableText(this, token);\n                break;\n            }\n            default:\n            // Do nothing\n        }\n    }\n}\n//Adoption agency algorithm\n//(see: http://www.whatwg.org/specs/web-apps/current-work/multipage/tree-construction.html#adoptionAgency)\n//------------------------------------------------------------------\n//Steps 5-8 of the algorithm\nfunction aaObtainFormattingElementEntry(p, token) {\n    let formattingElementEntry = p.activeFormattingElements.getElementEntryInScopeWithTagName(token.tagName);\n    if (formattingElementEntry) {\n        if (!p.openElements.contains(formattingElementEntry.element)) {\n            p.activeFormattingElements.removeEntry(formattingElementEntry);\n            formattingElementEntry = null;\n        }\n        else if (!p.openElements.hasInScope(token.tagID)) {\n            formattingElementEntry = null;\n        }\n    }\n    else {\n        genericEndTagInBody(p, token);\n    }\n    return formattingElementEntry;\n}\n//Steps 9 and 10 of the algorithm\nfunction aaObtainFurthestBlock(p, formattingElementEntry) {\n    let furthestBlock = null;\n    let idx = p.openElements.stackTop;\n    for (; idx >= 0; idx--) {\n        const element = p.openElements.items[idx];\n        if (element === formattingElementEntry.element) {\n            break;\n        }\n        if (p._isSpecialElement(element, p.openElements.tagIDs[idx])) {\n            furthestBlock = element;\n        }\n    }\n    if (!furthestBlock) {\n        p.openElements.shortenToLength(Math.max(idx, 0));\n        p.activeFormattingElements.removeEntry(formattingElementEntry);\n    }\n    return furthestBlock;\n}\n//Step 13 of the algorithm\nfunction aaInnerLoop(p, furthestBlock, formattingElement) {\n    let lastElement = furthestBlock;\n    let nextElement = p.openElements.getCommonAncestor(furthestBlock);\n    for (let i = 0, element = nextElement; element !== formattingElement; i++, element = nextElement) {\n        //NOTE: store the next element for the next loop iteration (it may be deleted from the stack by step 9.5)\n        nextElement = p.openElements.getCommonAncestor(element);\n        const elementEntry = p.activeFormattingElements.getElementEntry(element);\n        const counterOverflow = elementEntry && i >= AA_INNER_LOOP_ITER;\n        const shouldRemoveFromOpenElements = !elementEntry || counterOverflow;\n        if (shouldRemoveFromOpenElements) {\n            if (counterOverflow) {\n                p.activeFormattingElements.removeEntry(elementEntry);\n            }\n            p.openElements.remove(element);\n        }\n        else {\n            element = aaRecreateElementFromEntry(p, elementEntry);\n            if (lastElement === furthestBlock) {\n                p.activeFormattingElements.bookmark = elementEntry;\n            }\n            p.treeAdapter.detachNode(lastElement);\n            p.treeAdapter.appendChild(element, lastElement);\n            lastElement = element;\n        }\n    }\n    return lastElement;\n}\n//Step 13.7 of the algorithm\nfunction aaRecreateElementFromEntry(p, elementEntry) {\n    const ns = p.treeAdapter.getNamespaceURI(elementEntry.element);\n    const newElement = p.treeAdapter.createElement(elementEntry.token.tagName, ns, elementEntry.token.attrs);\n    p.openElements.replace(elementEntry.element, newElement);\n    elementEntry.element = newElement;\n    return newElement;\n}\n//Step 14 of the algorithm\nfunction aaInsertLastNodeInCommonAncestor(p, commonAncestor, lastElement) {\n    const tn = p.treeAdapter.getTagName(commonAncestor);\n    const tid = getTagID(tn);\n    if (p._isElementCausesFosterParenting(tid)) {\n        p._fosterParentElement(lastElement);\n    }\n    else {\n        const ns = p.treeAdapter.getNamespaceURI(commonAncestor);\n        if (tid === $.TEMPLATE && ns === NS.HTML) {\n            commonAncestor = p.treeAdapter.getTemplateContent(commonAncestor);\n        }\n        p.treeAdapter.appendChild(commonAncestor, lastElement);\n    }\n}\n//Steps 15-19 of the algorithm\nfunction aaReplaceFormattingElement(p, furthestBlock, formattingElementEntry) {\n    const ns = p.treeAdapter.getNamespaceURI(formattingElementEntry.element);\n    const { token } = formattingElementEntry;\n    const newElement = p.treeAdapter.createElement(token.tagName, ns, token.attrs);\n    p._adoptNodes(furthestBlock, newElement);\n    p.treeAdapter.appendChild(furthestBlock, newElement);\n    p.activeFormattingElements.insertElementAfterBookmark(newElement, token);\n    p.activeFormattingElements.removeEntry(formattingElementEntry);\n    p.openElements.remove(formattingElementEntry.element);\n    p.openElements.insertAfter(furthestBlock, newElement, token.tagID);\n}\n//Algorithm entry point\nfunction callAdoptionAgency(p, token) {\n    for (let i = 0; i < AA_OUTER_LOOP_ITER; i++) {\n        const formattingElementEntry = aaObtainFormattingElementEntry(p, token);\n        if (!formattingElementEntry) {\n            break;\n        }\n        const furthestBlock = aaObtainFurthestBlock(p, formattingElementEntry);\n        if (!furthestBlock) {\n            break;\n        }\n        p.activeFormattingElements.bookmark = formattingElementEntry;\n        const lastElement = aaInnerLoop(p, furthestBlock, formattingElementEntry.element);\n        const commonAncestor = p.openElements.getCommonAncestor(formattingElementEntry.element);\n        p.treeAdapter.detachNode(lastElement);\n        if (commonAncestor)\n            aaInsertLastNodeInCommonAncestor(p, commonAncestor, lastElement);\n        aaReplaceFormattingElement(p, furthestBlock, formattingElementEntry);\n    }\n}\n//Generic token handlers\n//------------------------------------------------------------------\nfunction appendComment(p, token) {\n    p._appendCommentNode(token, p.openElements.currentTmplContentOrNode);\n}\nfunction appendCommentToRootHtmlElement(p, token) {\n    p._appendCommentNode(token, p.openElements.items[0]);\n}\nfunction appendCommentToDocument(p, token) {\n    p._appendCommentNode(token, p.document);\n}\nfunction stopParsing(p, token) {\n    p.stopped = true;\n    // NOTE: Set end locations for elements that remain on the open element stack.\n    if (token.location) {\n        // NOTE: If we are not in a fragment, `html` and `body` will stay on the stack.\n        // This is a problem, as we might overwrite their end position here.\n        const target = p.fragmentContext ? 0 : 2;\n        for (let i = p.openElements.stackTop; i >= target; i--) {\n            p._setEndLocation(p.openElements.items[i], token);\n        }\n        // Handle `html` and `body`\n        if (!p.fragmentContext && p.openElements.stackTop >= 0) {\n            const htmlElement = p.openElements.items[0];\n            const htmlLocation = p.treeAdapter.getNodeSourceCodeLocation(htmlElement);\n            if (htmlLocation && !htmlLocation.endTag) {\n                p._setEndLocation(htmlElement, token);\n                if (p.openElements.stackTop >= 1) {\n                    const bodyElement = p.openElements.items[1];\n                    const bodyLocation = p.treeAdapter.getNodeSourceCodeLocation(bodyElement);\n                    if (bodyLocation && !bodyLocation.endTag) {\n                        p._setEndLocation(bodyElement, token);\n                    }\n                }\n            }\n        }\n    }\n}\n// The \"initial\" insertion mode\n//------------------------------------------------------------------\nfunction doctypeInInitialMode(p, token) {\n    p._setDocumentType(token);\n    const mode = token.forceQuirks ? DOCUMENT_MODE.QUIRKS : doctype.getDocumentMode(token);\n    if (!doctype.isConforming(token)) {\n        p._err(token, ERR.nonConformingDoctype);\n    }\n    p.treeAdapter.setDocumentMode(p.document, mode);\n    p.insertionMode = InsertionMode.BEFORE_HTML;\n}\nfunction tokenInInitialMode(p, token) {\n    p._err(token, ERR.missingDoctype, true);\n    p.treeAdapter.setDocumentMode(p.document, DOCUMENT_MODE.QUIRKS);\n    p.insertionMode = InsertionMode.BEFORE_HTML;\n    p._processToken(token);\n}\n// The \"before html\" insertion mode\n//------------------------------------------------------------------\nfunction startTagBeforeHtml(p, token) {\n    if (token.tagID === $.HTML) {\n        p._insertElement(token, NS.HTML);\n        p.insertionMode = InsertionMode.BEFORE_HEAD;\n    }\n    else {\n        tokenBeforeHtml(p, token);\n    }\n}\nfunction endTagBeforeHtml(p, token) {\n    const tn = token.tagID;\n    if (tn === $.HTML || tn === $.HEAD || tn === $.BODY || tn === $.BR) {\n        tokenBeforeHtml(p, token);\n    }\n}\nfunction tokenBeforeHtml(p, token) {\n    p._insertFakeRootElement();\n    p.insertionMode = InsertionMode.BEFORE_HEAD;\n    p._processToken(token);\n}\n// The \"before head\" insertion mode\n//------------------------------------------------------------------\nfunction startTagBeforeHead(p, token) {\n    switch (token.tagID) {\n        case $.HTML: {\n            startTagInBody(p, token);\n            break;\n        }\n        case $.HEAD: {\n            p._insertElement(token, NS.HTML);\n            p.headElement = p.openElements.current;\n            p.insertionMode = InsertionMode.IN_HEAD;\n            break;\n        }\n        default: {\n            tokenBeforeHead(p, token);\n        }\n    }\n}\nfunction endTagBeforeHead(p, token) {\n    const tn = token.tagID;\n    if (tn === $.HEAD || tn === $.BODY || tn === $.HTML || tn === $.BR) {\n        tokenBeforeHead(p, token);\n    }\n    else {\n        p._err(token, ERR.endTagWithoutMatchingOpenElement);\n    }\n}\nfunction tokenBeforeHead(p, token) {\n    p._insertFakeElement(TN.HEAD, $.HEAD);\n    p.headElement = p.openElements.current;\n    p.insertionMode = InsertionMode.IN_HEAD;\n    p._processToken(token);\n}\n// The \"in head\" insertion mode\n//------------------------------------------------------------------\nfunction startTagInHead(p, token) {\n    switch (token.tagID) {\n        case $.HTML: {\n            startTagInBody(p, token);\n            break;\n        }\n        case $.BASE:\n        case $.BASEFONT:\n        case $.BGSOUND:\n        case $.LINK:\n        case $.META: {\n            p._appendElement(token, NS.HTML);\n            token.ackSelfClosing = true;\n            break;\n        }\n        case $.TITLE: {\n            p._switchToTextParsing(token, TokenizerMode.RCDATA);\n            break;\n        }\n        case $.NOSCRIPT: {\n            if (p.options.scriptingEnabled) {\n                p._switchToTextParsing(token, TokenizerMode.RAWTEXT);\n            }\n            else {\n                p._insertElement(token, NS.HTML);\n                p.insertionMode = InsertionMode.IN_HEAD_NO_SCRIPT;\n            }\n            break;\n        }\n        case $.NOFRAMES:\n        case $.STYLE: {\n            p._switchToTextParsing(token, TokenizerMode.RAWTEXT);\n            break;\n        }\n        case $.SCRIPT: {\n            p._switchToTextParsing(token, TokenizerMode.SCRIPT_DATA);\n            break;\n        }\n        case $.TEMPLATE: {\n            p._insertTemplate(token);\n            p.activeFormattingElements.insertMarker();\n            p.framesetOk = false;\n            p.insertionMode = InsertionMode.IN_TEMPLATE;\n            p.tmplInsertionModeStack.unshift(InsertionMode.IN_TEMPLATE);\n            break;\n        }\n        case $.HEAD: {\n            p._err(token, ERR.misplacedStartTagForHeadElement);\n            break;\n        }\n        default: {\n            tokenInHead(p, token);\n        }\n    }\n}\nfunction endTagInHead(p, token) {\n    switch (token.tagID) {\n        case $.HEAD: {\n            p.openElements.pop();\n            p.insertionMode = InsertionMode.AFTER_HEAD;\n            break;\n        }\n        case $.BODY:\n        case $.BR:\n        case $.HTML: {\n            tokenInHead(p, token);\n            break;\n        }\n        case $.TEMPLATE: {\n            templateEndTagInHead(p, token);\n            break;\n        }\n        default: {\n            p._err(token, ERR.endTagWithoutMatchingOpenElement);\n        }\n    }\n}\nfunction templateEndTagInHead(p, token) {\n    if (p.openElements.tmplCount > 0) {\n        p.openElements.generateImpliedEndTagsThoroughly();\n        if (p.openElements.currentTagId !== $.TEMPLATE) {\n            p._err(token, ERR.closingOfElementWithOpenChildElements);\n        }\n        p.openElements.popUntilTagNamePopped($.TEMPLATE);\n        p.activeFormattingElements.clearToLastMarker();\n        p.tmplInsertionModeStack.shift();\n        p._resetInsertionMode();\n    }\n    else {\n        p._err(token, ERR.endTagWithoutMatchingOpenElement);\n    }\n}\nfunction tokenInHead(p, token) {\n    p.openElements.pop();\n    p.insertionMode = InsertionMode.AFTER_HEAD;\n    p._processToken(token);\n}\n// The \"in head no script\" insertion mode\n//------------------------------------------------------------------\nfunction startTagInHeadNoScript(p, token) {\n    switch (token.tagID) {\n        case $.HTML: {\n            startTagInBody(p, token);\n            break;\n        }\n        case $.BASEFONT:\n        case $.BGSOUND:\n        case $.HEAD:\n        case $.LINK:\n        case $.META:\n        case $.NOFRAMES:\n        case $.STYLE: {\n            startTagInHead(p, token);\n            break;\n        }\n        case $.NOSCRIPT: {\n            p._err(token, ERR.nestedNoscriptInHead);\n            break;\n        }\n        default: {\n            tokenInHeadNoScript(p, token);\n        }\n    }\n}\nfunction endTagInHeadNoScript(p, token) {\n    switch (token.tagID) {\n        case $.NOSCRIPT: {\n            p.openElements.pop();\n            p.insertionMode = InsertionMode.IN_HEAD;\n            break;\n        }\n        case $.BR: {\n            tokenInHeadNoScript(p, token);\n            break;\n        }\n        default: {\n            p._err(token, ERR.endTagWithoutMatchingOpenElement);\n        }\n    }\n}\nfunction tokenInHeadNoScript(p, token) {\n    const errCode = token.type === TokenType.EOF ? ERR.openElementsLeftAfterEof : ERR.disallowedContentInNoscriptInHead;\n    p._err(token, errCode);\n    p.openElements.pop();\n    p.insertionMode = InsertionMode.IN_HEAD;\n    p._processToken(token);\n}\n// The \"after head\" insertion mode\n//------------------------------------------------------------------\nfunction startTagAfterHead(p, token) {\n    switch (token.tagID) {\n        case $.HTML: {\n            startTagInBody(p, token);\n            break;\n        }\n        case $.BODY: {\n            p._insertElement(token, NS.HTML);\n            p.framesetOk = false;\n            p.insertionMode = InsertionMode.IN_BODY;\n            break;\n        }\n        case $.FRAMESET: {\n            p._insertElement(token, NS.HTML);\n            p.insertionMode = InsertionMode.IN_FRAMESET;\n            break;\n        }\n        case $.BASE:\n        case $.BASEFONT:\n        case $.BGSOUND:\n        case $.LINK:\n        case $.META:\n        case $.NOFRAMES:\n        case $.SCRIPT:\n        case $.STYLE:\n        case $.TEMPLATE:\n        case $.TITLE: {\n            p._err(token, ERR.abandonedHeadElementChild);\n            p.openElements.push(p.headElement, $.HEAD);\n            startTagInHead(p, token);\n            p.openElements.remove(p.headElement);\n            break;\n        }\n        case $.HEAD: {\n            p._err(token, ERR.misplacedStartTagForHeadElement);\n            break;\n        }\n        default: {\n            tokenAfterHead(p, token);\n        }\n    }\n}\nfunction endTagAfterHead(p, token) {\n    switch (token.tagID) {\n        case $.BODY:\n        case $.HTML:\n        case $.BR: {\n            tokenAfterHead(p, token);\n            break;\n        }\n        case $.TEMPLATE: {\n            templateEndTagInHead(p, token);\n            break;\n        }\n        default: {\n            p._err(token, ERR.endTagWithoutMatchingOpenElement);\n        }\n    }\n}\nfunction tokenAfterHead(p, token) {\n    p._insertFakeElement(TN.BODY, $.BODY);\n    p.insertionMode = InsertionMode.IN_BODY;\n    modeInBody(p, token);\n}\n// The \"in body\" insertion mode\n//------------------------------------------------------------------\nfunction modeInBody(p, token) {\n    switch (token.type) {\n        case TokenType.CHARACTER: {\n            characterInBody(p, token);\n            break;\n        }\n        case TokenType.WHITESPACE_CHARACTER: {\n            whitespaceCharacterInBody(p, token);\n            break;\n        }\n        case TokenType.COMMENT: {\n            appendComment(p, token);\n            break;\n        }\n        case TokenType.START_TAG: {\n            startTagInBody(p, token);\n            break;\n        }\n        case TokenType.END_TAG: {\n            endTagInBody(p, token);\n            break;\n        }\n        case TokenType.EOF: {\n            eofInBody(p, token);\n            break;\n        }\n        default:\n        // Do nothing\n    }\n}\nfunction whitespaceCharacterInBody(p, token) {\n    p._reconstructActiveFormattingElements();\n    p._insertCharacters(token);\n}\nfunction characterInBody(p, token) {\n    p._reconstructActiveFormattingElements();\n    p._insertCharacters(token);\n    p.framesetOk = false;\n}\nfunction htmlStartTagInBody(p, token) {\n    if (p.openElements.tmplCount === 0) {\n        p.treeAdapter.adoptAttributes(p.openElements.items[0], token.attrs);\n    }\n}\nfunction bodyStartTagInBody(p, token) {\n    const bodyElement = p.openElements.tryPeekProperlyNestedBodyElement();\n    if (bodyElement && p.openElements.tmplCount === 0) {\n        p.framesetOk = false;\n        p.treeAdapter.adoptAttributes(bodyElement, token.attrs);\n    }\n}\nfunction framesetStartTagInBody(p, token) {\n    const bodyElement = p.openElements.tryPeekProperlyNestedBodyElement();\n    if (p.framesetOk && bodyElement) {\n        p.treeAdapter.detachNode(bodyElement);\n        p.openElements.popAllUpToHtmlElement();\n        p._insertElement(token, NS.HTML);\n        p.insertionMode = InsertionMode.IN_FRAMESET;\n    }\n}\nfunction addressStartTagInBody(p, token) {\n    if (p.openElements.hasInButtonScope($.P)) {\n        p._closePElement();\n    }\n    p._insertElement(token, NS.HTML);\n}\nfunction numberedHeaderStartTagInBody(p, token) {\n    if (p.openElements.hasInButtonScope($.P)) {\n        p._closePElement();\n    }\n    if (p.openElements.currentTagId !== undefined && NUMBERED_HEADERS.has(p.openElements.currentTagId)) {\n        p.openElements.pop();\n    }\n    p._insertElement(token, NS.HTML);\n}\nfunction preStartTagInBody(p, token) {\n    if (p.openElements.hasInButtonScope($.P)) {\n        p._closePElement();\n    }\n    p._insertElement(token, NS.HTML);\n    //NOTE: If the next token is a U+000A LINE FEED (LF) character token, then ignore that token and move\n    //on to the next one. (Newlines at the start of pre blocks are ignored as an authoring convenience.)\n    p.skipNextNewLine = true;\n    p.framesetOk = false;\n}\nfunction formStartTagInBody(p, token) {\n    const inTemplate = p.openElements.tmplCount > 0;\n    if (!p.formElement || inTemplate) {\n        if (p.openElements.hasInButtonScope($.P)) {\n            p._closePElement();\n        }\n        p._insertElement(token, NS.HTML);\n        if (!inTemplate) {\n            p.formElement = p.openElements.current;\n        }\n    }\n}\nfunction listItemStartTagInBody(p, token) {\n    p.framesetOk = false;\n    const tn = token.tagID;\n    for (let i = p.openElements.stackTop; i >= 0; i--) {\n        const elementId = p.openElements.tagIDs[i];\n        if ((tn === $.LI && elementId === $.LI) ||\n            ((tn === $.DD || tn === $.DT) && (elementId === $.DD || elementId === $.DT))) {\n            p.openElements.generateImpliedEndTagsWithExclusion(elementId);\n            p.openElements.popUntilTagNamePopped(elementId);\n            break;\n        }\n        if (elementId !== $.ADDRESS &&\n            elementId !== $.DIV &&\n            elementId !== $.P &&\n            p._isSpecialElement(p.openElements.items[i], elementId)) {\n            break;\n        }\n    }\n    if (p.openElements.hasInButtonScope($.P)) {\n        p._closePElement();\n    }\n    p._insertElement(token, NS.HTML);\n}\nfunction plaintextStartTagInBody(p, token) {\n    if (p.openElements.hasInButtonScope($.P)) {\n        p._closePElement();\n    }\n    p._insertElement(token, NS.HTML);\n    p.tokenizer.state = TokenizerMode.PLAINTEXT;\n}\nfunction buttonStartTagInBody(p, token) {\n    if (p.openElements.hasInScope($.BUTTON)) {\n        p.openElements.generateImpliedEndTags();\n        p.openElements.popUntilTagNamePopped($.BUTTON);\n    }\n    p._reconstructActiveFormattingElements();\n    p._insertElement(token, NS.HTML);\n    p.framesetOk = false;\n}\nfunction aStartTagInBody(p, token) {\n    const activeElementEntry = p.activeFormattingElements.getElementEntryInScopeWithTagName(TN.A);\n    if (activeElementEntry) {\n        callAdoptionAgency(p, token);\n        p.openElements.remove(activeElementEntry.element);\n        p.activeFormattingElements.removeEntry(activeElementEntry);\n    }\n    p._reconstructActiveFormattingElements();\n    p._insertElement(token, NS.HTML);\n    p.activeFormattingElements.pushElement(p.openElements.current, token);\n}\nfunction bStartTagInBody(p, token) {\n    p._reconstructActiveFormattingElements();\n    p._insertElement(token, NS.HTML);\n    p.activeFormattingElements.pushElement(p.openElements.current, token);\n}\nfunction nobrStartTagInBody(p, token) {\n    p._reconstructActiveFormattingElements();\n    if (p.openElements.hasInScope($.NOBR)) {\n        callAdoptionAgency(p, token);\n        p._reconstructActiveFormattingElements();\n    }\n    p._insertElement(token, NS.HTML);\n    p.activeFormattingElements.pushElement(p.openElements.current, token);\n}\nfunction appletStartTagInBody(p, token) {\n    p._reconstructActiveFormattingElements();\n    p._insertElement(token, NS.HTML);\n    p.activeFormattingElements.insertMarker();\n    p.framesetOk = false;\n}\nfunction tableStartTagInBody(p, token) {\n    if (p.treeAdapter.getDocumentMode(p.document) !== DOCUMENT_MODE.QUIRKS && p.openElements.hasInButtonScope($.P)) {\n        p._closePElement();\n    }\n    p._insertElement(token, NS.HTML);\n    p.framesetOk = false;\n    p.insertionMode = InsertionMode.IN_TABLE;\n}\nfunction areaStartTagInBody(p, token) {\n    p._reconstructActiveFormattingElements();\n    p._appendElement(token, NS.HTML);\n    p.framesetOk = false;\n    token.ackSelfClosing = true;\n}\nfunction isHiddenInput(token) {\n    const inputType = getTokenAttr(token, ATTRS.TYPE);\n    return inputType != null && inputType.toLowerCase() === HIDDEN_INPUT_TYPE;\n}\nfunction inputStartTagInBody(p, token) {\n    p._reconstructActiveFormattingElements();\n    p._appendElement(token, NS.HTML);\n    if (!isHiddenInput(token)) {\n        p.framesetOk = false;\n    }\n    token.ackSelfClosing = true;\n}\nfunction paramStartTagInBody(p, token) {\n    p._appendElement(token, NS.HTML);\n    token.ackSelfClosing = true;\n}\nfunction hrStartTagInBody(p, token) {\n    if (p.openElements.hasInButtonScope($.P)) {\n        p._closePElement();\n    }\n    p._appendElement(token, NS.HTML);\n    p.framesetOk = false;\n    token.ackSelfClosing = true;\n}\nfunction imageStartTagInBody(p, token) {\n    token.tagName = TN.IMG;\n    token.tagID = $.IMG;\n    areaStartTagInBody(p, token);\n}\nfunction textareaStartTagInBody(p, token) {\n    p._insertElement(token, NS.HTML);\n    //NOTE: If the next token is a U+000A LINE FEED (LF) character token, then ignore that token and move\n    //on to the next one. (Newlines at the start of textarea elements are ignored as an authoring convenience.)\n    p.skipNextNewLine = true;\n    p.tokenizer.state = TokenizerMode.RCDATA;\n    p.originalInsertionMode = p.insertionMode;\n    p.framesetOk = false;\n    p.insertionMode = InsertionMode.TEXT;\n}\nfunction xmpStartTagInBody(p, token) {\n    if (p.openElements.hasInButtonScope($.P)) {\n        p._closePElement();\n    }\n    p._reconstructActiveFormattingElements();\n    p.framesetOk = false;\n    p._switchToTextParsing(token, TokenizerMode.RAWTEXT);\n}\nfunction iframeStartTagInBody(p, token) {\n    p.framesetOk = false;\n    p._switchToTextParsing(token, TokenizerMode.RAWTEXT);\n}\n//NOTE: here we assume that we always act as a user agent with enabled plugins/frames, so we parse\n//<noembed>/<noframes> as rawtext.\nfunction rawTextStartTagInBody(p, token) {\n    p._switchToTextParsing(token, TokenizerMode.RAWTEXT);\n}\nfunction selectStartTagInBody(p, token) {\n    p._reconstructActiveFormattingElements();\n    p._insertElement(token, NS.HTML);\n    p.framesetOk = false;\n    p.insertionMode =\n        p.insertionMode === InsertionMode.IN_TABLE ||\n            p.insertionMode === InsertionMode.IN_CAPTION ||\n            p.insertionMode === InsertionMode.IN_TABLE_BODY ||\n            p.insertionMode === InsertionMode.IN_ROW ||\n            p.insertionMode === InsertionMode.IN_CELL\n            ? InsertionMode.IN_SELECT_IN_TABLE\n            : InsertionMode.IN_SELECT;\n}\nfunction optgroupStartTagInBody(p, token) {\n    if (p.openElements.currentTagId === $.OPTION) {\n        p.openElements.pop();\n    }\n    p._reconstructActiveFormattingElements();\n    p._insertElement(token, NS.HTML);\n}\nfunction rbStartTagInBody(p, token) {\n    if (p.openElements.hasInScope($.RUBY)) {\n        p.openElements.generateImpliedEndTags();\n    }\n    p._insertElement(token, NS.HTML);\n}\nfunction rtStartTagInBody(p, token) {\n    if (p.openElements.hasInScope($.RUBY)) {\n        p.openElements.generateImpliedEndTagsWithExclusion($.RTC);\n    }\n    p._insertElement(token, NS.HTML);\n}\nfunction mathStartTagInBody(p, token) {\n    p._reconstructActiveFormattingElements();\n    foreignContent.adjustTokenMathMLAttrs(token);\n    foreignContent.adjustTokenXMLAttrs(token);\n    if (token.selfClosing) {\n        p._appendElement(token, NS.MATHML);\n    }\n    else {\n        p._insertElement(token, NS.MATHML);\n    }\n    token.ackSelfClosing = true;\n}\nfunction svgStartTagInBody(p, token) {\n    p._reconstructActiveFormattingElements();\n    foreignContent.adjustTokenSVGAttrs(token);\n    foreignContent.adjustTokenXMLAttrs(token);\n    if (token.selfClosing) {\n        p._appendElement(token, NS.SVG);\n    }\n    else {\n        p._insertElement(token, NS.SVG);\n    }\n    token.ackSelfClosing = true;\n}\nfunction genericStartTagInBody(p, token) {\n    p._reconstructActiveFormattingElements();\n    p._insertElement(token, NS.HTML);\n}\nfunction startTagInBody(p, token) {\n    switch (token.tagID) {\n        case $.I:\n        case $.S:\n        case $.B:\n        case $.U:\n        case $.EM:\n        case $.TT:\n        case $.BIG:\n        case $.CODE:\n        case $.FONT:\n        case $.SMALL:\n        case $.STRIKE:\n        case $.STRONG: {\n            bStartTagInBody(p, token);\n            break;\n        }\n        case $.A: {\n            aStartTagInBody(p, token);\n            break;\n        }\n        case $.H1:\n        case $.H2:\n        case $.H3:\n        case $.H4:\n        case $.H5:\n        case $.H6: {\n            numberedHeaderStartTagInBody(p, token);\n            break;\n        }\n        case $.P:\n        case $.DL:\n        case $.OL:\n        case $.UL:\n        case $.DIV:\n        case $.DIR:\n        case $.NAV:\n        case $.MAIN:\n        case $.MENU:\n        case $.ASIDE:\n        case $.CENTER:\n        case $.FIGURE:\n        case $.FOOTER:\n        case $.HEADER:\n        case $.HGROUP:\n        case $.DIALOG:\n        case $.DETAILS:\n        case $.ADDRESS:\n        case $.ARTICLE:\n        case $.SEARCH:\n        case $.SECTION:\n        case $.SUMMARY:\n        case $.FIELDSET:\n        case $.BLOCKQUOTE:\n        case $.FIGCAPTION: {\n            addressStartTagInBody(p, token);\n            break;\n        }\n        case $.LI:\n        case $.DD:\n        case $.DT: {\n            listItemStartTagInBody(p, token);\n            break;\n        }\n        case $.BR:\n        case $.IMG:\n        case $.WBR:\n        case $.AREA:\n        case $.EMBED:\n        case $.KEYGEN: {\n            areaStartTagInBody(p, token);\n            break;\n        }\n        case $.HR: {\n            hrStartTagInBody(p, token);\n            break;\n        }\n        case $.RB:\n        case $.RTC: {\n            rbStartTagInBody(p, token);\n            break;\n        }\n        case $.RT:\n        case $.RP: {\n            rtStartTagInBody(p, token);\n            break;\n        }\n        case $.PRE:\n        case $.LISTING: {\n            preStartTagInBody(p, token);\n            break;\n        }\n        case $.XMP: {\n            xmpStartTagInBody(p, token);\n            break;\n        }\n        case $.SVG: {\n            svgStartTagInBody(p, token);\n            break;\n        }\n        case $.HTML: {\n            htmlStartTagInBody(p, token);\n            break;\n        }\n        case $.BASE:\n        case $.LINK:\n        case $.META:\n        case $.STYLE:\n        case $.TITLE:\n        case $.SCRIPT:\n        case $.BGSOUND:\n        case $.BASEFONT:\n        case $.TEMPLATE: {\n            startTagInHead(p, token);\n            break;\n        }\n        case $.BODY: {\n            bodyStartTagInBody(p, token);\n            break;\n        }\n        case $.FORM: {\n            formStartTagInBody(p, token);\n            break;\n        }\n        case $.NOBR: {\n            nobrStartTagInBody(p, token);\n            break;\n        }\n        case $.MATH: {\n            mathStartTagInBody(p, token);\n            break;\n        }\n        case $.TABLE: {\n            tableStartTagInBody(p, token);\n            break;\n        }\n        case $.INPUT: {\n            inputStartTagInBody(p, token);\n            break;\n        }\n        case $.PARAM:\n        case $.TRACK:\n        case $.SOURCE: {\n            paramStartTagInBody(p, token);\n            break;\n        }\n        case $.IMAGE: {\n            imageStartTagInBody(p, token);\n            break;\n        }\n        case $.BUTTON: {\n            buttonStartTagInBody(p, token);\n            break;\n        }\n        case $.APPLET:\n        case $.OBJECT:\n        case $.MARQUEE: {\n            appletStartTagInBody(p, token);\n            break;\n        }\n        case $.IFRAME: {\n            iframeStartTagInBody(p, token);\n            break;\n        }\n        case $.SELECT: {\n            selectStartTagInBody(p, token);\n            break;\n        }\n        case $.OPTION:\n        case $.OPTGROUP: {\n            optgroupStartTagInBody(p, token);\n            break;\n        }\n        case $.NOEMBED:\n        case $.NOFRAMES: {\n            rawTextStartTagInBody(p, token);\n            break;\n        }\n        case $.FRAMESET: {\n            framesetStartTagInBody(p, token);\n            break;\n        }\n        case $.TEXTAREA: {\n            textareaStartTagInBody(p, token);\n            break;\n        }\n        case $.NOSCRIPT: {\n            if (p.options.scriptingEnabled) {\n                rawTextStartTagInBody(p, token);\n            }\n            else {\n                genericStartTagInBody(p, token);\n            }\n            break;\n        }\n        case $.PLAINTEXT: {\n            plaintextStartTagInBody(p, token);\n            break;\n        }\n        case $.COL:\n        case $.TH:\n        case $.TD:\n        case $.TR:\n        case $.HEAD:\n        case $.FRAME:\n        case $.TBODY:\n        case $.TFOOT:\n        case $.THEAD:\n        case $.CAPTION:\n        case $.COLGROUP: {\n            // Ignore token\n            break;\n        }\n        default: {\n            genericStartTagInBody(p, token);\n        }\n    }\n}\nfunction bodyEndTagInBody(p, token) {\n    if (p.openElements.hasInScope($.BODY)) {\n        p.insertionMode = InsertionMode.AFTER_BODY;\n        //NOTE: <body> is never popped from the stack, so we need to updated\n        //the end location explicitly.\n        if (p.options.sourceCodeLocationInfo) {\n            const bodyElement = p.openElements.tryPeekProperlyNestedBodyElement();\n            if (bodyElement) {\n                p._setEndLocation(bodyElement, token);\n            }\n        }\n    }\n}\nfunction htmlEndTagInBody(p, token) {\n    if (p.openElements.hasInScope($.BODY)) {\n        p.insertionMode = InsertionMode.AFTER_BODY;\n        endTagAfterBody(p, token);\n    }\n}\nfunction addressEndTagInBody(p, token) {\n    const tn = token.tagID;\n    if (p.openElements.hasInScope(tn)) {\n        p.openElements.generateImpliedEndTags();\n        p.openElements.popUntilTagNamePopped(tn);\n    }\n}\nfunction formEndTagInBody(p) {\n    const inTemplate = p.openElements.tmplCount > 0;\n    const { formElement } = p;\n    if (!inTemplate) {\n        p.formElement = null;\n    }\n    if ((formElement || inTemplate) && p.openElements.hasInScope($.FORM)) {\n        p.openElements.generateImpliedEndTags();\n        if (inTemplate) {\n            p.openElements.popUntilTagNamePopped($.FORM);\n        }\n        else if (formElement) {\n            p.openElements.remove(formElement);\n        }\n    }\n}\nfunction pEndTagInBody(p) {\n    if (!p.openElements.hasInButtonScope($.P)) {\n        p._insertFakeElement(TN.P, $.P);\n    }\n    p._closePElement();\n}\nfunction liEndTagInBody(p) {\n    if (p.openElements.hasInListItemScope($.LI)) {\n        p.openElements.generateImpliedEndTagsWithExclusion($.LI);\n        p.openElements.popUntilTagNamePopped($.LI);\n    }\n}\nfunction ddEndTagInBody(p, token) {\n    const tn = token.tagID;\n    if (p.openElements.hasInScope(tn)) {\n        p.openElements.generateImpliedEndTagsWithExclusion(tn);\n        p.openElements.popUntilTagNamePopped(tn);\n    }\n}\nfunction numberedHeaderEndTagInBody(p) {\n    if (p.openElements.hasNumberedHeaderInScope()) {\n        p.openElements.generateImpliedEndTags();\n        p.openElements.popUntilNumberedHeaderPopped();\n    }\n}\nfunction appletEndTagInBody(p, token) {\n    const tn = token.tagID;\n    if (p.openElements.hasInScope(tn)) {\n        p.openElements.generateImpliedEndTags();\n        p.openElements.popUntilTagNamePopped(tn);\n        p.activeFormattingElements.clearToLastMarker();\n    }\n}\nfunction brEndTagInBody(p) {\n    p._reconstructActiveFormattingElements();\n    p._insertFakeElement(TN.BR, $.BR);\n    p.openElements.pop();\n    p.framesetOk = false;\n}\nfunction genericEndTagInBody(p, token) {\n    const tn = token.tagName;\n    const tid = token.tagID;\n    for (let i = p.openElements.stackTop; i > 0; i--) {\n        const element = p.openElements.items[i];\n        const elementId = p.openElements.tagIDs[i];\n        // Compare the tag name here, as the tag might not be a known tag with an ID.\n        if (tid === elementId && (tid !== $.UNKNOWN || p.treeAdapter.getTagName(element) === tn)) {\n            p.openElements.generateImpliedEndTagsWithExclusion(tid);\n            if (p.openElements.stackTop >= i)\n                p.openElements.shortenToLength(i);\n            break;\n        }\n        if (p._isSpecialElement(element, elementId)) {\n            break;\n        }\n    }\n}\nfunction endTagInBody(p, token) {\n    switch (token.tagID) {\n        case $.A:\n        case $.B:\n        case $.I:\n        case $.S:\n        case $.U:\n        case $.EM:\n        case $.TT:\n        case $.BIG:\n        case $.CODE:\n        case $.FONT:\n        case $.NOBR:\n        case $.SMALL:\n        case $.STRIKE:\n        case $.STRONG: {\n            callAdoptionAgency(p, token);\n            break;\n        }\n        case $.P: {\n            pEndTagInBody(p);\n            break;\n        }\n        case $.DL:\n        case $.UL:\n        case $.OL:\n        case $.DIR:\n        case $.DIV:\n        case $.NAV:\n        case $.PRE:\n        case $.MAIN:\n        case $.MENU:\n        case $.ASIDE:\n        case $.BUTTON:\n        case $.CENTER:\n        case $.FIGURE:\n        case $.FOOTER:\n        case $.HEADER:\n        case $.HGROUP:\n        case $.DIALOG:\n        case $.ADDRESS:\n        case $.ARTICLE:\n        case $.DETAILS:\n        case $.SEARCH:\n        case $.SECTION:\n        case $.SUMMARY:\n        case $.LISTING:\n        case $.FIELDSET:\n        case $.BLOCKQUOTE:\n        case $.FIGCAPTION: {\n            addressEndTagInBody(p, token);\n            break;\n        }\n        case $.LI: {\n            liEndTagInBody(p);\n            break;\n        }\n        case $.DD:\n        case $.DT: {\n            ddEndTagInBody(p, token);\n            break;\n        }\n        case $.H1:\n        case $.H2:\n        case $.H3:\n        case $.H4:\n        case $.H5:\n        case $.H6: {\n            numberedHeaderEndTagInBody(p);\n            break;\n        }\n        case $.BR: {\n            brEndTagInBody(p);\n            break;\n        }\n        case $.BODY: {\n            bodyEndTagInBody(p, token);\n            break;\n        }\n        case $.HTML: {\n            htmlEndTagInBody(p, token);\n            break;\n        }\n        case $.FORM: {\n            formEndTagInBody(p);\n            break;\n        }\n        case $.APPLET:\n        case $.OBJECT:\n        case $.MARQUEE: {\n            appletEndTagInBody(p, token);\n            break;\n        }\n        case $.TEMPLATE: {\n            templateEndTagInHead(p, token);\n            break;\n        }\n        default: {\n            genericEndTagInBody(p, token);\n        }\n    }\n}\nfunction eofInBody(p, token) {\n    if (p.tmplInsertionModeStack.length > 0) {\n        eofInTemplate(p, token);\n    }\n    else {\n        stopParsing(p, token);\n    }\n}\n// The \"text\" insertion mode\n//------------------------------------------------------------------\nfunction endTagInText(p, token) {\n    var _a;\n    if (token.tagID === $.SCRIPT) {\n        (_a = p.scriptHandler) === null || _a === void 0 ? void 0 : _a.call(p, p.openElements.current);\n    }\n    p.openElements.pop();\n    p.insertionMode = p.originalInsertionMode;\n}\nfunction eofInText(p, token) {\n    p._err(token, ERR.eofInElementThatCanContainOnlyText);\n    p.openElements.pop();\n    p.insertionMode = p.originalInsertionMode;\n    p.onEof(token);\n}\n// The \"in table\" insertion mode\n//------------------------------------------------------------------\nfunction characterInTable(p, token) {\n    if (p.openElements.currentTagId !== undefined && TABLE_STRUCTURE_TAGS.has(p.openElements.currentTagId)) {\n        p.pendingCharacterTokens.length = 0;\n        p.hasNonWhitespacePendingCharacterToken = false;\n        p.originalInsertionMode = p.insertionMode;\n        p.insertionMode = InsertionMode.IN_TABLE_TEXT;\n        switch (token.type) {\n            case TokenType.CHARACTER: {\n                characterInTableText(p, token);\n                break;\n            }\n            case TokenType.WHITESPACE_CHARACTER: {\n                whitespaceCharacterInTableText(p, token);\n                break;\n            }\n            // Ignore null\n        }\n    }\n    else {\n        tokenInTable(p, token);\n    }\n}\nfunction captionStartTagInTable(p, token) {\n    p.openElements.clearBackToTableContext();\n    p.activeFormattingElements.insertMarker();\n    p._insertElement(token, NS.HTML);\n    p.insertionMode = InsertionMode.IN_CAPTION;\n}\nfunction colgroupStartTagInTable(p, token) {\n    p.openElements.clearBackToTableContext();\n    p._insertElement(token, NS.HTML);\n    p.insertionMode = InsertionMode.IN_COLUMN_GROUP;\n}\nfunction colStartTagInTable(p, token) {\n    p.openElements.clearBackToTableContext();\n    p._insertFakeElement(TN.COLGROUP, $.COLGROUP);\n    p.insertionMode = InsertionMode.IN_COLUMN_GROUP;\n    startTagInColumnGroup(p, token);\n}\nfunction tbodyStartTagInTable(p, token) {\n    p.openElements.clearBackToTableContext();\n    p._insertElement(token, NS.HTML);\n    p.insertionMode = InsertionMode.IN_TABLE_BODY;\n}\nfunction tdStartTagInTable(p, token) {\n    p.openElements.clearBackToTableContext();\n    p._insertFakeElement(TN.TBODY, $.TBODY);\n    p.insertionMode = InsertionMode.IN_TABLE_BODY;\n    startTagInTableBody(p, token);\n}\nfunction tableStartTagInTable(p, token) {\n    if (p.openElements.hasInTableScope($.TABLE)) {\n        p.openElements.popUntilTagNamePopped($.TABLE);\n        p._resetInsertionMode();\n        p._processStartTag(token);\n    }\n}\nfunction inputStartTagInTable(p, token) {\n    if (isHiddenInput(token)) {\n        p._appendElement(token, NS.HTML);\n    }\n    else {\n        tokenInTable(p, token);\n    }\n    token.ackSelfClosing = true;\n}\nfunction formStartTagInTable(p, token) {\n    if (!p.formElement && p.openElements.tmplCount === 0) {\n        p._insertElement(token, NS.HTML);\n        p.formElement = p.openElements.current;\n        p.openElements.pop();\n    }\n}\nfunction startTagInTable(p, token) {\n    switch (token.tagID) {\n        case $.TD:\n        case $.TH:\n        case $.TR: {\n            tdStartTagInTable(p, token);\n            break;\n        }\n        case $.STYLE:\n        case $.SCRIPT:\n        case $.TEMPLATE: {\n            startTagInHead(p, token);\n            break;\n        }\n        case $.COL: {\n            colStartTagInTable(p, token);\n            break;\n        }\n        case $.FORM: {\n            formStartTagInTable(p, token);\n            break;\n        }\n        case $.TABLE: {\n            tableStartTagInTable(p, token);\n            break;\n        }\n        case $.TBODY:\n        case $.TFOOT:\n        case $.THEAD: {\n            tbodyStartTagInTable(p, token);\n            break;\n        }\n        case $.INPUT: {\n            inputStartTagInTable(p, token);\n            break;\n        }\n        case $.CAPTION: {\n            captionStartTagInTable(p, token);\n            break;\n        }\n        case $.COLGROUP: {\n            colgroupStartTagInTable(p, token);\n            break;\n        }\n        default: {\n            tokenInTable(p, token);\n        }\n    }\n}\nfunction endTagInTable(p, token) {\n    switch (token.tagID) {\n        case $.TABLE: {\n            if (p.openElements.hasInTableScope($.TABLE)) {\n                p.openElements.popUntilTagNamePopped($.TABLE);\n                p._resetInsertionMode();\n            }\n            break;\n        }\n        case $.TEMPLATE: {\n            templateEndTagInHead(p, token);\n            break;\n        }\n        case $.BODY:\n        case $.CAPTION:\n        case $.COL:\n        case $.COLGROUP:\n        case $.HTML:\n        case $.TBODY:\n        case $.TD:\n        case $.TFOOT:\n        case $.TH:\n        case $.THEAD:\n        case $.TR: {\n            // Ignore token\n            break;\n        }\n        default: {\n            tokenInTable(p, token);\n        }\n    }\n}\nfunction tokenInTable(p, token) {\n    const savedFosterParentingState = p.fosterParentingEnabled;\n    p.fosterParentingEnabled = true;\n    // Process token in `In Body` mode\n    modeInBody(p, token);\n    p.fosterParentingEnabled = savedFosterParentingState;\n}\n// The \"in table text\" insertion mode\n//------------------------------------------------------------------\nfunction whitespaceCharacterInTableText(p, token) {\n    p.pendingCharacterTokens.push(token);\n}\nfunction characterInTableText(p, token) {\n    p.pendingCharacterTokens.push(token);\n    p.hasNonWhitespacePendingCharacterToken = true;\n}\nfunction tokenInTableText(p, token) {\n    let i = 0;\n    if (p.hasNonWhitespacePendingCharacterToken) {\n        for (; i < p.pendingCharacterTokens.length; i++) {\n            tokenInTable(p, p.pendingCharacterTokens[i]);\n        }\n    }\n    else {\n        for (; i < p.pendingCharacterTokens.length; i++) {\n            p._insertCharacters(p.pendingCharacterTokens[i]);\n        }\n    }\n    p.insertionMode = p.originalInsertionMode;\n    p._processToken(token);\n}\n// The \"in caption\" insertion mode\n//------------------------------------------------------------------\nconst TABLE_VOID_ELEMENTS = new Set([$.CAPTION, $.COL, $.COLGROUP, $.TBODY, $.TD, $.TFOOT, $.TH, $.THEAD, $.TR]);\nfunction startTagInCaption(p, token) {\n    const tn = token.tagID;\n    if (TABLE_VOID_ELEMENTS.has(tn)) {\n        if (p.openElements.hasInTableScope($.CAPTION)) {\n            p.openElements.generateImpliedEndTags();\n            p.openElements.popUntilTagNamePopped($.CAPTION);\n            p.activeFormattingElements.clearToLastMarker();\n            p.insertionMode = InsertionMode.IN_TABLE;\n            startTagInTable(p, token);\n        }\n    }\n    else {\n        startTagInBody(p, token);\n    }\n}\nfunction endTagInCaption(p, token) {\n    const tn = token.tagID;\n    switch (tn) {\n        case $.CAPTION:\n        case $.TABLE: {\n            if (p.openElements.hasInTableScope($.CAPTION)) {\n                p.openElements.generateImpliedEndTags();\n                p.openElements.popUntilTagNamePopped($.CAPTION);\n                p.activeFormattingElements.clearToLastMarker();\n                p.insertionMode = InsertionMode.IN_TABLE;\n                if (tn === $.TABLE) {\n                    endTagInTable(p, token);\n                }\n            }\n            break;\n        }\n        case $.BODY:\n        case $.COL:\n        case $.COLGROUP:\n        case $.HTML:\n        case $.TBODY:\n        case $.TD:\n        case $.TFOOT:\n        case $.TH:\n        case $.THEAD:\n        case $.TR: {\n            // Ignore token\n            break;\n        }\n        default: {\n            endTagInBody(p, token);\n        }\n    }\n}\n// The \"in column group\" insertion mode\n//------------------------------------------------------------------\nfunction startTagInColumnGroup(p, token) {\n    switch (token.tagID) {\n        case $.HTML: {\n            startTagInBody(p, token);\n            break;\n        }\n        case $.COL: {\n            p._appendElement(token, NS.HTML);\n            token.ackSelfClosing = true;\n            break;\n        }\n        case $.TEMPLATE: {\n            startTagInHead(p, token);\n            break;\n        }\n        default: {\n            tokenInColumnGroup(p, token);\n        }\n    }\n}\nfunction endTagInColumnGroup(p, token) {\n    switch (token.tagID) {\n        case $.COLGROUP: {\n            if (p.openElements.currentTagId === $.COLGROUP) {\n                p.openElements.pop();\n                p.insertionMode = InsertionMode.IN_TABLE;\n            }\n            break;\n        }\n        case $.TEMPLATE: {\n            templateEndTagInHead(p, token);\n            break;\n        }\n        case $.COL: {\n            // Ignore token\n            break;\n        }\n        default: {\n            tokenInColumnGroup(p, token);\n        }\n    }\n}\nfunction tokenInColumnGroup(p, token) {\n    if (p.openElements.currentTagId === $.COLGROUP) {\n        p.openElements.pop();\n        p.insertionMode = InsertionMode.IN_TABLE;\n        p._processToken(token);\n    }\n}\n// The \"in table body\" insertion mode\n//------------------------------------------------------------------\nfunction startTagInTableBody(p, token) {\n    switch (token.tagID) {\n        case $.TR: {\n            p.openElements.clearBackToTableBodyContext();\n            p._insertElement(token, NS.HTML);\n            p.insertionMode = InsertionMode.IN_ROW;\n            break;\n        }\n        case $.TH:\n        case $.TD: {\n            p.openElements.clearBackToTableBodyContext();\n            p._insertFakeElement(TN.TR, $.TR);\n            p.insertionMode = InsertionMode.IN_ROW;\n            startTagInRow(p, token);\n            break;\n        }\n        case $.CAPTION:\n        case $.COL:\n        case $.COLGROUP:\n        case $.TBODY:\n        case $.TFOOT:\n        case $.THEAD: {\n            if (p.openElements.hasTableBodyContextInTableScope()) {\n                p.openElements.clearBackToTableBodyContext();\n                p.openElements.pop();\n                p.insertionMode = InsertionMode.IN_TABLE;\n                startTagInTable(p, token);\n            }\n            break;\n        }\n        default: {\n            startTagInTable(p, token);\n        }\n    }\n}\nfunction endTagInTableBody(p, token) {\n    const tn = token.tagID;\n    switch (token.tagID) {\n        case $.TBODY:\n        case $.TFOOT:\n        case $.THEAD: {\n            if (p.openElements.hasInTableScope(tn)) {\n                p.openElements.clearBackToTableBodyContext();\n                p.openElements.pop();\n                p.insertionMode = InsertionMode.IN_TABLE;\n            }\n            break;\n        }\n        case $.TABLE: {\n            if (p.openElements.hasTableBodyContextInTableScope()) {\n                p.openElements.clearBackToTableBodyContext();\n                p.openElements.pop();\n                p.insertionMode = InsertionMode.IN_TABLE;\n                endTagInTable(p, token);\n            }\n            break;\n        }\n        case $.BODY:\n        case $.CAPTION:\n        case $.COL:\n        case $.COLGROUP:\n        case $.HTML:\n        case $.TD:\n        case $.TH:\n        case $.TR: {\n            // Ignore token\n            break;\n        }\n        default: {\n            endTagInTable(p, token);\n        }\n    }\n}\n// The \"in row\" insertion mode\n//------------------------------------------------------------------\nfunction startTagInRow(p, token) {\n    switch (token.tagID) {\n        case $.TH:\n        case $.TD: {\n            p.openElements.clearBackToTableRowContext();\n            p._insertElement(token, NS.HTML);\n            p.insertionMode = InsertionMode.IN_CELL;\n            p.activeFormattingElements.insertMarker();\n            break;\n        }\n        case $.CAPTION:\n        case $.COL:\n        case $.COLGROUP:\n        case $.TBODY:\n        case $.TFOOT:\n        case $.THEAD:\n        case $.TR: {\n            if (p.openElements.hasInTableScope($.TR)) {\n                p.openElements.clearBackToTableRowContext();\n                p.openElements.pop();\n                p.insertionMode = InsertionMode.IN_TABLE_BODY;\n                startTagInTableBody(p, token);\n            }\n            break;\n        }\n        default: {\n            startTagInTable(p, token);\n        }\n    }\n}\nfunction endTagInRow(p, token) {\n    switch (token.tagID) {\n        case $.TR: {\n            if (p.openElements.hasInTableScope($.TR)) {\n                p.openElements.clearBackToTableRowContext();\n                p.openElements.pop();\n                p.insertionMode = InsertionMode.IN_TABLE_BODY;\n            }\n            break;\n        }\n        case $.TABLE: {\n            if (p.openElements.hasInTableScope($.TR)) {\n                p.openElements.clearBackToTableRowContext();\n                p.openElements.pop();\n                p.insertionMode = InsertionMode.IN_TABLE_BODY;\n                endTagInTableBody(p, token);\n            }\n            break;\n        }\n        case $.TBODY:\n        case $.TFOOT:\n        case $.THEAD: {\n            if (p.openElements.hasInTableScope(token.tagID) || p.openElements.hasInTableScope($.TR)) {\n                p.openElements.clearBackToTableRowContext();\n                p.openElements.pop();\n                p.insertionMode = InsertionMode.IN_TABLE_BODY;\n                endTagInTableBody(p, token);\n            }\n            break;\n        }\n        case $.BODY:\n        case $.CAPTION:\n        case $.COL:\n        case $.COLGROUP:\n        case $.HTML:\n        case $.TD:\n        case $.TH: {\n            // Ignore end tag\n            break;\n        }\n        default: {\n            endTagInTable(p, token);\n        }\n    }\n}\n// The \"in cell\" insertion mode\n//------------------------------------------------------------------\nfunction startTagInCell(p, token) {\n    const tn = token.tagID;\n    if (TABLE_VOID_ELEMENTS.has(tn)) {\n        if (p.openElements.hasInTableScope($.TD) || p.openElements.hasInTableScope($.TH)) {\n            p._closeTableCell();\n            startTagInRow(p, token);\n        }\n    }\n    else {\n        startTagInBody(p, token);\n    }\n}\nfunction endTagInCell(p, token) {\n    const tn = token.tagID;\n    switch (tn) {\n        case $.TD:\n        case $.TH: {\n            if (p.openElements.hasInTableScope(tn)) {\n                p.openElements.generateImpliedEndTags();\n                p.openElements.popUntilTagNamePopped(tn);\n                p.activeFormattingElements.clearToLastMarker();\n                p.insertionMode = InsertionMode.IN_ROW;\n            }\n            break;\n        }\n        case $.TABLE:\n        case $.TBODY:\n        case $.TFOOT:\n        case $.THEAD:\n        case $.TR: {\n            if (p.openElements.hasInTableScope(tn)) {\n                p._closeTableCell();\n                endTagInRow(p, token);\n            }\n            break;\n        }\n        case $.BODY:\n        case $.CAPTION:\n        case $.COL:\n        case $.COLGROUP:\n        case $.HTML: {\n            // Ignore token\n            break;\n        }\n        default: {\n            endTagInBody(p, token);\n        }\n    }\n}\n// The \"in select\" insertion mode\n//------------------------------------------------------------------\nfunction startTagInSelect(p, token) {\n    switch (token.tagID) {\n        case $.HTML: {\n            startTagInBody(p, token);\n            break;\n        }\n        case $.OPTION: {\n            if (p.openElements.currentTagId === $.OPTION) {\n                p.openElements.pop();\n            }\n            p._insertElement(token, NS.HTML);\n            break;\n        }\n        case $.OPTGROUP: {\n            if (p.openElements.currentTagId === $.OPTION) {\n                p.openElements.pop();\n            }\n            if (p.openElements.currentTagId === $.OPTGROUP) {\n                p.openElements.pop();\n            }\n            p._insertElement(token, NS.HTML);\n            break;\n        }\n        case $.HR: {\n            if (p.openElements.currentTagId === $.OPTION) {\n                p.openElements.pop();\n            }\n            if (p.openElements.currentTagId === $.OPTGROUP) {\n                p.openElements.pop();\n            }\n            p._appendElement(token, NS.HTML);\n            token.ackSelfClosing = true;\n            break;\n        }\n        case $.INPUT:\n        case $.KEYGEN:\n        case $.TEXTAREA:\n        case $.SELECT: {\n            if (p.openElements.hasInSelectScope($.SELECT)) {\n                p.openElements.popUntilTagNamePopped($.SELECT);\n                p._resetInsertionMode();\n                if (token.tagID !== $.SELECT) {\n                    p._processStartTag(token);\n                }\n            }\n            break;\n        }\n        case $.SCRIPT:\n        case $.TEMPLATE: {\n            startTagInHead(p, token);\n            break;\n        }\n        default:\n        // Do nothing\n    }\n}\nfunction endTagInSelect(p, token) {\n    switch (token.tagID) {\n        case $.OPTGROUP: {\n            if (p.openElements.stackTop > 0 &&\n                p.openElements.currentTagId === $.OPTION &&\n                p.openElements.tagIDs[p.openElements.stackTop - 1] === $.OPTGROUP) {\n                p.openElements.pop();\n            }\n            if (p.openElements.currentTagId === $.OPTGROUP) {\n                p.openElements.pop();\n            }\n            break;\n        }\n        case $.OPTION: {\n            if (p.openElements.currentTagId === $.OPTION) {\n                p.openElements.pop();\n            }\n            break;\n        }\n        case $.SELECT: {\n            if (p.openElements.hasInSelectScope($.SELECT)) {\n                p.openElements.popUntilTagNamePopped($.SELECT);\n                p._resetInsertionMode();\n            }\n            break;\n        }\n        case $.TEMPLATE: {\n            templateEndTagInHead(p, token);\n            break;\n        }\n        default:\n        // Do nothing\n    }\n}\n// The \"in select in table\" insertion mode\n//------------------------------------------------------------------\nfunction startTagInSelectInTable(p, token) {\n    const tn = token.tagID;\n    if (tn === $.CAPTION ||\n        tn === $.TABLE ||\n        tn === $.TBODY ||\n        tn === $.TFOOT ||\n        tn === $.THEAD ||\n        tn === $.TR ||\n        tn === $.TD ||\n        tn === $.TH) {\n        p.openElements.popUntilTagNamePopped($.SELECT);\n        p._resetInsertionMode();\n        p._processStartTag(token);\n    }\n    else {\n        startTagInSelect(p, token);\n    }\n}\nfunction endTagInSelectInTable(p, token) {\n    const tn = token.tagID;\n    if (tn === $.CAPTION ||\n        tn === $.TABLE ||\n        tn === $.TBODY ||\n        tn === $.TFOOT ||\n        tn === $.THEAD ||\n        tn === $.TR ||\n        tn === $.TD ||\n        tn === $.TH) {\n        if (p.openElements.hasInTableScope(tn)) {\n            p.openElements.popUntilTagNamePopped($.SELECT);\n            p._resetInsertionMode();\n            p.onEndTag(token);\n        }\n    }\n    else {\n        endTagInSelect(p, token);\n    }\n}\n// The \"in template\" insertion mode\n//------------------------------------------------------------------\nfunction startTagInTemplate(p, token) {\n    switch (token.tagID) {\n        // First, handle tags that can start without a mode change\n        case $.BASE:\n        case $.BASEFONT:\n        case $.BGSOUND:\n        case $.LINK:\n        case $.META:\n        case $.NOFRAMES:\n        case $.SCRIPT:\n        case $.STYLE:\n        case $.TEMPLATE:\n        case $.TITLE: {\n            startTagInHead(p, token);\n            break;\n        }\n        // Re-process the token in the appropriate mode\n        case $.CAPTION:\n        case $.COLGROUP:\n        case $.TBODY:\n        case $.TFOOT:\n        case $.THEAD: {\n            p.tmplInsertionModeStack[0] = InsertionMode.IN_TABLE;\n            p.insertionMode = InsertionMode.IN_TABLE;\n            startTagInTable(p, token);\n            break;\n        }\n        case $.COL: {\n            p.tmplInsertionModeStack[0] = InsertionMode.IN_COLUMN_GROUP;\n            p.insertionMode = InsertionMode.IN_COLUMN_GROUP;\n            startTagInColumnGroup(p, token);\n            break;\n        }\n        case $.TR: {\n            p.tmplInsertionModeStack[0] = InsertionMode.IN_TABLE_BODY;\n            p.insertionMode = InsertionMode.IN_TABLE_BODY;\n            startTagInTableBody(p, token);\n            break;\n        }\n        case $.TD:\n        case $.TH: {\n            p.tmplInsertionModeStack[0] = InsertionMode.IN_ROW;\n            p.insertionMode = InsertionMode.IN_ROW;\n            startTagInRow(p, token);\n            break;\n        }\n        default: {\n            p.tmplInsertionModeStack[0] = InsertionMode.IN_BODY;\n            p.insertionMode = InsertionMode.IN_BODY;\n            startTagInBody(p, token);\n        }\n    }\n}\nfunction endTagInTemplate(p, token) {\n    if (token.tagID === $.TEMPLATE) {\n        templateEndTagInHead(p, token);\n    }\n}\nfunction eofInTemplate(p, token) {\n    if (p.openElements.tmplCount > 0) {\n        p.openElements.popUntilTagNamePopped($.TEMPLATE);\n        p.activeFormattingElements.clearToLastMarker();\n        p.tmplInsertionModeStack.shift();\n        p._resetInsertionMode();\n        p.onEof(token);\n    }\n    else {\n        stopParsing(p, token);\n    }\n}\n// The \"after body\" insertion mode\n//------------------------------------------------------------------\nfunction startTagAfterBody(p, token) {\n    if (token.tagID === $.HTML) {\n        startTagInBody(p, token);\n    }\n    else {\n        tokenAfterBody(p, token);\n    }\n}\nfunction endTagAfterBody(p, token) {\n    var _a;\n    if (token.tagID === $.HTML) {\n        if (!p.fragmentContext) {\n            p.insertionMode = InsertionMode.AFTER_AFTER_BODY;\n        }\n        //NOTE: <html> is never popped from the stack, so we need to updated\n        //the end location explicitly.\n        if (p.options.sourceCodeLocationInfo && p.openElements.tagIDs[0] === $.HTML) {\n            p._setEndLocation(p.openElements.items[0], token);\n            // Update the body element, if it doesn't have an end tag\n            const bodyElement = p.openElements.items[1];\n            if (bodyElement && !((_a = p.treeAdapter.getNodeSourceCodeLocation(bodyElement)) === null || _a === void 0 ? void 0 : _a.endTag)) {\n                p._setEndLocation(bodyElement, token);\n            }\n        }\n    }\n    else {\n        tokenAfterBody(p, token);\n    }\n}\nfunction tokenAfterBody(p, token) {\n    p.insertionMode = InsertionMode.IN_BODY;\n    modeInBody(p, token);\n}\n// The \"in frameset\" insertion mode\n//------------------------------------------------------------------\nfunction startTagInFrameset(p, token) {\n    switch (token.tagID) {\n        case $.HTML: {\n            startTagInBody(p, token);\n            break;\n        }\n        case $.FRAMESET: {\n            p._insertElement(token, NS.HTML);\n            break;\n        }\n        case $.FRAME: {\n            p._appendElement(token, NS.HTML);\n            token.ackSelfClosing = true;\n            break;\n        }\n        case $.NOFRAMES: {\n            startTagInHead(p, token);\n            break;\n        }\n        default:\n        // Do nothing\n    }\n}\nfunction endTagInFrameset(p, token) {\n    if (token.tagID === $.FRAMESET && !p.openElements.isRootHtmlElementCurrent()) {\n        p.openElements.pop();\n        if (!p.fragmentContext && p.openElements.currentTagId !== $.FRAMESET) {\n            p.insertionMode = InsertionMode.AFTER_FRAMESET;\n        }\n    }\n}\n// The \"after frameset\" insertion mode\n//------------------------------------------------------------------\nfunction startTagAfterFrameset(p, token) {\n    switch (token.tagID) {\n        case $.HTML: {\n            startTagInBody(p, token);\n            break;\n        }\n        case $.NOFRAMES: {\n            startTagInHead(p, token);\n            break;\n        }\n        default:\n        // Do nothing\n    }\n}\nfunction endTagAfterFrameset(p, token) {\n    if (token.tagID === $.HTML) {\n        p.insertionMode = InsertionMode.AFTER_AFTER_FRAMESET;\n    }\n}\n// The \"after after body\" insertion mode\n//------------------------------------------------------------------\nfunction startTagAfterAfterBody(p, token) {\n    if (token.tagID === $.HTML) {\n        startTagInBody(p, token);\n    }\n    else {\n        tokenAfterAfterBody(p, token);\n    }\n}\nfunction tokenAfterAfterBody(p, token) {\n    p.insertionMode = InsertionMode.IN_BODY;\n    modeInBody(p, token);\n}\n// The \"after after frameset\" insertion mode\n//------------------------------------------------------------------\nfunction startTagAfterAfterFrameset(p, token) {\n    switch (token.tagID) {\n        case $.HTML: {\n            startTagInBody(p, token);\n            break;\n        }\n        case $.NOFRAMES: {\n            startTagInHead(p, token);\n            break;\n        }\n        default:\n        // Do nothing\n    }\n}\n// The rules for parsing tokens in foreign content\n//------------------------------------------------------------------\nfunction nullCharacterInForeignContent(p, token) {\n    token.chars = unicode.REPLACEMENT_CHARACTER;\n    p._insertCharacters(token);\n}\nfunction characterInForeignContent(p, token) {\n    p._insertCharacters(token);\n    p.framesetOk = false;\n}\nfunction popUntilHtmlOrIntegrationPoint(p) {\n    while (p.treeAdapter.getNamespaceURI(p.openElements.current) !== NS.HTML &&\n        p.openElements.currentTagId !== undefined &&\n        !p._isIntegrationPoint(p.openElements.currentTagId, p.openElements.current)) {\n        p.openElements.pop();\n    }\n}\nfunction startTagInForeignContent(p, token) {\n    if (foreignContent.causesExit(token)) {\n        popUntilHtmlOrIntegrationPoint(p);\n        p._startTagOutsideForeignContent(token);\n    }\n    else {\n        const current = p._getAdjustedCurrentElement();\n        const currentNs = p.treeAdapter.getNamespaceURI(current);\n        if (currentNs === NS.MATHML) {\n            foreignContent.adjustTokenMathMLAttrs(token);\n        }\n        else if (currentNs === NS.SVG) {\n            foreignContent.adjustTokenSVGTagName(token);\n            foreignContent.adjustTokenSVGAttrs(token);\n        }\n        foreignContent.adjustTokenXMLAttrs(token);\n        if (token.selfClosing) {\n            p._appendElement(token, currentNs);\n        }\n        else {\n            p._insertElement(token, currentNs);\n        }\n        token.ackSelfClosing = true;\n    }\n}\nfunction endTagInForeignContent(p, token) {\n    if (token.tagID === $.P || token.tagID === $.BR) {\n        popUntilHtmlOrIntegrationPoint(p);\n        p._endTagOutsideForeignContent(token);\n        return;\n    }\n    for (let i = p.openElements.stackTop; i > 0; i--) {\n        const element = p.openElements.items[i];\n        if (p.treeAdapter.getNamespaceURI(element) === NS.HTML) {\n            p._endTagOutsideForeignContent(token);\n            break;\n        }\n        const tagName = p.treeAdapter.getTagName(element);\n        if (tagName.toLowerCase() === token.tagName) {\n            //NOTE: update the token tag name for `_setEndLocation`.\n            token.tagName = tagName;\n            p.openElements.shortenToLength(i);\n            break;\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACA,gBAAgB;AAChB,MAAM,oBAAoB;AAC1B,uCAAuC;AACvC,MAAM,qBAAqB;AAC3B,MAAM,qBAAqB;AAC3B,iBAAiB;AACjB,IAAI;AACJ,CAAC,SAAU,aAAa;IACpB,aAAa,CAAC,aAAa,CAAC,UAAU,GAAG,EAAE,GAAG;IAC9C,aAAa,CAAC,aAAa,CAAC,cAAc,GAAG,EAAE,GAAG;IAClD,aAAa,CAAC,aAAa,CAAC,cAAc,GAAG,EAAE,GAAG;IAClD,aAAa,CAAC,aAAa,CAAC,UAAU,GAAG,EAAE,GAAG;IAC9C,aAAa,CAAC,aAAa,CAAC,oBAAoB,GAAG,EAAE,GAAG;IACxD,aAAa,CAAC,aAAa,CAAC,aAAa,GAAG,EAAE,GAAG;IACjD,aAAa,CAAC,aAAa,CAAC,UAAU,GAAG,EAAE,GAAG;IAC9C,aAAa,CAAC,aAAa,CAAC,OAAO,GAAG,EAAE,GAAG;IAC3C,aAAa,CAAC,aAAa,CAAC,WAAW,GAAG,EAAE,GAAG;IAC/C,aAAa,CAAC,aAAa,CAAC,gBAAgB,GAAG,EAAE,GAAG;IACpD,aAAa,CAAC,aAAa,CAAC,aAAa,GAAG,GAAG,GAAG;IAClD,aAAa,CAAC,aAAa,CAAC,kBAAkB,GAAG,GAAG,GAAG;IACvD,aAAa,CAAC,aAAa,CAAC,gBAAgB,GAAG,GAAG,GAAG;IACrD,aAAa,CAAC,aAAa,CAAC,SAAS,GAAG,GAAG,GAAG;IAC9C,aAAa,CAAC,aAAa,CAAC,UAAU,GAAG,GAAG,GAAG;IAC/C,aAAa,CAAC,aAAa,CAAC,YAAY,GAAG,GAAG,GAAG;IACjD,aAAa,CAAC,aAAa,CAAC,qBAAqB,GAAG,GAAG,GAAG;IAC1D,aAAa,CAAC,aAAa,CAAC,cAAc,GAAG,GAAG,GAAG;IACnD,aAAa,CAAC,aAAa,CAAC,aAAa,GAAG,GAAG,GAAG;IAClD,aAAa,CAAC,aAAa,CAAC,cAAc,GAAG,GAAG,GAAG;IACnD,aAAa,CAAC,aAAa,CAAC,iBAAiB,GAAG,GAAG,GAAG;IACtD,aAAa,CAAC,aAAa,CAAC,mBAAmB,GAAG,GAAG,GAAG;IACxD,aAAa,CAAC,aAAa,CAAC,uBAAuB,GAAG,GAAG,GAAG;AAChE,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;AACvC,MAAM,WAAW;IACb,WAAW,CAAC;IACZ,UAAU,CAAC;IACX,aAAa,CAAC;IACd,SAAS,CAAC;IACV,QAAQ,CAAC;IACT,WAAW,CAAC;AAChB;AACA,MAAM,uBAAuB,IAAI,IAAI;IAAC,gJAAA,CAAA,SAAC,CAAC,KAAK;IAAE,gJAAA,CAAA,SAAC,CAAC,KAAK;IAAE,gJAAA,CAAA,SAAC,CAAC,KAAK;IAAE,gJAAA,CAAA,SAAC,CAAC,KAAK;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;CAAC;AAC/E,MAAM,uBAAuB;IACzB,kBAAkB;IAClB,wBAAwB;IACxB,aAAa,6JAAA,CAAA,qBAAkB;IAC/B,cAAc;AAClB;AAEO,MAAM;IACT,YAAY,OAAO,EAAE,QAAQ,EAC7B,cAAc,GACd,kBAAkB,IAAI,EACtB,cAAc,GACd,gBAAgB,IAAI,CAAE;QAClB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,OAAO,GAAG;QACf,cAAc,GACd,IAAI,CAAC,aAAa,GAAG,cAAc,OAAO;QAC1C,cAAc,GACd,IAAI,CAAC,qBAAqB,GAAG,cAAc,OAAO;QAClD,cAAc,GACd,IAAI,CAAC,WAAW,GAAG;QACnB,cAAc,GACd,IAAI,CAAC,WAAW,GAAG;QACnB,4EAA4E,GAC5E,IAAI,CAAC,gBAAgB,GAAG;QACxB;;;;;SAKC,GACD,IAAI,CAAC,sBAAsB,GAAG,EAAE;QAChC,cAAc,GACd,IAAI,CAAC,sBAAsB,GAAG,EAAE;QAChC,cAAc,GACd,IAAI,CAAC,qCAAqC,GAAG;QAC7C,cAAc,GACd,IAAI,CAAC,UAAU,GAAG;QAClB,cAAc,GACd,IAAI,CAAC,eAAe,GAAG;QACvB,cAAc,GACd,IAAI,CAAC,sBAAsB,GAAG;QAC9B,IAAI,CAAC,OAAO,GAAG;YACX,GAAG,oBAAoB;YACvB,GAAG,OAAO;QACd;QACA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;QAC3C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY;QAC7C,yDAAyD;QACzD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC,sBAAsB,GAAG;QAC1C;QACA,IAAI,CAAC,QAAQ,GAAG,aAAa,QAAQ,aAAa,KAAK,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,cAAc;QACrG,IAAI,CAAC,SAAS,GAAG,IAAI,oJAAA,CAAA,YAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI;QACjD,IAAI,CAAC,wBAAwB,GAAG,IAAI,yKAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,WAAW;QAC1E,IAAI,CAAC,iBAAiB,GAAG,kBAAkB,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,oBAAoB,gJAAA,CAAA,SAAC,CAAC,OAAO;QAC7G,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,iBAAiB;QACtI,IAAI,CAAC,YAAY,GAAG,IAAI,oKAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI;IAClF;IACA,MAAM;IACN,OAAO,MAAM,IAAI,EAAE,OAAO,EAAE;QACxB,MAAM,SAAS,IAAI,IAAI,CAAC;QACxB,OAAO,SAAS,CAAC,KAAK,CAAC,MAAM;QAC7B,OAAO,OAAO,QAAQ;IAC1B;IACA,OAAO,kBAAkB,eAAe,EAAE,OAAO,EAAE;QAC/C,MAAM,OAAO;YACT,GAAG,oBAAoB;YACvB,GAAG,OAAO;QACd;QACA,4FAA4F;QAC5F,0CAA0C;QAC1C,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,kBAAmB,kBAAkB,KAAK,WAAW,CAAC,aAAa,CAAC,gJAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE,EAAE;QACrJ,wFAAwF;QACxF,oFAAoF;QACpF,oDAAoD;QACpD,MAAM,eAAe,KAAK,WAAW,CAAC,aAAa,CAAC,gBAAgB,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE,EAAE;QAC/E,MAAM,SAAS,IAAI,IAAI,CAAC,MAAM,cAAc;QAC5C,IAAI,OAAO,iBAAiB,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,EAAE;YACzC,OAAO,sBAAsB,CAAC,OAAO,CAAC,cAAc,WAAW;QACnE;QACA,OAAO,gCAAgC;QACvC,OAAO,sBAAsB;QAC7B,OAAO,mBAAmB;QAC1B,OAAO,0BAA0B;QACjC,OAAO;IACX;IACA,cAAc;QACV,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ;QAChE,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,sBAAsB;QACxD,IAAI,CAAC,WAAW,CAAC,aAAa;QAC9B,OAAO;IACX;IACA,QAAQ;IACR,cAAc,GACd,KAAK,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE;QAC3B,IAAI;QACJ,IAAI,CAAC,IAAI,CAAC,YAAY,EAClB;QACJ,MAAM,MAAM,CAAC,KAAK,MAAM,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACnE,MAAM,MAAM;YACR;YACA,WAAW,IAAI,SAAS;YACxB,UAAU,IAAI,QAAQ;YACtB,aAAa,IAAI,WAAW;YAC5B,SAAS,cAAc,IAAI,SAAS,GAAG,IAAI,OAAO;YAClD,QAAQ,cAAc,IAAI,QAAQ,GAAG,IAAI,MAAM;YAC/C,WAAW,cAAc,IAAI,WAAW,GAAG,IAAI,SAAS;QAC5D;QACA,IAAI,CAAC,YAAY,CAAC;IACtB;IACA,cAAc;IACd,cAAc,GACd,WAAW,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE;QACzB,IAAI,IAAI;QACR,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,WAAW,EAAE,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI;QAC3F,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,GACtC,IAAI,CAAC,gBAAgB,CAAC,MAAM;IACpC;IACA,cAAc,GACd,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,IAAI,IAAI;QACR,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YACrC,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,YAAY;QAChD;QACA,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,WAAW,EAAE,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO;QACzH,IAAI,OAAO;YACP,IAAI;YACJ,IAAI;YACJ,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,eAAe,EAAE;gBAC1D,UAAU,IAAI,CAAC,eAAe;gBAC9B,eAAe,IAAI,CAAC,iBAAiB;YACzC,OACK;gBACD,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,YAAY;YAClD;YACA,IAAI,CAAC,gBAAgB,CAAC,SAAS;QACnC;IACJ;IACA,iBAAiB,OAAO,EAAE,GAAG,EAAE;QAC3B,MAAM,SAAS,YAAY,IAAI,CAAC,QAAQ,IAAK,WAAW,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,aAAa,gJAAA,CAAA,KAAE,CAAC,IAAI;QAC7G,IAAI,CAAC,gBAAgB,GAAG,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,aAAa,GACxB,CAAC,UAAU,YAAY,aAAa,QAAQ,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK;IAChG;IACA,eAAe,GACf,qBAAqB,YAAY,EAAE,kBAAkB,EAAE;QACnD,IAAI,CAAC,cAAc,CAAC,cAAc,gJAAA,CAAA,KAAE,CAAC,IAAI;QACzC,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;QACvB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,aAAa;QAC/C,IAAI,CAAC,aAAa,GAAG,cAAc,IAAI;IAC3C;IACA,2BAA2B;QACvB,IAAI,CAAC,aAAa,GAAG,cAAc,IAAI;QACvC,IAAI,CAAC,qBAAqB,GAAG,cAAc,OAAO;QAClD,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,oJAAA,CAAA,gBAAa,CAAC,SAAS;IAClD;IACA,kBAAkB;IAClB,eAAe,GACf,6BAA6B;QACzB,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,eAAe,GACzD,IAAI,CAAC,eAAe,GACpB,IAAI,CAAC,YAAY,CAAC,OAAO;IACnC;IACA,eAAe,GACf,6BAA6B;QACzB,IAAI,OAAO,IAAI,CAAC,eAAe;QAC/B,MAAO,KAAM;YACT,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,gJAAA,CAAA,YAAE,CAAC,IAAI,EAAE;gBAC/C,IAAI,CAAC,WAAW,GAAG;gBACnB;YACJ;YACA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;QAC1C;IACJ;IACA,mCAAmC;QAC/B,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,MAAM,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE;YAC7F;QACJ;QACA,OAAQ,IAAI,CAAC,iBAAiB;YAC1B,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;gBAAE;oBACb,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,oJAAA,CAAA,gBAAa,CAAC,MAAM;oBAC3C;gBACJ;YACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;YACV,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YACb,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;YACd,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YACf,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;gBAAE;oBACb,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,oJAAA,CAAA,gBAAa,CAAC,OAAO;oBAC5C;gBACJ;YACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;gBAAE;oBACX,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,oJAAA,CAAA,gBAAa,CAAC,WAAW;oBAChD;gBACJ;YACA,KAAK,gJAAA,CAAA,SAAC,CAAC,SAAS;gBAAE;oBACd,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,oJAAA,CAAA,gBAAa,CAAC,SAAS;oBAC9C;gBACJ;YACA;QAEJ;IACJ;IACA,eAAe;IACf,eAAe,GACf,iBAAiB,KAAK,EAAE;QACpB,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,WAAW,MAAM,QAAQ,IAAI;QACnC,MAAM,WAAW,MAAM,QAAQ,IAAI;QACnC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,UAAU;QAChE,IAAI,MAAM,QAAQ,EAAE;YAChB,MAAM,mBAAmB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ;YACrE,MAAM,cAAc,iBAAiB,IAAI,CAAC,CAAC,OAAS,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC;YACxF,IAAI,aAAa;gBACb,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,aAAa,MAAM,QAAQ;YAC1E;QACJ;IACJ;IACA,eAAe,GACf,qBAAqB,OAAO,EAAE,QAAQ,EAAE;QACpC,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YACrC,MAAM,MAAM,YAAY;gBACpB,GAAG,QAAQ;gBACX,UAAU;YACd;YACA,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,SAAS;QACxD;QACA,IAAI,IAAI,CAAC,8BAA8B,IAAI;YACvC,IAAI,CAAC,oBAAoB,CAAC;QAC9B,OACK;YACD,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC,wBAAwB;YACzD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;QAChG;IACJ;IACA;;;KAGC,GACD,eAAe,GACf,eAAe,KAAK,EAAE,YAAY,EAAE;QAChC,MAAM,UAAU,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,cAAc,MAAM,KAAK;QACvF,IAAI,CAAC,oBAAoB,CAAC,SAAS,MAAM,QAAQ;IACrD;IACA,eAAe,GACf,eAAe,KAAK,EAAE,YAAY,EAAE;QAChC,MAAM,UAAU,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,cAAc,MAAM,KAAK;QACvF,IAAI,CAAC,oBAAoB,CAAC,SAAS,MAAM,QAAQ;QACjD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,MAAM,KAAK;IAC/C;IACA,eAAe,GACf,mBAAmB,OAAO,EAAE,KAAK,EAAE;QAC/B,MAAM,UAAU,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE,EAAE;QACnE,IAAI,CAAC,oBAAoB,CAAC,SAAS;QACnC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS;IACpC;IACA,eAAe,GACf,gBAAgB,KAAK,EAAE;QACnB,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE,MAAM,KAAK;QAC/E,MAAM,UAAU,IAAI,CAAC,WAAW,CAAC,sBAAsB;QACvD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM;QAC1C,IAAI,CAAC,oBAAoB,CAAC,MAAM,MAAM,QAAQ;QAC9C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,MAAM,KAAK;QACxC,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EACnC,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,SAAS;IAC5D;IACA,eAAe,GACf,yBAAyB;QACrB,MAAM,UAAU,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,gJAAA,CAAA,YAAE,CAAC,IAAI,EAAE,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE,EAAE;QACnE,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EACnC,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,SAAS;QACxD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;QACxD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,gJAAA,CAAA,SAAC,CAAC,IAAI;IAC1C;IACA,eAAe,GACf,mBAAmB,KAAK,EAAE,MAAM,EAAE;QAC9B,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,MAAM,IAAI;QACjE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ;QACrC,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YACrC,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,aAAa,MAAM,QAAQ;QAC1E;IACJ;IACA,eAAe,GACf,kBAAkB,KAAK,EAAE;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,CAAC,8BAA8B,IAAI;YACvC,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,4BAA4B,EAAE;YAChE,IAAI,eAAe;gBACf,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,MAAM,KAAK,EAAE;YAC3D,OACK;gBACD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,QAAQ,MAAM,KAAK;YACnD;QACJ,OACK;YACD,SAAS,IAAI,CAAC,YAAY,CAAC,wBAAwB;YACnD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,QAAQ,MAAM,KAAK;QACnD;QACA,IAAI,CAAC,MAAM,QAAQ,EACf;QACJ,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;QAChD,MAAM,cAAc,gBAAgB,SAAS,WAAW,CAAC,iBAAiB,SAAS,MAAM;QACzF,MAAM,WAAW,QAAQ,CAAC,cAAc,EAAE;QAC1C,0FAA0F;QAC1F,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC;QACzD,IAAI,OAAO;YACP,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,QAAQ;YACrD,IAAI,CAAC,WAAW,CAAC,4BAA4B,CAAC,UAAU;gBAAE;gBAAS;gBAAQ;YAAU;QACzF,OACK,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YAC1C,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,UAAU,MAAM,QAAQ;QACvE;IACJ;IACA,eAAe,GACf,YAAY,KAAK,EAAE,SAAS,EAAE;QAC1B,IAAK,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,OAAO,QAAQ,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,OAAQ;YAC1G,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;YAC5B,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW;QAC5C;IACJ;IACA,eAAe,GACf,gBAAgB,OAAO,EAAE,YAAY,EAAE;QACnC,IAAI,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,YAAY,aAAa,QAAQ,EAAE;YAC9E,MAAM,QAAQ,aAAa,QAAQ;YACnC,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;YACvC,MAAM,SACN,yEAAyE;YACzE,4EAA4E;YAC5E,aAAa,IAAI,KAAK,iJAAA,CAAA,YAAS,CAAC,OAAO,IAAI,OAAO,aAAa,OAAO,GAChE;gBACE,QAAQ;oBAAE,GAAG,KAAK;gBAAC;gBACnB,SAAS,MAAM,OAAO;gBACtB,QAAQ,MAAM,MAAM;gBACpB,WAAW,MAAM,SAAS;YAC9B,IACE;gBACE,SAAS,MAAM,SAAS;gBACxB,QAAQ,MAAM,QAAQ;gBACtB,WAAW,MAAM,WAAW;YAChC;YACJ,IAAI,CAAC,WAAW,CAAC,4BAA4B,CAAC,SAAS;QAC3D;IACJ;IACA,kBAAkB;IAClB,2CAA2C,KAAK,EAAE;QAC9C,6DAA6D;QAC7D,IAAI,CAAC,IAAI,CAAC,gBAAgB,EACtB,OAAO;QACX,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,eAAe,EAAE;YAC1D,UAAU,IAAI,CAAC,eAAe;YAC9B,eAAe,IAAI,CAAC,iBAAiB;QACzC,OACK;YACD,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,YAAY;QAClD;QACA,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG,IACrB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,aAAa,gJAAA,CAAA,YAAE,CAAC,cAAc,IAC1D,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,aAAa,gJAAA,CAAA,KAAE,CAAC,MAAM,EAAE;YACzD,OAAO;QACX;QACA,OACA,gFAAgF;QAChF,IAAI,CAAC,SAAS,CAAC,aAAa,IAGvB,CAAC,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,UAAU,KACtD,iBAAiB,aACjB,CAAC,IAAI,CAAC,mBAAmB,CAAC,cAAc,SAAS,gJAAA,CAAA,KAAE,CAAC,IAAI;IACpE;IACA,eAAe,GACf,cAAc,KAAK,EAAE;QACjB,OAAQ,MAAM,IAAI;YACd,KAAK,iJAAA,CAAA,YAAS,CAAC,SAAS;gBAAE;oBACtB,IAAI,CAAC,WAAW,CAAC;oBACjB;gBACJ;YACA,KAAK,iJAAA,CAAA,YAAS,CAAC,cAAc;gBAAE;oBAC3B,IAAI,CAAC,eAAe,CAAC;oBACrB;gBACJ;YACA,KAAK,iJAAA,CAAA,YAAS,CAAC,OAAO;gBAAE;oBACpB,IAAI,CAAC,SAAS,CAAC;oBACf;gBACJ;YACA,KAAK,iJAAA,CAAA,YAAS,CAAC,OAAO;gBAAE;oBACpB,IAAI,CAAC,SAAS,CAAC;oBACf;gBACJ;YACA,KAAK,iJAAA,CAAA,YAAS,CAAC,SAAS;gBAAE;oBACtB,IAAI,CAAC,gBAAgB,CAAC;oBACtB;gBACJ;YACA,KAAK,iJAAA,CAAA,YAAS,CAAC,OAAO;gBAAE;oBACpB,IAAI,CAAC,QAAQ,CAAC;oBACd;gBACJ;YACA,KAAK,iJAAA,CAAA,YAAS,CAAC,GAAG;gBAAE;oBAChB,IAAI,CAAC,KAAK,CAAC;oBACX;gBACJ;YACA,KAAK,iJAAA,CAAA,YAAS,CAAC,oBAAoB;gBAAE;oBACjC,IAAI,CAAC,qBAAqB,CAAC;oBAC3B;gBACJ;QACJ;IACJ;IACA,oBAAoB;IACpB,eAAe,GACf,oBAAoB,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE;QACzC,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;QAC5C,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;QAC3C,OAAO,CAAA,GAAA,8JAAA,CAAA,qBAAiC,AAAD,EAAE,KAAK,IAAI,OAAO;IAC7D;IACA,2CAA2C;IAC3C,eAAe,GACf,uCAAuC;QACnC,MAAM,aAAa,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,MAAM;QAC/D,IAAI,YAAY;YACZ,MAAM,WAAW,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAU,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAS,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,OAAO;YACvJ,MAAM,YAAY,aAAa,CAAC,IAAI,aAAa,IAAI,WAAW;YAChE,IAAK,IAAI,IAAI,WAAW,KAAK,GAAG,IAAK;gBACjC,MAAM,QAAQ,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAE;gBACtD,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,OAAO;gBAC/E,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO;YAC7C;QACJ;IACJ;IACA,gBAAgB;IAChB,eAAe,GACf,kBAAkB;QACd,IAAI,CAAC,YAAY,CAAC,sBAAsB;QACxC,IAAI,CAAC,YAAY,CAAC,uBAAuB;QACzC,IAAI,CAAC,wBAAwB,CAAC,iBAAiB;QAC/C,IAAI,CAAC,aAAa,GAAG,cAAc,MAAM;IAC7C;IACA,eAAe,GACf,iBAAiB;QACb,IAAI,CAAC,YAAY,CAAC,mCAAmC,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC;QACzD,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC;IAC/C;IACA,iBAAiB;IACjB,eAAe,GACf,sBAAsB;QAClB,IAAK,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAK;YAClD,0BAA0B;YAC1B,OAAQ,MAAM,KAAK,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;gBAC1F,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;oBAAE;wBACP,IAAI,CAAC,aAAa,GAAG,cAAc,MAAM;wBACzC;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;gBACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;gBACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;oBAAE;wBACV,IAAI,CAAC,aAAa,GAAG,cAAc,aAAa;wBAChD;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;oBAAE;wBACZ,IAAI,CAAC,aAAa,GAAG,cAAc,UAAU;wBAC7C;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;oBAAE;wBACb,IAAI,CAAC,aAAa,GAAG,cAAc,eAAe;wBAClD;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;oBAAE;wBACV,IAAI,CAAC,aAAa,GAAG,cAAc,QAAQ;wBAC3C;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;oBAAE;wBACT,IAAI,CAAC,aAAa,GAAG,cAAc,OAAO;wBAC1C;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;oBAAE;wBACb,IAAI,CAAC,aAAa,GAAG,cAAc,WAAW;wBAC9C;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;oBAAE;wBACX,IAAI,CAAC,4BAA4B,CAAC;wBAClC;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;oBAAE;wBACb,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE;wBACnD;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;oBAAE;wBACT,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,cAAc,UAAU,GAAG,cAAc,WAAW;wBAC5F;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;gBACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;oBAAE;wBACP,IAAI,IAAI,GAAG;4BACP,IAAI,CAAC,aAAa,GAAG,cAAc,OAAO;4BAC1C;wBACJ;wBACA;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;oBAAE;wBACT,IAAI,IAAI,GAAG;4BACP,IAAI,CAAC,aAAa,GAAG,cAAc,OAAO;4BAC1C;wBACJ;wBACA;oBACJ;YACJ;QACJ;QACA,IAAI,CAAC,aAAa,GAAG,cAAc,OAAO;IAC9C;IACA,eAAe,GACf,6BAA6B,SAAS,EAAE;QACpC,IAAI,YAAY,GAAG;YACf,IAAK,IAAI,IAAI,YAAY,GAAG,IAAI,GAAG,IAAK;gBACpC,MAAM,KAAK,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;gBACtC,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,QAAQ,EAAE;oBACnB;gBACJ,OACK,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK,EAAE;oBACrB,IAAI,CAAC,aAAa,GAAG,cAAc,kBAAkB;oBACrD;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,aAAa,GAAG,cAAc,SAAS;IAChD;IACA,kBAAkB;IAClB,eAAe,GACf,gCAAgC,EAAE,EAAE;QAChC,OAAO,qBAAqB,GAAG,CAAC;IACpC;IACA,eAAe,GACf,iCAAiC;QAC7B,OAAQ,IAAI,CAAC,sBAAsB,IAC/B,IAAI,CAAC,YAAY,CAAC,YAAY,KAAK,aACnC,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY;IAC3E;IACA,eAAe,GACf,+BAA+B;QAC3B,IAAK,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAK;YAClD,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;YAC9C,OAAQ,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;gBAC/B,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;oBAAE;wBACb,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,iBAAiB,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE;4BAC3D,OAAO;gCAAE,QAAQ,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC;gCAAc,eAAe;4BAAK;wBAC3F;wBACA;oBACJ;gBACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;oBAAE;wBACV,MAAM,SAAS,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;wBAC9C,IAAI,QAAQ;4BACR,OAAO;gCAAE;gCAAQ,eAAe;4BAAY;wBAChD;wBACA,OAAO;4BAAE,QAAQ,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE;4BAAE,eAAe;wBAAK;oBACzE;gBACA;YAEJ;QACJ;QACA,OAAO;YAAE,QAAQ,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;YAAE,eAAe;QAAK;IACrE;IACA,eAAe,GACf,qBAAqB,OAAO,EAAE;QAC1B,MAAM,WAAW,IAAI,CAAC,4BAA4B;QAClD,IAAI,SAAS,aAAa,EAAE;YACxB,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,SAAS,MAAM,EAAE,SAAS,SAAS,aAAa;QAClF,OACK;YACD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,MAAM,EAAE;QAClD;IACJ;IACA,kBAAkB;IAClB,eAAe,GACf,kBAAkB,OAAO,EAAE,EAAE,EAAE;QAC3B,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;QAC5C,OAAO,gJAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC;IACpC;IACA,cAAc,GACd,YAAY,KAAK,EAAE;QACf,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE;YAC9B,0BAA0B,IAAI,EAAE;YAChC;QACJ;QACA,OAAQ,IAAI,CAAC,aAAa;YACtB,KAAK,cAAc,OAAO;gBAAE;oBACxB,mBAAmB,IAAI,EAAE;oBACzB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,gBAAgB,IAAI,EAAE;oBACtB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,gBAAgB,IAAI,EAAE;oBACtB;gBACJ;YACA,KAAK,cAAc,OAAO;gBAAE;oBACxB,YAAY,IAAI,EAAE;oBAClB;gBACJ;YACA,KAAK,cAAc,iBAAiB;gBAAE;oBAClC,oBAAoB,IAAI,EAAE;oBAC1B;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,eAAe,IAAI,EAAE;oBACrB;gBACJ;YACA,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,UAAU;YAC7B,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,WAAW;gBAAE;oBAC5B,gBAAgB,IAAI,EAAE;oBACtB;gBACJ;YACA,KAAK,cAAc,IAAI;YACvB,KAAK,cAAc,SAAS;YAC5B,KAAK,cAAc,kBAAkB;gBAAE;oBACnC,IAAI,CAAC,iBAAiB,CAAC;oBACvB;gBACJ;YACA,KAAK,cAAc,QAAQ;YAC3B,KAAK,cAAc,aAAa;YAChC,KAAK,cAAc,MAAM;gBAAE;oBACvB,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,aAAa;gBAAE;oBAC9B,qBAAqB,IAAI,EAAE;oBAC3B;gBACJ;YACA,KAAK,cAAc,eAAe;gBAAE;oBAChC,mBAAmB,IAAI,EAAE;oBACzB;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,eAAe,IAAI,EAAE;oBACrB;gBACJ;YACA,KAAK,cAAc,gBAAgB;gBAAE;oBACjC,oBAAoB,IAAI,EAAE;oBAC1B;gBACJ;YACA;QAEJ;IACJ;IACA,cAAc,GACd,gBAAgB,KAAK,EAAE;QACnB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE;YAC9B,8BAA8B,IAAI,EAAE;YACpC;QACJ;QACA,OAAQ,IAAI,CAAC,aAAa;YACtB,KAAK,cAAc,OAAO;gBAAE;oBACxB,mBAAmB,IAAI,EAAE;oBACzB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,gBAAgB,IAAI,EAAE;oBACtB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,gBAAgB,IAAI,EAAE;oBACtB;gBACJ;YACA,KAAK,cAAc,OAAO;gBAAE;oBACxB,YAAY,IAAI,EAAE;oBAClB;gBACJ;YACA,KAAK,cAAc,iBAAiB;gBAAE;oBAClC,oBAAoB,IAAI,EAAE;oBAC1B;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,eAAe,IAAI,EAAE;oBACrB;gBACJ;YACA,KAAK,cAAc,IAAI;gBAAE;oBACrB,IAAI,CAAC,iBAAiB,CAAC;oBACvB;gBACJ;YACA,KAAK,cAAc,QAAQ;YAC3B,KAAK,cAAc,aAAa;YAChC,KAAK,cAAc,MAAM;gBAAE;oBACvB,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,eAAe;gBAAE;oBAChC,mBAAmB,IAAI,EAAE;oBACzB;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,eAAe,IAAI,EAAE;oBACrB;gBACJ;YACA,KAAK,cAAc,gBAAgB;gBAAE;oBACjC,oBAAoB,IAAI,EAAE;oBAC1B;gBACJ;YACA;QAEJ;IACJ;IACA,cAAc,GACd,UAAU,KAAK,EAAE;QACb,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,IAAI,EAAE;YACpB;QACJ;QACA,OAAQ,IAAI,CAAC,aAAa;YACtB,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,WAAW;YAC9B,KAAK,cAAc,WAAW;YAC9B,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,iBAAiB;YACpC,KAAK,cAAc,UAAU;YAC7B,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,QAAQ;YAC3B,KAAK,cAAc,UAAU;YAC7B,KAAK,cAAc,eAAe;YAClC,KAAK,cAAc,aAAa;YAChC,KAAK,cAAc,MAAM;YACzB,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,SAAS;YAC5B,KAAK,cAAc,kBAAkB;YACrC,KAAK,cAAc,WAAW;YAC9B,KAAK,cAAc,WAAW;YAC9B,KAAK,cAAc,cAAc;gBAAE;oBAC/B,cAAc,IAAI,EAAE;oBACpB;gBACJ;YACA,KAAK,cAAc,aAAa;gBAAE;oBAC9B,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,+BAA+B,IAAI,EAAE;oBACrC;gBACJ;YACA,KAAK,cAAc,gBAAgB;YACnC,KAAK,cAAc,oBAAoB;gBAAE;oBACrC,wBAAwB,IAAI,EAAE;oBAC9B;gBACJ;YACA;QAEJ;IACJ;IACA,cAAc,GACd,UAAU,KAAK,EAAE;QACb,IAAI,CAAC,eAAe,GAAG;QACvB,OAAQ,IAAI,CAAC,aAAa;YACtB,KAAK,cAAc,OAAO;gBAAE;oBACxB,qBAAqB,IAAI,EAAE;oBAC3B;gBACJ;YACA,KAAK,cAAc,WAAW;YAC9B,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,iBAAiB;YACpC,KAAK,cAAc,UAAU;gBAAE;oBAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,gBAAgB;oBACrC;gBACJ;YACA,KAAK,cAAc,aAAa;gBAAE;oBAC9B,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA;QAEJ;IACJ;IACA,cAAc,GACd,WAAW,KAAK,EAAE;QACd,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,gBAAgB,CAAC;QACtB,IAAI,MAAM,WAAW,IAAI,CAAC,MAAM,cAAc,EAAE;YAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,6CAA6C;QACtE;IACJ;IACA;;;;;;;;;;KAUC,GACD,iBAAiB,KAAK,EAAE;QACpB,IAAI,IAAI,CAAC,0CAA0C,CAAC,QAAQ;YACxD,yBAAyB,IAAI,EAAE;QACnC,OACK;YACD,IAAI,CAAC,8BAA8B,CAAC;QACxC;IACJ;IACA,eAAe,GACf,+BAA+B,KAAK,EAAE;QAClC,OAAQ,IAAI,CAAC,aAAa;YACtB,KAAK,cAAc,OAAO;gBAAE;oBACxB,mBAAmB,IAAI,EAAE;oBACzB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,mBAAmB,IAAI,EAAE;oBACzB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,mBAAmB,IAAI,EAAE;oBACzB;gBACJ;YACA,KAAK,cAAc,OAAO;gBAAE;oBACxB,eAAe,IAAI,EAAE;oBACrB;gBACJ;YACA,KAAK,cAAc,iBAAiB;gBAAE;oBAClC,uBAAuB,IAAI,EAAE;oBAC7B;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,kBAAkB,IAAI,EAAE;oBACxB;gBACJ;YACA,KAAK,cAAc,OAAO;gBAAE;oBACxB,eAAe,IAAI,EAAE;oBACrB;gBACJ;YACA,KAAK,cAAc,QAAQ;gBAAE;oBACzB,gBAAgB,IAAI,EAAE;oBACtB;gBACJ;YACA,KAAK,cAAc,aAAa;gBAAE;oBAC9B,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,kBAAkB,IAAI,EAAE;oBACxB;gBACJ;YACA,KAAK,cAAc,eAAe;gBAAE;oBAChC,sBAAsB,IAAI,EAAE;oBAC5B;gBACJ;YACA,KAAK,cAAc,aAAa;gBAAE;oBAC9B,oBAAoB,IAAI,EAAE;oBAC1B;gBACJ;YACA,KAAK,cAAc,MAAM;gBAAE;oBACvB,cAAc,IAAI,EAAE;oBACpB;gBACJ;YACA,KAAK,cAAc,OAAO;gBAAE;oBACxB,eAAe,IAAI,EAAE;oBACrB;gBACJ;YACA,KAAK,cAAc,SAAS;gBAAE;oBAC1B,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,kBAAkB;gBAAE;oBACnC,wBAAwB,IAAI,EAAE;oBAC9B;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,mBAAmB,IAAI,EAAE;oBACzB;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,kBAAkB,IAAI,EAAE;oBACxB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,mBAAmB,IAAI,EAAE;oBACzB;gBACJ;YACA,KAAK,cAAc,cAAc;gBAAE;oBAC/B,sBAAsB,IAAI,EAAE;oBAC5B;gBACJ;YACA,KAAK,cAAc,gBAAgB;gBAAE;oBACjC,uBAAuB,IAAI,EAAE;oBAC7B;gBACJ;YACA,KAAK,cAAc,oBAAoB;gBAAE;oBACrC,2BAA2B,IAAI,EAAE;oBACjC;gBACJ;YACA;QAEJ;IACJ;IACA,cAAc,GACd,SAAS,KAAK,EAAE;QACZ,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,uBAAuB,IAAI,EAAE;QACjC,OACK;YACD,IAAI,CAAC,4BAA4B,CAAC;QACtC;IACJ;IACA,eAAe,GACf,6BAA6B,KAAK,EAAE;QAChC,OAAQ,IAAI,CAAC,aAAa;YACtB,KAAK,cAAc,OAAO;gBAAE;oBACxB,mBAAmB,IAAI,EAAE;oBACzB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,OAAO;gBAAE;oBACxB,aAAa,IAAI,EAAE;oBACnB;gBACJ;YACA,KAAK,cAAc,iBAAiB;gBAAE;oBAClC,qBAAqB,IAAI,EAAE;oBAC3B;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,gBAAgB,IAAI,EAAE;oBACtB;gBACJ;YACA,KAAK,cAAc,OAAO;gBAAE;oBACxB,aAAa,IAAI,EAAE;oBACnB;gBACJ;YACA,KAAK,cAAc,IAAI;gBAAE;oBACrB,aAAa,IAAI,EAAE;oBACnB;gBACJ;YACA,KAAK,cAAc,QAAQ;gBAAE;oBACzB,cAAc,IAAI,EAAE;oBACpB;gBACJ;YACA,KAAK,cAAc,aAAa;gBAAE;oBAC9B,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,gBAAgB,IAAI,EAAE;oBACtB;gBACJ;YACA,KAAK,cAAc,eAAe;gBAAE;oBAChC,oBAAoB,IAAI,EAAE;oBAC1B;gBACJ;YACA,KAAK,cAAc,aAAa;gBAAE;oBAC9B,kBAAkB,IAAI,EAAE;oBACxB;gBACJ;YACA,KAAK,cAAc,MAAM;gBAAE;oBACvB,YAAY,IAAI,EAAE;oBAClB;gBACJ;YACA,KAAK,cAAc,OAAO;gBAAE;oBACxB,aAAa,IAAI,EAAE;oBACnB;gBACJ;YACA,KAAK,cAAc,SAAS;gBAAE;oBAC1B,eAAe,IAAI,EAAE;oBACrB;gBACJ;YACA,KAAK,cAAc,kBAAkB;gBAAE;oBACnC,sBAAsB,IAAI,EAAE;oBAC5B;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,gBAAgB,IAAI,EAAE;oBACtB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,cAAc;gBAAE;oBAC/B,oBAAoB,IAAI,EAAE;oBAC1B;gBACJ;YACA,KAAK,cAAc,gBAAgB;gBAAE;oBACjC,oBAAoB,IAAI,EAAE;oBAC1B;gBACJ;YACA;QAEJ;IACJ;IACA,cAAc,GACd,MAAM,KAAK,EAAE;QACT,OAAQ,IAAI,CAAC,aAAa;YACtB,KAAK,cAAc,OAAO;gBAAE;oBACxB,mBAAmB,IAAI,EAAE;oBACzB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,gBAAgB,IAAI,EAAE;oBACtB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,gBAAgB,IAAI,EAAE;oBACtB;gBACJ;YACA,KAAK,cAAc,OAAO;gBAAE;oBACxB,YAAY,IAAI,EAAE;oBAClB;gBACJ;YACA,KAAK,cAAc,iBAAiB;gBAAE;oBAClC,oBAAoB,IAAI,EAAE;oBAC1B;gBACJ;YACA,KAAK,cAAc,UAAU;gBAAE;oBAC3B,eAAe,IAAI,EAAE;oBACrB;gBACJ;YACA,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,QAAQ;YAC3B,KAAK,cAAc,UAAU;YAC7B,KAAK,cAAc,eAAe;YAClC,KAAK,cAAc,aAAa;YAChC,KAAK,cAAc,MAAM;YACzB,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,SAAS;YAC5B,KAAK,cAAc,kBAAkB;gBAAE;oBACnC,UAAU,IAAI,EAAE;oBAChB;gBACJ;YACA,KAAK,cAAc,IAAI;gBAAE;oBACrB,UAAU,IAAI,EAAE;oBAChB;gBACJ;YACA,KAAK,cAAc,aAAa;gBAAE;oBAC9B,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,WAAW;gBAAE;oBAC5B,cAAc,IAAI,EAAE;oBACpB;gBACJ;YACA,KAAK,cAAc,UAAU;YAC7B,KAAK,cAAc,WAAW;YAC9B,KAAK,cAAc,cAAc;YACjC,KAAK,cAAc,gBAAgB;YACnC,KAAK,cAAc,oBAAoB;gBAAE;oBACrC,YAAY,IAAI,EAAE;oBAClB;gBACJ;YACA;QAEJ;IACJ;IACA,cAAc,GACd,sBAAsB,KAAK,EAAE;QACzB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,MAAM,KAAK,CAAC,UAAU,CAAC,OAAO,mJAAA,CAAA,cAAmB,CAAC,SAAS,EAAE;gBAC7D,IAAI,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG;oBAC1B;gBACJ;gBACA,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC;YACrC;QACJ;QACA,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE;YAC9B,IAAI,CAAC,iBAAiB,CAAC;YACvB;QACJ;QACA,OAAQ,IAAI,CAAC,aAAa;YACtB,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,iBAAiB;YACpC,KAAK,cAAc,UAAU;YAC7B,KAAK,cAAc,IAAI;YACvB,KAAK,cAAc,eAAe;YAClC,KAAK,cAAc,SAAS;YAC5B,KAAK,cAAc,kBAAkB;YACrC,KAAK,cAAc,WAAW;YAC9B,KAAK,cAAc,cAAc;gBAAE;oBAC/B,IAAI,CAAC,iBAAiB,CAAC;oBACvB;gBACJ;YACA,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,UAAU;YAC7B,KAAK,cAAc,OAAO;YAC1B,KAAK,cAAc,WAAW;YAC9B,KAAK,cAAc,UAAU;YAC7B,KAAK,cAAc,gBAAgB;YACnC,KAAK,cAAc,oBAAoB;gBAAE;oBACrC,0BAA0B,IAAI,EAAE;oBAChC;gBACJ;YACA,KAAK,cAAc,QAAQ;YAC3B,KAAK,cAAc,aAAa;YAChC,KAAK,cAAc,MAAM;gBAAE;oBACvB,iBAAiB,IAAI,EAAE;oBACvB;gBACJ;YACA,KAAK,cAAc,aAAa;gBAAE;oBAC9B,+BAA+B,IAAI,EAAE;oBACrC;gBACJ;YACA;QAEJ;IACJ;AACJ;AACA,2BAA2B;AAC3B,0GAA0G;AAC1G,oEAAoE;AACpE,4BAA4B;AAC5B,SAAS,+BAA+B,CAAC,EAAE,KAAK;IAC5C,IAAI,yBAAyB,EAAE,wBAAwB,CAAC,iCAAiC,CAAC,MAAM,OAAO;IACvG,IAAI,wBAAwB;QACxB,IAAI,CAAC,EAAE,YAAY,CAAC,QAAQ,CAAC,uBAAuB,OAAO,GAAG;YAC1D,EAAE,wBAAwB,CAAC,WAAW,CAAC;YACvC,yBAAyB;QAC7B,OACK,IAAI,CAAC,EAAE,YAAY,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;YAC9C,yBAAyB;QAC7B;IACJ,OACK;QACD,oBAAoB,GAAG;IAC3B;IACA,OAAO;AACX;AACA,iCAAiC;AACjC,SAAS,sBAAsB,CAAC,EAAE,sBAAsB;IACpD,IAAI,gBAAgB;IACpB,IAAI,MAAM,EAAE,YAAY,CAAC,QAAQ;IACjC,MAAO,OAAO,GAAG,MAAO;QACpB,MAAM,UAAU,EAAE,YAAY,CAAC,KAAK,CAAC,IAAI;QACzC,IAAI,YAAY,uBAAuB,OAAO,EAAE;YAC5C;QACJ;QACA,IAAI,EAAE,iBAAiB,CAAC,SAAS,EAAE,YAAY,CAAC,MAAM,CAAC,IAAI,GAAG;YAC1D,gBAAgB;QACpB;IACJ;IACA,IAAI,CAAC,eAAe;QAChB,EAAE,YAAY,CAAC,eAAe,CAAC,KAAK,GAAG,CAAC,KAAK;QAC7C,EAAE,wBAAwB,CAAC,WAAW,CAAC;IAC3C;IACA,OAAO;AACX;AACA,0BAA0B;AAC1B,SAAS,YAAY,CAAC,EAAE,aAAa,EAAE,iBAAiB;IACpD,IAAI,cAAc;IAClB,IAAI,cAAc,EAAE,YAAY,CAAC,iBAAiB,CAAC;IACnD,IAAK,IAAI,IAAI,GAAG,UAAU,aAAa,YAAY,mBAAmB,KAAK,UAAU,YAAa;QAC9F,yGAAyG;QACzG,cAAc,EAAE,YAAY,CAAC,iBAAiB,CAAC;QAC/C,MAAM,eAAe,EAAE,wBAAwB,CAAC,eAAe,CAAC;QAChE,MAAM,kBAAkB,gBAAgB,KAAK;QAC7C,MAAM,+BAA+B,CAAC,gBAAgB;QACtD,IAAI,8BAA8B;YAC9B,IAAI,iBAAiB;gBACjB,EAAE,wBAAwB,CAAC,WAAW,CAAC;YAC3C;YACA,EAAE,YAAY,CAAC,MAAM,CAAC;QAC1B,OACK;YACD,UAAU,2BAA2B,GAAG;YACxC,IAAI,gBAAgB,eAAe;gBAC/B,EAAE,wBAAwB,CAAC,QAAQ,GAAG;YAC1C;YACA,EAAE,WAAW,CAAC,UAAU,CAAC;YACzB,EAAE,WAAW,CAAC,WAAW,CAAC,SAAS;YACnC,cAAc;QAClB;IACJ;IACA,OAAO;AACX;AACA,4BAA4B;AAC5B,SAAS,2BAA2B,CAAC,EAAE,YAAY;IAC/C,MAAM,KAAK,EAAE,WAAW,CAAC,eAAe,CAAC,aAAa,OAAO;IAC7D,MAAM,aAAa,EAAE,WAAW,CAAC,aAAa,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,IAAI,aAAa,KAAK,CAAC,KAAK;IACvG,EAAE,YAAY,CAAC,OAAO,CAAC,aAAa,OAAO,EAAE;IAC7C,aAAa,OAAO,GAAG;IACvB,OAAO;AACX;AACA,0BAA0B;AAC1B,SAAS,iCAAiC,CAAC,EAAE,cAAc,EAAE,WAAW;IACpE,MAAM,KAAK,EAAE,WAAW,CAAC,UAAU,CAAC;IACpC,MAAM,MAAM,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EAAE;IACrB,IAAI,EAAE,+BAA+B,CAAC,MAAM;QACxC,EAAE,oBAAoB,CAAC;IAC3B,OACK;QACD,MAAM,KAAK,EAAE,WAAW,CAAC,eAAe,CAAC;QACzC,IAAI,QAAQ,gJAAA,CAAA,SAAC,CAAC,QAAQ,IAAI,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE;YACtC,iBAAiB,EAAE,WAAW,CAAC,kBAAkB,CAAC;QACtD;QACA,EAAE,WAAW,CAAC,WAAW,CAAC,gBAAgB;IAC9C;AACJ;AACA,8BAA8B;AAC9B,SAAS,2BAA2B,CAAC,EAAE,aAAa,EAAE,sBAAsB;IACxE,MAAM,KAAK,EAAE,WAAW,CAAC,eAAe,CAAC,uBAAuB,OAAO;IACvE,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,MAAM,aAAa,EAAE,WAAW,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,IAAI,MAAM,KAAK;IAC7E,EAAE,WAAW,CAAC,eAAe;IAC7B,EAAE,WAAW,CAAC,WAAW,CAAC,eAAe;IACzC,EAAE,wBAAwB,CAAC,0BAA0B,CAAC,YAAY;IAClE,EAAE,wBAAwB,CAAC,WAAW,CAAC;IACvC,EAAE,YAAY,CAAC,MAAM,CAAC,uBAAuB,OAAO;IACpD,EAAE,YAAY,CAAC,WAAW,CAAC,eAAe,YAAY,MAAM,KAAK;AACrE;AACA,uBAAuB;AACvB,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,IAAK;QACzC,MAAM,yBAAyB,+BAA+B,GAAG;QACjE,IAAI,CAAC,wBAAwB;YACzB;QACJ;QACA,MAAM,gBAAgB,sBAAsB,GAAG;QAC/C,IAAI,CAAC,eAAe;YAChB;QACJ;QACA,EAAE,wBAAwB,CAAC,QAAQ,GAAG;QACtC,MAAM,cAAc,YAAY,GAAG,eAAe,uBAAuB,OAAO;QAChF,MAAM,iBAAiB,EAAE,YAAY,CAAC,iBAAiB,CAAC,uBAAuB,OAAO;QACtF,EAAE,WAAW,CAAC,UAAU,CAAC;QACzB,IAAI,gBACA,iCAAiC,GAAG,gBAAgB;QACxD,2BAA2B,GAAG,eAAe;IACjD;AACJ;AACA,wBAAwB;AACxB,oEAAoE;AACpE,SAAS,cAAc,CAAC,EAAE,KAAK;IAC3B,EAAE,kBAAkB,CAAC,OAAO,EAAE,YAAY,CAAC,wBAAwB;AACvE;AACA,SAAS,+BAA+B,CAAC,EAAE,KAAK;IAC5C,EAAE,kBAAkB,CAAC,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;AACvD;AACA,SAAS,wBAAwB,CAAC,EAAE,KAAK;IACrC,EAAE,kBAAkB,CAAC,OAAO,EAAE,QAAQ;AAC1C;AACA,SAAS,YAAY,CAAC,EAAE,KAAK;IACzB,EAAE,OAAO,GAAG;IACZ,8EAA8E;IAC9E,IAAI,MAAM,QAAQ,EAAE;QAChB,+EAA+E;QAC/E,oEAAoE;QACpE,MAAM,SAAS,EAAE,eAAe,GAAG,IAAI;QACvC,IAAK,IAAI,IAAI,EAAE,YAAY,CAAC,QAAQ,EAAE,KAAK,QAAQ,IAAK;YACpD,EAAE,eAAe,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;QAC/C;QACA,2BAA2B;QAC3B,IAAI,CAAC,EAAE,eAAe,IAAI,EAAE,YAAY,CAAC,QAAQ,IAAI,GAAG;YACpD,MAAM,cAAc,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;YAC3C,MAAM,eAAe,EAAE,WAAW,CAAC,yBAAyB,CAAC;YAC7D,IAAI,gBAAgB,CAAC,aAAa,MAAM,EAAE;gBACtC,EAAE,eAAe,CAAC,aAAa;gBAC/B,IAAI,EAAE,YAAY,CAAC,QAAQ,IAAI,GAAG;oBAC9B,MAAM,cAAc,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;oBAC3C,MAAM,eAAe,EAAE,WAAW,CAAC,yBAAyB,CAAC;oBAC7D,IAAI,gBAAgB,CAAC,aAAa,MAAM,EAAE;wBACtC,EAAE,eAAe,CAAC,aAAa;oBACnC;gBACJ;YACJ;QACJ;IACJ;AACJ;AACA,+BAA+B;AAC/B,oEAAoE;AACpE,SAAS,qBAAqB,CAAC,EAAE,KAAK;IAClC,EAAE,gBAAgB,CAAC;IACnB,MAAM,OAAO,MAAM,WAAW,GAAG,gJAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,CAAA,GAAA,mJAAA,CAAA,kBAAuB,AAAD,EAAE;IAChF,IAAI,CAAC,CAAA,GAAA,mJAAA,CAAA,eAAoB,AAAD,EAAE,QAAQ;QAC9B,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,oBAAoB;IAC1C;IACA,EAAE,WAAW,CAAC,eAAe,CAAC,EAAE,QAAQ,EAAE;IAC1C,EAAE,aAAa,GAAG,cAAc,WAAW;AAC/C;AACA,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,cAAc,EAAE;IAClC,EAAE,WAAW,CAAC,eAAe,CAAC,EAAE,QAAQ,EAAE,gJAAA,CAAA,gBAAa,CAAC,MAAM;IAC9D,EAAE,aAAa,GAAG,cAAc,WAAW;IAC3C,EAAE,aAAa,CAAC;AACpB;AACA,mCAAmC;AACnC,oEAAoE;AACpE,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI,EAAE;QACxB,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;QAC/B,EAAE,aAAa,GAAG,cAAc,WAAW;IAC/C,OACK;QACD,gBAAgB,GAAG;IACvB;AACJ;AACA,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,MAAM,KAAK,MAAM,KAAK;IACtB,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,IAAI,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,IAAI,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,IAAI,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,EAAE;QAChE,gBAAgB,GAAG;IACvB;AACJ;AACA,SAAS,gBAAgB,CAAC,EAAE,KAAK;IAC7B,EAAE,sBAAsB;IACxB,EAAE,aAAa,GAAG,cAAc,WAAW;IAC3C,EAAE,aAAa,CAAC;AACpB;AACA,mCAAmC;AACnC,oEAAoE;AACpE,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B,EAAE,WAAW,GAAG,EAAE,YAAY,CAAC,OAAO;gBACtC,EAAE,aAAa,GAAG,cAAc,OAAO;gBACvC;YACJ;QACA;YAAS;gBACL,gBAAgB,GAAG;YACvB;IACJ;AACJ;AACA,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,MAAM,KAAK,MAAM,KAAK;IACtB,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,IAAI,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,IAAI,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,IAAI,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,EAAE;QAChE,gBAAgB,GAAG;IACvB,OACK;QACD,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,gCAAgC;IACtD;AACJ;AACA,SAAS,gBAAgB,CAAC,EAAE,KAAK;IAC7B,EAAE,kBAAkB,CAAC,gJAAA,CAAA,YAAE,CAAC,IAAI,EAAE,gJAAA,CAAA,SAAC,CAAC,IAAI;IACpC,EAAE,WAAW,GAAG,EAAE,YAAY,CAAC,OAAO;IACtC,EAAE,aAAa,GAAG,cAAc,OAAO;IACvC,EAAE,aAAa,CAAC;AACpB;AACA,+BAA+B;AAC/B,oEAAoE;AACpE,SAAS,eAAe,CAAC,EAAE,KAAK;IAC5B,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B,MAAM,cAAc,GAAG;gBACvB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,EAAE,oBAAoB,CAAC,OAAO,oJAAA,CAAA,gBAAa,CAAC,MAAM;gBAClD;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,IAAI,EAAE,OAAO,CAAC,gBAAgB,EAAE;oBAC5B,EAAE,oBAAoB,CAAC,OAAO,oJAAA,CAAA,gBAAa,CAAC,OAAO;gBACvD,OACK;oBACD,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;oBAC/B,EAAE,aAAa,GAAG,cAAc,iBAAiB;gBACrD;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,EAAE,oBAAoB,CAAC,OAAO,oJAAA,CAAA,gBAAa,CAAC,OAAO;gBACnD;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,EAAE,oBAAoB,CAAC,OAAO,oJAAA,CAAA,gBAAa,CAAC,WAAW;gBACvD;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,EAAE,eAAe,CAAC;gBAClB,EAAE,wBAAwB,CAAC,YAAY;gBACvC,EAAE,UAAU,GAAG;gBACf,EAAE,aAAa,GAAG,cAAc,WAAW;gBAC3C,EAAE,sBAAsB,CAAC,OAAO,CAAC,cAAc,WAAW;gBAC1D;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,+BAA+B;gBACjD;YACJ;QACA;YAAS;gBACL,YAAY,GAAG;YACnB;IACJ;AACJ;AACA,SAAS,aAAa,CAAC,EAAE,KAAK;IAC1B,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,EAAE,YAAY,CAAC,GAAG;gBAClB,EAAE,aAAa,GAAG,cAAc,UAAU;gBAC1C;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,YAAY,GAAG;gBACf;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,qBAAqB,GAAG;gBACxB;YACJ;QACA;YAAS;gBACL,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,gCAAgC;YACtD;IACJ;AACJ;AACA,SAAS,qBAAqB,CAAC,EAAE,KAAK;IAClC,IAAI,EAAE,YAAY,CAAC,SAAS,GAAG,GAAG;QAC9B,EAAE,YAAY,CAAC,gCAAgC;QAC/C,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,EAAE;YAC5C,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,qCAAqC;QAC3D;QACA,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,QAAQ;QAC/C,EAAE,wBAAwB,CAAC,iBAAiB;QAC5C,EAAE,sBAAsB,CAAC,KAAK;QAC9B,EAAE,mBAAmB;IACzB,OACK;QACD,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,gCAAgC;IACtD;AACJ;AACA,SAAS,YAAY,CAAC,EAAE,KAAK;IACzB,EAAE,YAAY,CAAC,GAAG;IAClB,EAAE,aAAa,GAAG,cAAc,UAAU;IAC1C,EAAE,aAAa,CAAC;AACpB;AACA,yCAAyC;AACzC,oEAAoE;AACpE,SAAS,uBAAuB,CAAC,EAAE,KAAK;IACpC,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,oBAAoB;gBACtC;YACJ;QACA;YAAS;gBACL,oBAAoB,GAAG;YAC3B;IACJ;AACJ;AACA,SAAS,qBAAqB,CAAC,EAAE,KAAK;IAClC,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,EAAE,YAAY,CAAC,GAAG;gBAClB,EAAE,aAAa,GAAG,cAAc,OAAO;gBACvC;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,oBAAoB,GAAG;gBACvB;YACJ;QACA;YAAS;gBACL,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,gCAAgC;YACtD;IACJ;AACJ;AACA,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,MAAM,UAAU,MAAM,IAAI,KAAK,iJAAA,CAAA,YAAS,CAAC,GAAG,GAAG,0JAAA,CAAA,MAAG,CAAC,wBAAwB,GAAG,0JAAA,CAAA,MAAG,CAAC,iCAAiC;IACnH,EAAE,IAAI,CAAC,OAAO;IACd,EAAE,YAAY,CAAC,GAAG;IAClB,EAAE,aAAa,GAAG,cAAc,OAAO;IACvC,EAAE,aAAa,CAAC;AACpB;AACA,kCAAkC;AAClC,oEAAoE;AACpE,SAAS,kBAAkB,CAAC,EAAE,KAAK;IAC/B,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B,EAAE,UAAU,GAAG;gBACf,EAAE,aAAa,GAAG,cAAc,OAAO;gBACvC;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B,EAAE,aAAa,GAAG,cAAc,WAAW;gBAC3C;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,yBAAyB;gBAC3C,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,gJAAA,CAAA,SAAC,CAAC,IAAI;gBACzC,eAAe,GAAG;gBAClB,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE,WAAW;gBACnC;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,+BAA+B;gBACjD;YACJ;QACA;YAAS;gBACL,eAAe,GAAG;YACtB;IACJ;AACJ;AACA,SAAS,gBAAgB,CAAC,EAAE,KAAK;IAC7B,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,qBAAqB,GAAG;gBACxB;YACJ;QACA;YAAS;gBACL,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,gCAAgC;YACtD;IACJ;AACJ;AACA,SAAS,eAAe,CAAC,EAAE,KAAK;IAC5B,EAAE,kBAAkB,CAAC,gJAAA,CAAA,YAAE,CAAC,IAAI,EAAE,gJAAA,CAAA,SAAC,CAAC,IAAI;IACpC,EAAE,aAAa,GAAG,cAAc,OAAO;IACvC,WAAW,GAAG;AAClB;AACA,+BAA+B;AAC/B,oEAAoE;AACpE,SAAS,WAAW,CAAC,EAAE,KAAK;IACxB,OAAQ,MAAM,IAAI;QACd,KAAK,iJAAA,CAAA,YAAS,CAAC,SAAS;YAAE;gBACtB,gBAAgB,GAAG;gBACnB;YACJ;QACA,KAAK,iJAAA,CAAA,YAAS,CAAC,oBAAoB;YAAE;gBACjC,0BAA0B,GAAG;gBAC7B;YACJ;QACA,KAAK,iJAAA,CAAA,YAAS,CAAC,OAAO;YAAE;gBACpB,cAAc,GAAG;gBACjB;YACJ;QACA,KAAK,iJAAA,CAAA,YAAS,CAAC,SAAS;YAAE;gBACtB,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,iJAAA,CAAA,YAAS,CAAC,OAAO;YAAE;gBACpB,aAAa,GAAG;gBAChB;YACJ;QACA,KAAK,iJAAA,CAAA,YAAS,CAAC,GAAG;YAAE;gBAChB,UAAU,GAAG;gBACb;YACJ;QACA;IAEJ;AACJ;AACA,SAAS,0BAA0B,CAAC,EAAE,KAAK;IACvC,EAAE,oCAAoC;IACtC,EAAE,iBAAiB,CAAC;AACxB;AACA,SAAS,gBAAgB,CAAC,EAAE,KAAK;IAC7B,EAAE,oCAAoC;IACtC,EAAE,iBAAiB,CAAC;IACpB,EAAE,UAAU,GAAG;AACnB;AACA,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,IAAI,EAAE,YAAY,CAAC,SAAS,KAAK,GAAG;QAChC,EAAE,WAAW,CAAC,eAAe,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,KAAK;IACtE;AACJ;AACA,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,MAAM,cAAc,EAAE,YAAY,CAAC,gCAAgC;IACnE,IAAI,eAAe,EAAE,YAAY,CAAC,SAAS,KAAK,GAAG;QAC/C,EAAE,UAAU,GAAG;QACf,EAAE,WAAW,CAAC,eAAe,CAAC,aAAa,MAAM,KAAK;IAC1D;AACJ;AACA,SAAS,uBAAuB,CAAC,EAAE,KAAK;IACpC,MAAM,cAAc,EAAE,YAAY,CAAC,gCAAgC;IACnE,IAAI,EAAE,UAAU,IAAI,aAAa;QAC7B,EAAE,WAAW,CAAC,UAAU,CAAC;QACzB,EAAE,YAAY,CAAC,qBAAqB;QACpC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;QAC/B,EAAE,aAAa,GAAG,cAAc,WAAW;IAC/C;AACJ;AACA,SAAS,sBAAsB,CAAC,EAAE,KAAK;IACnC,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC,GAAG;QACtC,EAAE,cAAc;IACpB;IACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;AACnC;AACA,SAAS,6BAA6B,CAAC,EAAE,KAAK;IAC1C,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC,GAAG;QACtC,EAAE,cAAc;IACpB;IACA,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,aAAa,gJAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC,YAAY,GAAG;QAChG,EAAE,YAAY,CAAC,GAAG;IACtB;IACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;AACnC;AACA,SAAS,kBAAkB,CAAC,EAAE,KAAK;IAC/B,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC,GAAG;QACtC,EAAE,cAAc;IACpB;IACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,qGAAqG;IACrG,oGAAoG;IACpG,EAAE,eAAe,GAAG;IACpB,EAAE,UAAU,GAAG;AACnB;AACA,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,MAAM,aAAa,EAAE,YAAY,CAAC,SAAS,GAAG;IAC9C,IAAI,CAAC,EAAE,WAAW,IAAI,YAAY;QAC9B,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC,GAAG;YACtC,EAAE,cAAc;QACpB;QACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;QAC/B,IAAI,CAAC,YAAY;YACb,EAAE,WAAW,GAAG,EAAE,YAAY,CAAC,OAAO;QAC1C;IACJ;AACJ;AACA,SAAS,uBAAuB,CAAC,EAAE,KAAK;IACpC,EAAE,UAAU,GAAG;IACf,MAAM,KAAK,MAAM,KAAK;IACtB,IAAK,IAAI,IAAI,EAAE,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAK;QAC/C,MAAM,YAAY,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE;QAC1C,IAAI,AAAC,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,IAAI,cAAc,gJAAA,CAAA,SAAC,CAAC,EAAE,IACjC,CAAC,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,KAAK,CAAC,cAAc,gJAAA,CAAA,SAAC,CAAC,EAAE,IAAI,cAAc,gJAAA,CAAA,SAAC,CAAC,EAAE,GAAI;YAC9E,EAAE,YAAY,CAAC,mCAAmC,CAAC;YACnD,EAAE,YAAY,CAAC,qBAAqB,CAAC;YACrC;QACJ;QACA,IAAI,cAAc,gJAAA,CAAA,SAAC,CAAC,OAAO,IACvB,cAAc,gJAAA,CAAA,SAAC,CAAC,GAAG,IACnB,cAAc,gJAAA,CAAA,SAAC,CAAC,CAAC,IACjB,EAAE,iBAAiB,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE,YAAY;YACzD;QACJ;IACJ;IACA,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC,GAAG;QACtC,EAAE,cAAc;IACpB;IACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;AACnC;AACA,SAAS,wBAAwB,CAAC,EAAE,KAAK;IACrC,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC,GAAG;QACtC,EAAE,cAAc;IACpB;IACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,SAAS,CAAC,KAAK,GAAG,oJAAA,CAAA,gBAAa,CAAC,SAAS;AAC/C;AACA,SAAS,qBAAqB,CAAC,EAAE,KAAK;IAClC,IAAI,EAAE,YAAY,CAAC,UAAU,CAAC,gJAAA,CAAA,SAAC,CAAC,MAAM,GAAG;QACrC,EAAE,YAAY,CAAC,sBAAsB;QACrC,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,MAAM;IACjD;IACA,EAAE,oCAAoC;IACtC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,UAAU,GAAG;AACnB;AACA,SAAS,gBAAgB,CAAC,EAAE,KAAK;IAC7B,MAAM,qBAAqB,EAAE,wBAAwB,CAAC,iCAAiC,CAAC,gJAAA,CAAA,YAAE,CAAC,CAAC;IAC5F,IAAI,oBAAoB;QACpB,mBAAmB,GAAG;QACtB,EAAE,YAAY,CAAC,MAAM,CAAC,mBAAmB,OAAO;QAChD,EAAE,wBAAwB,CAAC,WAAW,CAAC;IAC3C;IACA,EAAE,oCAAoC;IACtC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,wBAAwB,CAAC,WAAW,CAAC,EAAE,YAAY,CAAC,OAAO,EAAE;AACnE;AACA,SAAS,gBAAgB,CAAC,EAAE,KAAK;IAC7B,EAAE,oCAAoC;IACtC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,wBAAwB,CAAC,WAAW,CAAC,EAAE,YAAY,CAAC,OAAO,EAAE;AACnE;AACA,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,EAAE,oCAAoC;IACtC,IAAI,EAAE,YAAY,CAAC,UAAU,CAAC,gJAAA,CAAA,SAAC,CAAC,IAAI,GAAG;QACnC,mBAAmB,GAAG;QACtB,EAAE,oCAAoC;IAC1C;IACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,wBAAwB,CAAC,WAAW,CAAC,EAAE,YAAY,CAAC,OAAO,EAAE;AACnE;AACA,SAAS,qBAAqB,CAAC,EAAE,KAAK;IAClC,EAAE,oCAAoC;IACtC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,wBAAwB,CAAC,YAAY;IACvC,EAAE,UAAU,GAAG;AACnB;AACA,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,IAAI,EAAE,WAAW,CAAC,eAAe,CAAC,EAAE,QAAQ,MAAM,gJAAA,CAAA,gBAAa,CAAC,MAAM,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC,GAAG;QAC5G,EAAE,cAAc;IACpB;IACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,UAAU,GAAG;IACf,EAAE,aAAa,GAAG,cAAc,QAAQ;AAC5C;AACA,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,EAAE,oCAAoC;IACtC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,UAAU,GAAG;IACf,MAAM,cAAc,GAAG;AAC3B;AACA,SAAS,cAAc,KAAK;IACxB,MAAM,YAAY,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,OAAO,gJAAA,CAAA,QAAK,CAAC,IAAI;IAChD,OAAO,aAAa,QAAQ,UAAU,WAAW,OAAO;AAC5D;AACA,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,EAAE,oCAAoC;IACtC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,IAAI,CAAC,cAAc,QAAQ;QACvB,EAAE,UAAU,GAAG;IACnB;IACA,MAAM,cAAc,GAAG;AAC3B;AACA,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,MAAM,cAAc,GAAG;AAC3B;AACA,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC,GAAG;QACtC,EAAE,cAAc;IACpB;IACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,UAAU,GAAG;IACf,MAAM,cAAc,GAAG;AAC3B;AACA,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,MAAM,OAAO,GAAG,gJAAA,CAAA,YAAE,CAAC,GAAG;IACtB,MAAM,KAAK,GAAG,gJAAA,CAAA,SAAC,CAAC,GAAG;IACnB,mBAAmB,GAAG;AAC1B;AACA,SAAS,uBAAuB,CAAC,EAAE,KAAK;IACpC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,qGAAqG;IACrG,2GAA2G;IAC3G,EAAE,eAAe,GAAG;IACpB,EAAE,SAAS,CAAC,KAAK,GAAG,oJAAA,CAAA,gBAAa,CAAC,MAAM;IACxC,EAAE,qBAAqB,GAAG,EAAE,aAAa;IACzC,EAAE,UAAU,GAAG;IACf,EAAE,aAAa,GAAG,cAAc,IAAI;AACxC;AACA,SAAS,kBAAkB,CAAC,EAAE,KAAK;IAC/B,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC,GAAG;QACtC,EAAE,cAAc;IACpB;IACA,EAAE,oCAAoC;IACtC,EAAE,UAAU,GAAG;IACf,EAAE,oBAAoB,CAAC,OAAO,oJAAA,CAAA,gBAAa,CAAC,OAAO;AACvD;AACA,SAAS,qBAAqB,CAAC,EAAE,KAAK;IAClC,EAAE,UAAU,GAAG;IACf,EAAE,oBAAoB,CAAC,OAAO,oJAAA,CAAA,gBAAa,CAAC,OAAO;AACvD;AACA,kGAAkG;AAClG,kCAAkC;AAClC,SAAS,sBAAsB,CAAC,EAAE,KAAK;IACnC,EAAE,oBAAoB,CAAC,OAAO,oJAAA,CAAA,gBAAa,CAAC,OAAO;AACvD;AACA,SAAS,qBAAqB,CAAC,EAAE,KAAK;IAClC,EAAE,oCAAoC;IACtC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,UAAU,GAAG;IACf,EAAE,aAAa,GACX,EAAE,aAAa,KAAK,cAAc,QAAQ,IACtC,EAAE,aAAa,KAAK,cAAc,UAAU,IAC5C,EAAE,aAAa,KAAK,cAAc,aAAa,IAC/C,EAAE,aAAa,KAAK,cAAc,MAAM,IACxC,EAAE,aAAa,KAAK,cAAc,OAAO,GACvC,cAAc,kBAAkB,GAChC,cAAc,SAAS;AACrC;AACA,SAAS,uBAAuB,CAAC,EAAE,KAAK;IACpC,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM,EAAE;QAC1C,EAAE,YAAY,CAAC,GAAG;IACtB;IACA,EAAE,oCAAoC;IACtC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;AACnC;AACA,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,IAAI,EAAE,YAAY,CAAC,UAAU,CAAC,gJAAA,CAAA,SAAC,CAAC,IAAI,GAAG;QACnC,EAAE,YAAY,CAAC,sBAAsB;IACzC;IACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;AACnC;AACA,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,IAAI,EAAE,YAAY,CAAC,UAAU,CAAC,gJAAA,CAAA,SAAC,CAAC,IAAI,GAAG;QACnC,EAAE,YAAY,CAAC,mCAAmC,CAAC,gJAAA,CAAA,SAAC,CAAC,GAAG;IAC5D;IACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;AACnC;AACA,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,EAAE,oCAAoC;IACtC,CAAA,GAAA,8JAAA,CAAA,yBAAqC,AAAD,EAAE;IACtC,CAAA,GAAA,8JAAA,CAAA,sBAAkC,AAAD,EAAE;IACnC,IAAI,MAAM,WAAW,EAAE;QACnB,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,MAAM;IACrC,OACK;QACD,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,MAAM;IACrC;IACA,MAAM,cAAc,GAAG;AAC3B;AACA,SAAS,kBAAkB,CAAC,EAAE,KAAK;IAC/B,EAAE,oCAAoC;IACtC,CAAA,GAAA,8JAAA,CAAA,sBAAkC,AAAD,EAAE;IACnC,CAAA,GAAA,8JAAA,CAAA,sBAAkC,AAAD,EAAE;IACnC,IAAI,MAAM,WAAW,EAAE;QACnB,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,GAAG;IAClC,OACK;QACD,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,GAAG;IAClC;IACA,MAAM,cAAc,GAAG;AAC3B;AACA,SAAS,sBAAsB,CAAC,EAAE,KAAK;IACnC,EAAE,oCAAoC;IACtC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;AACnC;AACA,SAAS,eAAe,CAAC,EAAE,KAAK;IAC5B,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;QACR,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;QACR,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;QACR,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;QACR,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,gBAAgB,GAAG;gBACnB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;YAAE;gBACN,gBAAgB,GAAG;gBACnB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,6BAA6B,GAAG;gBAChC;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;QACR,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,UAAU;QACjB,KAAK,gJAAA,CAAA,SAAC,CAAC,UAAU;YAAE;gBACf,sBAAsB,GAAG;gBACzB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,uBAAuB,GAAG;gBAC1B;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,mBAAmB,GAAG;gBACtB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,iBAAiB,GAAG;gBACpB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;YAAE;gBACR,iBAAiB,GAAG;gBACpB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,iBAAiB,GAAG;gBACpB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;YAAE;gBACZ,kBAAkB,GAAG;gBACrB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;YAAE;gBACR,kBAAkB,GAAG;gBACrB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;YAAE;gBACR,kBAAkB,GAAG;gBACrB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,mBAAmB,GAAG;gBACtB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,mBAAmB,GAAG;gBACtB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,mBAAmB,GAAG;gBACtB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,mBAAmB,GAAG;gBACtB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,mBAAmB,GAAG;gBACtB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,oBAAoB,GAAG;gBACvB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,oBAAoB,GAAG;gBACvB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,oBAAoB,GAAG;gBACvB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,oBAAoB,GAAG;gBACvB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,qBAAqB,GAAG;gBACxB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;YAAE;gBACZ,qBAAqB,GAAG;gBACxB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,qBAAqB,GAAG;gBACxB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,qBAAqB,GAAG;gBACxB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,uBAAuB,GAAG;gBAC1B;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,sBAAsB,GAAG;gBACzB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,uBAAuB,GAAG;gBAC1B;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,uBAAuB,GAAG;gBAC1B;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,IAAI,EAAE,OAAO,CAAC,gBAAgB,EAAE;oBAC5B,sBAAsB,GAAG;gBAC7B,OACK;oBACD,sBAAsB,GAAG;gBAC7B;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,SAAS;YAAE;gBACd,wBAAwB,GAAG;gBAC3B;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBAEb;YACJ;QACA;YAAS;gBACL,sBAAsB,GAAG;YAC7B;IACJ;AACJ;AACA,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,IAAI,EAAE,YAAY,CAAC,UAAU,CAAC,gJAAA,CAAA,SAAC,CAAC,IAAI,GAAG;QACnC,EAAE,aAAa,GAAG,cAAc,UAAU;QAC1C,oEAAoE;QACpE,8BAA8B;QAC9B,IAAI,EAAE,OAAO,CAAC,sBAAsB,EAAE;YAClC,MAAM,cAAc,EAAE,YAAY,CAAC,gCAAgC;YACnE,IAAI,aAAa;gBACb,EAAE,eAAe,CAAC,aAAa;YACnC;QACJ;IACJ;AACJ;AACA,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,IAAI,EAAE,YAAY,CAAC,UAAU,CAAC,gJAAA,CAAA,SAAC,CAAC,IAAI,GAAG;QACnC,EAAE,aAAa,GAAG,cAAc,UAAU;QAC1C,gBAAgB,GAAG;IACvB;AACJ;AACA,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,MAAM,KAAK,MAAM,KAAK;IACtB,IAAI,EAAE,YAAY,CAAC,UAAU,CAAC,KAAK;QAC/B,EAAE,YAAY,CAAC,sBAAsB;QACrC,EAAE,YAAY,CAAC,qBAAqB,CAAC;IACzC;AACJ;AACA,SAAS,iBAAiB,CAAC;IACvB,MAAM,aAAa,EAAE,YAAY,CAAC,SAAS,GAAG;IAC9C,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,IAAI,CAAC,YAAY;QACb,EAAE,WAAW,GAAG;IACpB;IACA,IAAI,CAAC,eAAe,UAAU,KAAK,EAAE,YAAY,CAAC,UAAU,CAAC,gJAAA,CAAA,SAAC,CAAC,IAAI,GAAG;QAClE,EAAE,YAAY,CAAC,sBAAsB;QACrC,IAAI,YAAY;YACZ,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,IAAI;QAC/C,OACK,IAAI,aAAa;YAClB,EAAE,YAAY,CAAC,MAAM,CAAC;QAC1B;IACJ;AACJ;AACA,SAAS,cAAc,CAAC;IACpB,IAAI,CAAC,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,CAAC,GAAG;QACvC,EAAE,kBAAkB,CAAC,gJAAA,CAAA,YAAE,CAAC,CAAC,EAAE,gJAAA,CAAA,SAAC,CAAC,CAAC;IAClC;IACA,EAAE,cAAc;AACpB;AACA,SAAS,eAAe,CAAC;IACrB,IAAI,EAAE,YAAY,CAAC,kBAAkB,CAAC,gJAAA,CAAA,SAAC,CAAC,EAAE,GAAG;QACzC,EAAE,YAAY,CAAC,mCAAmC,CAAC,gJAAA,CAAA,SAAC,CAAC,EAAE;QACvD,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,EAAE;IAC7C;AACJ;AACA,SAAS,eAAe,CAAC,EAAE,KAAK;IAC5B,MAAM,KAAK,MAAM,KAAK;IACtB,IAAI,EAAE,YAAY,CAAC,UAAU,CAAC,KAAK;QAC/B,EAAE,YAAY,CAAC,mCAAmC,CAAC;QACnD,EAAE,YAAY,CAAC,qBAAqB,CAAC;IACzC;AACJ;AACA,SAAS,2BAA2B,CAAC;IACjC,IAAI,EAAE,YAAY,CAAC,wBAAwB,IAAI;QAC3C,EAAE,YAAY,CAAC,sBAAsB;QACrC,EAAE,YAAY,CAAC,4BAA4B;IAC/C;AACJ;AACA,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,MAAM,KAAK,MAAM,KAAK;IACtB,IAAI,EAAE,YAAY,CAAC,UAAU,CAAC,KAAK;QAC/B,EAAE,YAAY,CAAC,sBAAsB;QACrC,EAAE,YAAY,CAAC,qBAAqB,CAAC;QACrC,EAAE,wBAAwB,CAAC,iBAAiB;IAChD;AACJ;AACA,SAAS,eAAe,CAAC;IACrB,EAAE,oCAAoC;IACtC,EAAE,kBAAkB,CAAC,gJAAA,CAAA,YAAE,CAAC,EAAE,EAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAChC,EAAE,YAAY,CAAC,GAAG;IAClB,EAAE,UAAU,GAAG;AACnB;AACA,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,MAAM,KAAK,MAAM,OAAO;IACxB,MAAM,MAAM,MAAM,KAAK;IACvB,IAAK,IAAI,IAAI,EAAE,YAAY,CAAC,QAAQ,EAAE,IAAI,GAAG,IAAK;QAC9C,MAAM,UAAU,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;QACvC,MAAM,YAAY,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE;QAC1C,6EAA6E;QAC7E,IAAI,QAAQ,aAAa,CAAC,QAAQ,gJAAA,CAAA,SAAC,CAAC,OAAO,IAAI,EAAE,WAAW,CAAC,UAAU,CAAC,aAAa,EAAE,GAAG;YACtF,EAAE,YAAY,CAAC,mCAAmC,CAAC;YACnD,IAAI,EAAE,YAAY,CAAC,QAAQ,IAAI,GAC3B,EAAE,YAAY,CAAC,eAAe,CAAC;YACnC;QACJ;QACA,IAAI,EAAE,iBAAiB,CAAC,SAAS,YAAY;YACzC;QACJ;IACJ;AACJ;AACA,SAAS,aAAa,CAAC,EAAE,KAAK;IAC1B,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;QACR,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;QACR,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;QACR,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;QACR,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;QACR,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,mBAAmB,GAAG;gBACtB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC;YAAE;gBACN,cAAc;gBACd;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,UAAU;QACjB,KAAK,gJAAA,CAAA,SAAC,CAAC,UAAU;YAAE;gBACf,oBAAoB,GAAG;gBACvB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,eAAe;gBACf;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,2BAA2B;gBAC3B;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,eAAe;gBACf;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,iBAAiB,GAAG;gBACpB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,iBAAiB,GAAG;gBACpB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,iBAAiB;gBACjB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;YAAE;gBACZ,mBAAmB,GAAG;gBACtB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,qBAAqB,GAAG;gBACxB;YACJ;QACA;YAAS;gBACL,oBAAoB,GAAG;YAC3B;IACJ;AACJ;AACA,SAAS,UAAU,CAAC,EAAE,KAAK;IACvB,IAAI,EAAE,sBAAsB,CAAC,MAAM,GAAG,GAAG;QACrC,cAAc,GAAG;IACrB,OACK;QACD,YAAY,GAAG;IACnB;AACJ;AACA,4BAA4B;AAC5B,oEAAoE;AACpE,SAAS,aAAa,CAAC,EAAE,KAAK;IAC1B,IAAI;IACJ,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM,EAAE;QAC1B,CAAC,KAAK,EAAE,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC,OAAO;IACjG;IACA,EAAE,YAAY,CAAC,GAAG;IAClB,EAAE,aAAa,GAAG,EAAE,qBAAqB;AAC7C;AACA,SAAS,UAAU,CAAC,EAAE,KAAK;IACvB,EAAE,IAAI,CAAC,OAAO,0JAAA,CAAA,MAAG,CAAC,kCAAkC;IACpD,EAAE,YAAY,CAAC,GAAG;IAClB,EAAE,aAAa,GAAG,EAAE,qBAAqB;IACzC,EAAE,KAAK,CAAC;AACZ;AACA,gCAAgC;AAChC,oEAAoE;AACpE,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,aAAa,qBAAqB,GAAG,CAAC,EAAE,YAAY,CAAC,YAAY,GAAG;QACpG,EAAE,sBAAsB,CAAC,MAAM,GAAG;QAClC,EAAE,qCAAqC,GAAG;QAC1C,EAAE,qBAAqB,GAAG,EAAE,aAAa;QACzC,EAAE,aAAa,GAAG,cAAc,aAAa;QAC7C,OAAQ,MAAM,IAAI;YACd,KAAK,iJAAA,CAAA,YAAS,CAAC,SAAS;gBAAE;oBACtB,qBAAqB,GAAG;oBACxB;gBACJ;YACA,KAAK,iJAAA,CAAA,YAAS,CAAC,oBAAoB;gBAAE;oBACjC,+BAA+B,GAAG;oBAClC;gBACJ;QAEJ;IACJ,OACK;QACD,aAAa,GAAG;IACpB;AACJ;AACA,SAAS,uBAAuB,CAAC,EAAE,KAAK;IACpC,EAAE,YAAY,CAAC,uBAAuB;IACtC,EAAE,wBAAwB,CAAC,YAAY;IACvC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,aAAa,GAAG,cAAc,UAAU;AAC9C;AACA,SAAS,wBAAwB,CAAC,EAAE,KAAK;IACrC,EAAE,YAAY,CAAC,uBAAuB;IACtC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,aAAa,GAAG,cAAc,eAAe;AACnD;AACA,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,EAAE,YAAY,CAAC,uBAAuB;IACtC,EAAE,kBAAkB,CAAC,gJAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,gJAAA,CAAA,SAAC,CAAC,QAAQ;IAC5C,EAAE,aAAa,GAAG,cAAc,eAAe;IAC/C,sBAAsB,GAAG;AAC7B;AACA,SAAS,qBAAqB,CAAC,EAAE,KAAK;IAClC,EAAE,YAAY,CAAC,uBAAuB;IACtC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IAC/B,EAAE,aAAa,GAAG,cAAc,aAAa;AACjD;AACA,SAAS,kBAAkB,CAAC,EAAE,KAAK;IAC/B,EAAE,YAAY,CAAC,uBAAuB;IACtC,EAAE,kBAAkB,CAAC,gJAAA,CAAA,YAAE,CAAC,KAAK,EAAE,gJAAA,CAAA,SAAC,CAAC,KAAK;IACtC,EAAE,aAAa,GAAG,cAAc,aAAa;IAC7C,oBAAoB,GAAG;AAC3B;AACA,SAAS,qBAAqB,CAAC,EAAE,KAAK;IAClC,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,gJAAA,CAAA,SAAC,CAAC,KAAK,GAAG;QACzC,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,KAAK;QAC5C,EAAE,mBAAmB;QACrB,EAAE,gBAAgB,CAAC;IACvB;AACJ;AACA,SAAS,qBAAqB,CAAC,EAAE,KAAK;IAClC,IAAI,cAAc,QAAQ;QACtB,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;IACnC,OACK;QACD,aAAa,GAAG;IACpB;IACA,MAAM,cAAc,GAAG;AAC3B;AACA,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,IAAI,CAAC,EAAE,WAAW,IAAI,EAAE,YAAY,CAAC,SAAS,KAAK,GAAG;QAClD,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;QAC/B,EAAE,WAAW,GAAG,EAAE,YAAY,CAAC,OAAO;QACtC,EAAE,YAAY,CAAC,GAAG;IACtB;AACJ;AACA,SAAS,gBAAgB,CAAC,EAAE,KAAK;IAC7B,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,kBAAkB,GAAG;gBACrB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;YAAE;gBACR,mBAAmB,GAAG;gBACtB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,oBAAoB,GAAG;gBACvB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,qBAAqB,GAAG;gBACxB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,qBAAqB,GAAG;gBACxB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,qBAAqB,GAAG;gBACxB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;YAAE;gBACZ,uBAAuB,GAAG;gBAC1B;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,wBAAwB,GAAG;gBAC3B;YACJ;QACA;YAAS;gBACL,aAAa,GAAG;YACpB;IACJ;AACJ;AACA,SAAS,cAAc,CAAC,EAAE,KAAK;IAC3B,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,gJAAA,CAAA,SAAC,CAAC,KAAK,GAAG;oBACzC,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,KAAK;oBAC5C,EAAE,mBAAmB;gBACzB;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,qBAAqB,GAAG;gBACxB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBAEP;YACJ;QACA;YAAS;gBACL,aAAa,GAAG;YACpB;IACJ;AACJ;AACA,SAAS,aAAa,CAAC,EAAE,KAAK;IAC1B,MAAM,4BAA4B,EAAE,sBAAsB;IAC1D,EAAE,sBAAsB,GAAG;IAC3B,kCAAkC;IAClC,WAAW,GAAG;IACd,EAAE,sBAAsB,GAAG;AAC/B;AACA,qCAAqC;AACrC,oEAAoE;AACpE,SAAS,+BAA+B,CAAC,EAAE,KAAK;IAC5C,EAAE,sBAAsB,CAAC,IAAI,CAAC;AAClC;AACA,SAAS,qBAAqB,CAAC,EAAE,KAAK;IAClC,EAAE,sBAAsB,CAAC,IAAI,CAAC;IAC9B,EAAE,qCAAqC,GAAG;AAC9C;AACA,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,IAAI,IAAI;IACR,IAAI,EAAE,qCAAqC,EAAE;QACzC,MAAO,IAAI,EAAE,sBAAsB,CAAC,MAAM,EAAE,IAAK;YAC7C,aAAa,GAAG,EAAE,sBAAsB,CAAC,EAAE;QAC/C;IACJ,OACK;QACD,MAAO,IAAI,EAAE,sBAAsB,CAAC,MAAM,EAAE,IAAK;YAC7C,EAAE,iBAAiB,CAAC,EAAE,sBAAsB,CAAC,EAAE;QACnD;IACJ;IACA,EAAE,aAAa,GAAG,EAAE,qBAAqB;IACzC,EAAE,aAAa,CAAC;AACpB;AACA,kCAAkC;AAClC,oEAAoE;AACpE,MAAM,sBAAsB,IAAI,IAAI;IAAC,gJAAA,CAAA,SAAC,CAAC,OAAO;IAAE,gJAAA,CAAA,SAAC,CAAC,GAAG;IAAE,gJAAA,CAAA,SAAC,CAAC,QAAQ;IAAE,gJAAA,CAAA,SAAC,CAAC,KAAK;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,KAAK;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;IAAE,gJAAA,CAAA,SAAC,CAAC,KAAK;IAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;CAAC;AAC/G,SAAS,kBAAkB,CAAC,EAAE,KAAK;IAC/B,MAAM,KAAK,MAAM,KAAK;IACtB,IAAI,oBAAoB,GAAG,CAAC,KAAK;QAC7B,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,gJAAA,CAAA,SAAC,CAAC,OAAO,GAAG;YAC3C,EAAE,YAAY,CAAC,sBAAsB;YACrC,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,OAAO;YAC9C,EAAE,wBAAwB,CAAC,iBAAiB;YAC5C,EAAE,aAAa,GAAG,cAAc,QAAQ;YACxC,gBAAgB,GAAG;QACvB;IACJ,OACK;QACD,eAAe,GAAG;IACtB;AACJ;AACA,SAAS,gBAAgB,CAAC,EAAE,KAAK;IAC7B,MAAM,KAAK,MAAM,KAAK;IACtB,OAAQ;QACJ,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,gJAAA,CAAA,SAAC,CAAC,OAAO,GAAG;oBAC3C,EAAE,YAAY,CAAC,sBAAsB;oBACrC,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,OAAO;oBAC9C,EAAE,wBAAwB,CAAC,iBAAiB;oBAC5C,EAAE,aAAa,GAAG,cAAc,QAAQ;oBACxC,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK,EAAE;wBAChB,cAAc,GAAG;oBACrB;gBACJ;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBAEP;YACJ;QACA;YAAS;gBACL,aAAa,GAAG;YACpB;IACJ;AACJ;AACA,uCAAuC;AACvC,oEAAoE;AACpE,SAAS,sBAAsB,CAAC,EAAE,KAAK;IACnC,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;YAAE;gBACR,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B,MAAM,cAAc,GAAG;gBACvB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,eAAe,GAAG;gBAClB;YACJ;QACA;YAAS;gBACL,mBAAmB,GAAG;YAC1B;IACJ;AACJ;AACA,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,EAAE;oBAC5C,EAAE,YAAY,CAAC,GAAG;oBAClB,EAAE,aAAa,GAAG,cAAc,QAAQ;gBAC5C;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,qBAAqB,GAAG;gBACxB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;YAAE;gBAER;YACJ;QACA;YAAS;gBACL,mBAAmB,GAAG;YAC1B;IACJ;AACJ;AACA,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,EAAE;QAC5C,EAAE,YAAY,CAAC,GAAG;QAClB,EAAE,aAAa,GAAG,cAAc,QAAQ;QACxC,EAAE,aAAa,CAAC;IACpB;AACJ;AACA,qCAAqC;AACrC,oEAAoE;AACpE,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,EAAE,YAAY,CAAC,2BAA2B;gBAC1C,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B,EAAE,aAAa,GAAG,cAAc,MAAM;gBACtC;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,EAAE,YAAY,CAAC,2BAA2B;gBAC1C,EAAE,kBAAkB,CAAC,gJAAA,CAAA,YAAE,CAAC,EAAE,EAAE,gJAAA,CAAA,SAAC,CAAC,EAAE;gBAChC,EAAE,aAAa,GAAG,cAAc,MAAM;gBACtC,cAAc,GAAG;gBACjB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,IAAI,EAAE,YAAY,CAAC,+BAA+B,IAAI;oBAClD,EAAE,YAAY,CAAC,2BAA2B;oBAC1C,EAAE,YAAY,CAAC,GAAG;oBAClB,EAAE,aAAa,GAAG,cAAc,QAAQ;oBACxC,gBAAgB,GAAG;gBACvB;gBACA;YACJ;QACA;YAAS;gBACL,gBAAgB,GAAG;YACvB;IACJ;AACJ;AACA,SAAS,kBAAkB,CAAC,EAAE,KAAK;IAC/B,MAAM,KAAK,MAAM,KAAK;IACtB,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,KAAK;oBACpC,EAAE,YAAY,CAAC,2BAA2B;oBAC1C,EAAE,YAAY,CAAC,GAAG;oBAClB,EAAE,aAAa,GAAG,cAAc,QAAQ;gBAC5C;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,IAAI,EAAE,YAAY,CAAC,+BAA+B,IAAI;oBAClD,EAAE,YAAY,CAAC,2BAA2B;oBAC1C,EAAE,YAAY,CAAC,GAAG;oBAClB,EAAE,aAAa,GAAG,cAAc,QAAQ;oBACxC,cAAc,GAAG;gBACrB;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBAEP;YACJ;QACA;YAAS;gBACL,cAAc,GAAG;YACrB;IACJ;AACJ;AACA,8BAA8B;AAC9B,oEAAoE;AACpE,SAAS,cAAc,CAAC,EAAE,KAAK;IAC3B,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,EAAE,YAAY,CAAC,0BAA0B;gBACzC,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B,EAAE,aAAa,GAAG,cAAc,OAAO;gBACvC,EAAE,wBAAwB,CAAC,YAAY;gBACvC;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,gJAAA,CAAA,SAAC,CAAC,EAAE,GAAG;oBACtC,EAAE,YAAY,CAAC,0BAA0B;oBACzC,EAAE,YAAY,CAAC,GAAG;oBAClB,EAAE,aAAa,GAAG,cAAc,aAAa;oBAC7C,oBAAoB,GAAG;gBAC3B;gBACA;YACJ;QACA;YAAS;gBACL,gBAAgB,GAAG;YACvB;IACJ;AACJ;AACA,SAAS,YAAY,CAAC,EAAE,KAAK;IACzB,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,gJAAA,CAAA,SAAC,CAAC,EAAE,GAAG;oBACtC,EAAE,YAAY,CAAC,0BAA0B;oBACzC,EAAE,YAAY,CAAC,GAAG;oBAClB,EAAE,aAAa,GAAG,cAAc,aAAa;gBACjD;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,gJAAA,CAAA,SAAC,CAAC,EAAE,GAAG;oBACtC,EAAE,YAAY,CAAC,0BAA0B;oBACzC,EAAE,YAAY,CAAC,GAAG;oBAClB,EAAE,aAAa,GAAG,cAAc,aAAa;oBAC7C,kBAAkB,GAAG;gBACzB;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM,KAAK,KAAK,EAAE,YAAY,CAAC,eAAe,CAAC,gJAAA,CAAA,SAAC,CAAC,EAAE,GAAG;oBACrF,EAAE,YAAY,CAAC,0BAA0B;oBACzC,EAAE,YAAY,CAAC,GAAG;oBAClB,EAAE,aAAa,GAAG,cAAc,aAAa;oBAC7C,kBAAkB,GAAG;gBACzB;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBAEP;YACJ;QACA;YAAS;gBACL,cAAc,GAAG;YACrB;IACJ;AACJ;AACA,+BAA+B;AAC/B,oEAAoE;AACpE,SAAS,eAAe,CAAC,EAAE,KAAK;IAC5B,MAAM,KAAK,MAAM,KAAK;IACtB,IAAI,oBAAoB,GAAG,CAAC,KAAK;QAC7B,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,gJAAA,CAAA,SAAC,CAAC,EAAE,KAAK,EAAE,YAAY,CAAC,eAAe,CAAC,gJAAA,CAAA,SAAC,CAAC,EAAE,GAAG;YAC9E,EAAE,eAAe;YACjB,cAAc,GAAG;QACrB;IACJ,OACK;QACD,eAAe,GAAG;IACtB;AACJ;AACA,SAAS,aAAa,CAAC,EAAE,KAAK;IAC1B,MAAM,KAAK,MAAM,KAAK;IACtB,OAAQ;QACJ,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,KAAK;oBACpC,EAAE,YAAY,CAAC,sBAAsB;oBACrC,EAAE,YAAY,CAAC,qBAAqB,CAAC;oBACrC,EAAE,wBAAwB,CAAC,iBAAiB;oBAC5C,EAAE,aAAa,GAAG,cAAc,MAAM;gBAC1C;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,KAAK;oBACpC,EAAE,eAAe;oBACjB,YAAY,GAAG;gBACnB;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;QACV,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBAET;YACJ;QACA;YAAS;gBACL,aAAa,GAAG;YACpB;IACJ;AACJ;AACA,iCAAiC;AACjC,oEAAoE;AACpE,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM,EAAE;oBAC1C,EAAE,YAAY,CAAC,GAAG;gBACtB;gBACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM,EAAE;oBAC1C,EAAE,YAAY,CAAC,GAAG;gBACtB;gBACA,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,EAAE;oBAC5C,EAAE,YAAY,CAAC,GAAG;gBACtB;gBACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM,EAAE;oBAC1C,EAAE,YAAY,CAAC,GAAG;gBACtB;gBACA,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,EAAE;oBAC5C,EAAE,YAAY,CAAC,GAAG;gBACtB;gBACA,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B,MAAM,cAAc,GAAG;gBACvB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,MAAM,GAAG;oBAC3C,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,MAAM;oBAC7C,EAAE,mBAAmB;oBACrB,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM,EAAE;wBAC1B,EAAE,gBAAgB,CAAC;oBACvB;gBACJ;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,eAAe,GAAG;gBAClB;YACJ;QACA;IAEJ;AACJ;AACA,SAAS,eAAe,CAAC,EAAE,KAAK;IAC5B,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,IAAI,EAAE,YAAY,CAAC,QAAQ,GAAG,KAC1B,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM,IACxC,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE,YAAY,CAAC,QAAQ,GAAG,EAAE,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,EAAE;oBACnE,EAAE,YAAY,CAAC,GAAG;gBACtB;gBACA,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,EAAE;oBAC5C,EAAE,YAAY,CAAC,GAAG;gBACtB;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM,EAAE;oBAC1C,EAAE,YAAY,CAAC,GAAG;gBACtB;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;YAAE;gBACX,IAAI,EAAE,YAAY,CAAC,gBAAgB,CAAC,gJAAA,CAAA,SAAC,CAAC,MAAM,GAAG;oBAC3C,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,MAAM;oBAC7C,EAAE,mBAAmB;gBACzB;gBACA;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,qBAAqB,GAAG;gBACxB;YACJ;QACA;IAEJ;AACJ;AACA,0CAA0C;AAC1C,oEAAoE;AACpE,SAAS,wBAAwB,CAAC,EAAE,KAAK;IACrC,MAAM,KAAK,MAAM,KAAK;IACtB,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,OAAO,IAChB,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK,IACd,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK,IACd,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK,IACd,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK,IACd,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,IACX,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,IACX,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,EAAE;QACb,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,MAAM;QAC7C,EAAE,mBAAmB;QACrB,EAAE,gBAAgB,CAAC;IACvB,OACK;QACD,iBAAiB,GAAG;IACxB;AACJ;AACA,SAAS,sBAAsB,CAAC,EAAE,KAAK;IACnC,MAAM,KAAK,MAAM,KAAK;IACtB,IAAI,OAAO,gJAAA,CAAA,SAAC,CAAC,OAAO,IAChB,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK,IACd,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK,IACd,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK,IACd,OAAO,gJAAA,CAAA,SAAC,CAAC,KAAK,IACd,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,IACX,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,IACX,OAAO,gJAAA,CAAA,SAAC,CAAC,EAAE,EAAE;QACb,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,KAAK;YACpC,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,MAAM;YAC7C,EAAE,mBAAmB;YACrB,EAAE,QAAQ,CAAC;QACf;IACJ,OACK;QACD,eAAe,GAAG;IACtB;AACJ;AACA,mCAAmC;AACnC,oEAAoE;AACpE,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,OAAQ,MAAM,KAAK;QACf,0DAA0D;QAC1D,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;QACX,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,MAAM;QACb,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,eAAe,GAAG;gBAClB;YACJ;QACA,+CAA+C;QAC/C,KAAK,gJAAA,CAAA,SAAC,CAAC,OAAO;QACd,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;QACZ,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,EAAE,sBAAsB,CAAC,EAAE,GAAG,cAAc,QAAQ;gBACpD,EAAE,aAAa,GAAG,cAAc,QAAQ;gBACxC,gBAAgB,GAAG;gBACnB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,GAAG;YAAE;gBACR,EAAE,sBAAsB,CAAC,EAAE,GAAG,cAAc,eAAe;gBAC3D,EAAE,aAAa,GAAG,cAAc,eAAe;gBAC/C,sBAAsB,GAAG;gBACzB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,EAAE,sBAAsB,CAAC,EAAE,GAAG,cAAc,aAAa;gBACzD,EAAE,aAAa,GAAG,cAAc,aAAa;gBAC7C,oBAAoB,GAAG;gBACvB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;QACT,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE;YAAE;gBACP,EAAE,sBAAsB,CAAC,EAAE,GAAG,cAAc,MAAM;gBAClD,EAAE,aAAa,GAAG,cAAc,MAAM;gBACtC,cAAc,GAAG;gBACjB;YACJ;QACA;YAAS;gBACL,EAAE,sBAAsB,CAAC,EAAE,GAAG,cAAc,OAAO;gBACnD,EAAE,aAAa,GAAG,cAAc,OAAO;gBACvC,eAAe,GAAG;YACtB;IACJ;AACJ;AACA,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,EAAE;QAC5B,qBAAqB,GAAG;IAC5B;AACJ;AACA,SAAS,cAAc,CAAC,EAAE,KAAK;IAC3B,IAAI,EAAE,YAAY,CAAC,SAAS,GAAG,GAAG;QAC9B,EAAE,YAAY,CAAC,qBAAqB,CAAC,gJAAA,CAAA,SAAC,CAAC,QAAQ;QAC/C,EAAE,wBAAwB,CAAC,iBAAiB;QAC5C,EAAE,sBAAsB,CAAC,KAAK;QAC9B,EAAE,mBAAmB;QACrB,EAAE,KAAK,CAAC;IACZ,OACK;QACD,YAAY,GAAG;IACnB;AACJ;AACA,kCAAkC;AAClC,oEAAoE;AACpE,SAAS,kBAAkB,CAAC,EAAE,KAAK;IAC/B,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI,EAAE;QACxB,eAAe,GAAG;IACtB,OACK;QACD,eAAe,GAAG;IACtB;AACJ;AACA,SAAS,gBAAgB,CAAC,EAAE,KAAK;IAC7B,IAAI;IACJ,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI,EAAE;QACxB,IAAI,CAAC,EAAE,eAAe,EAAE;YACpB,EAAE,aAAa,GAAG,cAAc,gBAAgB;QACpD;QACA,oEAAoE;QACpE,8BAA8B;QAC9B,IAAI,EAAE,OAAO,CAAC,sBAAsB,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI,EAAE;YACzE,EAAE,eAAe,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YAC3C,yDAAyD;YACzD,MAAM,cAAc,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;YAC3C,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,EAAE,WAAW,CAAC,yBAAyB,CAAC,YAAY,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG;gBAC9H,EAAE,eAAe,CAAC,aAAa;YACnC;QACJ;IACJ,OACK;QACD,eAAe,GAAG;IACtB;AACJ;AACA,SAAS,eAAe,CAAC,EAAE,KAAK;IAC5B,EAAE,aAAa,GAAG,cAAc,OAAO;IACvC,WAAW,GAAG;AAClB;AACA,mCAAmC;AACnC,oEAAoE;AACpE,SAAS,mBAAmB,CAAC,EAAE,KAAK;IAChC,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,KAAK;YAAE;gBACV,EAAE,cAAc,CAAC,OAAO,gJAAA,CAAA,KAAE,CAAC,IAAI;gBAC/B,MAAM,cAAc,GAAG;gBACvB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,eAAe,GAAG;gBAClB;YACJ;QACA;IAEJ;AACJ;AACA,SAAS,iBAAiB,CAAC,EAAE,KAAK;IAC9B,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,IAAI,CAAC,EAAE,YAAY,CAAC,wBAAwB,IAAI;QAC1E,EAAE,YAAY,CAAC,GAAG;QAClB,IAAI,CAAC,EAAE,eAAe,IAAI,EAAE,YAAY,CAAC,YAAY,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ,EAAE;YAClE,EAAE,aAAa,GAAG,cAAc,cAAc;QAClD;IACJ;AACJ;AACA,sCAAsC;AACtC,oEAAoE;AACpE,SAAS,sBAAsB,CAAC,EAAE,KAAK;IACnC,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,eAAe,GAAG;gBAClB;YACJ;QACA;IAEJ;AACJ;AACA,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI,EAAE;QACxB,EAAE,aAAa,GAAG,cAAc,oBAAoB;IACxD;AACJ;AACA,wCAAwC;AACxC,oEAAoE;AACpE,SAAS,uBAAuB,CAAC,EAAE,KAAK;IACpC,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI,EAAE;QACxB,eAAe,GAAG;IACtB,OACK;QACD,oBAAoB,GAAG;IAC3B;AACJ;AACA,SAAS,oBAAoB,CAAC,EAAE,KAAK;IACjC,EAAE,aAAa,GAAG,cAAc,OAAO;IACvC,WAAW,GAAG;AAClB;AACA,4CAA4C;AAC5C,oEAAoE;AACpE,SAAS,2BAA2B,CAAC,EAAE,KAAK;IACxC,OAAQ,MAAM,KAAK;QACf,KAAK,gJAAA,CAAA,SAAC,CAAC,IAAI;YAAE;gBACT,eAAe,GAAG;gBAClB;YACJ;QACA,KAAK,gJAAA,CAAA,SAAC,CAAC,QAAQ;YAAE;gBACb,eAAe,GAAG;gBAClB;YACJ;QACA;IAEJ;AACJ;AACA,kDAAkD;AAClD,oEAAoE;AACpE,SAAS,8BAA8B,CAAC,EAAE,KAAK;IAC3C,MAAM,KAAK,GAAG,mJAAA,CAAA,wBAA6B;IAC3C,EAAE,iBAAiB,CAAC;AACxB;AACA,SAAS,0BAA0B,CAAC,EAAE,KAAK;IACvC,EAAE,iBAAiB,CAAC;IACpB,EAAE,UAAU,GAAG;AACnB;AACA,SAAS,+BAA+B,CAAC;IACrC,MAAO,EAAE,WAAW,CAAC,eAAe,CAAC,EAAE,YAAY,CAAC,OAAO,MAAM,gJAAA,CAAA,KAAE,CAAC,IAAI,IACpE,EAAE,YAAY,CAAC,YAAY,KAAK,aAChC,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,CAAC,YAAY,EAAE,EAAE,YAAY,CAAC,OAAO,EAAG;QAC7E,EAAE,YAAY,CAAC,GAAG;IACtB;AACJ;AACA,SAAS,yBAAyB,CAAC,EAAE,KAAK;IACtC,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAyB,AAAD,EAAE,QAAQ;QAClC,+BAA+B;QAC/B,EAAE,8BAA8B,CAAC;IACrC,OACK;QACD,MAAM,UAAU,EAAE,0BAA0B;QAC5C,MAAM,YAAY,EAAE,WAAW,CAAC,eAAe,CAAC;QAChD,IAAI,cAAc,gJAAA,CAAA,KAAE,CAAC,MAAM,EAAE;YACzB,CAAA,GAAA,8JAAA,CAAA,yBAAqC,AAAD,EAAE;QAC1C,OACK,IAAI,cAAc,gJAAA,CAAA,KAAE,CAAC,GAAG,EAAE;YAC3B,CAAA,GAAA,8JAAA,CAAA,wBAAoC,AAAD,EAAE;YACrC,CAAA,GAAA,8JAAA,CAAA,sBAAkC,AAAD,EAAE;QACvC;QACA,CAAA,GAAA,8JAAA,CAAA,sBAAkC,AAAD,EAAE;QACnC,IAAI,MAAM,WAAW,EAAE;YACnB,EAAE,cAAc,CAAC,OAAO;QAC5B,OACK;YACD,EAAE,cAAc,CAAC,OAAO;QAC5B;QACA,MAAM,cAAc,GAAG;IAC3B;AACJ;AACA,SAAS,uBAAuB,CAAC,EAAE,KAAK;IACpC,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,CAAC,IAAI,MAAM,KAAK,KAAK,gJAAA,CAAA,SAAC,CAAC,EAAE,EAAE;QAC7C,+BAA+B;QAC/B,EAAE,4BAA4B,CAAC;QAC/B;IACJ;IACA,IAAK,IAAI,IAAI,EAAE,YAAY,CAAC,QAAQ,EAAE,IAAI,GAAG,IAAK;QAC9C,MAAM,UAAU,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;QACvC,IAAI,EAAE,WAAW,CAAC,eAAe,CAAC,aAAa,gJAAA,CAAA,KAAE,CAAC,IAAI,EAAE;YACpD,EAAE,4BAA4B,CAAC;YAC/B;QACJ;QACA,MAAM,UAAU,EAAE,WAAW,CAAC,UAAU,CAAC;QACzC,IAAI,QAAQ,WAAW,OAAO,MAAM,OAAO,EAAE;YACzC,wDAAwD;YACxD,MAAM,OAAO,GAAG;YAChB,EAAE,YAAY,CAAC,eAAe,CAAC;YAC/B;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8832, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/serializer/index.js"], "sourcesContent": ["import { TAG_NAMES as $, NS, hasUnescapedText } from '../common/html.js';\nimport { escapeText, escapeAttribute } from 'entities/escape';\nimport { defaultTreeAdapter } from '../tree-adapters/default.js';\n// Sets\nconst VOID_ELEMENTS = new Set([\n    $.AREA,\n    $.BASE,\n    $.BASEFONT,\n    $.BGSOUND,\n    $.BR,\n    $.COL,\n    $.EMBED,\n    $.FRAME,\n    $.HR,\n    $.IMG,\n    $.INPUT,\n    $.KEYGEN,\n    $.LINK,\n    $.META,\n    $.PARAM,\n    $.SOURCE,\n    $.TRACK,\n    $.WBR,\n]);\nfunction isVoidElement(node, options) {\n    return (options.treeAdapter.isElementNode(node) &&\n        options.treeAdapter.getNamespaceURI(node) === NS.HTML &&\n        VOID_ELEMENTS.has(options.treeAdapter.getTagName(node)));\n}\nconst defaultOpts = { treeAdapter: defaultTreeAdapter, scriptingEnabled: true };\n/**\n * Serializes an AST node to an HTML string.\n *\n * @example\n *\n * ```js\n * const parse5 = require('parse5');\n *\n * const document = parse5.parse('<!DOCTYPE html><html><head></head><body>Hi there!</body></html>');\n *\n * // Serializes a document.\n * const html = parse5.serialize(document);\n *\n * // Serializes the <html> element content.\n * const str = parse5.serialize(document.childNodes[1]);\n *\n * console.log(str); //> '<head></head><body>Hi there!</body>'\n * ```\n *\n * @param node Node to serialize.\n * @param options Serialization options.\n */\nexport function serialize(node, options) {\n    const opts = { ...defaultOpts, ...options };\n    if (isVoidElement(node, opts)) {\n        return '';\n    }\n    return serializeChildNodes(node, opts);\n}\n/**\n * Serializes an AST element node to an HTML string, including the element node.\n *\n * @example\n *\n * ```js\n * const parse5 = require('parse5');\n *\n * const document = parse5.parseFragment('<div>Hello, <b>world</b>!</div>');\n *\n * // Serializes the <div> element.\n * const str = parse5.serializeOuter(document.childNodes[0]);\n *\n * console.log(str); //> '<div>Hello, <b>world</b>!</div>'\n * ```\n *\n * @param node Node to serialize.\n * @param options Serialization options.\n */\nexport function serializeOuter(node, options) {\n    const opts = { ...defaultOpts, ...options };\n    return serializeNode(node, opts);\n}\nfunction serializeChildNodes(parentNode, options) {\n    let html = '';\n    // Get container of the child nodes\n    const container = options.treeAdapter.isElementNode(parentNode) &&\n        options.treeAdapter.getTagName(parentNode) === $.TEMPLATE &&\n        options.treeAdapter.getNamespaceURI(parentNode) === NS.HTML\n        ? options.treeAdapter.getTemplateContent(parentNode)\n        : parentNode;\n    const childNodes = options.treeAdapter.getChildNodes(container);\n    if (childNodes) {\n        for (const currentNode of childNodes) {\n            html += serializeNode(currentNode, options);\n        }\n    }\n    return html;\n}\nfunction serializeNode(node, options) {\n    if (options.treeAdapter.isElementNode(node)) {\n        return serializeElement(node, options);\n    }\n    if (options.treeAdapter.isTextNode(node)) {\n        return serializeTextNode(node, options);\n    }\n    if (options.treeAdapter.isCommentNode(node)) {\n        return serializeCommentNode(node, options);\n    }\n    if (options.treeAdapter.isDocumentTypeNode(node)) {\n        return serializeDocumentTypeNode(node, options);\n    }\n    // Return an empty string for unknown nodes\n    return '';\n}\nfunction serializeElement(node, options) {\n    const tn = options.treeAdapter.getTagName(node);\n    return `<${tn}${serializeAttributes(node, options)}>${isVoidElement(node, options) ? '' : `${serializeChildNodes(node, options)}</${tn}>`}`;\n}\nfunction serializeAttributes(node, { treeAdapter }) {\n    let html = '';\n    for (const attr of treeAdapter.getAttrList(node)) {\n        html += ' ';\n        if (attr.namespace) {\n            switch (attr.namespace) {\n                case NS.XML: {\n                    html += `xml:${attr.name}`;\n                    break;\n                }\n                case NS.XMLNS: {\n                    if (attr.name !== 'xmlns') {\n                        html += 'xmlns:';\n                    }\n                    html += attr.name;\n                    break;\n                }\n                case NS.XLINK: {\n                    html += `xlink:${attr.name}`;\n                    break;\n                }\n                default: {\n                    html += `${attr.prefix}:${attr.name}`;\n                }\n            }\n        }\n        else {\n            html += attr.name;\n        }\n        html += `=\"${escapeAttribute(attr.value)}\"`;\n    }\n    return html;\n}\nfunction serializeTextNode(node, options) {\n    const { treeAdapter } = options;\n    const content = treeAdapter.getTextNodeContent(node);\n    const parent = treeAdapter.getParentNode(node);\n    const parentTn = parent && treeAdapter.isElementNode(parent) && treeAdapter.getTagName(parent);\n    return parentTn &&\n        treeAdapter.getNamespaceURI(parent) === NS.HTML &&\n        hasUnescapedText(parentTn, options.scriptingEnabled)\n        ? content\n        : escapeText(content);\n}\nfunction serializeCommentNode(node, { treeAdapter }) {\n    return `<!--${treeAdapter.getCommentNodeContent(node)}-->`;\n}\nfunction serializeDocumentTypeNode(node, { treeAdapter }) {\n    return `<!DOCTYPE ${treeAdapter.getDocumentTypeNodeName(node)}>`;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACA,OAAO;AACP,MAAM,gBAAgB,IAAI,IAAI;IAC1B,gJAAA,CAAA,YAAC,CAAC,IAAI;IACN,gJAAA,CAAA,YAAC,CAAC,IAAI;IACN,gJAAA,CAAA,YAAC,CAAC,QAAQ;IACV,gJAAA,CAAA,YAAC,CAAC,OAAO;IACT,gJAAA,CAAA,YAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,YAAC,CAAC,GAAG;IACL,gJAAA,CAAA,YAAC,CAAC,KAAK;IACP,gJAAA,CAAA,YAAC,CAAC,KAAK;IACP,gJAAA,CAAA,YAAC,CAAC,EAAE;IACJ,gJAAA,CAAA,YAAC,CAAC,GAAG;IACL,gJAAA,CAAA,YAAC,CAAC,KAAK;IACP,gJAAA,CAAA,YAAC,CAAC,MAAM;IACR,gJAAA,CAAA,YAAC,CAAC,IAAI;IACN,gJAAA,CAAA,YAAC,CAAC,IAAI;IACN,gJAAA,CAAA,YAAC,CAAC,KAAK;IACP,gJAAA,CAAA,YAAC,CAAC,MAAM;IACR,gJAAA,CAAA,YAAC,CAAC,KAAK;IACP,gJAAA,CAAA,YAAC,CAAC,GAAG;CACR;AACD,SAAS,cAAc,IAAI,EAAE,OAAO;IAChC,OAAQ,QAAQ,WAAW,CAAC,aAAa,CAAC,SACtC,QAAQ,WAAW,CAAC,eAAe,CAAC,UAAU,gJAAA,CAAA,KAAE,CAAC,IAAI,IACrD,cAAc,GAAG,CAAC,QAAQ,WAAW,CAAC,UAAU,CAAC;AACzD;AACA,MAAM,cAAc;IAAE,aAAa,6JAAA,CAAA,qBAAkB;IAAE,kBAAkB;AAAK;AAuBvE,SAAS,UAAU,IAAI,EAAE,OAAO;IACnC,MAAM,OAAO;QAAE,GAAG,WAAW;QAAE,GAAG,OAAO;IAAC;IAC1C,IAAI,cAAc,MAAM,OAAO;QAC3B,OAAO;IACX;IACA,OAAO,oBAAoB,MAAM;AACrC;AAoBO,SAAS,eAAe,IAAI,EAAE,OAAO;IACxC,MAAM,OAAO;QAAE,GAAG,WAAW;QAAE,GAAG,OAAO;IAAC;IAC1C,OAAO,cAAc,MAAM;AAC/B;AACA,SAAS,oBAAoB,UAAU,EAAE,OAAO;IAC5C,IAAI,OAAO;IACX,mCAAmC;IACnC,MAAM,YAAY,QAAQ,WAAW,CAAC,aAAa,CAAC,eAChD,QAAQ,WAAW,CAAC,UAAU,CAAC,gBAAgB,gJAAA,CAAA,YAAC,CAAC,QAAQ,IACzD,QAAQ,WAAW,CAAC,eAAe,CAAC,gBAAgB,gJAAA,CAAA,KAAE,CAAC,IAAI,GACzD,QAAQ,WAAW,CAAC,kBAAkB,CAAC,cACvC;IACN,MAAM,aAAa,QAAQ,WAAW,CAAC,aAAa,CAAC;IACrD,IAAI,YAAY;QACZ,KAAK,MAAM,eAAe,WAAY;YAClC,QAAQ,cAAc,aAAa;QACvC;IACJ;IACA,OAAO;AACX;AACA,SAAS,cAAc,IAAI,EAAE,OAAO;IAChC,IAAI,QAAQ,WAAW,CAAC,aAAa,CAAC,OAAO;QACzC,OAAO,iBAAiB,MAAM;IAClC;IACA,IAAI,QAAQ,WAAW,CAAC,UAAU,CAAC,OAAO;QACtC,OAAO,kBAAkB,MAAM;IACnC;IACA,IAAI,QAAQ,WAAW,CAAC,aAAa,CAAC,OAAO;QACzC,OAAO,qBAAqB,MAAM;IACtC;IACA,IAAI,QAAQ,WAAW,CAAC,kBAAkB,CAAC,OAAO;QAC9C,OAAO,0BAA0B,MAAM;IAC3C;IACA,2CAA2C;IAC3C,OAAO;AACX;AACA,SAAS,iBAAiB,IAAI,EAAE,OAAO;IACnC,MAAM,KAAK,QAAQ,WAAW,CAAC,UAAU,CAAC;IAC1C,OAAO,CAAC,CAAC,EAAE,KAAK,oBAAoB,MAAM,SAAS,CAAC,EAAE,cAAc,MAAM,WAAW,KAAK,GAAG,oBAAoB,MAAM,SAAS,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/I;AACA,SAAS,oBAAoB,IAAI,EAAE,EAAE,WAAW,EAAE;IAC9C,IAAI,OAAO;IACX,KAAK,MAAM,QAAQ,YAAY,WAAW,CAAC,MAAO;QAC9C,QAAQ;QACR,IAAI,KAAK,SAAS,EAAE;YAChB,OAAQ,KAAK,SAAS;gBAClB,KAAK,gJAAA,CAAA,KAAE,CAAC,GAAG;oBAAE;wBACT,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;wBAC1B;oBACJ;gBACA,KAAK,gJAAA,CAAA,KAAE,CAAC,KAAK;oBAAE;wBACX,IAAI,KAAK,IAAI,KAAK,SAAS;4BACvB,QAAQ;wBACZ;wBACA,QAAQ,KAAK,IAAI;wBACjB;oBACJ;gBACA,KAAK,gJAAA,CAAA,KAAE,CAAC,KAAK;oBAAE;wBACX,QAAQ,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;wBAC5B;oBACJ;gBACA;oBAAS;wBACL,QAAQ,GAAG,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;oBACzC;YACJ;QACJ,OACK;YACD,QAAQ,KAAK,IAAI;QACrB;QACA,QAAQ,CAAC,EAAE,EAAE,CAAA,GAAA,iJAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,KAAK,EAAE,CAAC,CAAC;IAC/C;IACA,OAAO;AACX;AACA,SAAS,kBAAkB,IAAI,EAAE,OAAO;IACpC,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,MAAM,UAAU,YAAY,kBAAkB,CAAC;IAC/C,MAAM,SAAS,YAAY,aAAa,CAAC;IACzC,MAAM,WAAW,UAAU,YAAY,aAAa,CAAC,WAAW,YAAY,UAAU,CAAC;IACvF,OAAO,YACH,YAAY,eAAe,CAAC,YAAY,gJAAA,CAAA,KAAE,CAAC,IAAI,IAC/C,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,QAAQ,gBAAgB,IACjD,UACA,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE;AACrB;AACA,SAAS,qBAAqB,IAAI,EAAE,EAAE,WAAW,EAAE;IAC/C,OAAO,CAAC,IAAI,EAAE,YAAY,qBAAqB,CAAC,MAAM,GAAG,CAAC;AAC9D;AACA,SAAS,0BAA0B,IAAI,EAAE,EAAE,WAAW,EAAE;IACpD,OAAO,CAAC,UAAU,EAAE,YAAY,uBAAuB,CAAC,MAAM,CAAC,CAAC;AACpE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8974, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/parse5/dist/index.js"], "sourcesContent": ["import { Parser } from './parser/index.js';\nexport { defaultTreeAdapter } from './tree-adapters/default.js';\nexport { /** @internal */ Parser } from './parser/index.js';\nexport { serialize, serializeOuter } from './serializer/index.js';\nexport { ERR as ErrorCodes } from './common/error-codes.js';\n/** @internal */\nexport * as foreignContent from './common/foreign-content.js';\nexport * as html from './common/html.js';\nexport * as Token from './common/token.js';\n/** @internal */\nexport { Tokenizer, TokenizerMode } from './tokenizer/index.js';\n// Shorthands\n/**\n * Parses an HTML string.\n *\n * @param html Input HTML string.\n * @param options Parsing options.\n * @returns Document\n *\n * @example\n *\n * ```js\n * const parse5 = require('parse5');\n *\n * const document = parse5.parse('<!DOCTYPE html><html><head></head><body>Hi there!</body></html>');\n *\n * console.log(document.childNodes[1].tagName); //> 'html'\n *```\n */\nexport function parse(html, options) {\n    return Parser.parse(html, options);\n}\nexport function parseFragment(fragmentContext, html, options) {\n    if (typeof fragmentContext === 'string') {\n        options = html;\n        html = fragmentContext;\n        fragmentContext = null;\n    }\n    const parser = Parser.getFragmentParser(fragmentContext, options);\n    parser.tokenizer.write(html, true);\n    return parser.getFragment();\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AACA;AACA,cAAc,GACd;AACA;AACA;AACA,cAAc,GACd;;;;;;;;;;AAmBO,SAAS,MAAM,IAAI,EAAE,OAAO;IAC/B,OAAO,iJAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM;AAC9B;AACO,SAAS,cAAc,eAAe,EAAE,IAAI,EAAE,OAAO;IACxD,IAAI,OAAO,oBAAoB,UAAU;QACrC,UAAU;QACV,OAAO;QACP,kBAAkB;IACtB;IACA,MAAM,SAAS,iJAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,iBAAiB;IACzD,OAAO,SAAS,CAAC,KAAK,CAAC,MAAM;IAC7B,OAAO,OAAO,WAAW;AAC7B", "ignoreList": [0], "debugId": null}}]}