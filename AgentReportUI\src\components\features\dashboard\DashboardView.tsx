"use client";

import React, { useMemo, useState, useCallback } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { BarChart3 } from 'lucide-react';
import { Dashboard, ChartWidget as ChartWidgetType, CHART_CONFIG, UpdateDashboardRequest, DragState } from '@/types';
import ChartWidget from './ChartWidget';
import InlineEdit from '@/components/ui/inline-edit';
import DragToDeleteZone from './DragToDeleteZone';

// Import CSS for react-grid-layout
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface DashboardViewProps {
  dashboard: Dashboard;
  widgets: ChartWidgetType[];
  onCreateChart: () => void;
  onDeleteWidget: (widgetId: string) => void;
  onUpdateWidget: (widgetId: string, updates: Partial<ChartWidgetType>) => void;
  onLayoutChange: (layout: any[]) => void;
  onUpdateDashboard: (dashboardId: string, updates: UpdateDashboardRequest) => Promise<void>;
  className?: string;
}

const DashboardView: React.FC<DashboardViewProps> = ({
  dashboard,
  widgets,
  onCreateChart,
  onDeleteWidget,
  onUpdateWidget,
  onLayoutChange,
  onUpdateDashboard,
  className = '',
}) => {
  // API calls are now handled by parent Dashboard component
  
  // Drag-to-delete state
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedWidgetId: null,
    isOverDeleteZone: false,
  });

  // Sidebar state is now managed by parent Dashboard component

  // Generate layout from widgets
  const layouts = useMemo(() => {
    const layout = widgets.map((widget) => ({
      i: widget.id,
      x: widget.layout.x,
      y: widget.layout.y,
      w: widget.layout.w,
      h: widget.layout.h,
      minW: CHART_CONFIG.MIN_WIDGET_SIZE.w,
      minH: CHART_CONFIG.MIN_WIDGET_SIZE.h,
      maxW: CHART_CONFIG.MAX_WIDGET_SIZE.w,
      maxH: CHART_CONFIG.MAX_WIDGET_SIZE.h,
    }));

    return {
      lg: layout,
      md: layout,
      sm: layout.map(item => ({ 
        ...item, 
        w: Math.min(item.w, 6),
        maxW: 6 
      })),
      xs: layout.map(item => ({ 
        ...item, 
        w: Math.min(item.w, 4),
        maxW: 4 
      })),
      xxs: layout.map(item => ({ 
        ...item, 
        w: 2,
        maxW: 2 
      })),
    };
  }, [widgets]);

  // Drag event handlers
  const handleDragStart = useCallback((_layout: any[], _oldItem: any, newItem: any, _placeholder: any, _e: MouseEvent, _element: HTMLElement) => {
    console.log('Drag started for widget:', newItem.i); // Debug log
    setDragState({
      isDragging: true,
      draggedWidgetId: newItem.i,
      isOverDeleteZone: false,
    });
  }, []);



  // Mouse position tracking for delete zone collision detection
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!dragState.isDragging) return;

    // Get delete zone element bounds
    const deleteZone = document.querySelector('.drag-delete-zone');
    if (!deleteZone) return;

    const rect = deleteZone.getBoundingClientRect();
    const isOverZone = (
      e.clientX >= rect.left &&
      e.clientX <= rect.right &&
      e.clientY >= rect.top &&
      e.clientY <= rect.bottom
    );

    // Update state only if hover state changed
    if (isOverZone !== dragState.isOverDeleteZone) {
      setDragState(prev => ({ ...prev, isOverDeleteZone: isOverZone }));
      console.log('Delete zone hover:', isOverZone); // Debug log
    }
  }, [dragState.isDragging, dragState.isOverDeleteZone]);

  // Enhanced drag stop handler with delete zone check
  const handleDragStopWithDelete = useCallback((_layout: any[], _oldItem: any, _newItem: any, _placeholder: any, _e: MouseEvent, _element: HTMLElement) => {
    const wasOverDeleteZone = dragState.isOverDeleteZone;
    const widgetId = dragState.draggedWidgetId;

    console.log('Drag stopped - Over delete zone:', wasOverDeleteZone, 'Widget ID:', widgetId); // Debug log

    setDragState({
      isDragging: false,
      draggedWidgetId: null,
      isOverDeleteZone: false,
    });

    // If dropped over delete zone, delete the widget
    if (wasOverDeleteZone && widgetId) {
      console.log('Deleting widget:', widgetId); // Debug log
      onDeleteWidget(widgetId);
      return; // Don't update layout if deleting
    }
  }, [dragState.isOverDeleteZone, dragState.draggedWidgetId, onDeleteWidget]);

  // Add/remove mouse move listener when dragging
  React.useEffect(() => {
    if (dragState.isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      return () => document.removeEventListener('mousemove', handleMouseMove);
    }
  }, [dragState.isDragging, handleMouseMove]);

  const canCreateChart = widgets.length < CHART_CONFIG.MAX_WIDGETS;

  // Sidebar handlers are now managed by parent Dashboard component

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Dashboard Info */}
      <div className="space-y-3">
        <div className="flex items-center">
          <InlineEdit
            value={dashboard.name}
            onSave={(newName) => onUpdateDashboard(dashboard.id, { name: newName })}
            placeholder="Dashboard name..."
            displayClassName="text-2xl font-bold text-foreground"
            editClassName="text-3xl font-bold"
            maxLength={100}
            hideButtons={true}
          />
        </div>
      </div>

      {/* Chart Widgets Grid */}
      {widgets.length > 0 && (
        <div className="relative">
          <ResponsiveGridLayout
            className="layout"
            layouts={layouts}
            breakpoints={CHART_CONFIG.BREAKPOINTS}
            cols={CHART_CONFIG.GRID_COLS}
            rowHeight={CHART_CONFIG.ROW_HEIGHT}
            onLayoutChange={onLayoutChange}
            onDragStart={handleDragStart}
            onDragStop={handleDragStopWithDelete}
            isDraggable={true}
            isResizable={true}
            margin={[4, 4]}
            containerPadding={[0, 0]}
            // Chart sizing constraints:
            // - Minimum: 3×4 grid units (246×356px at default row height)
            // - Maximum: 12×15 grid units (max width × 3× default height)
            // - Users can drag freely and resize within these bounds
          >
            {widgets.map((widget) => (
              <div key={widget.id} className="grid-item">
                <ChartWidget
                  widget={widget}
                  onDelete={onDeleteWidget}
                  onUpdate={onUpdateWidget}
                  className="h-full"
                />
              </div>
            ))}
          </ResponsiveGridLayout>
        </div>
      )}

      {/* Drag-to-Delete Zone */}
      <DragToDeleteZone
        isVisible={dragState.isDragging}
        isHovered={dragState.isOverDeleteZone}
      />
    </div>
  );
};

export default DashboardView;
