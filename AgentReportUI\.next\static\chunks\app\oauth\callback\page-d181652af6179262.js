(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[720],{35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},55559:(e,t,r)=>{Promise.resolve().then(r.bind(r,81306))},81306:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(95155),s=r(12115),n=r(35695),i=r(74045);function o(){let e=(0,n.useRouter)(),t=(0,n.useSearchParams)(),{handleOAuthCallback:r,isNewUser:o,isLoading:l}=(0,i.A)(),[c,d]=(0,s.useState)("loading"),[u,g]=(0,s.useState)(""),[h,m]=(0,s.useState)(!1),[x,k]=(0,s.useState)(!1),f=(0,s.useRef)(!1);return(0,s.useEffect)(()=>{f.current||(async()=>{try{f.current=!0,console.log("OAuth callback page loaded"),console.log("Current URL:",window.location.href),console.log("Search params:",Object.fromEntries(t.entries()));let a=t.get("error"),s=t.get("error_description");if(a){let e="Authentication failed. Please try again.";throw e="code_already_used"===a?"Authorization code has already been used. Please try signing in again.":"oauth_failed"===a?"OAuth authentication failed. Please try again.":"missing_tokens"===a?"Authentication tokens are missing. Please try again.":"OAuth Error: ".concat(a," - ").concat(s||"Unknown error"),Error(e)}let n=t.get("access_token"),i=t.get("refresh_token"),c=t.get("user_id"),u=t.get("expires_in"),g=t.get("token_type")||"bearer";if(!n||!c)throw Error("Missing required authentication tokens. Please try signing in again.");console.log("✅ Found tokens in URL params - OAuth successful!");let h=u?parseInt(u,10):86400;await r({access_token:n,refresh_token:i||"",expires_in:h,user_id:c,token_type:g}),d("success"),new Promise(e=>{let t=()=>{l?setTimeout(t,100):(k(!0),e())};t()}).then(()=>{m(!0),setTimeout(()=>{o?(console.log("✅ New user authenticated, redirecting to onboarding..."),e.push("/onboarding")):(console.log("✅ Existing user authenticated, redirecting to dashboard..."),e.push("/dashboard"))},500)})}catch(o){var a,s,n,i;console.error("OAuth callback error:",o);let t="Authentication failed",r="oauth_failed";(null==(a=o.message)?void 0:a.includes("network"))||(null==(s=o.message)?void 0:s.includes("fetch"))?(t="Network error during authentication. Please check your connection.",r="network_error"):(null==(n=o.message)?void 0:n.includes("token"))?(t="Invalid authentication token received.",r="invalid_token"):(null==(i=o.message)?void 0:i.includes("user_state"))&&(t="Failed to determine user onboarding status.",r="user_state_error"),g(t),d("error"),setTimeout(()=>{let a=new URLSearchParams({oauth_error:t,error_code:r,timestamp:new Date().toISOString()});e.push("/login?".concat(a.toString()))},4e3)}})()},[]),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"max-w-md w-full space-y-8 p-8",children:(0,a.jsxs)("div",{className:"text-center",children:["loading"===c&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Completing sign in..."}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Please wait while we authenticate you with Google."})]}),"success"===c&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Sign in successful!"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:h?o?"Redirecting to onboarding...":"Redirecting to dashboard...":"Preparing your account..."})]}),"error"===c&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Authentication failed"}),(0,a.jsx)("p",{className:"text-red-600 dark:text-red-400 mt-2 text-sm",children:u}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2 text-sm",children:"Redirecting to login page..."})]})]})})})}function l(){return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"max-w-md w-full space-y-8 p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Loading..."}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Preparing OAuth callback..."})]})})}),children:(0,a.jsx)(o,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,189,45,441,684,358],()=>t(55559)),_N_E=e.O()}]);