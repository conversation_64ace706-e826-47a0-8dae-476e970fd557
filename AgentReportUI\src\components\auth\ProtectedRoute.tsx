'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/providers/AuthContext';
import { useRouter, usePathname } from 'next/navigation';
import { isProtectedRoute } from '@/lib/utils/onboarding';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

export default function ProtectedRoute({ 
  children, 
  requireAuth = true, 
  redirectTo = '/login' 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, isNewUser, signIn } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [hasTriedAutoAuth, setHasTriedAutoAuth] = useState(false);

  useEffect(() => {
    const checkAuthentication = async () => {
      // Skip auth check for public routes
      if (!requireAuth || !isProtectedRoute(pathname)) {
        setIsCheckingAuth(false);
        return;
      }

      // If already authenticated, handle onboarding redirect
      if (isAuthenticated) {
        if (isNewUser && pathname !== '/onboarding') {
          console.log('New user on protected route, redirecting to onboarding');
          router.push('/onboarding');
          return;
        }
        if (!isNewUser && pathname === '/onboarding') {
          console.log('Existing user on onboarding, redirecting to dashboard');
          router.push('/dashboard');
          return;
        }
        setIsCheckingAuth(false);
        return;
      }

      // If not authenticated and haven't tried auto-auth yet, try it once
      if (!isAuthenticated && !hasTriedAutoAuth && !isLoading) {
        setHasTriedAutoAuth(true);
        console.log('Attempting automatic authentication from stored tokens...');

        try {
          await signIn(undefined, false); // Don't redirect, just authenticate
          // If successful, the useEffect will re-run due to isAuthenticated change
        } catch (error) {
          console.log('Auto-authentication failed, redirecting to login');
          router.push(redirectTo);
        }
        return;
      }

      // If not authenticated and already tried auto-auth, redirect to login
      if (!isAuthenticated && hasTriedAutoAuth && !isLoading) {
        console.log('Not authenticated, redirecting to login');
        router.push(redirectTo);
        return;
      }

      setIsCheckingAuth(false);
    };

    checkAuthentication();
  }, [isAuthenticated, isLoading, isNewUser, pathname, requireAuth, redirectTo, router, signIn, hasTriedAutoAuth]);

  // Show loading state while checking authentication
  if (isCheckingAuth || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // For protected routes, only render children if authenticated
  if (requireAuth && !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Authentication Required</h2>
          <p className="text-muted-foreground mb-4">Please sign in to access this page.</p>
          <button
            onClick={() => router.push(redirectTo)}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
          >
            Sign In
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// Higher-order component for easy wrapping
export function withProtectedRoute<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<ProtectedRouteProps, 'children'>
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// Hook for manual authentication checks
export function useRequireAuth(redirectTo: string = '/login') {
  const { isAuthenticated, isLoading, signIn } = useAuth();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      if (isLoading) return;

      if (!isAuthenticated) {
        try {
          await signIn(undefined, false); // Don't redirect, just authenticate
        } catch {
          router.push(redirectTo);
        }
      }
      setIsChecking(false);
    };

    checkAuth();
  }, [isAuthenticated, isLoading, signIn, router, redirectTo]);

  return { isAuthenticated, isLoading: isLoading || isChecking };
}
