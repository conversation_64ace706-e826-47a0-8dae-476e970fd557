import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useApi, ConnectedDatabase } from '@/providers/ApiContext';

interface DataSource {
  id: string;
  name: string;
  type: 'database' | 'file' | 'cloud' | 'other';
  description: string;
  isConnected: boolean;
}

interface DataSourcesContextType {
  dataSources: DataSource[];
  connectedDataSources: DataSource[];
  isLoading: boolean;
  error: string | null;
  connectDataSource: (sourceId: string) => Promise<void>;
  disconnectDataSource: (sourceId: string) => Promise<void>;
  getDataSource: (sourceId: string) => DataSource | undefined;
  refreshDataSources: () => Promise<void>;
}

const DataSourcesContext = createContext<DataSourcesContextType | undefined>(undefined);

export const useDataSources = () => {
  const context = useContext(DataSourcesContext);
  if (!context) {
    throw new Error('useDataSources must be used within a DataSourcesProvider');
  }
  return context;
};

interface DataSourcesProviderProps {
  children: React.ReactNode;
}

export const DataSourcesProvider: React.FC<DataSourcesProviderProps> = ({ children }) => {
  const { listDatabases, disconnectExistingDatabase } = useApi();
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Convert ConnectedDatabase to DataSource format
  const convertConnectedDatabase = useCallback((db: ConnectedDatabase): DataSource => ({
    id: db.id,
    name: db.name,
    type: 'database', // All connected databases are database type
    description: db.description || `${db.type} database`,
    isConnected: true, // All returned databases are connected
  }), []);

  // Fetch connected databases
  const fetchConnectedDatabases = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('Fetching connected databases...');
      const connectedDbs = await listDatabases();
      const dataSources = connectedDbs.map(convertConnectedDatabase);
      setDataSources(dataSources);
      setIsInitialized(true);
    } catch (err) {
      console.error('Failed to fetch connected databases:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch connected databases');
      // Don't set mock data - let the UI handle empty state
      setDataSources([]);
      setIsInitialized(true);
    } finally {
      setIsLoading(false);
    }
  }, [listDatabases, convertConnectedDatabase]);

  // Initialize data sources on mount - only once
  useEffect(() => {
    if (!isInitialized) {
      fetchConnectedDatabases();
    }
  }, [fetchConnectedDatabases, isInitialized]);

  // Get only connected data sources (which should be all of them now)
  const connectedDataSources = dataSources.filter(source => source.isConnected);

  // Connect a data source (not implemented - would need connection parameters)
  const connectDataSource = useCallback(async (sourceId: string) => {
    // This would require a connection form/modal to collect database credentials
    // For now, we'll just refresh the list
    console.log('Connect data source not implemented - use the Data Sources page');
    setError('Please use the Data Sources page to connect new databases');
  }, []);

  // Disconnect a data source
  const disconnectDataSource = useCallback(async (sourceId: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Use the real API to disconnect
      await disconnectExistingDatabase(sourceId);
      
      // Refresh the list
      await fetchConnectedDatabases();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to disconnect data source');
      setIsLoading(false);
    }
  }, [disconnectExistingDatabase, fetchConnectedDatabases]);

  // Get a specific data source by ID
  const getDataSource = useCallback((sourceId: string) => {
    return dataSources.find(source => source.id === sourceId);
  }, [dataSources]);

  // Manual refresh function
  const refreshDataSources = useCallback(async () => {
    await fetchConnectedDatabases();
  }, [fetchConnectedDatabases]);

  const value: DataSourcesContextType = {
    dataSources,
    connectedDataSources,
    isLoading,
    error,
    connectDataSource,
    disconnectDataSource,
    getDataSource,
    refreshDataSources,
  };

  return (
    <DataSourcesContext.Provider value={value}>
      {children}
    </DataSourcesContext.Provider>
  );
}; 