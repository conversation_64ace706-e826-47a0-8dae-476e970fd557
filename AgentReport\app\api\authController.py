"""Authentication API Routes

This module provides the API endpoints for user authentication.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, Response, status, Form, Query, Request, Body
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.models.user import (
    User,
    UserCreate,
    UserResponse,
    UserUpdate,
    Token,
    PasswordReset,
    AuthenticationResponse
)
from app.services.auth_service import AuthService
from app.utils.db import get_db
from app.utils.security import get_current_user, get_current_active_user
from app.utils.error_responses import AuthErrorResponse, ErrorCode
from app.utils.exceptions import (
    UserNotFoundError,
    UserInactiveError,
    InvalidTokenError,
    TokenExpiredError,
    TokenRevokedError,
    TokenNotFoundError,
    TokenBlacklistedError,
    TokenRotationError,
    ConcurrentTokenUseError,
    EmailAlreadyExistsError
)
from app.utils.oauth_state import generate_oauth_state, validate_oauth_state, get_client_type_from_state
from app.utils.request_context import get_client_type, should_return_json, get_correlation_id

router = APIRouter(
    prefix="/api/auth",
    tags=["auth"],
    responses={404: {"description": "Not found"}},
)

@router.post("/register", status_code=status.HTTP_201_CREATED)
async def register(
    request: Request,
    user_create: UserCreate,
    auth_service: AuthService = Depends(AuthService)
):
    """Register a new user with comprehensive email uniqueness validation."""
    correlation_id = get_correlation_id(request)

    try:
        user_response = auth_service.register_user(user_create)

        # For JSON responses, include user state information (new users are always new)
        if should_return_json(request):
            # user_response is now a dict from get_user_response
            if isinstance(user_response, dict):
                return {
                    **user_response,
                    "is_new_user": True,
                    "correlation_id": correlation_id
                }
            else:
                # Fallback for UserResponse model
                return {
                    **user_response.model_dump(),
                    "is_new_user": True,
                    "correlation_id": correlation_id
                }
        else:
            return user_response
    except EmailAlreadyExistsError:
        return AuthErrorResponse.create_error_response(
            ErrorCode.EMAIL_ALREADY_EXISTS,
            "An account with this email address already exists. Please use a different email or try logging in.",
            409,
            correlation_id,
            request
        )
    except Exception as e:
        return AuthErrorResponse.create_error_response(
            ErrorCode.INTERNAL_SERVER_ERROR,
            "An unexpected error occurred during registration.",
            500,
            correlation_id,
            request,
            details={"exception_type": type(e).__name__}
        )

@router.post("/login")
async def login_for_access_token(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    remember_me: bool = Form(False),
    auth_service: AuthService = Depends(AuthService)
):
    """Login to get access and refresh tokens with enhanced error handling, user state detection, and remember me functionality."""
    correlation_id = get_correlation_id(request)

    try:
        token_response = auth_service.login(form_data.username, form_data.password, remember_me=remember_me)

        # Always return enhanced response with user state information
        from datetime import datetime
        expires_in = int((token_response.expires_at - datetime.utcnow()).total_seconds())

        if should_return_json(request):
            # Return AuthenticationResponse for JSON clients
            return AuthenticationResponse(
                access_token=token_response.access_token,
                refresh_token=token_response.refresh_token,
                token_type=token_response.token_type,
                expires_in=expires_in,
                user_id=token_response.user_id,
                is_new_user=getattr(token_response, 'is_new_user', False),
                correlation_id=correlation_id
            )
        else:
            # Return Token model for traditional clients
            return token_response
    except UserNotFoundError:
        return AuthErrorResponse.create_error_response(
            ErrorCode.USER_NOT_FOUND,
            "No account found with this email address.",
            401,
            correlation_id,
            request
        )
    except UserInactiveError:
        return AuthErrorResponse.create_error_response(
            ErrorCode.USER_INACTIVE,
            "Your account has been deactivated. Please contact support.",
            401,
            correlation_id,
            request
        )
    except InvalidTokenError:
        return AuthErrorResponse.create_error_response(
            ErrorCode.INVALID_CREDENTIALS,
            "Invalid email or password.",
            401,
            correlation_id,
            request
        )
    except Exception as e:
        return AuthErrorResponse.create_error_response(
            ErrorCode.INTERNAL_SERVER_ERROR,
            "An unexpected error occurred during login.",
            500,
            correlation_id,
            request,
            details={"exception_type": type(e).__name__}
        )

@router.post("/refresh", response_model=Token)
async def refresh_token(
    request: Request,
    refresh_token: str = Form(...),
    auth_service: AuthService = Depends(AuthService)
):
    """Refresh an access token with enhanced error handling."""
    correlation_id = get_correlation_id(request)

    try:
        return auth_service.refresh_token(refresh_token)
    except TokenExpiredError:
        return AuthErrorResponse.create_error_response(
            ErrorCode.TOKEN_EXPIRED,
            "Your session has expired. Please log in again.",
            401,
            correlation_id,
            request
        )
    except TokenRevokedError:
        return AuthErrorResponse.create_error_response(
            ErrorCode.TOKEN_REVOKED,
            "Your session has been revoked. Please log in again.",
            401,
            correlation_id,
            request
        )
    except TokenNotFoundError:
        return AuthErrorResponse.create_error_response(
            ErrorCode.TOKEN_NOT_FOUND,
            "Invalid refresh token. Please log in again.",
            401,
            correlation_id,
            request
        )
    except TokenBlacklistedError:
        return AuthErrorResponse.create_error_response(
            ErrorCode.TOKEN_BLACKLISTED,
            "Your session is no longer valid. Please log in again.",
            401,
            correlation_id,
            request
        )
    except TokenRotationError:
        return AuthErrorResponse.create_error_response(
            ErrorCode.TOKEN_ROTATION_FAILED,
            "Failed to refresh your session. Please log in again.",
            500,
            correlation_id,
            request
        )
    except ConcurrentTokenUseError:
        return AuthErrorResponse.create_error_response(
            ErrorCode.CONCURRENT_TOKEN_USE,
            "Suspicious activity detected. Please log in again.",
            401,
            correlation_id,
            request
        )
    except Exception as e:
        return AuthErrorResponse.create_error_response(
            ErrorCode.INTERNAL_SERVER_ERROR,
            "An unexpected error occurred during token refresh.",
            500,
            correlation_id,
            request,
            details={"exception_type": type(e).__name__}
        )

@router.post("/logout", status_code=status.HTTP_204_NO_CONTENT)
async def logout(
    request: Request,
    refresh_token: str = Form(...),
    auth_service: AuthService = Depends(AuthService)
):
    """Logout a user by revoking their refresh token with enhanced error handling and resource cleanup."""
    correlation_id = get_correlation_id(request)

    try:
        # Get user info before logout for cleanup
        try:
            from app.utils.security import get_token_data
            # Try to extract user ID from refresh token for cleanup
            # Note: This is a best-effort cleanup, may not always work with expired tokens
            user_id = None
            try:
                # Attempt to get user from refresh token (may fail if token is invalid)
                db = next(get_db())
                from app.models.user import RefreshToken as RefreshTokenDB
                token_record = db.query(RefreshTokenDB).filter(RefreshTokenDB.token == refresh_token).first()
                if token_record:
                    user_id = token_record.user_id
            except:
                pass

            # Clean up user resources if we have user_id
            if user_id:
                from app.services.database_manager_service import DatabaseManagerService
                import logging
                logger = logging.getLogger(__name__)
                db_manager = DatabaseManagerService()
                db_manager.cleanup_user_resources(user_id, "user_logout")
                logger.info(f"Cleaned up resources for user {user_id} during logout")
        except Exception as cleanup_error:
            # Don't fail logout if cleanup fails
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Resource cleanup failed during logout: {cleanup_error}")

        # Proceed with logout
        auth_service.logout(refresh_token)
        return Response(status_code=status.HTTP_204_NO_CONTENT)
    except TokenNotFoundError:
        return AuthErrorResponse.create_error_response(
            ErrorCode.TOKEN_NOT_FOUND,
            "Invalid refresh token.",
            401,
            correlation_id,
            request
        )
    except Exception as e:
        return AuthErrorResponse.create_error_response(
            ErrorCode.INTERNAL_SERVER_ERROR,
            "An unexpected error occurred during logout.",
            500,
            correlation_id,
            request,
            details={"exception_type": type(e).__name__}
        )

@router.post("/me")
async def get_current_user_info(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(AuthService)
):
    """Get the current user's information with enhanced user state detection and automatic session extension."""
    user_response = auth_service.get_user_response(current_user)
    
    # Check if we should extend the session (sliding sessions)
    # This requires extracting token expiration from the current token
    token = request.headers.get("Authorization", "").replace("Bearer ", "")
    if token:
        try:
            from app.utils.security import decode_token
            from datetime import datetime
            
            token_data = decode_token(token)
            if "exp" in token_data:
                token_expires = datetime.fromtimestamp(token_data["exp"])
                session_extension = auth_service.extend_session_if_needed(current_user, token_expires)
                
                if session_extension:
                    new_token, new_expires = session_extension
                    # Add new token information to response for client to update
                    user_response["session_extended"] = True
                    user_response["new_access_token"] = new_token
                    user_response["new_expires_at"] = new_expires.isoformat()
        except Exception:
            # If token processing fails, just return the user info without extension
            pass
    
    return user_response

@router.patch("/me")
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(AuthService)
):
    """Update the current user."""
    return auth_service.update_current_user(current_user.id, user_update)

@router.post("/password-reset/request")
async def request_password_reset(
    email: str = Form(...),
    auth_service: AuthService = Depends(AuthService)
):
    """Request a password reset."""
    return auth_service.request_password_reset_handler(email)

@router.post("/password-reset/confirm")
async def reset_password(
    password_reset: PasswordReset,
    auth_service: AuthService = Depends(AuthService)
):
    """Reset a password with a reset token."""
    return auth_service.reset_password_handler(password_reset)

@router.post("/verify-email")
async def verify_email(
    token: str = Body(..., embed=True),
    auth_service: AuthService = Depends(AuthService)
):
    """Verify an email with a verification token."""
    return auth_service.verify_email_handler(token)

@router.post("/onboarding/complete")
async def complete_onboarding(
    request: Request,
    onboarding_data: dict = Body(default={}),
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(AuthService)
):
    """Mark user onboarding as complete and store onboarding data."""
    correlation_id = get_correlation_id(request)

    try:
        success = auth_service.mark_onboarding_complete(
            user_id=current_user.id,
            onboarding_data=onboarding_data
        )

        if success:
            return {
                "success": True,
                "message": "Onboarding completed successfully",
                "correlation_id": correlation_id
            }
        else:
            return AuthErrorResponse.create_error_response(
                ErrorCode.INTERNAL_SERVER_ERROR,
                "Failed to complete onboarding",
                500,
                correlation_id,
                request
            )
    except Exception as e:
        return AuthErrorResponse.create_error_response(
            ErrorCode.INTERNAL_SERVER_ERROR,
            "An unexpected error occurred during onboarding completion",
            500,
            correlation_id,
            request,
            details={"exception_type": type(e).__name__}
        )

@router.post("/google")
async def login_google(
    request: Request,
    auth_service: AuthService = Depends(AuthService)
):
    """Get the Google OAuth authorization URL with enhanced state management."""
    correlation_id = get_correlation_id(request)

    # For OAuth flows, always use "browser" client type since Google will redirect
    # back to the browser regardless of how the initial request was made
    client_type = "browser"

    # Generate secure OAuth state
    oauth_state = generate_oauth_state(
        client_type=client_type,
        correlation_id=correlation_id
    )

    try:
        # Get OAuth URL with state
        auth_url = await auth_service.get_google_auth_url(state=oauth_state)

        return {
            "success": True,
            "auth_url": auth_url,
            "state": oauth_state,
            "client_type": client_type,
            "correlation_id": correlation_id
        }

    except Exception as e:
        return AuthErrorResponse.create_error_response(
            ErrorCode.OAUTH_PROVIDER_ERROR,
            "Failed to generate Google OAuth URL.",
            500,
            correlation_id,
            request,
            details={"exception_type": type(e).__name__}
        )

@router.get("/google/callback")
async def google_callback_get(
    request: Request,
    code: str = Query(...),
    state: str = Query(None),
    auth_service: AuthService = Depends(AuthService)
):
    """Handle the Google OAuth callback via GET redirect from Google."""
    correlation_id = get_correlation_id(request)

    try:
        # Validate OAuth state parameter for CSRF protection
        client_type = "browser"  # Default for GET requests
        if state:
            is_valid_state, state_payload = validate_oauth_state(state)
            if not is_valid_state:
                return AuthErrorResponse.create_error_response(
                    ErrorCode.OAUTH_STATE_MISMATCH,
                    "Invalid or expired OAuth state parameter.",
                    400,
                    correlation_id,
                    request
                )

            # Extract client type from state
            client_type = get_client_type_from_state(state)

            # Use correlation ID from state if available
            state_correlation_id = state_payload.get("correlation_id")
            if state_correlation_id:
                correlation_id = state_correlation_id

        # Handle OAuth callback
        token_response = await auth_service.handle_google_callback(code)

        # Calculate expires_in from expires_at
        from datetime import datetime
        expires_in = int((token_response.expires_at - datetime.utcnow()).total_seconds())

        # Determine response format based on client type and request
        if should_return_json(request) or client_type in ["spa", "mobile", "api"]:
            # Return JSON response for SPAs, mobile apps, and API clients
            return AuthenticationResponse(
                access_token=token_response.access_token,
                refresh_token=token_response.refresh_token,
                token_type=token_response.token_type,
                expires_in=expires_in,
                user_id=getattr(token_response, 'user_id', None),
                is_new_user=getattr(token_response, 'is_new_user', False),
                client_type=client_type,
                correlation_id=correlation_id
            )
        else:
            # For browser requests, redirect to frontend with tokens
            from fastapi.responses import RedirectResponse

            # Construct redirect URL with tokens
            frontend_callback_url = "http://localhost:3000/oauth/callback"
            redirect_url = (
                f"{frontend_callback_url}?"
                f"access_token={token_response.access_token}&"
                f"refresh_token={token_response.refresh_token}&"
                f"token_type={token_response.token_type}&"
                f"expires_in={expires_in}&"
                f"user_id={getattr(token_response, 'user_id', '')}&"
                f"is_new_user={getattr(token_response, 'is_new_user', False)}&"
                f"client_type={client_type}&"
                f"correlation_id={correlation_id}"
            )

            return RedirectResponse(url=redirect_url)

    except Exception as e:
        # Check if this is a "code already used" error from Google
        error_message = str(e).lower()
        if "invalid_grant" in error_message or "authorization code" in error_message:
            # This is likely a duplicate request with an already-used code
            # For browser requests, redirect to frontend with a specific error
            if not should_return_json(request) and client_type == "browser":
                from fastapi.responses import RedirectResponse
                error_url = f"http://localhost:3000/oauth/callback?error=code_already_used&error_description=Authorization code has already been used. Please try logging in again."
                return RedirectResponse(url=error_url)
            else:
                return AuthErrorResponse.create_error_response(
                    ErrorCode.OAUTH_PROVIDER_ERROR,
                    "Authorization code has already been used. Please initiate a new OAuth flow.",
                    400,
                    correlation_id,
                    request,
                    details={"error_type": "code_already_used"}
                )

        # For other errors, use the standard error handling
        if not should_return_json(request) and client_type == "browser":
            from fastapi.responses import RedirectResponse
            error_url = f"http://localhost:3000/oauth/callback?error=authentication_failed&error_description={str(e)}"
            return RedirectResponse(url=error_url)
        else:
            return AuthErrorResponse.from_exception(
                e,
                request,
                correlation_id
            )


@router.post("/google/callback")
async def google_callback_post(
    request: Request,
    code: str = Body(..., embed=True),
    state: str = Body(None, embed=True),
    auth_service: AuthService = Depends(AuthService)
):
    """Handle the Google OAuth callback with enhanced state validation and client type detection."""
    correlation_id = get_correlation_id(request)

    try:
        # Validate OAuth state parameter for CSRF protection
        client_type = "browser"  # Default
        if state:
            is_valid_state, state_payload = validate_oauth_state(state)
            if not is_valid_state:
                return AuthErrorResponse.create_error_response(
                    ErrorCode.OAUTH_STATE_MISMATCH,
                    "Invalid or expired OAuth state parameter.",
                    400,
                    correlation_id,
                    request
                )

            # Extract client type from state
            client_type = get_client_type_from_state(state)

            # Use correlation ID from state if available
            state_correlation_id = state_payload.get("correlation_id")
            if state_correlation_id:
                correlation_id = state_correlation_id

        # Handle OAuth callback
        token_response = await auth_service.handle_google_callback(code)

        # Calculate expires_in from expires_at
        from datetime import datetime
        expires_in = int((token_response.expires_at - datetime.utcnow()).total_seconds())

        # Determine response format based on client type
        if should_return_json(request) or client_type in ["spa", "mobile", "api"]:
            # Return JSON response for SPAs, mobile apps, and API clients
            return AuthenticationResponse(
                access_token=token_response.access_token,
                refresh_token=token_response.refresh_token,
                token_type=token_response.token_type,
                expires_in=expires_in,
                user_id=getattr(token_response, 'user_id', None),
                is_new_user=getattr(token_response, 'is_new_user', False),
                client_type=client_type,
                correlation_id=correlation_id
            )
        else:
            # Return standard token response for traditional browsers
            return token_response

    except Exception as e:
        # Check if this is a "code already used" error from Google
        error_message = str(e).lower()
        if "invalid_grant" in error_message or "authorization code" in error_message:
            return AuthErrorResponse.create_error_response(
                ErrorCode.OAUTH_PROVIDER_ERROR,
                "Authorization code has already been used. Please initiate a new OAuth flow.",
                400,
                correlation_id,
                request,
                details={"error_type": "code_already_used"}
            )

        return AuthErrorResponse.from_exception(
            e,
            request,
            correlation_id
        )
