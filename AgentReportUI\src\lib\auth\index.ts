// Authentication utilities

import { STORAGE_KEYS } from '@/lib/constants';
import { isTokenExpired } from '@/lib/utils';

export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
  const expiresAt = localStorage.getItem(STORAGE_KEYS.EXPIRES_AT);
  
  if (!token || !expiresAt) {
    return false;
  }
  
  return !isTokenExpired(expiresAt);
};

export const getUserId = (): string | null => {
  return localStorage.getItem(STORAGE_KEYS.USER_ID);
};

export const getTokenExpirationTime = (): Date | null => {
  const expiresAt = localStorage.getItem(STORAGE_KEYS.EXPIRES_AT);
  return expiresAt ? new Date(expiresAt) : null;
};

export const shouldRefreshToken = (): boolean => {
  const expiresAt = localStorage.getItem(STORAGE_KEYS.EXPIRES_AT);
  if (!expiresAt) return false;
  
  const expirationTime = new Date(expiresAt).getTime();
  const currentTime = new Date().getTime();
  const bufferTime = 10 * 60 * 1000; // 10 minutes buffer
  
  return currentTime >= (expirationTime - bufferTime);
};

export const redirectToLogin = (): void => {
  window.location.href = '/login';
};

export const redirectToDashboard = (): void => {
  window.location.href = '/dashboard';
};
