"use client";
import React from 'react';
import Layout from '@/components/layout/Layout';
import SettingsPageContent from '@/components/features/settings/SettingsPageContent';
import { useAuth } from '@/providers/AuthContext';
import { useRouter } from 'next/navigation';

export default function SettingsPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  React.useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <Layout>
      <SettingsPageContent />
    </Layout>
  );
} 