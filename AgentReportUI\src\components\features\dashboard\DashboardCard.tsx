"use client";

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { MoreHorizontal, BarChart3, Calendar, Trash2, Edit } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Dashboard } from '@/types';

interface DashboardCardProps {
  dashboard: Dashboard;
  chartCount: number;
  onSelect: (dashboard: Dashboard) => void;
  onEdit?: (dashboard: Dashboard) => void;
  onDelete: (dashboardId: string) => void;
  className?: string;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  dashboard,
  chartCount,
  onSelect,
  onEdit,
  onDelete,
  className = '',
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Prevent card click when dropdown is clicked
    if ((e.target as HTMLElement).closest('[data-dropdown-trigger]')) {
      return;
    }
    onSelect(dashboard);
  };

  return (
    <Card 
      className={`cursor-pointer hover:shadow-md transition-shadow duration-200 ${className}`}
      onClick={handleCardClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          <BarChart3 className="h-5 w-5 text-primary" />
          <h3 className="font-semibold text-lg truncate">{dashboard.name}</h3>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild data-dropdown-trigger>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {onEdit && (
              <DropdownMenuItem onClick={() => onEdit(dashboard)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Dashboard
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onClick={() => onDelete(dashboard.id)}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Dashboard
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>

      <CardContent className="space-y-4">
        <p className="text-muted-foreground text-sm line-clamp-2">
          {dashboard.description || 'No description provided'}
        </p>
        
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-1 text-muted-foreground">
            <BarChart3 className="h-4 w-4" />
            <span>{chartCount} chart{chartCount !== 1 ? 's' : ''}</span>
          </div>
          
          <div className="flex items-center space-x-1 text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>{formatDate(dashboard.updated_at)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DashboardCard;
