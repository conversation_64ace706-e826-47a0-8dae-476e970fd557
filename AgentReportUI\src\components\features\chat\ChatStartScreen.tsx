"use client";

import { useState, useRef, useMemo } from "react";
import { PlaceholdersAndVanishInput } from "@/components/ui/placeholders-and-vanish-input";
import { usePageTitle } from '@/hooks/usePageTitle';
import { Plus } from 'lucide-react';

interface ChatStartScreenProps {
  onSendMessage: (message: string) => void;
}

export function ChatStartScreen({ onSendMessage }: ChatStartScreenProps) {
  const [isSending, setIsSending] = useState(false);
  const lastSubmitTime = useRef(0);
  const RATE_LIMIT_MS = 1000; // Prevent submissions within 1 second of each other

  // Set page title for new chat screen - memoized to prevent re-renders
  const pageConfig = useMemo(() => ({
    title: 'New Chat',
    icon: Plus
  }), []);

  usePageTitle(pageConfig);

  const placeholders = [
    "What would you like to know about your data?",
    "Ask me to analyze your database records",
    "Need help with data insights? Ask me anything",
    "What trends should we explore in your data?",
    "Looking for specific metrics or KPIs? Just ask",
  ];

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Rate limiting check
    const now = Date.now();
    if (now - lastSubmitTime.current < RATE_LIMIT_MS) {
      console.log('Rate limit exceeded, ignoring submission');
      return;
    }
    
    // Prevent multiple submissions while one is in progress
    if (isSending) {
      console.log('Message already being sent, ignoring submission');
      return;
    }
    
    const input = e.currentTarget.querySelector('input');
    if (input && input.value.trim()) {
      setIsSending(true);
      lastSubmitTime.current = now;
      
      try {
        onSendMessage(input.value.trim());
        // Clear the input after successful submission
        input.value = '';
      } catch (error) {
        console.error('Error sending message:', error);
        setIsSending(false);
      }
      
      // Reset sending state after a delay to prevent rapid re-submission
      setTimeout(() => {
        setIsSending(false);
      }, 2000);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Handle input changes if needed
  };

  return (
    <div 
      className="fixed inset-0 flex flex-col overflow-hidden overflow-x-hidden z-50 bg-sidebar-bg"
      style={{ 
        left: 'var(--sidebar-width)', // Responsive sidebar width
        top: 'var(--header-height)' // Responsive header height
      }}
    >
      <div className="flex flex-1 items-center justify-center min-h-0 @lg/thread:items-end">
        <div className="max-w-2xl w-full mx-auto px-6">
          {/* Main Content */}
          <div className="text-center mb-8">
            <div className="mb-6">
              <h1 className="text-sidebar-text-primary text-[28px] leading-[34px] font-semibold tracking-[0.38px] mb-3 motion-safe:transition-all duration-200 inline-flex min-h-10.5 items-baseline whitespace-pre-wrap opacity-100">
                Ready when you are.
              </h1>
              {isSending && (
                <p className="text-sidebar-text-secondary text-[15px] leading-[18px] tracking-[-0.23px] motion-safe:transition-opacity duration-200">
                  Sending your message...
                </p>
              )}
            </div>
          </div>
          
          {/* Input Section */}
          <div className="mb-6 motion-safe:transition-all duration-200">
            <PlaceholdersAndVanishInput
              placeholders={placeholders}
              onChange={handleChange}
              onSubmit={handleSubmit}
              disabled={isSending}
            />
          </div>
        </div>
      </div>
    </div>
  );
} 