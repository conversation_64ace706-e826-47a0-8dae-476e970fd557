"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[524],{3925:(e,t,r)=>{let a;r.d(t,{LF:()=>function e(t,r){v();var s,i,c,l,f,h,u,d=r||{};if("undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer)return e(new Uint8Array(t),((d=eV(d)).type="array",d));"undefined"!=typeof Uint8Array&&t instanceof Uint8Array&&!d.type&&(d.type="undefined"!=typeof Deno?"buffer":"array");var p=t,m=[0,0,0,0],g=!1;if(d.cellStyles&&(d.cellNF=!0,d.sheetStubs=!0),sO={},d.dateNF&&(sO.dateNF=d.dateNF),d.type||(d.type=_&&o.isBuffer(t)?"buffer":"base64"),"file"==d.type&&(d.type=_?"buffer":"binary",p=function(e){if(void 0!==a)return a.readFileSync(e);if("undefined"!=typeof Deno)return Deno.readFileSync(e);if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(e){if(!e.message||!e.message.match(/onstruct/))throw e}throw Error("Cannot access file "+e)}(t),"undefined"==typeof Uint8Array||_||(d.type="array")),"string"==d.type&&(g=!0,d.type="binary",d.codepage=65001,p=t.match(/[^\x00-\x7F]/)?tT(t):t),"array"==d.type&&"undefined"!=typeof Uint8Array&&t instanceof Uint8Array&&"undefined"!=typeof ArrayBuffer){var b=new Uint8Array(new ArrayBuffer(3));if(b.foo="bar",!b.foo)return(d=eV(d)).type="array",e(D(p),d)}switch((m=iK(p,d))[0]){case 208:if(207===m[1]&&17===m[2]&&224===m[3]&&161===m[4]&&177===m[5]&&26===m[6]&&225===m[7])return c=ey.read(p,d),l=d,ey.find(c,"EncryptedPackage")?function(e,t){var r=t||{},a="Workbook",n=ey.find(e,a);try{if(a="/!DataSpaces/Version",!(n=ey.find(e,a))||!n.content||(s=n.content,(i={}).id=s.read_shift(0,"lpp4"),i.R=nv(s,4),i.U=nv(s,4),i.W=nv(s,4),a="/!DataSpaces/DataSpaceMap",!(n=ey.find(e,a))||!n.content))throw Error("ECMA-376 Encrypted file missing "+a);var s,i,c=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(function(e){for(var t=e.read_shift(4),r=e.l+t-4,a={},n=e.read_shift(4),s=[];n-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=s,e.l!=r)throw Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return a}(e));return t}(n.content);if(1!==c.length||1!==c[0].comps.length||0!==c[0].comps[0].t||"StrongEncryptionDataSpace"!==c[0].name||"EncryptedPackage"!==c[0].comps[0].v)throw Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",!(n=ey.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);var o=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}(n.content);if(1!=o.length||"StrongEncryptionTransform"!=o[0])throw Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",!(n=ey.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);!function(e){var t,r=(t={},e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=nv(e,4),t.U=nv(e,4),t.W=nv(e,4),t);if(r.ename=e.read_shift(0,"8lpp4"),r.blksz=e.read_shift(4),r.cmode=e.read_shift(4),4!=e.read_shift(4))throw Error("Bad !Primary record")}(n.content)}catch(e){}if(a="/EncryptionInfo",!(n=ey.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);var l=function(e){var t,r,a,n,s=nv(e);switch(s.Minor){case 2:return[s.Minor,function(e){if((63&e.read_shift(4))!=36)throw Error("EncryptionInfo mismatch");var t=e.read_shift(4);return{t:"Std",h:nb(e,t),v:nT(e,e.length-e.l)}}(e,s)];case 3:return[s.Minor,function(){throw Error("File is password-protected: ECMA-376 Extensible")}(e,s)];case 4:return[s.Minor,(t=e,r=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"],t.l+=4,a=t.read_shift(t.length-t.l,"utf8"),n={},a.replace(e7,function(e){var t=tt(e);switch(tr(t[0])){case"<?xml":case"<encryption":case"</encryption>":case"</keyEncryptors>":case"</keyEncryptor>":break;case"<keyData":r.forEach(function(e){n[e]=t[e]});break;case"<dataIntegrity":n.encryptedHmacKey=t.encryptedHmacKey,n.encryptedHmacValue=t.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":n.encs=[];break;case"<keyEncryptor":n.uri=t.uri;break;case"<encryptedKey":n.encs.push(t);break;default:throw t[0]}}),n)]}throw Error("ECMA-376 Encrypted file unrecognized Version: "+s.Minor)}(n.content);if(a="/EncryptedPackage",!(n=ey.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);if(4==l[0]&&"undefined"!=typeof decrypt_agile)return decrypt_agile(l[1],n.content,r.password||"",r);if(2==l[0]&&"undefined"!=typeof decrypt_std76)return decrypt_std76(l[1],n.content,r.password||"",r);throw Error("File is password-protected")}(c,l):iu(c,l);break;case 9:if(m[1]<=8)return iu(p,d);break;case 60:return is(p,d);case 73:if(73===m[1]&&42===m[2]&&0===m[3])throw Error("TIFF Image File is not a spreadsheet");if(68===m[1]){var T=p,w=d,E=w||{},S=!!E.WTF;E.WTF=!0;try{var k=ne.to_workbook(T,E);return E.WTF=S,k}catch(e){if(E.WTF=S,!e.message.match(/SYLK bad record ID/)&&S)throw e;return na.to_workbook(T,w)}}break;case 84:if(65===m[1]&&66===m[2]&&76===m[3])return nt.to_workbook(p,d);break;case 80:return 75===m[1]&&m[2]<9&&m[3]<9?(s=p,(i=d||{}).type||(i.type=_&&o.isBuffer(s)?"buffer":"base64"),function(e,t){if(eE(),i$(t=t||{}),eJ(e,"META-INF/manifest.xml")||eJ(e,"objectdata.xml")){var r=e,a=t;a=a||{},eJ(r,"META-INF/manifest.xml")&&function(e,t){for(var r,a,n=tI(e);r=tN.exec(n);)switch(r[3]){case"manifest":break;case"file-entry":if("/"==(a=tt(r[0],!1)).path&&"application/vnd.oasis.opendocument.spreadsheet"!==a.type)throw Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw r}}(eZ(r,"META-INF/manifest.xml"),a);var n=eQ(r,"content.xml");if(!n)throw Error("Missing content.xml in ODS / UOF file");var s=iA(tb(n),a);return eJ(r,"meta.xml")&&(s.Props=at(eZ(r,"meta.xml"))),s}if(eJ(e,"Index/Document.iwa")){if("undefined"==typeof Uint8Array)throw Error("NUMBERS file parsing requires Uint8Array support");if(e.FileIndex)return iG(e);var i,c,o,l,f,h,u,d,p,m,g,v,b,T,w=ey.utils.cfb_new();return e1(e).forEach(function(t){e0(w,t,function e(t,r,a){if(!a)return eK(eq(t,r));if(!r)return null;try{return e(t,r)}catch(e){return null}}(e,t))}),iG(w)}if(!eJ(e,"[Content_Types].xml")){if(eJ(e,"index.xml.gz"))throw Error("Unsupported NUMBERS 08 file");if(eJ(e,"index.xml"))throw Error("Unsupported NUMBERS 09 file");throw Error("Unsupported ZIP file")}var E=e1(e),S=function(e){var t=r0();if(!e||!e.match)return t;var r={};if((e.match(e7)||[]).forEach(function(e){var a=tt(e);switch(a[0].replace(e9,"<")){case"<?xml":break;case"<Types":t.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[a.Extension]=a.ContentType;break;case"<Override":void 0!==t[rQ[a.ContentType]]&&t[rQ[a.ContentType]].push(a.PartName)}}),t.xmlns!==tD.CT)throw Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}(eQ(e,"[Content_Types].xml")),k=!1;if(0===S.workbooks.length&&eZ(e,v="xl/workbook.xml",!0)&&S.workbooks.push(v),0===S.workbooks.length){if(!eZ(e,v="xl/workbook.bin",!0))throw Error("Could not find workbook");S.workbooks.push(v),k=!0}"bin"==S.workbooks[0].slice(-3)&&(k=!0);var A={},y={};if(!t.bookSheets&&!t.bookProps){if(sC=[],S.sst)try{sC=function(e,t,r){if(".bin"===t.slice(-4)){var a,n;return a=[],n=!1,rc(e,function(e,t,s){switch(s){case 159:a.Count=e[0],a.Unique=e[1];break;case 19:a.push(e);break;case 160:return!0;case 35:n=!0;break;case 36:n=!1;break;default:if(t.T,!n||r.WTF)throw Error("Unexpected record 0x"+s.toString(16))}}),a}return function(e,t){var r=[],a="";if(!e)return r;var n=e.match(nh);if(n){a=n[2].replace(nu,"").split(nd);for(var s=0;s!=a.length;++s){var i=nf(a[s].trim(),t);null!=i&&(r[r.length]=i)}r.Count=(n=tt(n[1])).count,r.Unique=n.uniqueCount}return r}(e,r)}(eZ(e,iY(S.sst)),S.sst,t)}catch(e){if(t.WTF)throw e}t.cellStyles&&S.themes.length&&(i=eQ(e,S.themes[0].replace(/^\//,""),!0)||"",S.themes[0],A=nZ(i,t)),S.style&&(y=function(e,t,r,a){if(".bin"===t.slice(-4)){var n={};for(var s in n.NumberFmt=[],j)n.NumberFmt[s]=j[s];n.CellXf=[],n.Fonts=[];var i=[],c=!1;return rc(e,function(e,t,s){switch(s){case 44:n.NumberFmt[e[0]]=e[1],eT(e[1],e[0]);break;case 43:n.Fonts.push(e),null!=e.color.theme&&r&&r.themeElements&&r.themeElements.clrScheme&&(e.color.rgb=n_(r.themeElements.clrScheme[e.color.theme].rgb,e.color.tint||0));break;case 1025:case 45:case 46:case 48:case 507:case 572:case 475:case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 47:617==i[i.length-1]&&n.CellXf.push(e);break;case 35:c=!0;break;case 36:c=!1;break;case 37:i.push(s),c=!0;break;case 38:i.pop(),c=!1;break;default:if(t.T>0)i.push(s);else if(t.T<0)i.pop();else if(!c||a.WTF&&37!=i[i.length-1])throw Error("Unexpected record 0x"+s.toString(16))}}),n}return nB(e,r,a)}(eZ(e,iY(S.style)),S.style,A,t))}S.links.map(function(r){try{var a=r5(eQ(e,r3(iY(r))),r),n=eZ(e,iY(r)),s=0,i=r,c=t;if(".bin"===i.slice(-4)){if(!n)return n;var o=c||{},l=!1;return void rc(n,function(e,t,r){switch(r){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:l=!0;break;case 36:l=!1;break;default:if(t.T);else if(!l||o.WTF)throw Error("Unexpected record 0x"+r.toString(16))}},o)}return}catch(e){}});var _=function(e,t,r){if(".bin"===t.slice(-4)){var a,n,s,i,c,o;return n={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},s=[],i=!1,(a=r)||(a={}),a.biff=12,c=[],(o=[[]]).SheetNames=[],o.XTI=[],id[16]={n:"BrtFRTArchID$",f:s9},rc(e,function(e,t,r){switch(r){case 156:o.SheetNames.push(e.name),n.Sheets.push(e);break;case 153:n.WBProps=e;break;case 39:null!=e.Sheet&&(a.SID=e.Sheet),e.Ref=sw(e.Ptg,null,null,o,a),delete a.SID,delete e.Ptg,c.push(e);break;case 1036:case 361:case 2071:case 158:case 143:case 664:case 353:case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:case 16:break;case 357:case 358:case 355:case 667:o[0].length?o.push([r,e]):o[0]=[r,e],o[o.length-1].XTI=[];break;case 362:0===o.length&&(o[0]=[],o[0].XTI=[]),o[o.length-1].XTI=o[o.length-1].XTI.concat(e),o.XTI=o.XTI.concat(e);break;case 35:case 37:s.push(r),i=!0;break;case 36:case 38:s.pop(),i=!1;break;default:if(t.T);else if(!i||a.WTF&&37!=s[s.length-1]&&35!=s[s.length-1])throw Error("Unexpected record 0x"+r.toString(16))}},a),s3(n),n.Names=c,n.supbooks=o,n}return function(e,t){if(!e)throw Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",s={},i=0;if(e.replace(e7,function(c,o){var l=tt(c);switch(tr(l[0])){case"<?xml":case"</workbook>":case"<fileVersion/>":case"</fileVersion>":case"<fileSharing":case"<fileSharing/>":case"</workbookPr>":case"<workbookProtection":case"<workbookProtection/>":case"<bookViews":case"<bookViews>":case"</bookViews>":case"</workbookView>":case"<sheets":case"<sheets>":case"</sheets>":case"</sheet>":case"<functionGroups":case"<functionGroups/>":case"<functionGroup":case"<externalReferences":case"</externalReferences>":case"<externalReferences>":case"<externalReference":case"<definedNames/>":case"<definedName/>":case"</calcPr>":case"<oleSize":case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":case"<customWorkbookView":case"</customWorkbookView>":case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":case"<pivotCache":case"<smartTagPr":case"<smartTagPr/>":case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":case"<smartTagType":case"<webPublishing":case"<webPublishing/>":case"<fileRecoveryPr":case"<fileRecoveryPr/>":case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":case"<webPublishObject":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":case"<ArchID":case"<revisionPtr":break;case"<workbook":c.match(s8)&&(n="xmlns"+c.match(/<(\w+):/)[1]),r.xmlns=l[n];break;case"<fileVersion":delete l[0],r.AppVersion=l;break;case"<workbookPr":case"<workbookPr/>":sZ.forEach(function(e){if(null!=l[e[0]])switch(e[2]){case"bool":r.WBProps[e[0]]=td(l[e[0]]);break;case"int":r.WBProps[e[0]]=parseInt(l[e[0]],10);break;default:r.WBProps[e[0]]=l[e[0]]}}),l.codeName&&(r.WBProps.CodeName=tb(l.codeName));break;case"<workbookView":case"<workbookView/>":delete l[0],r.WBView.push(l);break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=ts(tb(l.name)),delete l[0],r.Sheets.push(l);break;case"<definedNames>":case"<definedNames":case"<ext":case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</definedNames>":case"</ext>":case"</AlternateContent>":a=!1;break;case"<definedName":(s={}).Name=tb(l.name),l.comment&&(s.Comment=l.comment),l.localSheetId&&(s.Sheet=+l.localSheetId),td(l.hidden||"0")&&(s.Hidden=!0),i=o+c.length;break;case"</definedName>":s.Ref=ts(tb(e.slice(i,o))),r.Names.push(s);break;case"<calcPr":case"<calcPr/>":delete l[0],r.CalcPr=l;break;default:if(!a&&t.WTF)throw Error("unrecognized "+l[0]+" in workbook")}return c}),-1===tF.indexOf(r.xmlns))throw Error("Unknown Namespace: "+r.xmlns);return s3(r),r}(e,r)}(eZ(e,iY(S.workbooks[0])),S.workbooks[0],t),x={},C="";S.coreprops.length&&((C=eZ(e,iY(S.coreprops[0]),!0))&&(x=at(C)),0!==S.extprops.length)&&(C=eZ(e,iY(S.extprops[0]),!0))&&(o=C,l=x,f=t,h={},l||(l={}),o=tb(o),an.forEach(function(e){var t=(o.match(tw(e[0]))||[])[1];switch(e[2]){case"string":t&&(l[e[1]]=ts(t));break;case"bool":l[e[1]]="true"===t;break;case"raw":var r=o.match(RegExp("<"+e[0]+"[^>]*>([\\s\\S]*?)</"+e[0]+">"));r&&r.length>0&&(h[e[1]]=r[1])}}),h.HeadingPairs&&h.TitlesOfParts&&as(h.HeadingPairs,h.TitlesOfParts,l,f));var O={};(!t.bookSheets||t.bookProps)&&0!==S.custprops.length&&(C=eQ(e,iY(S.custprops[0]),!0))&&(O=function(e,t){var r={},a="",n=e.match(ac);if(n)for(var s=0;s!=n.length;++s){var i=n[s],c=tt(i);switch(c[0]){case"<?xml":case"<Properties":break;case"<property":a=ts(c.name);break;case"</property>":a=null;break;default:if(0===i.indexOf("<vt:")){var o=i.split(">"),l=o[0].slice(4),f=o[1];switch(l){case"lpstr":case"bstr":case"lpwstr":case"cy":case"error":r[a]=ts(f);break;case"bool":r[a]=td(f);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[a]=parseInt(f,10);break;case"r4":case"r8":case"decimal":r[a]=parseFloat(f);break;case"filetime":case"date":r[a]=eW(f);break;default:if("/"==l.slice(-1))break;t.WTF&&"undefined"!=typeof console&&console.warn("Unexpected",i,l,o)}}else if("</"===i.slice(0,2));else if(t.WTF)throw Error(i)}}return r}(C,t));var R={};if((t.bookSheets||t.bookProps)&&(_.Sheets?g=_.Sheets.map(function(e){return e.name}):x.Worksheets&&x.SheetNames.length>0&&(g=x.SheetNames),t.bookProps&&(R.Props=x,R.Custprops=O),t.bookSheets&&void 0!==g&&(R.SheetNames=g),t.bookSheets?R.SheetNames:t.bookProps))return R;g={};var I={};t.bookDeps&&S.calcchain&&(I=function(e,t,r){if(".bin"===t.slice(-4)){var a;return a=[],rc(e,function(e,t,r){if(63===r)a.push(e);else if(t.T);else if(1)throw Error("Unexpected record 0x"+r.toString(16))}),a}var n=[];if(!e)return n;var s=1;return(e.match(e7)||[]).forEach(function(e){var t=tt(e);switch(t[0]){case"<?xml":case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete t[0],t.i?s=t.i:t.i=s,n.push(t)}}),n}(eZ(e,iY(S.calcchain)),S.calcchain,t));var N=0,D={},F=_.Sheets;x.Worksheets=F.length,x.SheetNames=[];for(var P=0;P!=F.length;++P)x.SheetNames[P]=F[P].name;var L=k?"bin":"xml",M=S.workbooks[0].lastIndexOf("/"),U=(S.workbooks[0].slice(0,M+1)+"_rels/"+S.workbooks[0].slice(M+1)+".rels").replace(/^\//,"");eJ(e,U)||(U="xl/_rels/workbook."+L+".rels");var B=r5(eQ(e,U,!0),U.replace(/_rels.*/,"s5s"));(S.metadata||[]).length>=1&&(t.xlmeta=function(e,t,r){if(".bin"===t.slice(-4)){var a,n,s,i,c;return a={Types:[],Cell:[],Value:[]},n=r||{},s=[],i=!1,c=2,rc(e,function(e,t,r){switch(r){case 335:a.Types.push({name:e.name});break;case 51:e.forEach(function(e){1==c?a.Cell.push({type:a.Types[e[0]-1].name,index:e[1]}):0==c&&a.Value.push({type:a.Types[e[0]-1].name,index:e[1]})});break;case 337:c=+!!e;break;case 338:c=2;break;case 35:s.push(r),i=!0;break;case 36:s.pop(),i=!1;break;default:if(t.T);else if(!i||n.WTF&&35!=s[s.length-1])throw Error("Unexpected record 0x"+r.toString(16))}}),a}var o,l={Types:[],Cell:[],Value:[]};if(!e)return l;var f=!1,h=2;return e.replace(e7,function(e){var t=tt(e);switch(tr(t[0])){case"<?xml":case"<metadata":case"</metadata>":case"<metadataTypes":case"</metadataTypes>":case"</metadataType>":case"</futureMetadata>":case"<bk>":case"</bk>":case"</rc>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<metadataType":l.Types.push({name:t.name});break;case"<futureMetadata":for(var a=0;a<l.Types.length;++a)l.Types[a].name==t.name&&(o=l.Types[a]);break;case"<rc":1==h?l.Cell.push({type:l.Types[t.t-1].name,index:+t.v}):0==h&&l.Value.push({type:l.Types[t.t-1].name,index:+t.v});break;case"<cellMetadata":h=1;break;case"</cellMetadata>":case"</valueMetadata>":h=2;break;case"<valueMetadata":h=0;break;case"<ext":f=!0;break;case"</ext>":f=!1;break;case"<rvb":if(!o)break;o.offsets||(o.offsets=[]),o.offsets.push(+t.i);break;default:if(!f&&r.WTF)throw Error("unrecognized "+t[0]+" in metadata")}return e}),l}(eZ(e,iY(S.metadata[0])),S.metadata[0],t)),(S.people||[]).length>=1&&(t.people=(u=eZ(e,iY(S.people[0])),d=t,p=[],m=!1,u.replace(e7,function(e){var t=tt(e);switch(tr(t[0])){case"<?xml":case"<personList":case"</personList>":case"</person>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<person":p.push({name:t.displayname,id:t.id});break;case"<ext":m=!0;break;case"</ext>":m=!1;break;default:if(!m&&d.WTF)throw Error("unrecognized "+t[0]+" in threaded comments")}return e}),p)),B&&(B=function(e,t){if(!e)return 0;try{e=t.map(function(t){var r;return t.id||(t.id=t.strRelID),[t.name,e["!id"][t.id].Target,(r=e["!id"][t.id].Type,r4.WS.indexOf(r)>-1?"sheet":r4.CS&&r==r4.CS?"chart":r4.DS&&r==r4.DS?"dialog":r4.MS&&r==r4.MS?"macro":r&&r.length?r:"sheet")]})}catch(e){return null}return e&&0!==e.length?e:null}(B,_.Sheets));var W=+!!eZ(e,"xl/worksheets/sheet.xml",!0);for(N=0;N!=x.Worksheets;++N){var H="sheet";if(B&&B[N]?(eJ(e,b="xl/"+B[N][1].replace(/[\/]?xl\//,""))||(b=B[N][1]),eJ(e,b)||(b=U.replace(/_rels\/.*$/,"")+B[N][1]),H=B[N][2]):b=(b="xl/worksheets/sheet"+(N+1-W)+"."+L).replace(/sheet0\./,"sheet."),T=b.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&null!=t.sheets)switch(typeof t.sheets){case"number":if(N!=t.sheets)continue;break;case"string":if(x.SheetNames[N].toLowerCase()!=t.sheets.toLowerCase())continue;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var V=!1,z=0;z!=t.sheets.length;++z)"number"==typeof t.sheets[z]&&t.sheets[z]==N&&(V=1),"string"==typeof t.sheets[z]&&t.sheets[z].toLowerCase()==x.SheetNames[N].toLowerCase()&&(V=1);if(!V)continue}}!function(e,t,r,a,n,s,i,c,o,l,f,h){try{s[a]=r5(eQ(e,r,!0),t);var u,d,p,m,g=eZ(e,t);switch(c){case"sheet":u=s[a],m=".bin"===t.slice(-4)?function(e,t,r,a,n,s,i){if(!e)return e;var c,o,l,f,h,u,d,p,m,g,v,b,T=t||{};a||(a={"!id":{}});var w=T.dense?[]:{},E={s:{r:2e6,c:2e6},e:{r:0,c:0}},S=[],k=!1,A=!1,y=[];T.biff=12,T["!row"]=0;var _=0,x=!1,C=[],O={},R=T.supbooks||n.supbooks||[[]];if(R.sharedf=O,R.arrayf=C,R.SheetNames=n.SheetNames||n.Sheets.map(function(e){return e.name}),!T.supbooks&&(T.supbooks=R,n.Names))for(var I=0;I<n.Names.length;++I)R[0][I+1]=n.Names[I];var N=[],D=[],F=!1;if(id[16]={n:"BrtShortReal",f:sJ},rc(e,function(e,t,I){if(!A)switch(I){case 148:c=e;break;case 0:o=e,T.sheetRows&&T.sheetRows<=o.r&&(A=!0),m=rm(h=o.r),T["!row"]=o.r,(e.hidden||e.hpt||null!=e.level)&&(e.hpt&&(e.hpx=nP(e.hpt)),D[e.r]=e);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(l={t:e[2]},e[2]){case"n":l.v=e[1];break;case"s":p=sC[e[1]],l.v=p.t,l.r=p.r;break;case"b":l.v=!!e[1];break;case"e":l.v=e[1],!1!==T.cellText&&(l.w=rq[l.v]);break;case"str":l.t="s",l.v=e[1];break;case"is":l.t="s",l.v=e[1].t}if((f=i.CellXf[e[0].iStyleRef])&&sP(l,f.numFmtId,null,T,s,i),u=-1==e[0].c?u+1:e[0].c,T.dense?(w[h]||(w[h]=[]),w[h][u]=l):w[rv(u)+m]=l,T.cellFormula){for(_=0,x=!1;_<C.length;++_){var P=C[_];o.r>=P[0].s.r&&o.r<=P[0].e.r&&u>=P[0].s.c&&u<=P[0].e.c&&(l.F=rE(P[0]),x=!0)}!x&&e.length>3&&(l.f=e[3])}if(E.s.r>o.r&&(E.s.r=o.r),E.s.c>u&&(E.s.c=u),E.e.r<o.r&&(E.e.r=o.r),E.e.c<u&&(E.e.c=u),T.cellDates&&f&&"n"==l.t&&em(j[f.numFmtId])){var L=J(l.v);L&&(l.t="d",l.v=new Date(L.y,L.m-1,L.d,L.H,L.M,L.S,L.u))}v&&("XLDAPR"==v.type&&(l.D=!0),v=void 0),b&&(b=void 0);break;case 1:case 12:if(!T.sheetStubs||k)break;l={t:"z",v:void 0},u=-1==e[0].c?u+1:e[0].c,T.dense?(w[h]||(w[h]=[]),w[h][u]=l):w[rv(u)+m]=l,E.s.r>o.r&&(E.s.r=o.r),E.s.c>u&&(E.s.c=u),E.e.r<o.r&&(E.e.r=o.r),E.e.c<u&&(E.e.c=u),v&&("XLDAPR"==v.type&&(l.D=!0),v=void 0),b&&(b=void 0);break;case 176:y.push(e);break;case 49:v=((T.xlmeta||{}).Cell||[])[e-1];break;case 494:var M=a["!id"][e.relId];for(M?(e.Target=M.Target,e.loc&&(e.Target+="#"+e.loc),e.Rel=M):""==e.relId&&(e.Target="#"+e.loc),h=e.rfx.s.r;h<=e.rfx.e.r;++h)for(u=e.rfx.s.c;u<=e.rfx.e.c;++u)T.dense?(w[h]||(w[h]=[]),w[h][u]||(w[h][u]={t:"z",v:void 0}),w[h][u].l=e):(w[d=rT({c:u,r:h})]||(w[d]={t:"z",v:void 0}),w[d].l=e);break;case 426:if(!T.cellFormula)break;C.push(e),(g=T.dense?w[h][u]:w[rv(u)+m]).f=sw(e[1],E,{r:o.r,c:u},R,T),g.F=rE(e[0]);break;case 427:if(!T.cellFormula)break;O[rT(e[0].s)]=e[1],(g=T.dense?w[h][u]:w[rv(u)+m]).f=sw(e[1],E,{r:o.r,c:u},R,T);break;case 60:if(!T.cellStyles)break;for(;e.e>=e.s;)N[e.e--]={width:e.w/256,hidden:!!(1&e.flags),level:e.level},F||(F=!0,nN(e.w/256)),nD(N[e.e+1]);break;case 161:w["!autofilter"]={ref:rE(e)};break;case 476:w["!margins"]=e;break;case 147:n.Sheets[r]||(n.Sheets[r]={}),e.name&&(n.Sheets[r].CodeName=e.name),(e.above||e.left)&&(w["!outline"]={above:e.above,left:e.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),e.RTL&&(n.Views[0].RTL=!0);break;case 485:case 64:case 1053:case 151:case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:k=!0;break;case 36:k=!1;break;case 37:S.push(I),k=!0;break;case 38:S.pop(),k=!1;break;default:if(t.T);else if(!k||T.WTF)throw Error("Unexpected record 0x"+I.toString(16))}},T),delete T.supbooks,delete T["!row"],!w["!ref"]&&(E.s.r<2e6||c&&(c.e.r>0||c.e.c>0||c.s.r>0||c.s.c>0))&&(w["!ref"]=rE(c||E)),T.sheetRows&&w["!ref"]){var P=rS(w["!ref"]);T.sheetRows<=+P.e.r&&(P.e.r=T.sheetRows-1,P.e.r>E.e.r&&(P.e.r=E.e.r),P.e.r<P.s.r&&(P.s.r=P.e.r),P.e.c>E.e.c&&(P.e.c=E.e.c),P.e.c<P.s.c&&(P.s.c=P.e.c),w["!fullref"]=w["!ref"],w["!ref"]=rE(P))}return y.length>0&&(w["!merges"]=y),N.length>0&&(w["!cols"]=N),D.length>0&&(w["!rows"]=D),w}(g,o,n,u,l,f,h):function(e,t,r,a,n,s,i){if(!e)return e;a||(a={"!id":{}});var c=t.dense?[]:{},o={s:{r:2e6,c:2e6},e:{r:0,c:0}},l="",f="",h=e.match(sM);h?(l=e.slice(0,h.index),f=e.slice(h.index+h[0].length)):l=f=e;var u=l.match(sz);u?s$(u[0],c,n,r):(u=l.match(sG))&&(p=u[0],m=u[1],g=c,v=n,b=r,s$(p.slice(0,p.indexOf(">")),g,v,b));var d=(l.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var p,m,g,v,b,T,w=l.slice(d,d+50).match(sB);w&&(T=rS(w[1])).s.r<=T.e.r&&T.s.c<=T.e.c&&T.s.r>=0&&T.s.c>=0&&(c["!ref"]=rE(T))}var E=l.match(sj);E&&E[1]&&function(e,t){t.Views||(t.Views=[{}]),(e.match(sX)||[]).forEach(function(e,r){var a=tt(e);t.Views[r]||(t.Views[r]={}),+a.zoomScale&&(t.Views[r].zoom=+a.zoomScale),td(a.rightToLeft)&&(t.Views[r].RTL=!0)})}(E[1],n);var S=[];if(t.cellStyles){var k=l.match(sW);k&&function(e,t){for(var r=!1,a=0;a!=t.length;++a){var n=tt(t[a],!0);n.hidden&&(n.hidden=td(n.hidden));var s=parseInt(n.min,10)-1,i=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!r&&n.width&&(r=!0,nN(n.width)),nD(n);s<=i;)e[s++]=eV(n)}}(S,k)}h&&sY(h[1],c,t,o,s,i);var A=f.match(sH);A&&(c["!autofilter"]={ref:(A[0].match(/ref="([^"]*)"/)||[])[1]});var y=[],_=f.match(sL);if(_)for(d=0;d!=_.length;++d)y[d]=rS(_[d].slice(_[d].indexOf('"')+1));var x=f.match(sU);x&&function(e,t,r){for(var a=Array.isArray(e),n=0;n!=t.length;++n){var s=tt(tb(t[n]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+ts(s.location))):(s.Target="#"+ts(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var c=rS(s.ref),o=c.s.r;o<=c.e.r;++o)for(var l=c.s.c;l<=c.e.c;++l){var f=rT({c:l,r:o});a?(e[o]||(e[o]=[]),e[o][l]||(e[o][l]={t:"z",v:void 0}),e[o][l].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}(c,x,a);var C=f.match(sV);if(C&&(c["!margins"]=function(e){var t={};return["left","right","top","bottom","header","footer"].forEach(function(r){e[r]&&(t[r]=parseFloat(e[r]))}),t}(tt(C[0]))),!c["!ref"]&&o.e.c>=o.s.c&&o.e.r>=o.s.r&&(c["!ref"]=rE(o)),t.sheetRows>0&&c["!ref"]){var O=rS(c["!ref"]);t.sheetRows<=+O.e.r&&(O.e.r=t.sheetRows-1,O.e.r>o.e.r&&(O.e.r=o.e.r),O.e.r<O.s.r&&(O.s.r=O.e.r),O.e.c>o.e.c&&(O.e.c=o.e.c),O.e.c<O.s.c&&(O.s.c=O.e.c),c["!fullref"]=c["!ref"],c["!ref"]=rE(O))}return S.length>0&&(c["!cols"]=S),y.length>0&&(c["!merges"]=y),c}(g,o,n,u,l,f,h);break;case"chart":if(!(m=function(e,t,r,a,n,s,i,c){if(".bin"===t.slice(-4)){var o=n;if(!e)return e;o||(o={"!id":{}});var l={"!type":"chart","!drawel":null,"!rel":""},f=[],h=!1;return rc(e,function(e,t,n){switch(n){case 550:l["!rel"]=e;break;case 651:s.Sheets[r]||(s.Sheets[r]={}),e.name&&(s.Sheets[r].CodeName=e.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:h=!0;break;case 36:h=!1;break;case 37:f.push(n);break;case 38:f.pop();break;default:if(t.T>0)f.push(n);else if(t.T<0)f.pop();else if(!h||a.WTF)throw Error("Unexpected record 0x"+n.toString(16))}},a),o["!id"][l["!rel"]]&&(l["!drawel"]=o["!id"][l["!rel"]]),l}var u=n;if(!e)return e;u||(u={"!id":{}});var d,p={"!type":"chart","!drawel":null,"!rel":""},m=e.match(sz);return m&&s$(m[0],p,s,r),(d=e.match(/drawing r:id="(.*?)"/))&&(p["!rel"]=d[1]),u["!id"][p["!rel"]]&&(p["!drawel"]=u["!id"][p["!rel"]]),p}(g,t,n,o,s[a],l,0,0))||!m["!drawel"])break;var v=e3(m["!drawel"].Target,t),b=r3(v),T=function(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return t["!id"][r].Target}(eQ(e,v,!0),r5(eQ(e,b,!0),v)),w=e3(T,v),E=r3(w);m=function(e,t,r,a,n,s){var i=s||{"!type":"chart"};if(!e)return s;var c=0,o=0,l="A",f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(e){var t,r,a,n,s=(r=[],a=e.match(/^<c:numCache>/),(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(e){var t=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);t&&(r[+t[1]]=a?+t[2]:t[2])}),n=ts((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]),(e.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(e){t=e.replace(/<.*?>/g,"")}),[r,n,t]);f.s.r=f.s.c=0,f.e.c=c,l=rv(c),s[0].forEach(function(e,t){i[l+rm(t)]={t:"n",v:e,z:s[1]},o=t}),f.e.r<o&&(f.e.r=o),++c}),c>0&&(i["!ref"]=rE(f)),i}(eQ(e,w,!0),0,0,r5(eQ(e,E,!0),w),0,m);break;case"macro":s[a],t.slice(-4),m={"!type":"macro"};break;case"dialog":s[a],t.slice(-4),m={"!type":"dialog"};break;default:throw Error("Unrecognized sheet type "+c)}i[a]=m;var S=[];s&&s[a]&&ex(s[a]).forEach(function(r){var n,i,c,l,f,h="";if(s[a][r].Type==r4.CMNT){h=e3(s[a][r].Target,t);var u=function(e,t,r){if(".bin"===t.slice(-4)){var a,n,s,i;return a=[],n=[],s={},i=!1,rc(e,function(e,t,c){switch(c){case 632:n.push(e);break;case 635:s=e;break;case 637:s.t=e.t,s.h=e.h,s.r=e.r;break;case 636:if(s.author=n[s.iauthor],delete s.iauthor,r.sheetRows&&s.rfx&&r.sheetRows<=s.rfx.r)break;s.t||(s.t=""),delete s.rfx,a.push(s);break;case 3072:case 37:case 38:break;case 35:i=!0;break;case 36:i=!1;break;default:if(t.T);else if(!i||r.WTF)throw Error("Unexpected record 0x"+c.toString(16))}}),a}if(e.match(/<(?:\w+:)?comments *\/>/))return[];var c=[],o=[],l=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);l&&l[1]&&l[1].split(/<\/\w*:?author>/).forEach(function(e){if(""!==e&&""!==e.trim()){var t=e.match(/<(?:\w+:)?author[^>]*>(.*)/);t&&c.push(t[1])}});var f=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return f&&f[1]&&f[1].split(/<\/\w*:?comment>/).forEach(function(e){if(""!==e&&""!==e.trim()){var t=e.match(/<(?:\w+:)?comment[^>]*>/);if(t){var a=tt(t[0]),n={author:a.authorId&&c[a.authorId]||"sheetjsghost",ref:a.ref,guid:a.guid},s=rb(a.ref);if(!r.sheetRows||!(r.sheetRows<=s.r)){var i=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),l=!!i&&!!i[1]&&nf(i[1])||{r:"",t:"",h:""};n.r=l.r,"<t></t>"==l.r&&(l.t=l.h=""),n.t=(l.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),r.cellHTML&&(n.h=l.h),o.push(n)}}}}),o}(eZ(e,h,!0),h,o);if(!u||!u.length)return;n4(m,u,!1)}s[a][r].Type==r4.TCMNT&&(h=e3(s[a][r].Target,t),S=S.concat((n=eZ(e,h,!0),i=[],c=!1,l={},f=0,n.replace(e7,function(e,t){var r=tt(e);switch(tr(r[0])){case"<?xml":case"<ThreadedComments":case"</ThreadedComments>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<threadedComment":l={author:r.personId,guid:r.id,ref:r.ref,T:1};break;case"</threadedComment>":null!=l.t&&i.push(l);break;case"<text>":case"<text":f=t+e.length;break;case"</text>":l.t=n.slice(f,t).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":case"<mentions>":case"<ext":c=!0;break;case"</mentions>":case"</ext>":c=!1;break;default:if(!c&&o.WTF)throw Error("unrecognized "+r[0]+" in threaded comments")}return e}),i)))}),S&&S.length&&n4(m,S,!0,o.people||[])}catch(e){if(o.WTF)throw e}}(e,b,T,x.SheetNames[N],N,D,g,H,t,_,A,y)}return R={Directory:S,Workbook:_,Props:x,Custprops:O,Deps:I,Sheets:g,SheetNames:x.SheetNames,Strings:sC,Styles:y,Themes:A,SSF:eV(j)},t&&t.bookFiles&&(e.files?(R.keys=E,R.files=e.files):(R.keys=[],R.files={},e.FullPaths.forEach(function(t,r){t=t.replace(/^Root Entry[\/]/,""),R.keys.push(t),R.files[t]=e.FileIndex[r]}))),t&&t.bookVBA&&(S.vba.length>0?R.vbaraw=eZ(e,iY(S.vba[0]),!0):S.defaults&&"application/vnd.ms-office.vbaProject"===S.defaults.bin&&(R.vbaraw=eZ(e,"xl/vbaProject.bin",!0))),R}(e4(s,i),i)):iq(t,p,d,g);case 239:return 60===m[3]?is(p,d):iq(t,p,d,g);case 255:if(254===m[1])return f=p,h=d,u=f,"base64"==h.type&&(u=y(u)),u=n.utils.decode(1200,u.slice(2),"str"),h.type="binary",iJ(u,h);if(0===m[1]&&2===m[2]&&0===m[3])return nn.to_workbook(p,d);break;case 0:if(0===m[1]&&(m[2]>=2&&0===m[3]||0===m[2]&&(8===m[3]||9===m[3])))return nn.to_workbook(p,d);break;case 3:case 131:case 139:case 140:return a9.to_workbook(p,d);case 123:if(92===m[1]&&114===m[2]&&116===m[3])return nA.to_workbook(p,d);break;case 10:case 13:case 32:var A=p,x=d,C="",O=iK(A,x);switch(x.type){case"base64":C=y(A);break;case"binary":C=A;break;case"buffer":C=A.toString("binary");break;case"array":C=eH(A);break;default:throw Error("Unrecognized type "+x.type)}return 239==O[0]&&187==O[1]&&191==O[2]&&(C=tb(C)),x.type="binary",iJ(C,x);case 137:if(80===m[1]&&78===m[2]&&71===m[3])throw Error("PNG Image File is not a spreadsheet")}return a7.indexOf(m[0])>-1&&m[2]<=12&&m[3]<=31?a9.to_workbook(p,d):iq(t,p,d,g)},Wp:()=>ce});var n,s,i,c,o=r(83686).Buffer,l=r(32383),f={};f.version="0.18.5";var h=1200,u=1252,d=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],p={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},m=function(e){-1!=d.indexOf(e)&&(u=p[0]=e)},g=function(e){h=e,m(e)};function v(){g(1200),m(1252)}function b(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function T(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var w=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);if(255==t&&254==r){for(var a=e.slice(2),n=[],s=0;s<a.length>>1;++s)n[s]=String.fromCharCode(a.charCodeAt(2*s)+(a.charCodeAt(2*s+1)<<8));return n.join("")}return 254==t&&255==r?T(e.slice(2)):65279==t?e.slice(1):e},E=function(e){return String.fromCharCode(e)},S=function(e){return String.fromCharCode(e)},k="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function A(e){for(var t="",r=0,a=0,n=0,s=0,i=0,c=0,o=0,l=0;l<e.length;)s=(r=e.charCodeAt(l++))>>2,i=(3&r)<<4|(a=e.charCodeAt(l++))>>4,c=(15&a)<<2|(n=e.charCodeAt(l++))>>6,o=63&n,isNaN(a)?c=o=64:isNaN(n)&&(o=64),t+=k.charAt(s)+k.charAt(i)+k.charAt(c)+k.charAt(o);return t}function y(e){var t="",r=0,a=0,n=0,s=0,i=0,c=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)t+=String.fromCharCode((s=k.indexOf(e.charAt(l++)))<<2|(i=k.indexOf(e.charAt(l++)))>>4),a=(15&i)<<4|(c=k.indexOf(e.charAt(l++)))>>2,64!==c&&(t+=String.fromCharCode(a)),n=(3&c)<<6|(o=k.indexOf(e.charAt(l++))),64!==o&&(t+=String.fromCharCode(n));return t}var _=void 0!==o&&void 0!==l&&void 0!==l.versions&&!!l.versions.node,x=function(){if(void 0!==o){var e=!o.from;if(!e)try{o.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new o(e,t):new o(e)}:o.from.bind(o)}return function(){}}();function C(e){return _?o.alloc?o.alloc(e):new o(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}function O(e){return _?o.allocUnsafe?o.allocUnsafe(e):new o(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}var R=function(e){return _?x(e,"binary"):e.split("").map(function(e){return 255&e.charCodeAt(0)})};function I(e){if("undefined"==typeof ArrayBuffer)return R(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=255&e.charCodeAt(a);return t}function N(e){if(Array.isArray(e))return e.map(function(e){return String.fromCharCode(e)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function D(e){if("undefined"==typeof ArrayBuffer)throw Error("Unsupported");if(e instanceof ArrayBuffer)return D(new Uint8Array(e));for(var t=Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var F=_?function(e){return o.concat(e.map(function(e){return o.isBuffer(e)?e:x(e)}))}:function(e){if("undefined"!=typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else if("string"==typeof e[t])throw"wtf";else a.set(new Uint8Array(e[t]),r);return a}return[].concat.apply([],e.map(function(e){return Array.isArray(e)?e:[].slice.call(e)}))},P=/\u0000/g,L=/[\u0001-\u0006]/g;function M(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function U(e,t){var r=""+e;return r.length>=t?r:ez("0",t-r.length)+r}function B(e,t){var r=""+e;return r.length>=t?r:ez(" ",t-r.length)+r}function W(e,t){var r=""+e;return r.length>=t?r:r+ez(" ",t-r.length)}function H(e,t){var r,a;return e>0x100000000||e<-0x100000000?(r=""+Math.round(e)).length>=t?r:ez("0",t-r.length)+r:(a=""+Math.round(e)).length>=t?a:ez("0",t-a.length)+a}function V(e,t){return t=t||0,e.length>=7+t&&(32|e.charCodeAt(t))==103&&(32|e.charCodeAt(t+1))==101&&(32|e.charCodeAt(t+2))==110&&(32|e.charCodeAt(t+3))==101&&(32|e.charCodeAt(t+4))==114&&(32|e.charCodeAt(t+5))==97&&(32|e.charCodeAt(t+6))==108}var z=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],G=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]],j={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},X={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Y={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function K(e,t,r){for(var a=e<0?-1:1,n=e*a,s=0,i=1,c=0,o=1,l=0,f=0,h=Math.floor(n);l<t&&(c=(h=Math.floor(n))*i+s,f=h*l+o,!(n-h<5e-8));)n=1/(n-h),s=i,i=c,o=l,l=f;if(f>t&&(l>t?(f=o,c=s):(f=l,c=i)),!r)return[0,a*c,f];var u=Math.floor(a*c/f);return[u,a*c-u*f,f]}function J(e,t,r){if(e>2958465||e<0)return null;var a=0|e,n=Math.floor(86400*(e-a)),s=0,i=[],c={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(1e-6>Math.abs(c.u)&&(c.u=0),t&&t.date1904&&(a+=1462),c.u>.9999&&(c.u=0,86400==++n&&(c.T=n=0,++a,++c.D)),60===a)i=r?[1317,10,29]:[1900,2,29],s=3;else if(0===a)i=r?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var o,l,f,h=new Date(1900,0,1);h.setDate(h.getDate()+a-1),i=[h.getFullYear(),h.getMonth()+1,h.getDate()],s=h.getDay(),a<60&&(s=(s+6)%7),r&&(o=h,l=i,l[0]-=581,f=o.getDay(),o<60&&(f=(f+6)%7),s=f)}return c.y=i[0],c.m=i[1],c.d=i[2],c.S=n%60,c.M=(n=Math.floor(n/60))%60,c.H=n=Math.floor(n/60),c.q=s,c}var q=new Date(1899,11,31,0,0,0),Z=q.getTime(),Q=new Date(1900,2,1,0,0,0);function ee(e,t){var r=e.getTime();return t?r-=1262304e5:e>=Q&&(r+=864e5),(r-(Z+(e.getTimezoneOffset()-q.getTimezoneOffset())*6e4))/864e5}function et(e){return -1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function er(e){var t,r,a,n,s,i=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return i>=-4&&i<=-1?s=e.toPrecision(10+i):9>=Math.abs(i)?(t=e<0?12:11,s=(r=et(e.toFixed(12))).length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)):s=10===i?e.toFixed(10).substr(0,12):(a=et(e.toFixed(11))).length>(e<0?12:11)||"0"===a||"-0"===a?e.toPrecision(6):a,et(-1==(n=s.toUpperCase()).indexOf("E")?n:n.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2"))}function ea(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):er(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return eb(14,ee(e,t&&t.date1904),t)}throw Error("unsupported value in General format: "+e)}function en(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var es=/%/g,ei=/# (\?+)( ?)\/( ?)(\d+)/,ec=/^#*0*\.([0#]+)/,eo=/\).*[0#]/,el=/\(###\) ###\\?-####/;function ef(e){for(var t,r="",a=0;a!=e.length;++a)switch(t=e.charCodeAt(a)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function eh(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function eu(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function ed(e,t,r){return(0|r)===r?function e(t,r,a){if(40===t.charCodeAt(0)&&!r.match(eo)){var n,s=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return a>=0?e("n",s,a):"("+e("n",s,-a)+")"}if(44===r.charCodeAt(r.length-1)){for(var i=r,c=i.length-1;44===i.charCodeAt(c-1);)--c;return ed(t,i.substr(0,c),a/Math.pow(10,3*(i.length-c)))}if(-1!==r.indexOf("%"))return l=(o=r).replace(es,""),f=o.length-l.length,ed(t,l,a*Math.pow(10,2*f))+ez("%",f);if(-1!==r.indexOf("E"))return function e(t,r){var a,n=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i=Math.floor(Math.log(r)*Math.LOG10E)%s;if(i<0&&(i+=s),!(a=(r/Math.pow(10,i)).toPrecision(n+1+(s+i)%s)).match(/[Ee]/)){var c=Math.floor(Math.log(r)*Math.LOG10E);-1===a.indexOf(".")?a=a.charAt(0)+"."+a.substr(1)+"E+"+(c-a.length+i):a+="E+"+(c-i),a=a.replace(/\+-/,"-")}a=a.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,a){return t+r+a.substr(0,(s+i)%s)+"."+a.substr(i)+"E"})}else a=r.toExponential(n);return t.match(/E\+00$/)&&a.match(/e[+-]\d$/)&&(a=a.substr(0,a.length-1)+"0"+a.charAt(a.length-1)),t.match(/E\-/)&&a.match(/e\+/)&&(a=a.replace(/e\+/,"e")),a.replace("e","E")}(r,a);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),a);var o,l,f,h,u,d,p,m=Math.abs(a),g=a<0?"-":"";if(r.match(/^00+$/))return g+U(m,r.length);if(r.match(/^[#?]+$/))return h=""+a,0===a&&(h=""),h.length>r.length?h:ef(r.substr(0,r.length-h.length))+h;if(u=r.match(ei))return g+(0===m?"":""+m)+ez(" ",(n=u)[1].length+2+n[4].length);if(r.match(/^#+0+$/))return g+U(m,r.length-r.indexOf("0"));if(u=r.match(ec))return h=(h=(""+a).replace(/^([^\.]+)$/,"$1."+ef(u[1])).replace(/\.$/,"."+ef(u[1]))).replace(/\.(\d*)$/,function(e,t){return"."+t+ez("0",ef(u[1]).length-t.length)}),-1!==r.indexOf("0.")?h:h.replace(/^0\./,".");if(u=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return g+(""+m).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,u[1].length?"0.":".");if(u=r.match(/^#{1,3},##0(\.?)$/))return g+en(""+m);if(u=r.match(/^#,##0\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):en(""+a)+"."+ez("0",u[1].length);if(u=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),a);if(u=r.match(/^([0#]+)(\\?-([0#]+))+$/))return h=M(e(t,r.replace(/[\\-]/g,""),a)),d=0,M(M(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return d<h.length?h.charAt(d++):"0"===e?"0":""}));if(r.match(el))return"("+(h=e(t,"##########",a)).substr(0,3)+") "+h.substr(3,3)+"-"+h.substr(6);var v="";if(u=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return p=K(m,Math.pow(10,d=Math.min(u[4].length,7))-1,!1),h=""+g," "==(v=ed("n",u[1],p[1])).charAt(v.length-1)&&(v=v.substr(0,v.length-1)+"0"),h+=v+u[2]+"/"+u[3],(v=W(p[2],d)).length<u[4].length&&(v=ef(u[4].substr(u[4].length-v.length))+v),h+=v;if(u=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return g+((p=K(m,Math.pow(10,d=Math.min(Math.max(u[1].length,u[4].length),7))-1,!0))[0]||(p[1]?"":"0"))+" "+(p[1]?B(p[1],d)+u[2]+"/"+u[3]+W(p[2],d):ez(" ",2*d+1+u[2].length+u[3].length));if(u=r.match(/^[#0?]+$/))return(h=""+a,r.length<=h.length)?h:ef(r.substr(0,r.length-h.length))+h;if(u=r.match(/^([#0]+)\.([#0]+)$/)){d=(h=""+a.toFixed(Math.min(u[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var b=r.indexOf(".")-d,T=r.length-h.length-b;return ef(r.substr(0,b)+h+r.substr(r.length-T))}if(u=r.match(/^00,000\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):en(""+a).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?U(0,3-e.length):"")+e})+"."+U(0,u[1].length);switch(r){case"###,###":case"##,###":case"#,###":var w=en(""+m);return"0"!==w?g+w:"";default:if(r.match(/\.[0#?]*$/))return e(t,r.slice(0,r.lastIndexOf(".")),a)+ef(r.slice(r.lastIndexOf(".")))}throw Error("unsupported format |"+r+"|")}(e,t,r):function e(t,r,a){if(40===t.charCodeAt(0)&&!r.match(eo)){var n,s,i,c,o,l,f=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return a>=0?e("n",f,a):"("+e("n",f,-a)+")"}if(44===r.charCodeAt(r.length-1)){for(var h=r,u=h.length-1;44===h.charCodeAt(u-1);)--u;return ed(t,h.substr(0,u),a/Math.pow(10,3*(h.length-u)))}if(-1!==r.indexOf("%"))return p=(d=r).replace(es,""),m=d.length-p.length,ed(t,p,a*Math.pow(10,2*m))+ez("%",m);if(-1!==r.indexOf("E"))return function e(t,r){var a,n=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i=Math.floor(Math.log(r)*Math.LOG10E)%s;if(i<0&&(i+=s),-1===(a=(r/Math.pow(10,i)).toPrecision(n+1+(s+i)%s)).indexOf("e")){var c=Math.floor(Math.log(r)*Math.LOG10E);for(-1===a.indexOf(".")?a=a.charAt(0)+"."+a.substr(1)+"E+"+(c-a.length+i):a+="E+"+(c-i);"0."===a.substr(0,2);)a=(a=a.charAt(0)+a.substr(2,s)+"."+a.substr(2+s)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");a=a.replace(/\+-/,"-")}a=a.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,a){return t+r+a.substr(0,(s+i)%s)+"."+a.substr(i)+"E"})}else a=r.toExponential(n);return t.match(/E\+00$/)&&a.match(/e[+-]\d$/)&&(a=a.substr(0,a.length-1)+"0"+a.charAt(a.length-1)),t.match(/E\-/)&&a.match(/e\+/)&&(a=a.replace(/e\+/,"e")),a.replace("e","E")}(r,a);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),a);var d,p,m,g,v,b,T,w=Math.abs(a),E=a<0?"-":"";if(r.match(/^00+$/))return E+H(w,r.length);if(r.match(/^[#?]+$/))return"0"===(g=H(a,0))&&(g=""),g.length>r.length?g:ef(r.substr(0,r.length-g.length))+g;if(v=r.match(ei))return c=Math.floor((i=Math.round(w*(s=parseInt((n=v)[4],10))))/s),o=i-c*s,E+(0===c?"":""+c)+" "+(0===o?ez(" ",n[1].length+1+n[4].length):B(o,n[1].length)+n[2]+"/"+n[3]+U(s,n[4].length));if(r.match(/^#+0+$/))return E+H(w,r.length-r.indexOf("0"));if(v=r.match(ec))return g=eh(a,v[1].length).replace(/^([^\.]+)$/,"$1."+ef(v[1])).replace(/\.$/,"."+ef(v[1])).replace(/\.(\d*)$/,function(e,t){return"."+t+ez("0",ef(v[1]).length-t.length)}),-1!==r.indexOf("0.")?g:g.replace(/^0\./,".");if(v=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return E+eh(w,v[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,v[1].length?"0.":".");if(v=r.match(/^#{1,3},##0(\.?)$/))return E+en(H(w,0));if(v=r.match(/^#,##0\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):en(""+(Math.floor(a)+ +((l=v[1].length)<(""+Math.round((a-Math.floor(a))*Math.pow(10,l))).length)))+"."+U(eu(a,v[1].length),v[1].length);if(v=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),a);if(v=r.match(/^([0#]+)(\\?-([0#]+))+$/))return g=M(e(t,r.replace(/[\\-]/g,""),a)),b=0,M(M(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return b<g.length?g.charAt(b++):"0"===e?"0":""}));if(r.match(el))return"("+(g=e(t,"##########",a)).substr(0,3)+") "+g.substr(3,3)+"-"+g.substr(6);var S="";if(v=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return T=K(w,Math.pow(10,b=Math.min(v[4].length,7))-1,!1),g=""+E," "==(S=ed("n",v[1],T[1])).charAt(S.length-1)&&(S=S.substr(0,S.length-1)+"0"),g+=S+v[2]+"/"+v[3],(S=W(T[2],b)).length<v[4].length&&(S=ef(v[4].substr(v[4].length-S.length))+S),g+=S;if(v=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return E+((T=K(w,Math.pow(10,b=Math.min(Math.max(v[1].length,v[4].length),7))-1,!0))[0]||(T[1]?"":"0"))+" "+(T[1]?B(T[1],b)+v[2]+"/"+v[3]+W(T[2],b):ez(" ",2*b+1+v[2].length+v[3].length));if(v=r.match(/^[#0?]+$/))return(g=H(a,0),r.length<=g.length)?g:ef(r.substr(0,r.length-g.length))+g;if(v=r.match(/^([#0?]+)\.([#0]+)$/)){b=(g=""+a.toFixed(Math.min(v[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var k=r.indexOf(".")-b,A=r.length-g.length-k;return ef(r.substr(0,k)+g+r.substr(r.length-A))}if(v=r.match(/^00,000\.([#0]*0)$/))return b=eu(a,v[1].length),a<0?"-"+e(t,r,-a):en(a<0x7fffffff&&a>-0x80000000?""+(a>=0?0|a:a-1|0):""+Math.floor(a)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?U(0,3-e.length):"")+e})+"."+U(b,v[1].length);switch(r){case"###,##0.00":return e(t,"#,##0.00",a);case"###,###":case"##,###":case"#,###":var y=en(H(w,0));return"0"!==y?E+y:"";case"###,###.00":return e(t,"###,##0.00",a).replace(/^0\./,".");case"#,###.00":return e(t,"#,##0.00",a).replace(/^0\./,".")}throw Error("unsupported format |"+r+"|")}(e,t,r)}var ep=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function em(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":V(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase()||"AM/PM"===e.substr(t,5).toUpperCase()||"上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(a=r;"]"!==e.charAt(t++)&&t<e.length;)a+=e.charAt(t);if(a.match(ep))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(" "==e.charAt(t)||"*"==e.charAt(t))&&++t;break;case"(":case")":case" ":default:++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);}return!1}var eg=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function ev(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function eb(e,t,r){null==r&&(r={});var a="";switch(typeof e){case"string":a="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(a=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:j)[e])&&(a=r.table&&r.table[X[e]]||j[X[e]]),null==a&&(a=Y[e]||"General")}if(V(a,0))return ea(t,r);t instanceof Date&&(t=ee(t,r.date1904));var n=function(e,t){var r=function(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),!0===r)throw Error("Format |"+e+"| unterminated string ");return t}(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var s=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[a,s];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var i=r[0].match(eg),c=r[1].match(eg);return ev(t,i)?[a,r[0]]:ev(t,c)?[a,r[1]]:[a,r[null!=i&&null!=c?2:1]]}return[a,s]}(a,t);if(V(n[1]))return ea(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,a){for(var n,s,i,c=[],o="",l=0,f="",h="t",u="H";l<e.length;)switch(f=e.charAt(l)){case"G":if(!V(e,l))throw Error("unrecognized character "+f+" in "+e);c[c.length]={t:"G",v:"General"},l+=7;break;case'"':for(o="";34!==(i=e.charCodeAt(++l))&&l<e.length;)o+=String.fromCharCode(i);c[c.length]={t:"t",v:o},++l;break;case"\\":var d=e.charAt(++l),p="("===d||")"===d?d:"t";c[c.length]={t:p,v:d},++l;break;case"_":c[c.length]={t:"t",v:" "},l+=2;break;case"@":c[c.length]={t:"T",v:t},++l;break;case"B":case"b":if("1"===e.charAt(l+1)||"2"===e.charAt(l+1)){if(null==n&&null==(n=J(t,r,"2"===e.charAt(l+1))))return"";c[c.length]={t:"X",v:e.substr(l,2)},h=f,l+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||null==n&&null==(n=J(t,r)))return"";for(o=f;++l<e.length&&e.charAt(l).toLowerCase()===f;)o+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),c[c.length]={t:f,v:o},h=f;break;case"A":case"a":case"上":var m={t:f,v:f};if(null==n&&(n=J(t,r)),"A/P"===e.substr(l,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?"P":"A"),m.t="T",u="h",l+=3):"AM/PM"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",l+=5,u="h"):"上午/下午"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"下午":"上午"),m.t="T",l+=5,u="h"):(m.t="t",++l),null==n&&"T"===m.t)return"";c[c.length]=m,h=f;break;case"[":for(o=f;"]"!==e.charAt(l++)&&l<e.length;)o+=e.charAt(l);if("]"!==o.slice(-1))throw'unterminated "[" block: |'+o+"|";if(o.match(ep)){if(null==n&&null==(n=J(t,r)))return"";c[c.length]={t:"Z",v:o.toLowerCase()},h=o.charAt(1)}else o.indexOf("$")>-1&&(o=(o.match(/\$([^-\[\]]*)/)||[])[1]||"$",em(e)||(c[c.length]={t:"t",v:o}));break;case".":if(null!=n){for(o=f;++l<e.length&&"0"===(f=e.charAt(l));)o+=f;c[c.length]={t:"s",v:o};break}case"0":case"#":for(o=f;++l<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(l))>-1;)o+=f;c[c.length]={t:"n",v:o};break;case"?":for(o=f;e.charAt(++l)===f;)o+=f;c[c.length]={t:f,v:o},h=f;break;case"*":++l,(" "==e.charAt(l)||"*"==e.charAt(l))&&++l;break;case"(":case")":c[c.length]={t:1===a?"t":f,v:f},++l;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(o=f;l<e.length&&"0123456789".indexOf(e.charAt(++l))>-1;)o+=e.charAt(l);c[c.length]={t:"D",v:o};break;case" ":c[c.length]={t:f,v:f},++l;break;case"$":c[c.length]={t:"t",v:"$"},++l;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw Error("unrecognized character "+f+" in "+e);c[c.length]={t:"t",v:f},++l}var g,v=0,b=0;for(l=c.length-1,h="t";l>=0;--l)switch(c[l].t){case"h":case"H":c[l].t=u,h="h",v<1&&(v=1);break;case"s":(g=c[l].v.match(/\.0+$/))&&(b=Math.max(b,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=c[l].t;break;case"m":"s"===h&&(c[l].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&c[l].v.match(/[Hh]/)&&(v=1),v<2&&c[l].v.match(/[Mm]/)&&(v=2),v<3&&c[l].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H);break;case 2:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M)}var T,w="";for(l=0;l<c.length;++l)switch(c[l].t){case"t":case"T":case" ":case"D":break;case"X":c[l].v="",c[l].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":c[l].v=function(e,t,r,a){var n,s="",i=0,c=0,o=r.y,l=0;switch(e){case 98:o=r.y+543;case 121:switch(t.length){case 1:case 2:n=o%100,l=2;break;default:n=o%1e4,l=4}break;case 109:switch(t.length){case 1:case 2:n=r.m,l=t.length;break;case 3:return G[r.m-1][1];case 5:return G[r.m-1][0];default:return G[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:n=r.d,l=t.length;break;case 3:return z[r.q][0];default:return z[r.q][1]}break;case 104:switch(t.length){case 1:case 2:n=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:n=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:n=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;if(0===r.u&&("s"==t||"ss"==t))return U(r.S,t.length);if((i=Math.round((c=a>=2?3===a?1e3:100:1===a?10:1)*(r.S+r.u)))>=60*c&&(i=0),"s"===t)return 0===i?"0":""+i/c;if(s=U(i,2+a),"ss"===t)return s.substr(0,2);return"."+s.substr(2,t.length-1);case 90:switch(t){case"[h]":case"[hh]":n=24*r.D+r.H;break;case"[m]":case"[mm]":n=(24*r.D+r.H)*60+r.M;break;case"[s]":case"[ss]":n=((24*r.D+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=3===t.length?1:2;break;case 101:n=o,l=1}return l>0?U(n,l):""}(c[l].t.charCodeAt(0),c[l].v,n,b),c[l].t="t";break;case"n":case"?":for(T=l+1;null!=c[T]&&("?"===(f=c[T].t)||"D"===f||(" "===f||"t"===f)&&null!=c[T+1]&&("?"===c[T+1].t||"t"===c[T+1].t&&"/"===c[T+1].v)||"("===c[l].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===c[T].v||" "===c[T].v&&null!=c[T+1]&&"?"==c[T+1].t));)c[l].v+=c[T].v,c[T]={v:"",t:";"},++T;w+=c[l].v,l=T-1;break;case"G":c[l].t="t",c[l].v=ea(t,r)}var E,S,k="";if(w.length>0){40==w.charCodeAt(0)?(E=t<0&&45===w.charCodeAt(0)?-t:t,S=ed("n",w,E)):(S=ed("n",w,E=t<0&&a>1?-t:t),E<0&&c[0]&&"t"==c[0].t&&(S=S.substr(1),c[0].v="-"+c[0].v)),T=S.length-1;var A=c.length;for(l=0;l<c.length;++l)if(null!=c[l]&&"t"!=c[l].t&&c[l].v.indexOf(".")>-1){A=l;break}var y=c.length;if(A===c.length&&-1===S.indexOf("E")){for(l=c.length-1;l>=0;--l)null!=c[l]&&-1!=="n?".indexOf(c[l].t)&&(T>=c[l].v.length-1?(T-=c[l].v.length,c[l].v=S.substr(T+1,c[l].v.length)):T<0?c[l].v="":(c[l].v=S.substr(0,T+1),T=-1),c[l].t="t",y=l);T>=0&&y<c.length&&(c[y].v=S.substr(0,T+1)+c[y].v)}else if(A!==c.length&&-1===S.indexOf("E")){for(T=S.indexOf(".")-1,l=A;l>=0;--l)if(null!=c[l]&&-1!=="n?".indexOf(c[l].t)){for(s=c[l].v.indexOf(".")>-1&&l===A?c[l].v.indexOf(".")-1:c[l].v.length-1,k=c[l].v.substr(s+1);s>=0;--s)T>=0&&("0"===c[l].v.charAt(s)||"#"===c[l].v.charAt(s))&&(k=S.charAt(T--)+k);c[l].v=k,c[l].t="t",y=l}for(T>=0&&y<c.length&&(c[y].v=S.substr(0,T+1)+c[y].v),T=S.indexOf(".")+1,l=A;l<c.length;++l)if(null!=c[l]&&(-1!=="n?(".indexOf(c[l].t)||l===A)){for(s=c[l].v.indexOf(".")>-1&&l===A?c[l].v.indexOf(".")+1:0,k=c[l].v.substr(0,s);s<c[l].v.length;++s)T<S.length&&(k+=S.charAt(T++));c[l].v=k,c[l].t="t",y=l}}}for(l=0;l<c.length;++l)null!=c[l]&&"n?".indexOf(c[l].t)>-1&&(E=a>1&&t<0&&l>0&&"-"===c[l-1].v?-t:t,c[l].v=ed(c[l].t,c[l].v,E),c[l].t="t");var _="";for(l=0;l!==c.length;++l)null!=c[l]&&(_+=c[l].v);return _}(n[1],t,r,n[0])}function eT(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r){if(void 0==j[r]){t<0&&(t=r);continue}if(j[r]==e){t=r;break}}t<0&&(t=391)}return j[t]=e,t}function ew(e){for(var t=0;392!=t;++t)void 0!==e[t]&&eT(e[t],t)}function eE(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',j=e}var eS={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},ek=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g,eA=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1,t[r]=e;return"undefined"!=typeof Int32Array?new Int32Array(t):t}(),r=function(e){var t=0,r=0,a=0,n="undefined"!=typeof Int32Array?new Int32Array(4096):Array(4096);for(a=0;256!=a;++a)n[a]=e[a];for(a=0;256!=a;++a)for(r=e[a],t=256+a;t<4096;t+=256)r=n[t]=r>>>8^e[255&r];var s=[];for(a=1;16!=a;++a)s[a-1]="undefined"!=typeof Int32Array?n.subarray(256*a,256*a+256):n.slice(256*a,256*a+256);return s}(t),a=r[0],n=r[1],s=r[2],i=r[3],c=r[4],o=r[5],l=r[6],f=r[7],h=r[8],u=r[9],d=r[10],p=r[11],m=r[12],g=r[13],v=r[14];return e.table=t,e.bstr=function(e,r){for(var a=-1^r,n=0,s=e.length;n<s;)a=a>>>8^t[(a^e.charCodeAt(n++))&255];return~a},e.buf=function(e,r){for(var b=-1^r,T=e.length-15,w=0;w<T;)b=v[e[w++]^255&b]^g[e[w++]^b>>8&255]^m[e[w++]^b>>16&255]^p[e[w++]^b>>>24]^d[e[w++]]^u[e[w++]]^h[e[w++]]^f[e[w++]]^l[e[w++]]^o[e[w++]]^c[e[w++]]^i[e[w++]]^s[e[w++]]^n[e[w++]]^a[e[w++]]^t[e[w++]];for(T+=15;w<T;)b=b>>>8^t[(b^e[w++])&255];return~b},e.str=function(e,r){for(var a=-1^r,n=0,s=e.length,i=0,c=0;n<s;)(i=e.charCodeAt(n++))<128?a=a>>>8^t[(a^i)&255]:i<2048?a=(a=a>>>8^t[(a^(192|i>>6&31))&255])>>>8^t[(a^(128|63&i))&255]:i>=55296&&i<57344?(i=(1023&i)+64,c=1023&e.charCodeAt(n++),a=(a=(a=(a=a>>>8^t[(a^(240|i>>8&7))&255])>>>8^t[(a^(128|i>>2&63))&255])>>>8^t[(a^(128|c>>6&15|(3&i)<<4))&255])>>>8^t[(a^(128|63&c))&255]):a=(a=(a=a>>>8^t[(a^(224|i>>12&15))&255])>>>8^t[(a^(128|i>>6&63))&255])>>>8^t[(a^(128|63&i))&255];return~a},e}(),ey=function(){var e,t,r={};function a(e){if("/"==e.charAt(e.length-1))return -1===e.slice(0,-1).indexOf("/")?e:a(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(0,t+1)}function n(e){if("/"==e.charAt(e.length-1))return n(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(t+1)}function s(e){rn(e,0);for(var t={},r=0;e.l<=e.length-4;){var a=e.read_shift(2),n=e.read_shift(2),s=e.l+n,i={};21589===a&&(1&(r=e.read_shift(1))&&(i.mtime=e.read_shift(4)),n>5&&(2&r&&(i.atime=e.read_shift(4)),4&r&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime))),e.l=s,t[a]=i}return t}function i(){return e||(e={})}function c(e,t){if(80==e[0]&&75==e[1])return eo(e,t);if((32|e[0])==109&&(32|e[1])==105)return function(e,t){if("mime-version:"!=T(e.slice(0,13)).toLowerCase())throw Error("Unsupported MAD header");var r=t&&t.root||"",a=(_&&o.isBuffer(e)?e.toString("binary"):T(e)).split("\r\n"),n=0,s="";for(n=0;n<a.length;++n)if((s=a[n],/^Content-Location:/i.test(s))&&(s=s.slice(s.indexOf("file")),r||(r=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),s.slice(0,r.length)!=r););var i=(a[1]||"").match(/boundary="(.*?)"/);if(!i)throw Error("MAD cannot find boundary");var c="--"+(i[1]||""),l={FileIndex:[],FullPaths:[]};f(l);var h,u=0;for(n=0;n<a.length;++n){var d=a[n];(d===c||d===c+"--")&&(u++&&function(e,t,r){for(var a,n="",s="",i="",c=0;c<10;++c){var o=t[c];if(!o||o.match(/^\s*$/))break;var l=o.match(/^(.*?):\s*([^\s].*)$/);if(l)switch(l[1].toLowerCase()){case"content-location":n=l[2].trim();break;case"content-type":i=l[2].trim();break;case"content-transfer-encoding":s=l[2].trim()}}switch(++c,s.toLowerCase()){case"base64":a=R(y(t.slice(c).join("")));break;case"quoted-printable":a=function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r];r<=e.length&&"="==a.charAt(a.length-1);)a=a.slice(0,a.length-1)+e[++r];t.push(a)}for(var n=0;n<t.length;++n)t[n]=t[n].replace(/[=][0-9A-Fa-f]{2}/g,function(e){return String.fromCharCode(parseInt(e.slice(1),16))});return R(t.join("\r\n"))}(t.slice(c));break;default:throw Error("Unsupported Content-Transfer-Encoding "+s)}var f=ef(e,n.slice(r.length),a,{unsafe:!0});i&&(f.ctype=i)}(l,a.slice(h,n),r),h=n)}return l}(e,t);if(e.length<512)throw Error("CFB file size "+e.length+" < 512");var r=3,a=512,n=0,s=0,i=0,c=0,h=0,u=[],g=e.slice(0,512);rn(g,0);var v=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(m,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(g);switch(r=v[0]){case 3:a=512;break;case 4:a=4096;break;case 0:if(0==v[1])return eo(e,t);default:throw Error("Major Version: Expected 3 or 4 saw "+r)}512!==a&&rn(g=e.slice(0,a),28);var b=e.slice(0,a),w=g,E=r,S=9;switch(w.l+=2,S=w.read_shift(2)){case 9:if(3!=E)throw Error("Sector Shift: Expected 9 saw "+S);break;case 12:if(4!=E)throw Error("Sector Shift: Expected 12 saw "+S);break;default:throw Error("Sector Shift: Expected 9 or 12 saw "+S)}w.chk("0600","Mini Sector Shift: "),w.chk("000000000000","Reserved: ");var k=g.read_shift(4,"i");if(3===r&&0!==k)throw Error("# Directory Sectors: Expected 0 saw "+k);g.l+=4,i=g.read_shift(4,"i"),g.l+=4,g.chk("00100000","Mini Stream Cutoff Size: "),c=g.read_shift(4,"i"),n=g.read_shift(4,"i"),h=g.read_shift(4,"i"),s=g.read_shift(4,"i");for(var A=-1,x=0;x<109&&!((A=g.read_shift(4,"i"))<0);++x)u[x]=A;var C=function(e,t){for(var r=Math.ceil(e.length/t)-1,a=[],n=1;n<r;++n)a[n-1]=e.slice(n*t,(n+1)*t);return a[r-1]=e.slice(r*t),a}(e,a);!function e(t,r,a,n,s){var i=p;if(t===p){if(0!==r)throw Error("DIFAT chain shorter than expected")}else if(-1!==t){var c=a[t],o=(n>>>2)-1;if(!c)return;for(var l=0;l<o&&(i=t8(c,4*l))!==p;++l)s.push(i);e(t8(c,n-4),r-1,a,n,s)}}(h,s,C,a,u);var O=function(e,t,r,a){var n=e.length,s=[],i=[],c=[],o=[],l=a-1,f=0,h=0,u=0,d=0;for(f=0;f<n;++f)if(c=[],(u=f+t)>=n&&(u-=n),!i[u]){o=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,c[c.length]=h,o.push(e[h]);var m=r[Math.floor(4*h/a)];if(a<4+(d=4*h&l))throw Error("FAT boundary crossed: "+h+" 4 "+a);if(!e[m]||p[h=t8(e[m],d)])break}s[u]={nodes:c,data:tM([o])}}return s}(C,i,u,a);O[i].name="!Directory",n>0&&c!==p&&(O[c].name="!MiniFAT"),O[u[0]].name="!FAT",O.fat_addrs=u,O.ssz=a;var I=[],N=[],D=[];(function(e,t,r,a,n,s,i,c){for(var o,f=0,h=2*!!a.length,u=t[e].data,m=0,g=0;m<u.length;m+=128){var v=u.slice(m,m+128);rn(v,64),g=v.read_shift(2),o=tB(v,0,g-h),a.push(o);var b={name:o,type:v.read_shift(1),color:v.read_shift(1),L:v.read_shift(4,"i"),R:v.read_shift(4,"i"),C:v.read_shift(4,"i"),clsid:v.read_shift(16),state:v.read_shift(4,"i"),start:0,size:0};0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(b.ct=l(v,v.l-8)),0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(b.mt=l(v,v.l-8)),b.start=v.read_shift(4,"i"),b.size=v.read_shift(4,"i"),b.size<0&&b.start<0&&(b.size=b.type=0,b.start=p,b.name=""),5===b.type?(f=b.start,n>0&&f!==p&&(t[f].name="!StreamData")):b.size>=4096?(b.storage="fat",void 0===t[b.start]&&(t[b.start]=function(e,t,r,a,n){var s=[],i=[];n||(n=[]);var c=a-1,o=0,l=0;for(o=t;o>=0;){n[o]=!0,s[s.length]=o,i.push(e[o]);var f=r[Math.floor(4*o/a)];if(a<4+(l=4*o&c))throw Error("FAT boundary crossed: "+o+" 4 "+a);if(!e[f])break;o=t8(e[f],l)}return{nodes:s,data:tM([i])}}(r,b.start,t.fat_addrs,t.ssz)),t[b.start].name=b.name,b.content=t[b.start].data.slice(0,b.size)):(b.storage="minifat",b.size<0?b.size=0:f!==p&&b.start!==p&&t[f]&&(b.content=function(e,t,r){for(var a=e.start,n=e.size,s=[],i=a;r&&n>0&&i>=0;)s.push(t.slice(i*d,i*d+d)),n-=d,i=t8(r,4*i);return 0===s.length?ri(0):F(s).slice(0,e.size)}(b,t[f].data,(t[c]||{}).data))),b.content&&rn(b.content,0),s[o]=b,i.push(b)}})(i,O,C,I,n,{},N,c),function(e,t,r){for(var a=0,n=0,s=0,i=0,c=0,o=r.length,l=[],f=[];a<o;++a)l[a]=f[a]=a,t[a]=r[a];for(;c<f.length;++c)n=e[a=f[c]].L,s=e[a].R,i=e[a].C,l[a]===a&&(-1!==n&&l[n]!==n&&(l[a]=l[n]),-1!==s&&l[s]!==s&&(l[a]=l[s])),-1!==i&&(l[i]=a),-1!==n&&a!=l[a]&&(l[n]=l[a],f.lastIndexOf(n)<c&&f.push(n)),-1!==s&&a!=l[a]&&(l[s]=l[a],f.lastIndexOf(s)<c&&f.push(s));for(a=1;a<o;++a)l[a]===a&&(-1!==s&&l[s]!==s?l[a]=l[s]:-1!==n&&l[n]!==n&&(l[a]=l[n]));for(a=1;a<o;++a)if(0!==e[a].type){if((c=a)!=l[c])do c=l[c],t[a]=t[c]+"/"+t[a];while(0!==c&&-1!==l[c]&&c!=l[c]);l[a]=-1}for(t[0]+="/",a=1;a<o;++a)2!==e[a].type&&(t[a]+="/")}(N,D,I),I.shift();var P={FileIndex:N,FullPaths:D};return t&&t.raw&&(P.raw={header:b,sectors:C}),P}function l(e,t){return new Date((t6(e,t+4)/1e7*0x100000000+t6(e,t)/1e7-0x2b6109100)*1e3)}function f(e,t){var r=t||{},a=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=a+"/",e.FileIndex[0]={name:a,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="\x01Sh33tJ5";if(!ey.find(e,"/"+t)){var r=ri(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),h(e)}}(e)}function h(e,t){f(e);for(var r=!1,s=!1,i=e.FullPaths.length-1;i>=0;--i){var c=e.FileIndex[i];switch(c.type){case 0:s?r=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(c.R*c.L*c.C)&&(r=!0),c.R>-1&&c.L>-1&&c.R==c.L&&(r=!0);break;default:r=!0}}if(r||t){var o=new Date(1987,1,19),l=0,h=Object.create?Object.create(null):{},u=[];for(i=0;i<e.FullPaths.length;++i)h[e.FullPaths[i]]=!0,0!==e.FileIndex[i].type&&u.push([e.FullPaths[i],e.FileIndex[i]]);for(i=0;i<u.length;++i){var d=a(u[i][0]);(s=h[d])||(u.push([d,{name:n(d).replace("/",""),type:1,clsid:v,ct:o,mt:o,content:null}]),h[d]=!0)}for(u.sort(function(e,t){return function(e,t){for(var r=e.split("/"),a=t.split("/"),n=0,s=0,i=Math.min(r.length,a.length);n<i;++n){if(s=r[n].length-a[n].length)return s;if(r[n]!=a[n])return r[n]<a[n]?-1:1}return r.length-a.length}(e[0],t[0])}),e.FullPaths=[],e.FileIndex=[],i=0;i<u.length;++i)e.FullPaths[i]=u[i][0],e.FileIndex[i]=u[i][1];for(i=0;i<u.length;++i){var p=e.FileIndex[i],m=e.FullPaths[i];if(p.name=n(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||v,0===i)p.C=u.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(l=i+1;l<u.length&&a(e.FullPaths[l])!=m;++l);for(p.C=l>=u.length?-1:l,l=i+1;l<u.length&&a(e.FullPaths[l])!=a(m);++l);p.R=l>=u.length?-1:l,p.type=1}else a(e.FullPaths[i+1]||"")==a(m)&&(p.R=i+1),p.type=2}}}function u(e,r){var a=r||{};if("mad"==a.fileType)return function(e,t){for(var r=t||{},a=r.boundary||"SheetJS",n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(a="------="+a).slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,c=e.FileIndex[0],l=1;l<e.FullPaths.length;++l)if(i=e.FullPaths[l].slice(s.length),(c=e.FileIndex[l]).size&&c.content&&"\x01Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"});for(var f=c.content,h=_&&o.isBuffer(f)?f.toString("binary"):T(f),u=0,d=Math.min(1024,h.length),p=0,m=0;m<=d;++m)(p=h.charCodeAt(m))>=32&&p<128&&++u;var g=u>=4*d/5;n.push(a),n.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+i),n.push("Content-Transfer-Encoding: "+(g?"quoted-printable":"base64")),n.push("Content-Type: "+function(e,t){if(e.ctype)return e.ctype;var r=e.name||"",a=r.match(/\.([^\.]+)$/);return a&&el[a[1]]||t&&(a=(r=t).match(/[\.\\]([^\.\\])+$/))&&el[a[1]]?el[a[1]]:"application/octet-stream"}(c,i)),n.push(""),n.push(g?function(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)});"\n"==(t=t.replace(/ $/mg,"=20").replace(/\t$/mg,"=09")).charAt(0)&&(t="=0D"+t.slice(1)),t=t.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,"\n=0A").replace(/([^\r\n])\n/mg,"$1=0A");for(var r=[],a=t.split("\r\n"),n=0;n<a.length;++n){var s=a[n];if(0==s.length){r.push("");continue}for(var i=0;i<s.length;){var c=76,o=s.slice(i,i+c);"="==o.charAt(c-1)?c--:"="==o.charAt(c-2)?c-=2:"="==o.charAt(c-3)&&(c-=3),o=s.slice(i,i+c),(i+=c)<s.length&&(o+="="),r.push(o)}}return r.join("\r\n")}(h):function(e){for(var t=A(e),r=[],a=0;a<t.length;a+=76)r.push(t.slice(a,a+76));return r.join("\r\n")+"\r\n"}(h))}return n.push(a+"--\r\n"),n.join("\r\n")}(e,a);if(h(e),"zip"===a.fileType)return function(e,r){var a=[],n=[],s=ri(1),i=8*!!(r||{}).compression,c=0,o=0,l=0,f=0,h=e.FullPaths[0],u=h,d=e.FileIndex[0],p=[],m=0;for(c=1;c<e.FullPaths.length;++c)if(u=e.FullPaths[c].slice(h.length),(d=e.FileIndex[c]).size&&d.content&&"\x01Sh33tJ5"!=u){var g,v=l,b=ri(u.length);for(o=0;o<u.length;++o)b.write_shift(1,127&u.charCodeAt(o));b=b.slice(0,b.l),p[f]=eA.buf(d.content,0);var T=d.content;8==i&&(g=T,T=t?t.deflateRawSync(g):ee(g)),(s=ri(30)).write_shift(4,0x4034b50),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),d.mt?function(e,t){"string"==typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var a=t.getFullYear()-1980;a=(a=a<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,a)}(s,d.mt):s.write_shift(4,0),s.write_shift(-4,(0,p[f])),s.write_shift(4,(0,T.length)),s.write_shift(4,(0,d.content.length)),s.write_shift(2,b.length),s.write_shift(2,0),l+=s.length,a.push(s),l+=b.length,a.push(b),l+=T.length,a.push(T),(s=ri(46)).write_shift(4,0x2014b50),s.write_shift(2,0),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),s.write_shift(4,0),s.write_shift(-4,p[f]),s.write_shift(4,T.length),s.write_shift(4,d.content.length),s.write_shift(2,b.length),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s.write_shift(4,v),m+=s.l,n.push(s),m+=b.length,n.push(b),++f}return(s=ri(22)).write_shift(4,0x6054b50),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,f),s.write_shift(2,f),s.write_shift(4,m),s.write_shift(4,l),s.write_shift(2,0),F([F(a),F(n),s])}(e,a);var n=function(e){for(var t=0,r=0,a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(n.content){var s=n.content.length;s>0&&(s<4096?t+=s+63>>6:r+=s+511>>9)}}for(var i=e.FullPaths.length+3>>2,c=t+7>>3,o=t+127>>7,l=c+r+i+o,f=l+127>>7,h=f<=109?0:Math.ceil((f-109)/127);l+f+h+127>>7>f;)h=++f<=109?0:Math.ceil((f-109)/127);var u=[1,h,f,o,i,r,t,0];return e.FileIndex[0].size=t<<6,u[7]=(e.FileIndex[0].start=u[0]+u[1]+u[2]+u[3]+u[4]+u[5])+(u[6]+7>>3),u}(e),s=ri(n[7]<<9),i=0,c=0;for(i=0;i<8;++i)s.write_shift(1,g[i]);for(i=0;i<8;++i)s.write_shift(2,0);for(s.write_shift(2,62),s.write_shift(2,3),s.write_shift(2,65534),s.write_shift(2,9),s.write_shift(2,6),i=0;i<3;++i)s.write_shift(2,0);for(s.write_shift(4,0),s.write_shift(4,n[2]),s.write_shift(4,n[0]+n[1]+n[2]+n[3]-1),s.write_shift(4,0),s.write_shift(4,4096),s.write_shift(4,n[3]?n[0]+n[1]+n[2]-1:p),s.write_shift(4,n[3]),s.write_shift(-4,n[1]?n[0]-1:p),s.write_shift(4,n[1]),i=0;i<109;++i)s.write_shift(-4,i<n[2]?n[1]+i:-1);if(n[1])for(c=0;c<n[1];++c){for(;i<236+127*c;++i)s.write_shift(-4,i<n[2]?n[1]+i:-1);s.write_shift(-4,c===n[1]-1?p:c+1)}var l=function(e){for(c+=e;i<c-1;++i)s.write_shift(-4,i+1);e&&(++i,s.write_shift(-4,p))};for(c=(i=0)+n[1];i<c;++i)s.write_shift(-4,b.DIFSECT);for(c+=n[2];i<c;++i)s.write_shift(-4,b.FATSECT);l(n[3]),l(n[4]);for(var f=0,u=0,d=e.FileIndex[0];f<e.FileIndex.length;++f)(d=e.FileIndex[f]).content&&((u=d.content.length)<4096||(d.start=c,l(u+511>>9)));for(l(n[6]+7>>3);511&s.l;)s.write_shift(-4,b.ENDOFCHAIN);for(f=0,c=i=0;f<e.FileIndex.length;++f)(d=e.FileIndex[f]).content&&(u=d.content.length)&&!(u>=4096)&&(d.start=c,l(u+63>>6));for(;511&s.l;)s.write_shift(-4,b.ENDOFCHAIN);for(i=0;i<n[4]<<2;++i){var m=e.FullPaths[i];if(!m||0===m.length){for(f=0;f<17;++f)s.write_shift(4,0);for(f=0;f<3;++f)s.write_shift(4,-1);for(f=0;f<12;++f)s.write_shift(4,0);continue}d=e.FileIndex[i],0===i&&(d.start=d.size?d.start-1:p);var v=0===i&&a.root||d.name;if(u=2*(v.length+1),s.write_shift(64,v,"utf16le"),s.write_shift(2,u),s.write_shift(1,d.type),s.write_shift(1,d.color),s.write_shift(-4,d.L),s.write_shift(-4,d.R),s.write_shift(-4,d.C),d.clsid)s.write_shift(16,d.clsid,"hex");else for(f=0;f<4;++f)s.write_shift(4,0);s.write_shift(4,d.state||0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,d.start),s.write_shift(4,d.size),s.write_shift(4,0)}for(i=1;i<e.FileIndex.length;++i)if((d=e.FileIndex[i]).size>=4096)if(s.l=d.start+1<<9,_&&o.isBuffer(d.content))d.content.copy(s,s.l,0,d.size),s.l+=d.size+511&-512;else{for(f=0;f<d.size;++f)s.write_shift(1,d.content[f]);for(;511&f;++f)s.write_shift(1,0)}for(i=1;i<e.FileIndex.length;++i)if((d=e.FileIndex[i]).size>0&&d.size<4096)if(_&&o.isBuffer(d.content))d.content.copy(s,s.l,0,d.size),s.l+=d.size+63&-64;else{for(f=0;f<d.size;++f)s.write_shift(1,d.content[f]);for(;63&f;++f)s.write_shift(1,0)}if(_)s.l=s.length;else for(;s.l<s.length;)s.write_shift(1,0);return s}r.version="1.2.1";var d=64,p=-2,m="d0cf11e0a1b11ae1",g=[208,207,17,224,161,177,26,225],v="00000000000000000000000000000000",b={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:-2,FREESECT:-1,HEADER_SIGNATURE:m,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:v,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function T(e){for(var t=Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}for(var w=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],E=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],S=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],k="undefined"!=typeof Uint8Array,I=k?new Uint8Array(256):[],N=0;N<256;++N)I[N]=function(e){var t=(e<<1|e<<11)&139536|(e<<5|e<<15)&558144;return(t>>16|t>>8|t)&255}(N);function D(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=5?0:e[a+1]<<8))>>>r&7}function M(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=3?0:e[a+1]<<8))>>>r&31}function U(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=1?0:e[a+1]<<8))>>>r&127}function B(e,t,r){var a=7&t,n=t>>>3,s=(1<<r)-1,i=e[n]>>>a;return r<8-a||(i|=e[n+1]<<8-a,r<16-a||(i|=e[n+2]<<16-a,r<24-a))?i&s:(i|=e[n+3]<<24-a)&s}function W(e,t,r){var a=7&t,n=t>>>3;return a<=5?e[n]|=(7&r)<<a:(e[n]|=r<<a&255,e[n+1]=(7&r)>>8-a),t+3}function H(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=r,t+8}function V(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=255&r,e[a+2]=r>>>8,t+16}function z(e,t){var r=e.length,a=2*r>t?2*r:t+5,n=0;if(r>=t)return e;if(_){var s=O(a);if(e.copy)e.copy(s);else for(;n<e.length;++n)s[n]=e[n];return s}if(k){var i=new Uint8Array(a);if(i.set)i.set(e);else for(;n<r;++n)i[n]=e[n];return i}return e.length=a,e}function G(e){for(var t=Array(e),r=0;r<e;++r)t[r]=0;return t}function j(e,t,r){var a=1,n=0,s=0,i=0,c=0,o=e.length,l=k?new Uint16Array(32):G(32);for(s=0;s<32;++s)l[s]=0;for(s=o;s<r;++s)e[s]=0;o=e.length;var f=k?new Uint16Array(o):G(o);for(s=0;s<o;++s)l[n=e[s]]++,a<n&&(a=n),f[s]=0;for(s=1,l[0]=0;s<=a;++s)l[s+16]=c=c+l[s-1]<<1;for(s=0;s<o;++s)0!=(c=e[s])&&(f[s]=l[c+16]++);var h=0;for(s=0;s<o;++s)if(0!=(h=e[s]))for(c=function(e,t){var r=I[255&e];return t<=8?r>>>8-t:(r=r<<8|I[e>>8&255],t<=16)?r>>>16-t:(r=r<<8|I[e>>16&255])>>>24-t}(f[s],a)>>a-h,i=(1<<a+4-h)-1;i>=0;--i)t[c|i<<h]=15&h|s<<4;return a}var X=k?new Uint16Array(512):G(512),Y=k?new Uint16Array(32):G(32);if(!k){for(var K=0;K<512;++K)X[K]=0;for(K=0;K<32;++K)Y[K]=0}for(var J=[],q=0;q<32;q++)J.push(5);j(J,Y,32);var Z=[];for(q=0;q<=143;q++)Z.push(8);for(;q<=255;q++)Z.push(9);for(;q<=279;q++)Z.push(7);for(;q<=287;q++)Z.push(8);j(Z,X,288);var Q=function(){for(var e=k?new Uint8Array(32768):[],t=0,r=0;t<S.length-1;++t)for(;r<S[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var a=k?new Uint8Array(259):[];for(t=0,r=0;t<E.length-1;++t)for(;r<E[t+1];++r)a[r]=t;return function(t,r){if(t.length<8){for(var n=0;n<t.length;){var s=Math.min(65535,t.length-n),i=n+s==t.length;for(r.write_shift(1,+i),r.write_shift(2,s),r.write_shift(2,65535&~s);s-- >0;)r[r.l++]=t[n++]}return r.l}return function(t,r){for(var n=0,s=0,i=k?new Uint16Array(32768):[];s<t.length;){var c=Math.min(65535,t.length-s);if(c<10){for(7&(n=W(r,n,+(s+c==t.length)))&&(n+=8-(7&n)),r.l=n/8|0,r.write_shift(2,c),r.write_shift(2,65535&~c);c-- >0;)r[r.l++]=t[s++];n=8*r.l;continue}n=W(r,n,+(s+c==t.length)+2);for(var o=0;c-- >0;){var l,f,h=t[s],u=-1,d=0;if((u=i[o=(o<<5^h)&32767])&&((u|=-32768&s)>s&&(u-=32768),u<s))for(;t[u+d]==t[s+d]&&d<250;)++d;if(d>2){(h=a[d])<=22?n=H(r,n,I[h+1]>>1)-1:(H(r,n,3),H(r,n+=5,I[h-23]>>5),n+=3);var p=h<8?0:h-4>>2;p>0&&(V(r,n,d-E[h]),n+=p),n=H(r,n,I[h=e[s-u]]>>3)-3;var m=h<4?0:h-2>>1;m>0&&(V(r,n,s-u-S[h]),n+=m);for(var g=0;g<d;++g)i[o]=32767&s,o=(o<<5^t[s])&32767,++s;c-=d-1}else h<=143?h+=48:(f=(1&(f=1))<<(7&(l=n)),r[l>>>3]|=f,n=l+1),n=H(r,n,I[h]),i[o]=32767&s,++s}n=H(r,n,0)-1}return r.l=(n+7)/8|0,r.l}(t,r)}}();function ee(e){var t=ri(50+Math.floor(1.1*e.length)),r=Q(e,t);return t.slice(0,r)}var et=k?new Uint16Array(32768):G(32768),er=k?new Uint16Array(32768):G(32768),ea=k?new Uint16Array(128):G(128),en=1,es=1;function ei(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[C(t),2];for(var r=0,a=0,n=O(t||262144),s=0,i=n.length>>>0,c=0,o=0;(1&a)==0;){if(a=D(e,r),r+=3,a>>>1==0){7&r&&(r+=8-(7&r));var l=e[r>>>3]|e[(r>>>3)+1]<<8;if(r+=32,l>0)for(!t&&i<s+l&&(i=(n=z(n,s+l)).length);l-- >0;)n[s++]=e[r>>>3],r+=8;continue}for(a>>1==1?(c=9,o=5):(r=function(e,t){var r,a,n,s=M(e,t)+257,i=M(e,t+=5)+1;t+=5;var c=(a=7&(r=t),((e[n=r>>>3]|(a<=4?0:e[n+1]<<8))>>>a&15)+4);t+=4;for(var o=0,l=k?new Uint8Array(19):G(19),f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],h=1,u=k?new Uint8Array(8):G(8),d=k?new Uint8Array(8):G(8),p=l.length,m=0;m<c;++m)l[w[m]]=o=D(e,t),h<o&&(h=o),u[o]++,t+=3;var g=0;for(m=1,u[0]=0;m<=h;++m)d[m]=g=g+u[m-1]<<1;for(m=0;m<p;++m)0!=(g=l[m])&&(f[m]=d[g]++);var v=0;for(m=0;m<p;++m)if(0!=(v=l[m])){g=I[f[m]]>>8-v;for(var b=(1<<7-v)-1;b>=0;--b)ea[g|b<<v]=7&v|m<<3}var T=[];for(h=1;T.length<s+i;)switch(g=ea[U(e,t)],t+=7&g,g>>>=3){case 16:for(o=3+function(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=6?0:e[a+1]<<8))>>>r&3}(e,t),t+=2,g=T[T.length-1];o-- >0;)T.push(g);break;case 17:for(o=3+D(e,t),t+=3;o-- >0;)T.push(0);break;case 18:for(o=11+U(e,t),t+=7;o-- >0;)T.push(0);break;default:T.push(g),h<g&&(h=g)}var E=T.slice(0,s),S=T.slice(s);for(m=s;m<286;++m)E[m]=0;for(m=i;m<30;++m)S[m]=0;return en=j(E,et,286),es=j(S,er,30),t}(e,r),c=en,o=es);;){!t&&i<s+32767&&(i=(n=z(n,s+32767)).length);var f=B(e,r,c),h=a>>>1==1?X[f]:et[f];if(r+=15&h,((h>>>=4)>>>8&255)==0)n[s++]=h;else if(256==h)break;else{var u=(h-=257)<8?0:h-4>>2;u>5&&(u=0);var d=s+E[h];u>0&&(d+=B(e,r,u),r+=u),f=B(e,r,o),r+=15&(h=a>>>1==1?Y[f]:er[f]);var p=(h>>>=4)<4?0:h-2>>1,m=S[h];for(p>0&&(m+=B(e,r,p),r+=p),!t&&i<d&&(i=(n=z(n,d+100)).length);s<d;)n[s]=n[s-m],++s}}}return t?[n,r+7>>>3]:[n.slice(0,s),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function ec(e,t){if(e)"undefined"!=typeof console&&console.error(t);else throw Error(t)}function eo(e,r){rn(e,0);var a={FileIndex:[],FullPaths:[]};f(a,{root:r.root});for(var n=e.length-4;(80!=e[n]||75!=e[n+1]||5!=e[n+2]||6!=e[n+3])&&n>=0;)--n;e.l=n+4,e.l+=4;var i=e.read_shift(2);e.l+=6;var c=e.read_shift(4);for(n=0,e.l=c;n<i;++n){e.l+=20;var o=e.read_shift(4),l=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d=e.read_shift(2);e.l+=8;var p=e.read_shift(4),m=s(e.slice(e.l+h,e.l+h+u));e.l+=h+u+d;var g=e.l;e.l=p+4,function(e,r,a,n,i){e.l+=2;var c,o,l,f,h,u,d,p=e.read_shift(2),m=e.read_shift(2),g=(c=65535&e.read_shift(2),o=65535&e.read_shift(2),l=new Date,f=31&o,h=15&(o>>>=5),o>>>=4,l.setMilliseconds(0),l.setFullYear(o+1980),l.setMonth(h-1),l.setDate(f),u=31&c,d=63&(c>>>=5),c>>>=6,l.setHours(c),l.setMinutes(d),l.setSeconds(u<<1),l);if(8257&p)throw Error("Unsupported ZIP encryption");for(var v=e.read_shift(4),b=e.read_shift(4),T=e.read_shift(4),w=e.read_shift(2),E=e.read_shift(2),S="",k=0;k<w;++k)S+=String.fromCharCode(e[e.l++]);if(E){var A=s(e.slice(e.l,e.l+E));(A[21589]||{}).mt&&(g=A[21589].mt),((i||{})[21589]||{}).mt&&(g=i[21589].mt)}e.l+=E;var y=e.slice(e.l,e.l+b);switch(m){case 8:y=function(e,r){if(!t)return ei(e,r);var a=new t.InflateRaw,n=a._processChunk(e.slice(e.l),a._finishFlushFlag);return e.l+=a.bytesRead,n}(e,T);break;case 0:break;default:throw Error("Unsupported ZIP Compression method "+m)}var _=!1;8&p&&(0x8074b50==e.read_shift(4)&&(e.read_shift(4),_=!0),b=e.read_shift(4),T=e.read_shift(4)),b!=r&&ec(_,"Bad compressed size: "+r+" != "+b),T!=a&&ec(_,"Bad uncompressed size: "+a+" != "+T),ef(n,S,y,{unsafe:!0,mt:g})}(e,o,l,a,m),e.l=g}return a}var el={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function ef(e,t,r,a){var s=a&&a.unsafe;s||f(e);var i=!s&&ey.find(e,t);if(!i){var c=e.FullPaths[0];t.slice(0,c.length)==c?c=t:("/"!=c.slice(-1)&&(c+="/"),c=(c+t).replace("//","/")),i={name:n(t),type:2},e.FileIndex.push(i),e.FullPaths.push(c),s||ey.utils.cfb_gc(e)}return i.content=r,i.size=r?r.length:0,a&&(a.CLSID&&(i.clsid=a.CLSID),a.mt&&(i.mt=a.mt),a.ct&&(i.ct=a.ct)),i}return r.find=function(e,t){var r=e.FullPaths.map(function(e){return e.toUpperCase()}),a=r.map(function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]}),n=!1;47===t.charCodeAt(0)?(n=!0,t=r[0].slice(0,-1)+t):n=-1!==t.indexOf("/");var s=t.toUpperCase(),i=!0===n?r.indexOf(s):a.indexOf(s);if(-1!==i)return e.FileIndex[i];var c=!s.match(L);for(s=s.replace(P,""),c&&(s=s.replace(L,"!")),i=0;i<r.length;++i)if((c?r[i].replace(L,"!"):r[i]).replace(P,"")==s||(c?a[i].replace(L,"!"):a[i]).replace(P,"")==s)return e.FileIndex[i];return null},r.read=function(t,r){var a=r&&r.type;switch(!a&&_&&o.isBuffer(t)&&(a="buffer"),a||"base64"){case"file":return i(),c(e.readFileSync(t),r);case"base64":return c(R(y(t)),r);case"binary":return c(R(t),r)}return c(t,r)},r.parse=c,r.write=function(t,r){var a=u(t,r);switch(r&&r.type||"buffer"){case"file":i(),e.writeFileSync(r.filename,a);break;case"binary":return"string"==typeof a?a:T(a);case"base64":return A("string"==typeof a?a:T(a));case"buffer":if(_)return o.isBuffer(a)?a:x(a);case"array":return"string"==typeof a?R(a):a}return a},r.writeFile=function(t,r,a){i();var n=u(t,a);e.writeFileSync(r,n)},r.utils={cfb_new:function(e){var t={};return f(t,e),t},cfb_add:ef,cfb_del:function(e,t){f(e);var r=ey.find(e,t);if(r){for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==r)return e.FileIndex.splice(a,1),e.FullPaths.splice(a,1),!0}return!1},cfb_mov:function(e,t,r){f(e);var a=ey.find(e,t);if(a){for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==a)return e.FileIndex[s].name=n(r),e.FullPaths[s]=r,!0}return!1},cfb_gc:function(e){h(e,!0)},ReadShift:t7,CheckField:ra,prep_blob:rn,bconcat:F,use_zlib:function(e){try{var r=new e.InflateRaw;if(r._processChunk(new Uint8Array([3,0]),r._finishFlushFlag),r.bytesRead)t=e;else throw Error("zlib does not expose bytesRead")}catch(e){console.error("cannot use native zlib: "+(e.message||e))}},_deflateRaw:ee,_inflateRaw:ei,consts:b},r}();function e_(e,t,r){if(void 0!==a&&a.writeFileSync)return r?a.writeFileSync(e,t,r):a.writeFileSync(e,t);if("undefined"!=typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=I(t);break;default:throw Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n="utf8"==r?tT(t):t;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(n,e);if("undefined"!=typeof Blob){var s=new Blob([function(e){if("string"==typeof e)return I(e);if(Array.isArray(e)){if("undefined"==typeof Uint8Array)throw Error("Unsupported");return new Uint8Array(e)}return e}(n)],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(s,e);if("undefined"!=typeof saveAs)return saveAs(s,e);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(s);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var c=document.createElement("a");if(null!=c.download)return c.download=e,c.href=i,document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var o=File(e);return o.open("w"),o.encoding="binary",Array.isArray(t)&&(t=N(t)),o.write(t),o.close(),t}catch(e){if(!e.message||!e.message.match(/onstruct/))throw e}throw Error("cannot save file "+e)}function ex(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function eC(e,t){for(var r=[],a=ex(e),n=0;n!==a.length;++n)null==r[e[a[n]][t]]&&(r[e[a[n]][t]]=a[n]);return r}function eO(e){for(var t=[],r=ex(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}function eR(e){for(var t=[],r=ex(e),a=0;a!==r.length;++a)t[e[r[a]]]=parseInt(r[a],10);return t}var eI=new Date(1899,11,30,0,0,0);function eN(e,t){var r=e.getTime();return t&&(r-=1263168e5),(r-(eI.getTime()+(e.getTimezoneOffset()-eI.getTimezoneOffset())*6e4))/864e5}var eD=new Date,eF=eI.getTime()+(eD.getTimezoneOffset()-eI.getTimezoneOffset())*6e4,eP=eD.getTimezoneOffset();function eL(e){var t=new Date;return t.setTime(24*e*36e5+eF),t.getTimezoneOffset()!==eP&&t.setTime(t.getTime()+(t.getTimezoneOffset()-eP)*6e4),t}var eM=new Date("2017-02-19T19:06:09.000Z"),eU=isNaN(eM.getFullYear())?new Date("2/19/17"):eM,eB=2017==eU.getFullYear();function eW(e,t){var r=new Date(e);if(eB)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==eU.getFullYear()&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-60*s.getTimezoneOffset()*1e3)),s}function eH(e,t){if(_&&o.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return tT(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return tT(T(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return tT(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return tT(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"\x80","‚":"\x82",ƒ:"\x83","„":"\x84","…":"\x85","†":"\x86","‡":"\x87",ˆ:"\x88","‰":"\x89",Š:"\x8a","‹":"\x8b",Œ:"\x8c",Ž:"\x8e","‘":"\x91","’":"\x92","“":"\x93","”":"\x94","•":"\x95","–":"\x96","—":"\x97","˜":"\x98","™":"\x99",š:"\x9a","›":"\x9b",œ:"\x9c",ž:"\x9e",Ÿ:"\x9f"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(e){return r[e]||e})}catch(e){}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function eV(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=eV(e[r]));return t}function ez(e,t){for(var r="";r.length<t;)r+=e;return r}function eG(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return isNaN(t=Number(a))&&isNaN(t=Number(a=a.replace(/[(](.*)[)]/,function(e,t){return r=-r,t})))?t:t/r}var ej=["january","february","march","april","may","june","july","august","september","october","november","december"];function e$(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==ej.indexOf(i))return r}else if(i.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||s>1)&&101!=a?t:e.match(/[^-0-9:,\/\\]/)?r:t}var eX=function(){var e=5=="abacaba".split(/(:?b)/i).length;return function(t,r,a){if(e||"string"==typeof r)return t.split(r);for(var n=t.split(r),s=[n[0]],i=1;i<n.length;++i)s.push(a),s.push(n[i]);return s}}();function eY(e){return e?e.content&&e.type?eH(e.content,!0):e.data?w(e.data):e.asNodeBuffer&&_?w(e.asNodeBuffer().toString("binary")):e.asBinary?w(e.asBinary()):e._data&&e._data.getContent?w(eH(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function eK(e){if(!e)return null;if(e.data)return b(e.data);if(e.asNodeBuffer&&_)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return"string"==typeof t?b(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function eJ(e,t){for(var r=e.FullPaths||ex(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function eq(e,t){var r=eJ(e,t);if(null==r)throw Error("Cannot find file "+t+" in zip");return r}function eZ(e,t,r){if(!r){var a;return(a=eq(e,t))&&".bin"===a.name.slice(-4)?eK(a):eY(a)}if(!t)return null;try{return eZ(e,t)}catch(e){return null}}function eQ(e,t,r){if(!r)return eY(eq(e,t));if(!t)return null;try{return eQ(e,t)}catch(e){return null}}function e1(e){for(var t=e.FullPaths||ex(e.files),r=[],a=0;a<t.length;++a)"/"!=t[a].slice(-1)&&r.push(t[a].replace(/^Root Entry[\/]/,""));return r.sort()}function e0(e,t,r){if(e.FullPaths){if("string"==typeof r){var a;return a=_?x(r):function(e){for(var t=[],r=0,a=e.length+250,n=C(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[r++]=i;else if(i<2048)n[r++]=192|i>>6&31,n[r++]=128|63&i;else if(i>=55296&&i<57344){i=(1023&i)+64;var c=1023&e.charCodeAt(++s);n[r++]=240|i>>8&7,n[r++]=128|i>>2&63,n[r++]=128|c>>6&15|(3&i)<<4,n[r++]=128|63&c}else n[r++]=224|i>>12&15,n[r++]=128|i>>6&63,n[r++]=128|63&i;r>a&&(t.push(n.slice(0,r)),r=0,n=C(65535),a=65530)}return t.push(n.slice(0,r)),F(t)}(r),ey.utils.cfb_add(e,t,a)}ey.utils.cfb_add(e,t,r)}else e.file(t,r)}function e2(){return ey.utils.cfb_new()}function e4(e,t){switch(t.type){case"base64":return ey.read(e,{type:"base64"});case"binary":return ey.read(e,{type:"binary"});case"buffer":case"array":return ey.read(e,{type:"buffer"})}throw Error("Unrecognized type "+t.type)}function e3(e,t){if("/"==e.charAt(0))return e.slice(1);var r=t.split("/");"/"!=t.slice(-1)&&r.pop();for(var a=e.split("/");0!==a.length;){var n=a.shift();".."===n?r.pop():"."!==n&&r.push(n)}return r.join("/")}var e5='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',e6=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,e8=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,e7=e5.match(e8)?e8:/<[^>]*>/g,e9=/<\w*:/,te=/<(\/?)\w+:/;function tt(e,t,r){for(var a={},n=0,s=0;n!==e.length&&32!==(s=e.charCodeAt(n))&&10!==s&&13!==s;++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(e6),c=0,o="",l=0,f="",h="",u=1;if(i)for(l=0;l!=i.length;++l){for(s=0,h=i[l];s!=h.length&&61!==h.charCodeAt(s);++s);for(f=h.slice(0,s).trim();32==h.charCodeAt(s+1);)++s;for(c=0,u=+(34==(n=h.charCodeAt(s+1))||39==n),o=h.slice(s+1+u,h.length-u);c!=f.length&&58!==f.charCodeAt(c);++c);if(c===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=o,r||(a[f.toLowerCase()]=o);else{var d=(5===c&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(c+1);if(a[d]&&"ext"==f.slice(c-3,c))continue;a[d]=o,r||(a[d.toLowerCase()]=o)}}return a}function tr(e){return e.replace(te,"<$1")}var ta={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},tn=eO(ta),ts=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,t=/_x([\da-fA-F]{4})_/ig;return function r(a){var n=a+"",s=n.indexOf("<![CDATA[");if(-1==s)return n.replace(e,function(e,t){return ta[e]||String.fromCharCode(parseInt(t,e.indexOf("x")>-1?16:10))||e}).replace(t,function(e,t){return String.fromCharCode(parseInt(t,16))});var i=n.indexOf("]]>");return r(n.slice(0,s))+n.slice(s+9,i)+r(n.slice(i+3))}}(),ti=/[&<>'"]/g,tc=/[\u0000-\u0008\u000b-\u001f]/g;function to(e){return(e+"").replace(ti,function(e){return tn[e]}).replace(tc,function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"})}function tl(e){return to(e).replace(/ /g,"_x0020_")}var tf=/[\u0000-\u001f]/g;function th(e){return(e+"").replace(ti,function(e){return tn[e]}).replace(/\n/g,"<br/>").replace(tf,function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})}var tu=function(){var e=/&#(\d+);/g;function t(e,t){return String.fromCharCode(parseInt(t,10))}return function(r){return r.replace(e,t)}}();function td(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function tp(e){for(var t="",r=0,a=0,n=0,s=0,i=0,c=0;r<e.length;){if((a=e.charCodeAt(r++))<128){t+=String.fromCharCode(a);continue}if(n=e.charCodeAt(r++),a>191&&a<224){t+=String.fromCharCode((31&a)<<6|63&n);continue}if(s=e.charCodeAt(r++),a<240){t+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&s);continue}t+=String.fromCharCode(55296+((c=((7&a)<<18|(63&n)<<12|(63&s)<<6|63&e.charCodeAt(r++))-65536)>>>10&1023)),t+=String.fromCharCode(56320+(1023&c))}return t}function tm(e){var t,r,a,n=C(2*e.length),s=1,i=0,c=0;for(r=0;r<e.length;r+=s)s=1,(a=e.charCodeAt(r))<128?t=a:a<224?(t=(31&a)*64+(63&e.charCodeAt(r+1)),s=2):a<240?(t=(15&a)*4096+(63&e.charCodeAt(r+1))*64+(63&e.charCodeAt(r+2)),s=3):(s=4,c=55296+((t=(7&a)*262144+(63&e.charCodeAt(r+1))*4096+(63&e.charCodeAt(r+2))*64+(63&e.charCodeAt(r+3))-65536)>>>10&1023),t=56320+(1023&t)),0!==c&&(n[i++]=255&c,n[i++]=c>>>8,c=0),n[i++]=t%256,n[i++]=t>>>8;return n.slice(0,i).toString("ucs2")}function tg(e){return x(e,"binary").toString("utf8")}var tv="foo bar baz\xe2\x98\x83\xf0\x9f\x8d\xa3",tb=_&&(tg(tv)==tp(tv)&&tg||tm(tv)==tp(tv)&&tm)||tp,tT=_?function(e){return x(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(63&a)));break;case a>=55296&&a<57344:a-=55296,t.push(String.fromCharCode(240+((n=e.charCodeAt(r++)-56320+(a<<10))>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)))}return t.join("")},tw=function(){var e={};return function(t,r){var a=t+"|"+(r||"");return e[a]?e[a]:e[a]=RegExp("<(?:\\w+:)?"+t+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+t+">",r||"")}}(),tE=function(){var e=[["nbsp"," "],["middot","\xb7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(e){return[RegExp("&"+e[0]+";","ig"),e[1]]});return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),a=0;a<e.length;++a)r=r.replace(e[a][0],e[a][1]);return r}}(),tS=function(){var e={};return function(t){return void 0!==e[t]?e[t]:e[t]=RegExp("<(?:vt:)?"+t+">([\\s\\S]*?)</(?:vt:)?"+t+">","g")}}(),tk=/<\/?(?:vt:)?variant>/g,tA=/<(?:vt:)([^>]*)>([\s\S]*)</;function ty(e,t){var r=tt(e),a=e.match(tS(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach(function(e){var t=e.replace(tk,"").match(tA);t&&n.push({v:tb(t[2]),t:t[1]})}),n}var t_=/(^\s|\s$|\n)/;function tx(e,t){return"<"+e+(t.match(t_)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function tC(e){return ex(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function tO(e,t,r){return"<"+e+(null!=r?tC(r):"")+(null!=t?(t.match(t_)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function tR(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(e){if(t)throw e}return""}function tI(e){if(_&&o.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return tb(N(D(e)));throw Error("Bad input format: expected Buffer or string")}var tN=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,tD={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},tF=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],tP={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"},tL=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var a=0,n=e[0][r].length;a<n;a+=10240)t.push.apply(t,e[0][r].slice(a,a+10240));return t},tM=_?function(e){return e[0].length>0&&o.isBuffer(e[0][0])?o.concat(e[0].map(function(e){return o.isBuffer(e)?e:x(e)})):tL(e)}:tL,tU=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(t3(e,n)));return a.join("").replace(P,"")},tB=_?function(e,t,r){return o.isBuffer(e)?e.toString("utf16le",t,r).replace(P,""):tU(e,t,r)}:tU,tW=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},tH=_?function(e,t,r){return o.isBuffer(e)?e.toString("hex",t,t+r):tW(e,t,r)}:tW,tV=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(t4(e,n)));return a.join("")},tz=_?function(e,t,r){return o.isBuffer(e)?e.toString("utf8",t,r):tV(e,t,r)}:tV,tG=function(e,t){var r=t6(e,t);return r>0?tz(e,t+4,t+4+r-1):""},tj=tG,t$=function(e,t){var r=t6(e,t);return r>0?tz(e,t+4,t+4+r-1):""},tX=t$,tY=function(e,t){var r=2*t6(e,t);return r>0?tz(e,t+4,t+4+r-1):""},tK=tY,tJ=function(e,t){var r=t6(e,t);return r>0?tB(e,t+4,t+4+r):""},tq=tJ,tZ=function(e,t){var r=t6(e,t);return r>0?tz(e,t+4,t+4+r):""},tQ=tZ,t1=function(e,t){for(var r=1-2*(e[t+7]>>>7),a=((127&e[t+7])<<4)+(e[t+6]>>>4&15),n=15&e[t+6],s=5;s>=0;--s)n=256*n+e[t+s];return 2047==a?0==n?1/0*r:NaN:(0==a?a=-1022:(a-=1023,n+=0x10000000000000),r*Math.pow(2,a-52)*n)},t0=t1,t2=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};_&&(tj=function(e,t){if(!o.isBuffer(e))return tG(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tX=function(e,t){if(!o.isBuffer(e))return t$(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tK=function(e,t){if(!o.isBuffer(e))return tY(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},tq=function(e,t){if(!o.isBuffer(e))return tJ(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},tQ=function(e,t){if(!o.isBuffer(e))return tZ(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},t0=function(e,t){return o.isBuffer(e)?e.readDoubleLE(t):t1(e,t)},t2=function(e){return o.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array}),void 0!==n&&(tB=function(e,t,r){return n.utils.decode(1200,e.slice(t,r)).replace(P,"")},tz=function(e,t,r){return n.utils.decode(65001,e.slice(t,r))},tj=function(e,t){var r=t6(e,t);return r>0?n.utils.decode(u,e.slice(t+4,t+4+r-1)):""},tX=function(e,t){var r=t6(e,t);return r>0?n.utils.decode(h,e.slice(t+4,t+4+r-1)):""},tK=function(e,t){var r=2*t6(e,t);return r>0?n.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},tq=function(e,t){var r=t6(e,t);return r>0?n.utils.decode(1200,e.slice(t+4,t+4+r)):""},tQ=function(e,t){var r=t6(e,t);return r>0?n.utils.decode(65001,e.slice(t+4,t+4+r)):""});var t4=function(e,t){return e[t]},t3=function(e,t){return 256*e[t+1]+e[t]},t5=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-((65535-r+1)*1)},t6=function(e,t){return 0x1000000*e[t+3]+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},t8=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]};function t7(e,t){var r,a,s,i,c,l,f="",u=[];switch(t){case"dbcs":if(l=this.l,_&&o.isBuffer(this))f=this.slice(this.l,this.l+2*e).toString("utf16le");else for(c=0;c<e;++c)f+=String.fromCharCode(t3(this,l)),l+=2;e*=2;break;case"utf8":f=tz(this,this.l,this.l+e);break;case"utf16le":e*=2,f=tB(this,this.l,this.l+e);break;case"wstr":if(void 0===n)return t7.call(this,e,"dbcs");f=n.utils.decode(h,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":f=tj(this,this.l),e=4+t6(this,this.l);break;case"lpstr-cp":f=tX(this,this.l),e=4+t6(this,this.l);break;case"lpwstr":f=tK(this,this.l),e=4+2*t6(this,this.l);break;case"lpp4":e=4+t6(this,this.l),f=tq(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+t6(this,this.l),f=tQ(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,f="";0!==(s=t4(this,this.l+e++));)u.push(E(s));f=u.join("");break;case"_wstr":for(e=0,f="";0!==(s=t3(this,this.l+e));)u.push(E(s)),e+=2;e+=2,f=u.join("");break;case"dbcs-cont":for(c=0,f="",l=this.l;c<e;++c){if(this.lens&&-1!==this.lens.indexOf(l))return s=t4(this,l),this.l=l+1,i=t7.call(this,e-c,s?"dbcs-cont":"sbcs-cont"),u.join("")+i;u.push(E(t3(this,l))),l+=2}f=u.join(""),e*=2;break;case"cpstr":if(void 0!==n){f=n.utils.decode(h,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(c=0,f="",l=this.l;c!=e;++c){if(this.lens&&-1!==this.lens.indexOf(l))return s=t4(this,l),this.l=l+1,i=t7.call(this,e-c,s?"dbcs-cont":"sbcs-cont"),u.join("")+i;u.push(E(t4(this,l))),l+=1}f=u.join("");break;default:switch(e){case 1:return r=t4(this,this.l),this.l++,r;case 2:return r=("i"===t?t5:t3)(this,this.l),this.l+=2,r;case 4:case -4:if("i"===t||(128&this[this.l+3])==0)return r=(e>0?t8:function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]})(this,this.l),this.l+=4,r;return a=t6(this,this.l),this.l+=4,a;case 8:case -8:if("f"===t)return a=8==e?t0(this,this.l):t0([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:f=tH(this,this.l,e)}}return this.l+=e,f}var t9=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},re=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},rt=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function rr(e,t,r){var a=0,s=0;if("dbcs"===r){for(s=0;s!=t.length;++s)rt(this,t.charCodeAt(s),this.l+2*s);a=2*t.length}else if("sbcs"===r){if(void 0!==n&&874==u)for(s=0;s!=t.length;++s){var i=n.utils.encode(u,t.charAt(s));this[this.l+s]=i[0]}else for(s=0,t=t.replace(/[^\x00-\x7F]/g,"_");s!=t.length;++s)this[this.l+s]=255&t.charCodeAt(s);a=t.length}else if("hex"===r){for(;s<e;++s)this[this.l++]=parseInt(t.slice(2*s,2*s+2),16)||0;return this}else if("utf16le"===r){var c=Math.min(this.l+e,this.length);for(s=0;s<Math.min(t.length,e);++s){var o=t.charCodeAt(s);this[this.l++]=255&o,this[this.l++]=o>>8}for(;this.l<c;)this[this.l++]=0;return this}else switch(e){case 1:a=1,this[this.l]=255&t;break;case 2:a=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:a=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:a=4,t9(this,t,this.l);break;case 8:if(a=8,"f"===r){!function(e,t,r){var a=(t<0||1/t==-1/0)<<7,n=0,s=0,i=a?-t:t;isFinite(i)?0==i?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<0x10000000000000)?n=-1022:(s-=0x10000000000000,n+=1023)):(n=2047,s=26985*!!isNaN(t));for(var c=0;c<=5;++c,s/=256)e[r+c]=255&s;e[r+6]=(15&n)<<4|15&s,e[r+7]=n>>4|a}(this,t,this.l);break}case 16:break;case -4:a=4,re(this,t,this.l)}return this.l+=a,this}function ra(e,t){var r=tH(this,this.l,e.length>>1);if(r!==e)throw Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function rn(e,t){e.l=t,e.read_shift=t7,e.chk=ra,e.write_shift=rr}function rs(e,t){e.l+=t}function ri(e){var t=C(e);return rn(t,0),t}function rc(e,t,r){if(e){rn(e,e.l||0);for(var a,n,s,i=e.length,c=0,o=0;e.l<i;){128&(c=e.read_shift(1))&&(c=(127&c)+((127&e.read_shift(1))<<7));var l=id[c]||id[65535];for(n=1,s=127&(a=e.read_shift(1));n<4&&128&a;++n)s+=(127&(a=e.read_shift(1)))<<7*n;o=e.l+s;var f=l.f&&l.f(e,s,r);if(e.l=o,t(f,l,c))return}}}function ro(){var e=[],t=_?256:2048,r=function(e){var t=ri(e);return rn(t,0),t},a=r(t),n=function(){a&&(a.length>a.l&&((a=a.slice(0,a.l)).l=a.length),a.length>0&&e.push(a),a=null)},s=function(e){return a&&e<a.length-a.l?a:(n(),a=r(Math.max(e+1,t)))};return{next:s,push:function(e){n(),null==(a=e).l&&(a.l=a.length),s(t)},end:function(){return n(),F(e)},_bufs:e}}function rl(e,t,r,a){var n,s=+t;if(!isNaN(s)){a||(a=id[s].p||(r||[]).length||0),n=1+ +(s>=128)+1,a>=128&&++n,a>=16384&&++n,a>=2097152&&++n;var i=e.next(n);s<=127?i.write_shift(1,s):(i.write_shift(1,(127&s)+128),i.write_shift(1,s>>7));for(var c=0;4!=c;++c)if(a>=128)i.write_shift(1,(127&a)+128),a>>=7;else{i.write_shift(1,a);break}a>0&&t2(r)&&e.push(r)}}function rf(e,t,r){var a=eV(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function rh(e,t,r){var a=eV(e);return a.s=rf(a.s,t.s,r),a.e=rf(a.e,t.s,r),a}function ru(e,t){if(e.cRel&&e.c<0)for(e=eV(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=eV(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=rT(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=r.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")),r}function rd(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?ru(e.s,t.biff)+":"+ru(e.e,t.biff):(e.s.rRel?"":"$")+rm(e.s.r)+":"+(e.e.rRel?"":"$")+rm(e.e.r):(e.s.cRel?"":"$")+rv(e.s.c)+":"+(e.e.cRel?"":"$")+rv(e.e.c)}function rp(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function rm(e){return""+(e+1)}function rg(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function rv(e){if(e<0)throw Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function rb(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function rT(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function rw(e){var t=e.indexOf(":");return -1==t?{s:rb(e),e:rb(e)}:{s:rb(e.slice(0,t)),e:rb(e.slice(t+1))}}function rE(e,t){return void 0===t||"number"==typeof t?rE(e.s,e.e):("string"!=typeof e&&(e=rT(e)),"string"!=typeof t&&(t=rT(t)),e==t?e:e+":"+t)}function rS(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,s=e.length;for(r=0;a<s&&!((n=e.charCodeAt(a)-64)<1)&&!(n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<s&&!((n=e.charCodeAt(a)-48)<0)&&!(n>9);++a)r=10*r+n;if(t.s.r=--r,a===s||10!=n)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=s&&!((n=e.charCodeAt(a)-64)<1)&&!(n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=s&&!((n=e.charCodeAt(a)-48)<0)&&!(n>9);++a)r=10*r+n;return t.e.r=--r,t}function rk(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=eb(e.z,r?eN(t):t)}catch(e){}try{return e.w=eb((e.XF||{}).numFmtId||14*!!r,r?eN(t):t)}catch(e){return""+t}}function rA(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t)?rq[e.v]||e.v:void 0==t?rk(e,e.v):rk(e,t)}function ry(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function r_(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense,s=e||(n?[]:{}),i=0,c=0;if(s&&null!=a.origin){if("number"==typeof a.origin)i=a.origin;else{var o="string"==typeof a.origin?rb(a.origin):a.origin;i=o.r,c=o.c}s["!ref"]||(s["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=rS(s["!ref"]);l.s.c=f.s.c,l.s.r=f.s.r,l.e.c=Math.max(l.e.c,f.e.c),l.e.r=Math.max(l.e.r,f.e.r),-1==i&&(l.e.r=i=f.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if(void 0!==t[h][u]){var d={v:t[h][u]},p=i+h,m=c+u;if(l.s.r>p&&(l.s.r=p),l.s.c>m&&(l.s.c=m),l.e.r<p&&(l.e.r=p),l.e.c<m&&(l.e.c=m),!t[h][u]||"object"!=typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=t[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else{if(!a.sheetStubs)continue;d.t="z"}else"number"==typeof d.v?d.t="n":"boolean"==typeof d.v?d.t="b":d.v instanceof Date?(d.z=a.dateNF||j[14],a.cellDates?(d.t="d",d.w=eb(d.z,eN(d.v))):(d.t="n",d.v=eN(d.v),d.w=eb(d.z,d.v))):d.t="s";else d=t[h][u];if(n)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var g=rT({c:m,r:p});s[g]&&s[g].z&&(d.z=s[g].z),s[g]=d}}}return l.s.c<1e7&&(s["!ref"]=rE(l)),s}function rx(e,t){return r_(null,e,t)}function rC(e,t){return t||(t=ri(4)),t.write_shift(4,e),t}function rO(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function rR(e,t){var r=!1;return null==t&&(r=!0,t=ri(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function rI(e,t){var r=e.l,a=e.read_shift(1),n=rO(e),s=[],i={t:n,h:n};if((1&a)!=0){for(var c=e.read_shift(4),o=0;o!=c;++o)s.push({ich:e.read_shift(2),ifnt:e.read_shift(2)});i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}function rN(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function rD(e,t){return null==t&&(t=ri(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rF(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function rP(e,t){return null==t&&(t=ri(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rL(e){var t=e.read_shift(4);return 0===t||0xffffffff===t?"":e.read_shift(t,"dbcs")}function rM(e,t){var r=!1;return null==t&&(r=!0,t=ri(127)),t.write_shift(4,e.length>0?e.length:0xffffffff),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function rU(e){var t=e.slice(e.l,e.l+4),r=1&t[0],a=2&t[0];e.l+=4;var n=0===a?t0([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):t8(t,0)>>2;return r?n/100:n}function rB(e,t){null==t&&(t=ri(4));var r=0,a=0,n=100*e;if(e==(0|e)&&e>=-0x20000000&&e<0x20000000?a=1:n==(0|n)&&n>=-0x20000000&&n<0x20000000&&(a=1,r=1),a)t.write_shift(-4,((r?n:e)<<2)+(r+2));else throw Error("unsupported RkNumber "+e)}function rW(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function rH(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function rV(e,t){return(t||ri(8)).write_shift(8,e,"f")}function rz(e,t){if(t||(t=ri(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var a=e.rgb||"FFFFFF";"number"==typeof a&&(a=("000000"+a.toString(16)).slice(-6)),t.write_shift(1,parseInt(a.slice(0,2),16)),t.write_shift(1,parseInt(a.slice(2,4),16)),t.write_shift(1,parseInt(a.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}function rG(e,t){var r=e.read_shift(4);switch(r){case 0:return"";case 0xffffffff:case 0xfffffffe:return({2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"})[e.read_shift(4)]||""}if(r>400)throw Error("Unsupported Clipboard: "+r.toString(16));return e.l-=4,e.read_shift(0,1==t?"lpstr":"lpwstr")}var rj=[80,81],r$={1:{n:"CodePage",t:2},2:{n:"Category",t:80},3:{n:"PresentationFormat",t:80},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:80},15:{n:"Company",t:80},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:80},27:{n:"ContentStatus",t:80},28:{n:"Language",t:80},29:{n:"Version",t:80},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},rX={1:{n:"CodePage",t:2},2:{n:"Title",t:80},3:{n:"Subject",t:80},4:{n:"Author",t:80},5:{n:"Keywords",t:80},6:{n:"Comments",t:80},7:{n:"Template",t:80},8:{n:"LastAuthor",t:80},9:{n:"RevNumber",t:80},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:80},19:{n:"DocSecurity",t:3},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},rY={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},rK=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],rJ=eV([0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,8388608,32768,128,8421376,8388736,32896,0xc0c0c0,8421504,0x9999ff,0x993366,0xffffcc,0xccffff,6684774,0xff8080,26316,0xccccff,128,0xff00ff,0xffff00,65535,8388736,8388608,32896,255,52479,0xccffff,0xccffcc,0xffff99,0x99ccff,0xff99cc,0xcc99ff,0xffcc99,3368703,3394764,0x99cc00,0xffcc00,0xff9900,0xff6600,6710937,9868950,13158,3381606,13056,3355392,0x993300,0x993366,3355545,3355443,0xffffff,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].map(function(e){return[e>>16&255,e>>8&255,255&e]})),rq={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},rZ={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},rQ={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},r1={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function r0(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function r2(e,t){var r,a=function(e){for(var t=[],r=ex(e),a=0;a!==r.length;++a)null==t[e[r[a]]]&&(t[e[r[a]]]=[]),t[e[r[a]]].push(r[a]);return t}(rQ),n=[];n[n.length]=e5,n[n.length]=tO("Types",null,{xmlns:tD.CT,"xmlns:xsd":tD.xsd,"xmlns:xsi":tD.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(e){return tO("Default",null,{Extension:e[0],ContentType:e[1]})}));var s=function(a){e[a]&&e[a].length>0&&(r=e[a][0],n[n.length]=tO("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:r1[a][t.bookType]||r1[a].xlsx}))},i=function(r){(e[r]||[]).forEach(function(e){n[n.length]=tO("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:r1[r][t.bookType]||r1[r].xlsx})})},c=function(t){(e[t]||[]).forEach(function(e){n[n.length]=tO("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:a[t][0]})})};return s("workbooks"),i("sheets"),i("charts"),c("themes"),["strs","styles"].forEach(s),["coreprops","extprops","custprops"].forEach(c),c("vba"),c("comments"),c("threadedcomments"),c("drawings"),i("metadata"),c("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var r4={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function r3(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function r5(e,t){var r={"!id":{}};if(!e)return r;"/"!==t.charAt(0)&&(t="/"+t);var a={};return(e.match(e7)||[]).forEach(function(e){var n=tt(e);if("<Relationship"===n[0]){var s={};s.Type=n.Type,s.Target=n.Target,s.Id=n.Id,n.TargetMode&&(s.TargetMode=n.TargetMode),r["External"===n.TargetMode?n.Target:e3(n.Target,t)]=s,a[n.Id]=s}}),r["!id"]=a,r}function r6(e){var t=[e5,tO("Relationships",null,{xmlns:tD.RELS})];return ex(e["!id"]).forEach(function(r){t[t.length]=tO("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function r8(e,t,r,a,n,s){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,n.Id="rId"+t,n.Type=a,n.Target=r,s?n.TargetMode=s:[r4.HLINK,r4.XPATH,r4.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw Error("Cannot rewrite rId "+t);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,t}function r7(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}var r9=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],ae=function(){for(var e=Array(r9.length),t=0;t<r9.length;++t){var r=r9[t],a="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function at(e){var t={};e=tb(e);for(var r=0;r<r9.length;++r){var a=r9[r],n=e.match(ae[r]);null!=n&&n.length>0&&(t[a[1]]=ts(n[1])),"date"===a[2]&&t[a[1]]&&(t[a[1]]=eW(t[a[1]]))}return t}function ar(e,t,r,a,n){null==n[e]&&null!=t&&""!==t&&(n[e]=t,t=to(t),a[a.length]=r?tO(e,t,r):tx(e,t))}function aa(e,t){var r=t||{},a=[e5,tO("cp:coreProperties",null,{"xmlns:cp":tD.CORE_PROPS,"xmlns:dc":tD.dc,"xmlns:dcterms":tD.dcterms,"xmlns:dcmitype":tD.dcmitype,"xmlns:xsi":tD.xsi})],n={};if(!e&&!r.Props)return a.join("");e&&(null!=e.CreatedDate&&ar("dcterms:created","string"==typeof e.CreatedDate?e.CreatedDate:tR(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),null!=e.ModifiedDate&&ar("dcterms:modified","string"==typeof e.ModifiedDate?e.ModifiedDate:tR(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var s=0;s!=r9.length;++s){var i=r9[s],c=r.Props&&null!=r.Props[i[1]]?r.Props[i[1]]:e?e[i[1]]:null;!0===c?c="1":!1===c?c="0":"number"==typeof c&&(c=String(c)),null!=c&&ar(i[0],c,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var an=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];function as(e,t,r,a){var n=[];if("string"==typeof e)n=ty(e,a);else for(var s=0;s<e.length;++s)n=n.concat(e[s].map(function(e){return{v:e}}));var i="string"==typeof t?ty(t,a).map(function(e){return e.v}):t,c=0,o=0;if(i.length>0)for(var l=0;l!==n.length;l+=2){switch(o=+n[l+1].v,n[l].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsbl\xe4tter":case"\xc7alışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de c\xe1lculo":case"Planilhas":case"Regneark":case"Hojas de c\xe1lculo":case"Werkbladen":r.Worksheets=o,r.SheetNames=i.slice(c,c+o);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne omr\xe5der":r.NamedRanges=o,r.DefinedNames=i.slice(c,c+o);break;case"Charts":case"Diagramme":r.Chartsheets=o,r.ChartNames=i.slice(c,c+o)}c+=o}}function ai(e){var t=[];return e||(e={}),e.Application="SheetJS",t[t.length]=e5,t[t.length]=tO("Properties",null,{xmlns:tD.EXT_PROPS,"xmlns:vt":tD.vt}),an.forEach(function(r){var a;if(void 0!==e[r[1]]){switch(r[2]){case"string":a=to(String(e[r[1]]));break;case"bool":a=e[r[1]]?"true":"false"}void 0!==a&&(t[t.length]=tO(r[0],a))}}),t[t.length]=tO("HeadingPairs",tO("vt:vector",tO("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+tO("vt:variant",tO("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=tO("TitlesOfParts",tO("vt:vector",e.SheetNames.map(function(e){return"<vt:lpstr>"+to(e)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var ac=/<[^>]+>[^<]*/g;function ao(e){var t=[e5,tO("Properties",null,{xmlns:tD.CUST_PROPS,"xmlns:vt":tD.vt})];if(!e)return t.join("");var r=1;return ex(e).forEach(function(a){++r,t[t.length]=tO("property",function(e,t){switch(typeof e){case"string":var r=tO("vt:lpwstr",to(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return tO((0|e)==e?"vt:i4":"vt:r8",to(String(e)));case"boolean":return tO("vt:bool",e?"true":"false")}if(e instanceof Date)return tO("vt:filetime",tR(e));throw Error("Unable to serialize "+e)}(e[a],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:to(a)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var al={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function af(e){var t=e.read_shift(4);return new Date((e.read_shift(4)/1e7*0x100000000+t/1e7-0x2b6109100)*1e3).toISOString().replace(/\.000/,"")}function ah(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function au(e,t,r){var a=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(a.length+1&3)&3),a}function ad(e,t,r){return 31===t?au(e):ah(e,t,r)}function ap(e,t,r){return ad(e,t,4*(!1!==r))}function am(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,1200===t?"utf16le":"utf8").replace(P,"").replace(L,"!"),1200===t&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),a}function ag(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(3&t)>0&&(e.l+=4-(3&t)&3),r}function av(e,t,r){var a,n,s=e.read_shift(2),i=r||{};if(e.l+=2,12!==t&&s!==t&&-1===rj.indexOf(t)&&((65534&t)!=4126||(65534&s)!=4126))throw Error("Expected type "+t+" saw "+s);switch(12===t?s:t){case 2:return n=e.read_shift(2,"i"),i.raw||(e.l+=2),n;case 3:return e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return e.read_shift(4);case 30:return ah(e,s,4).replace(P,"");case 31:return au(e);case 64:return af(e);case 65:return ag(e);case 71:return(a={}).Size=e.read_shift(4),e.l+=a.Size+3-(a.Size-1)%4,a;case 80:return ap(e,s,!i.raw).replace(P,"");case 81:return(function(e,t){if(!t)throw Error("VtUnalignedString must have positive length");return ad(e,t,0)})(e,s).replace(P,"");case 4108:for(var c=e.read_shift(4),o=[],l=0;l<c/2;++l)o.push(function(e){var t=e.l,r=av(e,81);return 0==e[e.l]&&0==e[e.l+1]&&e.l-t&2&&(e.l+=2),[r,av(e,3)]}(e));return o;case 4126:case 4127:return 4127==s?function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(P,""),e.l-n&2&&(e.l+=2)}return r}(e):function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(P,"");return r}(e);default:throw Error("TypedPropertyValue unrecognized type "+t+" "+s)}}function ab(e,t){var r,a,n,s,i,c=ri(4),o=ri(4);switch(c.write_shift(4,80==e?31:e),e){case 3:o.write_shift(-4,t);break;case 5:(o=ri(8)).write_shift(8,t,"f");break;case 11:o.write_shift(4,+!!t);break;case 64:a=(r=("string"==typeof t?new Date(Date.parse(t)):t).getTime()/1e3+0x2b6109100)%0x100000000,n=(r-a)/0x100000000*1e7,(s=(a*=1e7)/0x100000000|0)>0&&(a%=0x100000000,n+=s),(i=ri(8)).write_shift(4,a),i.write_shift(4,n),o=i;break;case 31:case 80:for((o=ri(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),o.write_shift(0,t,"dbcs");o.l!=o.length;)o.write_shift(1,0);break;default:throw Error("TypedPropertyValue unrecognized type "+e+" "+t)}return F([c,o])}function aT(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,c=0,o=-1,l={};for(i=0;i!=n;++i){var f=e.read_shift(4),h=e.read_shift(4);s[i]=[f,h+r]}s.sort(function(e,t){return e[1]-t[1]});var u={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var d=!0;if(i>0&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,d=!1);break;case 80:case 4108:e.l<=s[i][1]&&(e.l=s[i][1],d=!1)}if((!t||0==i)&&e.l<=s[i][1]&&(d=!1,e.l=s[i][1]),d)throw Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){var p=t[s[i][0]];if(u[p.n]=av(e,p.t,{raw:!0}),"version"===p.p&&(u[p.n]=String(u[p.n]>>16)+"."+("0000"+String(65535&u[p.n])).slice(-4)),"CodePage"==p.n)switch(u[p.n]){case 0:u[p.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case -536:case 65001:case -535:g(c=u[p.n]>>>0&65535);break;default:throw Error("Unsupported CodePage: "+u[p.n])}}else if(1===s[i][0]){if(g(c=u.CodePage=av(e,2)),-1!==o){var m=e.l;e.l=s[o][1],l=am(e,c),e.l=m}}else if(0===s[i][0]){if(0===c){o=i,e.l=s[i+1][1];continue}l=am(e,c)}else{var v,b=l[s[i][0]];switch(e[e.l]){case 65:e.l+=4,v=ag(e);break;case 30:case 31:e.l+=4,v=ap(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,v=e.read_shift(4,"i");break;case 19:e.l+=4,v=e.read_shift(4);break;case 5:e.l+=4,v=e.read_shift(8,"f");break;case 11:e.l+=4,v=aA(e,4);break;case 64:e.l+=4,v=eW(af(e));break;default:throw Error("unparsed value: "+e[e.l])}u[b]=v}}return e.l=r+a,u}function aw(e,t,r){var a=ri(8),n=[],s=[],i=8,c=0,o=ri(8),l=ri(8);if(o.write_shift(4,2),o.write_shift(4,1200),l.write_shift(4,1),s.push(o),n.push(l),i+=8+o.length,!t){(l=ri(8)).write_shift(4,0),n.unshift(l);var f=[ri(4)];for(f[0].write_shift(4,e.length),c=0;c<e.length;++c){var h=e[c][0];for((o=ri(8+2*(h.length+1)+(h.length%2?0:2))).write_shift(4,c+2),o.write_shift(4,h.length+1),o.write_shift(0,h,"dbcs");o.l!=o.length;)o.write_shift(1,0);f.push(o)}o=F(f),s.unshift(o),i+=8+o.length}for(c=0;c<e.length;++c)if(!(t&&!t[e[c][0]]||null.indexOf(e[c][0])>-1||null.indexOf(e[c][0])>-1)&&null!=e[c][1]){var u=e[c][1],d=0;if(t){var p=r[d=+t[e[c][0]]];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(m[0]<<16)+(+m[1]||0)}o=ab(p.t,u)}else{var g=function(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return -1}(u);-1==g&&(g=31,u=String(u)),o=ab(g,u)}s.push(o),(l=ri(8)).write_shift(4,t?d:2+c),n.push(l),i+=8+o.length}var v=8*(s.length+1);for(c=0;c<s.length;++c)n[c].write_shift(4,v),v+=s[c].length;return a.write_shift(4,i),a.write_shift(4,s.length),F([a].concat(n).concat(s))}function aE(e,t,r){var a,n=e.content;if(!n)return{};rn(n,0);var s,i,c,o,l=0;n.chk("feff","Byte Order: "),n.read_shift(2);var f=n.read_shift(4),h=n.read_shift(16);if(h!==ey.utils.consts.HEADER_CLSID&&h!==r)throw Error("Bad PropertySet CLSID "+h);if(1!==(s=n.read_shift(4))&&2!==s)throw Error("Unrecognized #Sets: "+s);if(i=n.read_shift(16),o=n.read_shift(4),1===s&&o!==n.l)throw Error("Length mismatch: "+o+" !== "+n.l);2===s&&(c=n.read_shift(16),l=n.read_shift(4));var u=aT(n,t),d={SystemIdentifier:f};for(var p in u)d[p]=u[p];if(d.FMTID=i,1===s)return d;if(l-n.l==2&&(n.l+=2),n.l!==l)throw Error("Length mismatch 2: "+n.l+" !== "+l);try{a=aT(n,null)}catch(e){}for(p in a)d[p]=a[p];return d.FMTID=[i,c],d}function aS(e,t,r,a,n,s){var i=ri(n?68:48),c=[i];i.write_shift(2,65534),i.write_shift(2,0),i.write_shift(4,0x32363237),i.write_shift(16,ey.utils.consts.HEADER_CLSID,"hex"),i.write_shift(4,n?2:1),i.write_shift(16,t,"hex"),i.write_shift(4,n?68:48);var o=aw(e,r,a);if(c.push(o),n){var l=aw(n,null,null);i.write_shift(16,s,"hex"),i.write_shift(4,68+o.length),c.push(l)}return F(c)}function ak(e,t){return e.read_shift(t),null}function aA(e,t){return 1===e.read_shift(t)}function ay(e,t){return t||(t=ri(2)),t.write_shift(2,+!!e),t}function a_(e){return e.read_shift(2,"u")}function ax(e,t){return t||(t=ri(2)),t.write_shift(2,e),t}function aC(e,t){for(var r=[],a=e.l+t;e.l<a;)r.push(a_(e,a-e.l));if(a!==e.l)throw Error("Slurp error");return r}function aO(e,t,r){return r||(r=ri(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,+("e"==t)),r}function aR(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont",s=h;r&&r.biff>=8&&(h=1200),r&&8!=r.biff?12==r.biff&&(n="wstr"):e.read_shift(1)&&(n="dbcs-cont"),r.biff>=2&&r.biff<=5&&(n="cpstr");var i=a?e.read_shift(a,n):"";return h=s,i}function aI(e,t,r){var a;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function aN(e,t,r){var a=e.read_shift(r&&2==r.biff?1:2);return 0===a?(e.l++,""):aI(e,a,r)}function aD(e,t,r){if(r.biff>5)return aN(e,t,r);var a=e.read_shift(1);return 0===a?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function aF(e,t,r){return r||(r=ri(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function aP(e){var t=e.read_shift(4);return t>0?e.read_shift(t,"utf16le").replace(P,""):""}function aL(e,t){t||(t=ri(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function aM(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function aU(e,t){var r=aM(e,t);return r[3]=0,r}function aB(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function aW(e,t,r,a){return a||(a=ri(6)),a.write_shift(2,e),a.write_shift(2,t),a.write_shift(2,r||0),a}function aH(e){return[e.read_shift(2),rU(e)]}function aV(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function az(e,t){return t||(t=ri(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function aG(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(1),r:t},e:{c:e.read_shift(1),r:r}}}function aj(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function a$(e){e.l+=2,e.l+=e.read_shift(2)}var aX={0:a$,4:a$,5:a$,6:a$,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:a$,9:a$,10:a$,11:a$,12:a$,13:function(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t},14:a$,15:a$,16:a$,17:a$,18:a$,19:a$,20:a$,21:aj};function aY(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),(t-=2)>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function aK(e,t,r){var a=1536,n=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;default:throw Error("unsupported BIFF version")}var s=ri(n);return s.write_shift(2,a),s.write_shift(2,t),n>4&&s.write_shift(2,29282),n>6&&s.write_shift(2,1997),n>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function aJ(e,t,r){var a=0;r&&2==r.biff||(a=e.read_shift(2));var n=e.read_shift(2);return r&&2==r.biff&&(a=1-(n>>15),n&=32767),[{Unsynced:1&a,DyZero:(2&a)>>1,ExAsc:(4&a)>>2,ExDsc:(8&a)>>3},n]}function aq(e,t,r){var a=e.l+t,n=8!=r.biff&&r.biff?2:4,s=e.read_shift(n),i=e.read_shift(n),c=e.read_shift(2),o=e.read_shift(2);return e.l=a,{s:{r:s,c:c},e:{r:i,c:o}}}function aZ(e,t,r,a){var n=r&&5==r.biff;a||(a=ri(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,t<<4));var s=0;return e.numFmtId>0&&n&&(s|=1024),a.write_shift(4,s),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function aQ(e,t,r){var a,n=aB(e,6);(2==r.biff||9==t)&&++e.l;var s=(a=e.read_shift(1),1===e.read_shift(1)?a:1===a);return n.val=s,n.t=!0===s||!1===s?"b":"e",n}var a1=function(e,t,r){return 0===t?"":aD(e,t,r)};function a0(e,t,r){var a,n=e.read_shift(2),s={fBuiltIn:1&n,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return 14849===r.sbcch&&(a=function(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=aR(e,t,r),s=e.read_shift(2);if(s!==(a-=e.l))throw Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}(e,t-2,r)),s.body=a||e.read_shift(t-2),"string"==typeof a&&(s.Name=a),s}var a2=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function a4(e,t,r){var a,n,s,i,c,o,l,f=e.l+t,h=e.read_shift(2),u=e.read_shift(1),d=e.read_shift(1),p=e.read_shift(r&&2==r.biff?1:2),m=0;(!r||r.biff>=5)&&(5!=r.biff&&(e.l+=2),m=e.read_shift(2),5==r.biff&&(e.l+=2),e.l+=4);var g=aI(e,d,r);32&h&&(g=a2[g.charCodeAt(0)]);var v=f-e.l;return r&&2==r.biff&&--v,{chKey:u,Name:g,itab:m,rgce:f!=e.l&&0!==p&&v>0?(a=e,n=v,s=r,i=p,o=a.l+n,l=sg(a,i,s),o!==a.l&&(c=sm(a,o-a.l,l,s)),[l,c]):[]}}function a3(e,t,r){if(r.biff<8){var a,n,s,i;return a=e,n=t,s=r,3==a[a.l+1]&&a[a.l]++,3==(i=aR(a,n,s)).charCodeAt(0)?i.slice(1):i}for(var c=[],o=e.l+t,l=e.read_shift(r.biff>8?4:2);0!=l--;)c.push(function(e,t,r){var a=r.biff>8?4:2;return[e.read_shift(a),e.read_shift(a,"i"),e.read_shift(a,"i")]}(e,r.biff,r));if(e.l!=o)throw Error("Bad ExternSheet: "+e.l+" != "+o);return c}function a5(e,t,r){var a=aG(e,6);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,function(e,t,r){var a,n,s=e.l+t,i=2==r.biff?1:2,c=e.read_shift(i);if(65535==c)return[[],(a=t-2,void(e.l+=a))];var o=sg(e,c,r);return t!==c+i&&(n=sm(e,t-c-i,o,r)),e.l=s,[o,n]}(e,t,r,a)]}var a6={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function a8(e,t,r){if(!r.cellStyles)return void(e.l+=t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),c=e.read_shift(a),o=e.read_shift(2);2==a&&(e.l+=2);var l={s:n,e:s,w:i,ixfe:c,flags:o};return(r.biff>=5||!r.biff)&&(l.level=o>>8&7),l}var a7=[2,3,48,49,131,139,140,245],a9=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=eO({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var a=r||{};a.dateNF||(a.dateNF="yyyymmdd");var s=rx(function(t,r){var a=[],s=C(1);switch(r.type){case"base64":s=R(y(t));break;case"binary":s=R(t);break;case"buffer":case"array":s=t}rn(s,0);var i=s.read_shift(1),c=!!(136&i),o=!1,l=!1;switch(i){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:o=!0,c=!0;break;case 140:l=!0;break;default:throw Error("DBF Unsupported Version: "+i.toString(16))}var f=0,h=521;2==i&&(f=s.read_shift(2)),s.l+=3,2!=i&&(f=s.read_shift(4)),f>1048576&&(f=1e6),2!=i&&(h=s.read_shift(2));var u=s.read_shift(2),d=r.codepage||1252;2!=i&&(s.l+=16,s.read_shift(1),0!==s[s.l]&&(d=e[s[s.l]]),s.l+=1,s.l+=2),l&&(s.l+=36);for(var p=[],m={},g=Math.min(s.length,2==i?521:h-10-264*!!o),v=l?32:11;s.l<g&&13!=s[s.l];)switch((m={}).name=n.utils.decode(d,s.slice(s.l,s.l+v)).replace(/[\u0000\r\n].*$/g,""),s.l+=v,m.type=String.fromCharCode(s.read_shift(1)),2!=i&&!l&&(m.offset=s.read_shift(4)),m.len=s.read_shift(1),2==i&&(m.offset=s.read_shift(2)),m.dec=s.read_shift(1),m.name.length&&p.push(m),2!=i&&(s.l+=l?13:14),m.type){case"B":(!o||8!=m.len)&&r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw Error("Unknown Field Type: "+m.type)}if(13!==s[s.l]&&(s.l=h-1),13!==s.read_shift(1))throw Error("DBF Terminator not found "+s.l+" "+s[s.l]);s.l=h;var b=0,T=0;for(T=0,a[0]=[];T!=p.length;++T)a[0][T]=p[T].name;for(;f-- >0;){if(42===s[s.l]){s.l+=u;continue}for(++s.l,a[++b]=[],T=0,T=0;T!=p.length;++T){var w=s.slice(s.l,s.l+p[T].len);s.l+=p[T].len,rn(w,0);var E=n.utils.decode(d,w);switch(p[T].type){case"C":E.trim().length&&(a[b][T]=E.replace(/\s+$/,""));break;case"D":8===E.length?a[b][T]=new Date(+E.slice(0,4),E.slice(4,6)-1,+E.slice(6,8)):a[b][T]=E;break;case"F":a[b][T]=parseFloat(E.trim());break;case"+":case"I":a[b][T]=l?0x80000000^w.read_shift(-4,"i"):w.read_shift(4,"i");break;case"L":switch(E.trim().toUpperCase()){case"Y":case"T":a[b][T]=!0;break;case"N":case"F":a[b][T]=!1;break;case"":case"?":break;default:throw Error("DBF Unrecognized L:|"+E+"|")}break;case"M":if(!c)throw Error("DBF Unexpected MEMO for type "+i.toString(16));a[b][T]="##MEMO##"+(l?parseInt(E.trim(),10):w.read_shift(4));break;case"N":(E=E.replace(/\u0000/g,"").trim())&&"."!=E&&(a[b][T]=+E||0);break;case"@":a[b][T]=new Date(w.read_shift(-8,"f")-621356832e5);break;case"T":a[b][T]=new Date((w.read_shift(4)-2440588)*864e5+w.read_shift(4));break;case"Y":a[b][T]=w.read_shift(4,"i")/1e4+w.read_shift(4,"i")/1e4*0x100000000;break;case"O":a[b][T]=-w.read_shift(-8,"f");break;case"B":if(o&&8==p[T].len){a[b][T]=w.read_shift(8,"f");break}case"G":case"P":w.l+=p[T].len;break;case"0":if("_NullFlags"===p[T].name)break;default:throw Error("DBF Unsupported data type "+p[T].type)}}}if(2!=i&&s.l<s.length&&26!=s[s.l++])throw Error("DBF EOF Marker missing "+(s.l-1)+" of "+s.length+" "+s[s.l-1].toString(16));return r&&r.sheetRows&&(a=a.slice(0,r.sheetRows)),r.DBF=p,a}(t,a),a);return s["!cols"]=a.DBF.map(function(e){return{wch:e.len,DBF:e}}),delete a.DBF,s}var a={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return ry(r(e,t),t)}catch(e){if(t&&t.WTF)throw e}return{SheetNames:[],Sheets:{}}},to_sheet:r,from_sheet:function(e,r){var n=r||{};if(+n.codepage>=0&&g(+n.codepage),"string"==n.type)throw Error("Cannot write DBF to JS string");var s=ro(),i=i0(e,{header:1,raw:!0,cellDates:!0}),c=i[0],o=i.slice(1),l=e["!cols"]||[],f=0,h=0,d=0,p=1;for(f=0;f<c.length;++f){if(((l[f]||{}).DBF||{}).name){c[f]=l[f].DBF.name,++d;continue}if(null!=c[f]){if(++d,"number"==typeof c[f]&&(c[f]=c[f].toString(10)),"string"!=typeof c[f])throw Error("DBF Invalid column name "+c[f]+" |"+typeof c[f]+"|");if(c.indexOf(c[f])!==f){for(h=0;h<1024;++h)if(-1==c.indexOf(c[f]+"_"+h)){c[f]+="_"+h;break}}}}var m=rS(e["!ref"]),v=[],b=[],T=[];for(f=0;f<=m.e.c-m.s.c;++f){var w="",E="",S=0,k=[];for(h=0;h<o.length;++h)null!=o[h][f]&&k.push(o[h][f]);if(0==k.length||null==c[f]){v[f]="?";continue}for(h=0;h<k.length;++h){switch(typeof k[h]){case"number":E="B";break;case"string":default:E="C";break;case"boolean":E="L";break;case"object":E=k[h]instanceof Date?"D":"C"}S=Math.max(S,String(k[h]).length),w=w&&w!=E?"C":E}S>250&&(S=250),"C"==(E=((l[f]||{}).DBF||{}).type)&&l[f].DBF.len>S&&(S=l[f].DBF.len),"B"==w&&"N"==E&&(w="N",T[f]=l[f].DBF.dec,S=l[f].DBF.len),b[f]="C"==w||"N"==E?S:a[w]||0,p+=b[f],v[f]=w}var A=s.next(32);for(A.write_shift(4,0x13021130),A.write_shift(4,o.length),A.write_shift(2,296+32*d),A.write_shift(2,p),f=0;f<4;++f)A.write_shift(4,0);for(A.write_shift(4,(+t[u]||3)<<8),f=0,h=0;f<c.length;++f)if(null!=c[f]){var y=s.next(32),_=(c[f].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);y.write_shift(1,_,"sbcs"),y.write_shift(1,"?"==v[f]?"C":v[f],"sbcs"),y.write_shift(4,h),y.write_shift(1,b[f]||a[v[f]]||0),y.write_shift(1,T[f]||0),y.write_shift(1,2),y.write_shift(4,0),y.write_shift(1,0),y.write_shift(4,0),y.write_shift(4,0),h+=b[f]||a[v[f]]||0}var x=s.next(264);for(x.write_shift(4,13),f=0;f<65;++f)x.write_shift(4,0);for(f=0;f<o.length;++f){var C=s.next(p);for(C.write_shift(1,0),h=0;h<c.length;++h)if(null!=c[h])switch(v[h]){case"L":C.write_shift(1,null==o[f][h]?63:o[f][h]?84:70);break;case"B":C.write_shift(8,o[f][h]||0,"f");break;case"N":var O="0";for("number"==typeof o[f][h]&&(O=o[f][h].toFixed(T[h]||0)),d=0;d<b[h]-O.length;++d)C.write_shift(1,32);C.write_shift(1,O,"sbcs");break;case"D":o[f][h]?(C.write_shift(4,("0000"+o[f][h].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(o[f][h].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+o[f][h].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var R=String(null!=o[f][h]?o[f][h]:"").slice(0,b[h]);for(C.write_shift(1,R,"sbcs"),d=0;d<b[h]-R.length;++d)C.write_shift(1,32)}}return s.next(1).write_shift(1,26),s.end()}}}(),ne=function(){var e={AA:"\xc0",BA:"\xc1",CA:"\xc2",DA:195,HA:"\xc4",JA:197,AE:"\xc8",BE:"\xc9",CE:"\xca",HE:"\xcb",AI:"\xcc",BI:"\xcd",CI:"\xce",HI:"\xcf",AO:"\xd2",BO:"\xd3",CO:"\xd4",DO:213,HO:"\xd6",AU:"\xd9",BU:"\xda",CU:"\xdb",HU:"\xdc",Aa:"\xe0",Ba:"\xe1",Ca:"\xe2",Da:227,Ha:"\xe4",Ja:229,Ae:"\xe8",Be:"\xe9",Ce:"\xea",He:"\xeb",Ai:"\xec",Bi:"\xed",Ci:"\xee",Hi:"\xef",Ao:"\xf2",Bo:"\xf3",Co:"\xf4",Do:245,Ho:"\xf6",Au:"\xf9",Bu:"\xfa",Cu:"\xfb",Hu:"\xfc",KC:"\xc7",Kc:"\xe7",q:"\xe6",z:"œ",a:"\xc6",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=RegExp("\x1bN("+ex(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var a=e[r];return"number"==typeof a?S(a):a},a=function(e,t,r){var a=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==a?e:S(a)};function s(e,s){var i,c=e.split(/[\n\r]+/),o=-1,l=-1,f=0,h=0,u=[],d=[],p=null,m={},v=[],b=[],T=[],w=0;for(+s.codepage>=0&&g(+s.codepage);f!==c.length;++f){w=0;var E,S=c[f].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),k=S.replace(/;;/g,"\0").split(";").map(function(e){return e.replace(/\u0000/g,";")}),A=k[0];if(S.length>0)switch(A){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==k[1].charAt(0)&&d.push(S.slice(3).replace(/;;/g,";"));break;case"C":var y=!1,_=!1,x=!1,C=!1,O=-1,R=-1;for(h=1;h<k.length;++h)switch(k[h].charAt(0)){case"A":case"G":break;case"X":l=parseInt(k[h].slice(1))-1,_=!0;break;case"Y":for(o=parseInt(k[h].slice(1))-1,_||(l=0),i=u.length;i<=o;++i)u[i]=[];break;case"K":'"'===(E=k[h].slice(1)).charAt(0)?E=E.slice(1,E.length-1):"TRUE"===E?E=!0:"FALSE"===E?E=!1:isNaN(eG(E))?isNaN(e$(E).getDate())||(E=eW(E)):(E=eG(E),null!==p&&em(p)&&(E=eL(E))),void 0!==n&&"string"==typeof E&&"string"!=(s||{}).type&&(s||{}).codepage&&(E=n.utils.decode(s.codepage,E)),y=!0;break;case"E":C=!0;var I=n5(k[h].slice(1),{r:o,c:l});u[o][l]=[u[o][l],I];break;case"S":x=!0,u[o][l]=[u[o][l],"S5S"];break;case"R":O=parseInt(k[h].slice(1))-1;break;case"C":R=parseInt(k[h].slice(1))-1;break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}if(y&&(u[o][l]&&2==u[o][l].length?u[o][l][0]=E:u[o][l]=E,p=null),x){if(C)throw Error("SYLK shared formula cannot have own formula");var N=O>-1&&u[O][R];if(!N||!N[1])throw Error("SYLK shared formula cannot find base");u[o][l][1]=n7(N[1],{r:o-O,c:l-R})}break;case"F":var D=0;for(h=1;h<k.length;++h)switch(k[h].charAt(0)){case"X":l=parseInt(k[h].slice(1))-1,++D;break;case"Y":for(o=parseInt(k[h].slice(1))-1,i=u.length;i<=o;++i)u[i]=[];break;case"M":w=parseInt(k[h].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":p=d[parseInt(k[h].slice(1))];break;case"W":for(i=parseInt((T=k[h].slice(1).split(" "))[0],10);i<=parseInt(T[1],10);++i)w=parseInt(T[2],10),b[i-1]=0===w?{hidden:!0}:{wch:w},nD(b[i-1]);break;case"C":b[l=parseInt(k[h].slice(1))-1]||(b[l]={});break;case"R":v[o=parseInt(k[h].slice(1))-1]||(v[o]={}),w>0?(v[o].hpt=w,v[o].hpx=nP(w)):0===w&&(v[o].hidden=!0);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}D<1&&(p=null);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}}return v.length>0&&(m["!rows"]=v),b.length>0&&(m["!cols"]=b),s&&s.sheetRows&&(u=u.slice(0,s.sheetRows)),[u,m]}function i(e,t){var r=function(e,t){switch(t.type){case"base64":return s(y(e),t);case"binary":return s(e,t);case"buffer":return s(_&&o.isBuffer(e)?e.toString("binary"):N(e),t);case"array":return s(eH(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),a=r[0],n=r[1],i=rx(a,t);return ex(n).forEach(function(e){i[e]=n[e]}),i}return e["|"]=254,{to_workbook:function(e,t){return ry(i(e,t),t)},to_sheet:i,from_sheet:function(e,t){var r,a=["ID;PWXL;N;E"],n=[],s=rS(e["!ref"]),i=Array.isArray(e);a.push("P;PGeneral"),a.push("F;P0;DG0G8;M255"),e["!cols"]&&e["!cols"].forEach(function(e,t){var r="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?r+="0":("number"!=typeof e.width||e.wpx||(e.wpx=nC(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=nO(e.wpx)),"number"==typeof e.wch&&(r+=Math.round(e.wch)))," "!=r.charAt(r.length-1)&&a.push(r)}),e["!rows"]&&e["!rows"].forEach(function(e,t){var r="F;";e.hidden?r+="M0;":e.hpt?r+="M"+20*e.hpt+";":e.hpx&&(r+="M"+20*nF(e.hpx)+";"),r.length>2&&a.push(r+"R"+(t+1))}),a.push("B;Y"+(s.e.r-s.s.r+1)+";X"+(s.e.c-s.s.c+1)+";D"+[s.s.c,s.s.r,s.e.c,s.e.r].join(" "));for(var c=s.s.r;c<=s.e.r;++c)for(var o=s.s.c;o<=s.e.c;++o){var l=rT({r:c,c:o});(r=i?(e[c]||[])[o]:e[l])&&(null!=r.v||r.f&&!r.F)&&n.push(function(e,t,r,a){var n="C;Y"+(r+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0,e.f&&!e.F&&(n+=";E"+n8(e.f,{r:r,c:a}));break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return n}(r,0,c,o,t))}return a.join("\r\n")+"\r\n"+n.join("\r\n")+"\r\nE\r\n"}}}(),nt=function(){var e,t;function r(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){if("BOT"===r[s].trim()){i[++a]=[],n=0;continue}if(!(a<0)){for(var c=r[s].trim().split(","),o=c[0],l=c[1],f=r[++s]||"";1&(f.match(/["]/g)||[]).length&&s<r.length-1;)f+="\n"+r[++s];switch(f=f.trim(),+o){case -1:if("BOT"===f){i[++a]=[],n=0;continue}if("EOD"!==f)throw Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[a][n]=!0:"FALSE"===f?i[a][n]=!1:isNaN(eG(l))?isNaN(e$(l).getDate())?i[a][n]=l:i[a][n]=eW(l):i[a][n]=eG(l),++n;break;case 1:(f=(f=f.slice(1,f.length-1)).replace(/""/g,'"'))&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),i[a][n++]=""!==f?f:null}if("EOD"===f)break}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}function a(e,t){return rx(function(e,t){switch(t.type){case"base64":return r(y(e),t);case"binary":return r(e,t);case"buffer":return r(_&&o.isBuffer(e)?e.toString("binary"):N(e),t);case"array":return r(eH(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),t)}return{to_workbook:function(e,t){return ry(a(e,t),t)},to_sheet:a,from_sheet:(e=function(e,t,r,a,n){e.push(t),e.push(r+","+a),e.push('"'+n.replace(/"/g,'""')+'"')},t=function(e,t,r,a){e.push(t+","+r),e.push(1==t?'"'+a.replace(/"/g,'""')+'"':a)},function(r){var a,n=[],s=rS(r["!ref"]),i=Array.isArray(r);e(n,"TABLE",0,1,"sheetjs"),e(n,"VECTORS",0,s.e.r-s.s.r+1,""),e(n,"TUPLES",0,s.e.c-s.s.c+1,""),e(n,"DATA",0,0,"");for(var c=s.s.r;c<=s.e.r;++c){t(n,-1,0,"BOT");for(var o=s.s.c;o<=s.e.c;++o){var l=rT({r:c,c:o});if(!(a=i?(r[c]||[])[o]:r[l])){t(n,1,0,"");continue}switch(a.t){case"n":var f=a.w;f||null==a.v||(f=a.v),null==f?!a.f||a.F?t(n,1,0,""):t(n,1,0,"="+a.f):t(n,0,f,"V");break;case"b":t(n,0,+!!a.v,a.v?"TRUE":"FALSE");break;case"s":t(n,1,0,isNaN(a.v)?a.v:'="'+a.v+'"');break;case"d":a.w||(a.w=eb(a.z||j[14],eN(eW(a.v)))),t(n,0,a.w,"V");break;default:t(n,1,0,"")}}}return t(n,-1,0,"EOD"),n.join("\r\n")})}}(),nr=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return rx(function(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){var c=r[s].trim().split(":");if("cell"===c[0]){var o=rb(c[1]);if(i.length<=o.r)for(a=i.length;a<=o.r;++a)i[a]||(i[a]=[]);switch(a=o.r,n=o.c,c[2]){case"t":i[a][n]=c[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[a][n]=+c[3];break;case"vtf":var l=c[c.length-1];case"vtc":"nl"===c[3]?i[a][n]=!!+c[4]:i[a][n]=+c[4],"vtf"==c[2]&&(i[a][n]=[i[a][n],l])}}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}(e,t),t)}var r="--SocialCalcSpreadsheetControlSave\nContent-type: text/plain; charset=UTF-8\n";return{to_workbook:function(e,r){return ry(t(e,r),r)},to_sheet:t,from_sheet:function(t){return["socialcalc:version:1.5\nMIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave",r,"# SocialCalc Spreadsheet Control Save\npart:sheet",r,function(t){if(!t||!t["!ref"])return"";for(var r,a=[],n=[],s="",i=rw(t["!ref"]),c=Array.isArray(t),o=i.s.r;o<=i.e.r;++o)for(var l=i.s.c;l<=i.e.c;++l)if(s=rT({r:o,c:l}),(r=c?(t[o]||[])[l]:t[s])&&null!=r.v&&"z"!==r.t){switch(n=["cell",s,"t"],r.t){case"s":case"str":n.push(e(r.v));break;case"n":r.f?(n[2]="vtf",n[3]="n",n[4]=r.v,n[5]=e(r.f)):(n[2]="v",n[3]=r.v);break;case"b":n[2]="vt"+(r.f?"f":"c"),n[3]="nl",n[4]=r.v?"1":"0",n[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var f=eN(eW(r.v));n[2]="vtc",n[3]="nd",n[4]=""+f,n[5]=r.w||eb(r.z||j[14],f);break;case"e":continue}a.push(n.join(":"))}return a.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),a.push("valueformat:1:text-wiki"),a.join("\n")}(t),"--SocialCalcSpreadsheetControlSave--"].join("\n")}}}(),na=function(){function e(e,t,r,a,n){n.raw?t[r][a]=e:""===e||("TRUE"===e?t[r][a]=!0:"FALSE"===e?t[r][a]=!1:isNaN(eG(e))?isNaN(e$(e).getDate())?t[r][a]=e:t[r][a]=eW(e):t[r][a]=eG(e))}var t={44:",",9:"	",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function a(e){for(var a={},n=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?n=!n:!n&&i in t&&(a[i]=(a[i]||0)+1);for(s in i=[],a)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);if(!i.length)for(s in a=r)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);return i.sort(function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]}),t[i.pop()[1]]||44}function s(t,r){var s,i="",c="string"==r.type?[0,0,0,0]:iK(t,r);switch(r.type){case"base64":i=y(t);break;case"binary":case"string":i=t;break;case"buffer":i=65001==r.codepage?t.toString("utf8"):r.codepage&&void 0!==n?n.utils.decode(r.codepage,t):_&&o.isBuffer(t)?t.toString("binary"):N(t);break;case"array":i=eH(t);break;default:throw Error("Unrecognized type "+r.type)}return(239==c[0]&&187==c[1]&&191==c[2]?i=tb(i.slice(3)):"string"!=r.type&&"buffer"!=r.type&&65001==r.codepage?i=tb(i):"binary"==r.type&&void 0!==n&&r.codepage&&(i=n.utils.decode(r.codepage,n.utils.encode(28591,i))),"socialcalc:version:"==i.slice(0,19))?nr.to_sheet("string"==r.type?i:tb(i),r):(s=i,!(r&&r.PRN)||r.FS||"sep="==s.slice(0,4)||s.indexOf("	")>=0||s.indexOf(",")>=0||s.indexOf(";")>=0?function(e,t){var r,n,s=t||{},i="",c=s.dense?[]:{},o={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(i=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(i=e.charAt(4),e=e.slice(6)):i=a(e.slice(0,1024)):i=s&&s.FS?s.FS:a(e.slice(0,1024));var l=0,f=0,h=0,u=0,d=0,p=i.charCodeAt(0),m=!1,g=0,v=e.charCodeAt(0);e=e.replace(/\r\n/mg,"\n");var b=null!=s.dateNF?RegExp("^"+("number"==typeof(r=s.dateNF)?j[r]:r).replace(ek,"(\\d+)")+"$"):null;function T(){var t=e.slice(u,d),r={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)r.t="z";else if(s.raw)r.t="s",r.v=t;else if(0===t.trim().length)r.t="s",r.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(r.t="s",r.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(r.t="n",r.f=t.slice(1)):(r.t="s",r.v=t);else if("TRUE"==t)r.t="b",r.v=!0;else if("FALSE"==t)r.t="b",r.v=!1;else if(isNaN(h=eG(t)))if(!isNaN(e$(t).getDate())||b&&t.match(b)){r.z=s.dateNF||j[14];var a,n,i,m,T,w,E,S,k,A,y=0;b&&t.match(b)&&(a=s.dateNF,n=t.match(b)||[],i=-1,m=-1,T=-1,w=-1,E=-1,S=-1,(a.match(ek)||[]).forEach(function(e,t){var r=parseInt(n[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":i=r;break;case"d":T=r;break;case"h":w=r;break;case"s":S=r;break;case"m":w>=0?E=r:m=r}}),S>=0&&-1==E&&m>=0&&(E=m,m=-1),7==(k=(""+(i>=0?i:new Date().getFullYear())).slice(-4)+"-"+("00"+(m>=1?m:1)).slice(-2)+"-"+("00"+(T>=1?T:1)).slice(-2)).length&&(k="0"+k),8==k.length&&(k="20"+k),A=("00"+(w>=0?w:0)).slice(-2)+":"+("00"+(E>=0?E:0)).slice(-2)+":"+("00"+(S>=0?S:0)).slice(-2),t=-1==w&&-1==E&&-1==S?k:-1==i&&-1==m&&-1==T?A:k+"T"+A,y=1),s.cellDates?(r.t="d",r.v=eW(t,y)):(r.t="n",r.v=eN(eW(t,y))),!1!==s.cellText&&(r.w=eb(r.z,r.v instanceof Date?eN(r.v):r.v)),s.cellNF||delete r.z}else r.t="s",r.v=t;else r.t="n",!1!==s.cellText&&(r.w=t),r.v=h;if("z"==r.t||(s.dense?(c[l]||(c[l]=[]),c[l][f]=r):c[rT({c:f,r:l})]=r),u=d+1,v=e.charCodeAt(u),o.e.c<f&&(o.e.c=f),o.e.r<l&&(o.e.r=l),g==p)++f;else if(f=0,++l,s.sheetRows&&s.sheetRows<=l)return!0}e:for(;d<e.length;++d)switch(g=e.charCodeAt(d)){case 34:34===v&&(m=!m);break;case p:case 10:case 13:if(!m&&T())break e}return d-u>0&&T(),c["!ref"]=rE(o),c}(s,r):rx(function(t,r){var a=r||{},n=[];if(!t||0===t.length)return n;for(var s=t.split(/[\r\n]/),i=s.length-1;i>=0&&0===s[i].length;)--i;for(var c=10,o=0,l=0;l<=i;++l)-1==(o=s[l].indexOf(" "))?o=s[l].length:o++,c=Math.max(c,o);for(l=0;l<=i;++l){n[l]=[];var f=0;for(e(s[l].slice(0,c).trim(),n,l,f,a),f=1;f<=(s[l].length-c)/10+1;++f)e(s[l].slice(c+(f-1)*10,c+10*f).trim(),n,l,f,a)}return a.sheetRows&&(n=n.slice(0,a.sheetRows)),n}(s,r),r))}return{to_workbook:function(e,t){return ry(s(e,t),t)},to_sheet:s,from_sheet:function(e){for(var t,r=[],a=rS(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){for(var i=[],c=a.s.c;c<=a.e.c;++c){var o=rT({r:s,c:c});if(!(t=n?(e[s]||[])[c]:e[o])||null==t.v){i.push("          ");continue}for(var l=(t.w||(rA(t),t.w)||"").slice(0,10);l.length<10;)l+=" ";i.push(l+(0===c?" ":""))}r.push(i.join(""))}return r.join("\n")}}}(),nn=function(){function e(e,t,r){if(e){rn(e,e.l||0);for(var a=r.Enum||h;e.l<e.length;){var n=e.read_shift(2),s=a[n]||a[65535],i=e.read_shift(2),c=e.l+i,o=s.f&&s.f(e,i,r);if(e.l=c,t(o,s,n))return}}}function t(t,r){if(!t)return t;var a=r||{},n=a.dense?[]:{},s="Sheet1",i="",c=0,o={},l=[],f=[],d={s:{r:0,c:0},e:{r:0,c:0}},p=a.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw Error("Unsupported Works 3 for Mac file");if(2==t[2])a.Enum=h,e(t,function(e,t,r){switch(r){case 0:a.vers=e,e>=4096&&(a.qpro=!0);break;case 6:d=e;break;case 204:e&&(i=e);break;case 222:i=e;break;case 15:case 51:a.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&(112&e[2])==112&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=a.dateNF||j[14],a.cellDates&&(e[1].t="d",e[1].v=eL(e[1].v))),a.qpro&&e[3]>c&&(n["!ref"]=rE(d),o[s]=n,l.push(s),n=a.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},c=e[3],s=i||"Sheet"+(c+1),i="");var f=a.dense?(n[e[0].r]||[])[e[0].c]:n[rT(e[0])];if(f){f.t=e[1].t,f.v=e[1].v,null!=e[1].z&&(f.z=e[1].z),null!=e[1].f&&(f.f=e[1].f);break}a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[rT(e[0])]=e[1]}},a);else if(26==t[2]||14==t[2])a.Enum=u,14==t[2]&&(a.qpro=!0,t.l=0),e(t,function(e,t,r){switch(r){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>c&&(n["!ref"]=rE(d),o[s]=n,l.push(s),n=a.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},s="Sheet"+((c=e[3])+1)),p>0&&e[0].r>=p)break;a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[rT(e[0])]=e[1],d.e.c<e[0].c&&(d.e.c=e[0].c),d.e.r<e[0].r&&(d.e.r=e[0].r);break;case 27:e[14e3]&&(f[e[14e3][0]]=e[14e3][1]);break;case 1537:f[e[0]]=e[1],e[0]==c&&(s=e[1])}},a);else throw Error("Unrecognized LOTUS BOF "+t[2]);if(n["!ref"]=rE(d),o[i||s]=n,l.push(i||s),!f.length)return{SheetNames:l,Sheets:o};for(var m={},g=[],v=0;v<f.length;++v)o[l[v]]?(g.push(f[v]||l[v]),m[f[v]]=o[f[v]]||o[l[v]]):(g.push(f[v]),m[f[v]]={"!ref":"A1"});return{SheetNames:g,Sheets:m}}function r(e,t,r){var a=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(a[0].c=e.read_shift(1),a[3]=e.read_shift(1),a[0].r=e.read_shift(2),e.l+=2):(a[2]=e.read_shift(1),a[0].c=e.read_shift(2),a[0].r=e.read_shift(2)),a}function a(e,t,a){var n=e.l+t,s=r(e,t,a);if(s[1].t="s",20768==a.vers){e.l++;var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return a.qpro&&e.l++,s[1].v=e.read_shift(n-e.l,"cstr"),s}function n(e,t,r){var a=32768&t;return t&=-32769,t=(a?e:0)+(t>=8192?t-16384:t),(a?"":"$")+(r?rv(t):rm(t))}var s={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},i=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function c(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function o(e,t){var r=c(e,t),a=e.read_shift(4),n=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===a&&0xc0000000===n?(r[1].t="e",r[1].v=15):0===a&&0xd0000000===n?(r[1].t="e",r[1].v=42):r[1].v=0,r;var i=32768&s;return s=(32767&s)-16446,r[1].v=(1-2*i)*(n*Math.pow(2,s+32)+a*Math.pow(2,s)),r}function l(e,t){var r=c(e,t),a=e.read_shift(8,"f");return r[1].v=a,r}function f(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}var h={0:{n:"BOF",f:a_},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var a={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(a.s.c=e.read_shift(1),e.l++,a.s.r=e.read_shift(2),a.e.c=e.read_shift(1),e.l++,a.e.r=e.read_shift(2)):(a.s.c=e.read_shift(2),a.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),a.e.c=e.read_shift(2),a.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==a.s.c&&(a.s.c=a.e.c=a.s.r=a.e.r=0)),a}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,a){var n=r(e,t,a);return n[1].v=e.read_shift(2,"i"),n}},14:{n:"NUMBER",f:function(e,t,a){var n=r(e,t,a);return n[1].v=e.read_shift(8,"f"),n}},15:{n:"LABEL",f:a},16:{n:"FORMULA",f:function(e,t,a){var c=e.l+t,o=r(e,t,a);if(o[1].v=e.read_shift(8,"f"),a.qpro)e.l=c;else{var l=e.read_shift(2);(function(e,t){rn(e,0);for(var r=[],a=0,c="",o="",l="",f="";e.l<e.length;){var h=e[e.l++];switch(h){case 0:r.push(e.read_shift(8,"f"));break;case 1:o=n(t[0].c,e.read_shift(2),!0),c=n(t[0].r,e.read_shift(2),!1),r.push(o+c);break;case 2:var u=n(t[0].c,e.read_shift(2),!0),d=n(t[0].r,e.read_shift(2),!1);o=n(t[0].c,e.read_shift(2),!0),c=n(t[0].r,e.read_shift(2),!1),r.push(u+d+":"+o+c);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var p="";h=e[e.l++];)p+=String.fromCharCode(h);r.push('"'+p.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:f=r.pop(),l=r.pop(),r.push(["AND","OR"][h-20]+"("+l+","+f+")");break;default:if(h<32&&i[h])f=r.pop(),l=r.pop(),r.push(l+i[h]+f);else if(s[h]){if(69==(a=s[h][1])&&(a=e[e.l++]),a>r.length)return void console.error("WK1 bad formula parse 0x"+h.toString(16)+":|"+r.join("|")+"|");var m=r.slice(-a);r.length-=a,r.push(s[h][0]+"("+m.join(",")+")")}else if(h<=7)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=24)return console.error("WK1 unsupported op "+h.toString(16));else if(h<=30)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=115)return console.error("WK1 unsupported function opcode "+h.toString(16));else return console.error("WK1 unrecognized opcode "+h.toString(16))}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")})(e.slice(e.l,e.l+l),o),e.l+=l}return o}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:a},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:f},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var a="";a.length<r;)a+=String.fromCharCode(e[e.l++]);return a}},65535:{n:""}},u={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=c(e,t);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:o},24:{n:"NUMBER18",f:function(e,t){var r=c(e,t);r[1].v=e.read_shift(2);var a=r[1].v>>1;if(1&r[1].v)switch(7&a){case 0:a=(a>>3)*5e3;break;case 1:a=(a>>3)*500;break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64}return r[1].v=a,r}},25:{n:"FORMULA19",f:function(e,t){var r=o(e,14);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},a=e.l+t;e.l<a;){var n=e.read_shift(2);if(14e3==n){for(r[n]=[0,""],r[n][0]=e.read_shift(2);e[e.l];)r[n][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=c(e,t),a=e.read_shift(4);return r[1].v=a>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:l},40:{n:"FORMULA28",f:function(e,t){var r=l(e,14);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:f},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var a=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[a,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r,a,n,s,i=t||{};if(+i.codepage>=0&&g(+i.codepage),"string"==i.type)throw Error("Cannot write WK1 to JS string");var c=ro(),o=rS(e["!ref"]),l=Array.isArray(e),f=[];im(c,0,(r=1030,(a=ri(2)).write_shift(2,1030),a)),im(c,6,(n=o,(s=ri(8)).write_shift(2,n.s.c),s.write_shift(2,n.s.r),s.write_shift(2,n.e.c),s.write_shift(2,n.e.r),s));for(var h=Math.min(o.e.r,8191),u=o.s.r;u<=h;++u)for(var d=rm(u),p=o.s.c;p<=o.e.c;++p){u===o.s.r&&(f[p]=rv(p));var m=f[p]+d,v=l?(e[u]||[])[p]:e[m];v&&"z"!=v.t&&("n"==v.t?(0|v.v)==v.v&&v.v>=-32768&&v.v<=32767?im(c,13,function(e,t,r){var a=ri(7);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(2,r,"i"),a}(u,p,v.v)):im(c,14,function(e,t,r){var a=ri(13);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(8,r,"f"),a}(u,p,v.v)):im(c,15,function(e,t,r){var a=ri(7+r.length);a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(1,39);for(var n=0;n<a.length;++n){var s=r.charCodeAt(n);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}(u,p,rA(v).slice(0,239))))}return im(c,1),c.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&g(+r.codepage),"string"==r.type)throw Error("Cannot write WK3 to JS string");var a=ro();im(a,0,function(e){var t=ri(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,a=0,n=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],c=e.Sheets[i];if(c&&c["!ref"]){++n;var o=rw(c["!ref"]);r<o.e.r&&(r=o.e.r),a<o.e.c&&(a=o.e.c)}}return r>8191&&(r=8191),t.write_shift(2,r),t.write_shift(1,n),t.write_shift(1,a),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var n=0,s=0;n<e.SheetNames.length;++n)(e.Sheets[e.SheetNames[n]]||{})["!ref"]&&im(a,27,function(e,t){var r=ri(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);r[r.l++]=n>127?95:n}return r[r.l++]=0,r}(e.SheetNames[n],s++));var i=0;for(n=0;n<e.SheetNames.length;++n){var c=e.Sheets[e.SheetNames[n]];if(c&&c["!ref"]){for(var o=rS(c["!ref"]),l=Array.isArray(c),f=[],h=Math.min(o.e.r,8191),u=o.s.r;u<=h;++u)for(var d=rm(u),p=o.s.c;p<=o.e.c;++p){u===o.s.r&&(f[p]=rv(p));var m=f[p]+d,v=l?(c[u]||[])[p]:c[m];v&&"z"!=v.t&&("n"==v.t?im(a,23,function(e,t,r,a){var n=ri(14);if(n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),0==a)return n.write_shift(4,0),n.write_shift(4,0),n.write_shift(2,65535),n;var s=0,i=0,c=0,o=0;return a<0&&(s=1,a=-a),i=0|Math.log2(a),a/=Math.pow(2,i-31),(0x80000000&(o=a>>>0))==0&&(a/=2,++i,o=a>>>0),a-=o,o|=0x80000000,o>>>=0,a*=0x100000000,c=a>>>0,n.write_shift(4,c),n.write_shift(4,o),i+=16383+32768*!!s,n.write_shift(2,i),n}(u,p,i,v.v)):im(a,22,function(e,t,r,a){var n=ri(6+a.length);n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),n.write_shift(1,39);for(var s=0;s<a.length;++s){var i=a.charCodeAt(s);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}(u,p,i,rA(v).slice(0,239))))}++i}}return im(a,1),a.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(R(y(e)),r);case"binary":return t(R(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}(),ns=function(){var e=tw("t"),t=tw("rPr");function r(r){var a=r.match(e);if(!a)return{t:"s",v:""};var n={t:"s",v:ts(a[1])},s=r.match(t);return s&&(n.s=function(e){var t={},r=e.match(e7),a=0,n=!1;if(r)for(;a!=r.length;++a){var s=tt(r[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==s.val)break;t.cp=p[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=s.val;break;case"<sz":t.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting"}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if("0"==s.val)break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if("0"==s.val)break;case"<i>":case"<i/>":t.i=1;break;case"</i>":case"<color>":case"<color/>":case"</color>":case"<family>":case"<family/>":case"</family>":case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":case"<scheme":case"<scheme>":case"<scheme/>":case"</scheme>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<color":s.rgb&&(t.color=s.rgb.slice(2,8));break;case"<family":t.family=s.val;break;case"<vertAlign":t.valign=s.val;break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(47!==s[0].charCodeAt(1)&&!n)throw Error("Unrecognized rich format "+s[0])}}return t}(s[1])),n}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(e){return e.replace(a,"").split(n).map(r).filter(function(e){return e.v})}}(),ni=function(){var e=/(\r\n|\n)/g;function t(t){var r,a,n,s,i,c=[[],t.v,[]];return t.v?(t.s&&(r=t.s,a=c[0],n=c[2],s=[],r.u&&s.push("text-decoration: underline;"),r.uval&&s.push("text-underline-style:"+r.uval+";"),r.sz&&s.push("font-size:"+r.sz+"pt;"),r.outline&&s.push("text-effect: outline;"),r.shadow&&s.push("text-shadow: auto;"),a.push('<span style="'+s.join("")+'">'),r.b&&(a.push("<b>"),n.push("</b>")),r.i&&(a.push("<i>"),n.push("</i>")),r.strike&&(a.push("<s>"),n.push("</s>")),"superscript"==(i=r.valign||"")||"super"==i?i="sup":"subscript"==i&&(i="sub"),""!=i&&(a.push("<"+i+">"),n.push("</"+i+">")),n.push("</span>")),c[0].join("")+c[1].replace(e,"<br/>")+c[2].join("")):""}return function(e){return e.map(t).join("")}}(),nc=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,no=/<(?:\w+:)?r>/,nl=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function nf(e,t){var r=!t||t.cellHTML,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=ts(tb(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=tb(e),r&&(a.h=th(a.t))):e.match(no)&&(a.r=tb(e),a.t=ts(tb((e.replace(nl,"").match(nc)||[]).join("").replace(e7,""))),r&&(a.h=ni(ns(a.r)))),a):{t:""}}var nh=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,nu=/<(?:\w+:)?(?:si|sstItem)>/g,nd=/<\/(?:\w+:)?(?:si|sstItem)>/,np=/^\s|\s$|[\t\n\r]/;function nm(e,t){if(!t.bookSST)return"";var r=[e5];r[r.length]=tO("sst",null,{xmlns:tF[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(null!=e[a]){var n=e[a],s="<si>";n.r?s+=n.r:(s+="<t",n.t||(n.t=""),n.t.match(np)&&(s+=' xml:space="preserve"'),s+=">"+to(n.t)+"</t>"),s+="</si>",r[r.length]=s}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function ng(e){if(void 0!==n)return n.utils.encode(u,e);for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function nv(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function nb(e,t){var r=e.l+t,a={};a.Flags=63&e.read_shift(4),e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=36==a.Flags;break;case 26625:n=4==a.Flags;break;case 0:n=16==a.Flags||4==a.Flags||36==a.Flags;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}function nT(e,t){var r={},a=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,a),e.l=a,r}function nw(e){var t,r,a=0,n=ng(e),s=n.length+1;for(r=1,(t=C(s))[0]=n.length;r!=s;++r)t[r]=n[r-1];for(r=s-1;r>=0;--r)a=((16384&a)!=0|a<<1&32767)^t[r];return 52811^a}var nE=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(e,t){var r;return((r=e^t)/2|128*r)&255},n=function(e){for(var a=t[e.length-1],n=104,s=e.length-1;s>=0;--s)for(var i=e[s],c=0;7!=c;++c)64&i&&(a^=r[n]),i*=2,--n;return a};return function(t){for(var r,s,i,c=ng(t),o=n(c),l=c.length,f=C(16),h=0;16!=h;++h)f[h]=0;for((1&l)==1&&(r=o>>8,f[l]=a(e[0],r),--l,r=255&o,s=c[c.length-1],f[l]=a(s,r));l>0;)--l,r=o>>8,f[l]=a(c[l],r),--l,r=255&o,f[l]=a(c[l],r);for(l=15,i=15-c.length;i>0;)r=o>>8,f[l]=a(e[i],r),--l,--i,r=255&o,f[l]=a(c[l],r),--l,--i;return f}}(),nS=function(e,t,r,a,n){var s,i;for(n||(n=t),a||(a=nE(e)),s=0;s!=t.length;++s)i=((i=t[s]^a[r])>>5|i<<3)&255,n[s]=i,++r;return[n,r,a]},nk=function(e){var t=0,r=nE(e);return function(e){var a=nS("",e,t,r);return t=a[1],a[0]}},nA=function(){function e(e,r){switch(r.type){case"base64":return t(y(e),r);case"binary":return t(e,r);case"buffer":return t(_&&o.isBuffer(e)?e.toString("binary"):N(e),r);case"array":return t(eH(e),r)}throw Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},a=e.match(/\\trowd.*?\\row\b/g);if(!a.length)throw Error("RTF missing table");var n={s:{c:0,r:0},e:{c:0,r:a.length-1}};return a.forEach(function(e,t){Array.isArray(r)&&(r[t]=[]);for(var a,s=/\\\w+\b/g,i=0,c=-1;a=s.exec(e);){if("\\cell"===a[0]){var o=e.slice(i,s.lastIndex-a[0].length);if(" "==o[0]&&(o=o.slice(1)),++c,o.length){var l={v:o,t:"s"};Array.isArray(r)?r[t][c]=l:r[rT({r:t,c:c})]=l}}i=s.lastIndex}c>n.e.c&&(n.e.c=c)}),r["!ref"]=rE(n),r}return{to_workbook:function(t,r){return ry(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],a=rS(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){r.push("\\trowd\\trautofit1");for(var i=a.s.c;i<=a.e.c;++i)r.push("\\cellx"+(i+1));for(r.push("\\pard\\intbl"),i=a.s.c;i<=a.e.c;++i){var c=rT({r:s,c:i});(t=n?(e[s]||[])[i]:e[c])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(rA(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function ny(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function n_(e,t){if(0===t)return e;var r,a=function(e){var t=e[0]/255,r=e[1]/255,a=e[2]/255,n=Math.max(t,r,a),s=Math.min(t,r,a),i=n-s;if(0===i)return[0,0,t];var c=0,o=0,l=n+s;switch(o=i/(l>1?2-l:l),n){case t:c=((r-a)/i+6)%6;break;case r:c=(a-t)/i+2;break;case a:c=(t-r)/i+4}return[c/6,o,l/2]}([parseInt((r=e.slice(+("#"===e[0])).slice(0,6)).slice(0,2),16),parseInt(r.slice(2,4),16),parseInt(r.slice(4,6),16)]);return t<0?a[2]=a[2]*(1+t):a[2]=1-(1-a[2])*(1-t),ny(function(e){var t,r=e[0],a=e[1],n=e[2],s=2*a*(n<.5?n:1-n),i=n-s/2,c=[i,i,i],o=6*r;if(0!==a)switch(0|o){case 0:case 6:t=s*o,c[0]+=s,c[1]+=t;break;case 1:t=s*(2-o),c[0]+=t,c[1]+=s;break;case 2:t=s*(o-2),c[1]+=s,c[2]+=t;break;case 3:t=s*(4-o),c[1]+=t,c[2]+=s;break;case 4:t=s*(o-4),c[2]+=s,c[0]+=t;break;case 5:t=s*(6-o),c[2]+=t,c[0]+=s}for(var l=0;3!=l;++l)c[l]=Math.round(255*c[l]);return c}(a))}var nx=6;function nC(e){return Math.floor((e+Math.round(128/nx)/256)*nx)}function nO(e){return Math.floor((e-5)/nx*100+.5)/100}function nR(e){return Math.round((e*nx+5)/nx*256)/256}function nI(e){return nR(nO(nC(e)))}function nN(e){var t=Math.abs(e-nI(e)),r=nx;if(t>.005)for(nx=1;nx<15;++nx)Math.abs(e-nI(e))<=t&&(t=Math.abs(e-nI(e)),r=nx);nx=r}function nD(e){e.width?(e.wpx=nC(e.width),e.wch=nO(e.wpx),e.MDW=nx):e.wpx?(e.wch=nO(e.wpx),e.width=nR(e.wch),e.MDW=nx):"number"==typeof e.wch&&(e.width=nR(e.wch),e.wpx=nC(e.width),e.MDW=nx),e.customWidth&&delete e.customWidth}function nF(e){return 96*e/96}function nP(e){return 96*e/96}var nL={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"},nM=["numFmtId","fillId","fontId","borderId","xfId"],nU=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"],nB=function(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,t=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,r=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,n=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(s,i,c){var o,l,f,h,u,d,m,g,v,b,T,w,E,S={};return s?((E=(s=s.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"")).match(e))&&function(e,t,r){t.NumberFmt=[];for(var a=ex(j),n=0;n<a.length;++n)t.NumberFmt[a[n]]=j[a[n]];var s=e[0].match(e7);if(s)for(n=0;n<s.length;++n){var i=tt(s[n]);switch(tr(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":case"</numFmt>":break;case"<numFmt":var c=ts(tb(i.formatCode)),o=parseInt(i.numFmtId,10);if(t.NumberFmt[o]=c,o>0){if(o>392){for(o=392;o>60&&null!=t.NumberFmt[o];--o);t.NumberFmt[o]=c}eT(c,o)}break;default:if(r.WTF)throw Error("unrecognized "+i[0]+" in numFmts")}}}(E,S,c),(E=s.match(a))&&(o=E,S.Fonts=[],l={},f=!1,(o[0].match(e7)||[]).forEach(function(e){var t=tt(e);switch(tr(t[0])){case"<fonts":case"<fonts>":case"</fonts>":case"<font":case"<font>":case"<name/>":case"</name>":case"<sz/>":case"</sz>":case"<vertAlign/>":case"</vertAlign>":case"<family/>":case"</family>":case"<scheme/>":case"</scheme>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"</font>":case"<font/>":S.Fonts.push(l),l={};break;case"<name":t.val&&(l.name=tb(t.val));break;case"<b":l.bold=t.val?td(t.val):1;break;case"<b/>":l.bold=1;break;case"<i":l.italic=t.val?td(t.val):1;break;case"<i/>":l.italic=1;break;case"<u":switch(t.val){case"none":l.underline=0;break;case"single":l.underline=1;break;case"double":l.underline=2;break;case"singleAccounting":l.underline=33;break;case"doubleAccounting":l.underline=34}break;case"<u/>":l.underline=1;break;case"<strike":l.strike=t.val?td(t.val):1;break;case"<strike/>":l.strike=1;break;case"<outline":l.outline=t.val?td(t.val):1;break;case"<outline/>":l.outline=1;break;case"<shadow":l.shadow=t.val?td(t.val):1;break;case"<shadow/>":l.shadow=1;break;case"<condense":l.condense=t.val?td(t.val):1;break;case"<condense/>":l.condense=1;break;case"<extend":l.extend=t.val?td(t.val):1;break;case"<extend/>":l.extend=1;break;case"<sz":t.val&&(l.sz=+t.val);break;case"<vertAlign":t.val&&(l.vertAlign=t.val);break;case"<family":t.val&&(l.family=parseInt(t.val,10));break;case"<scheme":t.val&&(l.scheme=t.val);break;case"<charset":if("1"==t.val)break;t.codepage=p[parseInt(t.val,10)];break;case"<color":if(l.color||(l.color={}),t.auto&&(l.color.auto=td(t.auto)),t.rgb)l.color.rgb=t.rgb.slice(-6);else if(t.indexed){l.color.index=parseInt(t.indexed,10);var r=rJ[l.color.index];81==l.color.index&&(r=rJ[1]),r||(r=rJ[1]),l.color.rgb=r[0].toString(16)+r[1].toString(16)+r[2].toString(16)}else t.theme&&(l.color.theme=parseInt(t.theme,10),t.tint&&(l.color.tint=parseFloat(t.tint)),t.theme&&i.themeElements&&i.themeElements.clrScheme&&(l.color.rgb=n_(i.themeElements.clrScheme[l.color.theme].rgb,l.color.tint||0)));break;case"<AlternateContent":case"<ext":f=!0;break;case"</AlternateContent>":case"</ext>":f=!1;break;default:if(c&&c.WTF&&!f)throw Error("unrecognized "+t[0]+" in fonts")}})),(E=s.match(r))&&(h=E,S.Fills=[],u={},d=!1,(h[0].match(e7)||[]).forEach(function(e){var t=tt(e);switch(tr(t[0])){case"<fills":case"<fills>":case"</fills>":case"</fill>":case"<gradientFill>":case"<patternFill/>":case"</patternFill>":case"<bgColor/>":case"</bgColor>":case"<fgColor/>":case"</fgColor>":case"<stop":case"<stop/>":case"</stop>":case"<color":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<fill>":case"<fill":case"<fill/>":u={},S.Fills.push(u);break;case"<gradientFill":case"</gradientFill>":S.Fills.push(u),u={};break;case"<patternFill":case"<patternFill>":t.patternType&&(u.patternType=t.patternType);break;case"<bgColor":u.bgColor||(u.bgColor={}),t.indexed&&(u.bgColor.indexed=parseInt(t.indexed,10)),t.theme&&(u.bgColor.theme=parseInt(t.theme,10)),t.tint&&(u.bgColor.tint=parseFloat(t.tint)),t.rgb&&(u.bgColor.rgb=t.rgb.slice(-6));break;case"<fgColor":u.fgColor||(u.fgColor={}),t.theme&&(u.fgColor.theme=parseInt(t.theme,10)),t.tint&&(u.fgColor.tint=parseFloat(t.tint)),null!=t.rgb&&(u.fgColor.rgb=t.rgb.slice(-6));break;case"<ext":d=!0;break;case"</ext>":d=!1;break;default:if(c&&c.WTF&&!d)throw Error("unrecognized "+t[0]+" in fills")}})),(E=s.match(n))&&(m=E,S.Borders=[],g={},v=!1,(m[0].match(e7)||[]).forEach(function(e){var t=tt(e);switch(tr(t[0])){case"<borders":case"<borders>":case"</borders>":case"</border>":case"<left/>":case"<left":case"<left>":case"</left>":case"<right/>":case"<right":case"<right>":case"</right>":case"<top/>":case"<top":case"<top>":case"</top>":case"<bottom/>":case"<bottom":case"<bottom>":case"</bottom>":case"<diagonal":case"<diagonal>":case"<diagonal/>":case"</diagonal>":case"<horizontal":case"<horizontal>":case"<horizontal/>":case"</horizontal>":case"<vertical":case"<vertical>":case"<vertical/>":case"</vertical>":case"<start":case"<start>":case"<start/>":case"</start>":case"<end":case"<end>":case"<end/>":case"</end>":case"<color":case"<color>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<border":case"<border>":case"<border/>":g={},t.diagonalUp&&(g.diagonalUp=td(t.diagonalUp)),t.diagonalDown&&(g.diagonalDown=td(t.diagonalDown)),S.Borders.push(g);break;case"<ext":v=!0;break;case"</ext>":v=!1;break;default:if(c&&c.WTF&&!v)throw Error("unrecognized "+t[0]+" in borders")}})),(E=s.match(t))&&(b=E,S.CellXf=[],w=!1,(b[0].match(e7)||[]).forEach(function(e){var t=tt(e),r=0;switch(tr(t[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":case"</xf>":case"</alignment>":case"<protection":case"</protection>":case"<protection/>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<xf":case"<xf/>":for(T=t,delete T[0],r=0;r<nM.length;++r)T[nM[r]]&&(T[nM[r]]=parseInt(T[nM[r]],10));for(r=0;r<nU.length;++r)T[nU[r]]&&(T[nU[r]]=td(T[nU[r]]));if(S.NumberFmt&&T.numFmtId>392){for(r=392;r>60;--r)if(S.NumberFmt[T.numFmtId]==S.NumberFmt[r]){T.numFmtId=r;break}}S.CellXf.push(T);break;case"<alignment":case"<alignment/>":var a={};t.vertical&&(a.vertical=t.vertical),t.horizontal&&(a.horizontal=t.horizontal),null!=t.textRotation&&(a.textRotation=t.textRotation),t.indent&&(a.indent=t.indent),t.wrapText&&(a.wrapText=td(t.wrapText)),T.alignment=a;break;case"<AlternateContent":case"<ext":w=!0;break;case"</AlternateContent>":case"</ext>":w=!1;break;default:if(c&&c.WTF&&!w)throw Error("unrecognized "+t[0]+" in cellXfs")}})),S):S}}();function nW(e,t){var r,a,n,s,i,c=[e5,tO("styleSheet",null,{xmlns:tF[0],"xmlns:vt":tD.vt})];return e.SSF&&null!=(r=e.SSF,a=["<numFmts>"],[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=r[t]&&(a[a.length]=tO("numFmt",null,{numFmtId:t,formatCode:to(r[t])}))}),i=1===a.length?"":(a[a.length]="</numFmts>",a[0]=tO("numFmts",null,{count:a.length-2}).replace("/>",">"),a.join("")))&&(c[c.length]=i),c[c.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',c[c.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',c[c.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',c[c.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',n=t.cellXfs,(s=[])[s.length]=tO("cellXfs",null),n.forEach(function(e){s[s.length]=tO("xf",null,e)}),s[s.length]="</cellXfs>",(i=2===s.length?"":(s[0]=tO("cellXfs",null,{count:s.length-2}).replace("/>",">"),s.join("")))&&(c[c.length]=i),c[c.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',c[c.length]='<dxfs count="0"/>',c[c.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',c.length>2&&(c[c.length]="</styleSheet>",c[1]=c[1].replace("/>",">")),c.join("")}function nH(e,t){t||(t=ri(84)),i||(i=eO(null));var r=i[e.patternType];null==r&&(r=40),t.write_shift(4,r);var a=0;if(40!=r)for(rz({auto:1},t),rz({auto:1},t);a<12;++a)t.write_shift(4,0);else{for(;a<4;++a)t.write_shift(4,0);for(;a<12;++a)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function nV(e,t,r){return r||(r=ri(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function nz(e,t){return t||(t=ri(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var nG=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function nj(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(e7)||[]).forEach(function(e){var n=tt(e);switch(n[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===n[0].charAt(1)?(t.themeElements.clrScheme[nG.indexOf(n[0])]=a,a={}):a.name=n[0].slice(3,n[0].length-1);break;default:if(r&&r.WTF)throw Error("Unrecognized "+n[0]+" in clrScheme")}})}function n$(){}function nX(){}var nY=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,nK=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,nJ=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/,nq=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function nZ(e,t){e&&0!==e.length||(e=nQ());var r,a,n,s={};if(!(n=e.match(nq)))throw Error("themeElements not found in theme");return r=n[0],s.themeElements={},[["clrScheme",nY,nj],["fontScheme",nK,n$],["fmtScheme",nJ,nX]].forEach(function(e){if(!(a=r.match(e[1])))throw Error(e[0]+" not found in themeElements");e[2](a,s,t)}),s.raw=e,s}function nQ(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[e5];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function n1(){var e=[e5];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}var n0=1024;function n2(e,t){for(var r=[21600,21600],a=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),n=[tO("xml",null,{"xmlns:v":tP.v,"xmlns:o":tP.o,"xmlns:x":tP.x,"xmlns:mv":tP.mv}).replace(/\/>/,">"),tO("o:shapelayout",tO("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),tO("v:shapetype",[tO("v:stroke",null,{joinstyle:"miter"}),tO("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:a})];n0<1e3*e;)n0+=1e3;return t.forEach(function(e){var t=rb(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var a="gradient"==r.type?tO("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,s=tO("v:fill",a,r);++n0,n=n.concat(["<v:shape"+tC({id:"_x0000_s"+n0,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",s,tO("v:shadow",null,{on:"t",obscured:"t"}),tO("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",tx("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),tx("x:AutoFill","False"),tx("x:Row",String(t.r)),tx("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),n.push("</xml>"),n.join("")}function n4(e,t,r,a){var n,s=Array.isArray(e);t.forEach(function(t){var i=rb(t.ref);if(s?(e[i.r]||(e[i.r]=[]),n=e[i.r][i.c]):n=e[t.ref],!n){n={t:"z"},s?e[i.r][i.c]=n:e[t.ref]=n;var c=rS(e["!ref"]||"BDWGO1000001:A1");c.s.r>i.r&&(c.s.r=i.r),c.e.r<i.r&&(c.e.r=i.r),c.s.c>i.c&&(c.s.c=i.c),c.e.c<i.c&&(c.e.c=i.c);var o=rE(c);o!==e["!ref"]&&(e["!ref"]=o)}n.c||(n.c=[]);var l={a:t.author,t:t.t,r:t.r,T:r};t.h&&(l.h=t.h);for(var f=n.c.length-1;f>=0;--f){if(!r&&n.c[f].T)return;r&&!n.c[f].T&&n.c.splice(f,1)}if(r&&a){for(f=0;f<a.length;++f)if(l.a==a[f].id){l.a=a[f].name||l.a;break}}n.c.push(l)})}function n3(e){var t=[e5,tO("comments",null,{xmlns:tF[0]})],r=[];return t.push("<authors>"),e.forEach(function(e){e[1].forEach(function(e){var a=to(e.a);-1==r.indexOf(a)&&(r.push(a),t.push("<author>"+a+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))})}),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(e){var a=0,n=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?a=r.indexOf("tc="+e[1][0].ID):e[1].forEach(function(e){e.a&&(a=r.indexOf(to(e.a))),n.push(e.t||"")}),t.push('<comment ref="'+e[0]+'" authorId="'+a+'"><text>'),n.length<=1)t.push(tx("t",to(n[0]||"")));else{for(var s="Comment:\n    "+n[0]+"\n",i=1;i<n.length;++i)s+="Reply:\n    "+n[i]+"\n";t.push(tx("t",to(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}var n5=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,a,n){var s=!1,i=!1;0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1)),0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1));var c=a.length>0?0|parseInt(a,10):0,o=n.length>0?0|parseInt(n,10):0;return s?o+=t.c:--o,i?c+=t.r:--c,r+(s?"":"$")+rv(o)+(i?"":"$")+rm(c)}return function(a,n){return t=n,a.replace(e,r)}}(),n6=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,n8=function(e,t){return e.replace(n6,function(e,r,a,n,s,i){var c=rg(n)-(a?0:t.c),o=rp(i)-(s?0:t.r);return r+"R"+(0==o?"":s?o+1:"["+o+"]")+"C"+(0==c?"":a?c+1:"["+c+"]")})};function n7(e,t){return e.replace(n6,function(e,r,a,n,s,i){return r+("$"==a?a+n:rv(rg(n)+t.c))+("$"==s?s+i:rm(rp(i)+t.r))})}function n9(e){return e.replace(/_xlfn\./g,"")}function se(e){e.l+=1}function st(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function sr(e,t,r){var a=2;if(r)if(r.biff>=2&&r.biff<=5)return sa(e,t,r);else 12==r.biff&&(a=4);var n=e.read_shift(a),s=e.read_shift(a),i=st(e,2),c=st(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:c[0],cRel:c[1],rRel:c[2]}}}function sa(e){var t=st(e,2),r=st(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function sn(e,t,r){if(r&&r.biff>=2&&r.biff<=5){var a,n,s;return n=st(a=e,2),s=a.read_shift(1),{r:n[0],c:s,cRel:n[1],rRel:n[2]}}var i=e.read_shift(r&&12==r.biff?4:2),c=st(e,2);return{r:i,c:c[0],cRel:c[1],rRel:c[2]}}function ss(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function si(e){return[e.read_shift(1),e.read_shift(1)]}function sc(e,t,r){var a;return e.l+=2,[{r:e.read_shift(2),c:255&(a=e.read_shift(2)),fQuoted:!!(16384&a),cRel:a>>15,rRel:a>>15}]}function so(e){return e.l+=6,[]}function sl(e){return e.l+=2,[a_(e),1&e.read_shift(2)]}var sf=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"],sh={1:{n:"PtgExp",f:function(e,t,r){return(e.l++,r&&12==r.biff)?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:rs},3:{n:"PtgAdd",f:se},4:{n:"PtgSub",f:se},5:{n:"PtgMul",f:se},6:{n:"PtgDiv",f:se},7:{n:"PtgPower",f:se},8:{n:"PtgConcat",f:se},9:{n:"PtgLt",f:se},10:{n:"PtgLe",f:se},11:{n:"PtgEq",f:se},12:{n:"PtgGe",f:se},13:{n:"PtgGt",f:se},14:{n:"PtgNe",f:se},15:{n:"PtgIsect",f:se},16:{n:"PtgUnion",f:se},17:{n:"PtgRange",f:se},18:{n:"PtgUplus",f:se},19:{n:"PtgUminus",f:se},20:{n:"PtgPercent",f:se},21:{n:"PtgParen",f:se},22:{n:"PtgMissArg",f:se},23:{n:"PtgStr",f:function(e,t,r){return e.l++,aR(e,t-1,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,rq[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,rH(e,8)}},32:{n:"PtgArray",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[a]}},33:{n:"PtgFunc",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[sy[n],sA[n],a]}},34:{n:"PtgFuncVar",f:function(e,t,r){var a,n=e[e.l++],s=e.read_shift(1),i=r&&r.biff<=3?[88==n?-1:0,e.read_shift(1)]:[(a=e)[a.l+1]>>7,32767&a.read_shift(2)];return[s,(0===i[0]?sA:sk)[i[1]]]}},35:{n:"PtgName",f:function(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,s=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[a,0,s]}},36:{n:"PtgRef",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,sn(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,sr(e,r.biff>=2&&r.biff<=5?6:8,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[a,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:rs},40:{n:"PtgMemNoMem",f:rs},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[a]}},43:{n:"PtgAreaErr",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}},44:{n:"PtgRefN",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,function(e,t,r){var a,n,s,i,c,o=r&&r.biff?r.biff:8;if(o>=2&&o<=5){return n=(a=e).read_shift(2),s=a.read_shift(1),i=(32768&n)>>15,c=(16384&n)>>14,n&=16383,1==i&&n>=8192&&(n-=16384),1==c&&s>=128&&(s-=256),{r:n,c:s,cRel:c,rRel:i}}var l=e.read_shift(o>=12?4:2),f=e.read_shift(2),h=(16384&f)>>14,u=(32768&f)>>15;if(f&=16383,1==u)for(;l>524287;)l-=1048576;if(1==h)for(;f>8191;)f-=16384;return{r:l,c:f,cRel:h,rRel:u}}(e,0,r)]}},45:{n:"PtgAreaN",f:function(e,t,r){return[(96&e[e.l++])>>5,function(e,t,r){if(r.biff<8)return sa(e,t,r);var a=e.read_shift(12==r.biff?4:2),n=e.read_shift(12==r.biff?4:2),s=st(e,2),i=st(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}(e,t-1,r)]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){var a,n,s,i;return 5==r.biff?(n=(a=e).read_shift(1)>>>5&3,s=a.read_shift(2,"i"),a.l+=8,i=a.read_shift(2),a.l+=12,[n,s,i]):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[a,n,sn(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2,"i"),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return[a,n,sr(e,s,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[a,n]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[a,n]}},255:{}},su={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},sd={1:{n:"PtgElfLel",f:sl},2:{n:"PtgElfRw",f:sc},3:{n:"PtgElfCol",f:sc},6:{n:"PtgElfRwV",f:sc},7:{n:"PtgElfColV",f:sc},10:{n:"PtgElfRadical",f:sc},11:{n:"PtgElfRadicalS",f:so},13:{n:"PtgElfColS",f:so},15:{n:"PtgElfColSV",f:so},16:{n:"PtgElfRadicalLel",f:sl},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2),i=sf[r>>2&31];return{ixti:t,coltype:3&r,rt:i,idx:a,c:n,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},sp={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[a]}},2:{n:"PtgAttrIf",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var a=e.read_shift(r&&2==r.biff?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(r&&2==r.biff?1:2));return n}},8:{n:"PtgAttrGoto",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:ss},33:{n:"PtgAttrBaxcel",f:ss},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),si(e,2)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),si(e,2)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function sm(e,t,r,a){if(a.biff<8)return n=t,void(e.l+=n);for(var n,s,i=e.l+t,c=[],o=0;o!==r.length;++o)switch(r[o][0]){case"PtgArray":r[o][1]=function(e,t,r){var a=0,n=0;12==r.biff?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,0==--n&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var c=0;c!=n;++c)i[s][c]=function(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=aA(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=rq[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=rH(e,8);break;case 2:r[1]=aD(e,0,{biff:t>0&&t<8?2:t});break;default:throw Error("Bad SerAr: "+r[0])}return r}(e,r.biff);return i}(e,0,a),c.push(r[o][1]);break;case"PtgMemArea":r[o][2]=function(e,t,r){for(var a=e.read_shift(12==r.biff?4:2),n=[],s=0;s!=a;++s)n.push((12==r.biff?rW:aV)(e,8));return n}(e,r[o][1],a),c.push(r[o][2]);break;case"PtgExp":a&&12==a.biff&&(r[o][1][1]=e.read_shift(4),c.push(r[o][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[o][0]}return 0!=(t=i-e.l)&&c.push((s=t,void(e.l+=s))),c}function sg(e,t,r){for(var a,n,s,i=e.l+t,c=[];i!=e.l;)(t=i-e.l,n=sh[s=e[e.l]]||sh[su[s]],(24===s||25===s)&&(n=(24===s?sd:sp)[e[e.l+1]]),n&&n.f)?c.push([n.n,n.f(e,t,r)]):(a=t,e.l+=a);return c}var sv={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function sb(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=-1==a[1]?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:if(null!=r.SID)return e.SheetNames[r.SID];return"SH33TJSSAME"+e[a[0]][0];default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=-1==a[1]?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map(function(e){return e.Name}).join(";;");default:if(!e[a[0]][0][3])return"SH33TJSERR2";return n=-1==a[1]?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]}}function sT(e,t,r){var a=sb(e,t,r);return"#REF"==a?a:function(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(a,r)}function sw(e,t,r,a,n){var s,i,c,o,l=n&&n.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,b=e[0].length;v<b;++v){var T=e[0][v];switch(T[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=ez(" ",e[0][m][1][1]);break;case 1:g=ez("\r",e[0][m][1][1]);break;default:if(g="",n.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}i+=g,m=-1}h.push(i+sv[T[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push(i+":"+s);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":c=rf(T[1][1],f,n),h.push(ru(c,l));break;case"PtgRefN":c=r?rf(T[1][1],r,n):T[1][1],h.push(ru(c,l));break;case"PtgRef3d":u=T[1][1],c=rf(T[1][2],f,n),p=sT(a,u,n),h.push(p+"!"+ru(c,l));break;case"PtgFunc":case"PtgFuncVar":var w=T[1][0],E=T[1][1];w||(w=0);var S=0==(w&=127)?[]:h.slice(-w);h.length-=w,"User"===E&&(E=S.shift()),h.push(E+"("+S.join(",")+")");break;case"PtgBool":h.push(T[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(T[1]);break;case"PtgNum":h.push(String(T[1]));break;case"PtgStr":h.push('"'+T[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":o=rh(T[1][1],r?{s:r}:f,n),h.push(rd(o,n));break;case"PtgArea":o=rh(T[1][1],f,n),h.push(rd(o,n));break;case"PtgArea3d":u=T[1][1],o=T[1][2],p=sT(a,u,n),h.push(p+"!"+rd(o,n));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=T[1][2];var k=(a.names||[])[d-1]||(a[0]||[])[d],A=k?k.Name:"SH33TJSNAME"+String(d);A&&"_xlfn."==A.slice(0,6)&&!n.xlfn&&(A=A.slice(6)),h.push(A);break;case"PtgNameX":var y,_=T[1][1];if(d=T[1][2],n.biff<=5)_<0&&(_=-_),a[_]&&(y=a[_][d]);else{var x="";if(14849==((a[_]||[])[0]||[])[0]||(1025==((a[_]||[])[0]||[])[0]?a[_][d]&&a[_][d].itab>0&&(x=a.SheetNames[a[_][d].itab-1]+"!"):x=a.SheetNames[d-1]+"!"),a[_]&&a[_][d])x+=a[_][d].Name;else if(a[0]&&a[0][d])x+=a[0][d].Name;else{var C=(sb(a,_,n)||"").split(";;");C[d-1]?x=C[d-1]:x+="SH33TJSERRX"}h.push(x);break}y||(y={Name:"SH33TJSERRY"}),h.push(y.Name);break;case"PtgParen":var O="(",R=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:O=ez(" ",e[0][m][1][1])+O;break;case 3:O=ez("\r",e[0][m][1][1])+O;break;case 4:R=ez(" ",e[0][m][1][1])+R;break;case 5:R=ez("\r",e[0][m][1][1])+R;break;default:if(n.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(O+h.pop()+R);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":c={c:T[1][1],r:T[1][0]};var I={c:r.c,r:r.r};if(a.sharedf[rT(c)]){var N=a.sharedf[rT(c)];h.push(sw(N,f,I,a,n))}else{var D=!1;for(s=0;s!=a.arrayf.length;++s)if((i=a.arrayf[s],!(c.c<i[0].s.c)&&!(c.c>i[0].e.c))&&!(c.r<i[0].s.r)&&!(c.r>i[0].e.r)){h.push(sw(i[1],f,I,a,n)),D=!0;break}D||h.push(T[1])}break;case"PtgArray":h.push("{"+function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],s=0;s<a.length;++s){var i=a[s];i?2===i[0]?n.push('"'+i[1].replace(/"/g,'""')+'"'):n.push(i[1]):n.push("")}t.push(n.join(","))}return t.join(";")}(T[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+T[1].idx+"[#"+T[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw Error("Unsupported ELFs");default:throw Error("Unrecognized Formula Token: "+String(T))}var F=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=n.biff&&m>=0&&-1==F.indexOf(e[0][v][0])){T=e[0][m];var P=!0;switch(T[1][0]){case 4:P=!1;case 0:g=ez(" ",T[1][1]);break;case 5:P=!1;case 1:g=ez("\r",T[1][1]);break;default:if(g="",n.WTF)throw Error("Unexpected PtgAttrSpaceType "+T[1][0])}h.push((P?g:"")+h.pop()+(P?"":g)),m=-1}}if(h.length>1&&n.WTF)throw Error("bad formula stack");return h[0]}function sE(e,t,r){var a=e.l+t,n=aB(e,6);2==r.biff&&++e.l;var s=function(e){var t;if(65535!==t3(e,e.l+6))return[rH(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=1===e[e.l+2],e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}(e,8),i=e.read_shift(1);2!=r.biff&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var c=function(e,t,r){var a,n,s=e.l+t,i=2==r.biff?1:2,c=e.read_shift(i);if(65535==c)return[[],(a=t-2,void(e.l+=a))];var o=sg(e,c,r);return t!==c+i&&(n=sm(e,t-c-i,o,r)),e.l=s,[o,n]}(e,a-e.l,r);return{cell:n,val:s[0],formula:c,shared:i>>3&1,tt:s[1]}}function sS(e,t,r){var a=e.read_shift(4),n=sg(e,a,r),s=e.read_shift(4),i=s>0?sm(e,s,n,r):null;return[n,i]}var sk={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},sA={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},sy={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function s_(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(e,t){return t.replace(/\./g,"")})).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function sx(e){var t=e.split(":");return[t[0].split(".")[0],t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}var sC={},sO={},sR="undefined"!=typeof Map;function sI(e,t,r){var a=0,n=e.length;if(r){if(sR?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var s=sR?r.get(t):r[t];a<s.length;++a)if(e[s[a]].t===t)return e.Count++,s[a]}}else for(;a<n;++a)if(e[a].t===t)return e.Count++,a;return e[n]={t:t},e.Count++,e.Unique++,r&&(sR?(r.has(t)||r.set(t,[]),r.get(t).push(n)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(n))),n}function sN(e,t){var r={min:e+1,max:e+1},a=-1;return t.MDW&&(nx=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?a=nO(t.wpx):null!=t.wch&&(a=t.wch),a>-1?(r.width=nR(a),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function sD(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function sF(e,t,r){var a=r.revssf[null!=t.z?t.z:"General"],n=60,s=e.length;if(null==a&&r.ssf){for(;n<392;++n)if(null==r.ssf[n]){eT(t.z,n),r.ssf[n]=t.z,r.revssf[t.z]=a=n;break}}for(n=0;n!=s;++n)if(e[n].numFmtId===a)return n;return e[s]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}function sP(e,t,r,a,n,s){try{a.cellNF&&(e.z=j[t])}catch(e){if(a.WTF)throw e}if("z"!==e.t||a.cellStyles){if("d"===e.t&&"string"==typeof e.v&&(e.v=eW(e.v)),(!a||!1!==a.cellText)&&"z"!==e.t)try{if(null==j[t]&&eT(eS[t]||"General",t),"e"===e.t)e.w=e.w||rq[e.v];else if(0===t)if("n"===e.t)(0|e.v)===e.v?e.w=e.v.toString(10):e.w=er(e.v);else if("d"===e.t){var i=eN(e.v);(0|i)===i?e.w=i.toString(10):e.w=er(i)}else{if(void 0===e.v)return"";e.w=ea(e.v,sO)}else"d"===e.t?e.w=eb(t,eN(e.v),sO):e.w=eb(t,e.v,sO)}catch(e){if(a.WTF)throw e}if(a.cellStyles&&null!=r)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=n_(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=n_(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(e){if(a.WTF&&s.Fills)throw e}}}var sL=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,sM=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,sU=/<(?:\w:)?hyperlink [^>]*>/mg,sB=/"(\w*:\w*)"/,sW=/<(?:\w:)?col\b[^>]*[\/]?>/g,sH=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,sV=/<(?:\w:)?pageMargins[^>]*\/>/g,sz=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,sG=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,sj=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function s$(e,t,r,a){var n=tt(e);r.Sheets[a]||(r.Sheets[a]={}),n.codeName&&(r.Sheets[a].CodeName=ts(tb(n.codeName)))}var sX=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/,sY=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,s=tw("v"),i=tw("f");return function(c,o,l,f,h,u){for(var d,p,m,g,v,b=0,T="",w=[],E=[],S=0,k=0,A=0,y="",_=0,x=0,C=0,O=0,R=Array.isArray(u.CellXf),I=[],N=[],D=Array.isArray(o),F=[],P={},L=!1,M=!!l.sheetStubs,U=c.split(t),B=0,W=U.length;B!=W;++B){var H=(T=U[B].trim()).length;if(0!==H){var V=0;t:for(b=0;b<H;++b)switch(T[b]){case">":if("/"!=T[b-1]){++b;break t}if(l&&l.cellStyles){if(_=null!=(p=tt(T.slice(V,b),!0)).r?parseInt(p.r,10):_+1,x=-1,l.sheetRows&&l.sheetRows<_)continue;P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=nP(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(F[_-1]=P)}break;case"<":V=b}if(V>=b)break;if(_=null!=(p=tt(T.slice(V,b),!0)).r?parseInt(p.r,10):_+1,x=-1,!l.sheetRows||!(l.sheetRows<_)){f.s.r>_-1&&(f.s.r=_-1),f.e.r<_-1&&(f.e.r=_-1),l&&l.cellStyles&&(P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=nP(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(F[_-1]=P)),w=T.slice(b).split(e);for(var z=0;z!=w.length&&"<"==w[z].trim().charAt(0);++z);for(b=0,w=w.slice(z);b!=w.length;++b)if(0!==(T=w[b].trim()).length){if(E=T.match(r),S=b,k=0,A=0,T="<c "+("<"==T.slice(0,1)?">":"")+T,null!=E&&2===E.length){for(k=0,S=0,y=E[1];k!=y.length&&!((A=y.charCodeAt(k)-64)<1)&&!(A>26);++k)S=26*S+A;x=--S}else++x;for(k=0;k!=T.length&&62!==T.charCodeAt(k);++k);if(++k,(p=tt(T.slice(0,k),!0)).r||(p.r=rT({r:_-1,c:x})),y=T.slice(k),d={t:""},null!=(E=y.match(s))&&""!==E[1]&&(d.v=ts(E[1])),l.cellFormula){if(null!=(E=y.match(i))&&""!==E[1]){if(d.f=ts(tb(E[1])).replace(/\r\n/g,"\n"),l.xlfn||(d.f=n9(d.f)),E[0].indexOf('t="array"')>-1)d.F=(y.match(n)||[])[1],d.F.indexOf(":")>-1&&I.push([rS(d.F),d.F]);else if(E[0].indexOf('t="shared"')>-1){g=tt(E[0]);var G=ts(tb(E[1]));l.xlfn||(G=n9(G)),N[parseInt(g.si,10)]=[g,G,p.r]}}else(E=y.match(/<f[^>]*\/>/))&&N[(g=tt(E[0])).si]&&(d.f=function(e,t,r){var a=rw(t).s,n=rb(r);return n7(e,{r:n.r-a.r,c:n.c-a.c})}(N[g.si][1],N[g.si][2],p.r));var X=rb(p.r);for(k=0;k<I.length;++k)X.r>=I[k][0].s.r&&X.r<=I[k][0].e.r&&X.c>=I[k][0].s.c&&X.c<=I[k][0].e.c&&(d.F=I[k][1])}if(null==p.t&&void 0===d.v)if(d.f||d.F)d.v=0,d.t="n";else{if(!M)continue;d.t="z"}else d.t=p.t||"n";switch(f.s.c>x&&(f.s.c=x),f.e.c<x&&(f.e.c=x),d.t){case"n":if(""==d.v||null==d.v){if(!M)continue;d.t="z"}else d.v=parseFloat(d.v);break;case"s":if(void 0===d.v){if(!M)continue;d.t="z"}else m=sC[parseInt(d.v,10)],d.v=m.t,d.r=m.r,l.cellHTML&&(d.h=m.h);break;case"str":d.t="s",d.v=null!=d.v?tb(d.v):"",l.cellHTML&&(d.h=th(d.v));break;case"inlineStr":E=y.match(a),d.t="s",null!=E&&(m=nf(E[1]))?(d.v=m.t,l.cellHTML&&(d.h=m.h)):d.v="";break;case"b":d.v=td(d.v);break;case"d":l.cellDates?d.v=eW(d.v,1):(d.v=eN(eW(d.v,1)),d.t="n");break;case"e":l&&!1===l.cellText||(d.w=d.v),d.v=rZ[d.v]}if(C=O=0,v=null,R&&void 0!==p.s&&null!=(v=u.CellXf[p.s])&&(null!=v.numFmtId&&(C=v.numFmtId),l.cellStyles&&null!=v.fillId&&(O=v.fillId)),sP(d,C,O,l,h,u),l.cellDates&&R&&"n"==d.t&&em(j[C])&&(d.t="d",d.v=eL(d.v)),p.cm&&l.xlmeta){var Y=(l.xlmeta.Cell||[])[p.cm-1];Y&&"XLDAPR"==Y.type&&(d.D=!0)}if(D){var K=rb(p.r);o[K.r]||(o[K.r]=[]),o[K.r][K.c]=d}else o[p.r]=d}}}}F.length>0&&(o["!rows"]=F)}}();function sK(e,t,r,a){var n,s=[e5,tO("worksheet",null,{xmlns:tF[0],"xmlns:r":tD.r})],i=r.SheetNames[e],c=0,o="",l=r.Sheets[i];null==l&&(l={});var f=l["!ref"]||"A1",h=rS(f);if(h.e.c>16383||h.e.r>1048575){if(t.WTF)throw Error("Range "+f+" exceeds format limit A1:XFD1048576");h.e.c=Math.min(h.e.c,16383),h.e.r=Math.min(h.e.c,1048575),f=rE(h)}a||(a={}),l["!comments"]=[];var u=[];!function(e,t,r,a,n){var s=!1,i={},c=null;if("xlsx"!==a.bookType&&t.vbaraw){var o=t.SheetNames[r];try{t.Workbook&&(o=t.Workbook.Sheets[r].CodeName||o)}catch(e){}s=!0,i.codeName=tT(to(o))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),c=(c||"")+tO("outlinePr",null,l)}(s||c)&&(n[n.length]=tO("sheetPr",c,i))}(l,r,e,t,s),s[s.length]=tO("dimension",null,{ref:f}),s[s.length]=(d={workbookViewId:"0"},(((r||{}).Workbook||{}).Views||[])[0]&&(d.rightToLeft=r.Workbook.Views[0].RTL?"1":"0"),tO("sheetViews",tO("sheetView",null,d),{})),t.sheetFormat&&(s[s.length]=tO("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=l["!cols"]&&l["!cols"].length>0&&(s[s.length]=function(e,t){for(var r,a=["<cols>"],n=0;n!=t.length;++n)(r=t[n])&&(a[a.length]=tO("col",null,sN(n,r)));return a[a.length]="</cols>",a.join("")}(0,l["!cols"])),s[c=s.length]="<sheetData/>",l["!links"]=[],null!=l["!ref"]&&(o=function(e,t,r,a){var n,s,i=[],c=[],o=rS(e["!ref"]),l="",f="",h=[],u=0,d=0,p=e["!rows"],m=Array.isArray(e),g={r:f},v=-1;for(d=o.s.c;d<=o.e.c;++d)h[d]=rv(d);for(u=o.s.r;u<=o.e.r;++u){for(c=[],f=rm(u),d=o.s.c;d<=o.e.c;++d){n=h[d]+f;var b=m?(e[u]||[])[d]:e[n];void 0!==b&&null!=(l=function(e,t,r,a){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!=typeof e.f||"z"===e.t&&!e.f)return"";var n="",s=e.t,i=e.v;if("z"!==e.t)switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=rq[e.v];break;case"d":a&&a.cellDates?n=eW(e.v,-1).toISOString():((e=eV(e)).t="n",n=""+(e.v=eN(eW(e.v)))),void 0===e.z&&(e.z=j[14]);break;default:n=e.v}var c=tx("v",to(n)),o={r:t},l=sF(a.cellXfs,e,a);switch(0!==l&&(o.s=l),e.t){case"n":case"z":break;case"d":o.t="d";break;case"b":o.t="b";break;case"e":o.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){c=tx("v",""+sI(a.Strings,e.v,a.revStrings)),o.t="s";break}o.t="str"}if(e.t!=s&&(e.t=s,e.v=i),"string"==typeof e.f&&e.f){var f=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;c=tO("f",to(e.f),f)+(null!=e.v?c:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(o.cm=1),tO("c",c,o)}(b,n,e,t,r,a))&&c.push(l)}(c.length>0||p&&p[u])&&(g={r:f},p&&p[u]&&((s=p[u]).hidden&&(g.hidden=1),v=-1,s.hpx?v=nF(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(g.ht=v,g.customHeight=1),s.level&&(g.outlineLevel=s.level)),i[i.length]=tO("row",c.join(""),g))}if(p)for(;u<p.length;++u)p&&p[u]&&(g={r:u+1},(s=p[u]).hidden&&(g.hidden=1),v=-1,s.hpx?v=nF(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(g.ht=v,g.customHeight=1),s.level&&(g.outlineLevel=s.level),i[i.length]=tO("row","",g));return i.join("")}(l,t,e,r,a)).length>0&&(s[s.length]=o),s.length>c+1&&(s[s.length]="</sheetData>",s[c]=s[c].replace("/>",">")),l["!protect"]&&(s[s.length]=(p=l["!protect"],m={sheet:1},null.forEach(function(e){null!=p[e]&&p[e]&&(m[e]="1")}),null.forEach(function(e){null==p[e]||p[e]||(m[e]="0")}),p.password&&(m.password=nw(p.password).toString(16).toUpperCase()),tO("sheetProtection",null,m))),null!=l["!autofilter"]&&(s[s.length]=function(e,t,r,a){var n="string"==typeof e.ref?e.ref:rE(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,i=rw(n);i.s.r==i.e.r&&(i.e.r=rw(t["!ref"]).e.r,n=rE(i));for(var c=0;c<s.length;++c){var o=s[c];if("_xlnm._FilterDatabase"==o.Name&&o.Sheet==a){o.Ref="'"+r.SheetNames[a]+"'!"+n;break}}return c==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+n}),tO("autoFilter",null,{ref:n})}(l["!autofilter"],l,r,e)),null!=l["!merges"]&&l["!merges"].length>0&&(s[s.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+rE(e[r])+'"/>';return t+"</mergeCells>"}(l["!merges"]));var d,p,m,g,v=-1,b=-1;return l["!links"].length>0&&(s[s.length]="<hyperlinks>",l["!links"].forEach(function(e){e[1].Target&&(g={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(b=r8(a,-1,to(e[1].Target).replace(/#.*$/,""),r4.HLINK),g["r:id"]="rId"+b),(v=e[1].Target.indexOf("#"))>-1&&(g.location=to(e[1].Target.slice(v+1))),e[1].Tooltip&&(g.tooltip=to(e[1].Tooltip)),s[s.length]=tO("hyperlink",null,g))}),s[s.length]="</hyperlinks>"),delete l["!links"],null!=l["!margins"]&&(s[s.length]=(sD(n=l["!margins"]),tO("pageMargins",null,n))),(!t||t.ignoreEC||void 0==t.ignoreEC)&&(s[s.length]=tx("ignoredErrors",tO("ignoredError",null,{numberStoredAsText:1,sqref:f}))),u.length>0&&(b=r8(a,-1,"../drawings/drawing"+(e+1)+".xml",r4.DRAW),s[s.length]=tO("drawing",null,{"r:id":"rId"+b}),l["!drawing"]=u),l["!comments"].length>0&&(b=r8(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",r4.VML),s[s.length]=tO("legacyDrawing",null,{"r:id":"rId"+b}),l["!legacy"]=b),s.length>1&&(s[s.length]="</worksheet>",s[1]=s[1].replace("/>",">")),s.join("")}function sJ(e){return[rF(e),rH(e),"n"]}var sq=["left","right","top","bottom","header","footer"],sZ=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],sQ=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],s1=[],s0=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function s2(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var s=t[n];if(null==a[s[0]])a[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof a[s[0]]&&(a[s[0]]=td(a[s[0]]));break;case"int":"string"==typeof a[s[0]]&&(a[s[0]]=parseInt(a[s[0]],10))}}}function s4(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(null==e[a[0]])e[a[0]]=a[1];else switch(a[2]){case"bool":"string"==typeof e[a[0]]&&(e[a[0]]=td(e[a[0]]));break;case"int":"string"==typeof e[a[0]]&&(e[a[0]]=parseInt(e[a[0]],10))}}}function s3(e){s4(e.WBProps,sZ),s4(e.CalcPr,s0),s2(e.WBView,sQ),s2(e.Sheets,s1),sO.date1904=td(e.WBProps.date1904)}var s5="][*?/\\".split("");function s6(e,t){if(e.length>31){if(t)return!1;throw Error("Sheet names cannot exceed 31 chars")}var r=!0;return s5.forEach(function(a){if(-1!=e.indexOf(a)){if(!t)throw Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}),r}var s8=/<\w+:workbook/;function s7(e){var t=[e5];t[t.length]=tO("workbook",null,{xmlns:tF[0],"xmlns:r":tD.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(sZ.forEach(function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(a[t[0]]=e.Workbook.WBProps[t[0]])}),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),t[t.length]=tO("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],s=0;if(n&&n[0]&&n[0].Hidden){for(s=0,t[t.length]="<bookViews>";s!=e.SheetNames.length&&n[s]&&n[s].Hidden;++s);s==e.SheetNames.length&&(s=0),t[t.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',t[t.length]="</bookViews>"}for(s=0,t[t.length]="<sheets>";s!=e.SheetNames.length;++s){var i={name:to(e.SheetNames[s].slice(0,31))};if(i.sheetId=""+(s+1),i["r:id"]="rId"+(s+1),n[s])switch(n[s].Hidden){case 1:i.state="hidden";break;case 2:i.state="veryHidden"}t[t.length]=tO("sheet",null,i)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=tO("definedName",to(e.Ref),r))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function s9(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}var ie=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,it=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function ir(e,t){var r=e.split(/\s+/),a=[];if(t||(a[0]=r[0]),1===r.length)return a;var n,s,i,c,o=e.match(ie);if(o)for(c=0;c!=o.length;++c)-1===(s=(n=o[c].match(it))[1].indexOf(":"))?a[n[1]]=n[2].slice(1,n[2].length-1):a["xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(s+1)]=n[2].slice(1,n[2].length-1);return a}function ia(e,t){var r,a,i,o=t||{};eE();var l=w(tI(e));("binary"==o.type||"array"==o.type||"base64"==o.type)&&(l=void 0!==n?n.utils.decode(65001,b(l)):tb(l));var f=l.slice(0,1024).toLowerCase(),h=!1;if((1023&(f=f.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&f.indexOf(","),1023&f.indexOf(";"))){var u=eV(o);return u.type="string",na.to_workbook(l,u)}if(-1==f.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach(function(e){f.indexOf("<"+e)>=0&&(h=!0)}),h){var d=l,p=o,m=d.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!m||0==m.length)throw Error("Invalid HTML: could not find <table>");if(1==m.length)return ry(ib(m[0],p),p);var g=i8();return m.forEach(function(e,t){i7(g,ib(e,p),"Sheet"+(t+1))}),g}c={"General Number":"General","General Date":j[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":j[15],"Short Date":j[14],"Long Time":j[19],"Medium Time":j[18],"Short Time":j[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:j[2],Standard:j[4],Percent:j[10],Scientific:j[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var v,T,E,S=[],k={},A=[],y=o.dense?[]:{},_="",x={},C={},O=ir('<Data ss:Type="String">'),R=0,I=0,N=0,D={s:{r:2e6,c:2e6},e:{r:0,c:0}},F={},P={},L="",M=0,U=[],B={},W={},H=0,V=[],z=[],G={},X=[],Y=!1,K=[],q=[],Z={},Q=0,ee=0,et={Sheets:[],WBProps:{date1904:!1}},en={};tN.lastIndex=0,l=l.replace(/<!--([\s\S]*?)-->/mg,"");for(var es="";v=tN.exec(l);)switch(v[3]=(es=v[3]).toLowerCase()){case"data":if("data"==es){if("/"===v[1]){if((T=S.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"))}else"/"!==v[0].charAt(v[0].length-2)&&S.push([v[3],!0]);break}if(S[S.length-1][1])break;"/"===v[1]?function(e,t,r,a,n,s,i,o,l,f){var h="General",u=a.StyleID,d={};f=f||{};var p=[],m=0;for(void 0===u&&o&&(u=o.StyleID),void 0===u&&i&&(u=i.StyleID);void 0!==s[u]&&(s[u].nf&&(h=s[u].nf),s[u].Interior&&p.push(s[u].Interior),s[u].Parent);)u=s[u].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=td(e);break;case"String":a.t="s",a.r=tu(ts(e)),a.v=e.indexOf("<")>-1?ts(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),a.v=(eW(e)-new Date(Date.UTC(1899,11,30)))/864e5,a.v!=a.v?a.v=ts(e):a.v<60&&(a.v=a.v-1),h&&"General"!=h||(h="yyyy-mm-dd");case"Number":void 0===a.v&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=rZ[e],!1!==f.cellText&&(a.w=e);break;default:""==e&&""==t?a.t="z":(a.t="s",a.v=tu(t||e))}if(!function(e,t,r){if("z"!==e.t){if(!r||!1!==r.cellText)try{if("e"===e.t)e.w=e.w||rq[e.v];else if("General"===t)"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=er(e.v):e.w=ea(e.v);else{var a,n,s;a=t||"General",n=e.v,s=c[a]||ts(a),e.w="General"===s?ea(n):eb(s,n)}}catch(e){if(r.WTF)throw e}try{var i=c[t]||t||"General";if(r.cellNF&&(e.z=i),r.cellDates&&"n"==e.t&&em(i)){var o=J(e.v);o&&(e.t="d",e.v=new Date(o.y,o.m-1,o.d,o.H,o.M,o.S,o.u))}}catch(e){if(r.WTF)throw e}}}(a,h,f),!1!==f.cellFormula)if(a.Formula){var g=ts(a.Formula);61==g.charCodeAt(0)&&(g=g.slice(1)),a.f=n5(g,n),delete a.Formula,"RC"==a.ArrayRange?a.F=n5("RC:RC",n):a.ArrayRange&&(a.F=n5(a.ArrayRange,n),l.push([rS(a.F),a.F]))}else for(m=0;m<l.length;++m)n.r>=l[m][0].s.r&&n.r<=l[m][0].e.r&&n.c>=l[m][0].s.c&&n.c<=l[m][0].e.c&&(a.F=l[m][1]);f.cellStyles&&(p.forEach(function(e){!d.patternType&&e.patternType&&(d.patternType=e.patternType)}),a.s=d),void 0!==a.StyleID&&(a.ixfe=a.StyleID)}(l.slice(R,v.index),L,O,"comment"==S[S.length-1][0]?G:x,{c:I,r:N},F,X[I],C,K,o):(L="",O=ir(v[0]),R=v.index+v[0].length);break;case"cell":if("/"===v[1])if(z.length>0&&(x.c=z),(!o.sheetRows||o.sheetRows>N)&&void 0!==x.v&&(o.dense?(y[N]||(y[N]=[]),y[N][I]=x):y[rv(I)+rm(N)]=x),x.HRef&&(x.l={Target:ts(x.HRef)},x.HRefScreenTip&&(x.l.Tooltip=x.HRefScreenTip),delete x.HRef,delete x.HRefScreenTip),(x.MergeAcross||x.MergeDown)&&(Q=I+(0|parseInt(x.MergeAcross,10)),ee=N+(0|parseInt(x.MergeDown,10)),U.push({s:{c:I,r:N},e:{c:Q,r:ee}})),o.sheetStubs)if(x.MergeAcross||x.MergeDown){for(var ei=I;ei<=Q;++ei)for(var ec=N;ec<=ee;++ec)(ei>I||ec>N)&&(o.dense?(y[ec]||(y[ec]=[]),y[ec][ei]={t:"z"}):y[rv(ei)+rm(ec)]={t:"z"});I=Q+1}else++I;else x.MergeAcross?I=Q+1:++I;else(x=function(e){var t=e.split(/\s+/),r={};if(1===t.length)return r;var a,n,s,i,c=e.match(ie);if(c)for(i=0;i!=c.length;++i)-1===(n=(a=c[i].match(it))[1].indexOf(":"))?r[a[1]]=a[2].slice(1,a[2].length-1):r["xmlns:"===a[1].slice(0,6)?"xmlns"+a[1].slice(6):a[1].slice(n+1)]=a[2].slice(1,a[2].length-1);return r}(v[0])).Index&&(I=x.Index-1),I<D.s.c&&(D.s.c=I),I>D.e.c&&(D.e.c=I),"/>"===v[0].slice(-2)&&++I,z=[];break;case"row":"/"===v[1]||"/>"===v[0].slice(-2)?(N<D.s.r&&(D.s.r=N),N>D.e.r&&(D.e.r=N),"/>"===v[0].slice(-2)&&(C=ir(v[0])).Index&&(N=C.Index-1),I=0,++N):((C=ir(v[0])).Index&&(N=C.Index-1),Z={},("0"==C.AutoFitHeight||C.Height)&&(Z.hpx=parseInt(C.Height,10),Z.hpt=nF(Z.hpx),q[N]=Z),"1"==C.Hidden&&(Z.hidden=!0,q[N]=Z));break;case"worksheet":if("/"===v[1]){if((T=S.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"));A.push(_),D.s.r<=D.e.r&&D.s.c<=D.e.c&&(y["!ref"]=rE(D),o.sheetRows&&o.sheetRows<=D.e.r&&(y["!fullref"]=y["!ref"],D.e.r=o.sheetRows-1,y["!ref"]=rE(D))),U.length&&(y["!merges"]=U),X.length>0&&(y["!cols"]=X),q.length>0&&(y["!rows"]=q),k[_]=y}else D={s:{r:2e6,c:2e6},e:{r:0,c:0}},N=I=0,S.push([v[3],!1]),_=ts((T=ir(v[0])).Name),y=o.dense?[]:{},U=[],K=[],q=[],en={name:_,Hidden:0},et.Sheets.push(en);break;case"table":if("/"===v[1]){if((T=S.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"))}else"/>"==v[0].slice(-2)||(S.push([v[3],!1]),X=[],Y=!1);break;case"style":if("/"===v[1]){var eo=P;if(o.cellStyles&&eo.Interior){var el=eo.Interior;el.Pattern&&(el.patternType=nL[el.Pattern]||el.Pattern)}F[eo.ID]=eo}else P=ir(v[0]);break;case"numberformat":P.nf=ts(ir(v[0]).Format||"General"),c[P.nf]&&(P.nf=c[P.nf]);for(var ef=0;392!=ef&&j[ef]!=P.nf;++ef);if(392==ef){for(ef=57;392!=ef;++ef)if(null==j[ef]){eT(P.nf,ef);break}}break;case"column":if("table"!==S[S.length-1][0])break;if((E=ir(v[0])).Hidden&&(E.hidden=!0,delete E.Hidden),E.Width&&(E.wpx=parseInt(E.Width,10)),!Y&&E.wpx>10){Y=!0,nx=6;for(var eh=0;eh<X.length;++eh)X[eh]&&nD(X[eh])}Y&&nD(E),X[E.Index-1||X.length]=E;for(var eu=0;eu<+E.Span;++eu)X[X.length]=eV(E);break;case"namedrange":if("/"===v[1])break;et.Names||(et.Names=[]);var ed=tt(v[0]),ep={Name:ed.Name,Ref:n5(ed.RefersTo.slice(1),{r:0,c:0})};et.Sheets.length>0&&(ep.Sheet=et.Sheets.length-1),et.Names.push(ep);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":"/>"===v[0].slice(-2)||("/"===v[1]?L+=l.slice(M,v.index):M=v.index+v[0].length);break;case"interior":if(!o.cellStyles)break;P.Interior=ir(v[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":"/>"===v[0].slice(-2)||("/"===v[1]?(r=es,a=l.slice(H,v.index),s||(s=eO(al)),B[r=s[r]||r]=a):H=v.index+v[0].length);break;case"styles":case"workbook":if("/"===v[1]){if((T=S.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"))}else S.push([v[3],!1]);break;case"comment":if("/"===v[1]){if((T=S.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"));(i=G).t=i.v||"",i.t=i.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),i.v=i.w=i.ixfe=void 0,z.push(G)}else S.push([v[3],!1]),G={a:(T=ir(v[0])).Author};break;case"autofilter":if("/"===v[1]){if((T=S.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"))}else if("/"!==v[0].charAt(v[0].length-2)){var eg=ir(v[0]);y["!autofilter"]={ref:n5(eg.Range).replace(/\$/g,"")},S.push([v[3],!0])}break;case"datavalidation":case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===v[1]){if((T=S.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"))}else"/"!==v[0].charAt(v[0].length-2)&&S.push([v[3],!0]);break;default:if(0==S.length&&"document"==v[3]||0==S.length&&"uof"==v[3])return iA(l,o);var ev=!0;switch(S[S.length-1][0]){case"officedocumentsettings":switch(v[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:ev=!1}break;case"componentoptions":switch(v[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:ev=!1}break;case"excelworkbook":switch(v[3]){case"date1904":et.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:ev=!1}break;case"workbookoptions":switch(v[3]){case"owcversion":case"height":case"width":break;default:ev=!1}break;case"worksheetoptions":switch(v[3]){case"visible":if("/>"===v[0].slice(-2));else if("/"===v[1])switch(l.slice(H,v.index)){case"SheetHidden":en.Hidden=1;break;case"SheetVeryHidden":en.Hidden=2}else H=v.index+v[0].length;break;case"header":y["!margins"]||sD(y["!margins"]={},"xlml"),isNaN(+tt(v[0]).Margin)||(y["!margins"].header=+tt(v[0]).Margin);break;case"footer":y["!margins"]||sD(y["!margins"]={},"xlml"),isNaN(+tt(v[0]).Margin)||(y["!margins"].footer=+tt(v[0]).Margin);break;case"pagemargins":var ew=tt(v[0]);y["!margins"]||sD(y["!margins"]={},"xlml"),isNaN(+ew.Top)||(y["!margins"].top=+ew.Top),isNaN(+ew.Left)||(y["!margins"].left=+ew.Left),isNaN(+ew.Right)||(y["!margins"].right=+ew.Right),isNaN(+ew.Bottom)||(y["!margins"].bottom=+ew.Bottom);break;case"displayrighttoleft":et.Views||(et.Views=[]),et.Views[0]||(et.Views[0]={}),et.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":y["!outline"]||(y["!outline"]={}),y["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":y["!outline"]||(y["!outline"]={}),y["!outline"].left=!0;break;default:ev=!1}break;case"pivottable":case"pivotcache":switch(v[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:ev=!1}break;case"pagebreaks":switch(v[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:ev=!1}break;case"autofilter":switch(v[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:ev=!1}break;case"querytable":switch(v[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:ev=!1}break;case"datavalidation":switch(v[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:ev=!1}break;case"sorting":case"conditionalformatting":switch(v[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:ev=!1}break;case"mapinfo":case"schema":case"data":switch(v[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:ev=!1}break;case"smarttags":break;default:ev=!1}if(ev||v[3].match(/!\[CDATA/))break;if(!S[S.length-1][1])throw"Unrecognized tag: "+v[3]+"|"+S.join("|");if("customdocumentproperties"===S[S.length-1][0]){"/>"===v[0].slice(-2)||("/"===v[1]?function(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=td(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=eW(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw Error("bad custprop:"+r[0])}e[ts(t)]=n}(W,es,V,l.slice(H,v.index)):(V=v,H=v.index+v[0].length));break}if(o.WTF)throw"Unrecognized tag: "+v[3]+"|"+S.join("|")}var eS={};return o.bookSheets||o.bookProps||(eS.Sheets=k),eS.SheetNames=A,eS.Workbook=et,eS.SSF=eV(j),eS.Props=B,eS.Custprops=W,eS}function is(e,t){switch(i$(t=t||{}),t.type||"base64"){case"base64":return ia(y(e),t);case"binary":case"buffer":case"file":return ia(e,t);case"array":return ia(N(e),t)}}function ii(e){return tO("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+n8(e.Ref,{r:0,c:0})})}var ic=[60,1084,2066,2165,2175];function io(e,t,r){if("z"!==e.t&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=j[a])}catch(e){if(t.WTF)throw e}if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||rq[e.v]:0===a||"General"==a?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=er(e.v):e.w=ea(e.v):e.w=eb(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(e){if(t.WTF)throw e}if(t.cellDates&&a&&"n"==e.t&&em(j[a]||String(a))){var n=J(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function il(e,t,r){return{v:e,ixfe:t,t:r}}var ih={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function iu(e,t){if(t||(t={}),i$(t),v(),t.codepage&&m(t.codepage),e.FullPaths){if(ey.find(e,"/encryption"))throw Error("File is password-protected");n=ey.find(e,"!CompObj"),s=ey.find(e,"/Workbook")||ey.find(e,"/Book")}else{switch(t.type){case"base64":e=R(y(e));break;case"binary":e=R(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e))}rn(e,0),s={content:e}}if(n&&function(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=rG(r,1),r.length-r.l<=4)return;var a=r.read_shift(4);if(0!=a&&!(a>40)&&(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),!(r.length-r.l<=4)&&0x71b239f4===(a=r.read_shift(4)))&&(t.UnicodeClipboardFormat=rG(r,2),0!=(a=r.read_shift(4))&&!(a>40)))r.l-=4,t.Reserved2=r.read_shift(0,"lpwstr")}(n),t.bookProps&&!t.bookSheets)i={};else{var r,a,n,s,i,c,o=_?"buffer":"array";if(s&&s.content)i=function(e,t){var r,a,n,s,i={opts:{}},c={},o,l,f,h,u,d=t.dense?[]:{},p={},m={},v=null,b=[],T="",w={},E="",S={},k=[],A=[],y=[],_={Sheets:[],WBProps:{date1904:!1},Views:[{}]},x={},C=function(e){return e<8?rJ[e]:e<64&&y[e-8]||rJ[e]},O=function(e,t,r){var a,n=t.XF.data;n&&n.patternType&&r&&r.cellStyles&&(t.s={},t.s.patternType=n.patternType,(a=ny(C(n.icvFore)))&&(t.s.fgColor={rgb:a}),(a=ny(C(n.icvBack)))&&(t.s.bgColor={rgb:a}))},R=function(e,t,r){if(!(W>1)&&(!r.sheetRows||!(e.r>=r.sheetRows))){if(r.cellStyles&&t.XF&&t.XF.data&&O(e,t,r),delete t.ixfe,delete t.XF,o=e,E=rT(e),m&&m.s&&m.e||(m={s:{r:0,c:0},e:{r:0,c:0}}),e.r<m.s.r&&(m.s.r=e.r),e.c<m.s.c&&(m.s.c=e.c),e.r+1>m.e.r&&(m.e.r=e.r+1),e.c+1>m.e.c&&(m.e.c=e.c+1),r.cellFormula&&t.f){for(var a=0;a<k.length;++a)if(!(k[a][0].s.c>e.c)&&!(k[a][0].s.r>e.r)&&!(k[a][0].e.c<e.c)&&!(k[a][0].e.r<e.r)){t.F=rE(k[a][0]),(k[a][0].s.c!=e.c||k[a][0].s.r!=e.r)&&delete t.f,t.f&&(t.f=""+sw(k[a][1],m,e,U,I));break}}r.dense?(d[e.r]||(d[e.r]=[]),d[e.r][e.c]=t):d[E]=t}},I={enc:!1,sbcch:0,snames:[],sharedf:S,arrayf:k,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(I.password=t.password);var N=[],D=[],P=[],L=[],M=!1,U=[];U.SheetNames=I.snames,U.sharedf=I.sharedf,U.arrayf=I.arrayf,U.names=[],U.XTI=[];var B=0,W=0,H=0,V=[],z=[];I.codepage=1200,g(1200);for(var G=!1;e.l<e.length-1;){var X=e.l,Y=e.read_shift(2);if(0===Y&&10===B)break;var K=e.l===e.length?0:e.read_shift(2),J=ip[Y];if(J&&J.f){if(t.bookSheets&&133===B&&133!==Y)break;if(B=Y,2===J.r||12==J.r){var q,Z=e.read_shift(2);if(K-=2,!I.enc&&Z!==Y&&((255&Z)<<8|Z>>8)!==Y)throw Error("rt mismatch: "+Z+"!="+Y);12==J.r&&(e.l+=10,K-=10)}var Q={};if(Q=10===Y?J.f(e,K,I):function(e,t,r,a,n){var s=a,i=[],c=r.slice(r.l,r.l+s);if(n&&n.enc&&n.enc.insitu&&c.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:case 133:break;default:n.enc.insitu(c)}i.push(c),r.l+=s;for(var o=t3(r,r.l),l=ip[o],f=0;null!=l&&ic.indexOf(o)>-1;)s=t3(r,r.l+2),f=r.l+4,2066==o?f+=4:(2165==o||2175==o)&&(f+=12),c=r.slice(f,r.l+4+s),i.push(c),r.l+=4+s,l=ip[o=t3(r,r.l)];var h=F(i);rn(h,0);var u=0;h.lens=[];for(var d=0;d<i.length;++d)h.lens.push(u),u+=i[d].length;if(h.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+a;return t.f(h,h.length,n)}(Y,J,e,K,I),0==W&&-1===[9,521,1033,2057].indexOf(B))continue;switch(Y){case 34:i.opts.Date1904=_.WBProps.date1904=Q;break;case 134:i.opts.WriteProtect=!0;break;case 47:if(I.enc||(e.l=0),I.enc=Q,!t.password)throw Error("File is password-protected");if(null==Q.valid)throw Error("Encryption scheme unsupported");if(!Q.valid)throw Error("Password is incorrect");break;case 92:I.lastuser=Q;break;case 66:var ee=Number(Q);switch(ee){case 21010:ee=1200;break;case 32768:ee=1e4;break;case 32769:ee=1252}g(I.codepage=ee),G=!0;break;case 317:I.rrtabid=Q;break;case 25:I.winlocked=Q;break;case 439:i.opts.RefreshAll=Q;break;case 12:i.opts.CalcCount=Q;break;case 16:i.opts.CalcDelta=Q;break;case 17:i.opts.CalcIter=Q;break;case 13:i.opts.CalcMode=Q;break;case 14:i.opts.CalcPrecision=Q;break;case 95:i.opts.CalcSaveRecalc=Q;break;case 15:I.CalcRefMode=Q;break;case 2211:i.opts.FullCalc=Q;break;case 129:Q.fDialog&&(d["!type"]="dialog"),Q.fBelow||((d["!outline"]||(d["!outline"]={})).above=!0),Q.fRight||((d["!outline"]||(d["!outline"]={})).left=!0);break;case 224:A.push(Q);break;case 430:U.push([Q]),U[U.length-1].XTI=[];break;case 35:case 547:U[U.length-1].push(Q);break;case 24:case 536:s={Name:Q.Name,Ref:sw(Q.rgce,m,null,U,I)},Q.itab>0&&(s.Sheet=Q.itab-1),U.names.push(s),U[0]||(U[0]=[],U[0].XTI=[]),U[U.length-1].push(Q),"_xlnm._FilterDatabase"==Q.Name&&Q.itab>0&&Q.rgce&&Q.rgce[0]&&Q.rgce[0][0]&&"PtgArea3d"==Q.rgce[0][0][0]&&(z[Q.itab-1]={ref:rE(Q.rgce[0][0][1][2])});break;case 22:I.ExternCount=Q;break;case 23:0==U.length&&(U[0]=[],U[0].XTI=[]),U[U.length-1].XTI=U[U.length-1].XTI.concat(Q),U.XTI=U.XTI.concat(Q);break;case 2196:if(I.biff<8)break;null!=s&&(s.Comment=Q[1]);break;case 18:d["!protect"]=Q;break;case 19:0!==Q&&I.WTF&&console.error("Password verifier: "+Q);break;case 133:p[Q.pos]=Q,I.snames.push(Q.name);break;case 10:if(--W)break;if(m.e){if(m.e.r>0&&m.e.c>0){if(m.e.r--,m.e.c--,d["!ref"]=rE(m),t.sheetRows&&t.sheetRows<=m.e.r){var et=m.e.r;m.e.r=t.sheetRows-1,d["!fullref"]=d["!ref"],d["!ref"]=rE(m),m.e.r=et}m.e.r++,m.e.c++}N.length>0&&(d["!merges"]=N),D.length>0&&(d["!objects"]=D),P.length>0&&(d["!cols"]=P),L.length>0&&(d["!rows"]=L),_.Sheets.push(x)}""===T?w=d:c[T]=d,d=t.dense?[]:{};break;case 9:case 521:case 1033:case 2057:if(8===I.biff&&(I.biff=({9:2,521:3,1033:4})[Y]||({512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2})[Q.BIFFVer]||8),I.biffguess=0==Q.BIFFVer,0==Q.BIFFVer&&4096==Q.dt&&(I.biff=5,G=!0,g(I.codepage=28591)),8==I.biff&&0==Q.BIFFVer&&16==Q.dt&&(I.biff=2),W++)break;if(d=t.dense?[]:{},I.biff<8&&!G&&(G=!0,g(I.codepage=t.codepage||1252)),I.biff<5||0==Q.BIFFVer&&4096==Q.dt){""===T&&(T="Sheet1"),m={s:{r:0,c:0},e:{r:0,c:0}};var er={pos:e.l-K,name:T};p[er.pos]=er,I.snames.push(T)}else T=(p[X]||{name:""}).name;32==Q.dt&&(d["!type"]="chart"),64==Q.dt&&(d["!type"]="macro"),N=[],D=[],I.arrayf=k=[],P=[],L=[],M=!1,x={Hidden:(p[X]||{hs:0}).hs,name:T};break;case 515:case 3:case 2:"chart"==d["!type"]&&(t.dense?(d[Q.r]||[])[Q.c]:d[rT({c:Q.c,r:Q.r})])&&++Q.c,r={ixfe:Q.ixfe,XF:A[Q.ixfe]||{},v:Q.val,t:"n"},H>0&&(r.z=V[r.ixfe>>8&63]),io(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t);break;case 5:case 517:r={ixfe:Q.ixfe,XF:A[Q.ixfe],v:Q.val,t:Q.t},H>0&&(r.z=V[r.ixfe>>8&63]),io(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t);break;case 638:r={ixfe:Q.ixfe,XF:A[Q.ixfe],v:Q.rknum,t:"n"},H>0&&(r.z=V[r.ixfe>>8&63]),io(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t);break;case 189:for(var ea=Q.c;ea<=Q.C;++ea){var en=Q.rkrec[ea-Q.c][0];r={ixfe:en,XF:A[en],v:Q.rkrec[ea-Q.c][1],t:"n"},H>0&&(r.z=V[r.ixfe>>8&63]),io(r,t,i.opts.Date1904),R({c:ea,r:Q.r},r,t)}break;case 6:case 518:case 1030:if("String"==Q.val){v=Q;break}if((r=il(Q.val,Q.cell.ixfe,Q.tt)).XF=A[r.ixfe],t.cellFormula){var es=Q.formula;if(es&&es[0]&&es[0][0]&&"PtgExp"==es[0][0][0]){var ei=es[0][0][1][0],ec=es[0][0][1][1],eo=rT({r:ei,c:ec});S[eo]?r.f=""+sw(Q.formula,m,Q.cell,U,I):r.F=((t.dense?(d[ei]||[])[ec]:d[eo])||{}).F}else r.f=""+sw(Q.formula,m,Q.cell,U,I)}H>0&&(r.z=V[r.ixfe>>8&63]),io(r,t,i.opts.Date1904),R(Q.cell,r,t),v=Q;break;case 7:case 519:if(v)v.val=Q,(r=il(Q,v.cell.ixfe,"s")).XF=A[r.ixfe],t.cellFormula&&(r.f=""+sw(v.formula,m,v.cell,U,I)),H>0&&(r.z=V[r.ixfe>>8&63]),io(r,t,i.opts.Date1904),R(v.cell,r,t),v=null;else throw Error("String record expects Formula");break;case 33:case 545:k.push(Q);var el=rT(Q[0].s);if(l=t.dense?(d[Q[0].s.r]||[])[Q[0].s.c]:d[el],t.cellFormula&&l){if(!v||!el||!l)break;l.f=""+sw(Q[1],m,Q[0],U,I),l.F=rE(Q[0])}break;case 1212:if(!t.cellFormula)break;if(E){if(!v)break;S[rT(v.cell)]=Q[0],((l=t.dense?(d[v.cell.r]||[])[v.cell.c]:d[rT(v.cell)])||{}).f=""+sw(Q[0],m,o,U,I)}break;case 253:r=il(b[Q.isst].t,Q.ixfe,"s"),b[Q.isst].h&&(r.h=b[Q.isst].h),r.XF=A[r.ixfe],H>0&&(r.z=V[r.ixfe>>8&63]),io(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t);break;case 513:t.sheetStubs&&(r={ixfe:Q.ixfe,XF:A[Q.ixfe],t:"z"},H>0&&(r.z=V[r.ixfe>>8&63]),io(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t));break;case 190:if(t.sheetStubs)for(var ef=Q.c;ef<=Q.C;++ef){var eh=Q.ixfe[ef-Q.c];r={ixfe:eh,XF:A[eh],t:"z"},H>0&&(r.z=V[r.ixfe>>8&63]),io(r,t,i.opts.Date1904),R({c:ef,r:Q.r},r,t)}break;case 214:case 516:case 4:(r=il(Q.val,Q.ixfe,"s")).XF=A[r.ixfe],H>0&&(r.z=V[r.ixfe>>8&63]),io(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t);break;case 0:case 512:1===W&&(m=Q);break;case 252:b=Q;break;case 1054:if(4==I.biff){V[H++]=Q[1];for(var eu=0;eu<H+163&&j[eu]!=Q[1];++eu);eu>=163&&eT(Q[1],H+163)}else eT(Q[1],Q[0]);break;case 30:V[H++]=Q;for(var ed=0;ed<H+163&&j[ed]!=Q;++ed);ed>=163&&eT(Q,H+163);break;case 229:N=N.concat(Q);break;case 93:D[Q.cmo[0]]=I.lastobj=Q;break;case 438:I.lastobj.TxO=Q;break;case 127:I.lastobj.ImData=Q;break;case 440:for(u=Q[0].s.r;u<=Q[0].e.r;++u)for(h=Q[0].s.c;h<=Q[0].e.c;++h)(l=t.dense?(d[u]||[])[h]:d[rT({c:h,r:u})])&&(l.l=Q[1]);break;case 2048:for(u=Q[0].s.r;u<=Q[0].e.r;++u)for(h=Q[0].s.c;h<=Q[0].e.c;++h)(l=t.dense?(d[u]||[])[h]:d[rT({c:h,r:u})])&&l.l&&(l.l.Tooltip=Q[1]);break;case 28:if(I.biff<=5&&I.biff>=2)break;l=t.dense?(d[Q[0].r]||[])[Q[0].c]:d[rT(Q[0])];var ep=D[Q[2]];l||(t.dense?(d[Q[0].r]||(d[Q[0].r]=[]),l=d[Q[0].r][Q[0].c]={t:"z"}):l=d[rT(Q[0])]={t:"z"},m.e.r=Math.max(m.e.r,Q[0].r),m.s.r=Math.min(m.s.r,Q[0].r),m.e.c=Math.max(m.e.c,Q[0].c),m.s.c=Math.min(m.s.c,Q[0].c)),l.c||(l.c=[]),f={a:Q[1],t:ep.TxO.t},l.c.push(f);break;case 2173:A[Q.ixfe],Q.ext.forEach(function(e){e[0]});break;case 125:if(!I.cellStyles)break;for(;Q.e>=Q.s;)P[Q.e--]={width:Q.w/256,level:Q.level||0,hidden:!!(1&Q.flags)},M||(M=!0,nN(Q.w/256)),nD(P[Q.e+1]);break;case 520:var em={};null!=Q.level&&(L[Q.r]=em,em.level=Q.level),Q.hidden&&(L[Q.r]=em,em.hidden=!0),Q.hpt&&(L[Q.r]=em,em.hpt=Q.hpt,em.hpx=nP(Q.hpt));break;case 38:case 39:case 40:case 41:d["!margins"]||sD(d["!margins"]={}),d["!margins"][({38:"left",39:"right",40:"top",41:"bottom"})[Y]]=Q;break;case 161:d["!margins"]||sD(d["!margins"]={}),d["!margins"].header=Q.header,d["!margins"].footer=Q.footer;break;case 574:Q.RTL&&(_.Views[0].RTL=!0);break;case 146:y=Q;break;case 2198:n=Q;break;case 140:a=Q;break;case 442:T?x.CodeName=Q||x.name:_.WBProps.CodeName=Q||"ThisWorkbook"}}else J||console.error("Missing Info for XLS Record 0x"+Y.toString(16)),e.l+=K}return i.SheetNames=ex(p).sort(function(e,t){return Number(e)-Number(t)}).map(function(e){return p[e].name}),t.bookSheets||(i.Sheets=c),!i.SheetNames.length&&w["!ref"]?(i.SheetNames.push("Sheet1"),i.Sheets&&(i.Sheets.Sheet1=w)):i.Preamble=w,i.Sheets&&z.forEach(function(e,t){i.Sheets[i.SheetNames[t]]["!autofilter"]=e}),i.Strings=b,i.SSF=eV(j),I.enc&&(i.Encryption=I.enc),n&&(i.Themes=n),i.Metadata={},void 0!==a&&(i.Metadata.Country=a),U.names.length>0&&(_.Names=U.names),i.Workbook=_,i}(s.content,t);else if((c=ey.find(e,"PerfectOffice_MAIN"))&&c.content)i=nn.to_workbook(c.content,(t.type=o,t));else if((c=ey.find(e,"NativeContent_MAIN"))&&c.content)i=nn.to_workbook(c.content,(t.type=o,t));else if((c=ey.find(e,"MN0"))&&c.content)throw Error("Unsupported Works 4 for Mac file");else throw Error("Cannot find Workbook stream");t.bookVBA&&e.FullPaths&&ey.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(i.vbaraw=(r=e,a=ey.utils.cfb_new({root:"R"}),r.FullPaths.forEach(function(e,t){if("/"!==e.slice(-1)&&e.match(/_VBA_PROJECT_CUR/)){var n=e.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");ey.utils.cfb_add(a,n,r.FileIndex[t].content)}}),ey.write(a)))}var l={};return e.FullPaths&&function(e,t,r){var a=ey.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=aE(a,r$,ih.DSI);for(var s in n)t[s]=n[s]}catch(e){if(r.WTF)throw e}var i=ey.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var c=aE(i,rX,ih.SI);for(var o in c)null==t[o]&&(t[o]=c[o])}catch(e){if(r.WTF)throw e}t.HeadingPairs&&t.TitlesOfParts&&(as(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}(e,l,t),i.Props=i.Custprops=l,t.bookFiles&&(i.cfb=e),i}var id={0:{f:function(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,7&s&&(r.level=7&s),16&s&&(r.hidden=!0),32&s&&(r.hpt=n/20),r}},1:{f:function(e){return[rN(e)]}},2:{f:function(e){return[rN(e),rU(e),"n"]}},3:{f:function(e){return[rN(e),e.read_shift(1),"e"]}},4:{f:function(e){return[rN(e),e.read_shift(1),"b"]}},5:{f:function(e){return[rN(e),rH(e),"n"]}},6:{f:function(e){return[rN(e),rO(e),"str"]}},7:{f:function(e){return[rN(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var a=e.l+t,n=rN(e);n.r=r["!row"];var s=[n,rO(e),"str"];if(r.cellFormula){e.l+=2;var i=sS(e,a-e.l,r);s[3]=sw(i,null,n,r.supbooks,r)}else e.l=a;return s}},9:{f:function(e,t,r){var a=e.l+t,n=rN(e);n.r=r["!row"];var s=[n,rH(e),"n"];if(r.cellFormula){e.l+=2;var i=sS(e,a-e.l,r);s[3]=sw(i,null,n,r.supbooks,r)}else e.l=a;return s}},10:{f:function(e,t,r){var a=e.l+t,n=rN(e);n.r=r["!row"];var s=[n,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var i=sS(e,a-e.l,r);s[3]=sw(i,null,n,r.supbooks,r)}else e.l=a;return s}},11:{f:function(e,t,r){var a=e.l+t,n=rN(e);n.r=r["!row"];var s=[n,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var i=sS(e,a-e.l,r);s[3]=sw(i,null,n,r.supbooks,r)}else e.l=a;return s}},12:{f:function(e){return[rF(e)]}},13:{f:function(e){return[rF(e),rU(e),"n"]}},14:{f:function(e){return[rF(e),e.read_shift(1),"e"]}},15:{f:function(e){return[rF(e),e.read_shift(1),"b"]}},16:{f:sJ},17:{f:function(e){return[rF(e),rO(e),"str"]}},18:{f:function(e){return[rF(e),e.read_shift(4),"s"]}},19:{f:rI},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),s=rO(e),i=sS(e,0,r),c=rL(e);e.l=a;var o={Name:s,Ptg:i};return n<0xfffffff&&(o.Sheet=n),c&&(o.Comment=c),o}},40:{},42:{},43:{f:function(e,t,r){var a,n={};n.sz=e.read_shift(2)/20;var s=(a=e.read_shift(1),e.l++,{fBold:1&a,fItalic:2&a,fUnderline:4&a,fStrikeout:8&a,fOutline:16&a,fShadow:32&a,fCondense:64&a,fExtend:128&a});switch(s.fItalic&&(n.italic=1),s.fCondense&&(n.condense=1),s.fExtend&&(n.extend=1),s.fShadow&&(n.shadow=1),s.fOutline&&(n.outline=1),s.fStrikeout&&(n.strike=1),700===e.read_shift(2)&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript"}var i=e.read_shift(1);0!=i&&(n.underline=i);var c=e.read_shift(1);c>0&&(n.family=c);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=function(e){var t={},r=e.read_shift(1),a=e.read_shift(1),n=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),c=e.read_shift(1);switch(e.l++,r>>>1){case 0:t.auto=1;break;case 1:t.index=a;var o=rJ[a];o&&(t.rgb=ny(o));break;case 2:t.rgb=ny([s,i,c]);break;case 3:t.theme=a}return 0!=n&&(t.tint=n>0?n/32767:n/32768),t}(e,8),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor"}return n.name=rO(e,t-21),n}},44:{f:function(e,t){return[e.read_shift(2),rO(e,t-2)]}},45:{f:rs},46:{f:rs},47:{f:function(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:a8},62:{f:function(e){return[rN(e),rI(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=rT(r);var a=e.read_shift(1);return 2&a&&(t.l="1"),8&a&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:rs,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},a=e[e.l];return++e.l,r.above=!(64&a),r.left=!(128&a),e.l+=18,r.name=rO(e,t-19),r}},148:{f:rW,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?rO(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(65536&a),r.backupFile=!!(64&a),r.checkCompatibility=!!(4096&a),r.date1904=!!(1&a),r.filterPrivacy=!!(8&a),r.hidePivotFieldList=!!(1024&a),r.promptedSolutions=!!(16&a),r.publishItems=!!(2048&a),r.refreshAllConnections=!!(262144&a),r.saveExternalLinkValues=!!(128&a),r.showBorderUnselectedTables=!!(4&a),r.showInkAnnotation=!!(32&a),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(32768&a),r.updateLinks=["userSet","never","always"][a>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=rL(e,t-8),r.name=rO(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:rW},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:rW},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:rO(e,t-8)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:rL},357:{},358:{},359:{},360:{T:1},361:{},362:{f:a3},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var a=e.l+t,n=rW(e,16),s=e.read_shift(1),i=[n];if(i[2]=s,r.cellFormula){var c=sS(e,a-e.l,r);i[1]=c}else e.l=a;return i}},427:{f:function(e,t,r){var a=e.l+t,n=[rW(e,16)];if(r.cellFormula){var s=sS(e,a-e.l,r);n[1]=s,e.l=a}else e.l=a;return n}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return sq.forEach(function(r){t[r]=rH(e,8)}),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,a=rW(e,16),n=rL(e),s=rO(e),i=rO(e),c=rO(e);e.l=r;var o={rfx:a,relId:n,loc:s,display:c};return i&&(o.Tooltip=i),o}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:rL},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:rO},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=rW(e,16);return t.rfx=r.s,t.ref=rT(r.s),e.l+=16,t}},636:{T:-1},637:{f:rI},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:rO(e,t-10)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},ip={6:{f:sE},10:{f:ak},12:{f:a_},13:{f:a_},14:{f:aA},15:{f:aA},16:{f:rH},17:{f:aA},18:{f:aA},19:{f:a_},20:{f:a1},21:{f:a1},23:{f:a3},24:{f:a4},25:{f:aA},26:{},27:{},28:{f:function(e,t,r){return function(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=aD(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},c,i,s]}}(e,0,r)}},29:{},34:{f:aA},35:{f:a0},38:{f:rH},39:{f:rH},40:{f:rH},41:{f:rH},42:{f:aA},43:{f:aA},47:{f:function(e,t,r){var a,n,s,i,c={Type:r.biff>=8?e.read_shift(2):0};return c.Type?(a=t-2,(n=c||{}).Info=e.read_shift(2),e.l-=2,1===n.Info?n.Data=function(e){var t={},r=t.EncryptionVersionInfo=nv(e,4);if(1!=r.Major||1!=r.Minor)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}(e,a):n.Data=function(e,t){var r={},a=r.EncryptionVersionInfo=nv(e,4);if(t-=4,2!=a.Minor)throw Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw Error("unrecognized major version code: "+a.Major);r.Flags=e.read_shift(4),t-=4;var n=e.read_shift(4);return t-=4,r.EncryptionHeader=nb(e,n),r.EncryptionVerifier=nT(e,t-=n),r}(e,a)):(r.biff,i={key:a_(e),verificationBytes:a_(e)},r.password&&(i.verifier=nw(r.password)),c.valid=i.verificationBytes===i.verifier,c.valid&&(c.insitu=nk(r.password))),c}},49:{f:function(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return a.name=aR(e,0,r),a}},51:{f:a_},60:{},61:{f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{f:aA},65:{f:function(){}},66:{f:a_},77:{},80:{},81:{},82:{},85:{f:a_},89:{},90:{},91:{},92:{f:function(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=aD(e,0,r);return e.read_shift(t+a-e.l),n}},93:{f:function(e,t,r){if(r&&r.biff<8){var a,n,s,i,c,o,l;return a=e,n=t,s=r,a.l+=4,i=a.read_shift(2),c=a.read_shift(2),o=a.read_shift(2),a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=6,n-=36,(l=[]).push((a6[i]||rs)(a,n,s)),{cmo:[c,i,o],ft:l}}var f=aj(e,22),h=function(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(aX[n](e,r-e.l))}catch(t){return e.l=r,a}}return e.l!=r&&(e.l=r),a}(e,t-22,f[1]);return{cmo:f,ft:h}}},94:{},95:{f:aA},96:{},97:{},99:{f:aA},125:{f:a8},128:{f:function(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(0!==t[0]&&t[0]--,0!==t[1]&&t[1]--,t[0]>7||t[1]>7)throw Error("Bad Gutters: "+t.join("|"));return t}},129:{f:function(e,t,r){var a=r&&8==r.biff||2==t?e.read_shift(2):(e.l+=t,0);return{fDialog:16&a,fBelow:64&a,fRight:128&a}}},130:{f:a_},131:{f:aA},132:{f:aA},133:{f:function(e,t,r){var a=e.read_shift(4),n=3&e.read_shift(1),s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule"}var i=aR(e,0,r);return 0===i.length&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}},134:{},140:{f:function(e){var t,r=[0,0];return t=e.read_shift(2),r[0]=rY[t]||t,t=e.read_shift(2),r[1]=rY[t]||t,r}},141:{f:a_},144:{},146:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(aU(e,8));return r}},151:{},152:{},153:{},154:{},155:{},156:{f:a_},157:{},158:{},160:{f:aC},161:{f:function(e,t){var r={};return t<32||(e.l+=16,r.header=rH(e,8),r.footer=rH(e,8),e.l+=2),r}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(aH(e));if(e.l!==r)throw Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}},190:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}},193:{f:ak},197:{},198:{},199:{},200:{},201:{},202:{f:aA},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:a_},220:{},221:{f:aA},222:{},224:{f:function(e,t,r){var a,n,s,i,c,o,l={};return l.ifnt=e.read_shift(2),l.numFmtId=e.read_shift(2),l.flags=e.read_shift(2),l.fStyle=l.flags>>2&1,t-=6,l.fStyle,n={},s=e.read_shift(4),i=e.read_shift(4),c=e.read_shift(4),o=e.read_shift(2),n.patternType=rK[c>>26],r.cellStyles&&(n.alc=7&s,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=15&i,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=127&c,n.icvBottom=c>>7&127,n.icvDiag=c>>14&127,n.dgDiag=c>>21&15,n.icvFore=127&o,n.icvBack=o>>7&127,n.fsxButton=o>>14&1),l.data=n,l}},225:{f:function(e,t){return 0===t||e.read_shift(2),1200}},226:{f:ak},227:{},229:{f:function(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(aV(e,t));return r}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<r;++i)s.push(function(e){var t=h;h=1200;var r,a=e.read_shift(2),n=e.read_shift(1),s=4&n,i=8&n,c=0,o={};i&&(c=e.read_shift(2)),s&&(r=e.read_shift(4));var l=0===a?"":e.read_shift(a,2==1+(1&n)?"dbcs-cont":"sbcs-cont");return i&&(e.l+=4*c),s&&(e.l+=r),o.t=l,i||(o.raw="<t>"+o.t+"</t>",o.r=o.t),h=t,o}(e));return s.Count=a,s.Unique=n,s}},253:{f:function(e){var t=aB(e);return t.isst=e.read_shift(4),t}},255:{f:function(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:aC},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:aA},353:{f:ak},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,1025==s||14849==s)return[s,n];if(s<1||s>255)throw Error("Unexpected SupBook type: "+s);for(var i=aI(e,s),c=[];a>e.l;)c.push(aN(e));return[s,n,i,c]}},431:{f:aA},432:{},433:{},434:{},437:{},438:{f:function(e,t,r){var a=e.l,n="";try{e.l+=4;var s,i,c=(r.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(c)?e.l+=6:(e.read_shift(1),e.l++,e.read_shift(2),e.l+=2);var o=e.read_shift(2);e.read_shift(2),a_(e,2);var l=e.read_shift(2);e.l+=l;for(var f=1;f<e.lens.length-1;++f){if(e.l-a!=e.lens[f])throw Error("TxO: bad continue record");var h=e[e.l],u=aI(e,e.lens[f+1]-e.lens[f]-1);if((n+=u).length>=(h?o:2*o))break}if(n.length!==o&&n.length!==2*o)throw Error("cchText: "+o+" != "+n.length);return e.l=a+t,{t:n}}catch(r){return e.l=a+t,{t:n}}}},439:{f:aA},440:{f:function(e,t){var r=aV(e,8);return e.l+=16,[r,function(e,t){var r=e.l+t,a=e.read_shift(4);if(2!==a)throw Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,c,o,l,f,h="";16&n&&(s=aP(e,r-e.l)),128&n&&(i=aP(e,r-e.l)),(257&n)==257&&(c=aP(e,r-e.l)),(257&n)==1&&(o=function(e,t){var r,a,n,s,i=e.read_shift(16);switch(t-=16,i){case"e0c9ea79f9bace118c8200aa004ba90b":return r=e.read_shift(4),a=e.l,n=!1,r>24&&(e.l+=r-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(n=!0),e.l=a),s=e.read_shift((n?r-24:r)>>1,"utf16le").replace(P,""),n&&(e.l+=24),s;case"0303000000000000c000000000000046":for(var c=e.read_shift(2),o="";c-- >0;)o+="../";var l=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw Error("Bad FileMoniker");if(0===e.read_shift(4))return o+l.replace(/\\/g,"/");var f=e.read_shift(4);if(3!=e.read_shift(2))throw Error("Bad FileMoniker");return o+e.read_shift(f>>1,"utf16le").replace(P,"");default:throw Error("Unsupported Moniker "+i)}}(e,r-e.l)),8&n&&(h=aP(e,r-e.l)),32&n&&(l=e.read_shift(16)),64&n&&(f=af(e)),e.l=r;var u=i||c||o||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&n&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return l&&(d.guid=l),f&&(d.time=f),s&&(d.Tooltip=s),d}(e,t-24)]}},441:{},442:{f:aN},443:{},444:{f:a_},445:{},446:{},448:{f:ak},449:{f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{f:ak},512:{f:aq},513:{f:aB},515:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=aB(e,6);return a.val=rH(e,8),a}},516:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=e.l+t,n=aB(e,6);return 2==r.biff&&e.l++,n.val=aN(e,a-e.l,r),n}},517:{f:aQ},519:{f:aN},520:{f:function(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,7&a&&(t.level=7&a),32&a&&(t.hidden=!0),64&a&&(t.hpt=r/20),t}},523:{},545:{f:a5},549:{f:aJ},566:{},574:{f:function(e,t,r){return r&&r.biff>=2&&r.biff<5?{}:{RTL:64&e.read_shift(2)}}},638:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=aH(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}},659:{},1048:{},1054:{f:function(e,t,r){return[e.read_shift(2),aD(e,0,r)]}},1084:{},1212:{f:function(e,t,r){var a=aG(e,6);e.l++;var n=e.read_shift(1);return[function(e,t,r){var a,n,s=e.l+t,i=e.read_shift(2),c=sg(e,i,r);return 65535==i?[[],(a=t-2,void(e.l+=a))]:(t!==i+2&&(n=sm(e,s-i-2,c,r)),[c,n])}(e,t-=8,r),n,a]}},2048:{f:function(e,t){e.read_shift(2);var r=aV(e,8),a=e.read_shift((t-10)/2,"dbcs-cont");return[r,a=a.replace(P,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:aY},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:ak},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t},r:12},2173:{f:function(e,t){var r=e.l+t;e.l+=2;var a=e.read_shift(2);e.l+=2;for(var n=e.read_shift(2),s=[];n-- >0;)s.push(function(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=function(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:case 4:e.l+=4;break;case 1:t.xclrValue=function(e,t){e.l+=t}(e,4);break;case 2:t.xclrValue=aM(e,4);break;case 3:t.xclrValue=e.read_shift(4)}return e.l+=8,t}(e,r);break;case 6:a[1]=void(e.l+=r);break;case 14:case 15:a[1]=e.read_shift(1===r?1:2);break;default:throw Error("Unrecognized ExtProp type: "+t+" "+r)}return a}(e,r-e.l));return{ixfe:a,ext:s}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:aA,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(e,t,r){if(r.biff<8){e.l+=t;return}var a=e.read_shift(2),n=e.read_shift(2);return[aI(e,a,r),aI(e,n,r)]},r:12},2197:{},2198:{f:function(e,t,r){var a,n=e.l+t;if(124226!==e.read_shift(4)){if(!r.cellStyles){e.l=n;return}var s=e.slice(e.l);e.l=n;try{a=e4(s,{type:"array"})}catch(e){return}var i=eQ(a,"theme/theme/theme1.xml",!0);if(i)return nZ(i,r)}},r:12},2199:{},2200:{},2201:{},2202:{f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{f:ak},2204:{},2205:{},2206:{},2207:{},2211:{f:function(e){var t,r,a=(t=e.read_shift(2),r=e.read_shift(2),e.l+=8,{type:t,flags:r});if(2211!=a.type)throw Error("Invalid Future Record "+a.type);return 0!==e.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:a_},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(e,t,r){var a={area:!1};if(5!=r.biff)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,16&n&&(a.area=!0),a}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(aU(e,8));return r}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:aq},1:{},2:{f:function(e){var t=aB(e,6);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}},3:{f:function(e){var t=aB(e,6);++e.l;var r=rH(e,8);return t.t="n",t.val=r,t}},4:{f:function(e,t,r){r.biffguess&&5==r.biff&&(r.biff=2);var a=aB(e,6);++e.l;var n=aD(e,t-7,r);return a.t="str",a.val=n,a}},5:{f:aQ},7:{f:function(e){var t=e.read_shift(1);return 0===t?(e.l++,""):e.read_shift(t,"sbcs-cont")}},8:{},9:{f:aY},11:{},22:{f:a_},30:{f:aD},31:{},32:{},33:{f:a5},36:{},37:{f:aJ},50:{f:function(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}},62:{},52:{},67:{},68:{f:a_},69:{},86:{},126:{},127:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(e,t,r){var a=e.l+t,n=aB(e,6),s=e.read_shift(2),i=aI(e,s,r);return e.l=a,n.t="str",n.val=i,n}},223:{},234:{},354:{},421:{},518:{f:sE},521:{f:aY},536:{f:a4},547:{f:a0},561:{},579:{},1030:{f:sE},1033:{f:aY},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function im(e,t,r,a){if(!isNaN(t)){var n=a||(r||[]).length||0,s=e.next(4);s.write_shift(2,t),s.write_shift(2,n),n>0&&t2(r)&&e.push(r)}}function ig(e,t,r){return e||(e=ri(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function iv(e,t){for(var r=0;r<=e.SheetNames.length;++r){var a=e.Sheets[e.SheetNames[r]];a&&a["!ref"]&&rw(a["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}var n=t||{};switch(n.biff||2){case 8:case 5:var s=t||{},i=[];e&&!e.SSF&&(e.SSF=eV(j)),e&&e.SSF&&(eE(),ew(e.SSF),s.revssf=eR(e.SSF),s.revssf[e.SSF[65535]]=0,s.ssf=e.SSF),s.Strings=[],s.Strings.Count=0,s.Strings.Unique=0,iX(s),s.cellXfs=[],sF(s.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var c=0;c<e.SheetNames.length;++c)i[i.length]=function(e,t,r){var a,n,s,i,c,o,l,f,h,u=ro(),d=r.SheetNames[e],p=r.Sheets[d]||{},m=(r||{}).Workbook||{},g=(m.Sheets||[])[e]||{},v=Array.isArray(p),b=8==t.biff,T="",w=[],E=rS(p["!ref"]||"A1"),S=b?65536:16384;if(E.e.c>255||E.e.r>=S){if(t.WTF)throw Error("Range "+(p["!ref"]||"A1")+" exceeds format limit A1:IV16384");E.e.c=Math.min(E.e.c,255),E.e.r=Math.min(E.e.c,S-1)}im(u,2057,aK(r,16,t)),im(u,13,ax(1)),im(u,12,ax(100)),im(u,15,ay(!0)),im(u,17,ay(!1)),im(u,16,rV(.001)),im(u,95,ay(!0)),im(u,42,ay(!1)),im(u,43,ay(!1)),im(u,130,ax(1)),im(u,128,(a=[0,0],(n=ri(8)).write_shift(4,0),n.write_shift(2,a[0]?a[0]+1:0),n.write_shift(2,a[1]?a[1]+1:0),n)),im(u,131,ay(!1)),im(u,132,ay(!1)),b&&function(e,t){if(t){var r=0;t.forEach(function(t,a){var n,s,i;++r<=256&&t&&im(e,125,(n=sN(a,t),(s=ri(12)).write_shift(2,a),s.write_shift(2,a),s.write_shift(2,256*n.width),s.write_shift(2,0),i=0,n.hidden&&(i|=1),s.write_shift(1,i),i=n.level||0,s.write_shift(1,i),s.write_shift(2,0),s))})}}(u,p["!cols"]),im(u,512,((i=ri(2*(s=8!=t.biff&&t.biff?2:4)+6)).write_shift(s,E.s.r),i.write_shift(s,E.e.r+1),i.write_shift(2,E.s.c),i.write_shift(2,E.e.c+1),i.write_shift(2,0),i)),b&&(p["!links"]=[]);for(var k=E.s.r;k<=E.e.r;++k){T=rm(k);for(var A=E.s.c;A<=E.e.c;++A){k===E.s.r&&(w[A]=rv(A)),h=w[A]+T;var y=v?(p[k]||[])[A]:p[h];y&&(!function(e,t,r,a,n){var s=16+sF(n.cellXfs,t,n);if(null==t.v&&!t.bf)return im(e,513,aW(r,a,s));if(t.bf)im(e,6,function(e,t,r,a,n){var s=aW(t,r,n),i=function(e){if(null==e){var t=ri(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return"number"==typeof e?rV(e):rV(0)}(e.v),c=ri(6);c.write_shift(2,33),c.write_shift(4,0);for(var o=ri(e.bf.length),l=0;l<e.bf.length;++l)o[l]=e.bf[l];return F([s,i,c,o])}(t,r,a,0,s));else switch(t.t){case"d":case"n":var i,c="d"==t.t?eN(eW(t.v)):t.v;im(e,515,(aW(r,a,s,i=ri(14)),rV(c,i),i));break;case"b":case"e":im(e,517,(o=t.v,l=t.t,aW(r,a,s,f=ri(8)),aO(o,l,f),f));break;case"s":case"str":if(n.bookSST){var o,l,f,h,u,d,p,m=sI(n.Strings,t.v,n.revStrings);im(e,253,(aW(r,a,s,p=ri(10)),p.write_shift(4,m),p))}else im(e,516,(h=(t.v||"").slice(0,255),aW(r,a,s,d=ri(8+ +(u=!n||8==n.biff)+(1+u)*h.length)),d.write_shift(2,h.length),u&&d.write_shift(1,1),d.write_shift((1+u)*h.length,h,u?"utf16le":"sbcs"),d));break;default:im(e,513,aW(r,a,s))}}(u,y,k,A,t),b&&y.l&&p["!links"].push([h,y.l]))}}var _=g.CodeName||g.name||d;return b&&im(u,574,(c=(m.Views||[])[0],o=ri(18),l=1718,c&&c.RTL&&(l|=64),o.write_shift(2,l),o.write_shift(4,0),o.write_shift(4,64),o.write_shift(4,0),o.write_shift(4,0),o)),b&&(p["!merges"]||[]).length&&im(u,229,function(e){var t=ri(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)az(e[r],t);return t}(p["!merges"])),b&&function(e,t){for(var r=0;r<t["!links"].length;++r){var a=t["!links"][r];im(e,440,function(e){var t=ri(24),r=rb(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)t.write_shift(1,parseInt(a[n],16));return F([t,function(e){var t=ri(512),r=0,a=e.Target;"file://"==a.slice(0,7)&&(a=a.slice(7));var n=a.indexOf("#"),s=n>-1?31:23;switch(a.charAt(0)){case"#":s=28;break;case".":s&=-3}t.write_shift(4,2),t.write_shift(4,s);var i=[8,6815827,6619237,4849780,83];for(r=0;r<i.length;++r)t.write_shift(4,i[r]);if(28==s)aL(a=a.slice(1),t);else if(2&s){for(r=0,i="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" ");r<i.length;++r)t.write_shift(1,parseInt(i[r],16));var c=n>-1?a.slice(0,n):a;for(t.write_shift(4,2*(c.length+1)),r=0;r<c.length;++r)t.write_shift(2,c.charCodeAt(r));t.write_shift(2,0),8&s&&aL(n>-1?a.slice(n+1):"",t)}else{for(r=0,i="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" ");r<i.length;++r)t.write_shift(1,parseInt(i[r],16));for(var o=0;"../"==a.slice(3*o,3*o+3)||"..\\"==a.slice(3*o,3*o+3);)++o;for(t.write_shift(2,o),t.write_shift(4,a.length-3*o+1),r=0;r<a.length-3*o;++r)t.write_shift(1,255&a.charCodeAt(r+3*o));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}(e[1])])}(a)),a[1].Tooltip&&im(e,2048,function(e){var t=e[1].Tooltip,r=ri(10+2*(t.length+1));r.write_shift(2,2048);var a=rb(e[0]);r.write_shift(2,a.r),r.write_shift(2,a.r),r.write_shift(2,a.c),r.write_shift(2,a.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}(a))}delete t["!links"]}(u,p),im(u,442,aF(_,t)),b&&((f=ri(19)).write_shift(4,2151),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(2,3),f.write_shift(1,1),f.write_shift(4,0),im(u,2151,f),(f=ri(39)).write_shift(4,2152),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(2,3),f.write_shift(1,0),f.write_shift(4,0),f.write_shift(2,1),f.write_shift(4,4),f.write_shift(2,0),az(rS(p["!ref"]||"A1"),f),f.write_shift(4,4),im(u,2152,f)),im(u,10),u.end()}(c,s,e);return i.unshift(function(e,t,r){var a,n,s,i,c,o,l,f=ro(),h=(e||{}).Workbook||{},u=h.Sheets||[],d=h.WBProps||{},p=8==r.biff,m=5==r.biff;im(f,2057,aK(e,5,r)),"xla"==r.bookType&&im(f,135),im(f,225,p?ax(1200):null),im(f,193,function(e,t){t||(t=ri(2));for(var r=0;r<2;++r)t.write_shift(1,0);return t}(2)),m&&im(f,191),m&&im(f,192),im(f,226),im(f,92,function(e,t){var r=!t||8==t.biff,a=ri(r?112:54);for(a.write_shift(8==t.biff?2:1,7),r&&a.write_shift(1,0),a.write_shift(4,0x33336853),a.write_shift(4,5458548|0x20000000*!r);a.l<a.length;)a.write_shift(1,32*!r);return a}(0,r)),im(f,66,ax(p?1200:1252)),p&&im(f,353,ax(0)),p&&im(f,448),im(f,317,function(e){for(var t=ri(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),p&&e.vbaraw&&im(f,211),p&&e.vbaraw&&im(f,442,aF(d.CodeName||"ThisWorkbook",r)),im(f,156,ax(17)),im(f,25,ay(!1)),im(f,18,ay(!1)),im(f,19,ax(0)),p&&im(f,431,ay(!1)),p&&im(f,444,ax(0)),im(f,61,((a=ri(18)).write_shift(2,0),a.write_shift(2,0),a.write_shift(2,29280),a.write_shift(2,17600),a.write_shift(2,56),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,1),a.write_shift(2,500),a)),im(f,64,ay(!1)),im(f,141,ax(0)),im(f,34,ay("true"==(e.Workbook&&e.Workbook.WBProps&&td(e.Workbook.WBProps.date1904)?"true":"false"))),im(f,14,ay(!0)),p&&im(f,439,ay(!1)),im(f,218,ax(0)),im(f,49,(s=(n={sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"}).name||"Arial",(c=ri((i=r&&5==r.biff)?15+s.length:16+2*s.length)).write_shift(2,20*(n.sz||12)),c.write_shift(4,0),c.write_shift(2,400),c.write_shift(4,0),c.write_shift(2,0),c.write_shift(1,s.length),i||c.write_shift(1,1),c.write_shift((i?1:2)*s.length,s,i?"sbcs":"utf16le"),c)),o=e.SSF,o&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=o[t]&&im(f,1054,function(e,t,r,a){var n=r&&5==r.biff;a||(a=ri(n?3+t.length:5+2*t.length)),a.write_shift(2,e),a.write_shift(n?1:2,t.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*t.length,t,n?"sbcs":"utf16le");var s=a.length>a.l?a.slice(0,a.l):a;return null==s.l&&(s.l=s.length),s}(t,o[t],r))});for(var g=0;g<16;++g)im(f,224,aZ({numFmtId:0,style:!0},0,r));r.cellXfs.forEach(function(e){im(f,224,aZ(e,0,r))}),p&&im(f,352,ay(!1));var v=f.end(),b=ro();p&&im(b,140,(l||(l=ri(4)),l.write_shift(2,1),l.write_shift(2,1),l)),p&&r.Strings&&function(e,t,r,a){var n=(r||[]).length||0;if(n<=8224)return im(e,252,r,n);if(!isNaN(252)){for(var s=r.parts||[],i=0,c=0,o=0;o+(s[i]||8224)<=8224;)o+=s[i]||8224,i++;var l=e.next(4);for(l.write_shift(2,t),l.write_shift(2,o),e.push(r.slice(c,c+o)),c+=o;c<n;){for((l=e.next(4)).write_shift(2,60),o=0;o+(s[i]||8224)<=8224;)o+=s[i]||8224,i++;l.write_shift(2,o),e.push(r.slice(c,c+o)),c+=o}}}(b,252,function(e,t){var r=ri(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var a=[],n=0;n<e.length;++n)a[n]=function(e){var t=e.t||"",r=ri(3);r.write_shift(2,t.length),r.write_shift(1,1);var a=ri(2*t.length);return a.write_shift(2*t.length,t,"utf16le"),F([r,a])}(e[n],t);var s=F([r].concat(a));return s.parts=[r.length].concat(a.map(function(e){return e.length})),s}(r.Strings,r)),im(b,10);var T=b.end(),w=ro(),E=0,S=0;for(S=0;S<e.SheetNames.length;++S)E+=(p?12:11)+(p?2:1)*e.SheetNames[S].length;var k=v.length+E+T.length;for(S=0;S<e.SheetNames.length;++S)im(w,133,function(e,t){var r=!t||t.biff>=8?2:1,a=ri(8+r*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),t.biff>=8&&a.write_shift(1,1),a.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}({pos:k,hs:(u[S]||{}).Hidden||0,dt:0,name:e.SheetNames[S]},r)),k+=t[S].length;var A=w.end();if(E!=A.length)throw Error("BS8 "+E+" != "+A.length);var y=[];return v.length&&y.push(v),A.length&&y.push(A),T.length&&y.push(T),F(y)}(e,i,s)),F(i);case 4:case 3:case 2:for(var o=t||{},l=ro(),f=0,h=0;h<e.SheetNames.length;++h)e.SheetNames[h]==o.sheet&&(f=h);if(0==f&&o.sheet&&e.SheetNames[0]!=o.sheet)throw Error("Sheet not found: "+o.sheet);return im(l,4==o.biff?1033:3==o.biff?521:9,aK(e,16,o)),!function(e,t,r,a){var n,s=Array.isArray(t),i=rS(t["!ref"]||"A1"),c="",o=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),n=rE(i)}for(var l=i.s.r;l<=i.e.r;++l){c=rm(l);for(var f=i.s.c;f<=i.e.c;++f){l===i.s.r&&(o[f]=rv(f)),n=o[f]+c;var h=s?(t[l]||[])[f]:t[n];h&&function(e,t,r,a){if(null!=t.v)switch(t.t){case"d":case"n":var n,s,i,c,o,l,f,h="d"==t.t?eN(eW(t.v)):t.v;h==(0|h)&&h>=0&&h<65536?im(e,2,(ig(l=ri(9),r,a),l.write_shift(2,h),l)):im(e,3,(ig(f=ri(15),r,a),f.write_shift(8,h,"f"),f));return;case"b":case"e":im(e,5,(n=t.v,s=t.t,ig(i=ri(9),r,a),aO(n,s||"b",i),i));return;case"s":case"str":im(e,4,(ig(o=ri(8+2*(c=(t.v||"").slice(0,255)).length),r,a),o.write_shift(1,c.length),o.write_shift(c.length,c,"sbcs"),o.l<o.length?o.slice(0,o.l):o));return}im(e,1,ig(null,r,a))}(e,h,l,f,a)}}}(l,e.Sheets[e.SheetNames[f]],0,o,e),im(l,10),l.end()}throw Error("invalid type "+n.bookType+" for BIFF")}function ib(e,t){var r=t||{},a=r.dense?[]:{},n=(e=e.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!n)throw Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,c=s&&s.index||e.length,o=eX(e.slice(i,c),/(:?<tr[^>]*>)/i,"<tr>"),l=-1,f=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<o.length;++i){var m=o[i].trim(),g=m.slice(0,3).toLowerCase();if("<tr"==g){if(++l,r.sheetRows&&r.sheetRows<=l){--l;break}f=0;continue}if("<td"==g||"<th"==g){var v=m.split(/<\/t[dh]>/i);for(c=0;c<v.length;++c){var b=v[c].trim();if(b.match(/<t[dh]/i)){for(var T=b,w=0;"<"==T.charAt(0)&&(w=T.indexOf(">"))>-1;)T=T.slice(w+1);for(var E=0;E<p.length;++E){var S=p[E];S.s.c==f&&S.s.r<l&&l<=S.e.r&&(f=S.e.c+1,E=-1)}var k=tt(b.slice(0,b.indexOf(">")));u=k.colspan?+k.colspan:1,((h=+k.rowspan)>1||u>1)&&p.push({s:{r:l,c:f},e:{r:l+(h||1)-1,c:f+u-1}});var A=k.t||k["data-t"]||"";if(!T.length||(T=tE(T),d.s.r>l&&(d.s.r=l),d.e.r<l&&(d.e.r=l),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),!T.length)){f+=u;continue}var y={t:"s",v:T};r.raw||!T.trim().length||"s"==A||("TRUE"===T?y={t:"b",v:!0}:"FALSE"===T?y={t:"b",v:!1}:isNaN(eG(T))?isNaN(e$(T).getDate())||(y={t:"d",v:eW(T)},r.cellDates||(y={t:"n",v:eN(y.v)}),y.z=r.dateNF||j[14]):y={t:"n",v:eG(T)}),r.dense?(a[l]||(a[l]=[]),a[l][f]=y):a[rT({r:l,c:f})]=y,f+=u}}}}return a["!ref"]=rE(d),p.length&&(a["!merges"]=p),a}function iT(e,t){var r,a,n,s=t||{},i=null!=s.header?s.header:'<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',c=null!=s.footer?s.footer:"</body></html>",o=[i],l=rw(e["!ref"]);s.dense=Array.isArray(e),o.push((r=0,a=0,"<table"+((n=s)&&n.id?' id="'+n.id+'"':"")+">"));for(var f=l.s.r;f<=l.e.r;++f)o.push(function(e,t,r,a){for(var n=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var c=0,o=0,l=0;l<n.length;++l)if(!(n[l].s.r>r)&&!(n[l].s.c>i)&&!(n[l].e.r<r)&&!(n[l].e.c<i)){if(n[l].s.r<r||n[l].s.c<i){c=-1;break}c=n[l].e.r-n[l].s.r+1,o=n[l].e.c-n[l].s.c+1;break}if(!(c<0)){var f=rT({r:r,c:i}),h=a.dense?(e[r]||[])[i]:e[f],u=h&&null!=h.v&&(h.h||th(h.w||(rA(h),h.w)||""))||"",d={};c>1&&(d.rowspan=c),o>1&&(d.colspan=o),a.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(a.id||"sjs")+"-"+f,s.push(tO("td",u,d))}}return"<tr>"+s.join("")+"</tr>"}(e,l,f,s));return o.push("</table>"+c),o.join("")}function iw(e,t,r){var a=r||{},n=0,s=0;if(null!=a.origin)if("number"==typeof a.origin)n=a.origin;else{var i="string"==typeof a.origin?rb(a.origin):a.origin;n=i.r,s=i.c}var c=t.getElementsByTagName("tr"),o=Math.min(a.sheetRows||1e7,c.length),l={s:{r:0,c:0},e:{r:n,c:s}};if(e["!ref"]){var f=rw(e["!ref"]);l.s.r=Math.min(l.s.r,f.s.r),l.s.c=Math.min(l.s.c,f.s.c),l.e.r=Math.max(l.e.r,f.e.r),l.e.c=Math.max(l.e.c,f.e.c),-1==n&&(l.e.r=n=f.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,g=0,v=0,b=0,T=0;for(e["!cols"]||(e["!cols"]=[]);p<c.length&&m<o;++p){var w=c[p];if(iS(w)){if(a.display)continue;d[m]={hidden:!0}}var E=w.children;for(g=v=0;g<E.length;++g){var S=E[g];if(!(a.display&&iS(S))){var k=S.hasAttribute("data-v")?S.getAttribute("data-v"):S.hasAttribute("v")?S.getAttribute("v"):tE(S.innerHTML),A=S.getAttribute("data-z")||S.getAttribute("z");for(u=0;u<h.length;++u){var y=h[u];y.s.c==v+s&&y.s.r<m+n&&m+n<=y.e.r&&(v=y.e.c+1-s,u=-1)}T=+S.getAttribute("colspan")||1,((b=+S.getAttribute("rowspan")||1)>1||T>1)&&h.push({s:{r:m+n,c:v+s},e:{r:m+n+(b||1)-1,c:v+s+(T||1)-1}});var _={t:"s",v:k},x=S.getAttribute("data-t")||S.getAttribute("t")||"";null!=k&&(0==k.length?_.t=x||"z":a.raw||0==k.trim().length||"s"==x||("TRUE"===k?_={t:"b",v:!0}:"FALSE"===k?_={t:"b",v:!1}:isNaN(eG(k))?isNaN(e$(k).getDate())||(_={t:"d",v:eW(k)},a.cellDates||(_={t:"n",v:eN(_.v)}),_.z=a.dateNF||j[14]):_={t:"n",v:eG(k)})),void 0===_.z&&null!=A&&(_.z=A);var C="",O=S.getElementsByTagName("A");if(O&&O.length)for(var R=0;R<O.length&&(!O[R].hasAttribute("href")||"#"==(C=O[R].getAttribute("href")).charAt(0));++R);C&&"#"!=C.charAt(0)&&(_.l={Target:C}),a.dense?(e[m+n]||(e[m+n]=[]),e[m+n][v+s]=_):e[rT({c:v+s,r:m+n})]=_,l.e.c<v+s&&(l.e.c=v+s),v+=T}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),l.e.r=Math.max(l.e.r,m-1+n),e["!ref"]=rE(l),m>=o&&(e["!fullref"]=rE((l.e.r=c.length-p+m-1+n,l))),e}function iE(e,t){return iw((t||{}).dense?[]:{},e,t)}function iS(e){var t,r="",a=(t=e).ownerDocument.defaultView&&"function"==typeof t.ownerDocument.defaultView.getComputedStyle?t.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null;return a&&(r=a(e).getPropertyValue("display")),r||(r=e.style&&e.style.display),"none"===r}var ik={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function iA(e,t){var r=t||{},a,n,s,i,c,o,l,f=tI(e),h=[],u={name:""},d="",p=0,m={},g=[],v=r.dense?[]:{},b={value:""},T="",w=0,E=[],S=-1,k=-1,A={s:{r:1e6,c:1e7},e:{r:0,c:0}},y=0,_={},x=[],C={},O=0,R=0,I=[],N=1,D=1,F=[],P={Names:[]},L={},M=["",""],U=[],B={},W="",H=0,V=!1,z=!1,G=0;for(tN.lastIndex=0,f=f.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");c=tN.exec(f);)switch(c[3]=c[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===c[1]?(A.e.c>=A.s.c&&A.e.r>=A.s.r?v["!ref"]=rE(A):v["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=A.e.r&&(v["!fullref"]=v["!ref"],A.e.r=r.sheetRows-1,v["!ref"]=rE(A)),x.length&&(v["!merges"]=x),I.length&&(v["!rows"]=I),s.name=s["名称"]||s.name,"undefined"!=typeof JSON&&JSON.stringify(s),g.push(s.name),m[s.name]=v,z=!1):"/"!==c[0].charAt(c[0].length-2)&&(s=tt(c[0],!1),S=k=-1,A.s.r=A.s.c=1e7,A.e.r=A.e.c=0,v=r.dense?[]:{},x=[],I=[],z=!0);break;case"table-row-group":"/"===c[1]?--y:++y;break;case"table-row":case"行":if("/"===c[1]){S+=N,N=1;break}if((i=tt(c[0],!1))["行号"]?S=i["行号"]-1:-1==S&&(S=0),(N=+i["number-rows-repeated"]||1)<10)for(G=0;G<N;++G)y>0&&(I[S+G]={level:y});k=-1;break;case"covered-table-cell":"/"!==c[1]&&++k,r.sheetStubs&&(r.dense?(v[S]||(v[S]=[]),v[S][k]={t:"z"}):v[rT({r:S,c:k})]={t:"z"}),T="",E=[];break;case"table-cell":case"数据":if("/"===c[0].charAt(c[0].length-2))++k,D=parseInt((b=tt(c[0],!1))["number-columns-repeated"]||"1",10),o={t:"z",v:null},b.formula&&!1!=r.cellFormula&&(o.f=s_(ts(b.formula))),"string"==(b["数据类型"]||b["value-type"])&&(o.t="s",o.v=ts(b["string-value"]||""),r.dense?(v[S]||(v[S]=[]),v[S][k]=o):v[rT({r:S,c:k})]=o),k+=D-1;else if("/"!==c[1]){T="",w=0,E=[],D=1;var j=N?S+N-1:S;if(++k>A.e.c&&(A.e.c=k),k<A.s.c&&(A.s.c=k),S<A.s.r&&(A.s.r=S),j>A.e.r&&(A.e.r=j),b=tt(c[0],!1),U=[],B={},o={t:b["数据类型"]||b["value-type"],v:null},r.cellFormula)if(b.formula&&(b.formula=ts(b.formula)),b["number-matrix-columns-spanned"]&&b["number-matrix-rows-spanned"]&&(C={s:{r:S,c:k},e:{r:S+(O=parseInt(b["number-matrix-rows-spanned"],10)||0)-1,c:k+(parseInt(b["number-matrix-columns-spanned"],10)||0)-1}},o.F=rE(C),F.push([C,o.F])),b.formula)o.f=s_(b.formula);else for(G=0;G<F.length;++G)S>=F[G][0].s.r&&S<=F[G][0].e.r&&k>=F[G][0].s.c&&k<=F[G][0].e.c&&(o.F=F[G][1]);switch((b["number-columns-spanned"]||b["number-rows-spanned"])&&(C={s:{r:S,c:k},e:{r:S+(O=parseInt(b["number-rows-spanned"],10)||0)-1,c:k+(parseInt(b["number-columns-spanned"],10)||0)-1}},x.push(C)),b["number-columns-repeated"]&&(D=parseInt(b["number-columns-repeated"],10)),o.t){case"boolean":o.t="b",o.v=td(b["boolean-value"]);break;case"float":case"percentage":case"currency":o.t="n",o.v=parseFloat(b.value);break;case"date":o.t="d",o.v=eW(b["date-value"]),r.cellDates||(o.t="n",o.v=eN(o.v)),o.z="m/d/yy";break;case"time":o.t="n",o.v=function(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(r=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(a)r*=60;else throw Error("Unsupported ISO Duration Field: M")}t+=r*parseInt(n[s],10)}return t}(b["time-value"])/86400,r.cellDates&&(o.t="d",o.v=eL(o.v)),o.z="HH:MM:SS";break;case"number":o.t="n",o.v=parseFloat(b["数据数值"]);break;default:if("string"!==o.t&&"text"!==o.t&&o.t)throw Error("Unsupported value type "+o.t);o.t="s",null!=b["string-value"]&&(T=ts(b["string-value"]),E=[])}}else{if(V=!1,"s"===o.t&&(o.v=T||"",E.length&&(o.R=E),V=0==w),L.Target&&(o.l=L),U.length>0&&(o.c=U,U=[]),T&&!1!==r.cellText&&(o.w=T),V&&(o.t="z",delete o.v),(!V||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=S))for(var X=0;X<N;++X){if(D=parseInt(b["number-columns-repeated"]||"1",10),r.dense)for(v[S+X]||(v[S+X]=[]),v[S+X][k]=0==X?o:eV(o);--D>0;)v[S+X][k+D]=eV(o);else for(v[rT({r:S+X,c:k})]=o;--D>0;)v[rT({r:S+X,c:k+D})]=eV(o);A.e.c<=k&&(A.e.c=k)}k+=(D=parseInt(b["number-columns-repeated"]||"1",10))-1,D=0,o={},T="",E=[]}L={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===c[1]){if((a=h.pop())[0]!==c[3])throw"Bad state: "+a}else"/"!==c[0].charAt(c[0].length-2)&&h.push([c[3],!0]);break;case"annotation":if("/"===c[1]){if((a=h.pop())[0]!==c[3])throw"Bad state: "+a;B.t=T,E.length&&(B.R=E),B.a=W,U.push(B)}else"/"!==c[0].charAt(c[0].length-2)&&h.push([c[3],!1]);W="",H=0,T="",w=0,E=[];break;case"creator":"/"===c[1]?W=f.slice(H,c.index):H=c.index+c[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===c[1]){if((a=h.pop())[0]!==c[3])throw"Bad state: "+a}else"/"!==c[0].charAt(c[0].length-2)&&h.push([c[3],!1]);T="",w=0,E=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===c[1]){if(_[u.name]=d,(a=h.pop())[0]!==c[3])throw"Bad state: "+a}else"/"!==c[0].charAt(c[0].length-2)&&(d="",u=tt(c[0],!1),h.push([c[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(h[h.length-1][0]){case"time-style":case"date-style":n=tt(c[0],!1),d+=ik[c[3]][+("long"===n.style)]}break;case"text":if("/>"===c[0].slice(-2));else if("/"===c[1])switch(h[h.length-1][0]){case"number-style":case"date-style":case"time-style":d+=f.slice(p,c.index)}else p=c.index+c[0].length;break;case"named-range":M=sx((n=tt(c[0],!1))["cell-range-address"]);var Y={Name:n.name,Ref:M[0]+"!"+M[1]};z&&(Y.Sheet=g.length),P.Names.push(Y);break;case"p":case"文本串":if(["master-styles"].indexOf(h[h.length-1][0])>-1)break;if("/"!==c[1]||b&&b["string-value"])tt(c[0],!1),w=c.index+c[0].length;else{var K=[ts(f.slice(w,c.index).replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(e,t){return Array(parseInt(t,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,"\n").replace(/<[^>]*>/g,""))];T=(T.length>0?T+"\n":"")+K[0]}break;case"database-range":if("/"===c[1])break;try{m[(M=sx(tt(c[0])["target-range-address"]))[0]]["!autofilter"]={ref:M[1]}}catch(e){}break;case"a":if("/"!==c[1]){if(!(L=tt(c[0],!1)).href)break;L.Target=ts(L.href),delete L.href,"#"==L.Target.charAt(0)&&L.Target.indexOf(".")>-1?(M=sx(L.Target.slice(1)),L.Target="#"+M[0]+"!"+M[1]):L.Target.match(/^\.\.[\\\/]/)&&(L.Target=L.Target.slice(3))}break;default:switch(c[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw Error(c)}}var J={Sheets:m,SheetNames:g,Workbook:P};return r.bookSheets&&delete J.Sheets,J}function iy(e,t){if("fods"==t.bookType)return(null)(e,t);var r=e2(),a="",n=[],s=[];return e0(r,a="mimetype","application/vnd.oasis.opendocument.spreadsheet"),e0(r,a="content.xml",(null)(e,t)),n.push([a,"text/xml"]),s.push([a,"ContentFile"]),e0(r,a="styles.xml",(null)(e,t)),n.push([a,"text/xml"]),s.push([a,"StylesFile"]),e0(r,a="meta.xml",e5+'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+f.version+"</meta:generator></office:meta></office:document-meta>"),n.push([a,"text/xml"]),s.push([a,"MetadataFile"]),e0(r,a="manifest.rdf",function(e){var t=[e5];t.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var r=0;r!=e.length;++r)t.push(r7(e[r][0],e[r][1])),t.push(['  <rdf:Description rdf:about="">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+e[r][0]+'"/>\n',"  </rdf:Description>\n"].join(""));return t.push(r7("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}(s)),n.push([a,"application/rdf+xml"]),e0(r,a="META-INF/manifest.xml",function(e){var t=[e5];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(n)),r}function i_(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function ix(e){return"undefined"!=typeof TextDecoder?new TextDecoder().decode(e):tb(N(e))}function iC(e){var t=new Uint8Array(e.reduce(function(e,t){return e+t.length},0)),r=0;return e.forEach(function(e){t.set(e,r),r+=e.length}),t}function iO(e){return e-=e>>1&0x55555555,((e=(0x33333333&e)+(e>>2&0x33333333))+(e>>4)&0xf0f0f0f)*0x1010101>>>24}function iR(e,t){var r=t?t[0]:0,a=127&e[r];r:if(e[r++]>=128&&(a|=(127&e[r])<<7,e[r++]<128||(a|=(127&e[r])<<14,e[r++]<128)||(a|=(127&e[r])<<21,e[r++]<128)||(a+=(127&e[r])*0x10000000,++r,e[r++]<128)||(a+=(127&e[r])*0x800000000,++r,e[r++]<128)||(a+=(127&e[r])*0x40000000000,++r,e[r++]<128)))break r;return t&&(t[0]=r),a}function iI(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;a:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=0xfffffff)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=0x7ffffffff)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=0x3ffffffffff))break a;t[r-1]|=128,t[r]=e/0x1000000>>>21&127,++r}return t.slice(0,r)}function iN(e){var t=0,r=127&e[0];r:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128||(r|=(127&e[t])<<14,e[t++]<128)||(r|=(127&e[t])<<21,e[t++]<128))break r;r|=(127&e[t])<<28}return r}function iD(e){for(var t=[],r=[0];r[0]<e.length;){var a,n=r[0],s=iR(e,r),i=7&s,c=0;if(0==(s=Math.floor(s/8)))break;switch(i){case 0:for(var o=r[0];e[r[0]++]>=128;);a=e.slice(o,r[0]);break;case 5:c=4,a=e.slice(r[0],r[0]+c),r[0]+=c;break;case 1:c=8,a=e.slice(r[0],r[0]+c),r[0]+=c;break;case 2:c=iR(e,r),a=e.slice(r[0],r[0]+c),r[0]+=c;break;default:throw Error("PB Type ".concat(i," for Field ").concat(s," at offset ").concat(n))}var l={data:a,type:i};null==t[s]?t[s]=[l]:t[s].push(l)}return t}function iF(e){var t=[];return e.forEach(function(e,r){e.forEach(function(e){e.data&&(t.push(iI(8*r+e.type)),2==e.type&&t.push(iI(e.data.length)),t.push(e.data))})}),iC(t)}function iP(e,t){return(null==e?void 0:e.map(function(e){return t(e.data)}))||[]}function iL(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=iR(e,a),s=iD(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:iN(s[1][0].data),messages:[]};s[2].forEach(function(t){var r=iD(t.data),n=iN(r[3][0].data);i.messages.push({meta:r,data:e.slice(a[0],a[0]+n)}),a[0]+=n}),(null==(t=s[3])?void 0:t[0])&&(i.merge=iN(s[3][0].data)>>>0>0),r.push(i)}return r}function iM(e){var t=[];return e.forEach(function(e){var r=[];r[1]=[{data:iI(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:iI(+!!e.merge),type:0}]);var a=[];e.messages.forEach(function(e){a.push(e.data),e.meta[3]=[{type:0,data:iI(e.data.length)}],r[2].push({data:iF(e.meta),type:2})});var n=iF(r);t.push(iI(n.length)),t.push(n),a.forEach(function(e){return t.push(e)})}),iC(t)}function iU(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(function(e,t){if(0!=e)throw Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=iR(t,r),n=[];r[0]<t.length;){var s=3&t[r[0]];if(0==s){var i=t[r[0]++]>>2;if(i<60)++i;else{var c=i-59;i=t[r[0]],c>1&&(i|=t[r[0]+1]<<8),c>2&&(i|=t[r[0]+2]<<16),c>3&&(i|=t[r[0]+3]<<24),i>>>=0,i++,r[0]+=c}n.push(t.slice(r[0],r[0]+i)),r[0]+=i;continue}var o=0,l=0;if(1==s?(l=(t[r[0]]>>2&7)+4,o=(224&t[r[0]++])<<3|t[r[0]++]):(l=(t[r[0]++]>>2)+1,2==s?(o=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(o=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[iC(n)],0==o)throw Error("Invalid offset 0");if(o>n[0].length)throw Error("Invalid offset beyond length");if(l>=o)for(n.push(n[0].slice(-o)),l-=o;l>=n[n.length-1].length;)n.push(n[n.length-1]),l-=n[n.length-1].length;n.push(n[0].slice(-o,-o+l))}var f=iC(n);if(f.length!=a)throw Error("Unexpected length: ".concat(f.length," != ").concat(a));return f}(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw Error("data is not a valid framed stream!");return iC(t)}function iB(e){for(var t=[],r=0;r<e.length;){var a=Math.min(e.length-r,0xfffffff),n=new Uint8Array(4);t.push(n);var s=iI(a),i=s.length;t.push(s),a<=60?(i++,t.push(new Uint8Array([a-1<<2]))):a<=256?(i+=2,t.push(new Uint8Array([240,a-1&255]))):a<=65536?(i+=3,t.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=0x1000000?(i+=4,t.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=0x100000000&&(i+=5,t.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),t.push(e.slice(r,r+a)),i+=a,n[0]=0,n[1]=255&i,n[2]=i>>8&255,n[3]=i>>16&255,r+=a}return iC(t)}function iW(e,t){var r=new Uint8Array(32),a=i_(r),n=12,s=0;switch(r[0]=5,e.t){case"n":r[1]=2,function(e,t,r){var a=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,n=r/Math.pow(10,a-6176);e[t+15]|=a>>7,e[t+14]|=(127&a)<<1;for(var s=0;n>=1;++s,n/=256)e[t+s]=255&n;e[t+15]|=r>=0?0:128}(r,n,e.v),s|=1,n+=16;break;case"b":r[1]=6,a.setFloat64(n,+!!e.v,!0),s|=2,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[1]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=8,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,s,!0),r.slice(0,n)}function iH(e,t){var r=new Uint8Array(32),a=i_(r),n=12,s=0;switch(r[0]=3,e.t){case"n":r[2]=2,a.setFloat64(n,e.v,!0),s|=32,n+=8;break;case"b":r[2]=6,a.setFloat64(n,+!!e.v,!0),s|=32,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[2]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=16,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(4,s,!0),r.slice(0,n)}function iV(e){return iR(iD(e)[1][0].data)}function iz(e,t){var r=iD(t.data),a=iN(r[1][0].data),n=r[3],s=[];return(n||[]).forEach(function(t){var r=iD(t.data),n=iN(r[1][0].data)>>>0;switch(a){case 1:s[n]=ix(r[3][0].data);break;case 8:var i=iD(e[iV(r[9][0].data)][0].data),c=e[iV(i[1][0].data)][0],o=iN(c.meta[1][0].data);if(2001!=o)throw Error("2000 unexpected reference to ".concat(o));var l=iD(c.data);s[n]=l[3].map(function(e){return ix(e.data)}).join("")}}),s}function iG(e){var t,r,a,n,s={},i=[];if(e.FullPaths.forEach(function(e){if(e.match(/\.iwpv2/))throw Error("Unsupported password protection")}),e.FileIndex.forEach(function(e){var t,r;if(e.name.match(/\.iwa$/)){try{t=iU(e.content)}catch(t){return console.log("?? "+e.content.length+" "+(t.message||t))}try{r=iL(t)}catch(e){return console.log("## "+(e.message||e))}r.forEach(function(e){s[e.id]=e.messages,i.push(e.id)})}}),!i.length)throw Error("File has no messages");var c=(null==(n=null==(a=null==(r=null==(t=null==s?void 0:s[1])?void 0:t[0])?void 0:r.meta)?void 0:a[1])?void 0:n[0].data)&&1==iN(s[1][0].meta[1][0].data)&&s[1][0];if(c||i.forEach(function(e){s[e].forEach(function(e){if(1==iN(e.meta[1][0].data)>>>0)if(c)throw Error("Document has multiple roots");else c=e})}),!c)throw Error("Cannot find Document root");var o=c,l=i8();if(iP(iD(o.data)[1],iV).forEach(function(e){s[e].forEach(function(e){if(2==iN(e.meta[1][0].data)){var t,r,a,n=(a={name:(null==(t=(r=iD(e.data))[1])?void 0:t[0])?ix(r[1][0].data):"",sheets:[]},iP(r[2],iV).forEach(function(e){s[e].forEach(function(e){6e3==iN(e.meta[1][0].data)&&a.sheets.push(function(e,t){var r=iD(t.data),a={"!ref":"A1"},n=e[iV(r[2][0].data)],s=iN(n[0].meta[1][0].data);if(6001!=s)throw Error("6000 unexpected reference to ".concat(s));return!function(e,t,r){var a,n=iD(t.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(iN(n[6][0].data)>>>0)-1,s.e.r<0)throw Error("Invalid row varint ".concat(n[6][0].data));if(s.e.c=(iN(n[7][0].data)>>>0)-1,s.e.c<0)throw Error("Invalid col varint ".concat(n[7][0].data));r["!ref"]=rE(s);var i=iD(n[4][0].data),c=iz(e,e[iV(i[4][0].data)][0]),o=(null==(a=i[17])?void 0:a[0])?iz(e,e[iV(i[17][0].data)][0]):[],l=iD(i[3][0].data),f=0;l[1].forEach(function(t){var a,n,s,i,l=e[iV(iD(t.data)[2][0].data)][0],h=iN(l.meta[1][0].data);if(6002!=h)throw Error("6001 unexpected reference to ".concat(h));var u=(s=(null==(a=null==(n=iD(l.data))?void 0:n[7])?void 0:a[0])?+(iN(n[7][0].data)>>>0>0):-1,i=iP(n[5],function(e){return function(e,t){var r,a,n,s,i,c,o,l,f,h,u,d,p,m,g,v,b=iD(e),T=iN(b[1][0].data)>>>0,w=iN(b[2][0].data)>>>0,E=(null==(a=null==(r=b[8])?void 0:r[0])?void 0:a.data)&&iN(b[8][0].data)>0||!1;if((null==(s=null==(n=b[7])?void 0:n[0])?void 0:s.data)&&0!=t)g=null==(c=null==(i=b[7])?void 0:i[0])?void 0:c.data,v=null==(l=null==(o=b[6])?void 0:o[0])?void 0:l.data;else if((null==(h=null==(f=b[4])?void 0:f[0])?void 0:h.data)&&1!=t)g=null==(d=null==(u=b[4])?void 0:u[0])?void 0:d.data,v=null==(m=null==(p=b[3])?void 0:p[0])?void 0:m.data;else throw"NUMBERS Tile missing ".concat(t," cell storage");for(var S=E?4:1,k=i_(g),A=[],y=0;y<g.length/2;++y){var _=k.getUint16(2*y,!0);_<65535&&A.push([y,_])}if(A.length!=w)throw"Expected ".concat(w," cells, found ").concat(A.length);var x=[];for(y=0;y<A.length-1;++y)x[A[y][0]]=v.subarray(A[y][1]*S,A[y+1][1]*S);return A.length>=1&&(x[A[A.length-1][0]]=v.subarray(A[A.length-1][1]*S)),{R:T,cells:x}}(e,s)}),{nrows:iN(n[4][0].data)>>>0,data:i.reduce(function(e,t){return e[t.R]||(e[t.R]=[]),t.cells.forEach(function(r,a){if(e[t.R][a])throw Error("Duplicate cell r=".concat(t.R," c=").concat(a));e[t.R][a]=r}),e},[])});u.data.forEach(function(e,t){e.forEach(function(e,a){var n=rT({r:f+t,c:a}),s=function(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:return function(e,t,r,a){var n,s=i_(e),i=s.getUint32(4,!0),c=(a>1?12:8)+4*iO(i&(a>1?3470:398)),o=-1,l=-1,f=NaN,h=new Date(2001,0,1);switch(512&i&&(o=s.getUint32(c,!0),c+=4),c+=4*iO(i&(a>1?12288:4096)),16&i&&(l=s.getUint32(c,!0),c+=4),32&i&&(f=s.getFloat64(c,!0),c+=8),64&i&&(h.setTime(h.getTime()+1e3*s.getFloat64(c,!0)),c+=8),e[2]){case 0:break;case 2:n={t:"n",v:f};break;case 3:n={t:"s",v:t[l]};break;case 5:n={t:"d",v:h};break;case 6:n={t:"b",v:f>0};break;case 7:n={t:"n",v:f/86400};break;case 8:n={t:"e",v:0};break;case 9:if(o>-1)n={t:"s",v:r[o]};else if(l>-1)n={t:"s",v:t[l]};else if(isNaN(f))throw Error("Unsupported cell type ".concat(e.slice(0,4)));else n={t:"n",v:f};break;default:throw Error("Unsupported cell type ".concat(e.slice(0,4)))}return n}(e,t,r,e[0]);case 5:return function(e,t,r){var a,n=i_(e),s=n.getUint32(8,!0),i=12,c=-1,o=-1,l=NaN,f=NaN,h=new Date(2001,0,1);switch(1&s&&(l=function(e,t){for(var r=(127&e[t+15])<<7|e[t+14]>>1,a=1&e[t+14],n=t+13;n>=t;--n)a=256*a+e[n];return(128&e[t+15]?-a:a)*Math.pow(10,r-6176)}(e,i),i+=16),2&s&&(f=n.getFloat64(i,!0),i+=8),4&s&&(h.setTime(h.getTime()+1e3*n.getFloat64(i,!0)),i+=8),8&s&&(o=n.getUint32(i,!0),i+=4),16&s&&(c=n.getUint32(i,!0),i+=4),e[1]){case 0:break;case 2:case 10:a={t:"n",v:l};break;case 3:a={t:"s",v:t[o]};break;case 5:a={t:"d",v:h};break;case 6:a={t:"b",v:f>0};break;case 7:a={t:"n",v:f/86400};break;case 8:a={t:"e",v:0};break;case 9:if(c>-1)a={t:"s",v:r[c]};else throw Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)));break;default:throw Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)))}return a}(e,t,r);default:throw Error("Unsupported payload version ".concat(e[0]))}}(e,c,o);s&&(r[n]=s)})}),f+=u.nrows})}(e,n[0],a),a}(s,e))})}),a);n.sheets.forEach(function(e,t){i7(l,e,0==t?n.name:n.name+"_"+t,!0)})}})}),0==l.SheetNames.length)throw Error("Empty NUMBERS file");return l}function ij(e){return function(t){for(var r=0;r!=e.length;++r){var a=e[r];void 0===t[a[0]]&&(t[a[0]]=a[1]),"n"===a[2]&&(t[a[0]]=Number(t[a[0]]))}}}function i$(e){ij([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function iX(e){ij([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function iY(e){return"/"==e.charAt(0)?e.slice(1):e}function iK(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=y(e.slice(0,12));break;case"binary":r=e;break;default:throw Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function iJ(e,t){var r=0;n:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return is(e.slice(r),t);default:break n}return na.to_workbook(e,t)}function iq(e,t,r,a){return a?(r.type="string",na.to_workbook(e,r)):na.to_workbook(t,r)}function iZ(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return e_(t.file,ey.write(e,{type:_?"buffer":""}));case"string":throw Error("'string' output type invalid for '"+t.bookType+"' files");default:throw Error("Unrecognized type "+t.type)}return ey.write(e,t)}function iQ(e,t,r){r||(r="");var a=r+e;switch(t.type){case"base64":return A(tT(a));case"binary":return tT(a);case"string":return e;case"file":return e_(t.file,a,"utf8");case"buffer":if(_)return x(a,"utf8");if("undefined"!=typeof TextEncoder)return new TextEncoder().encode(a);return iQ(a,{type:"binary"}).split("").map(function(e){return e.charCodeAt(0)})}throw Error("Unrecognized type "+t.type)}function i1(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",a=0;a<e.length;++a)r+=String.fromCharCode(e[a]);return"base64"==t.type?A(r):"string"==t.type?tb(r):r;case"file":return e_(t.file,e);case"buffer":return e;default:throw Error("Unrecognized type "+t.type)}}function i0(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},a=0,n=1,s=[],i=0,c="",o={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},f=null!=l.range?l.range:e["!ref"];switch(1===l.header?a=1:"A"===l.header?a=2:Array.isArray(l.header)?a=3:null==l.header&&(a=0),typeof f){case"string":o=rS(f);break;case"number":(o=rS(e["!ref"])).s.r=f;break;default:o=f}a>0&&(n=0);var h=rm(o.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=o.s.r,b=0,T={};g&&!e[v]&&(e[v]=[]);var w=l.skipHidden&&e["!cols"]||[],E=l.skipHidden&&e["!rows"]||[];for(b=o.s.c;b<=o.e.c;++b)if(!(w[b]||{}).hidden)switch(u[b]=rv(b),r=g?e[v][b]:e[u[b]+h],a){case 1:s[b]=b-o.s.c;break;case 2:s[b]=u[b];break;case 3:s[b]=l.header[b-o.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),c=i=rA(r,null,l),m=T[i]||0){do c=i+"_"+m++;while(T[c]);T[i]=m,T[c]=1}else T[i]=1;s[b]=c}for(v=o.s.r+n;v<=o.e.r;++v)if(!(E[v]||{}).hidden){var S=function(e,t,r,a,n,s,i,c){var o=rm(r),l=c.defval,f=c.raw||!Object.prototype.hasOwnProperty.call(c,"raw"),h=!0,u=1===n?[]:{};if(1!==n)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(e){u.__rowNum__=r}else u.__rowNum__=r;if(!i||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=i?e[r][d]:e[a[d]+o];if(void 0===p||void 0===p.t){if(void 0===l)continue;null!=s[d]&&(u[s[d]]=l);continue}var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==l)u[s[d]]=l;else{if(!f||null!==m)continue;u[s[d]]=null}else u[s[d]]=f&&("n"!==p.t||"n"===p.t&&!1!==c.rawNumbers)?m:rA(p,m,c);null!=m&&(h=!1)}}return{row:u,isempty:h}}(e,o,v,u,a,s,g,l);(!1===S.isempty||(1===a?!1!==l.blankrows:l.blankrows))&&(d[p++]=S.row)}return d.length=p,d}var i2=/"/g;function i4(e,t){var r=[],a=null==t?{}:t;if(null==e||null==e["!ref"])return"";var n=rS(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),c=void 0!==a.RS?a.RS:"\n",o=c.charCodeAt(0),l=RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];a.dense=Array.isArray(e);for(var u=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(u[p]||{}).hidden||(h[p]=rv(p));for(var m=0,g=n.s.r;g<=n.e.r;++g)!(d[g]||{}).hidden&&null!=(f=function(e,t,r,a,n,s,i,c){for(var o=!0,l=[],f="",h=rm(r),u=t.s.c;u<=t.e.c;++u)if(a[u]){var d=c.dense?(e[r]||[])[u]:e[a[u]+h];if(null==d)f="";else if(null!=d.v){o=!1,f=""+(c.rawNumbers&&"n"==d.t?d.v:rA(d,null,c));for(var p=0,m=0;p!==f.length;++p)if((m=f.charCodeAt(p))===n||m===s||34===m||c.forceQuotes){f='"'+f.replace(i2,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==d.f||d.F?f="":(o=!1,(f="="+d.f).indexOf(",")>=0&&(f='"'+f.replace(i2,'""')+'"'));l.push(f)}return!1===c.blankrows&&o?null:l.join(i)}(e,n,g,h,i,o,s,a))&&(a.strip&&(f=f.replace(l,"")),(f||!1!==a.blankrows)&&r.push((m++?c:"")+f));return delete a.dense,r.join("")}function i3(e,t){t||(t={}),t.FS="	",t.RS="\n";var r=i4(e,t);if(void 0===n||"string"==t.type)return r;var a=n.utils.encode(1200,r,"str");return String.fromCharCode(255)+String.fromCharCode(254)+a}function i5(e,t,r){var a,n=r||{},s=+!n.skipHeader,i=e||{},c=0,o=0;if(i&&null!=n.origin)if("number"==typeof n.origin)c=n.origin;else{var l="string"==typeof n.origin?rb(n.origin):n.origin;c=l.r,o=l.c}var f={s:{c:0,r:0},e:{c:o,r:c+t.length-1+s}};if(i["!ref"]){var h=rS(i["!ref"]);f.e.c=Math.max(f.e.c,h.e.c),f.e.r=Math.max(f.e.r,h.e.r),-1==c&&(c=h.e.r+1,f.e.r=c+t.length-1+s)}else -1==c&&(c=0,f.e.r=t.length-1+s);var u=n.header||[],d=0;t.forEach(function(e,t){ex(e).forEach(function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var l=e[r],f="z",h="",p=rT({c:o+d,r:c+t+s});a=i6(i,p),!l||"object"!=typeof l||l instanceof Date?("number"==typeof l?f="n":"boolean"==typeof l?f="b":"string"==typeof l?f="s":l instanceof Date?(f="d",n.cellDates||(f="n",l=eN(l)),h=n.dateNF||j[14]):null===l&&n.nullError&&(f="e",l=0),a?(a.t=f,a.v=l,delete a.w,delete a.R,h&&(a.z=h)):i[p]=a={t:f,v:l},h&&(a.z=h)):i[p]=l})}),f.e.c=Math.max(f.e.c,o+u.length-1);var p=rm(c);if(s)for(d=0;d<u.length;++d)i[rv(d+o)+p]={t:"s",v:u[d]};return i["!ref"]=rE(f),i}function i6(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var a=rb(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return"number"!=typeof t?i6(e,rT(t)):i6(e,rT({r:t,c:r||0}))}function i8(){return{SheetNames:[],Sheets:{}}}function i7(e,t,r,a){var n=1;if(!r)for(;n<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+n);++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||r;for(++n;n<=65535&&-1!=e.SheetNames.indexOf(r=i+n);++n);}if(s6(r),e.SheetNames.indexOf(r)>=0)throw Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function i9(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var ce={encode_col:rv,encode_row:rm,encode_cell:rT,encode_range:rE,decode_col:rg,decode_row:rp,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:rb,decode_range:rw,format_cell:rA,sheet_add_aoa:r_,sheet_add_json:i5,sheet_add_dom:iw,aoa_to_sheet:rx,json_to_sheet:function(e,t){return i5(null,e,t)},table_to_sheet:iE,table_to_book:function(e,t){return ry(iE(e,t),t)},sheet_to_csv:i4,sheet_to_txt:i3,sheet_to_json:i0,sheet_to_html:iT,sheet_to_formulae:function(e){var t,r="",a="";if(null==e||null==e["!ref"])return[];var n,s=rS(e["!ref"]),i="",c=[],o=[],l=Array.isArray(e);for(n=s.s.c;n<=s.e.c;++n)c[n]=rv(n);for(var f=s.s.r;f<=s.e.r;++f)for(i=rm(f),n=s.s.c;n<=s.e.c;++n)if(r=c[n]+i,t=l?(e[f]||[])[n]:e[r],a="",void 0!==t){if(null!=t.F){if(r=t.F,!t.f)continue;a=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)a=t.f;else if("z"==t.t)continue;else if("n"==t.t&&null!=t.v)a=""+t.v;else if("b"==t.t)a=t.v?"TRUE":"FALSE";else if(void 0!==t.w)a="'"+t.w;else{if(void 0===t.v)continue;a="s"==t.t?"'"+t.v:""+t.v}o[o.length]=r+"="+a}return o},sheet_to_row_object_array:i0,sheet_get_cell:i6,book_new:i8,book_append_sheet:i7,book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw Error("Cannot find sheet name |"+t+"|")}throw Error("Cannot find sheet |"+t+"|")}(e,t);switch(!e.Workbook.Sheets[a]&&(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:i9,cell_set_internal_link:function(e,t,r){return i9(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,a){for(var n="string"!=typeof t?t:rS(t),s="string"==typeof t?t:rE(t),i=n.s.r;i<=n.e.r;++i)for(var c=n.s.c;c<=n.e.c;++c){var o=i6(e,i,c);o.t="n",o.F=s,delete o.v,i==n.s.r&&c==n.s.c&&(o.f=r,a&&(o.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};f.version}}]);