// Application route constants

export const ROUTES = {
  // Public routes
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',

  // Auth routes
  AUTH: {
    CALLBACK: '/auth/callback',
  },

  // OAuth routes
  OAUTH: {
    CALLBACK: '/oauth/callback',
  },

  // Onboarding routes
  ONBOARDING: '/onboarding',

  // Protected routes
  DASHBOARD: '/dashboard',
  CHAT: '/chat',
  DATASOURCES: '/datasources',
  REPORTS: '/reports',

  // Dynamic routes
  CHAT_WITH_ID: (chatId: string) => `/chat/${chatId}`,
} as const;

// Routes that don't require authentication
export const PUBLIC_ROUTES = [
  ROUTES.HOME,
  ROUTES.LOGIN,
  ROUTES.REGISTER,
  ROUTES.AUTH.CALLBACK,
  ROUTES.OAUTH.CALLBACK,
  ROUTES.ONBOARDING,
] as const;

// Routes that require authentication
export const PROTECTED_ROUTES = [
  ROUTES.DASHBOARD,
  ROUTES.CHAT,
  ROUTES.DATASOURCES,
  ROUTES.REPORTS,
] as const;

// Default redirect after login
export const DEFAULT_LOGIN_REDIRECT = ROUTES.DASHBOARD;
