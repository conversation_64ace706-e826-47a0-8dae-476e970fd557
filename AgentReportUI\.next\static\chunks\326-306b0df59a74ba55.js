(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[326],{52:(e,t,r)=>{"use strict";function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>u,Qd:()=>l,Tw:()=>f,Zz:()=>c,ve:()=>d,y$:()=>s});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",a=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function l(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function s(e,t,r){if("function"!=typeof e)throw Error(n(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(s)(e,t)}let a=e,u=t,c=new Map,f=c,d=0,p=!1;function h(){f===c&&(f=new Map,c.forEach((e,t)=>{f.set(t,e)}))}function y(){if(p)throw Error(n(3));return u}function g(e){if("function"!=typeof e)throw Error(n(4));if(p)throw Error(n(5));let t=!0;h();let r=d++;return f.set(r,e),function(){if(t){if(p)throw Error(n(6));t=!1,h(),f.delete(r),c=null}}}function v(e){if(!l(e))throw Error(n(7));if(void 0===e.type)throw Error(n(8));if("string"!=typeof e.type)throw Error(n(17));if(p)throw Error(n(9));try{p=!0,u=a(u,e)}finally{p=!1}return(c=f).forEach(e=>{e()}),e}return v({type:o.INIT}),{dispatch:v,subscribe:g,getState:y,replaceReducer:function(e){if("function"!=typeof e)throw Error(n(10));a=e,v({type:o.REPLACE})},[i]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(n(11));function t(){e.next&&e.next(y())}return t(),{unsubscribe:g(t)}},[i](){return this}}}}}function u(e){let t,r=Object.keys(e),i={};for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof e[n]&&(i[n]=e[n])}let a=Object.keys(i);try{Object.keys(i).forEach(e=>{let t=i[e];if(void 0===t(void 0,{type:o.INIT}))throw Error(n(12));if(void 0===t(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let o=!1,l={};for(let t=0;t<a.length;t++){let s=a[t],u=i[s],c=e[s],f=u(c,r);if(void 0===f)throw r&&r.type,Error(n(14));l[s]=f,o=o||f!==c}return(o=o||a.length!==Object.keys(e).length)?l:e}}function c(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function f(...e){return t=>(r,i)=>{let a=t(r,i),o=()=>{throw Error(n(15))},l={getState:a.getState,dispatch:(e,...t)=>o(e,...t)};return o=c(...e.map(e=>e(l)))(a.dispatch),{...a,dispatch:o}}}function d(e){return l(e)&&"type"in e&&"string"==typeof e.type}},675:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var n=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},841:(e,t,r)=>{"use strict";r.d(t,{$7:()=>f,Ru:()=>c,uZ:()=>u});var n=r(5710),i=r(34890),a=r(20215),o=r(94732),l=r(14299),s=r(60841),u=(0,n.VP)("keyDown"),c=(0,n.VP)("focus"),f=(0,n.Nc)();f.startListening({actionCreator:u,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,u=e.payload;if("ArrowRight"===u||"ArrowLeft"===u||"Enter"===u){var c=Number((0,s.P)(n,(0,a.n4)(r))),f=(0,a.R4)(r);if("Enter"===u){var d=(0,o.pg)(r,"axis","hover",String(n.index));t.dispatch((0,i.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:d}));return}var p=c+("ArrowRight"===u?1:-1)*("left-to-right"===(0,l._y)(r)?1:-1);if(null!=f&&!(p>=f.length)&&!(p<0)){var h=(0,o.pg)(r,"axis","hover",String(p));t.dispatch((0,i.o4)({active:!0,activeIndex:p.toString(),activeDataKey:void 0,activeCoordinate:h}))}}}}}),f.startListening({actionCreator:c,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var a=(0,o.pg)(r,"axis","hover",String("0"));t.dispatch((0,i.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:a}))}}}})},1147:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},2267:(e,t,r)=>{"use strict";r.d(t,{J:()=>a,U:()=>i});var n=(0,r(5710).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:i}=n.actions,a=n.reducer},2348:(e,t,r)=>{"use strict";r.d(t,{W:()=>s});var n=r(12115),i=r(52596),a=r(70788),o=["children","className"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=n.forwardRef((e,t)=>{var{children:r,className:s}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o),c=(0,i.$)("recharts-layer",s);return n.createElement("g",l({className:c},(0,a.J9)(u,!0),{ref:t}),r)})},2589:(e,t,r)=>{"use strict";r.d(t,{A$:()=>i,HK:()=>o,Lp:()=>n,et:()=>a});var n=e=>e.layout.width,i=e=>e.layout.height,a=e=>e.layout.scale,o=e=>e.layout.margin},2767:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},3401:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var n=r(12115),i=r(46641),a=r(82396),o=["axis","item"],l=(0,n.forwardRef)((e,t)=>n.createElement(a.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:o,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))},4993:(e,t,r)=>{"use strict";var n=r(12115);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},5102:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>O});var n=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var r=-1;return e.some(function(e,n){return e[0]===t&&(r=n,!0)}),r}function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var r=e(this.__entries__,t),n=this.__entries__[r];return n&&n[1]},t.prototype.set=function(t,r){var n=e(this.__entries__,t);~n?this.__entries__[n][1]=r:this.__entries__.push([t,r])},t.prototype.delete=function(t){var r=this.__entries__,n=e(r,t);~n&&r.splice(n,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var r=0,n=this.__entries__;r<n.length;r++){var i=n[r];e.call(t,i[1],i[0])}},t}(),i="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,a=void 0!==r.g&&r.g.Math===Math?r.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),o="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(a):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)},l=["top","right","bottom","left","width","height","size","weight"],s="undefined"!=typeof MutationObserver,u=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var r=!1,n=!1,i=0;function a(){r&&(r=!1,e()),n&&s()}function l(){o(a)}function s(){var e=Date.now();if(r){if(e-i<2)return;n=!0}else r=!0,n=!1,setTimeout(l,20);i=e}return s}(this.refresh.bind(this),0)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,r=t.indexOf(e);~r&&t.splice(r,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter(function(e){return e.gatherActive(),e.hasActive()});return e.forEach(function(e){return e.broadcastActive()}),e.length>0},e.prototype.connect_=function(){i&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),s?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){i&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,r=void 0===t?"":t;l.some(function(e){return!!~r.indexOf(e)})&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),c=function(e,t){for(var r=0,n=Object.keys(t);r<n.length;r++){var i=n[r];Object.defineProperty(e,i,{value:t[i],enumerable:!1,writable:!1,configurable:!0})}return e},f=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||a},d=g(0,0,0,0);function p(e){return parseFloat(e)||0}function h(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return t.reduce(function(t,r){return t+p(e["border-"+r+"-width"])},0)}var y="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof f(e).SVGGraphicsElement}:function(e){return e instanceof f(e).SVGElement&&"function"==typeof e.getBBox};function g(e,t,r,n){return{x:e,y:t,width:r,height:n}}var v=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=g(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=function(e){if(!i)return d;if(y(e)){var t;return g(0,0,(t=e.getBBox()).width,t.height)}return function(e){var t,r=e.clientWidth,n=e.clientHeight;if(!r&&!n)return d;var i=f(e).getComputedStyle(e),a=function(e){for(var t={},r=0,n=["top","right","bottom","left"];r<n.length;r++){var i=n[r],a=e["padding-"+i];t[i]=p(a)}return t}(i),o=a.left+a.right,l=a.top+a.bottom,s=p(i.width),u=p(i.height);if("border-box"===i.boxSizing&&(Math.round(s+o)!==r&&(s-=h(i,"left","right")+o),Math.round(u+l)!==n&&(u-=h(i,"top","bottom")+l)),(t=e)!==f(t).document.documentElement){var c=Math.round(s+o)-r,y=Math.round(u+l)-n;1!==Math.abs(c)&&(s-=c),1!==Math.abs(y)&&(u-=y)}return g(a.left,a.top,s,u)}(e)}(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),m=function(e,t){var r,n,i,a,o,l=(r=t.x,n=t.y,i=t.width,a=t.height,c(o=Object.create(("undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object).prototype),{x:r,y:n,width:i,height:a,top:n,right:r+i,bottom:a+n,left:r}),o);c(this,{target:e,contentRect:l})},b=function(){function e(e,t,r){if(this.activeObservations_=[],this.observations_=new n,"function"!=typeof e)throw TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=r}return e.prototype.observe=function(e){if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof f(e).Element))throw TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new v(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof f(e).Element))throw TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(t){t.isActive()&&e.activeObservations_.push(t)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map(function(e){return new m(e.target,e.broadcastRect())});this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),x="undefined"!=typeof WeakMap?new WeakMap:new n,w=function e(t){if(!(this instanceof e))throw TypeError("Cannot call a class as a function.");if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");var r=new b(t,u.getInstance(),this);x.set(this,r)};["observe","unobserve","disconnect"].forEach(function(e){w.prototype[e]=function(){var t;return(t=x.get(this))[e].apply(t,arguments)}});let O=void 0!==a.ResizeObserver?a.ResizeObserver:w},5623:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5638:(e,t,r)=>{"use strict";r.d(t,{m:()=>et});var n=r(12115),i=r(47650),a=r(20241),o=r.n(a),l=r(52596),s=r(16377);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return Array.isArray(e)&&(0,s.vh)(e[0])&&(0,s.vh)(e[1])?e.join(" ~ "):e}var p=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:c,formatter:p,itemSorter:h,wrapperClassName:y,labelClassName:g,label:v,labelFormatter:m,accessibilityLayer:b=!1}=e,x=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),w=f({margin:0},a),O=!(0,s.uy)(v),P=O?v:"",j=(0,l.$)("recharts-default-tooltip",y),S=(0,l.$)("recharts-tooltip-label",g);return O&&m&&null!=c&&(P=m(v,c)),n.createElement("div",u({className:j,style:x},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:S,style:w},n.isValidElement(P)?P:"".concat(P)),(()=>{if(c&&c.length){var e=(h?o()(c,h):c).map((e,r)=>{if("none"===e.type)return null;var a=e.formatter||p||d,{value:o,name:l}=e,u=o,h=l;if(a){var y=a(o,l,e,r,c);if(Array.isArray(y))[u,h]=y;else{if(null==y)return null;u=y}}var g=f({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:g},(0,s.vh)(h)?n.createElement("span",{className:"recharts-tooltip-item-name"},h):null,(0,s.vh)(h)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},u),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},h="recharts-tooltip-wrapper",y={visibility:"hidden"};function g(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:u,viewBoxDimension:c}=e;if(a&&(0,s.Et)(a[n]))return a[n];var f=r[n]-l-(i>0?i:0),d=r[n]+i;if(t[n])return o[n]?f:d;var p=u[n];return null==p?0:o[n]?f<p?Math.max(d,p):Math.max(f,p):null==c?0:d+l>p+c?Math.max(f,p):Math.max(d,p)}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class x extends n.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:u,isAnimationActive:c,offset:f,position:d,reverseDirection:p,useTranslate3d:v,viewBox:b,wrapperStyle:x,lastBoundingBox:w,innerRef:O,hasPortalFromProps:P}=this.props,{cssClasses:j,cssProperties:S}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:u,reverseDirection:c,tooltipBox:f,useTranslate3d:d,viewBox:p}=e;return{cssProperties:t=f.height>0&&f.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=g({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:u,reverseDirection:c,tooltipDimension:f.width,viewBox:p,viewBoxDimension:p.width}),translateY:n=g({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:u,reverseDirection:c,tooltipDimension:f.height,viewBox:p,viewBoxDimension:p.height}),useTranslate3d:d}):y,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,l.$)(h,{["".concat(h,"-right")]:(0,s.Et)(r)&&t&&(0,s.Et)(t.x)&&r>=t.x,["".concat(h,"-left")]:(0,s.Et)(r)&&t&&(0,s.Et)(t.x)&&r<t.x,["".concat(h,"-bottom")]:(0,s.Et)(n)&&t&&(0,s.Et)(t.y)&&n>=t.y,["".concat(h,"-top")]:(0,s.Et)(n)&&t&&(0,s.Et)(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:f,position:d,reverseDirection:p,tooltipBox:{height:w.height,width:w.width},useTranslate3d:v,viewBox:b}),E=P?{}:m(m({transition:c&&e?"transform ".concat(r,"ms ").concat(i):void 0},S),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&u?"visible":"hidden",position:"absolute",top:0,left:0}),M=m(m({},E),{},{visibility:!this.state.dismissed&&e&&u?"visible":"hidden"},x);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:j,style:M,ref:O},a)}constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}}var w=r(41643),O=r(60512),P=r.n(O),j=r(97238),S=r(96752),E=r(70688),M=r(70788),A=["x","y","top","left","width","height","className"];function k(){return(k=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var D=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),C=e=>{var{x:t=0,y:r=0,top:i=0,left:a=0,width:o=0,height:u=0,className:c}=e,f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:i,left:a,width:o,height:u},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,A));return(0,s.Et)(t)&&(0,s.Et)(r)&&(0,s.Et)(o)&&(0,s.Et)(u)&&(0,s.Et)(i)&&(0,s.Et)(a)?n.createElement("path",k({},(0,M.J9)(f,!0),{className:(0,l.$)("recharts-cross",c),d:D(t,r,o,u,i,a)})):null},T=r(44538),N=r(25641);function z(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[(0,N.IZ)(t,r,n,i),(0,N.IZ)(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}var I=r(77283),R=r(71420),L=r(94732);function H(){return(H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function $(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function B(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function W(e){var t,r,i,{coordinate:a,payload:o,index:s,offset:u,tooltipAxisBandSize:c,layout:f,cursor:d,tooltipEventType:p,chartName:h}=e;if(!d||!a||"ScatterChart"!==h&&"axis"!==p)return null;if("ScatterChart"===h)r=a,i=C;else if("BarChart"===h)t=c/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?a.x-t:u.left+.5,y:"horizontal"===f?u.top+.5:a.y-t,width:"horizontal"===f?c:u.width-1,height:"horizontal"===f?u.height-1:c},i=T.M;else if("radial"===f){var{cx:y,cy:g,radius:v,startAngle:m,endAngle:b}=z(a);r={cx:y,cy:g,startAngle:m,endAngle:b,innerRadius:v,outerRadius:v},i=I.h}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return z(t);else{var{cx:l,cy:s,innerRadius:u,outerRadius:c,angle:f}=t,d=(0,N.IZ)(l,s,u,f),p=(0,N.IZ)(l,s,c,f);n=d.x,i=d.y,a=p.x,o=p.y}return[{x:n,y:i},{x:a,y:o}]}(f,a,u)},i=E.I;var x="object"==typeof d&&"className"in d?d.className:void 0,w=B(B(B(B({stroke:"#ccc",pointerEvents:"none"},u),r),(0,M.J9)(d,!1)),{},{payload:o,payloadIndex:s,className:(0,l.$)("recharts-tooltip-cursor",x)});return(0,n.isValidElement)(d)?(0,n.cloneElement)(d,w):(0,n.createElement)(i,w)}function U(e){var t=(0,R.O)(),r=(0,j.hj)(),i=(0,j.WX)(),a=(0,L.fW)();return n.createElement(W,H({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:i,tooltipAxisBandSize:t,chartName:a}))}var F=r(25115),K=r(81971),G=r(34890),q=r(46850),V=r(96523),Y=r(93389);function Z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function X(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function J(e){return e.dataKey}var Q=[],ee={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!w.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function et(e){var t,r,a=(0,Y.e)(e,ee),{active:o,allowEscapeViewBox:l,animationDuration:s,animationEasing:u,content:c,filterNull:f,isAnimationActive:d,offset:h,payloadUniqBy:y,position:g,reverseDirection:v,useTranslate3d:m,wrapperStyle:b,cursor:w,shared:O,trigger:E,defaultIndex:M,portal:A,axisId:k}=a,_=(0,K.j)(),D="number"==typeof M?String(M):M;(0,n.useEffect)(()=>{_((0,G.UF)({shared:O,trigger:E,axisId:k,active:o,defaultIndex:D}))},[_,O,E,k,o,D]);var C=(0,j.sk)(),T=(0,S.$)(),N=(0,V.Td)(O),{activeIndex:z,isActive:I}=(0,K.G)(e=>(0,L.yn)(e,N,E,D)),R=(0,K.G)(e=>(0,L.u9)(e,N,E,D)),H=(0,K.G)(e=>(0,L.BZ)(e,N,E,D)),$=(0,K.G)(e=>(0,L.dS)(e,N,E,D)),B=(0,F.X)(),W=null!=o?o:I,[Z,et]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,i]}([R,W]),er="axis"===N?H:void 0;(0,q.m7)(N,E,$,er,z,W);var en=null!=A?A:B;if(null==en)return null;var ei=null!=R?R:Q;W||(ei=Q),f&&ei.length&&(t=R.filter(e=>null!=e.value&&(!0!==e.hide||a.includeHidden)),ei=!0===y?P()(t,J):"function"==typeof y?P()(t,y):t);var ea=ei.length>0,eo=n.createElement(x,{allowEscapeViewBox:l,animationDuration:s,animationEasing:u,isAnimationActive:d,active:W,coordinate:$,hasPayload:ea,offset:h,position:g,reverseDirection:v,useTranslate3d:m,viewBox:C,wrapperStyle:b,lastBoundingBox:Z,innerRef:et,hasPortalFromProps:!!A},(r=X(X({},a),{},{payload:ei,label:er,active:W,coordinate:$,accessibilityLayer:T}),n.isValidElement(c)?n.cloneElement(c,r):"function"==typeof c?n.createElement(c,r):n.createElement(p,r)));return n.createElement(n.Fragment,null,(0,i.createPortal)(eo,en),W&&n.createElement(U,{cursor:w,tooltipEventType:N,coordinate:$,payload:R,index:z}))}},5710:(e,t,r)=>{"use strict";r.d(t,{U1:()=>m,VP:()=>u,Nc:()=>ey,Z0:()=>D});var n=r(52);function i(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var a=i(),o=r(74532),l=(r(49509),"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)});"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var s=e=>e&&"function"==typeof e.match;function u(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(ew(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,n.ve)(t)&&t.type===e,r}function c(e){return["type","payload","error","meta"].indexOf(e)>-1}var f=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function d(e){return(0,o.a6)(e)?(0,o.jM)(e,()=>{}):e}function p(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var h=()=>function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{},l=new f;return t&&("boolean"==typeof t?l.push(a):l.push(i(t.extraArgument))),l},y=e=>t=>{setTimeout(t,e)},g=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,s="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:y(10):"callback"===e.type?e.queueNotification:y(e.timeout),u=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,s(u)),n.dispatch(e)}finally{i=!0}}})},v=e=>function(t){let{autoBatch:r=!0}=t??{},n=new f(e);return r&&n.push(g("object"==typeof r?r:void 0)),n};function m(e){let t,r,i=h(),{reducer:a,middleware:o,devTools:s=!0,duplicateMiddlewareCheck:u=!0,preloadedState:c,enhancers:f}=e||{};if("function"==typeof a)t=a;else if((0,n.Qd)(a))t=(0,n.HY)(a);else throw Error(ew(1));r="function"==typeof o?o(i):i();let d=n.Zz;s&&(d=l({trace:!1,..."object"==typeof s&&s}));let p=v((0,n.Tw)(...r)),y=d(..."function"==typeof f?f(p):p());return(0,n.y$)(t,c,y)}function b(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(ew(28));if(n in r)throw Error(ew(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var x=(e,t)=>s(e)?e.match(t):e(t);function w(...e){return t=>e.some(e=>x(e,t))}var O=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},P=["name","message","stack","code"],j=class{constructor(e,t){this.payload=e,this.meta=t}_type},S=class{constructor(e,t){this.payload=e,this.meta=t}_type},E=e=>{if("object"==typeof e&&null!==e){let t={};for(let r of P)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},M="External signal was aborted";function A(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var k=Symbol.for("rtk-slice-createasyncthunk"),_=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(_||{}),D=function({creators:e}={}){let t=e?.asyncThunk?.[k];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(ew(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},l=Object.keys(a),s={},c={},f={},h=[],y={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(ew(12));if(r in c)throw Error(ew(13));return c[r]=t,y},addMatcher:(e,t)=>(h.push({matcher:e,reducer:t}),y),exposeAction:(e,t)=>(f[e]=t,y),exposeCaseReducer:(e,t)=>(s[e]=t,y)};function g(){let[t={},r=[],n]="function"==typeof e.extraReducers?b(e.extraReducers):[e.extraReducers],i={...t,...c};return function(e,t){let r,[n,i,a]=b(t);if("function"==typeof e)r=()=>d(e());else{let t=d(e);r=()=>t}function l(e=r(),t){let s=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===s.filter(e=>!!e).length&&(s=[a]),s.reduce((e,r)=>{if(r)if((0,o.Qx)(e)){let n=r(e,t);return void 0===n?e:n}else{if((0,o.a6)(e))return(0,o.jM)(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return l.getInitialState=r,l}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of h)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}l.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(ew(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:s,settled:u,options:c}=r,f=i(e,a,c);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),s&&n.addCase(f.rejected,s),u&&n.addMatcher(f.settled,u),n.exposeCaseReducer(t,{fulfilled:o||C,pending:l||C,rejected:s||C,settled:u||C})}(o,i,y,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(ew(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?u(e,o):u(e))}(o,i,y)});let v=e=>e,m=new Map,x=new WeakMap;function w(e,t){return r||(r=g()),r(e,t)}function O(){return r||(r=g()),r.getInitialState()}function P(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=p(x,n,O)),i}function i(t=v){let n=p(m,r,()=>new WeakMap);return p(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>p(x,t,O),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let j={name:n,reducer:w,actions:f,caseReducers:s,getInitialState:O,...P(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:w},r),{...j,...P(n,!0)}}};return j}}();function C(){}function T(e){return function(t,r){let n=t=>{isAction(r)&&Object.keys(r).every(c)?e(r.payload,t):e(r,t)};return(null)(t)?(n(t),t):createNextState3(t,n)}}function N(e,t){return t(e)}function z(e){return Array.isArray(e)||(e=Object.values(e)),e}var I="listener",R="completed",L="cancelled",H=`task-${L}`,$=`task-${R}`,B=`${I}-${L}`,W=`${I}-${R}`,U=class{constructor(e){this.code=e,this.message=`task ${L} (reason: ${e})`}name="TaskAbortError";message},F=(e,t)=>{if("function"!=typeof e)throw TypeError(ew(32))},K=()=>{},G=(e,t=K)=>(e.catch(t),e),q=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),V=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},Y=e=>{if(e.aborted){let{reason:t}=e;throw new U(t)}};function Z(e,t){let r=K;return new Promise((n,i)=>{let a=()=>i(new U(e.reason));if(e.aborted)return void a();r=q(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=K})}var X=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof U?"cancelled":"rejected",error:e}}finally{t?.()}},J=e=>t=>G(Z(e,t).then(t=>(Y(e),t))),Q=e=>{let t=J(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:ee}=Object,et={},er="listenerMiddleware",en=(e,t)=>{let r=t=>q(e,()=>V(t,e.reason));return(n,i)=>{F(n,"taskExecutor");let a=new AbortController;r(a);let o=X(async()=>{Y(e),Y(a.signal);let t=await n({pause:J(a.signal),delay:Q(a.signal),signal:a.signal});return Y(a.signal),t},()=>V(a,$));return i?.autoJoin&&t.push(o.catch(K)),{result:J(e)(o),cancel(){V(a,H)}}}},ei=(e,t)=>{let r=async(r,n)=>{Y(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await Z(t,Promise.race(a));return Y(t),e}finally{i()}};return(e,t)=>G(r(e,t))},ea=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=u(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(ew(21));return F(a,"options.listener"),{predicate:i,type:t,effect:a}},eo=ee(e=>{let{type:t,predicate:r,effect:n}=ea(e);return{id:O(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(ew(22))}}},{withTypes:()=>eo}),el=(e,t)=>{let{type:r,effect:n,predicate:i}=ea(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},es=e=>{e.pending.forEach(e=>{V(e,B)})},eu=e=>()=>{e.forEach(es),e.clear()},ec=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},ef=ee(u(`${er}/add`),{withTypes:()=>ef}),ed=u(`${er}/removeAll`),ep=ee(u(`${er}/remove`),{withTypes:()=>ep}),eh=(...e)=>{console.error(`${er}/error`,...e)},ey=(e={})=>{let t=new Map,{extra:r,onError:i=eh}=e;F(i,"onError");let a=e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&es(e)}),o=e=>a(el(t,e)??eo(e));ee(o,{withTypes:()=>o});let l=e=>{let r=el(t,e);return r&&(r.unsubscribe(),e.cancelActive&&es(r)),!!r};ee(l,{withTypes:()=>l});let s=async(e,n,a,l)=>{let s=new AbortController,u=ei(o,s.signal),c=[];try{e.pending.add(s),await Promise.resolve(e.effect(n,ee({},a,{getOriginalState:l,condition:(e,t)=>u(e,t).then(Boolean),take:u,delay:Q(s.signal),pause:J(s.signal),extra:r,signal:s.signal,fork:en(s.signal,c),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==s&&(V(e,B),r.delete(e))})},cancel:()=>{V(s,B),e.pending.delete(s)},throwIfCancelled:()=>{Y(s.signal)}})))}catch(e){e instanceof U||ec(i,e,{raisedBy:"effect"})}finally{await Promise.all(c),V(s,W),e.pending.delete(s)}},u=eu(t);return{middleware:e=>r=>a=>{let c;if(!(0,n.ve)(a))return r(a);if(ef.match(a))return o(a.payload);if(ed.match(a))return void u();if(ep.match(a))return l(a.payload);let f=e.getState(),d=()=>{if(f===et)throw Error(ew(23));return f};try{if(c=r(a),t.size>0){let r=e.getState();for(let n of Array.from(t.values())){let t=!1;try{t=n.predicate(a,r,f)}catch(e){t=!1,ec(i,e,{raisedBy:"predicate"})}t&&s(n,a,e,d)}}}finally{f=et}return c},startListening:o,stopListening:l,clearListeners:u}},eg=e=>"reducerPath"in e&&"string"==typeof e.reducerPath,ev=Symbol.for("rtk-state-proxy-original"),em=e=>!!e&&!!e[ev],eb=new WeakMap,ex={};function ew(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}},5939:(e,t,r)=>{"use strict";let{default:n,DraggableCore:i}=r(73666);e.exports=n,e.exports.default=n,e.exports.DraggableCore=i},5993:(e,t,r)=>{"use strict";t.default=void 0;var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=c(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(n,a,o):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(12115)),i=u(r(38637)),a=r(76725),o=r(50507),l=r(18957),s=u(r(78503));function u(e){return e&&e.__esModule?e:{default:e}}function c(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(c=function(e){return e?r:t})(e)}function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:String(n))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let p=e=>Object.prototype.toString.call(e);function h(e,t){return null==e?null:Array.isArray(e)?e:e[t]}class y extends n.Component{constructor(){super(...arguments),d(this,"state",this.generateInitialState()),d(this,"onLayoutChange",e=>{this.props.onLayoutChange(e,{...this.props.layouts,[this.state.breakpoint]:e})})}generateInitialState(){let{width:e,breakpoints:t,layouts:r,cols:n}=this.props,i=(0,l.getBreakpointFromWidth)(t,e),a=(0,l.getColsFromBreakpoint)(i,n),o=!1===this.props.verticalCompact?null:this.props.compactType;return{layout:(0,l.findOrGenerateResponsiveLayout)(r,t,i,i,a,o),breakpoint:i,cols:a}}static getDerivedStateFromProps(e,t){if(!(0,a.deepEqual)(e.layouts,t.layouts)){let{breakpoint:r,cols:n}=t;return{layout:(0,l.findOrGenerateResponsiveLayout)(e.layouts,e.breakpoints,r,r,n,e.compactType),layouts:e.layouts}}return null}componentDidUpdate(e){this.props.width==e.width&&this.props.breakpoint===e.breakpoint&&(0,a.deepEqual)(this.props.breakpoints,e.breakpoints)&&(0,a.deepEqual)(this.props.cols,e.cols)||this.onWidthChange(e)}onWidthChange(e){let{breakpoints:t,cols:r,layouts:n,compactType:i}=this.props,a=this.props.breakpoint||(0,l.getBreakpointFromWidth)(this.props.breakpoints,this.props.width),s=this.state.breakpoint,u=(0,l.getColsFromBreakpoint)(a,r),c={...n};if(s!==a||e.breakpoints!==t||e.cols!==r){s in c||(c[s]=(0,o.cloneLayout)(this.state.layout));let e=(0,l.findOrGenerateResponsiveLayout)(c,t,a,s,u,i);e=(0,o.synchronizeLayoutWithChildren)(e,this.props.children,u,i,this.props.allowOverlap),c[a]=e,this.props.onBreakpointChange(a,u),this.props.onLayoutChange(e,c),this.setState({breakpoint:a,layout:e,cols:u})}let f=h(this.props.margin,a),d=h(this.props.containerPadding,a);this.props.onWidthChange(this.props.width,f,u,d)}render(){let{breakpoint:e,breakpoints:t,cols:r,layouts:i,margin:a,containerPadding:o,onBreakpointChange:l,onLayoutChange:u,onWidthChange:c,...d}=this.props;return n.createElement(s.default,f({},d,{margin:h(a,this.state.breakpoint),containerPadding:h(o,this.state.breakpoint),onLayoutChange:this.onLayoutChange,layout:this.state.layout,cols:this.state.cols}))}}t.default=y,d(y,"propTypes",{breakpoint:i.default.string,breakpoints:i.default.object,allowOverlap:i.default.bool,cols:i.default.object,margin:i.default.oneOfType([i.default.array,i.default.object]),containerPadding:i.default.oneOfType([i.default.array,i.default.object]),layouts(e,t){if("[object Object]"!==p(e[t]))throw Error("Layout property must be an object. Received: "+p(e[t]));Object.keys(e[t]).forEach(t=>{if(!(t in e.breakpoints))throw Error("Each key in layouts must align with a key in breakpoints.");(0,o.validateLayout)(e.layouts[t],"layouts."+t)})},width:i.default.number.isRequired,onBreakpointChange:i.default.func,onLayoutChange:i.default.func,onWidthChange:i.default.func}),d(y,"defaultProps",{breakpoints:{lg:1200,md:996,sm:768,xs:480,xxs:0},cols:{lg:12,md:10,sm:6,xs:4,xxs:2},containerPadding:{lg:null,md:null,sm:null,xs:null,xxs:null},layouts:{},margin:[10,10],allowOverlap:!1,onBreakpointChange:o.noop,onLayoutChange:o.noop,onWidthChange:o.noop})},6115:(e,t,r)=>{"use strict";var n=r(12115),i=r(49033),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,s=n.useEffect,u=n.useMemo,c=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=o(e,(f=u(function(){function e(e){if(!s){if(s=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,s=!1,u=void 0===r?null:r;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,r,n,i]))[0],f[1]);return s(function(){d.hasValue=!0,d.value=p},[p]),c(p),p}},7547:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(23676),i=r(72465),a=r(10656),o=r(81571);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},8287:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},8870:function(e,t,r){var n;!function(i){"use strict";var a,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,s="[DecimalError] ",u=s+"Invalid argument: ",c=s+"Exponent out of range: ",f=Math.floor,d=Math.pow,p=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=f(1286742750677284.5),y={};function g(e,t){var r,n,i,a,o,s,u,c,f=e.constructor,d=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?E(t,d):t;if(u=e.d,c=t.d,o=e.e,i=t.e,u=u.slice(),a=o-i){for(a<0?(n=u,a=-a,s=c.length):(n=c,i=o,s=u.length),a>(s=(o=Math.ceil(d/7))>s?o+1:s+1)&&(a=s,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((s=u.length)-(a=c.length)<0&&(a=s,n=c,c=u,u=n),r=0;a;)r=(u[--a]=u[a]+c[a]+r)/1e7|0,u[a]%=1e7;for(r&&(u.unshift(r),++i),s=u.length;0==u[--s];)u.pop();return t.d=u,t.e=i,l?E(t,d):t}function v(e,t,r){if(e!==~~e||e<t||e>r)throw Error(u+e)}function m(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=P(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=P(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}y.absoluteValue=y.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},y.comparedTo=y.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},y.dividedBy=y.div=function(e){return b(this,new this.constructor(e))},y.dividedToIntegerBy=y.idiv=function(e){var t=this.constructor;return E(b(this,new t(e),0,1),t.precision)},y.equals=y.eq=function(e){return!this.cmp(e)},y.exponent=function(){return w(this)},y.greaterThan=y.gt=function(e){return this.cmp(e)>0},y.greaterThanOrEqualTo=y.gte=function(e){return this.cmp(e)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(e){return 0>this.cmp(e)},y.lessThanOrEqualTo=y.lte=function(e){return 1>this.cmp(e)},y.logarithm=y.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(a))throw Error(s+"NaN");if(this.s<1)throw Error(s+(this.s?"NaN":"-Infinity"));return this.eq(a)?new r(0):(l=!1,t=b(j(this,i),j(e,i),i),l=!0,E(t,n))},y.minus=y.sub=function(e){return e=new this.constructor(e),this.s==e.s?M(this,e):g(this,(e.s=-e.s,e))},y.modulo=y.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(s+"NaN");return this.s?(l=!1,t=b(this,e,0,1).times(e),l=!0,this.minus(t)):E(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return j(this)},y.negated=y.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},y.plus=y.add=function(e){return e=new this.constructor(e),this.s==e.s?g(this,e):M(this,(e.s=-e.s,e))},y.precision=y.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(u+e);if(t=w(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},y.squareRoot=y.sqrt=function(){var e,t,r,n,i,a,o,u=this.constructor;if(this.s<1){if(!this.s)return new u(0);throw Error(s+"NaN")}for(e=w(this),l=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=m(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new u(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new u(i.toString()),i=o=(r=u.precision)+3;;)if(n=(a=n).plus(b(this,a,o+2)).times(.5),m(a.d).slice(0,o)===(t=m(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(E(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return l=!0,E(n,r)},y.times=y.mul=function(e){var t,r,n,i,a,o,s,u,c,f=this.constructor,d=this.d,p=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(u=d.length)<(c=p.length)&&(a=d,d=p,p=a,o=u,u=c,c=o),a=[],n=o=u+c;n--;)a.push(0);for(n=c;--n>=0;){for(t=0,i=u+n;i>n;)s=a[i]+p[n]*d[i-n-1]+t,a[i--]=s%1e7|0,t=s/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,l?E(e,f.precision):e},y.toDecimalPlaces=y.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(v(e,0,1e9),void 0===t?t=n.rounding:v(t,0,8),E(r,e+w(r)+1,t))},y.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=A(n,!0):(v(e,0,1e9),void 0===t?t=i.rounding:v(t,0,8),r=A(n=E(new i(n),e+1,t),!0,e+1)),r},y.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?A(this):(v(e,0,1e9),void 0===t?t=i.rounding:v(t,0,8),r=A((n=E(new i(this),e+w(this)+1,t)).abs(),!1,e+w(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var e=this.constructor;return E(new e(this),w(this)+1,e.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(e){var t,r,n,i,o,u,c=this,d=c.constructor,p=+(e=new d(e));if(!e.s)return new d(a);if(!(c=new d(c)).s){if(e.s<1)throw Error(s+"Infinity");return c}if(c.eq(a))return c;if(n=d.precision,e.eq(a))return E(c,n);if(u=(t=e.e)>=(r=e.d.length-1),o=c.s,u){if((r=p<0?-p:p)<=0x1fffffffffffff){for(i=new d(a),t=Math.ceil(n/7+4),l=!1;r%2&&k((i=i.times(c)).d,t),0!==(r=f(r/2));)k((c=c.times(c)).d,t);return l=!0,e.s<0?new d(a).div(i):E(i,n)}}else if(o<0)throw Error(s+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,c.s=1,l=!1,i=e.times(j(c,n+12)),l=!0,(i=x(i)).s=o,i},y.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=w(i),n=A(i,r<=a.toExpNeg||r>=a.toExpPos)):(v(e,1,1e9),void 0===t?t=a.rounding:v(t,0,8),r=w(i=E(new a(i),e,t)),n=A(i,e<=r||r<=a.toExpNeg,e)),n},y.toSignificantDigits=y.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(v(e,1,1e9),void 0===t?t=r.rounding:v(t,0,8)),E(new r(this),e,t)},y.toString=y.valueOf=y.val=y.toJSON=function(){var e=w(this),t=this.constructor;return A(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,u,c,f,d,p,h,y,g,v,m,b,x,O,P,j,S,M,A=n.constructor,k=n.s==i.s?1:-1,_=n.d,D=i.d;if(!n.s)return new A(n);if(!i.s)throw Error(s+"Division by zero");for(c=0,u=n.e-i.e,S=D.length,P=_.length,y=(h=new A(k)).d=[];D[c]==(_[c]||0);)++c;if(D[c]>(_[c]||0)&&--u,(b=null==a?a=A.precision:o?a+(w(n)-w(i))+1:a)<0)return new A(0);if(b=b/7+2|0,c=0,1==S)for(f=0,D=D[0],b++;(c<P||f)&&b--;c++)x=1e7*f+(_[c]||0),y[c]=x/D|0,f=x%D|0;else{for((f=1e7/(D[0]+1)|0)>1&&(D=e(D,f),_=e(_,f),S=D.length,P=_.length),O=S,v=(g=_.slice(0,S)).length;v<S;)g[v++]=0;(M=D.slice()).unshift(0),j=D[0],D[1]>=1e7/2&&++j;do f=0,(l=t(D,g,S,v))<0?(m=g[0],S!=v&&(m=1e7*m+(g[1]||0)),(f=m/j|0)>1?(f>=1e7&&(f=1e7-1),p=(d=e(D,f)).length,v=g.length,1==(l=t(d,g,p,v))&&(f--,r(d,S<p?M:D,p))):(0==f&&(l=f=1),d=D.slice()),(p=d.length)<v&&d.unshift(0),r(g,d,v),-1==l&&(v=g.length,(l=t(D,g,S,v))<1&&(f++,r(g,S<v?M:D,v))),v=g.length):0===l&&(f++,g=[0]),y[c++]=f,l&&g[0]?g[v++]=_[O]||0:(g=[_[O]],v=1);while((O++<P||void 0!==g[0])&&b--)}return y[0]||y.shift(),h.e=u,E(h,o?a+w(h)+1:a)}}();function x(e,t){var r,n,i,o,s,u=0,f=0,p=e.constructor,h=p.precision;if(w(e)>16)throw Error(c+w(e));if(!e.s)return new p(a);for(null==t?(l=!1,s=h):s=t,o=new p(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(s+=Math.log(d(2,f))/Math.LN10*2+5|0,r=n=i=new p(a),p.precision=s;;){if(n=E(n.times(e),s),r=r.times(++u),m((o=i.plus(b(n,r,s))).d).slice(0,s)===m(i.d).slice(0,s)){for(;f--;)i=E(i.times(i),s);return p.precision=h,null==t?(l=!0,E(i,h)):i}i=o}}function w(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(s+"LN10 precision limit exceeded");return E(new e(e.LN10),t)}function P(e){for(var t="";e--;)t+="0";return t}function j(e,t){var r,n,i,o,u,c,f,d,p,h=1,y=e,g=y.d,v=y.constructor,x=v.precision;if(y.s<1)throw Error(s+(y.s?"NaN":"-Infinity"));if(y.eq(a))return new v(0);if(null==t?(l=!1,d=x):d=t,y.eq(10))return null==t&&(l=!0),O(v,d);if(v.precision=d+=10,n=(r=m(g)).charAt(0),!(15e14>Math.abs(o=w(y))))return f=O(v,d+2,x).times(o+""),y=j(new v(n+"."+r.slice(1)),d-10).plus(f),v.precision=x,null==t?(l=!0,E(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=m((y=y.times(e)).d)).charAt(0),h++;for(o=w(y),n>1?(y=new v("0."+r),o++):y=new v(n+"."+r.slice(1)),c=u=y=b(y.minus(a),y.plus(a),d),p=E(y.times(y),d),i=3;;){if(u=E(u.times(p),d),m((f=c.plus(b(u,new v(i),d))).d).slice(0,d)===m(c.d).slice(0,d))return c=c.times(2),0!==o&&(c=c.plus(O(v,d+2,x).times(o+""))),c=b(c,new v(h),d),v.precision=x,null==t?(l=!0,E(c,x)):c;c=f,i+=2}}function S(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=f((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>h||e.e<-h))throw Error(c+r)}else e.s=0,e.e=0,e.d=[0];return e}function E(e,t,r){var n,i,a,o,s,u,p,y,g=e.d;for(o=1,a=g[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,p=g[y=0];else{if((y=Math.ceil((n+1)/7))>=(a=g.length))return e;for(o=1,p=a=g[y];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(s=p/(a=d(10,o-i-1))%10|0,u=t<0||void 0!==g[y+1]||p%a,u=r<4?(s||u)&&(0==r||r==(e.s<0?3:2)):s>5||5==s&&(4==r||u||6==r&&(n>0?i>0?p/d(10,o-i):0:g[y-1])%10&1||r==(e.s<0?8:7))),t<1||!g[0])return u?(a=w(e),g.length=1,t=t-a-1,g[0]=d(10,(7-t%7)%7),e.e=f(-t/7)||0):(g.length=1,g[0]=e.e=e.s=0),e;if(0==n?(g.length=y,a=1,y--):(g.length=y+1,a=d(10,7-n),g[y]=i>0?(p/d(10,o-i)%d(10,i)|0)*a:0),u)for(;;)if(0==y){1e7==(g[0]+=a)&&(g[0]=1,++e.e);break}else{if(g[y]+=a,1e7!=g[y])break;g[y--]=0,a=1}for(n=g.length;0===g[--n];)g.pop();if(l&&(e.e>h||e.e<-h))throw Error(c+w(e));return e}function M(e,t){var r,n,i,a,o,s,u,c,f,d,p=e.constructor,h=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),l?E(t,h):t;if(u=e.d,d=t.d,n=t.e,c=e.e,u=u.slice(),o=c-n){for((f=o<0)?(r=u,o=-o,s=d.length):(r=d,n=c,s=u.length),o>(i=Math.max(Math.ceil(h/7),s)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=u.length)<(s=d.length))&&(s=i),i=0;i<s;i++)if(u[i]!=d[i]){f=u[i]<d[i];break}o=0}for(f&&(r=u,u=d,d=r,t.s=-t.s),s=u.length,i=d.length-s;i>0;--i)u[s++]=0;for(i=d.length;i>o;){if(u[--i]<d[i]){for(a=i;a&&0===u[--a];)u[a]=1e7-1;--u[a],u[i]+=1e7}u[i]-=d[i]}for(;0===u[--s];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(t.d=u,t.e=n,l?E(t,h):t):new p(0)}function A(e,t,r){var n,i=w(e),a=m(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+P(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+P(-i-1)+a,r&&(n=r-o)>0&&(a+=P(n))):i>=o?(a+=P(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+P(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=P(n))),e.s<0?"-"+a:a}function k(e,t){if(e.length>t)return e.length=t,!0}function _(e){if(!e||"object"!=typeof e)throw Error(s+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(f(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(u+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(u+r+": "+n);return this}(o=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(u+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return S(this,e.toString())}if("string"!=typeof e)throw Error(u+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,p.test(e))S(this,e);else throw Error(u+e)}if(a.prototype=y,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=_,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(o)).default=o.Decimal=o,a=new o(1),void 0===(n=(function(){return o}).call(t,r,t,e))||(e.exports=n)}(0)},9055:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.canDragX=function(e){return"both"===e.props.axis||"x"===e.props.axis},t.canDragY=function(e){return"both"===e.props.axis||"y"===e.props.axis},t.createCoreData=function(e,t,r){let i=!(0,n.isNum)(e.lastX),o=a(e);return i?{node:o,deltaX:0,deltaY:0,lastX:t,lastY:r,x:t,y:r}:{node:o,deltaX:t-e.lastX,deltaY:r-e.lastY,lastX:e.lastX,lastY:e.lastY,x:t,y:r}},t.createDraggableData=function(e,t){let r=e.props.scale;return{node:t.node,x:e.state.x+t.deltaX/r,y:e.state.y+t.deltaY/r,deltaX:t.deltaX/r,deltaY:t.deltaY/r,lastX:e.state.x,lastY:e.state.y}},t.getBoundPosition=function(e,t,r){var o;if(!e.props.bounds)return[t,r];let{bounds:l}=e.props;l="string"==typeof l?l:{left:(o=l).left,top:o.top,right:o.right,bottom:o.bottom};let s=a(e);if("string"==typeof l){let e,{ownerDocument:t}=s,r=t.defaultView;if(!((e="parent"===l?s.parentNode:t.querySelector(l))instanceof r.HTMLElement))throw Error('Bounds selector "'+l+'" could not find an element.');let a=r.getComputedStyle(s),o=r.getComputedStyle(e);l={left:-s.offsetLeft+(0,n.int)(o.paddingLeft)+(0,n.int)(a.marginLeft),top:-s.offsetTop+(0,n.int)(o.paddingTop)+(0,n.int)(a.marginTop),right:(0,i.innerWidth)(e)-(0,i.outerWidth)(s)-s.offsetLeft+(0,n.int)(o.paddingRight)-(0,n.int)(a.marginRight),bottom:(0,i.innerHeight)(e)-(0,i.outerHeight)(s)-s.offsetTop+(0,n.int)(o.paddingBottom)-(0,n.int)(a.marginBottom)}}return(0,n.isNum)(l.right)&&(t=Math.min(t,l.right)),(0,n.isNum)(l.bottom)&&(r=Math.min(r,l.bottom)),(0,n.isNum)(l.left)&&(t=Math.max(t,l.left)),(0,n.isNum)(l.top)&&(r=Math.max(r,l.top)),[t,r]},t.getControlPosition=function(e,t,r){let n="number"==typeof t?(0,i.getTouch)(e,t):null;if("number"==typeof t&&!n)return null;let o=a(r),l=r.props.offsetParent||o.offsetParent||o.ownerDocument.body;return(0,i.offsetXYFromParent)(n||e,l,r.props.scale)},t.snapToGrid=function(e,t,r){return[Math.round(t/e[0])*e[0],Math.round(r/e[1])*e[1]]};var n=r(34425),i=r(30624);function a(e){let t=e.findDOMNode();if(!t)throw Error("<DraggableCore>: Unmounted during event!");return t}},9819:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{A:()=>n}),Array.prototype.slice},10656:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(68179),i=r(99279);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},10921:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(27040),i=r(14545),a=r(46200),o=r(64072);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a)if(i.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var s=t,u=r,c=l;if(0===u.length)return c;let e=s;for(let t=0;t<u.length;t++){if(null==e||n.isUnsafeProperty(u[t]))return c;e=e[u[t]]}return void 0===e?c:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},11808:(e,t,r)=>{"use strict";r.d(t,{_:()=>b});var n=r(12115),i=r(2348),a=r(70788),o=r(39226),l=r(14299),s=r(81971),u=r(71807),c=e=>{var t=(0,u.r)();return(0,s.G)(r=>(0,l.Gx)(r,"xAxis",e,t))},f=e=>{var t=(0,u.r)();return(0,s.G)(r=>(0,l.Gx)(r,"yAxis",e,t))},d=r(93389),p=r(74460),h=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function y(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e){var{direction:t,width:r,dataKey:l,isAnimationActive:s,animationBegin:u,animationDuration:d,animationEasing:y}=e,v=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,h),m=(0,a.J9)(v,!1),{data:b,dataPointFormatter:x,xAxisId:w,yAxisId:O,errorBarOffset:P}=(0,o.G9)(),j=c(w),S=f(O);if((null==j?void 0:j.scale)==null||(null==S?void 0:S.scale)==null||null==b||"x"===t&&"number"!==j.type)return null;var E=b.map(e=>{var a,o,{x:c,y:f,value:h,errorVal:v}=x(e,l,t);if(!v)return null;var b=[];if(Array.isArray(v)?[a,o]=v:a=o=v,"x"===t){var{scale:w}=j,O=f+P,E=O+r,M=O-r,A=w(h-a),k=w(h+o);b.push({x1:k,y1:E,x2:k,y2:M}),b.push({x1:A,y1:O,x2:k,y2:O}),b.push({x1:A,y1:E,x2:A,y2:M})}else if("y"===t){var{scale:_}=S,D=c+P,C=D-r,T=D+r,N=_(h-a),z=_(h+o);b.push({x1:C,y1:z,x2:T,y2:z}),b.push({x1:D,y1:N,x2:D,y2:z}),b.push({x1:C,y1:N,x2:T,y2:N})}var I="".concat(c+P,"px ").concat(f+P,"px");return n.createElement(i.W,g({className:"recharts-errorBar",key:"bar-".concat(b.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},m),b.map(e=>{var t=s?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return n.createElement(p.i,{from:{transform:"scaleY(0)",transformOrigin:I},to:{transform:"scaleY(1)",transformOrigin:I},begin:u,easing:y,isActive:s,duration:d,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:I}},n.createElement("line",g({},e,{style:t})))}))});return n.createElement(i.W,{className:"recharts-errorBars"},E)}var m=(0,n.createContext)(void 0);function b(e){var{direction:t,children:r}=e;return n.createElement(m.Provider,{value:t},r)}var x={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function w(e){var t,r,i=(t=e.direction,r=(0,n.useContext)(m),null!=t?t:null!=r?r:"x"),{width:a,isAnimationActive:l,animationBegin:s,animationDuration:u,animationEasing:c}=(0,d.e)(e,x);return n.createElement(n.Fragment,null,n.createElement(o.pU,{dataKey:e.dataKey,direction:i}),n.createElement(v,g({},e,{direction:i,width:a,isAnimationActive:l,animationBegin:s,animationDuration:u,animationEasing:c})))}class O extends n.Component{render(){return n.createElement(w,this.props)}}y(O,"defaultProps",x),y(O,"displayName","ErrorBar")},11928:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var n=r(34890);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o=(e,t,r,i)=>{if(null==t)return n.k_;var o=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==o)return n.k_;if(o.active)return o;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var l=!0===e.settings.active;if(null!=o.index){if(l)return a(a({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return a(a({},n.k_),{},{coordinate:o.coordinate})}},12287:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(e,t,r)=>r},12429:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(44117);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},12486:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},13279:(e,t,r)=>{"use strict";r.d(t,{N:()=>et,l:()=>ee});var n=r(12115),i=r(52596),a=r(70688),o=r(51172),l=r(2348),s=r(36079),u=r(11808),c=r(16377),f=r(70788),d=r(41643),p=r(39827),h=r(93262),y=r(56091),g=r(39226),v=r(37195),m=r(97238),b=r(71807),x=r(68924),w=r(60356),O=r(14299),P=(e,t,r,n)=>(0,O.Gx)(e,"xAxis",t,n),j=(e,t,r,n)=>(0,O.CR)(e,"xAxis",t,n),S=(e,t,r,n)=>(0,O.Gx)(e,"yAxis",r,n),E=(e,t,r,n)=>(0,O.CR)(e,"yAxis",r,n),M=(0,x.Mz)([m.fz,P,S,j,E],(e,t,r,n,i)=>(0,p._L)(e,"xAxis")?(0,p.Hj)(t,n,!1):(0,p.Hj)(r,i,!1)),A=(0,x.Mz)([O.ld,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"line"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),k=(0,x.Mz)([m.fz,P,S,j,E,A,M,w.HS],(e,t,r,n,i,a,o,l)=>{var s,{chartData:u,dataStartIndex:c,dataEndIndex:f}=l;if(null!=a&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=o){var{dataKey:d,data:p}=a;if(null!=(s=null!=p&&p.length>0?p:null==u?void 0:u.slice(c,f+1)))return ee({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:d,bandSize:o,displayedData:s})}}),_=r(81971),D=r(79020),C=r(39426),T=r(93389),N=r(74460),z=["type","layout","connectNulls","needClip"],I=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function R(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function L(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?L(Object(r),!0).forEach(function(t){$(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function $(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function B(){return(B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var W=e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,p.uM)(r,t),payload:e}]};function U(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:s}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:(0,p.uM)(o,t),hide:l,type:e.tooltipType,color:e.stroke,unit:s}}}var F=(e,t)=>"".concat(t,"px ").concat(e-t,"px"),K=(e,t,r)=>{var n=r.reduce((e,t)=>e+t);if(!n)return F(t,e);for(var i=Math.floor(e/n),a=e%n,o=t-e,l=[],s=0,u=0;s<r.length;u+=r[s],++s)if(u+r[s]>a){l=[...r.slice(0,s),a-u];break}var c=l.length%2==0?[0,o]:[o];return[...function(e,t){for(var r=e.length%2!=0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}(r,i),...l,...c].map(e=>"".concat(e,"px")).join(", ")};function G(e){var{clipPathId:t,points:r,props:a}=e,{dot:s,dataKey:u,needClip:c}=a;if(null==r||!s&&1!==r.length)return null;var d=(0,f.y$)(s),p=(0,f.J9)(a,!1),h=(0,f.J9)(s,!0),y=r.map((e,t)=>{var a,l=H(H(H({key:"dot-".concat(t),r:3},p),h),{},{index:t,cx:e.x,cy:e.y,dataKey:u,value:e.value,payload:e.payload,points:r});if(n.isValidElement(s))a=n.cloneElement(s,l);else if("function"==typeof s)a=s(l);else{var c=(0,i.$)("recharts-line-dot","boolean"!=typeof s?s.className:"");a=n.createElement(o.c,B({},l,{className:c}))}return a}),g={clipPath:c?"url(#clipPath-".concat(d?"":"dots-").concat(t,")"):null};return n.createElement(l.W,B({className:"recharts-line-dots",key:"dots"},g),y)}function q(e){var{clipPathId:t,pathRef:r,points:i,strokeDasharray:o,props:l,showLabels:u}=e,{type:c,layout:d,connectNulls:p,needClip:h}=l,y=R(l,z),g=H(H({},(0,f.J9)(y,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:h?"url(#clipPath-".concat(t,")"):null,points:i,type:c,layout:d,connectNulls:p,strokeDasharray:null!=o?o:l.strokeDasharray});return n.createElement(n.Fragment,null,(null==i?void 0:i.length)>1&&n.createElement(a.I,B({},g,{pathRef:r})),n.createElement(G,{points:i,clipPathId:t,props:l}),u&&s.Z.renderCallByParent(l,i))}function V(e){var{clipPathId:t,props:r,pathRef:i,previousPointsRef:a,longestAnimatedLengthRef:o}=e,{points:l,strokeDasharray:s,isAnimationActive:u,animationBegin:f,animationDuration:d,animationEasing:p,animateNewValues:h,width:y,height:g,onAnimationEnd:v,onAnimationStart:m}=r,b=a.current,x=(0,C.n)(r,"recharts-line-"),[w,O]=(0,n.useState)(!1),P=(0,n.useCallback)(()=>{"function"==typeof v&&v(),O(!1)},[v]),j=(0,n.useCallback)(()=>{"function"==typeof m&&m(),O(!0)},[m]),S=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}(i.current),E=o.current;return n.createElement(N.i,{begin:f,duration:d,isActive:u,easing:p,from:{t:0},to:{t:1},onAnimationEnd:P,onAnimationStart:j,key:x},e=>{var u,{t:f}=e,d=Math.min((0,c.Dj)(E,S+E)(f),S);if(u=s?K(d,S,"".concat(s).split(/[,\s]+/gim).map(e=>parseFloat(e))):F(S,d),b){var p=b.length/l.length,v=1===f?l:l.map((e,t)=>{var r=Math.floor(t*p);if(b[r]){var n=b[r],i=(0,c.Dj)(n.x,e.x),a=(0,c.Dj)(n.y,e.y);return H(H({},e),{},{x:i(f),y:a(f)})}if(h){var o=(0,c.Dj)(2*y,e.x),l=(0,c.Dj)(g/2,e.y);return H(H({},e),{},{x:o(f),y:l(f)})}return H(H({},e),{},{x:e.x,y:e.y})});return a.current=v,n.createElement(q,{props:r,points:v,clipPathId:t,pathRef:i,showLabels:!w,strokeDasharray:u})}return f>0&&S>0&&(a.current=l,o.current=d),n.createElement(q,{props:r,points:l,clipPathId:t,pathRef:i,showLabels:!w,strokeDasharray:u})})}function Y(e){var{clipPathId:t,props:r}=e,{points:i,isAnimationActive:a}=r,o=(0,n.useRef)(null),l=(0,n.useRef)(0),s=(0,n.useRef)(null),u=o.current;return a&&i&&i.length&&u!==i?n.createElement(V,{props:r,clipPathId:t,previousPointsRef:o,longestAnimatedLengthRef:l,pathRef:s}):n.createElement(q,{props:r,points:i,clipPathId:t,pathRef:s,showLabels:!0})}var Z=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:(0,p.kr)(e.payload,t)});class X extends n.Component{render(){var e,{hide:t,dot:r,points:a,className:o,xAxisId:s,yAxisId:d,top:p,left:y,width:m,height:b,id:x,needClip:w,layout:O}=this.props;if(t)return null;var P=(0,i.$)("recharts-line",o),j=(0,c.uy)(x)?this.id:x,{r:S=3,strokeWidth:E=2}=null!=(e=(0,f.J9)(r,!1))?e:{r:3,strokeWidth:2},M=(0,f.y$)(r),A=2*S+E;return n.createElement(n.Fragment,null,n.createElement(l.W,{className:P},w&&n.createElement("defs",null,n.createElement(v.Q,{clipPathId:j,xAxisId:s,yAxisId:d}),!M&&n.createElement("clipPath",{id:"clipPath-dots-".concat(j)},n.createElement("rect",{x:y-A/2,y:p-A/2,width:m+A,height:b+A}))),n.createElement(Y,{props:this.props,clipPathId:j}),n.createElement(u._,{direction:"horizontal"===O?"y":"x"},n.createElement(g.zk,{xAxisId:s,yAxisId:d,data:a,dataPointFormatter:Z,errorBarOffset:0},this.props.children))),n.createElement(h.W,{activeDot:this.props.activeDot,points:a,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}constructor(){super(...arguments),$(this,"id",(0,c.NF)("recharts-line-"))}}var J={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!d.m.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function Q(e){var t=(0,T.e)(e,J),{activeDot:r,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:l,connectNulls:s,dot:u,hide:c,isAnimationActive:f,label:d,legendType:p,xAxisId:h,yAxisId:y}=t,g=R(t,I),{needClip:x}=(0,v.l)(h,y),{height:w,width:O,left:P,top:j}=(0,m.hj)(),S=(0,m.WX)(),E=(0,b.r)(),M=(0,n.useMemo)(()=>({dataKey:e.dataKey,data:e.data}),[e.dataKey,e.data]),A=(0,_.G)(e=>k(e,h,y,E,M));return"horizontal"!==S&&"vertical"!==S?null:n.createElement(X,B({},g,{connectNulls:s,dot:u,activeDot:r,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:l,isAnimationActive:f,hide:c,label:d,legendType:p,xAxisId:h,yAxisId:y,points:A,layout:S,height:w,width:O,left:P,top:j,needClip:x}))}function ee(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:l,displayedData:s}=e;return s.map((e,s)=>{var u=(0,p.kr)(e,o);return"horizontal"===t?{x:(0,p.nb)({axis:r,ticks:i,bandSize:l,entry:e,index:s}),y:(0,c.uy)(u)?null:n.scale(u),value:u,payload:e}:{x:(0,c.uy)(u)?null:r.scale(u),y:(0,p.nb)({axis:n,ticks:a,bandSize:l,entry:e,index:s}),value:u,payload:e}})}class et extends n.PureComponent{render(){return n.createElement(g._S,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},n.createElement(D.A,{legendPayload:W(this.props)}),n.createElement(y.r,{fn:U,args:this.props}),n.createElement(Q,this.props))}}$(et,"displayName","Line"),$(et,"defaultProps",J)},13717:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14299:(e,t,r)=>{"use strict";r.d(t,{kz:()=>ia,fb:()=>n9,q:()=>iO,tP:()=>iD,g1:()=>iL,iv:()=>an,Nk:()=>n4,pM:()=>ir,Oz:()=>ix,tF:()=>at,rj:()=>n2,ec:()=>nJ,bb:()=>ij,xp:()=>iI,wL:()=>iA,sr:()=>iT,Qn:()=>iz,MK:()=>ie,IO:()=>n0,P9:()=>ih,S5:()=>ic,PU:()=>n$,cd:()=>nW,eo:()=>nY,yi:()=>id,ZB:()=>aa,D5:()=>iG,iV:()=>iV,Hd:()=>nq,Gx:()=>as,DP:()=>nG,BQ:()=>ae,_y:()=>ac,AV:()=>iM,um:()=>nV,xM:()=>iN,gT:()=>ig,Kr:()=>ip,$X:()=>im,TC:()=>it,Zi:()=>ao,CR:()=>al,ld:()=>nZ,L$:()=>i8,Rl:()=>nB,Lw:()=>i2,KR:()=>i6,sf:()=>nU,wP:()=>i7});var n,i,a,o,l,s,u,c={};r.r(c),r.d(c,{scaleBand:()=>w,scaleDiverging:()=>function e(){var t=eK(r9()(eM));return t.copy=function(){return r5(t,e())},y.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=eQ(r9()).domain([.1,1,10]);return t.copy=function(){return r5(t,e()).base(t.base())},y.apply(t,arguments)},scaleDivergingPow:()=>r8,scaleDivergingSqrt:()=>r6,scaleDivergingSymlog:()=>function e(){var t=e2(r9());return t.copy=function(){return r5(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,eS),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,eS):[0,1],eK(n)},scaleImplicit:()=>b,scaleLinear:()=>function e(){var t=eT();return t.copy=function(){return eD(t,e())},h.apply(t,arguments),eK(t)},scaleLog:()=>function e(){let t=eQ(eC()).domain([1,10]);return t.copy=()=>eD(t,e()).base(t.base()),h.apply(t,arguments),t},scaleOrdinal:()=>x,scalePoint:()=>O,scalePow:()=>e8,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=N){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e*=1)?t:n[I(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(_),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},h.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[I(a,e,0,i)]:t}function s(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,s()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,s()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},h.apply(eK(l),arguments)},scaleRadial:()=>function e(){var t,r=eT(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(e7(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,eS)).map(e7)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},h.apply(a,arguments),eK(a)},scaleSequential:()=>function e(){var t=eK(r2()(eM));return t.copy=function(){return r5(t,e())},y.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=eQ(r2()).domain([1,10]);return t.copy=function(){return r5(t,e()).base(t.base())},y.apply(t,arguments)},scaleSequentialPow:()=>r4,scaleSequentialQuantile:()=>function e(){var t=[],r=eM;function n(e){if(null!=e&&!isNaN(e*=1))return r((I(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(_),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return tt(e);if(t>=1)return te(e);var n,i=(n-1)*t,a=Math.floor(i),o=te((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?tr:function(e=_){if(e===_)return tr;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,s=Math.log(o),u=.5*Math.exp(2*s/3),c=.5*Math.sqrt(s*u*(o-u)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*u/o+c)),d=Math.min(i,Math.floor(r+(o-l)*u/o+c));e(t,r,f,d,a)}let o=t[r],l=n,s=i;for(tn(t,n,r),a(t[i],o)>0&&tn(t,n,i);l<s;){for(tn(t,l,s),++l,--s;0>a(t[l],o);)++l;for(;a(t[s],o)>0;)--s}0===a(t[n],o)?tn(t,n,s):tn(t,++s,i),s<=r&&(n=s+1),r<=s&&(i=s-1)}return t})(e,a).subarray(0,a+1));return o+(tt(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},y.apply(n,arguments)},scaleSequentialSqrt:()=>r3,scaleSequentialSymlog:()=>function e(){var t=e2(r2());return t.copy=function(){return r5(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleSqrt:()=>e6,scaleSymlog:()=>function e(){var t=e2(eC());return t.copy=function(){return eD(t,e()).constant(t.constant())},h.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[I(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},h.apply(a,arguments)},scaleTime:()=>r0,scaleUtc:()=>r1,tickFormat:()=>eF});var f=r(68924),d=r(83949),p=r.n(d);function h(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function y(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class g extends Map{constructor(e,t=m){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(v(this,e))}has(e){return super.has(v(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function v({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function m(e){return null!==e&&"object"==typeof e?e.valueOf():e}let b=Symbol("implicit");function x(){var e=new g,t=[],r=[],n=b;function i(i){let a=e.get(i);if(void 0===a){if(n!==b)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new g,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return x(t,r).unknown(n)},h.apply(i,arguments),i}function w(){var e,t,r=x().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,s=0,u=0,c=.5;function f(){var r=n().length,f=o<a,d=f?o:a,p=f?a:o;e=(p-d)/Math.max(1,r-s+2*u),l&&(e=Math.floor(e)),d+=(p-d-e*(r-s))*c,t=e*(1-s),l&&(d=Math.round(d),t=Math.round(t));var h=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return d+e*t});return i(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a*=1,o*=1,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a*=1,o*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(s=Math.min(1,u=+e),f()):s},r.paddingInner=function(e){return arguments.length?(s=Math.min(1,e),f()):s},r.paddingOuter=function(e){return arguments.length?(u=+e,f()):u},r.align=function(e){return arguments.length?(c=Math.max(0,Math.min(1,e)),f()):c},r.copy=function(){return w(n(),[a,o]).round(l).paddingInner(s).paddingOuter(u).align(c)},h.apply(f(),arguments)}function O(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(w.apply(null,arguments).paddingInner(1))}let P=Math.sqrt(50),j=Math.sqrt(10),S=Math.sqrt(2);function E(e,t,r){let n,i,a,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),s=o/Math.pow(10,l),u=s>=P?10:s>=j?5:s>=S?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/u)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*u)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?E(e,t,2*r):[n,i,a]}function M(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?E(t,e,r):E(e,t,r);if(!(a>=i))return[];let l=a-i+1,s=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)s[e]=-((a-e)/o);else for(let e=0;e<l;++e)s[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)s[e]=-((i+e)/o);else for(let e=0;e<l;++e)s[e]=(i+e)*o;return s}function A(e,t,r){return E(e*=1,t*=1,r*=1)[2]}function k(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?A(t,e,r):A(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function _(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function D(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function C(e){let t,r,n;function i(e,n,a=0,o=e.length){if(a<o){if(0!==t(n,n))return o;do{let t=a+o>>>1;0>r(e[t],n)?a=t+1:o=t}while(a<o)}return a}return 2!==e.length?(t=_,r=(t,r)=>_(e(t),r),n=(t,r)=>e(t)-r):(t=e===_||e===D?e:T,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function T(){return 0}function N(e){return null===e?NaN:+e}let z=C(_),I=z.right;function R(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function L(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function H(){}z.left,C(N).center;var $="\\s*([+-]?\\d+)\\s*",B="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",W="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",U=/^#([0-9a-f]{3,8})$/,F=RegExp(`^rgb\\(${$},${$},${$}\\)$`),K=RegExp(`^rgb\\(${W},${W},${W}\\)$`),G=RegExp(`^rgba\\(${$},${$},${$},${B}\\)$`),q=RegExp(`^rgba\\(${W},${W},${W},${B}\\)$`),V=RegExp(`^hsl\\(${B},${W},${W}\\)$`),Y=RegExp(`^hsla\\(${B},${W},${W},${B}\\)$`),Z={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function X(){return this.rgb().formatHex()}function J(){return this.rgb().formatRgb()}function Q(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=U.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?ee(t):3===r?new en(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?et(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?et(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=F.exec(e))?new en(t[1],t[2],t[3],1):(t=K.exec(e))?new en(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=G.exec(e))?et(t[1],t[2],t[3],t[4]):(t=q.exec(e))?et(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=V.exec(e))?eu(t[1],t[2]/100,t[3]/100,1):(t=Y.exec(e))?eu(t[1],t[2]/100,t[3]/100,t[4]):Z.hasOwnProperty(e)?ee(Z[e]):"transparent"===e?new en(NaN,NaN,NaN,0):null}function ee(e){return new en(e>>16&255,e>>8&255,255&e,1)}function et(e,t,r,n){return n<=0&&(e=t=r=NaN),new en(e,t,r,n)}function er(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof H||(i=Q(i)),i)?new en((i=i.rgb()).r,i.g,i.b,i.opacity):new en:new en(e,t,r,null==n?1:n)}function en(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function ei(){return`#${es(this.r)}${es(this.g)}${es(this.b)}`}function ea(){let e=eo(this.opacity);return`${1===e?"rgb(":"rgba("}${el(this.r)}, ${el(this.g)}, ${el(this.b)}${1===e?")":`, ${e})`}`}function eo(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function el(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function es(e){return((e=el(e))<16?"0":"")+e.toString(16)}function eu(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ef(e,t,r,n)}function ec(e){if(e instanceof ef)return new ef(e.h,e.s,e.l,e.opacity);if(e instanceof H||(e=Q(e)),!e)return new ef;if(e instanceof ef)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,s=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=s<.5?a+i:2-a-i,o*=60):l=s>0&&s<1?0:o,new ef(o,l,s,e.opacity)}function ef(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function ed(e){return(e=(e||0)%360)<0?e+360:e}function ep(e){return Math.max(0,Math.min(1,e||0))}function eh(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function ey(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}R(H,Q,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:X,formatHex:X,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ec(this).formatHsl()},formatRgb:J,toString:J}),R(en,er,L(H,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new en(el(this.r),el(this.g),el(this.b),eo(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ei,formatHex:ei,formatHex8:function(){return`#${es(this.r)}${es(this.g)}${es(this.b)}${es((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:ea,toString:ea})),R(ef,function(e,t,r,n){return 1==arguments.length?ec(e):new ef(e,t,r,null==n?1:n)},L(H,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ef(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ef(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new en(eh(e>=240?e-240:e+120,i,n),eh(e,i,n),eh(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ef(ed(this.h),ep(this.s),ep(this.l),eo(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=eo(this.opacity);return`${1===e?"hsl(":"hsla("}${ed(this.h)}, ${100*ep(this.s)}%, ${100*ep(this.l)}%${1===e?")":`, ${e})`}`}}));let eg=e=>()=>e;function ev(e,t){var r,n,i=t-e;return i?(r=e,n=i,function(e){return r+e*n}):eg(isNaN(e)?t:e)}let em=function e(t){var r,n=1==(r=+t)?ev:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):eg(isNaN(e)?t:e)};function i(e,t){var r=n((e=er(e)).r,(t=er(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=ev(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function eb(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=er(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}eb(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return ey((r-n/t)*t,o,i,a,l)}}),eb(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return ey((r-n/t)*t,i,a,o,l)}});function ex(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var ew=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eO=RegExp(ew.source,"g");function eP(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?eg(t):("number"===i?ex:"string"===i?(n=Q(t))?(t=n,em):function(e,t){var r,n,i,a,o,l=ew.lastIndex=eO.lastIndex=0,s=-1,u=[],c=[];for(e+="",t+="";(i=ew.exec(e))&&(a=eO.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),u[s]?u[s]+=o:u[++s]=o),(i=i[0])===(a=a[0])?u[s]?u[s]+=a:u[++s]=a:(u[++s]=null,c.push({i:s,x:ex(i,a)})),l=eO.lastIndex;return l<t.length&&(o=t.slice(l),u[s]?u[s]+=o:u[++s]=o),u.length<2?c[0]?(r=c[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=c.length,function(e){for(var r,n=0;n<t;++n)u[(r=c[n]).i]=r.x(e);return u.join("")})}:t instanceof Q?em:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=eP(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=eP(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:ex:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function ej(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function eS(e){return+e}var eE=[0,1];function eM(e){return e}function eA(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function ek(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=eA(i,n),a=r(o,a)):(n=eA(n,i),a=r(a,o)),function(e){return a(n(e))}}function e_(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=eA(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=I(e,t,1,n)-1;return a[r](i[r](t))}}function eD(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function eC(){var e,t,r,n,i,a,o=eE,l=eE,s=eP,u=eM;function c(){var e,t,r,s=Math.min(o.length,l.length);return u!==eM&&(e=o[0],t=o[s-1],e>t&&(r=e,e=t,t=r),u=function(r){return Math.max(e,Math.min(t,r))}),n=s>2?e_:ek,i=a=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(o.map(e),l,s)))(e(u(t)))}return f.invert=function(r){return u(t((a||(a=n(l,o.map(e),ex)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,eS),c()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),c()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),s=ej,c()},f.clamp=function(e){return arguments.length?(u=!!e||eM,c()):u!==eM},f.interpolate=function(e){return arguments.length?(s=e,c()):s},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,c()}}function eT(){return eC()(eM,eM)}var eN=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ez(e){var t;if(!(t=eN.exec(e)))throw Error("invalid format: "+e);return new eI({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function eI(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function eR(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function eL(e){return(e=eR(Math.abs(e)))?e[1]:NaN}function eH(e,t){var r=eR(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}ez.prototype=eI.prototype,eI.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let e$={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>eH(100*e,t),r:eH,s:function(e,t){var r=eR(e,t);if(!r)return e+"";var i=r[0],a=r[1],o=a-(n=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,l=i.length;return o===l?i:o>l?i+Array(o-l+1).join("0"):o>0?i.slice(0,o)+"."+i.slice(o):"0."+Array(1-o).join("0")+eR(e,Math.max(0,t+o-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function eB(e){return e}var eW=Array.prototype.map,eU=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eF(e,t,r,n){var i,l,s,u=k(e,t,r);switch((n=ez(null==n?",f":n)).type){case"s":var c=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(s=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(eL(c)/3)))-eL(Math.abs(u))))||(n.precision=s),o(n,c);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(s=Math.max(0,eL(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=u)))-eL(i))+1)||(n.precision=s-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(s=Math.max(0,-eL(Math.abs(u))))||(n.precision=s-("%"===n.type)*2)}return a(n)}function eK(e){var t=e.domain;return e.ticks=function(e){var r=t();return M(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return eF(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,s=a[o],u=a[l],c=10;for(u<s&&(i=s,s=u,u=i,i=o,o=l,l=i);c-- >0;){if((i=A(s,u,r))===n)return a[o]=s,a[l]=u,t(a);if(i>0)s=Math.floor(s/i)*i,u=Math.ceil(u/i)*i;else if(i<0)s=Math.ceil(s*i)/i,u=Math.floor(u*i)/i;else break;n=i}return e},e}function eG(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function eq(e){return Math.log(e)}function eV(e){return Math.exp(e)}function eY(e){return-Math.log(-e)}function eZ(e){return-Math.exp(-e)}function eX(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eJ(e){return(t,r)=>-e(-t,r)}function eQ(e){let t,r,n=e(eq,eV),i=n.domain,o=10;function l(){var a,l;return t=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(l=o)?eX:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=eJ(t),r=eJ(r),e(eY,eZ)):e(eq,eV),n}return n.base=function(e){return arguments.length?(o=+e,l()):o},n.domain=function(e){return arguments.length?(i(e),l()):i()},n.ticks=e=>{let n,a,l=i(),s=l[0],u=l[l.length-1],c=u<s;c&&([s,u]=[u,s]);let f=t(s),d=t(u),p=null==e?10:+e,h=[];if(!(o%1)&&d-f<p){if(f=Math.floor(f),d=Math.ceil(d),s>0){for(;f<=d;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<s)){if(a>u)break;h.push(a)}}else for(;f<=d;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<s)){if(a>u)break;h.push(a)}2*h.length<p&&(h=M(s,u,p))}else h=M(f,d,Math.min(d-f,p)).map(r);return c?h.reverse():h},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=ez(i)).precision||(i.trim=!0),i=a(i)),e===1/0)return i;let l=Math.max(1,o*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*o<o-.5&&(n*=o),n<=l?i(e):""}},n.nice=()=>i(eG(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function e0(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function e1(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function e2(e){var t=1,r=e(e0(1),e1(t));return r.constant=function(r){return arguments.length?e(e0(t=+r),e1(t)):t},eK(r)}function e5(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function e4(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function e3(e){return e<0?-e*e:e*e}function e9(e){var t=e(eM,eM),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(eM,eM):.5===r?e(e4,e3):e(e5(r),e5(1/r)):r},eK(t)}function e8(){var e=e9(eC());return e.copy=function(){return eD(e,e8()).exponent(e.exponent())},h.apply(e,arguments),e}function e6(){return e8.apply(null,arguments).exponent(.5)}function e7(e){return Math.sign(e)*e*e}function te(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function tt(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}a=(i=function(e){var t,r,i,a=void 0===e.grouping||void 0===e.thousands?eB:(t=eW.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],s=0;i>0&&l>0&&(s+l+1>n&&(l=Math.max(1,n-s)),a.push(e.substring(i-=l,i+l)),!((s+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),o=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",s=void 0===e.decimal?".":e.decimal+"",u=void 0===e.numerals?eB:(i=eW.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return i[+e]})}),c=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",d=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=ez(e)).fill,r=e.align,i=e.sign,p=e.symbol,h=e.zero,y=e.width,g=e.comma,v=e.precision,m=e.trim,b=e.type;"n"===b?(g=!0,b="g"):e$[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var x="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===p?l:/[%p]/.test(b)?c:"",O=e$[b],P=/[defgprs%]/.test(b);function j(e){var o,l,c,p=x,j=w;if("c"===b)j=O(e)+j,e="";else{var S=(e*=1)<0||1/e<0;if(e=isNaN(e)?d:O(Math.abs(e),v),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),S&&0==+e&&"+"!==i&&(S=!1),p=(S?"("===i?i:f:"-"===i||"("===i?"":i)+p,j=("s"===b?eU[8+n/3]:"")+j+(S&&"("===i?")":""),P){for(o=-1,l=e.length;++o<l;)if(48>(c=e.charCodeAt(o))||c>57){j=(46===c?s+e.slice(o+1):e.slice(o))+j,e=e.slice(0,o);break}}}g&&!h&&(e=a(e,1/0));var E=p.length+e.length+j.length,M=E<y?Array(y-E+1).join(t):"";switch(g&&h&&(e=a(M+e,M.length?y-j.length:1/0),M=""),r){case"<":e=p+e+j+M;break;case"=":e=p+M+e+j;break;case"^":e=M.slice(0,E=M.length>>1)+p+e+j+M.slice(E);break;default:e=M+p+e+j}return u(e)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return e+""},j}return{format:p,formatPrefix:function(e,t){var r=p(((e=ez(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(eL(t)/3))),i=Math.pow(10,-n),a=eU[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,o=i.formatPrefix;function tr(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function tn(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let ti=new Date,ta=new Date;function to(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>to(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(ti.setTime(+t),ta.setTime(+n),e(ti),e(ta),Math.floor(r(ti,ta))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let tl=to(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);tl.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?to(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):tl:null,tl.range;let ts=to(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());ts.range;let tu=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());tu.range;let tc=to(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());tc.range;let tf=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());tf.range;let td=to(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());td.range;let tp=to(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);tp.range;let th=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);th.range;let ty=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function tg(e){return to(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}ty.range;let tv=tg(0),tm=tg(1),tb=tg(2),tx=tg(3),tw=tg(4),tO=tg(5),tP=tg(6);function tj(e){return to(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}tv.range,tm.range,tb.range,tx.range,tw.range,tO.range,tP.range;let tS=tj(0),tE=tj(1),tM=tj(2),tA=tj(3),tk=tj(4),t_=tj(5),tD=tj(6);tS.range,tE.range,tM.range,tA.range,tk.range,t_.range,tD.range;let tC=to(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tC.range;let tT=to(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());tT.range;let tN=to(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());tN.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,tN.range;let tz=to(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tI(e,t,r,n,i,a){let o=[[ts,1,1e3],[ts,5,5e3],[ts,15,15e3],[ts,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=C(([,,e])=>e).right(o,i);if(a===o.length)return e.every(k(t/31536e6,r/31536e6,n));if(0===a)return tl.every(Math.max(k(t,r,n),1));let[l,s]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(s)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}tz.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,tz.range;let[tR,tL]=tI(tz,tT,tS,ty,td,tc),[tH,t$]=tI(tN,tC,tv,tp,tf,tu);function tB(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tW(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tU(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tF={"-":"",_:" ",0:"0"},tK=/^\s*\d+/,tG=/^%/,tq=/[\\^$*+?|[\]().{}]/g;function tV(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function tY(e){return e.replace(tq,"\\$&")}function tZ(e){return RegExp("^(?:"+e.map(tY).join("|")+")","i")}function tX(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tJ(e,t,r){var n=tK.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tQ(e,t,r){var n=tK.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function t0(e,t,r){var n=tK.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function t1(e,t,r){var n=tK.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function t2(e,t,r){var n=tK.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function t5(e,t,r){var n=tK.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function t4(e,t,r){var n=tK.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function t3(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function t9(e,t,r){var n=tK.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function t8(e,t,r){var n=tK.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function t6(e,t,r){var n=tK.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function t7(e,t,r){var n=tK.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function re(e,t,r){var n=tK.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function rt(e,t,r){var n=tK.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function rr(e,t,r){var n=tK.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function rn(e,t,r){var n=tK.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function ri(e,t,r){var n=tK.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ra(e,t,r){var n=tG.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function ro(e,t,r){var n=tK.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function rl(e,t,r){var n=tK.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function rs(e,t){return tV(e.getDate(),t,2)}function ru(e,t){return tV(e.getHours(),t,2)}function rc(e,t){return tV(e.getHours()%12||12,t,2)}function rf(e,t){return tV(1+tp.count(tN(e),e),t,3)}function rd(e,t){return tV(e.getMilliseconds(),t,3)}function rp(e,t){return rd(e,t)+"000"}function rh(e,t){return tV(e.getMonth()+1,t,2)}function ry(e,t){return tV(e.getMinutes(),t,2)}function rg(e,t){return tV(e.getSeconds(),t,2)}function rv(e){var t=e.getDay();return 0===t?7:t}function rm(e,t){return tV(tv.count(tN(e)-1,e),t,2)}function rb(e){var t=e.getDay();return t>=4||0===t?tw(e):tw.ceil(e)}function rx(e,t){return e=rb(e),tV(tw.count(tN(e),e)+(4===tN(e).getDay()),t,2)}function rw(e){return e.getDay()}function rO(e,t){return tV(tm.count(tN(e)-1,e),t,2)}function rP(e,t){return tV(e.getFullYear()%100,t,2)}function rj(e,t){return tV((e=rb(e)).getFullYear()%100,t,2)}function rS(e,t){return tV(e.getFullYear()%1e4,t,4)}function rE(e,t){var r=e.getDay();return tV((e=r>=4||0===r?tw(e):tw.ceil(e)).getFullYear()%1e4,t,4)}function rM(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tV(t/60|0,"0",2)+tV(t%60,"0",2)}function rA(e,t){return tV(e.getUTCDate(),t,2)}function rk(e,t){return tV(e.getUTCHours(),t,2)}function r_(e,t){return tV(e.getUTCHours()%12||12,t,2)}function rD(e,t){return tV(1+th.count(tz(e),e),t,3)}function rC(e,t){return tV(e.getUTCMilliseconds(),t,3)}function rT(e,t){return rC(e,t)+"000"}function rN(e,t){return tV(e.getUTCMonth()+1,t,2)}function rz(e,t){return tV(e.getUTCMinutes(),t,2)}function rI(e,t){return tV(e.getUTCSeconds(),t,2)}function rR(e){var t=e.getUTCDay();return 0===t?7:t}function rL(e,t){return tV(tS.count(tz(e)-1,e),t,2)}function rH(e){var t=e.getUTCDay();return t>=4||0===t?tk(e):tk.ceil(e)}function r$(e,t){return e=rH(e),tV(tk.count(tz(e),e)+(4===tz(e).getUTCDay()),t,2)}function rB(e){return e.getUTCDay()}function rW(e,t){return tV(tE.count(tz(e)-1,e),t,2)}function rU(e,t){return tV(e.getUTCFullYear()%100,t,2)}function rF(e,t){return tV((e=rH(e)).getUTCFullYear()%100,t,2)}function rK(e,t){return tV(e.getUTCFullYear()%1e4,t,4)}function rG(e,t){var r=e.getUTCDay();return tV((e=r>=4||0===r?tk(e):tk.ceil(e)).getUTCFullYear()%1e4,t,4)}function rq(){return"+0000"}function rV(){return"%"}function rY(e){return+e}function rZ(e){return Math.floor(e/1e3)}function rX(e){return new Date(e)}function rJ(e){return e instanceof Date?+e:+new Date(+e)}function rQ(e,t,r,n,i,a,o,l,s,u){var c=eT(),f=c.invert,d=c.domain,p=u(".%L"),h=u(":%S"),y=u("%I:%M"),g=u("%I %p"),v=u("%a %d"),m=u("%b %d"),b=u("%B"),x=u("%Y");function w(e){return(s(e)<e?p:l(e)<e?h:o(e)<e?y:a(e)<e?g:n(e)<e?i(e)<e?v:m:r(e)<e?b:x)(e)}return c.invert=function(e){return new Date(f(e))},c.domain=function(e){return arguments.length?d(Array.from(e,rJ)):d().map(rX)},c.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},c.tickFormat=function(e,t){return null==t?w:u(t)},c.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(eG(r,e)):c},c.copy=function(){return eD(c,rQ(e,t,r,n,i,a,o,l,s,u))},c}function r0(){return h.apply(rQ(tH,t$,tN,tC,tv,tp,tf,tu,ts,s).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function r1(){return h.apply(rQ(tR,tL,tz,tT,tS,th,td,tc,ts,u).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r2(){var e,t,r,n,i,a=0,o=1,l=eM,s=!1;function u(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,s?Math.max(0,Math.min(1,t)):t))}function c(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),u):[l(0),l(1)]}}return u.domain=function(i){return arguments.length?([a,o]=i,e=n(a*=1),t=n(o*=1),r=e===t?0:1/(t-e),u):[a,o]},u.clamp=function(e){return arguments.length?(s=!!e,u):s},u.interpolator=function(e){return arguments.length?(l=e,u):l},u.range=c(eP),u.rangeRound=c(ej),u.unknown=function(e){return arguments.length?(i=e,u):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),u}}function r5(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function r4(){var e=e9(r2());return e.copy=function(){return r5(e,r4()).exponent(e.exponent())},y.apply(e,arguments)}function r3(){return r4.apply(null,arguments).exponent(.5)}function r9(){var e,t,r,n,i,a,o,l=0,s=.5,u=1,c=1,f=eM,d=!1;function p(e){return isNaN(e*=1)?o:(e=.5+((e=+a(e))-t)*(c*e<c*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=eP);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),p):[f(0),f(.5),f(1)]}}return p.domain=function(o){return arguments.length?([l,s,u]=o,e=a(l*=1),t=a(s*=1),r=a(u*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),c=t<e?-1:1,p):[l,s,u]},p.clamp=function(e){return arguments.length?(d=!!e,p):d},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=h(eP),p.rangeRound=h(ej),p.unknown=function(e){return arguments.length?(o=e,p):o},function(o){return a=o,e=o(l),t=o(s),r=o(u),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),c=t<e?-1:1,p}}function r8(){var e=e9(r9());return e.copy=function(){return r5(e,r8()).exponent(e.exponent())},y.apply(e,arguments)}function r6(){return r8.apply(null,arguments).exponent(.5)}s=(l=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,s=e.shortMonths,u=tZ(i),c=tX(i),f=tZ(a),d=tX(a),p=tZ(o),h=tX(o),y=tZ(l),g=tX(l),v=tZ(s),m=tX(s),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return s[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:rs,e:rs,f:rp,g:rj,G:rE,H:ru,I:rc,j:rf,L:rd,m:rh,M:ry,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rY,s:rZ,S:rg,u:rv,U:rm,V:rx,w:rw,W:rO,x:null,X:null,y:rP,Y:rS,Z:rM,"%":rV},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return s[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:rA,e:rA,f:rT,g:rF,G:rG,H:rk,I:r_,j:rD,L:rC,m:rN,M:rz,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rY,s:rZ,S:rI,u:rR,U:rL,V:r$,w:rB,W:rW,x:null,X:null,y:rU,Y:rK,Z:rq,"%":rV},w={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=v.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return j(e,t,r,n)},d:t6,e:t6,f:ri,g:t4,G:t5,H:re,I:re,j:t7,L:rn,m:t8,M:rt,p:function(e,t,r){var n=u.exec(t.slice(r));return n?(e.p=c.get(n[0].toLowerCase()),r+n[0].length):-1},q:t9,Q:ro,s:rl,S:rr,u:tQ,U:t0,V:t1,w:tJ,W:t2,x:function(e,t,n){return j(e,r,t,n)},X:function(e,t,r){return j(e,n,t,r)},y:t4,Y:t5,Z:t3,"%":ra};function O(e,t){return function(r){var n,i,a,o=[],l=-1,s=0,u=e.length;for(r instanceof Date||(r=new Date(+r));++l<u;)37===e.charCodeAt(l)&&(o.push(e.slice(s,l)),null!=(i=tF[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),s=l+1);return o.push(e.slice(s,l)),o.join("")}}function P(e,t){return function(r){var n,i,a=tU(1900,void 0,1);if(j(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=tW(tU(a.y,0,1))).getUTCDay())>4||0===i?tE.ceil(n):tE(n),n=th.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=tB(tU(a.y,0,1))).getDay())>4||0===i?tm.ceil(n):tm(n),n=tp.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?tW(tU(a.y,0,1)).getUTCDay():tB(tU(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,tW(a)):tB(a)}}function j(e,t,r,n){for(var i,a,o=0,l=t.length,s=r.length;o<l;){if(n>=s)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=w[(i=t.charAt(o++))in tF?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=P(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=P(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l.parse,u=l.utcFormat,l.utcParse;var r7=r(97238),ne=r(39827),nt=r(60356),nr=r(16377),nn=r(78892);function ni(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,nn.H)(t)&&(0,nn.H)(r))return!0}return!1}function na(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var no=r(8870),nl=r.n(no),ns=e=>e,nu={},nc=e=>e===nu,nf=e=>function t(){return 0==arguments.length||1==arguments.length&&nc(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},nd=(e,t)=>1===e?t:nf(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==nu).length;return a>=e?t(...n):nd(e-a,nf(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>nc(e)?r.shift():e),...r)}))}),np=e=>nd(e.length,e),nh=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},ny=np((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),ng=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return ns;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},nv=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),nm=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function nb(e){var t;return 0===e?1:Math.floor(new(nl())(e).abs().log(10).toNumber())+1}function nx(e,t,r){for(var n=new(nl())(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}np((e,t,r)=>{var n=+e;return n+r*(t-n)}),np((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),np((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var nw=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},nO=(e,t,r)=>{if(e.lte(0))return new(nl())(0);var n=nb(e.toNumber()),i=new(nl())(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new(nl())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new(nl())(t?l.toNumber():Math.ceil(l.toNumber()))},nP=(e,t,r)=>{var n=new(nl())(1),i=new(nl())(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new(nl())(10).pow(nb(e)-1),i=new(nl())(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new(nl())(Math.floor(e)))}else 0===e?i=new(nl())(Math.floor((t-1)/2)):r||(i=new(nl())(Math.floor(e)));var o=Math.floor((t-1)/2);return ng(ny(e=>i.add(new(nl())(e-o).mul(n)).toNumber()),nh)(0,t)},nj=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(nl())(0),tickMin:new(nl())(0),tickMax:new(nl())(0)};var o=nO(new(nl())(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new(nl())(0):(i=new(nl())(e).add(t).div(2)).sub(new(nl())(i).mod(o))).sub(e).div(o).toNumber()),s=Math.ceil(new(nl())(t).sub(i).div(o).toNumber()),u=l+s+1;return u>r?nj(e,t,r,n,a+1):(u<r&&(s=t>0?s+(r-u):s,l=t>0?l:l+(r-u)),{step:o,tickMin:i.sub(new(nl())(l).mul(o)),tickMax:i.add(new(nl())(s).mul(o))})},nS=nm(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=nw([t,r]);if(o===-1/0||l===1/0){var s=l===1/0?[o,...nh(0,n-1).map(()=>1/0)]:[...nh(0,n-1).map(()=>-1/0),l];return t>r?nv(s):s}if(o===l)return nP(o,n,i);var{step:u,tickMin:c,tickMax:f}=nj(o,l,a,i,0),d=nx(c,f.add(new(nl())(.1).mul(u)),u);return t>r?nv(d):d}),nE=nm(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=nw([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),s=nO(new(nl())(o).sub(a).div(l-1),i,0),u=[...nx(new(nl())(a),new(nl())(o).sub(new(nl())(.99).mul(s)),s),o];return r>n?nv(u):u}),nM=r(2589),nA=r(96908),nk=r(69449),n_=r(20972),nD=r(18478),nC=r(47062),nT=r(66038),nN=r(12287),nz=r(18190),nI=r(84421);function nR(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nL(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nR(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nR(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nH=[0,"auto"],n$={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},nB=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?n$:r},nW={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:nH,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nI.tQ},nU=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?nW:r},nF={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},nK=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?nF:r},nG=(e,t,r)=>{switch(t){case"xAxis":return nB(e,r);case"yAxis":return nU(e,r);case"zAxis":return nK(e,r);case"angleAxis":return(0,nC.Be)(e,r);case"radiusAxis":return(0,nC.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nq=(e,t,r)=>{switch(t){case"xAxis":return nB(e,r);case"yAxis":return nU(e,r);case"angleAxis":return(0,nC.Be)(e,r);case"radiusAxis":return(0,nC.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nV=e=>e.graphicalItems.countOfBars>0;function nY(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var nZ=e=>e.graphicalItems.cartesianItems,nX=(0,f.Mz)([nT.N,nN.E],nY),nJ=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),nQ=(0,f.Mz)([nZ,nG,nX],nJ),n0=e=>e.filter(e=>void 0===e.stackId),n1=(0,f.Mz)([nQ],n0),n2=e=>e.map(e=>e.data).filter(Boolean).flat(1),n5=(0,f.Mz)([nQ],n2),n4=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},n3=(0,f.Mz)([n5,nt.HS],n4),n9=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,ne.kr)(e,t)}))):e.map(e=>({value:e})),n8=(0,f.Mz)([n3,nG,nQ],n9);function n6(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function n7(e){return e.filter(e=>(0,nr.vh)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,nr.M8)(e))}var ie=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t,a=i.map(e=>e.dataKey);return[n,{stackedData:(0,ne.yy)(e,a,r),graphicalItems:i}]})),it=(0,f.Mz)([n3,nQ,nD.eC],ie),ir=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=(0,ne.Mk)(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},ii=(0,f.Mz)([it,nt.LF,nT.N],ir),ia=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null==(i=r.errorBars)?void 0:i.filter(e=>n6(n,e)),l=(0,ne.kr)(e,null!=(a=t.dataKey)?a:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||(0,nr.M8)(t)||!r.length?[]:n7(r.flatMap(r=>{var n,i,a=(0,ne.kr)(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,(0,nn.H)(n)&&(0,nn.H)(i))return[t-n,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),io=(0,f.Mz)(n3,nG,n1,nT.N,ia);function il(e){var{value:t}=e;if((0,nr.vh)(t)||t instanceof Date)return t}var is=e=>{var t=n7(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},iu=(e,t,r)=>{var n=e.map(il).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,nr.CG)(n))?p()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},ic=e=>{var t;if(null==e||!("domain"in e))return nH;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=n7(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:nH},id=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},ip=e=>e.referenceElements.dots,ih=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),iy=(0,f.Mz)([ip,nT.N,nN.E],ih),ig=e=>e.referenceElements.areas,iv=(0,f.Mz)([ig,nT.N,nN.E],ih),im=e=>e.referenceElements.lines,ib=(0,f.Mz)([im,nT.N,nN.E],ih),ix=(e,t)=>{var r=n7(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iw=(0,f.Mz)(iy,nT.N,ix),iO=(e,t)=>{var r=n7(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iP=(0,f.Mz)([iv,nT.N],iO),ij=(e,t)=>{var r=n7(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iS=(0,f.Mz)(ib,nT.N,ij),iE=(0,f.Mz)(iw,iS,iP,(e,t,r)=>id(e,r,t)),iM=(0,f.Mz)([nG],ic),iA=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if((0,nn.H)(i))r=i;else if("function"==typeof i)return;if((0,nn.H)(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(ni(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(ni(n))return na(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if((0,nr.Et)(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&ne.IH.test(o)){var s=ne.IH.exec(o);if(null==s||null==t)i=void 0;else{var u=+s[1];i=t[0]-u}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if((0,nr.Et)(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&ne.qx.test(l)){var c=ne.qx.exec(l);if(null==c||null==t)a=void 0;else{var f=+c[1];a=t[1]+f}}else a=null==t?void 0:t[1];var d=[i,a];if(ni(d))return null==t?d:na(d,t,r)}}}(t,id(r,i,is(n)),e.allowDataOverflow)},ik=(0,f.Mz)([nG,iM,ii,io,iE],iA),i_=[0,1],iD=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:s}=e,u=(0,ne._L)(t,a);return u&&null==l?p()(0,r.length):"category"===s?iu(n,e,u):"expand"===i?i_:o}},iC=(0,f.Mz)([nG,r7.fz,n3,n8,nD.eC,nT.N,ik],iD),iT=(e,t,r,n,i)=>{if(null!=e){var{scale:a,type:o}=e;if("auto"===a)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof a){var l="scale".concat((0,nr.Zb)(a));return l in c?l:"point"}}},iN=(0,f.Mz)([nG,r7.fz,nV,nD.iO,nT.N],iT);function iz(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in c)return c[e]();var t="scale".concat((0,nr.Zb)(e));if(t in c)return c[t]()}}(t);if(null!=i){var a=i.domain(r).range(n);return(0,ne.YB)(a),a}}}var iI=(e,t,r)=>{var n=ic(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&ni(e))return nS(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&null!=e)return nE(e,t.tickCount,t.allowDecimals)}},iR=(0,f.Mz)([iC,nq,iN],iI),iL=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&ni(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,iH=(0,f.Mz)([nG,iC,iR,nT.N],iL),i$=(0,f.Mz)(n8,nG,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(n7(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),iB=(0,f.Mz)(i$,r7.fz,nD.gY,nk.GO,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!(0,nn.H)(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=(0,nr.F4)(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),iW=(0,f.Mz)(nB,(e,t)=>{var r=nB(e,t);return null==r||"string"!=typeof r.padding?0:iB(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),iU=(0,f.Mz)(nU,(e,t)=>{var r=nU(e,t);return null==r||"string"!=typeof r.padding?0:iB(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),iF=(0,f.Mz)([nk.GO,iW,n_.U,n_.C,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),iK=(0,f.Mz)([nk.GO,r7.fz,iU,n_.U,n_.C,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),iG=(e,t,r,n)=>{var i;switch(t){case"xAxis":return iF(e,r,n);case"yAxis":return iK(e,r,n);case"zAxis":return null==(i=nK(e,r))?void 0:i.range;case"angleAxis":return(0,nC.Cv)(e);case"radiusAxis":return(0,nC.Dc)(e,r);default:return}},iq=(0,f.Mz)([nG,iG],nz.I),iV=(0,f.Mz)([nG,iN,iH,iq],iz);function iY(e,t){return e.id<t.id?-1:+(e.id>t.id)}(0,f.Mz)(nQ,nT.N,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>n6(t,e)));var iZ=(e,t)=>t,iX=(e,t,r)=>r,iJ=(0,f.Mz)(nA.h,iZ,iX,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iY)),iQ=(0,f.Mz)(nA.W,iZ,iX,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iY)),i0=(e,t)=>({width:e.width,height:t.height}),i1=(e,t)=>({width:"number"==typeof t.width?t.width:nI.tQ,height:e.height}),i2=(0,f.Mz)(nk.GO,nB,i0),i5=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},i4=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},i3=(0,f.Mz)(nM.A$,nk.GO,iJ,iZ,iX,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=i0(t,r);null==a&&(a=i5(t,n,e));var s="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(s)*l.height,a+=(s?-1:1)*l.height}),o}),i9=(0,f.Mz)(nM.Lp,nk.GO,iQ,iZ,iX,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=i1(t,r);null==a&&(a=i4(t,n,e));var s="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(s)*l.width,a+=(s?-1:1)*l.width}),o}),i8=(e,t)=>{var r=(0,nk.GO)(e),n=nB(e,t);if(null!=n){var i=i3(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},i6=(e,t)=>{var r=(0,nk.GO)(e),n=nU(e,t);if(null!=n){var i=i9(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},i7=(0,f.Mz)(nk.GO,nU,(e,t)=>({width:"number"==typeof t.width?t.width:nI.tQ,height:e.height})),ae=(e,t,r)=>{switch(t){case"xAxis":return i2(e,r).width;case"yAxis":return i7(e,r).height;default:return}},at=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=(0,ne._L)(e,n),s=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&(0,nr.CG)(s))return s}},ar=(0,f.Mz)([r7.fz,n8,nG,nT.N],at),an=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if((0,ne._L)(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},ai=(0,f.Mz)([r7.fz,n8,nq,nT.N],an),aa=(0,f.Mz)([r7.fz,(e,t,r)=>{switch(t){case"xAxis":return nB(e,r);case"yAxis":return nU(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},iN,iV,ar,ai,iG,iR,nT.N],(e,t,r,n,i,a,o,l,s)=>{if(null==t)return null;var u=(0,ne._L)(e,s);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:s,categoricalDomain:a,duplicateDomain:i,isCategorical:u,niceTicks:l,range:o,realScaleType:r,scale:n}}),ao=(0,f.Mz)([r7.fz,nq,iN,iV,iR,iG,ar,ai,nT.N],(e,t,r,n,i,a,o,l,s)=>{if(null!=t&&null!=n){var u=(0,ne._L)(e,s),{type:c,ticks:f,tickCount:d}=t,p="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,h="category"===c&&n.bandwidth?n.bandwidth()/p:0;h="angleAxis"===s&&null!=a&&a.length>=2?2*(0,nr.sA)(a[0]-a[1])*h:h;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+h,value:e,offset:h})).filter(e=>!(0,nr.M8)(e.coordinate)):u&&l?l.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+h,value:e,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:o?o[e]:e,index:t,offset:h}))}}),al=(0,f.Mz)([r7.fz,nq,iV,iG,ar,ai,nT.N],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,ne._L)(e,o),{tickCount:s}=t,u=0;return(u="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*(0,nr.sA)(n[0]-n[1])*u:u,l&&a)?a.map((e,t)=>({coordinate:r(e)+u,value:e,index:t,offset:u})):r.ticks?r.ticks(s).map(e=>({coordinate:r(e)+u,value:e,offset:u})):r.domain().map((e,t)=>({coordinate:r(e)+u,value:i?i[e]:e,index:t,offset:u}))}}),as=(0,f.Mz)(nG,iV,(e,t)=>{if(null!=e&&null!=t)return nL(nL({},e),{},{scale:t})}),au=(0,f.Mz)([nG,iN,iC,iq],iz);(0,f.Mz)((e,t,r)=>nK(e,r),au,(e,t)=>{if(null!=e&&null!=t)return nL(nL({},e),{},{scale:t})});var ac=(0,f.Mz)([r7.fz,nA.h,nA.W],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},14545:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},14804:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),i=r(12429);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},14986:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},15064:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(0,r(12115).createContext)(null)},15073:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=d(r(12115)),i=r(47650),a=d(r(38637)),o=r(5939),l=r(45157),s=r(50507),u=r(21383),c=r(61299),f=d(r(20749));function d(e){return e&&e.__esModule?e:{default:e}}function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:String(n))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class h extends n.default.Component{constructor(){super(...arguments),p(this,"state",{resizing:null,dragging:null,className:""}),p(this,"elementRef",n.default.createRef()),p(this,"onDragStart",(e,t)=>{let{node:r}=t,{onDragStart:n,transformScale:i}=this.props;if(!n)return;let a={top:0,left:0},{offsetParent:o}=r;if(!o)return;let l=o.getBoundingClientRect(),s=r.getBoundingClientRect(),c=s.left/i,f=l.left/i,d=s.top/i,p=l.top/i;a.left=c-f+o.scrollLeft,a.top=d-p+o.scrollTop,this.setState({dragging:a});let{x:h,y}=(0,u.calcXY)(this.getPositionParams(),a.top,a.left,this.props.w,this.props.h);return n.call(this,this.props.i,h,y,{e,node:r,newPosition:a})}),p(this,"onDrag",(e,t,r)=>{let{node:n,deltaX:a,deltaY:o}=t,{onDrag:l}=this.props;if(!l)return;if(!this.state.dragging)throw Error("onDrag called before onDragStart.");let s=this.state.dragging.top+o,c=this.state.dragging.left+a,{isBounded:f,i:d,w:p,h,containerWidth:y}=this.props,g=this.getPositionParams();if(f){let{offsetParent:e}=n;if(e){let{margin:t,rowHeight:r,containerPadding:n}=this.props,i=e.clientHeight-(0,u.calcGridItemWHPx)(h,r,t[1]);s=(0,u.clamp)(s-n[1],0,i);let a=(0,u.calcGridColWidth)(g),o=y-(0,u.calcGridItemWHPx)(p,a,t[0]);c=(0,u.clamp)(c-n[0],0,o)}}let v={top:s,left:c};r?this.setState({dragging:v}):(0,i.flushSync)(()=>{this.setState({dragging:v})});let{x:m,y:b}=(0,u.calcXY)(g,s,c,p,h);return l.call(this,d,m,b,{e,node:n,newPosition:v})}),p(this,"onDragStop",(e,t)=>{let{node:r}=t,{onDragStop:n}=this.props;if(!n)return;if(!this.state.dragging)throw Error("onDragEnd called before onDragStart.");let{w:i,h:a,i:o}=this.props,{left:l,top:s}=this.state.dragging;this.setState({dragging:null});let{x:c,y:f}=(0,u.calcXY)(this.getPositionParams(),s,l,i,a);return n.call(this,o,c,f,{e,node:r,newPosition:{top:s,left:l}})}),p(this,"onResizeStop",(e,t,r)=>this.onResizeHandler(e,t,r,"onResizeStop")),p(this,"onResizeStart",(e,t,r)=>this.onResizeHandler(e,t,r,"onResizeStart")),p(this,"onResize",(e,t,r)=>this.onResizeHandler(e,t,r,"onResize"))}shouldComponentUpdate(e,t){if(this.props.children!==e.children||this.props.droppingPosition!==e.droppingPosition)return!0;let r=(0,u.calcGridItemPosition)(this.getPositionParams(this.props),this.props.x,this.props.y,this.props.w,this.props.h,this.state),n=(0,u.calcGridItemPosition)(this.getPositionParams(e),e.x,e.y,e.w,e.h,t);return!(0,s.fastPositionEqual)(r,n)||this.props.useCSSTransforms!==e.useCSSTransforms}componentDidMount(){this.moveDroppingItem({})}componentDidUpdate(e){this.moveDroppingItem(e)}moveDroppingItem(e){let{droppingPosition:t}=this.props;if(!t)return;let r=this.elementRef.current;if(!r)return;let n=e.droppingPosition||{left:0,top:0},{dragging:i}=this.state,a=i&&t.left!==n.left||t.top!==n.top;if(i){if(a){let e=t.left-i.left,n=t.top-i.top;this.onDrag(t.e,{node:r,deltaX:e,deltaY:n},!0)}}else this.onDragStart(t.e,{node:r,deltaX:t.left,deltaY:t.top})}getPositionParams(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props;return{cols:e.cols,containerPadding:e.containerPadding,containerWidth:e.containerWidth,margin:e.margin,maxRows:e.maxRows,rowHeight:e.rowHeight}}createStyle(e){let t,{usePercentages:r,containerWidth:n,useCSSTransforms:i}=this.props;return i?t=(0,s.setTransform)(e):(t=(0,s.setTopLeft)(e),r&&(t.left=(0,s.perc)(e.left/n),t.width=(0,s.perc)(e.width/n))),t}mixinDraggable(e,t){return n.default.createElement(o.DraggableCore,{disabled:!t,onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop,handle:this.props.handle,cancel:".react-resizable-handle"+(this.props.cancel?","+this.props.cancel:""),scale:this.props.transformScale,nodeRef:this.elementRef},e)}curryResizeHandler(e,t){return(r,n)=>t(r,n,e)}mixinResizable(e,t,r){let{cols:i,minW:a,minH:o,maxW:s,maxH:c,transformScale:f,resizeHandles:d,resizeHandle:p}=this.props,h=this.getPositionParams(),y=(0,u.calcGridItemPosition)(h,0,0,i,0).width,g=(0,u.calcGridItemPosition)(h,0,0,a,o),v=(0,u.calcGridItemPosition)(h,0,0,s,c),m=[g.width,g.height],b=[Math.min(v.width,y),Math.min(v.height,1/0)];return n.default.createElement(l.Resizable,{draggableOpts:{disabled:!r},className:r?void 0:"react-resizable-hide",width:t.width,height:t.height,minConstraints:m,maxConstraints:b,onResizeStop:this.curryResizeHandler(t,this.onResizeStop),onResizeStart:this.curryResizeHandler(t,this.onResizeStart),onResize:this.curryResizeHandler(t,this.onResize),transformScale:f,resizeHandles:d,handle:p},e)}onResizeHandler(e,t,r,n){let{node:a,size:o,handle:l}=t,c=this.props[n];if(!c)return;let{x:f,y:d,i:p,maxH:h,minH:y,containerWidth:g}=this.props,{minW:v,maxW:m}=this.props,b=o;a&&(b=(0,s.resizeItemInDirection)(l,r,o,g),(0,i.flushSync)(()=>{this.setState({resizing:"onResizeStop"===n?null:b})}));let{w:x,h:w}=(0,u.calcWH)(this.getPositionParams(),b.width,b.height,f,d,l);x=(0,u.clamp)(x,Math.max(v,1),m),w=(0,u.clamp)(w,y,h),c.call(this,p,x,w,{e,node:a,size:b,handle:l})}render(){let{x:e,y:t,w:r,h:i,isDraggable:a,isResizable:o,droppingPosition:l,useCSSTransforms:s}=this.props,c=(0,u.calcGridItemPosition)(this.getPositionParams(),e,t,r,i,this.state),d=n.default.Children.only(this.props.children),p=n.default.cloneElement(d,{ref:this.elementRef,className:(0,f.default)("react-grid-item",d.props.className,this.props.className,{static:this.props.static,resizing:!!this.state.resizing,"react-draggable":a,"react-draggable-dragging":!!this.state.dragging,dropping:!!l,cssTransforms:s}),style:{...this.props.style,...d.props.style,...this.createStyle(c)}});return p=this.mixinResizable(p,c,o),p=this.mixinDraggable(p,a)}}t.default=h,p(h,"propTypes",{children:a.default.element,cols:a.default.number.isRequired,containerWidth:a.default.number.isRequired,rowHeight:a.default.number.isRequired,margin:a.default.array.isRequired,maxRows:a.default.number.isRequired,containerPadding:a.default.array.isRequired,x:a.default.number.isRequired,y:a.default.number.isRequired,w:a.default.number.isRequired,h:a.default.number.isRequired,minW:function(e,t){let r=e[t];return"number"!=typeof r?Error("minWidth not Number"):r>e.w||r>e.maxW?Error("minWidth larger than item width/maxWidth"):void 0},maxW:function(e,t){let r=e[t];return"number"!=typeof r?Error("maxWidth not Number"):r<e.w||r<e.minW?Error("maxWidth smaller than item width/minWidth"):void 0},minH:function(e,t){let r=e[t];return"number"!=typeof r?Error("minHeight not Number"):r>e.h||r>e.maxH?Error("minHeight larger than item height/maxHeight"):void 0},maxH:function(e,t){let r=e[t];return"number"!=typeof r?Error("maxHeight not Number"):r<e.h||r<e.minH?Error("maxHeight smaller than item height/minHeight"):void 0},i:a.default.string.isRequired,resizeHandles:c.resizeHandleAxesType,resizeHandle:c.resizeHandleType,onDragStop:a.default.func,onDragStart:a.default.func,onDrag:a.default.func,onResizeStop:a.default.func,onResizeStart:a.default.func,onResize:a.default.func,isDraggable:a.default.bool.isRequired,isResizable:a.default.bool.isRequired,isBounded:a.default.bool.isRequired,static:a.default.bool,useCSSTransforms:a.default.bool.isRequired,transformScale:a.default.number,className:a.default.string,handle:a.default.string,cancel:a.default.string,droppingPosition:a.default.shape({e:a.default.object.isRequired,left:a.default.number.isRequired,top:a.default.number.isRequired})}),p(h,"defaultProps",{className:"",cancel:"",handle:"",minH:1,minW:1,maxH:1/0,maxW:1/0,transformScale:1})},15160:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},16377:(e,t,r)=>{"use strict";r.d(t,{CG:()=>p,Dj:()=>h,Et:()=>s,F4:()=>d,GW:()=>y,M8:()=>o,NF:()=>f,Zb:()=>m,_3:()=>l,eP:()=>g,sA:()=>a,uy:()=>v,vh:()=>u});var n=r(95672),i=r.n(n),a=e=>0===e?0:e>0?1:-1,o=e=>"number"==typeof e&&e!=+e,l=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,s=e=>("number"==typeof e||e instanceof Number)&&!o(e),u=e=>s(e)||"string"==typeof e,c=0,f=e=>{var t=++c;return"".concat(e||"").concat(t)},d=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!s(e)&&"string"!=typeof e)return n;if(l(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return o(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},p=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},h=(e,t)=>s(e)&&s(t)?r=>e+r*(t-e):()=>t;function y(e,t,r){return s(e)&&s(t)?e+r*(t-e):t}function g(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):i()(e,t))===r)}var v=e=>null==e,m=e=>v(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},18190:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var n=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t}},18357:(e,t,r)=>{"use strict";r.d(t,{F:()=>eP,L:()=>ev});var n=r(12115),i=r(95672),a=r.n(i),o=r(52596),l=r(68924),s=r(60356),u=r(69449),c=r(39827),f=r(14299),d=r(97238),p=r(66038),h=r(12287),y=r(18478),g=e=>e.graphicalItems.polarItems,v=(0,l.Mz)([p.N,h.E],f.eo),m=(0,l.Mz)([g,f.DP,v],f.ec),b=(0,l.Mz)([m],f.rj),x=(0,l.Mz)([b,s.z3],f.Nk),w=(0,l.Mz)([x,f.DP,m],f.fb),O=(0,l.Mz)([x,f.DP,m],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:(0,c.kr)(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,c.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),P=()=>void 0,j=(0,l.Mz)([f.DP,f.AV,P,O,P],f.wL),S=(0,l.Mz)([f.DP,d.fz,x,w,y.eC,p.N,j],f.tP),E=(0,l.Mz)([S,f.DP,f.xM],f.xp);function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,l.Mz)([f.DP,S,E,p.N],f.g1);var k=(e,t)=>t,_=[],D=(e,t,r)=>(null==r?void 0:r.length)===0?_:r,C=(0,l.Mz)([s.z3,k,D],(e,t,r)=>{var n,{chartData:i}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>A(A({},t.presentationProps),e.props))),null!=n)return n}),T=(0,l.Mz)([C,k,D],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=(0,c.kr)(e,t.nameKey,t.name);return a=null!=r&&null!=(i=r[n])&&null!=(i=i.props)&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,c.uM)(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),N=(0,l.Mz)([g,k],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),z=(0,l.Mz)([C,N,D,u.GO],(e,t,r,n)=>{if(null!=t&&null!=e)return ev({offset:n,pieSettings:t,displayedData:e,cells:r})}),I=r(81971),R=r(27119),L=r(2348),H=r(70688),$=r(79095),B=r(54811),W=r(70788),U=r(41643),F=r(25641),K=r(16377),G=r(43597),q=r(48605),V=r(99129),Y=r(56091),Z=r(20215),X=r(79020),J=r(84421),Q=r(39426),ee=r(93389),et=r(74460),er=["onMouseEnter","onClick","onMouseLeave"];function en(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ei(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?en(Object(r),!0).forEach(function(t){ea(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):en(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ea(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eo(){return(eo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function el(e){var t=(0,n.useMemo)(()=>(0,W.J9)(e,!1),[e]),r=(0,n.useMemo)(()=>(0,W.aS)(e.children,B.f),[e.children]),i=(0,n.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),a=(0,I.G)(e=>T(e,i,r));return n.createElement(X._,{legendPayload:a})}function es(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:s,tooltipType:u}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:(0,c.uM)(l,t),hide:s,type:u,color:o,unit:""}}}var eu=(e,t)=>e>t?"start":e<t?"end":"middle",ec=(e,t,r)=>"function"==typeof t?t(e):(0,K.F4)(t,r,.8*r),ef=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,l=(0,F.lY)(a,o),s=i+(0,K.F4)(e.cx,a,a/2),u=n+(0,K.F4)(e.cy,o,o/2),c=(0,K.F4)(e.innerRadius,l,0);return{cx:s,cy:u,innerRadius:c,outerRadius:ec(r,e.outerRadius,l),maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}},ed=(e,t)=>(0,K.sA)(t-e)*Math.min(Math.abs(t-e),360),ep=(e,t)=>{if(n.isValidElement(e))return n.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,o.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return n.createElement(H.I,eo({},t,{type:"linear",className:r}))},eh=(e,t,r)=>{if(n.isValidElement(e))return n.cloneElement(e,t);var i=r;if("function"==typeof e&&(i=e(t),n.isValidElement(i)))return i;var a=(0,o.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return n.createElement($.E,eo({},t,{alignmentBaseline:"middle",className:a}),i)};function ey(e){var{sectors:t,props:r,showLabels:i}=e,{label:a,labelLine:o,dataKey:l}=r;if(!i||!a||!t)return null;var s=(0,W.J9)(r,!1),u=(0,W.J9)(a,!1),f=(0,W.J9)(o,!1),d="object"==typeof a&&"offsetRadius"in a&&a.offsetRadius||20,p=t.map((e,t)=>{var r=(e.startAngle+e.endAngle)/2,i=(0,F.IZ)(e.cx,e.cy,e.outerRadius+d,r),p=ei(ei(ei(ei({},s),e),{},{stroke:"none"},u),{},{index:t,textAnchor:eu(i.x,e.cx)},i),h=ei(ei(ei(ei({},s),e),{},{fill:"none",stroke:e.fill},f),{},{index:t,points:[(0,F.IZ)(e.cx,e.cy,e.outerRadius,r),i],key:"line"});return n.createElement(L.W,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&ep(o,h),eh(a,p,(0,c.kr)(e,l)))});return n.createElement(L.W,{className:"recharts-pie-labels"},p)}function eg(e){var{sectors:t,activeShape:r,inactiveShape:i,allOtherPieProps:a,showLabels:o}=e,l=(0,I.G)(Z.A2),{onMouseEnter:s,onClick:u,onMouseLeave:c}=a,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(a,er),d=(0,V.Cj)(s,a.dataKey),p=(0,V.Pg)(c),h=(0,V.Ub)(u,a.dataKey);return null==t?null:n.createElement(n.Fragment,null,t.map((e,o)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var s=r&&String(o)===l,u=s?r:l?i:null,c=ei(ei({},e),{},{stroke:e.stroke,tabIndex:-1,[J.F0]:o,[J.um]:a.dataKey});return n.createElement(L.W,eo({tabIndex:-1,className:"recharts-pie-sector"},(0,G.XC)(f,e,o),{onMouseEnter:d(e,o),onMouseLeave:p(e,o),onClick:h(e,o),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(o)}),n.createElement(q.y,eo({option:u,isActive:s,shapeType:"sector"},c)))}),n.createElement(ey,{sectors:t,props:a,showLabels:o}))}function ev(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:s,startAngle:u,endAngle:f,dataKey:d,nameKey:p,tooltipType:h}=i,y=Math.abs(i.minAngle),g=ed(u,f),v=Math.abs(g),m=a.length<=1?0:null!=(t=i.paddingAngle)?t:0,b=a.filter(e=>0!==(0,c.kr)(e,d,0)).length,x=v-b*y-(v>=360?b:b-1)*m,w=a.reduce((e,t)=>{var r=(0,c.kr)(t,d,0);return e+((0,K.Et)(r)?r:0)},0);return w>0&&(r=a.map((e,t)=>{var r,a=(0,c.kr)(e,d,0),f=(0,c.kr)(e,p,t),v=ef(i,l,e),b=((0,K.Et)(a)?a:0)/w,O=ei(ei({},e),o&&o[t]&&o[t].props),P=(r=t?n.endAngle+(0,K.sA)(g)*m*(0!==a):u)+(0,K.sA)(g)*((0!==a?y:0)+b*x),j=(r+P)/2,S=(v.innerRadius+v.outerRadius)/2,E=[{name:f,value:a,payload:O,dataKey:d,type:h}],M=(0,F.IZ)(v.cx,v.cy,S,j);return n=ei(ei(ei(ei({},i.presentationProps),{},{percent:b,cornerRadius:s,name:f,tooltipPayload:E,midAngle:j,middleRadius:S,tooltipPosition:M},O),v),{},{value:(0,c.kr)(e,d),startAngle:r,endAngle:P,payload:O,paddingAngle:(0,K.sA)(g)*m})})),r}function em(e){var{props:t,previousSectorsRef:r}=e,{sectors:i,isAnimationActive:o,animationBegin:l,animationDuration:s,animationEasing:u,activeShape:c,inactiveShape:f,onAnimationStart:d,onAnimationEnd:p}=t,h=(0,Q.n)(t,"recharts-pie-"),y=r.current,[g,v]=(0,n.useState)(!0),m=(0,n.useCallback)(()=>{"function"==typeof p&&p(),v(!1)},[p]),b=(0,n.useCallback)(()=>{"function"==typeof d&&d(),v(!0)},[d]);return n.createElement(et.i,{begin:l,duration:s,isActive:o,easing:u,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:m,key:h},e=>{var{t:o}=e,l=[],s=(i&&i[0]).startAngle;return i.forEach((e,t)=>{var r=y&&y[t],n=t>0?a()(e,"paddingAngle",0):0;if(r){var i=(0,K.Dj)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),u=ei(ei({},e),{},{startAngle:s+n,endAngle:s+i(o)+n});l.push(u),s=u.endAngle}else{var{endAngle:c,startAngle:f}=e,d=(0,K.Dj)(0,c-f)(o),p=ei(ei({},e),{},{startAngle:s+n,endAngle:s+d+n});l.push(p),s=p.endAngle}}),r.current=l,n.createElement(L.W,null,n.createElement(eg,{sectors:l,activeShape:c,inactiveShape:f,allOtherPieProps:t,showLabels:!g}))})}function eb(e){var{sectors:t,isAnimationActive:r,activeShape:i,inactiveShape:a}=e,o=(0,n.useRef)(null),l=o.current;return r&&t&&t.length&&(!l||l!==t)?n.createElement(em,{props:e,previousSectorsRef:o}):n.createElement(eg,{sectors:t,activeShape:i,inactiveShape:a,allOtherPieProps:e,showLabels:!0})}function ex(e){var{hide:t,className:r,rootTabIndex:i}=e,a=(0,o.$)("recharts-pie",r);return t?null:n.createElement(L.W,{tabIndex:i,className:a},n.createElement(eb,e))}var ew={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!U.m.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function eO(e){var t=(0,ee.e)(e,ew),r=(0,n.useMemo)(()=>(0,W.aS)(e.children,B.f),[e.children]),i=(0,W.J9)(t,!1),a=(0,n.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:i}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,i]),o=(0,I.G)(e=>z(e,a,r));return n.createElement(n.Fragment,null,n.createElement(Y.r,{fn:es,args:ei(ei({},t),{},{sectors:o})}),n.createElement(ex,eo({},t,{sectors:o})))}class eP extends n.PureComponent{render(){return n.createElement(n.Fragment,null,n.createElement(R.v,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),n.createElement(el,this.props),n.createElement(eO,this.props),this.props.children)}constructor(){super(...arguments),ea(this,"id",(0,K.NF)("recharts-pie-"))}}ea(eP,"displayName","Pie"),ea(eP,"defaultProps",ew)},18478:(e,t,r)=>{"use strict";r.d(t,{JN:()=>n,_5:()=>i,eC:()=>l,gY:()=>a,hX:()=>c,iO:()=>s,lZ:()=>u,pH:()=>f,x3:()=>o});var n=e=>e.rootProps.maxBarSize,i=e=>e.rootProps.barGap,a=e=>e.rootProps.barCategoryGap,o=e=>e.rootProps.barSize,l=e=>e.rootProps.stackOffset,s=e=>e.options.chartName,u=e=>e.rootProps.syncId,c=e=>e.rootProps.syncMethod,f=e=>e.options.eventEmitter},18957:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.findOrGenerateResponsiveLayout=function(e,t,r,a,o,l){if(e[r])return(0,n.cloneLayout)(e[r]);let s=e[a],u=i(t),c=u.slice(u.indexOf(r));for(let t=0,r=c.length;t<r;t++){let r=c[t];if(e[r]){s=e[r];break}}return s=(0,n.cloneLayout)(s||[]),(0,n.compact)((0,n.correctBounds)(s,{cols:o}),l,o)},t.getBreakpointFromWidth=function(e,t){let r=i(e),n=r[0];for(let i=1,a=r.length;i<a;i++){let a=r[i];t>e[a]&&(n=a)}return n},t.getColsFromBreakpoint=function(e,t){if(!t[e])throw Error("ResponsiveReactGridLayout: `cols` entry for breakpoint "+e+" is missing!");return t[e]},t.sortBreakpoints=i;var n=r(50507);function i(e){return Object.keys(e).sort(function(t,r){return e[t]-e[r]})}},19035:(e,t,r)=>{"use strict";r.d(t,{f:()=>h});var n=r(16377),i=r(46605),a=r(41643);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class s{static create(e){return new s(e)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}constructor(e){this.scale=e}}l(s,"EPS",1e-4);var u=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function c(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)if(void 0!==r&&!0!==r(e[i]))return;else n.push(e[i]);return n}function f(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e,t,r){var o,{tick:l,ticks:s,viewBox:d,minTickGap:h,orientation:y,interval:g,tickFormatter:v,unit:m,angle:b}=e;if(!s||!s.length||!l)return[];if((0,n.Et)(g)||a.m.isSsr)return null!=(o=c(s,((0,n.Et)(g)?g:0)+1))?o:[];var x=[],w="top"===y||"bottom"===y?"width":"height",O=m&&"width"===w?(0,i.P)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},P=(e,n)=>{var a,o="function"==typeof v?v(e.value,n):e.value;return"width"===w?(a=(0,i.P)(o,{fontSize:t,letterSpacing:r}),u({width:a.width+O.width,height:a.height+O.height},b)):(0,i.P)(o,{fontSize:t,letterSpacing:r})[w]},j=s.length>=2?(0,n.sA)(s[1].coordinate-s[0].coordinate):1,S=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(d,j,w);return"equidistantPreserveStart"===g?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:s}=t,u=0,d=1,p=l;d<=o.length;)if(a=function(){var t,a=null==n?void 0:n[u];if(void 0===a)return{v:c(n,d)};var o=u,h=()=>(void 0===t&&(t=r(a,o)),t),y=a.coordinate,g=0===u||f(e,y,h,p,s);g||(u=0,p=l,d+=1),g&&(p=y+e*(h()/2+i),u+=d)}())return a.v;return[]}(j,S,P,s,h):("preserveStart"===g||"preserveStartEnd"===g?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:s,end:u}=t;if(a){var c=n[l-1],d=r(c,l-1),h=e*(c.coordinate+e*d/2-u);o[l-1]=c=p(p({},c),{},{tickCoord:h>0?c.coordinate-h*e:c.coordinate}),f(e,c.tickCoord,()=>d,s,u)&&(u=c.tickCoord-e*(d/2+i),o[l-1]=p(p({},c),{},{isShow:!0}))}for(var y=a?l-1:l,g=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var c=e*(a.coordinate-e*l()/2-s);o[t]=a=p(p({},a),{},{tickCoord:c<0?a.coordinate-c*e:a.coordinate})}else o[t]=a=p(p({},a),{},{tickCoord:a.coordinate});f(e,a.tickCoord,l,s,u)&&(s=a.tickCoord+e*(l()/2+i),o[t]=p(p({},a),{},{isShow:!0}))},v=0;v<y;v++)g(v);return o}(j,S,P,s,h,"preserveStartEnd"===g):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:s}=t,u=function(t){var n,u=a[t],c=()=>(void 0===n&&(n=r(u,t)),n);if(t===o-1){var d=e*(u.coordinate+e*c()/2-s);a[t]=u=p(p({},u),{},{tickCoord:d>0?u.coordinate-d*e:u.coordinate})}else a[t]=u=p(p({},u),{},{tickCoord:u.coordinate});f(e,u.tickCoord,c,l,s)&&(s=u.tickCoord-e*(c()/2+i),a[t]=p(p({},u),{},{isShow:!0}))},c=o-1;c>=0;c--)u(c);return a}(j,S,P,s,h)).filter(e=>e.isShow)}},19297:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=f(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(n,a,o):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(12115)),i=c(r(38637)),a=c(r(47650)),o=r(30624),l=r(9055),s=r(34425),u=c(r(63265));function c(e){return e&&e.__esModule?e:{default:e}}function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(f=function(e){return e?r:t})(e)}function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:String(n))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let p={touch:{start:"touchstart",move:"touchmove",stop:"touchend"},mouse:{start:"mousedown",move:"mousemove",stop:"mouseup"}},h=p.mouse;class y extends n.Component{constructor(){super(...arguments),d(this,"dragging",!1),d(this,"lastX",NaN),d(this,"lastY",NaN),d(this,"touchIdentifier",null),d(this,"mounted",!1),d(this,"handleDragStart",e=>{if(this.props.onMouseDown(e),!this.props.allowAnyClick&&"number"==typeof e.button&&0!==e.button)return!1;let t=this.findDOMNode();if(!t||!t.ownerDocument||!t.ownerDocument.body)throw Error("<DraggableCore> not mounted on DragStart!");let{ownerDocument:r}=t;if(this.props.disabled||!(e.target instanceof r.defaultView.Node)||this.props.handle&&!(0,o.matchesSelectorAndParentsTo)(e.target,this.props.handle,t)||this.props.cancel&&(0,o.matchesSelectorAndParentsTo)(e.target,this.props.cancel,t))return;"touchstart"===e.type&&e.preventDefault();let n=(0,o.getTouchIdentifier)(e);this.touchIdentifier=n;let i=(0,l.getControlPosition)(e,n,this);if(null==i)return;let{x:a,y:s}=i,c=(0,l.createCoreData)(this,a,s);(0,u.default)("DraggableCore: handleDragStart: %j",c),(0,u.default)("calling",this.props.onStart),!1!==this.props.onStart(e,c)&&!1!==this.mounted&&(this.props.enableUserSelectHack&&(0,o.addUserSelectStyles)(r),this.dragging=!0,this.lastX=a,this.lastY=s,(0,o.addEvent)(r,h.move,this.handleDrag),(0,o.addEvent)(r,h.stop,this.handleDragStop))}),d(this,"handleDrag",e=>{let t=(0,l.getControlPosition)(e,this.touchIdentifier,this);if(null==t)return;let{x:r,y:n}=t;if(Array.isArray(this.props.grid)){let e=r-this.lastX,t=n-this.lastY;if([e,t]=(0,l.snapToGrid)(this.props.grid,e,t),!e&&!t)return;r=this.lastX+e,n=this.lastY+t}let i=(0,l.createCoreData)(this,r,n);if((0,u.default)("DraggableCore: handleDrag: %j",i),!1===this.props.onDrag(e,i)||!1===this.mounted){try{this.handleDragStop(new MouseEvent("mouseup"))}catch(t){let e=document.createEvent("MouseEvents");e.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),this.handleDragStop(e)}return}this.lastX=r,this.lastY=n}),d(this,"handleDragStop",e=>{if(!this.dragging)return;let t=(0,l.getControlPosition)(e,this.touchIdentifier,this);if(null==t)return;let{x:r,y:n}=t;if(Array.isArray(this.props.grid)){let e=r-this.lastX||0,t=n-this.lastY||0;[e,t]=(0,l.snapToGrid)(this.props.grid,e,t),r=this.lastX+e,n=this.lastY+t}let i=(0,l.createCoreData)(this,r,n);if(!1===this.props.onStop(e,i)||!1===this.mounted)return!1;let a=this.findDOMNode();a&&this.props.enableUserSelectHack&&(0,o.removeUserSelectStyles)(a.ownerDocument),(0,u.default)("DraggableCore: handleDragStop: %j",i),this.dragging=!1,this.lastX=NaN,this.lastY=NaN,a&&((0,u.default)("DraggableCore: Removing handlers"),(0,o.removeEvent)(a.ownerDocument,h.move,this.handleDrag),(0,o.removeEvent)(a.ownerDocument,h.stop,this.handleDragStop))}),d(this,"onMouseDown",e=>(h=p.mouse,this.handleDragStart(e))),d(this,"onMouseUp",e=>(h=p.mouse,this.handleDragStop(e))),d(this,"onTouchStart",e=>(h=p.touch,this.handleDragStart(e))),d(this,"onTouchEnd",e=>(h=p.touch,this.handleDragStop(e)))}componentDidMount(){this.mounted=!0;let e=this.findDOMNode();e&&(0,o.addEvent)(e,p.touch.start,this.onTouchStart,{passive:!1})}componentWillUnmount(){this.mounted=!1;let e=this.findDOMNode();if(e){let{ownerDocument:t}=e;(0,o.removeEvent)(t,p.mouse.move,this.handleDrag),(0,o.removeEvent)(t,p.touch.move,this.handleDrag),(0,o.removeEvent)(t,p.mouse.stop,this.handleDragStop),(0,o.removeEvent)(t,p.touch.stop,this.handleDragStop),(0,o.removeEvent)(e,p.touch.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,o.removeUserSelectStyles)(t)}}findDOMNode(){var e,t;return null!=(e=this.props)&&e.nodeRef?null==(t=this.props)||null==(t=t.nodeRef)?void 0:t.current:a.default.findDOMNode(this)}render(){return n.cloneElement(n.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}}t.default=y,d(y,"displayName","DraggableCore"),d(y,"propTypes",{allowAnyClick:i.default.bool,children:i.default.node.isRequired,disabled:i.default.bool,enableUserSelectHack:i.default.bool,offsetParent:function(e,t){if(e[t]&&1!==e[t].nodeType)throw Error("Draggable's offsetParent must be a DOM Node.")},grid:i.default.arrayOf(i.default.number),handle:i.default.string,cancel:i.default.string,nodeRef:i.default.object,onStart:i.default.func,onDrag:i.default.func,onStop:i.default.func,onMouseDown:i.default.func,scale:i.default.number,className:s.dontSetMe,style:s.dontSetMe,transform:s.dontSetMe}),d(y,"defaultProps",{allowAnyClick:!1,disabled:!1,enableUserSelectHack:!0,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1})},19452:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},20215:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>ei,eE:()=>es,Xb:()=>ea,A2:()=>en,yn:()=>eu,Dn:()=>P,gL:()=>V,fl:()=>Y,R4:()=>J,Re:()=>w,n4:()=>k});var n=r(68924),i=r(14299),a=r(97238),o=r(39827),l=r(60356),s=r(18478),u=r(16377),c=r(18190),f=r(96523),d=r(60530),p=r(11928),h=r(60841),y=r(64968),g=r(2589),v=r(69449),m=r(85146),b=r(46670),x=r(75714),w=e=>{var t=(0,a.fz)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},O=e=>e.tooltip.settings.axisId,P=e=>{var t=w(e),r=O(e);return(0,i.Hd)(e,t,r)},j=(0,n.Mz)([P,a.fz,i.um,s.iO,w],i.sr),S=(0,n.Mz)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),E=(0,n.Mz)([w,O],i.eo),M=(0,n.Mz)([S,P,E],i.ec),A=(0,n.Mz)([M],i.rj),k=(0,n.Mz)([A,l.LF],i.Nk),_=(0,n.Mz)([k,P,M],i.fb),D=(0,n.Mz)([P],i.S5),C=(0,n.Mz)([k,M,s.eC],i.MK),T=(0,n.Mz)([C,l.LF,w],i.pM),N=(0,n.Mz)([M],i.IO),z=(0,n.Mz)([k,P,N,w],i.kz),I=(0,n.Mz)([i.Kr,w,O],i.P9),R=(0,n.Mz)([I,w],i.Oz),L=(0,n.Mz)([i.gT,w,O],i.P9),H=(0,n.Mz)([L,w],i.q),$=(0,n.Mz)([i.$X,w,O],i.P9),B=(0,n.Mz)([$,w],i.bb),W=(0,n.Mz)([R,B,H],i.yi),U=(0,n.Mz)([P,D,T,z,W],i.wL),F=(0,n.Mz)([P,a.fz,k,_,s.eC,w,U],i.tP),K=(0,n.Mz)([F,P,j],i.xp),G=(0,n.Mz)([P,F,K,w],i.g1),q=e=>{var t=w(e),r=O(e);return(0,i.D5)(e,t,r,!1)},V=(0,n.Mz)([P,q],c.I),Y=(0,n.Mz)([P,j,G,V],i.Qn),Z=(0,n.Mz)([a.fz,_,P,w],i.tF),X=(0,n.Mz)([a.fz,_,P,w],i.iv),J=(0,n.Mz)([a.fz,P,j,Y,q,Z,X,w],(e,t,r,n,i,a,l,s)=>{if(t){var{type:c}=t,f=(0,o._L)(e,s);if(n){var d="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,p="category"===c&&n.bandwidth?n.bandwidth()/d:0;return(p="angleAxis"===s&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,u.sA)(i[0]-i[1])*p:p,f&&l)?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:a?a[e]:e,index:t,offset:p}))}}}),Q=(0,n.Mz)([f.xH,f.Hw,e=>e.tooltip.settings],(e,t,r)=>(0,f.$g)(r.shared,e,t)),ee=e=>e.tooltip.settings.trigger,et=e=>e.tooltip.settings.defaultIndex,er=(0,n.Mz)([x.J,Q,ee,et],p.i),en=(0,n.Mz)([er,k],h.P),ei=(0,n.Mz)([J,en],d.E),ea=(0,n.Mz)([er],e=>{if(e)return e.dataKey}),eo=(0,n.Mz)([x.J,Q,ee,et],m.q),el=(0,n.Mz)([g.Lp,g.A$,a.fz,v.GO,J,et,eo,b.x],y.o),es=(0,n.Mz)([er,el],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),eu=(0,n.Mz)([er],e=>e.active)},20241:(e,t,r)=>{e.exports=r(22434).sortBy},20400:(e,t,r)=>{e.exports=r(82962).throttle},20749:e=>{function t(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}e.exports=t,e.exports.clsx=t},20972:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,U:()=>s});var n=r(68924),i=r(69449),a=r(2589),o=r(16377),l=e=>e.brush,s=(0,n.Mz)([l,i.GO,a.HK],(e,t,r)=>({height:e.height,x:(0,o.Et)(e.x)?e.x:t.left,y:(0,o.Et)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.Et)(e.width)?e.width:t.width}))},21383:(e,t)=>{"use strict";function r(e){let{margin:t,containerPadding:r,containerWidth:n,cols:i}=e;return(n-t[0]*(i-1)-2*r[0])/i}function n(e,t,r){return Number.isFinite(e)?Math.round(t*e+Math.max(0,e-1)*r):e}function i(e,t,r){return Math.max(Math.min(e,r),t)}Object.defineProperty(t,"__esModule",{value:!0}),t.calcGridColWidth=r,t.calcGridItemPosition=function(e,t,i,a,o,l){let{margin:s,containerPadding:u,rowHeight:c}=e,f=r(e),d={};return l&&l.resizing?(d.width=Math.round(l.resizing.width),d.height=Math.round(l.resizing.height)):(d.width=n(a,f,s[0]),d.height=n(o,c,s[1])),l&&l.dragging?(d.top=Math.round(l.dragging.top),d.left=Math.round(l.dragging.left)):l&&l.resizing&&"number"==typeof l.resizing.top&&"number"==typeof l.resizing.left?(d.top=Math.round(l.resizing.top),d.left=Math.round(l.resizing.left)):(d.top=Math.round((c+s[1])*i+u[1]),d.left=Math.round((f+s[0])*t+u[0])),d},t.calcGridItemWHPx=n,t.calcWH=function(e,t,n,a,o,l){let{margin:s,maxRows:u,cols:c,rowHeight:f}=e,d=r(e),p=Math.round((t+s[0])/(d+s[0])),h=Math.round((n+s[1])/(f+s[1])),y=i(p,0,c-a),g=i(h,0,u-o);return -1!==["sw","w","nw"].indexOf(l)&&(y=i(p,0,c)),-1!==["nw","n","ne"].indexOf(l)&&(g=i(h,0,u)),{w:y,h:g}},t.calcXY=function(e,t,n,a,o){let{margin:l,containerPadding:s,cols:u,rowHeight:c,maxRows:f}=e,d=r(e),p=Math.round((n-s[0])/(d+l[0])),h=Math.round((t-s[1])/(c+l[1]));return{x:p=i(p,0,u-a),y:h=i(h,0,f-o)}},t.clamp=i},22188:(e,t,r)=>{e.exports=r(85252).isEqual},22248:(e,t,r)=>{"use strict";r.d(t,{As:()=>c,Ch:()=>l,TK:()=>f,Vi:()=>u,g5:()=>s,iZ:()=>d,lm:()=>o});var n=r(5710),i=r(74532),a=(0,n.Z0)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,i.h4)(t.payload))},removeCartesianGraphicalItem(e,t){var r=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,i.h4)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,i.ss)(e).polarItems.indexOf((0,i.h4)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addBar:o,removeBar:l,addCartesianGraphicalItem:s,removeCartesianGraphicalItem:u,addPolarGraphicalItem:c,removePolarGraphicalItem:f}=a.actions,d=a.reducer},22434:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(47064),i=r(55998),a=r(64373);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},22436:(e,t,r)=>{"use strict";var n=r(12115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,s=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,c=n[1];return l(function(){i.value=r,i.getSnapshot=t,u(i)&&c({inst:i})},[e,r,t]),o(function(){return u(i)&&c({inst:i}),e(function(){u(i)&&c({inst:i})})},[e]),s(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},22520:(e,t,r)=>{"use strict";var n=r(49641).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(1147),a=r(98221),o=r(15160),l=r(42721),s=r(83616);t.isEqualWith=function(e,t,r){return function e(t,r,u,c,f,d,p){let h=p(t,r,u,c,f,d);if(void 0!==h)return h;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,u,c,f){if(Object.is(r,u))return!0;let d=o.getTag(r),p=o.getTag(u);if(d===l.argumentsTag&&(d=l.objectTag),p===l.argumentsTag&&(p=l.objectTag),d!==p)return!1;switch(d){case l.stringTag:return r.toString()===u.toString();case l.numberTag:{let e=r.valueOf(),t=u.valueOf();return s.eq(e,t)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),u.valueOf());case l.regexpTag:return r.source===u.source&&r.flags===u.flags;case l.functionTag:return r===u}let h=(c=c??new Map).get(r),y=c.get(u);if(null!=h&&null!=y)return h===u;c.set(r,u),c.set(u,r);try{switch(d){case l.mapTag:if(r.size!==u.size)return!1;for(let[t,n]of r.entries())if(!u.has(t)||!e(n,u.get(t),t,r,u,c,f))return!1;return!0;case l.setTag:{if(r.size!==u.size)return!1;let t=Array.from(r.values()),n=Array.from(u.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,u,c,f));if(-1===o)return!1;n.splice(o,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(u)||r.length!==u.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],u[t],t,r,u,c,f))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==u.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(u),c,f);case l.dataViewTag:if(r.byteLength!==u.byteLength||r.byteOffset!==u.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(u),c,f);case l.errorTag:return r.name===u.name&&r.message===u.message;case l.objectTag:{if(!(t(r.constructor,u.constructor,c,f)||i.isPlainObject(r)&&i.isPlainObject(u)))return!1;let n=[...Object.keys(r),...a.getSymbols(r)],o=[...Object.keys(u),...a.getSymbols(u)];if(n.length!==o.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],a=r[i];if(!Object.hasOwn(u,i))return!1;let o=u[i];if(!e(a,o,i,r,u,c,f))return!1}return!0}default:return!1}}finally{c.delete(r),c.delete(u)}}(t,r,d,p)}(e,t,void 0,void 0,void 0,void 0,r)}},22897:(e,t,r)=>{e.exports=r(78503).default,r(50507),r(21383),e.exports.Responsive=r(5993).default,e.exports.Responsive.utils=r(18957),e.exports.WidthProvider=r(54528).default},23676:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},24517:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),i=r(46200),a=r(37298),o=r(10921),l=r(93205);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},25115:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,X:()=>a});var n=r(12115),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},25641:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{IZ:()=>l,Kg:()=>a,lY:()=>s,yy:()=>p}),r(12115);var a=Math.PI/180,o=e=>180*e/Math.PI,l=(e,t,r,n)=>({x:e+Math.cos(-a*n)*r,y:t+Math.sin(-a*n)*r}),s=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},u=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},c=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,l=u({x:r,y:n},{x:i,y:a});if(l<=0)return{radius:l,angle:0};var s=Math.acos((r-i)/l);return n>a&&(s=2*Math.PI-s),{radius:l,angle:o(s),angleInRadian:s}},f=e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},d=(e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))},p=(e,t)=>{var r,{x:n,y:a}=e,{radius:o,angle:l}=c({x:n,y:a},t),{innerRadius:s,outerRadius:u}=t;if(o<s||o>u||0===o)return null;var{startAngle:p,endAngle:h}=f(t),y=l;if(p<=h){for(;y>h;)y-=360;for(;y<p;)y+=360;r=y>=p&&y<=h}else{for(;y>p;)y-=360;for(;y<h;)y+=360;r=y>=h&&y<=p}return r?i(i({},t),{},{radius:o,angle:d(y,t)}):null}},27040:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},27119:(e,t,r)=>{"use strict";r.d(t,{p:()=>u,v:()=>c});var n=r(12115),i=r(81971),a=r(22248),o=r(39827);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){var t=(0,i.j)();return(0,n.useEffect)(()=>{var r=s(s({},e),{},{stackId:(0,o.$8)(e.stackId)});return t((0,a.g5)(r)),()=>{t((0,a.Vi)(r))}},[t,e]),null}function c(e){var t=(0,i.j)();return(0,n.useEffect)(()=>(t((0,a.As)(e)),()=>{t((0,a.TK)(e))}),[t,e]),null}},29738:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(44117),i=r(42721);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let s=t?.(r,a,o,l);if(null!=s)return s;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},30564:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t,{signal:r,edges:n}={}){let i,a=null,o=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),s=()=>{null!==a&&(e.apply(i,a),i=void 0,a=null)},u=()=>{l&&s(),p()},c=null,f=()=>{null!=c&&clearTimeout(c),c=setTimeout(()=>{c=null,u()},t)},d=()=>{null!==c&&(clearTimeout(c),c=null)},p=()=>{d(),i=void 0,a=null},h=function(...e){if(r?.aborted)return;i=this,a=e;let t=null==c;f(),o&&t&&s()};return h.schedule=f,h.cancel=p,h.flush=()=>{d(),s()},r?.addEventListener("abort",p,{once:!0}),h}},30624:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addClassName=u,t.addEvent=function(e,t,r,n){if(!e)return;let i={capture:!0,...n};e.addEventListener?e.addEventListener(t,r,i):e.attachEvent?e.attachEvent("on"+t,r):e["on"+t]=r},t.addUserSelectStyles=function(e){if(!e)return;let t=e.getElementById("react-draggable-style-el");t||((t=e.createElement("style")).type="text/css",t.id="react-draggable-style-el",t.innerHTML=".react-draggable-transparent-selection *::-moz-selection {all: inherit;}\n",t.innerHTML+=".react-draggable-transparent-selection *::selection {all: inherit;}\n",e.getElementsByTagName("head")[0].appendChild(t)),e.body&&u(e.body,"react-draggable-transparent-selection")},t.createCSSTransform=function(e,t){let r=s(e,t,"px");return{[(0,i.browserPrefixToKey)("transform",i.default)]:r}},t.createSVGTransform=function(e,t){return s(e,t,"")},t.getTouch=function(e,t){return e.targetTouches&&(0,n.findInArray)(e.targetTouches,e=>t===e.identifier)||e.changedTouches&&(0,n.findInArray)(e.changedTouches,e=>t===e.identifier)},t.getTouchIdentifier=function(e){return e.targetTouches&&e.targetTouches[0]?e.targetTouches[0].identifier:e.changedTouches&&e.changedTouches[0]?e.changedTouches[0].identifier:void 0},t.getTranslation=s,t.innerHeight=function(e){let t=e.clientHeight,r=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,n.int)(r.paddingTop),t-=(0,n.int)(r.paddingBottom)},t.innerWidth=function(e){let t=e.clientWidth,r=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,n.int)(r.paddingLeft),t-=(0,n.int)(r.paddingRight)},t.matchesSelector=l,t.matchesSelectorAndParentsTo=function(e,t,r){let n=e;do{if(l(n,t))return!0;if(n===r)break;n=n.parentNode}while(n);return!1},t.offsetXYFromParent=function(e,t,r){let n=t===t.ownerDocument.body?{left:0,top:0}:t.getBoundingClientRect();return{x:(e.clientX+t.scrollLeft-n.left)/r,y:(e.clientY+t.scrollTop-n.top)/r}},t.outerHeight=function(e){let t=e.clientHeight,r=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,n.int)(r.borderTopWidth),t+=(0,n.int)(r.borderBottomWidth)},t.outerWidth=function(e){let t=e.clientWidth,r=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,n.int)(r.borderLeftWidth),t+=(0,n.int)(r.borderRightWidth)},t.removeClassName=c,t.removeEvent=function(e,t,r,n){if(!e)return;let i={capture:!0,...n};e.removeEventListener?e.removeEventListener(t,r,i):e.detachEvent?e.detachEvent("on"+t,r):e["on"+t]=null},t.removeUserSelectStyles=function(e){if(e)try{if(e.body&&c(e.body,"react-draggable-transparent-selection"),e.selection)e.selection.empty();else{let t=(e.defaultView||window).getSelection();t&&"Caret"!==t.type&&t.removeAllRanges()}}catch(e){}};var n=r(34425),i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=i?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(55379));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}let o="";function l(e,t){return o||(o=(0,n.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],function(t){return(0,n.isFunction)(e[t])})),!!(0,n.isFunction)(e[o])&&e[o](t)}function s(e,t,r){let{x:n,y:i}=e,a="translate(".concat(n).concat(r,",").concat(i).concat(r,")");if(t){let e="".concat("string"==typeof t.x?t.x:t.x+r),n="".concat("string"==typeof t.y?t.y:t.y+r);a="translate(".concat(e,", ").concat(n,")")+a}return a}function u(e,t){e.classList?e.classList.add(t):e.className.match(new RegExp("(?:^|\\s)".concat(t,"(?!\\S)")))||(e.className+=" ".concat(t))}function c(e,t){e.classList?e.classList.remove(t):e.className=e.className.replace(RegExp("(?:^|\\s)".concat(t,"(?!\\S)"),"g"),"")}},31e3:(e,t,r)=>{"use strict";t.__esModule=!0,t.resizableProps=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(r(38637));r(5939),t.resizableProps={axis:n.default.oneOf(["both","x","y","none"]),className:n.default.string,children:n.default.element.isRequired,draggableOpts:n.default.shape({allowAnyClick:n.default.bool,cancel:n.default.string,children:n.default.node,disabled:n.default.bool,enableUserSelectHack:n.default.bool,offsetParent:n.default.node,grid:n.default.arrayOf(n.default.number),handle:n.default.string,nodeRef:n.default.object,onStart:n.default.func,onDrag:n.default.func,onStop:n.default.func,onMouseDown:n.default.func,scale:n.default.number}),height:function(){for(var e,t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];var a=r[0];return"both"===a.axis||"y"===a.axis?(e=n.default.number).isRequired.apply(e,r):n.default.number.apply(n.default,r)},handle:n.default.oneOfType([n.default.node,n.default.func]),handleSize:n.default.arrayOf(n.default.number),lockAspectRatio:n.default.bool,maxConstraints:n.default.arrayOf(n.default.number),minConstraints:n.default.arrayOf(n.default.number),onResizeStop:n.default.func,onResizeStart:n.default.func,onResize:n.default.func,resizeHandles:n.default.arrayOf(n.default.oneOf(["s","w","e","n","sw","nw","se","ne"])),transformScale:n.default.number,width:function(){for(var e,t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];var a=r[0];return"both"===a.axis||"x"===a.axis?(e=n.default.number).isRequired.apply(e,r):n.default.number.apply(n.default,r)}}},31847:(e,t,r)=>{"use strict";r.d(t,{i:()=>s});let n=Math.PI,i=2*n,a=i-1e-6;function o(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class l{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?o:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return o;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,i,a){if(e*=1,t*=1,r*=1,i*=1,(a*=1)<0)throw Error(`negative radius: ${a}`);let o=this._x1,l=this._y1,s=r-e,u=i-t,c=o-e,f=l-t,d=c*c+f*f;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(d>1e-6)if(Math.abs(f*s-u*c)>1e-6&&a){let p=r-o,h=i-l,y=s*s+u*u,g=Math.sqrt(y),v=Math.sqrt(d),m=a*Math.tan((n-Math.acos((y+d-(p*p+h*h))/(2*g*v)))/2),b=m/v,x=m/g;Math.abs(b-1)>1e-6&&this._append`L${e+b*c},${t+b*f}`,this._append`A${a},${a},0,0,${+(f*p>c*h)},${this._x1=e+x*s},${this._y1=t+x*u}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,o,l,s){if(e*=1,t*=1,r*=1,s=!!s,r<0)throw Error(`negative radius: ${r}`);let u=r*Math.cos(o),c=r*Math.sin(o),f=e+u,d=t+c,p=1^s,h=s?o-l:l-o;null===this._x1?this._append`M${f},${d}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-d)>1e-6)&&this._append`L${f},${d}`,r&&(h<0&&(h=h%i+i),h>a?this._append`A${r},${r},0,1,${p},${e-u},${t-c}A${r},${r},0,1,${p},${this._x1=f},${this._y1=d}`:h>1e-6&&this._append`A${r},${r},0,${+(h>=n)},${p},${this._x1=e+r*Math.cos(l)},${this._y1=t+r*Math.sin(l)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function s(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new l(t)}l.prototype},32634:(e,t,r)=>{"use strict";r.d(t,{CU:()=>c,Lx:()=>s,u3:()=>u});var n=r(5710),i=r(74532),a=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign},addLegendPayload(e,t){e.payload.push((0,i.h4)(t.payload))},removeLegendPayload(e,t){var r=(0,i.ss)(e).payload.indexOf((0,i.h4)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:l,addLegendPayload:s,removeLegendPayload:u}=a.actions,c=a.reducer},33725:(e,t,r)=>{"use strict";r.d(t,{L:()=>R});var n=r(12115),i=r(70788),a=r(97238),o=r(96752),l=r(71807),s=r(52596),u=["children","width","height","viewBox","className","style","title","desc"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var f=(0,n.forwardRef)((e,t)=>{var{children:r,width:a,height:o,viewBox:l,className:f,style:d,title:p,desc:h}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,u),g=l||{width:a,height:o,x:0,y:0},v=(0,s.$)("recharts-surface",f);return n.createElement("svg",c({},(0,i.J9)(y,!0,"svg"),{className:v,width:a,height:o,style:d,viewBox:"".concat(g.x," ").concat(g.y," ").concat(g.width," ").concat(g.height),ref:t}),n.createElement("title",null,p),n.createElement("desc",null,h),r)}),d=r(81971),p=r(20972),h=r(78892),y=["children"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v={width:"100%",height:"100%"},m=(0,n.forwardRef)((e,t)=>{var r,i,l=(0,a.yi)(),s=(0,a.rY)(),u=(0,o.$)();if(!(0,h.F)(l)||!(0,h.F)(s))return null;var{children:c,otherAttributes:d,title:p,desc:y}=e;return r="number"==typeof d.tabIndex?d.tabIndex:u?0:void 0,i="string"==typeof d.role?d.role:u?"application":void 0,n.createElement(f,g({},d,{title:p,desc:y,role:i,tabIndex:r,width:l,height:s,style:v,ref:t}),c)}),b=e=>{var{children:t}=e,r=(0,d.G)(p.U);if(!r)return null;var{width:i,height:a,y:o,x:l}=r;return n.createElement(f,{width:i,height:a,x:l,y:o},t)},x=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y);return(0,l.r)()?n.createElement(b,null,r):n.createElement(m,g({ref:t},i),r)}),w=r(34890),O=r(94685),P=r(46850),j=r(841),S=r(2589),E=r(48627),M=r(37335),A=r(36144),k=r(25115),_=(0,n.createContext)(null);function D(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var C=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,height:a,onClick:o,onContextMenu:l,onDoubleClick:u,onMouseDown:c,onMouseEnter:f,onMouseLeave:p,onMouseMove:y,onMouseUp:g,onTouchEnd:v,onTouchMove:m,onTouchStart:b,style:x,width:C}=e,T=(0,d.j)(),[N,z]=(0,n.useState)(null),[I,R]=(0,n.useState)(null);(0,P.l3)();var L=function(){var e=(0,d.j)(),[t,r]=(0,n.useState)(null),i=(0,d.G)(S.et);return(0,n.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;(0,h.H)(r)&&r!==i&&e((0,E.hF)(r))}},[t,e,i]),r}(),H=(0,n.useCallback)(e=>{L(e),"function"==typeof t&&t(e),z(e),R(e)},[L,t,z,R]),$=(0,n.useCallback)(e=>{T((0,O.ky)(e)),T((0,M.y)({handler:o,reactEvent:e}))},[T,o]),B=(0,n.useCallback)(e=>{T((0,O.dj)(e)),T((0,M.y)({handler:f,reactEvent:e}))},[T,f]),W=(0,n.useCallback)(e=>{T((0,w.xS)()),T((0,M.y)({handler:p,reactEvent:e}))},[T,p]),U=(0,n.useCallback)(e=>{T((0,O.dj)(e)),T((0,M.y)({handler:y,reactEvent:e}))},[T,y]),F=(0,n.useCallback)(()=>{T((0,j.Ru)())},[T]),K=(0,n.useCallback)(e=>{T((0,j.uZ)(e.key))},[T]),G=(0,n.useCallback)(e=>{T((0,M.y)({handler:l,reactEvent:e}))},[T,l]),q=(0,n.useCallback)(e=>{T((0,M.y)({handler:u,reactEvent:e}))},[T,u]),V=(0,n.useCallback)(e=>{T((0,M.y)({handler:c,reactEvent:e}))},[T,c]),Y=(0,n.useCallback)(e=>{T((0,M.y)({handler:g,reactEvent:e}))},[T,g]),Z=(0,n.useCallback)(e=>{T((0,M.y)({handler:b,reactEvent:e}))},[T,b]),X=(0,n.useCallback)(e=>{T((0,A.e)(e)),T((0,M.y)({handler:m,reactEvent:e}))},[T,m]),J=(0,n.useCallback)(e=>{T((0,M.y)({handler:v,reactEvent:e}))},[T,v]);return n.createElement(k.$.Provider,{value:N},n.createElement(_.Provider,{value:I},n.createElement("div",{className:(0,s.$)("recharts-wrapper",i),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?D(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):D(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:C,height:a},x),role:"application",onClick:$,onContextMenu:G,onDoubleClick:q,onFocus:F,onKeyDown:K,onMouseDown:V,onMouseEnter:B,onMouseLeave:W,onMouseMove:U,onMouseUp:Y,onTouchEnd:J,onTouchMove:X,onTouchStart:Z,ref:H},r)))}),T=r(16377),N=(0,n.createContext)(void 0),z=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,T.NF)("recharts"),"-clip")),i=(0,a.hj)();if(null==i)return null;var{left:o,top:l,height:s,width:u}=i;return n.createElement(N.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:o,y:l,height:s,width:u}))),t)},I=["children","className","width","height","style","compact","title","desc"],R=(0,n.forwardRef)((e,t)=>{var{children:r,className:a,width:o,height:l,style:s,compact:u,title:c,desc:f}=e,d=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,I),p=(0,i.J9)(d,!1);return u?n.createElement(x,{otherAttributes:p,title:c,desc:f},r):n.createElement(C,{className:a,style:s,width:o,height:l,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(x,{otherAttributes:p,title:c,desc:f,ref:t},n.createElement(z,null,r)))})},34259:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="";r<arguments.length;)(e=arguments[r++])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t))for(r=0;r<t.length;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n);else for(r in t)t[r]&&(i&&(i+=" "),i+=r);return i}(e))&&(n&&(n+=" "),n+=t);return n}r.r(t),r.d(t,{clsx:()=>n,default:()=>i});let i=n},34425:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.dontSetMe=function(e,t,r){if(e[t])return Error("Invalid prop ".concat(t," passed to ").concat(r," - do not set this, set it on the child."))},t.findInArray=function(e,t){for(let r=0,n=e.length;r<n;r++)if(t.apply(t,[e[r],r,e]))return e[r]},t.int=function(e){return parseInt(e,10)},t.isFunction=function(e){return"function"==typeof e||"[object Function]"===Object.prototype.toString.call(e)},t.isNum=function(e){return"number"==typeof e&&!isNaN(e)}},34487:(e,t,r)=>{"use strict";r.d(t,{LV:()=>l,M:()=>a,hq:()=>i});var n=(0,r(5710).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:a,setComputedData:o}=n.actions,l=n.reducer},34890:(e,t,r)=>{"use strict";r.d(t,{E1:()=>g,En:()=>m,Ix:()=>l,ML:()=>p,Nt:()=>h,RD:()=>c,UF:()=>u,XB:()=>s,jF:()=>y,k_:()=>a,o4:()=>v,oP:()=>f,xS:()=>d});var n=r(5710),i=r(74532),a={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:a,hover:a},axisInteraction:{click:a,hover:a},keyboardInteraction:a,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,i.h4)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,i.ss)(e).tooltipItemPayloads.indexOf((0,i.h4)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:s,setTooltipSettingsState:u,setActiveMouseOverItemIndex:c,mouseLeaveItem:f,mouseLeaveChart:d,setActiveClickItemIndex:p,setMouseOverAxisIndex:h,setMouseClickAxisIndex:y,setSyncInteraction:g,setKeyboardInteraction:v}=o.actions,m=o.reducer},36079:(e,t,r)=>{"use strict";r.d(t,{Z:()=>m});var n=r(12115),i=r(58080),a=r.n(i),o=r(60379),l=r(2348),s=r(70788),u=r(39827),c=r(16377),f=["valueAccessor"],d=["data","dataKey","clockWise","id","textBreakAll"];function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var v=e=>Array.isArray(e.value)?a()(e.value):e.value;function m(e){var{valueAccessor:t=v}=e,r=g(e,f),{data:i,dataKey:a,clockWise:h,id:m,textBreakAll:b}=r,x=g(r,d);return i&&i.length?n.createElement(l.W,{className:"recharts-label-list"},i.map((e,r)=>{var i=(0,c.uy)(a)?t(e,r):(0,u.kr)(e&&e.payload,a),l=(0,c.uy)(m)?{}:{id:"".concat(m,"-").concat(r)};return n.createElement(o.J,p({},(0,s.J9)(e,!0),x,l,{parentViewBox:e.parentViewBox,value:i,textBreakAll:b,viewBox:o.J.parseViewBox((0,c.uy)(h)?e:y(y({},e),{},{clockWise:h})),key:"label-".concat(r),index:r}))})):null}m.displayName="LabelList",m.renderCallByParent=function(e,t){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&i&&!e.label)return null;var{children:a}=e,l=(0,s.aS)(a,m).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return i?[(r=e.label,r?!0===r?n.createElement(m,{key:"labelList-implicit",data:t}):n.isValidElement(r)||(0,o.Z)(r)?n.createElement(m,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?n.createElement(m,p({data:t},r,{key:"labelList-implicit"})):null:null),...l]:l}},36144:(e,t,r)=>{"use strict";r.d(t,{e:()=>h,k:()=>y});var n=r(5710),i=r(34890),a=r(63027),o=r(91165),l=r(96523),s=r(84421),u=r(68924),c=r(46670),f=r(75714),d=(0,u.Mz)([f.J],e=>e.tooltipItemPayloads),p=(0,u.Mz)([d,c.x,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),h=(0,n.VP)("touchMove"),y=(0,n.Nc)();y.startListening({actionCreator:h,effect:(e,t)=>{var r=e.payload,n=t.getState(),u=(0,l.au)(n,n.tooltip.settings.shared);if("axis"===u){var c=(0,a.g)(n,(0,o.w)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==c?void 0:c.activeIndex)!=null&&t.dispatch((0,i.Nt)({activeIndex:c.activeIndex,activeDataKey:void 0,activeCoordinate:c.activeCoordinate}))}else if("item"===u){var f,d=r.touches[0],h=document.elementFromPoint(d.clientX,d.clientY);if(!h||!h.getAttribute)return;var y=h.getAttribute(s.F0),g=null!=(f=h.getAttribute(s.um))?f:void 0,v=p(t.getState(),y,g);t.dispatch((0,i.RD)({activeDataKey:g,activeIndex:y,activeCoordinate:v}))}}})},36633:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},37195:(e,t,r)=>{"use strict";r.d(t,{Q:()=>s,l:()=>l});var n=r(12115),i=r(97238),a=r(81971),o=r(14299);function l(e,t){var r,n,i=(0,a.G)(t=>(0,o.Rl)(t,e)),l=(0,a.G)(e=>(0,o.sf)(e,t)),s=null!=(r=null==i?void 0:i.allowDataOverflow)?r:o.PU.allowDataOverflow,u=null!=(n=null==l?void 0:l.allowDataOverflow)?n:o.cd.allowDataOverflow;return{needClip:s||u,needClipX:s,needClipY:u}}function s(e){var{xAxisId:t,yAxisId:r,clipPathId:a}=e,o=(0,i.hj)(),{needClipX:s,needClipY:u,needClip:c}=l(t,r);if(!c)return null;var{left:f,top:d,width:p,height:h}=o;return n.createElement("clipPath",{id:"clipPath-".concat(a)},n.createElement("rect",{x:s?f:f-p/2,y:u?d:d-h/2,width:s?p:2*p,height:u?h:2*h}))}},37298:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(29738);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},37335:(e,t,r)=>{"use strict";r.d(t,{x:()=>o,y:()=>a});var n=r(5710),i=r(20215),a=(0,n.VP)("externalEvent"),o=(0,n.Nc)();o.startListening({actionCreator:a,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,i.eE)(r),activeDataKey:(0,i.Xb)(r),activeIndex:(0,i.A2)(r),activeLabel:(0,i.BZ)(r),activeTooltipIndex:(0,i.A2)(r),isTooltipActive:(0,i.yn)(r)};e.payload.handler(n,e.payload.reactEvent)}}})},38637:(e,t,r)=>{e.exports=r(79399)()},38924:(e,t,r)=>{"use strict";t.default=void 0;var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=u(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(n,a,o):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(12115)),i=s(r(38637)),a=s(r(56335)),o=r(31e3),l=["handle","handleSize","onResize","onResizeStart","onResizeStop","draggableOpts","minConstraints","maxConstraints","lockAspectRatio","axis","width","height","resizeHandles","style","transformScale"];function s(e){return e&&e.__esModule?e:{default:e}}function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(u=function(e){return e?r:t})(e)}function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p(e,t){return(p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var h=function(e){function t(){for(var t,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return(t=e.call.apply(e,[this].concat(n))||this).state={width:t.props.width,height:t.props.height,propsWidth:t.props.width,propsHeight:t.props.height},t.onResize=function(e,r){var n=r.size;t.props.onResize?(null==e.persist||e.persist(),t.setState(n,function(){return t.props.onResize&&t.props.onResize(e,r)})):t.setState(n)},t}return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,p(t,e),t.getDerivedStateFromProps=function(e,t){return t.propsWidth!==e.width||t.propsHeight!==e.height?{width:e.width,height:e.height,propsWidth:e.width,propsHeight:e.height}:null},t.prototype.render=function(){var e=this.props,t=e.handle,r=e.handleSize,i=(e.onResize,e.onResizeStart),o=e.onResizeStop,s=e.draggableOpts,u=e.minConstraints,f=e.maxConstraints,p=e.lockAspectRatio,h=e.axis,y=(e.width,e.height,e.resizeHandles),g=e.style,v=e.transformScale,m=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,l);return n.createElement(a.default,{axis:h,draggableOpts:s,handle:t,handleSize:r,height:this.state.height,lockAspectRatio:p,maxConstraints:f,minConstraints:u,onResizeStart:i,onResize:this.onResize,onResizeStop:o,resizeHandles:y,transformScale:v,width:this.state.width},n.createElement("div",c({},m,{style:d(d({},g),{},{width:this.state.width+"px",height:this.state.height+"px"})})))},t}(n.Component);t.default=h,h.propTypes=d(d({},o.resizableProps),{},{children:i.default.element})},39195:()=>{},39226:(e,t,r)=>{"use strict";r.d(t,{G9:()=>f,_S:()=>d,pU:()=>p,zk:()=>c});var n=r(12115),i=r(27119),a=r(71807),o=["children"],l=()=>{},s=(0,n.createContext)({addErrorBar:l,removeErrorBar:l}),u=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function c(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o);return n.createElement(u.Provider,{value:r},t)}var f=()=>(0,n.useContext)(u),d=e=>{var{children:t,xAxisId:r,yAxisId:o,zAxisId:l,dataKey:u,data:c,stackId:f,hide:d,type:p,barSize:h}=e,[y,g]=n.useState([]),v=(0,n.useCallback)(e=>{g(t=>[...t,e])},[g]),m=(0,n.useCallback)(e=>{g(t=>t.filter(t=>t!==e))},[g]),b=(0,a.r)();return n.createElement(s.Provider,{value:{addErrorBar:v,removeErrorBar:m}},n.createElement(i.p,{type:p,data:c,xAxisId:r,yAxisId:o,zAxisId:l,dataKey:u,errorBars:y,stackId:f,hide:d,barSize:h,isPanorama:b}),t)};function p(e){var{addErrorBar:t,removeErrorBar:r}=(0,n.useContext)(s);return(0,n.useEffect)(()=>(t(e),()=>{r(e)}),[t,r,e]),null}},39426:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});var n=r(12115),i=r(16377);function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,i.NF)(t)),a=(0,n.useRef)(e);return a.current!==e&&(r.current=(0,i.NF)(t),a.current=e),r.current}},39611:(e,t,r)=>{"use strict";r(4993)},39827:(e,t,r)=>{"use strict";r.d(t,{qx:()=>z,IH:()=>N,s0:()=>b,gH:()=>m,SW:()=>B,YB:()=>P,bk:()=>$,Hj:()=>I,DW:()=>_,y2:()=>k,nb:()=>A,PW:()=>w,Mk:()=>T,$8:()=>M,yy:()=>E,Rh:()=>O,GF:()=>R,uM:()=>L,kr:()=>v,r4:()=>H,_L:()=>x,_f:()=>j});var n=r(20241),i=r.n(n),a=r(95672),o=r.n(a);function l(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var s=r(9819),u=r(85654);function c(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function f(e,t){return e[t]}function d(e){let t=[];return t.key=e,t}var p=r(16377),h=r(25641);function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v(e,t,r){return(0,p.uy)(e)||(0,p.uy)(t)?r:(0,p.vh)(t)?o()(e,t,r):"function"==typeof t?t(e):r}var m=(e,t,r,n,i)=>{var a,o=-1,l=null!=(a=null==t?void 0:t.length)?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var s=0;s<l;s++){var u=s>0?r[s-1].coordinate:r[l-1].coordinate,c=r[s].coordinate,f=s>=l-1?r[0].coordinate:r[s+1].coordinate,d=void 0;if((0,p.sA)(c-u)!==(0,p.sA)(f-c)){var h=[];if((0,p.sA)(f-c)===(0,p.sA)(i[1]-i[0])){d=f;var y=c+i[1]-i[0];h[0]=Math.min(y,(y+u)/2),h[1]=Math.max(y,(y+u)/2)}else{d=u;var g=f+i[1]-i[0];h[0]=Math.min(c,(g+c)/2),h[1]=Math.max(c,(g+c)/2)}var v=[Math.min(c,(d+c)/2),Math.max(c,(d+c)/2)];if(e>v[0]&&e<=v[1]||e>=h[0]&&e<=h[1]){({index:o}=r[s]);break}}else{var m=Math.min(u,f),b=Math.max(u,f);if(e>(m+c)/2&&e<=(b+c)/2){({index:o}=r[s]);break}}}else if(t){for(var x=0;x<l;x++)if(0===x&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x>0&&x<l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x===l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2){({index:o}=t[x]);break}}return o},b=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&(0,p.Et)(e[a]))return g(g({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&(0,p.Et)(e[o]))return g(g({},e),{},{[o]:e[o]+(i||0)})}return e},x=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,w=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},O=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:s,categoricalDomain:u,tickCount:c,ticks:f,niceTicks:d,axisType:h}=e;if(!o)return null;var y="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,g=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/y:0;return(g="angleAxis"===h&&a&&a.length>=2?2*(0,p.sA)(a[0]-a[1])*g:g,t&&(f||d))?(f||d||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+g,value:e,offset:g,index:t})).filter(e=>!(0,p.M8)(e.coordinate)):s&&u?u.map((e,t)=>({coordinate:o(e)+g,value:e,index:t,offset:g})):o.ticks&&!r&&null!=c?o.ticks(c).map((e,t)=>({coordinate:o(e)+g,value:e,offset:g,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+g,value:n?n[e]:e,index:t,offset:g}))},P=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}},j=(e,t)=>{if(!t||2!==t.length||!(0,p.Et)(t[0])||!(0,p.Et)(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!(0,p.Et)(e[0])||e[0]<r)&&(i[0]=r),(!(0,p.Et)(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},S={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=(0,p.M8)(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}l(e,t)}},none:l,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,s=0;o<r;++o)s+=e[o][n][1]||0;i[n][1]+=i[n][0]=-s/2}l(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var s=0,u=0,c=0;s<i;++s){for(var f=e[t[s]],d=f[o][1]||0,p=(d-(f[o-1][1]||0))/2,h=0;h<s;++h){var y=e[t[h]];p+=(y[o][1]||0)-(y[o-1][1]||0)}u+=d,c+=p*d}r[o-1][1]+=r[o-1][0]=a,u&&(a-=c/u)}r[o-1][1]+=r[o-1][0]=a,l(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=(0,p.M8)(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},E=(e,t,r)=>{var n=S[r];return(function(){var e=(0,u.A)([]),t=c,r=l,n=f;function i(i){var a,o,l=Array.from(e.apply(this,arguments),d),u=l.length,c=-1;for(let e of i)for(a=0,++c;a<u;++a)(l[a][c]=[0,+n(e,l[a].key,c,i)]).data=e;for(a=0,o=(0,s.A)(t(l));a<u;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,u.A)(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,u.A)(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?c:"function"==typeof e?e:(0,u.A)(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?l:e,i):r},i})().keys(t).value((e,t)=>+v(e,t,0)).order(c).offset(n)(e)};function M(e){return null==e?void 0:String(e)}function A(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!(0,p.uy)(i[t.dataKey])){var l=(0,p.eP)(r,"value",i[t.dataKey]);if(l)return l.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var s=v(i,(0,p.uy)(o)?t.dataKey:o);return(0,p.uy)(s)?null:t.scale(s)}var k=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=v(a,t.dataKey,t.scale.domain()[o]);return(0,p.uy)(l)?null:t.scale(l)-i/2+n},_=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},D=e=>{var t=e.flat(2).filter(p.Et);return[Math.min(...t),Math.max(...t)]},C=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],T=(e,t,r)=>{if(null!=e)return C(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=D(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},N=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,z=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,I=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var a=i()(t,e=>e.coordinate),o=1/0,l=1,s=a.length;l<s;l++){var u=a[l],c=a[l-1];o=Math.min((u.coordinate||0)-(c.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function R(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return g(g({},t),{},{dataKey:r,payload:n,value:i,name:a})}function L(e,t){return e?String(e):"string"==typeof t?t:void 0}function H(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?(0,h.yy)({x:e,y:t},n):null}var $=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return g(g(g({},n),(0,h.IZ)(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:s}=n;return g(g(g({},n),(0,h.IZ)(n.cx,n.cy,l,s)),{},{angle:s,radius:l})}return{x:0,y:0}},B=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},40220:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},41643:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)}},42694:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8287);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},42721:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},43597:(e,t,r)=>{"use strict";r.d(t,{QQ:()=>i,VU:()=>o,XC:()=>c,_U:()=>s,j2:()=>l});var n=r(12115),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],a=["points","pathLength"],o={svg:["viewBox","children"],polygon:a,polyline:a},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],s=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var i={};return Object.keys(r).forEach(e=>{l.includes(e)&&(i[e]=t||(t=>r[e](r,t)))}),i},u=(e,t,r)=>n=>(e(t,r,n),null),c=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];l.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=u(a,t,r))}),n}},44117:(e,t,r)=>{"use strict";var n=r(49641).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(98221),a=r(15160),o=r(42721),l=r(36633),s=r(80885);function u(e,t,r,i=new Map,f){let d=f?.(e,t,r,i);if(null!=d)return d;if(l.isPrimitive(e))return e;if(i.has(e))return i.get(e);if(Array.isArray(e)){let t=Array(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=u(e[n],n,r,i,f);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,a]of(i.set(e,t),e))t.set(n,u(a,n,r,i,f));return t}if(e instanceof Set){let t=new Set;for(let n of(i.set(e,t),e))t.add(u(n,void 0,r,i,f));return t}if(void 0!==n&&n.isBuffer(e))return e.subarray();if(s.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=u(e[n],n,r,i,f);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return i.set(e,t),c(t,e,r,i,f),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return i.set(e,t),c(t,e,r,i,f),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return i.set(e,t),c(t,e,r,i,f),t}if(e instanceof Error){let t=new e.constructor;return i.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,c(t,e,r,i,f),t}if("object"==typeof e&&function(e){switch(a.getTag(e)){case o.argumentsTag:case o.arrayTag:case o.arrayBufferTag:case o.dataViewTag:case o.booleanTag:case o.dateTag:case o.float32ArrayTag:case o.float64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.mapTag:case o.numberTag:case o.objectTag:case o.regexpTag:case o.setTag:case o.stringTag:case o.symbolTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return i.set(e,t),c(t,e,r,i,f),t}return e}function c(e,t,r=e,n,a){let o=[...Object.keys(t),...i.getSymbols(t)];for(let i=0;i<o.length;i++){let l=o[i],s=Object.getOwnPropertyDescriptor(e,l);(null==s||s.writable)&&(e[l]=u(t[l],l,r,n,a))}}t.cloneDeepWith=function(e,t){return u(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=u,t.copyProperties=c},44538:(e,t,r)=>{"use strict";r.d(t,{M:()=>f});var n=r(12115),i=r(52596),a=r(70788),o=r(93389),l=r(74460);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,s=r>=0?1:-1,u=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var c=[0,0,0,0],f=0;f<4;f++)c[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*c[0]),c[0]>0&&(a+="A ".concat(c[0],",").concat(c[0],",0,0,").concat(u,",").concat(e+s*c[0],",").concat(t)),a+="L ".concat(e+r-s*c[1],",").concat(t),c[1]>0&&(a+="A ".concat(c[1],",").concat(c[1],",0,0,").concat(u,",\n        ").concat(e+r,",").concat(t+l*c[1])),a+="L ".concat(e+r,",").concat(t+n-l*c[2]),c[2]>0&&(a+="A ".concat(c[2],",").concat(c[2],",0,0,").concat(u,",\n        ").concat(e+r-s*c[2],",").concat(t+n)),a+="L ".concat(e+s*c[3],",").concat(t+n),c[3]>0&&(a+="A ".concat(c[3],",").concat(c[3],",0,0,").concat(u,",\n        ").concat(e,",").concat(t+n-l*c[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+s*d,",").concat(t,"\n            L ").concat(e+r-s*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+r-s*d,",").concat(t+n,"\n            L ").concat(e+s*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e,",").concat(t+n-l*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},c={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=e=>{var t=(0,o.e)(e,c),r=(0,n.useRef)(null),[f,d]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&d(e)}catch(e){}},[]);var{x:p,y:h,width:y,height:g,radius:v,className:m}=t,{animationEasing:b,animationDuration:x,animationBegin:w,isAnimationActive:O,isUpdateAnimationActive:P}=t;if(p!==+p||h!==+h||y!==+y||g!==+g||0===y||0===g)return null;var j=(0,i.$)("recharts-rectangle",m);return P?n.createElement(l.i,{canBegin:f>0,from:{width:y,height:g,x:p,y:h},to:{width:y,height:g,x:p,y:h},duration:x,animationEasing:b,isActive:P},e=>{var{width:i,height:o,x:c,y:d}=e;return n.createElement(l.i,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,isActive:O,easing:b},n.createElement("path",s({},(0,a.J9)(t,!0),{className:j,d:u(c,d,i,o,v),ref:r})))}):n.createElement("path",s({},(0,a.J9)(t,!0),{className:j,d:u(p,h,y,g,v)}))}},45157:(e,t,r)=>{"use strict";e.exports=function(){throw Error("Don't instantiate Resizable directly! Use require('react-resizable').Resizable")},e.exports.Resizable=r(56335).default,e.exports.ResizableBox=r(38924).default},45643:(e,t,r)=>{"use strict";e.exports=r(6115)},46200:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},46605:(e,t,r)=>{"use strict";r.d(t,{P:()=>u});var n=r(41643);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span",u=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n.m.isSsr)return{width:0,height:0};var i=(Object.keys(t=a({},r)).forEach(e=>{t[e]||delete t[e]}),t),u=JSON.stringify({text:e,copyStyle:i});if(o.widthCache[u])return o.widthCache[u];try{var c=document.getElementById(s);c||((c=document.createElement("span")).setAttribute("id",s),c.setAttribute("aria-hidden","true"),document.body.appendChild(c));var f=a(a({},l),i);Object.assign(c.style,f),c.textContent="".concat(e);var d=c.getBoundingClientRect(),p={width:d.width,height:d.height};return o.widthCache[u]=p,++o.cacheCount>2e3&&(o.cacheCount=0,o.widthCache={}),p}catch(e){return{width:0,height:0}}}},46641:(e,t,r)=>{"use strict";r.d(t,{dl:()=>s,lJ:()=>l,uN:()=>a});var n=r(5710),i=r(16377);function a(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.M8)(r))return null==e?void 0:e[r]}}var o=(0,n.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=o.reducer,{createEventEmitter:s}=o.actions},46670:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var n=e=>e.options.tooltipPayloadSearcher},46850:(e,t,r)=>{"use strict";r.d(t,{l3:()=>v,m7:()=>m});var n=r(12115),i=r(81971),a=r(18478),o=new(r(82661)),l="recharts.syncEvent.tooltip",s="recharts.syncEvent.brush",u=r(46641),c=r(34890),f=r(94732),d=r(20215);function p(e){return e.tooltip.syncInteraction}var h=r(97238),y=r(34487),g=()=>{};function v(){var e,t,r,f,p,v,m,b,x,w,O,P=(0,i.j)();(0,n.useEffect)(()=>{P((0,u.dl)())},[P]),e=(0,i.G)(a.lZ),t=(0,i.G)(a.pH),r=(0,i.j)(),f=(0,i.G)(a.hX),p=(0,i.G)(d.R4),v=(0,h.WX)(),m=(0,h.sk)(),b=(0,i.G)(e=>e.rootProps.className),(0,n.useEffect)(()=>{if(null==e)return g;var n=(n,i,a)=>{if(t!==a&&e===n){if("index"===f)return void r(i);if(null!=p){if("function"==typeof f){var o,l=f(p,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});o=p[l]}else"value"===f&&(o=p.find(e=>String(e.value)===i.payload.label));var{coordinate:s}=i.payload;if(null==o||!1===i.payload.active||null==s||null==m)return void r((0,c.E1)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:u,y:d}=s,h=Math.min(u,m.x+m.width),y=Math.min(d,m.y+m.height),g={x:"horizontal"===v?o.coordinate:h,y:"horizontal"===v?y:o.coordinate};r((0,c.E1)({active:i.payload.active,coordinate:g,dataKey:i.payload.dataKey,index:String(o.index),label:i.payload.label}))}}};return o.on(l,n),()=>{o.off(l,n)}},[b,r,t,e,f,p,v,m]),x=(0,i.G)(a.lZ),w=(0,i.G)(a.pH),O=(0,i.j)(),(0,n.useEffect)(()=>{if(null==x)return g;var e=(e,t,r)=>{w!==r&&x===e&&O((0,y.M)(t))};return o.on(s,e),()=>{o.off(s,e)}},[O,w,x])}function m(e,t,r,s,u,d){var h=(0,i.G)(r=>(0,f.dp)(r,e,t)),y=(0,i.G)(a.pH),g=(0,i.G)(a.lZ),v=(0,i.G)(a.hX),m=(0,i.G)(p),b=null==m?void 0:m.active;(0,n.useEffect)(()=>{if(!b&&null!=g&&null!=y){var e=(0,c.E1)({active:d,coordinate:r,dataKey:h,index:u,label:"number"==typeof s?String(s):s});o.emit(l,g,e,y)}},[b,r,h,u,s,y,g,v,d])}},47062:(e,t,r)=>{"use strict";r.d(t,{Be:()=>g,Cv:()=>O,D0:()=>j,Gl:()=>v,Dc:()=>P});var n=r(68924),i=r(2589),a=r(69449),o=r(25641),l=r(16377),s={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},u={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},c=r(18190),f=r(97238),d={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:s.angleAxisId,includeHidden:!1,name:void 0,reversed:s.reversed,scale:s.scale,tick:s.tick,tickCount:void 0,ticks:void 0,type:s.type,unit:void 0},p={allowDataOverflow:u.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:u.tickCount,ticks:void 0,type:u.type,unit:void 0},h={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:s.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:s.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:s.scale,tick:s.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:u.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:u.tickCount,ticks:void 0,type:"category",unit:void 0},g=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?h:d,v=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?y:p,m=e=>e.polarOptions,b=(0,n.Mz)([i.Lp,i.A$,a.GO],o.lY),x=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.innerRadius,t,0)}),w=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.outerRadius,t,.8*t)}),O=(0,n.Mz)([m],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,n.Mz)([g,O],c.I);var P=(0,n.Mz)([b,x,w],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,n.Mz)([v,P],c.I);var j=(0,n.Mz)([f.fz,m,x,w,i.Lp,i.A$],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:s,startAngle:u,endAngle:c}=t;return{cx:(0,l.F4)(o,i,i/2),cy:(0,l.F4)(s,a,a/2),innerRadius:r,outerRadius:n,startAngle:u,endAngle:c,clockWise:!1}}})},47064:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(55181),i=r(51551),a=r(64072);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},s=(e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t,u=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:u.map(t=>s(t,e))})).slice().sort((e,t)=>{for(let i=0;i<u.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},47127:()=>{},48407:e=>{e.exports=function(e,t,r){return e===t||e.className===t.className&&r(e.style,t.style)&&e.width===t.width&&e.autoSize===t.autoSize&&e.cols===t.cols&&e.draggableCancel===t.draggableCancel&&e.draggableHandle===t.draggableHandle&&r(e.verticalCompact,t.verticalCompact)&&r(e.compactType,t.compactType)&&r(e.layout,t.layout)&&r(e.margin,t.margin)&&r(e.containerPadding,t.containerPadding)&&e.rowHeight===t.rowHeight&&e.maxRows===t.maxRows&&e.isBounded===t.isBounded&&e.isDraggable===t.isDraggable&&e.isResizable===t.isResizable&&e.allowOverlap===t.allowOverlap&&e.preventCollision===t.preventCollision&&e.useCSSTransforms===t.useCSSTransforms&&e.transformScale===t.transformScale&&e.isDroppable===t.isDroppable&&r(e.resizeHandles,t.resizeHandles)&&r(e.resizeHandle,t.resizeHandle)&&e.onLayoutChange===t.onLayoutChange&&e.onDragStart===t.onDragStart&&e.onDrag===t.onDrag&&e.onDragStop===t.onDragStop&&e.onResizeStart===t.onResizeStart&&e.onResize===t.onResize&&e.onResizeStop===t.onResizeStop&&e.onDrop===t.onDrop&&r(e.droppingItem,t.droppingItem)&&r(e.innerRef,t.innerRef)}},48605:(e,t,r)=>{"use strict";r.d(t,{y:()=>Y});var n=r(12115),i=r(80931),a=r.n(i),o=r(44538),l=r(52596),s=r(70788),u=r(93389),c=r(74460);function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var d=(e,t,r,n,i)=>{var a,o=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-o/2,",").concat(t+i)+"L ".concat(e+r-o/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},p={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},h=e=>{var t=(0,u.e)(e,p),r=(0,n.useRef)(),[i,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(e){}},[]);var{x:o,y:h,upperWidth:y,lowerWidth:g,height:v,className:m}=t,{animationEasing:b,animationDuration:x,animationBegin:w,isUpdateAnimationActive:O}=t;if(o!==+o||h!==+h||y!==+y||g!==+g||v!==+v||0===y&&0===g||0===v)return null;var P=(0,l.$)("recharts-trapezoid",m);return O?n.createElement(c.i,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:v,x:o,y:h},to:{upperWidth:y,lowerWidth:g,height:v,x:o,y:h},duration:x,animationEasing:b,isActive:O},e=>{var{upperWidth:a,lowerWidth:o,height:l,x:u,y:p}=e;return n.createElement(c.i,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,easing:b},n.createElement("path",f({},(0,s.J9)(t,!0),{className:P,d:d(u,p,a,o,l),ref:r})))}):n.createElement("g",null,n.createElement("path",f({},(0,s.J9)(t,!0),{className:P,d:d(o,h,y,g,v)})))},y=r(77283),g=r(2348);let v=Math.cos,m=Math.sin,b=Math.sqrt,x=Math.PI,w=2*x,O={draw(e,t){let r=b(t/x);e.moveTo(r,0),e.arc(0,0,r,0,w)}},P=b(1/3),j=2*P,S=m(x/10)/m(7*x/10),E=m(w/10)*S,M=-v(w/10)*S,A=b(3),k=b(3)/2,_=1/b(12),D=(_/2+1)*3;var C=r(85654),T=r(31847);b(3),b(3);var N=r(16377),z=["type","size","sizeType"];function I(){return(I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function R(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function L(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?R(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var H={symbolCircle:O,symbolCross:{draw(e,t){let r=b(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=b(t/j),n=r*P;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=b(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=b(.8908130915292852*t),n=E*r,i=M*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=w*t/5,o=v(a),l=m(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-b(t/(3*A));e.moveTo(0,2*r),e.lineTo(-A*r,-r),e.lineTo(A*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=b(t/D),n=r/2,i=r*_,a=r*_+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-k*i,k*n+-.5*i),e.lineTo(-.5*n-k*a,k*n+-.5*a),e.lineTo(-.5*o-k*a,k*o+-.5*a),e.lineTo(-.5*n+k*i,-.5*i-k*n),e.lineTo(-.5*n+k*a,-.5*a-k*n),e.lineTo(-.5*o+k*a,-.5*a-k*o),e.closePath()}}},$=Math.PI/180,B=e=>H["symbol".concat((0,N.Zb)(e))]||O,W=(e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*$;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},U=e=>{var{type:t="circle",size:r=64,sizeType:i="area"}=e,a=L(L({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,z)),{},{type:t,size:r,sizeType:i}),{className:o,cx:u,cy:c}=a,f=(0,s.J9)(a,!0);return u===+u&&c===+c&&r===+r?n.createElement("path",I({},f,{className:(0,l.$)("recharts-symbols",o),transform:"translate(".concat(u,", ").concat(c,")"),d:(()=>{var e=B(t);return(function(e,t){let r=null,n=(0,T.i)(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:(0,C.A)(e||O),t="function"==typeof t?t:(0,C.A)(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,C.A)(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,C.A)(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(W(r,i,t))()})()})):null};U.registerSymbol=(e,t)=>{H["symbol".concat((0,N.Zb)(e))]=t};var F=["option","shapeType","propTransformer","activeClassName","isActive"];function K(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function G(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?K(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):K(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function q(e,t){return G(G({},t),e)}function V(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(o.M,r);case"trapezoid":return n.createElement(h,r);case"sector":return n.createElement(y.h,r);case"symbols":if("symbols"===t)return n.createElement(U,r);break;default:return null}}function Y(e){var t,{option:r,shapeType:i,propTransformer:o=q,activeClassName:l="recharts-active-shape",isActive:s}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,F);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,G(G({},u),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(u);else if(a()(r)&&"boolean"!=typeof r){var c=o(r,u);t=n.createElement(V,{shapeType:i,elementProps:c})}else t=n.createElement(V,{shapeType:i,elementProps:u});return s?n.createElement(g.W,{className:l},t):t}},48627:(e,t,r)=>{"use strict";r.d(t,{B_:()=>i,JK:()=>a,Vp:()=>s,gX:()=>o,hF:()=>l});var n=(0,r(5710).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:i,setLayout:a,setChartSize:o,setScale:l}=n.actions,s=n.reducer},49033:(e,t,r)=>{"use strict";e.exports=r(22436)},49901:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(64373),i=r(64664);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},49972:(e,t,r)=>{"use strict";r.d(t,{J:()=>ed});var n=r(12115);r(39611);var i=Symbol.for("react.forward_ref"),a=Symbol.for("react.memo");function o(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}var l={notify(){},get:()=>[]},s="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,u="undefined"!=typeof navigator&&"ReactNative"===navigator.product,c=s||u?n.useLayoutEffect:n.useEffect;function f(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}var d={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},p={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},h={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},y={[i]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[a]:h};function g(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case null:switch(e=e.type){case null:case null:case null:case null:case null:return e;default:switch(e=e&&e.$$typeof){case null:case i:case null:case a:case null:return e;default:return t}}case null:return t}}}(e)===a?h:y[e.$$typeof]||d}var v=Object.defineProperty,m=Object.getOwnPropertyNames,b=Object.getOwnPropertySymbols,x=Object.getOwnPropertyDescriptor,w=Object.getPrototypeOf,O=Object.prototype,P=Symbol.for("react-redux-context"),j="undefined"!=typeof globalThis?globalThis:{},S=function(){if(!n.createContext)return{};let e=j[P]??=new Map,t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}(),E=function(e){let{children:t,context:r,serverState:i,store:a}=e,o=n.useMemo(()=>{let e=function(e,t){let r,n=l,i=0,a=!1;function o(){c.onStateChange&&c.onStateChange()}function s(){if(i++,!r){let t,i;r=e.subscribe(o),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function u(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=l)}let c={addNestedSub:function(e){s();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),u())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,s())},tryUnsubscribe:function(){a&&(a=!1,u())},getListeners:()=>n};return c}(a);return{store:a,subscription:e,getServerState:i?()=>i:void 0}},[a,i]),s=n.useMemo(()=>a.getState(),[a]);return c(()=>{let{subscription:e}=o;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),s!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[o,s]),n.createElement((r||S).Provider,{value:o},t)},M=r(52),A=r(5710),k=r(46641),_=r(34890),D=r(34487),C=r(48627),T=r(94685);function N(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var z=r(55306),I=r(22248),R=r(74532),L=(0,A.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,R.ss)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,R.ss)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,R.ss)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:H,removeDot:$,addArea:B,removeArea:W,addLine:U,removeLine:F}=L.actions,K=L.reducer,G={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},q=(0,A.Z0)({name:"brush",initialState:G,reducers:{setBrushSettings:(e,t)=>null==t.payload?G:t.payload}}),{setBrushSettings:V}=q.actions,Y=q.reducer,Z=r(32634),X=r(60429),J=(0,A.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,R.h4)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,R.h4)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:Q,removeRadiusAxis:ee,addAngleAxis:et,removeAngleAxis:er}=J.actions,en=J.reducer,ei=r(2267),ea=r(841),eo=r(37335),el=r(36144),es=(0,M.HY)({brush:Y,cartesianAxis:z.CA,chartData:D.LV,graphicalItems:I.iZ,layout:C.Vp,legend:Z.CU,options:k.lJ,polarAxis:en,polarOptions:ei.J,referenceElements:K,rootProps:X.vE,tooltip:_.En}),eu=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,A.U1)({reducer:es,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([T.YF.middleware,T.fP.middleware,ea.$7.middleware,eo.x.middleware,el.k.middleware]),devTools:{serialize:{replacer:N},name:"recharts-".concat(t)}})},ec=r(71807),ef=r(15064);function ed(e){var{preloadedState:t,children:r,reduxStoreName:i}=e,a=(0,ec.r)(),o=(0,n.useRef)(null);if(a)return r;null==o.current&&(o.current=eu(t,i));var l=ef.E;return n.createElement(E,{context:l,store:o.current},r)}},50177:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(15160);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},50507:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.bottom=a,t.childrenEqual=function(e,t){return(0,n.deepEqual)(i.default.Children.map(e,e=>e?.key),i.default.Children.map(t,e=>e?.key))&&(0,n.deepEqual)(i.default.Children.map(e,e=>e?.props["data-grid"]),i.default.Children.map(t,e=>e?.props["data-grid"]))},t.cloneLayout=o,t.cloneLayoutItem=s,t.collides=u,t.compact=c,t.compactItem=p,t.compactType=function(e){let{verticalCompact:t,compactType:r}=e||{};return!1===t?null:r},t.correctBounds=h,t.fastPositionEqual=function(e,t){return e.left===t.left&&e.top===t.top&&e.width===t.width&&e.height===t.height},t.fastRGLPropsEqual=void 0,t.getAllCollisions=v,t.getFirstCollision=g,t.getLayoutItem=y,t.getStatics=m,t.modifyLayout=l,t.moveElement=b,t.moveElementAwayFromCollision=x,t.noop=void 0,t.perc=function(e){return 100*e+"%"},t.resizeItemInDirection=function(e,t,r,n){let i=k[e];return i?i(t,{...t,...r},n):r},t.setTopLeft=function(e){let{top:t,left:r,width:n,height:i}=e;return{top:`${t}px`,left:`${r}px`,width:`${n}px`,height:`${i}px`,position:"absolute"}},t.setTransform=function(e){let{top:t,left:r,width:n,height:i}=e,a=`translate(${r}px,${t}px)`;return{transform:a,WebkitTransform:a,MozTransform:a,msTransform:a,OTransform:a,width:`${n}px`,height:`${i}px`,position:"absolute"}},t.sortLayoutItems=_,t.sortLayoutItemsByColRow=C,t.sortLayoutItemsByRowCol=D,t.synchronizeLayoutWithChildren=function(e,t,r,n,o){e=e||[];let l=[];i.default.Children.forEach(t,t=>{if(t?.key==null)return;let r=y(e,String(t.key)),n=t.props["data-grid"];r&&null==n?l.push(s(r)):n?l.push(s({...n,i:t.key})):l.push(s({w:1,h:1,x:0,y:a(l),i:String(t.key)}))});let u=h(l,{cols:r});return o?u:c(u,n,r)},t.validateLayout=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Layout",r=["x","y","w","h"];if(!Array.isArray(e))throw Error(t+" must be an array!");for(let n=0,i=e.length;n<i;n++){let i=e[n];for(let e=0;e<r.length;e++){let a=r[e],o=i[a];if("number"!=typeof o||Number.isNaN(o))throw Error(`ReactGridLayout: ${t}[${n}].${a} must be a number! Received: ${o} (${typeof o})`)}if(void 0!==i.i&&"string"!=typeof i.i)throw Error(`ReactGridLayout: ${t}[${n}].i must be a string! Received: ${i.i} (${typeof i.i})`)}},t.withLayoutItem=function(e,t,r){let n=y(e,t);return n?[e=l(e,n=r(s(n))),n]:[e,null]};var n=r(76725),i=function(e){return e&&e.__esModule?e:{default:e}}(r(12115));function a(e){let t=0,r;for(let n=0,i=e.length;n<i;n++)(r=e[n].y+e[n].h)>t&&(t=r);return t}function o(e){let t=Array(e.length);for(let r=0,n=e.length;r<n;r++)t[r]=s(e[r]);return t}function l(e,t){let r=Array(e.length);for(let n=0,i=e.length;n<i;n++)t.i===e[n].i?r[n]=t:r[n]=e[n];return r}function s(e){return{w:e.w,h:e.h,x:e.x,y:e.y,i:e.i,minW:e.minW,maxW:e.maxW,minH:e.minH,maxH:e.maxH,moved:!!e.moved,static:!!e.static,isDraggable:e.isDraggable,isResizable:e.isResizable,resizeHandles:e.resizeHandles,isBounded:e.isBounded}}function u(e,t){return e.i!==t.i&&!(e.x+e.w<=t.x)&&!(e.x>=t.x+t.w)&&!(e.y+e.h<=t.y)&&!(e.y>=t.y+t.h)}function c(e,t,r,n){let i=m(e),a=_(e,t),o=Array(e.length);for(let l=0,u=a.length;l<u;l++){let u=s(a[l]);u.static||(u=p(i,u,t,r,a,n),i.push(u)),o[e.indexOf(a[l])]=u,u.moved=!1}return o}t.fastRGLPropsEqual=r(48407);let f={x:"w",y:"h"};function d(e,t,r,n){let i=f[n];t[n]+=1;let a=e.map(e=>e.i).indexOf(t.i);for(let o=a+1;o<e.length;o++){let a=e[o];if(!a.static){if(a.y>t.y+t.h)break;u(t,a)&&d(e,a,r+t[i],n)}}t[n]=r}function p(e,t,r,n,i,o){let l,s="horizontal"===r;if("vertical"===r)for(t.y=Math.min(a(e),t.y);t.y>0&&!g(e,t);)t.y--;else if(s)for(;t.x>0&&!g(e,t);)t.x--;for(;(l=g(e,t))&&!(null===r&&o);)if(s?d(i,t,l.x+l.w,"x"):d(i,t,l.y+l.h,"y"),s&&t.x+t.w>n)for(t.x=n-t.w,t.y++;t.x>0&&!g(e,t);)t.x--;return t.y=Math.max(t.y,0),t.x=Math.max(t.x,0),t}function h(e,t){let r=m(e);for(let n=0,i=e.length;n<i;n++){let i=e[n];if(i.x+i.w>t.cols&&(i.x=t.cols-i.w),i.x<0&&(i.x=0,i.w=t.cols),i.static)for(;g(r,i);)i.y++;else r.push(i)}return e}function y(e,t){for(let r=0,n=e.length;r<n;r++)if(e[r].i===t)return e[r]}function g(e,t){for(let r=0,n=e.length;r<n;r++)if(u(e[r],t))return e[r]}function v(e,t){return e.filter(e=>u(e,t))}function m(e){return e.filter(e=>e.static)}function b(e,t,r,n,i,a,l,s,u){if(t.static&&!0!==t.isDraggable||t.y===n&&t.x===r)return e;t.i,String(r),String(n),t.x,t.y;let c=t.x,f=t.y;"number"==typeof r&&(t.x=r),"number"==typeof n&&(t.y=n),t.moved=!0;let d=_(e,l);("vertical"===l&&"number"==typeof n?f>=n:"horizontal"===l&&"number"==typeof r&&c>=r)&&(d=d.reverse());let p=v(d,t),h=p.length>0;if(h&&u)return o(e);if(h&&a)return T(`Collision prevented on ${t.i}, reverting.`),t.x=c,t.y=f,t.moved=!1,e;for(let r=0,n=p.length;r<n;r++){let n=p[r];t.i,t.x,t.y,n.i,n.x,n.y,n.moved||(e=n.static?x(e,n,t,i,l,s):x(e,t,n,i,l,s))}return e}function x(e,t,r,n,i,a){let o="horizontal"===i,l="vertical"===i,s=t.static;if(n){n=!1;let u={x:o?Math.max(t.x-r.w,0):r.x,y:l?Math.max(t.y-r.h,0):r.y,w:r.w,h:r.h,i:"-1"},c=g(e,u),f=c&&c.y+c.h>t.y,d=c&&t.x+t.w>c.x;if(!c)return T(`Doing reverse collision on ${r.i} up to [${u.x},${u.y}].`),b(e,r,o?u.x:void 0,l?u.y:void 0,n,s,i,a);if(f&&l)return b(e,r,void 0,t.y+1,n,s,i,a);if(f&&null==i)return t.y=r.y,r.y=r.y+r.h,e;else if(d&&o)return b(e,t,r.x,void 0,n,s,i,a)}let u=o?r.x+1:void 0,c=l?r.y+1:void 0;return null==u&&null==c?e:b(e,r,o?r.x+1:void 0,l?r.y+1:void 0,n,s,i,a)}let w=(e,t,r,n)=>e+r>n?t:r,O=(e,t,r)=>e<0?t:r,P=e=>Math.max(0,e),j=e=>Math.max(0,e),S=(e,t,r)=>{let{left:n,height:i,width:a}=t,o=e.top-(i-e.height);return{left:n,width:a,height:O(o,e.height,i),top:j(o)}},E=(e,t,r)=>{let{top:n,left:i,height:a,width:o}=t;return{top:n,height:a,width:w(e.left,e.width,o,r),left:P(i)}},M=(e,t,r)=>{let{top:n,height:i,width:a}=t,o=e.left-(a-e.width);return{height:i,width:o<0?e.width:w(e.left,e.width,a,r),top:j(n),left:P(o)}},A=(e,t,r)=>{let{top:n,left:i,height:a,width:o}=t;return{width:o,left:i,height:O(n,e.height,a),top:j(n)}},k={n:S,ne:function(){return S(arguments.length<=0?void 0:arguments[0],E(...arguments),arguments.length<=2?void 0:arguments[2])},e:E,se:function(){return A(arguments.length<=0?void 0:arguments[0],E(...arguments),arguments.length<=2?void 0:arguments[2])},s:A,sw:function(){return A(arguments.length<=0?void 0:arguments[0],M(...arguments),arguments.length<=2?void 0:arguments[2])},w:M,nw:function(){return S(arguments.length<=0?void 0:arguments[0],M(...arguments),arguments.length<=2?void 0:arguments[2])}};function _(e,t){return"horizontal"===t?C(e):"vertical"===t?D(e):e}function D(e){return e.slice(0).sort(function(e,t){return e.y>t.y||e.y===t.y&&e.x>t.x?1:e.y===t.y&&e.x===t.x?0:-1})}function C(e){return e.slice(0).sort(function(e,t){return e.x>t.x||e.x===t.x&&e.y>t.y?1:-1})}function T(){}t.noop=()=>{}},51172:(e,t,r)=>{"use strict";r.d(t,{c:()=>s});var n=r(12115),i=r(52596),a=r(43597),o=r(70788);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=e=>{var{cx:t,cy:r,r:s,className:u}=e,c=(0,i.$)("recharts-dot",u);return t===+t&&r===+r&&s===+s?n.createElement("circle",l({},(0,o.J9)(e,!1),(0,a._U)(e),{className:c,cx:t,cy:r,r:s})):null}},51551:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8287),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},52071:(e,t,r)=>{"use strict";r.d(t,{h:()=>x});var n=r(12115),i=r(52596),a=r(79584),o=r(55306),l=r(81971),s=r(14299),u=r(69449),c=r(71807),f=e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0},d=r(60379),p=["dangerouslySetInnerHTML","ticks"];function h(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function g(e){var t=(0,l.j)();return(0,n.useEffect)(()=>(t((0,o.cU)(e)),()=>{t((0,o.fR)(e))}),[e,t]),null}var v=e=>{var t,{yAxisId:r,className:h,width:g,label:v}=e,m=(0,n.useRef)(null),b=(0,n.useRef)(null),x=(0,l.G)(u.c2),w=(0,c.r)(),O=(0,l.j)(),P="yAxis",j=(0,l.G)(e=>(0,s.iV)(e,P,r,w)),S=(0,l.G)(e=>(0,s.wP)(e,r)),E=(0,l.G)(e=>(0,s.KR)(e,r)),M=(0,l.G)(e=>(0,s.Zi)(e,P,r,w));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==g||!S||(0,d.Z)(v)||(0,n.isValidElement)(v))){var e,t=m.current,i=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:a,tickMargin:l}=t.props,s=f({ticks:i,label:b.current,labelGapWithTick:5,tickSize:a,tickMargin:l});Math.round(S.width)!==Math.round(s)&&O((0,o.QG)({id:r,width:s}))}},[m,null==m||null==(t=m.current)||null==(t=t.tickRefs)?void 0:t.current,null==S?void 0:S.width,S,O,v,r,g]),null==S||null==E)return null;var{dangerouslySetInnerHTML:A,ticks:k}=e,_=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,p);return n.createElement(a.u,y({},_,{ref:m,labelRef:b,scale:j,x:E.x,y:E.y,width:S.width,height:S.height,className:(0,i.$)("recharts-".concat(P," ").concat(P),h),viewBox:x,ticks:M}))},m=e=>{var t,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(g,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter}),n.createElement(v,e))},b={allowDataOverflow:s.cd.allowDataOverflow,allowDecimals:s.cd.allowDecimals,allowDuplicatedCategory:s.cd.allowDuplicatedCategory,hide:!1,mirror:s.cd.mirror,orientation:s.cd.orientation,padding:s.cd.padding,reversed:s.cd.reversed,scale:s.cd.scale,tickCount:s.cd.tickCount,type:s.cd.type,width:s.cd.width,yAxisId:0};class x extends n.Component{render(){return n.createElement(m,this.props)}}h(x,"displayName","YAxis"),h(x,"defaultProps",b)},53588:(e,t)=>{"use strict";var r=Symbol.for("react.transitional.element"),n=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler");Symbol.for("react.provider");var l=Symbol.for("react.consumer"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),h=Symbol.for("react.view_transition");Symbol.for("react.client.reference");t.zv=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case i:case o:case a:case c:case f:case h:return e;default:switch(e=e&&e.$$typeof){case s:case u:case p:case d:case l:return e;default:return t}}case n:return t}}}(e)===i}},54416:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},54528:(e,t,r)=>{"use strict";t.default=function(e){var t;return t=class extends n.Component{constructor(){super(...arguments),c(this,"state",{width:1280}),c(this,"elementRef",n.createRef()),c(this,"mounted",!1),c(this,"resizeObserver",void 0)}componentDidMount(){this.mounted=!0,this.resizeObserver=new a.default(e=>{if(this.elementRef.current instanceof HTMLElement){let t=e[0].contentRect.width;this.setState({width:t})}});let e=this.elementRef.current;e instanceof HTMLElement&&this.resizeObserver.observe(e)}componentWillUnmount(){this.mounted=!1;let e=this.elementRef.current;e instanceof HTMLElement&&this.resizeObserver.unobserve(e),this.resizeObserver.disconnect()}render(){let{measureBeforeMount:t,...r}=this.props;return t&&!this.mounted?n.createElement("div",{className:(0,o.default)(this.props.className,"react-grid-layout"),style:this.props.style,ref:this.elementRef}):n.createElement(e,u({innerRef:this.elementRef},r,this.state))}},c(t,"defaultProps",{measureBeforeMount:!1}),c(t,"propTypes",{measureBeforeMount:i.default.bool}),t};var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(n,a,o):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(12115)),i=l(r(38637)),a=l(r(5102)),o=l(r(20749));function l(e){return e&&e.__esModule?e:{default:e}}function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function c(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:String(n))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}},54811:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var n=e=>null;n.displayName="Cell"},55181:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},55306:(e,t,r)=>{"use strict";r.d(t,{CA:()=>y,MC:()=>u,QG:()=>h,Vi:()=>s,cU:()=>c,fR:()=>f});var n=r(5710),i=r(74532);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(0,n.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,i.h4)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,i.h4)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,i.h4)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=o(o({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:s,removeXAxis:u,addYAxis:c,removeYAxis:f,addZAxis:d,removeZAxis:p,updateYAxisWidth:h}=l.actions,y=l.reducer},55379:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.browserPrefixToKey=i,t.browserPrefixToStyle=function(e,t){return t?"-".concat(t.toLowerCase(),"-").concat(e):e},t.default=void 0,t.getPrefix=n;let r=["Moz","Webkit","O","ms"];function n(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"transform";if("undefined"==typeof window)return"";let n=null==(e=window.document)||null==(e=e.documentElement)?void 0:e.style;if(!n||t in n)return"";for(let e=0;e<r.length;e++)if(i(t,r[e])in n)return r[e];return""}function i(e,t){return t?"".concat(t).concat(function(e){let t="",r=!0;for(let n=0;n<e.length;n++)r?(t+=e[n].toUpperCase(),r=!1):"-"===e[n]?r=!0:t+=e[n];return t}(e)):e}t.default=n()},55998:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},56091:(e,t,r)=>{"use strict";r.d(t,{r:()=>l});var n=r(12115),i=r(81971),a=r(34890),o=r(71807);function l(e){var{fn:t,args:r}=e,l=(0,i.j)(),s=(0,o.r)();return(0,n.useEffect)(()=>{if(!s){var e=t(r);return l((0,a.Ix)(e)),()=>{l((0,a.XB)(e))}}},[t,r,l,s]),null}},56287:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]])},56335:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(n,a,o):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(12115)),i=r(5939),a=r(84895),o=r(31e3),l=["children","className","draggableOpts","width","height","handle","handleSize","lockAspectRatio","axis","minConstraints","maxConstraints","onResize","onResizeStop","onResizeStart","resizeHandles","transformScale"];function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e,t){return(d=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var p=function(e){function t(){for(var t,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return(t=e.call.apply(e,[this].concat(n))||this).handleRefs={},t.lastHandleRect=null,t.slack=null,t}t.prototype=Object.create(e.prototype),t.prototype.constructor=t,d(t,e);var r=t.prototype;return r.componentWillUnmount=function(){this.resetData()},r.resetData=function(){this.lastHandleRect=this.slack=null},r.runConstraints=function(e,t){var r=this.props,n=r.minConstraints,i=r.maxConstraints,a=r.lockAspectRatio;if(!n&&!i&&!a)return[e,t];if(a){var o=this.props.width/this.props.height;Math.abs(e-this.props.width)>Math.abs((t-this.props.height)*o)?t=e/o:e=t*o}var l=e,s=t,u=this.slack||[0,0],c=u[0],f=u[1];return e+=c,t+=f,n&&(e=Math.max(n[0],e),t=Math.max(n[1],t)),i&&(e=Math.min(i[0],e),t=Math.min(i[1],t)),this.slack=[c+(l-e),f+(s-t)],[e,t]},r.resizeHandler=function(e,t){var r=this;return function(n,i){var a=i.node,o=i.deltaX,l=i.deltaY;"onResizeStart"===e&&r.resetData();var s=("both"===r.props.axis||"x"===r.props.axis)&&"n"!==t&&"s"!==t,u=("both"===r.props.axis||"y"===r.props.axis)&&"e"!==t&&"w"!==t;if(s||u){var c=t[0],f=t[t.length-1],d=a.getBoundingClientRect();null!=r.lastHandleRect&&("w"===f&&(o+=d.left-r.lastHandleRect.left),"n"===c&&(l+=d.top-r.lastHandleRect.top)),r.lastHandleRect=d,"w"===f&&(o=-o),"n"===c&&(l=-l);var p=r.props.width+(s?o/r.props.transformScale:0),h=r.props.height+(u?l/r.props.transformScale:0),y=r.runConstraints(p,h);p=y[0],h=y[1];var g=p!==r.props.width||h!==r.props.height,v="function"==typeof r.props[e]?r.props[e]:null;v&&!("onResize"===e&&!g)&&(null==n.persist||n.persist(),v(n,{node:a,size:{width:p,height:h},handle:t})),"onResizeStop"===e&&r.resetData()}}},r.renderResizeHandle=function(e,t){var r=this.props.handle;if(!r)return n.createElement("span",{className:"react-resizable-handle react-resizable-handle-"+e,ref:t});if("function"==typeof r)return r(e,t);var i=f({ref:t},"string"==typeof r.type?{}:{handleAxis:e});return n.cloneElement(r,i)},r.render=function(){var e=this,t=this.props,r=t.children,o=t.className,s=t.draggableOpts,c=(t.width,t.height,t.handle,t.handleSize,t.lockAspectRatio,t.axis,t.minConstraints,t.maxConstraints,t.onResize,t.onResizeStop,t.onResizeStart,t.resizeHandles),d=(t.transformScale,function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(t,l));return(0,a.cloneElement)(r,f(f({},d),{},{className:(o?o+" ":"")+"react-resizable",children:[].concat(r.props.children,c.map(function(t){var r,a=null!=(r=e.handleRefs[t])?r:e.handleRefs[t]=n.createRef();return n.createElement(i.DraggableCore,u({},s,{nodeRef:a,key:"resizableHandle-"+t,onStop:e.resizeHandler("onResizeStop",t),onStart:e.resizeHandler("onResizeStart",t),onDrag:e.resizeHandler("onResize",t)}),e.renderResizeHandle(t,a))}))}))},t}(n.Component);t.default=p,p.propTypes=o.resizableProps,p.defaultProps={axis:"both",handleSize:[20,20],lockAspectRatio:!1,minConstraints:[20,20],maxConstraints:[1/0,1/0],resizeHandles:["se"],transformScale:1}},56690:(e,t,r)=>{"use strict";r.d(t,{y:()=>ex,L:()=>eb});var n=r(12115),i=r(52596),a=r(2348),o=r(11808),l=r(54811),s=r(36079),u=r(16377),c=r(70788),f=r(41643),d=r(39827),p=r(43597),h=r(48605),y=["x","y"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y),a=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(t.height||i.height),10),s=parseInt("".concat(t.width||i.width),10);return m(m(m(m(m({},t),i),a?{x:a}:{}),o?{y:o}:{}),{},{height:l,width:s,name:t.name,radius:t.radius})}function x(e){return n.createElement(h.y,g({shapeType:"rectangle",propTransformer:b,activeClassName:"recharts-active-bar"},e))}var w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,u.Et)(e))return e;var i=(0,u.Et)(r)||(0,u.uy)(r);return i?e(r,n):(i||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}},O=r(99129),P=r(56091),j=r(81971),S=r(22248),E=()=>{var e=(0,j.j)();return(0,n.useEffect)(()=>(e((0,S.lm)()),()=>{e((0,S.Ch)())})),null},M=r(39226),A=r(37195),k=r(97238),_=r(68924),D=r(14299),C=r(60356),T=r(69449),N=r(18478),z=r(78892);function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L=(e,t,r,n,i)=>i,H=(e,t,r)=>{var n=null!=r?r:e;if(!(0,u.uy)(n))return(0,u.F4)(n,t,0)},$=(0,_.Mz)([k.fz,D.ld,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function B(e){return null!=e.stackId&&null!=e.dataKey}var W=(0,_.Mz)([$,N.x3,(e,t,r)=>"horizontal"===(0,k.fz)(e)?(0,D.BQ)(e,"xAxis",t):(0,D.BQ)(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(B),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:H(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:H(t,r,e.barSize)}))]}),U=(e,t,r,n)=>{var i,a;return"horizontal"===(0,k.fz)(e)?(i=(0,D.Gx)(e,"xAxis",t,n),a=(0,D.CR)(e,"xAxis",t,n)):(i=(0,D.Gx)(e,"yAxis",r,n),a=(0,D.CR)(e,"yAxis",r,n)),(0,d.Hj)(i,a)},F=(0,_.Mz)([W,N.JN,N._5,N.gY,(e,t,r,n,i)=>{var a,o,l,s,c=(0,k.fz)(e),f=(0,N.JN)(e),{maxBarSize:p}=i,h=(0,u.uy)(p)?f:p;return"horizontal"===c?(l=(0,D.Gx)(e,"xAxis",t,n),s=(0,D.CR)(e,"xAxis",t,n)):(l=(0,D.Gx)(e,"yAxis",r,n),s=(0,D.CR)(e,"yAxis",r,n)),null!=(a=null!=(o=(0,d.Hj)(l,s,!0))?o:h)?a:0},U,(e,t,r,n,i)=>i.maxBarSize],(e,t,r,n,i,a,o)=>{var l=function(e,t,r,n,i){var a,o=n.length;if(!(o<1)){var l=(0,u.F4)(e,r,0,!0),s=[];if((0,z.H)(n[0].barSize)){var c=!1,f=r/o,d=n.reduce((e,t)=>e+(t.barSize||0),0);(d+=(o-1)*l)>=r&&(d-=(o-1)*l,l=0),d>=r&&f>0&&(c=!0,f*=.9,d=o*f);var p={offset:((r-d)/2|0)-l,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p.offset+p.size+l,size:c?f:null!=(r=t.barSize)?r:0}}];return p=n[n.length-1].position,n},s)}else{var h=(0,u.F4)(t,r,0,!0);r-2*h-(o-1)*l<=0&&(l=0);var y=(r-2*h-(o-1)*l)/o;y>1&&(y>>=0);var g=(0,z.H)(i)?Math.min(y,i):y;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:h+(y+l)*r+(y-g)/2,size:g}}],s)}return a}}(r,n,i!==a?i:a,e,(0,u.uy)(o)?t:o);return i!==a&&null!=l&&(l=l.map(e=>R(R({},e),{},{position:R(R({},e.position),{},{offset:e.position.offset-i/2})}))),l}),K=(0,_.Mz)([F,L],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),G=(0,_.Mz)([D.ld,L],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),q=(0,_.Mz)([(e,t,r,n)=>"horizontal"===(0,k.fz)(e)?(0,D.TC)(e,"yAxis",r,n):(0,D.TC)(e,"xAxis",t,n),L],(e,t)=>{if(!e||(null==t?void 0:t.dataKey)==null)return;var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:i}=n;if(i)return i.find(e=>e.key===t.dataKey)}}}),V=(0,_.Mz)([T.GO,(e,t,r,n)=>(0,D.Gx)(e,"xAxis",t,n),(e,t,r,n)=>(0,D.Gx)(e,"yAxis",r,n),(e,t,r,n)=>(0,D.CR)(e,"xAxis",t,n),(e,t,r,n)=>(0,D.CR)(e,"yAxis",r,n),K,k.fz,C.HS,U,q,G,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l,s,u,c,f)=>{var d,{chartData:p,dataStartIndex:h,dataEndIndex:y}=l;if(null!=c&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=s){var{data:g}=c;if(null!=(d=null!=g&&g.length>0?g:null==p?void 0:p.slice(h,y+1)))return eb({layout:o,barSettings:c,pos:a,bandSize:s,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:u,dataStartIndex:h,displayedData:d,offset:e,cells:f})}}),Y=r(71807),Z=r(20215),X=r(79020),J=r(39426),Q=r(93389),ee=r(74460),et=["onMouseEnter","onMouseLeave","onClick"],er=["value","background","tooltipPosition"],en=["onMouseEnter","onClick","onMouseLeave"];function ei(){return(ei=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ea(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eo(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ea(Object(r),!0).forEach(function(t){el(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ea(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function el(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function es(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var eu=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,d.uM)(r,t),payload:e}]};function ec(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:(0,d.uM)(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function ef(e){var t=(0,j.G)(Z.A2),{data:r,dataKey:i,background:a,allOtherBarProps:o}=e,{onMouseEnter:l,onMouseLeave:s,onClick:u}=o,f=es(o,et),d=(0,O.Cj)(l,i),h=(0,O.Pg)(s),y=(0,O.Ub)(u,i);if(!a||null==r)return null;var g=(0,c.J9)(a,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:o,background:l,tooltipPosition:s}=e,u=es(e,er);if(!l)return null;var c=d(e,r),v=h(e,r),m=y(e,r),b=eo(eo(eo(eo(eo({option:a,isActive:String(r)===t},u),{},{fill:"#eee"},l),g),(0,p.XC)(f,e,r)),{},{onMouseEnter:c,onMouseLeave:v,onClick:m,dataKey:i,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(x,ei({key:"background-bar-".concat(r)},b))}))}function ed(e){var{data:t,props:r,showLabels:i}=e,o=(0,c.J9)(r,!1),{shape:l,dataKey:u,activeBar:f}=r,d=(0,j.G)(Z.A2),h=(0,j.G)(Z.Xb),{onMouseEnter:y,onClick:g,onMouseLeave:v}=r,m=es(r,en),b=(0,O.Cj)(y,u),w=(0,O.Pg)(v),P=(0,O.Ub)(g,u);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=f&&String(t)===d&&(null==h||u===h),i=eo(eo(eo({},o),e),{},{isActive:r,option:r?f:l,index:t,dataKey:u});return n.createElement(a.W,ei({className:"recharts-bar-rectangle"},(0,p.XC)(m,e,t),{onMouseEnter:b(e,t),onMouseLeave:w(e,t),onClick:P(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),n.createElement(x,i))}),i&&s.Z.renderCallByParent(r,t)):null}function ep(e){var{props:t,previousRectanglesRef:r}=e,{data:i,layout:o,isAnimationActive:l,animationBegin:s,animationDuration:c,animationEasing:f,onAnimationEnd:d,onAnimationStart:p}=t,h=r.current,y=(0,J.n)(t,"recharts-bar-"),[g,v]=(0,n.useState)(!1),m=(0,n.useCallback)(()=>{"function"==typeof d&&d(),v(!1)},[d]),b=(0,n.useCallback)(()=>{"function"==typeof p&&p(),v(!0)},[p]);return n.createElement(ee.i,{begin:s,duration:c,isActive:l,easing:f,from:{t:0},to:{t:1},onAnimationEnd:m,onAnimationStart:b,key:y},e=>{var{t:l}=e,s=1===l?i:i.map((e,t)=>{var r=h&&h[t];if(r){var n=(0,u.Dj)(r.x,e.x),i=(0,u.Dj)(r.y,e.y),a=(0,u.Dj)(r.width,e.width),s=(0,u.Dj)(r.height,e.height);return eo(eo({},e),{},{x:n(l),y:i(l),width:a(l),height:s(l)})}if("horizontal"===o){var c=(0,u.Dj)(0,e.height)(l);return eo(eo({},e),{},{y:e.y+e.height-c,height:c})}var f=(0,u.Dj)(0,e.width)(l);return eo(eo({},e),{},{width:f})});return l>0&&(r.current=s),n.createElement(a.W,null,n.createElement(ed,{props:t,data:s,showLabels:!g}))})}function eh(e){var{data:t,isAnimationActive:r}=e,i=(0,n.useRef)(null);return r&&t&&t.length&&(null==i.current||i.current!==t)?n.createElement(ep,{previousRectanglesRef:i,props:e}):n.createElement(ed,{props:e,data:t,showLabels:!0})}var ey=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,d.kr)(e,t)}};class eg extends n.PureComponent{render(){var{hide:e,data:t,dataKey:r,className:l,xAxisId:s,yAxisId:c,needClip:f,background:d,id:p,layout:h}=this.props;if(e)return null;var y=(0,i.$)("recharts-bar",l),g=(0,u.uy)(p)?this.id:p;return n.createElement(a.W,{className:y},f&&n.createElement("defs",null,n.createElement(A.Q,{clipPathId:g,xAxisId:s,yAxisId:c})),n.createElement(a.W,{className:"recharts-bar-rectangles",clipPath:f?"url(#clipPath-".concat(g,")"):null},n.createElement(ef,{data:t,dataKey:r,background:d,allOtherBarProps:this.props}),n.createElement(eh,this.props)),n.createElement(o._,{direction:"horizontal"===h?"y":"x"},this.props.children))}constructor(){super(...arguments),el(this,"id",(0,u.NF)("recharts-bar-"))}}var ev={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!f.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function em(e){var t,{xAxisId:r,yAxisId:i,hide:a,legendType:o,minPointSize:s,activeBar:u,animationBegin:f,animationDuration:p,animationEasing:h,isAnimationActive:y}=(0,Q.e)(e,ev),{needClip:g}=(0,A.l)(r,i),v=(0,k.WX)(),m=(0,Y.r)(),b=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:s,stackId:(0,d.$8)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,s,e.stackId]),x=(0,c.aS)(e.children,l.f),w=(0,j.G)(e=>V(e,r,i,m,b,x));if("vertical"!==v&&"horizontal"!==v)return null;var O=null==w?void 0:w[0];return t=null==O||null==O.height||null==O.width?0:"vertical"===v?O.height/2:O.width/2,n.createElement(M.zk,{xAxisId:r,yAxisId:i,data:w,dataPointFormatter:ey,errorBarOffset:t},n.createElement(eg,ei({},e,{layout:v,needClip:g,data:w,xAxisId:r,yAxisId:i,hide:a,legendType:o,minPointSize:s,activeBar:u,animationBegin:f,animationDuration:p,animationEasing:h,isAnimationActive:y})))}function eb(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:s,yAxisTicks:c,stackedData:f,dataStartIndex:p,displayedData:h,offset:y,cells:g}=e,v="horizontal"===t?l:o,m=f?v.scale.domain():null,b=(0,d.DW)({numericAxis:v});return h.map((e,h)=>{f?x=(0,d._f)(f[p+h],m):Array.isArray(x=(0,d.kr)(e,r))||(x=[b,x]);var v=w(n,0)(x[1],h);if("horizontal"===t){var x,O,P,j,S,E,M,[A,k]=[l.scale(x[0]),l.scale(x[1])];O=(0,d.y2)({axis:o,ticks:s,bandSize:a,offset:i.offset,entry:e,index:h}),P=null!=(M=null!=k?k:A)?M:void 0,j=i.size;var _=A-k;if(S=(0,u.M8)(_)?0:_,E={x:O,y:y.top,width:j,height:y.height},Math.abs(v)>0&&Math.abs(S)<Math.abs(v)){var D=(0,u.sA)(S||v)*(Math.abs(v)-Math.abs(S));P-=D,S+=D}}else{var[C,T]=[o.scale(x[0]),o.scale(x[1])];if(O=C,P=(0,d.y2)({axis:l,ticks:c,bandSize:a,offset:i.offset,entry:e,index:h}),j=T-C,S=i.size,E={x:y.left,y:P,width:y.width,height:S},Math.abs(v)>0&&Math.abs(j)<Math.abs(v)){var N=(0,u.sA)(j||v)*(Math.abs(v)-Math.abs(j));j+=N}}return eo(eo({},e),{},{x:O,y:P,width:j,height:S,value:f?x:x[1],payload:e,background:E,tooltipPosition:{x:O+j/2,y:P+S/2}},g&&g[h]&&g[h].props)})}class ex extends n.PureComponent{render(){return n.createElement(M._S,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(E,null),n.createElement(X.A,{legendPayload:eu(this.props)}),n.createElement(P.r,{fn:ec,args:this.props}),n.createElement(em,this.props))}}el(ex,"displayName","Bar"),el(ex,"defaultProps",ev)},58080:(e,t,r)=>{e.exports=r(78359).last},59068:(e,t,r)=>{"use strict";r.d(t,{TK:()=>l});var n=r(12115),i=r(34487),a=r(81971),o=r(71807),l=e=>{var{chartData:t}=e,r=(0,a.j)(),l=(0,o.r)();return(0,n.useEffect)(()=>l?()=>{}:(r((0,i.hq)(t)),()=>{r((0,i.hq)(void 0))}),[t,r,l]),null}},60356:(e,t,r)=>{"use strict";r.d(t,{HS:()=>o,LF:()=>i,z3:()=>a});var n=r(68924),i=e=>e.chartData,a=(0,n.Mz)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),o=(e,t,r,n)=>n?a(e):i(e)},60379:(e,t,r)=>{"use strict";r.d(t,{J:()=>x,Z:()=>h});var n=r(12115),i=r(52596),a=r(79095),o=r(70788),l=r(16377),s=r(25641),u=["offset"];function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var p=e=>{var{value:t,formatter:r}=e,n=(0,l.uy)(e.children)?t:e.children;return"function"==typeof r?r(n):n},h=e=>null!=e&&"function"==typeof e,y=(e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),360),g=(e,t,r)=>{var a,o,{position:u,viewBox:c,offset:f,className:p}=e,{cx:h,cy:g,innerRadius:v,outerRadius:m,startAngle:b,endAngle:x,clockWise:w}=c,O=(v+m)/2,P=y(b,x),j=P>=0?1:-1;"insideStart"===u?(a=b+j*f,o=w):"insideEnd"===u?(a=x-j*f,o=!w):"end"===u&&(a=x+j*f,o=w),o=P<=0?o:!o;var S=(0,s.IZ)(h,g,O,a),E=(0,s.IZ)(h,g,O,a+(o?1:-1)*359),M="M".concat(S.x,",").concat(S.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(+!o,",\n    ").concat(E.x,",").concat(E.y),A=(0,l.uy)(e.id)?(0,l.NF)("recharts-radial-line-"):e.id;return n.createElement("text",d({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",p)}),n.createElement("defs",null,n.createElement("path",{id:A,d:M})),n.createElement("textPath",{xlinkHref:"#".concat(A)},t))},v=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:u,endAngle:c}=t,f=(u+c)/2;if("outside"===n){var{x:d,y:p}=(0,s.IZ)(i,a,l+r,f);return{x:d,y:p,textAnchor:d>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:h,y}=(0,s.IZ)(i,a,(o+l)/2,f);return{x:h,y,textAnchor:"middle",verticalAnchor:"middle"}},m=e=>{var{viewBox:t,parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:s,height:u}=t,c=u>=0?1:-1,d=c*n,p=c>0?"end":"start",h=c>0?"start":"end",y=s>=0?1:-1,g=y*n,v=y>0?"end":"start",m=y>0?"start":"end";if("top"===i)return f(f({},{x:a+s/2,y:o-c*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(o-r.y,0),width:s}:{});if("bottom"===i)return f(f({},{x:a+s/2,y:o+u+d,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(o+u),0),width:s}:{});if("left"===i){var b={x:a-g,y:o+u/2,textAnchor:v,verticalAnchor:"middle"};return f(f({},b),r?{width:Math.max(b.x-r.x,0),height:u}:{})}if("right"===i){var x={x:a+s+g,y:o+u/2,textAnchor:m,verticalAnchor:"middle"};return f(f({},x),r?{width:Math.max(r.x+r.width-x.x,0),height:u}:{})}var w=r?{width:s,height:u}:{};return"insideLeft"===i?f({x:a+g,y:o+u/2,textAnchor:m,verticalAnchor:"middle"},w):"insideRight"===i?f({x:a+s-g,y:o+u/2,textAnchor:v,verticalAnchor:"middle"},w):"insideTop"===i?f({x:a+s/2,y:o+d,textAnchor:"middle",verticalAnchor:h},w):"insideBottom"===i?f({x:a+s/2,y:o+u-d,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===i?f({x:a+g,y:o+d,textAnchor:m,verticalAnchor:h},w):"insideTopRight"===i?f({x:a+s-g,y:o+d,textAnchor:v,verticalAnchor:h},w):"insideBottomLeft"===i?f({x:a+g,y:o+u-d,textAnchor:m,verticalAnchor:p},w):"insideBottomRight"===i?f({x:a+s-g,y:o+u-d,textAnchor:v,verticalAnchor:p},w):i&&"object"==typeof i&&((0,l.Et)(i.x)||(0,l._3)(i.x))&&((0,l.Et)(i.y)||(0,l._3)(i.y))?f({x:a+(0,l.F4)(i.x,s),y:o+(0,l.F4)(i.y,u),textAnchor:"end",verticalAnchor:"end"},w):f({x:a+s/2,y:o+u/2,textAnchor:"middle",verticalAnchor:"middle"},w)},b=e=>"cx"in e&&(0,l.Et)(e.cx);function x(e){var t,{offset:r=5}=e,s=f({offset:r},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,u)),{viewBox:c,position:h,value:y,children:x,content:w,className:O="",textBreakAll:P,labelRef:j}=s;if(!c||(0,l.uy)(y)&&(0,l.uy)(x)&&!(0,n.isValidElement)(w)&&"function"!=typeof w)return null;if((0,n.isValidElement)(w))return(0,n.cloneElement)(w,s);if("function"==typeof w){if(t=(0,n.createElement)(w,s),(0,n.isValidElement)(t))return t}else t=p(s);var S=b(c),E=(0,o.J9)(s,!0);if(S&&("insideStart"===h||"insideEnd"===h||"end"===h))return g(s,t,E);var M=S?v(s):m(s);return n.createElement(a.E,d({ref:j,className:(0,i.$)("recharts-label",O)},E,M,{breakAll:P}),t)}x.displayName="Label";var w=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:s,innerRadius:u,outerRadius:c,x:f,y:d,top:p,left:h,width:y,height:g,clockWise:v,labelViewBox:m}=e;if(m)return m;if((0,l.Et)(y)&&(0,l.Et)(g)){if((0,l.Et)(f)&&(0,l.Et)(d))return{x:f,y:d,width:y,height:g};if((0,l.Et)(p)&&(0,l.Et)(h))return{x:p,y:h,width:y,height:g}}return(0,l.Et)(f)&&(0,l.Et)(d)?{x:f,y:d,width:0,height:0}:(0,l.Et)(t)&&(0,l.Et)(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:u||0,outerRadius:c||s||o||0,clockWise:v}:e.viewBox?e.viewBox:{}},O=(e,t,r)=>{if(!e)return null;var i={viewBox:t,labelRef:r};return!0===e?n.createElement(x,d({key:"label-implicit"},i)):(0,l.vh)(e)?n.createElement(x,d({key:"label-implicit",value:e},i)):(0,n.isValidElement)(e)?e.type===x?(0,n.cloneElement)(e,f({key:"label-implicit"},i)):n.createElement(x,d({key:"label-implicit",content:e},i)):h(e)?n.createElement(x,d({key:"label-implicit",content:e},i)):e&&"object"==typeof e?n.createElement(x,d({},e,{key:"label-implicit"},i)):null};x.parseViewBox=w,x.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i,labelRef:a}=e,l=w(e),s=(0,o.aS)(i,x).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||l,key:"label-".concat(r)}));return r?[O(e.label,t||l,a),...s]:s}},60429:(e,t,r)=>{"use strict";r.d(t,{mZ:()=>l,vE:()=>o});var n=r(5710),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},a=(0,n.Z0)({name:"rootProps",initialState:i,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:i.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),o=a.reducer,{updateOptions:l}=a.actions},60512:(e,t,r)=>{e.exports=r(7547).uniqBy},60530:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(16377),i=(e,t)=>{var r,i=Number(t);if(!(0,n.M8)(i)&&null!=t)return i>=0?null==e||null==(r=e[i])?void 0:r.value:void 0}},60841:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(78892),i=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var a=Infinity;return t.length>0&&(a=t.length-1),String(Math.max(0,Math.min(i,a)))}},61299:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.resizeHandleType=t.resizeHandleAxesType=t.default=void 0;var n=a(r(38637)),i=a(r(12115));function a(e){return e&&e.__esModule?e:{default:e}}let o=t.resizeHandleAxesType=n.default.arrayOf(n.default.oneOf(["s","w","e","n","sw","nw","se","ne"])),l=t.resizeHandleType=n.default.oneOfType([n.default.node,n.default.func]);t.default={className:n.default.string,style:n.default.object,width:n.default.number,autoSize:n.default.bool,cols:n.default.number,draggableCancel:n.default.string,draggableHandle:n.default.string,verticalCompact:function(e){e.verticalCompact},compactType:n.default.oneOf(["vertical","horizontal"]),layout:function(e){var t=e.layout;void 0!==t&&r(50507).validateLayout(t,"layout")},margin:n.default.arrayOf(n.default.number),containerPadding:n.default.arrayOf(n.default.number),rowHeight:n.default.number,maxRows:n.default.number,isBounded:n.default.bool,isDraggable:n.default.bool,isResizable:n.default.bool,allowOverlap:n.default.bool,preventCollision:n.default.bool,useCSSTransforms:n.default.bool,transformScale:n.default.number,isDroppable:n.default.bool,resizeHandles:o,resizeHandle:l,onLayoutChange:n.default.func,onDragStart:n.default.func,onDrag:n.default.func,onDragStop:n.default.func,onResizeStart:n.default.func,onResize:n.default.func,onResizeStop:n.default.func,onDrop:n.default.func,droppingItem:n.default.shape({i:n.default.string.isRequired,w:n.default.number.isRequired,h:n.default.number.isRequired}),children:function(e,t){let r=e[t],n={};i.default.Children.forEach(r,function(e){if(e?.key!=null){if(n[e.key])throw Error('Duplicate child key "'+e.key+'" found! This will cause problems in ReactGridLayout.');n[e.key]=!0}})},innerRef:n.default.any}},61667:(e,t,r)=>{"use strict";r.d(t,{Gk:()=>ea,Vf:()=>ei});var n=r(12115),i=r(52596),a=r(70688),o=r(51172),l=r(2348),s=r(36079),u=r(41643),c=r(16377),f=r(39827),d=r(70788),p=r(93262),h=r(56091),y=r(39226),g=r(37195),v=r(68924),m=r(14299),b=r(97238),x=r(60356),w=(e,t,r,n)=>(0,m.Gx)(e,"xAxis",t,n),O=(e,t,r,n)=>(0,m.CR)(e,"xAxis",t,n),P=(e,t,r,n)=>(0,m.Gx)(e,"yAxis",r,n),j=(e,t,r,n)=>(0,m.CR)(e,"yAxis",r,n),S=(0,v.Mz)([b.fz,w,P,O,j],(e,t,r,n,i)=>(0,f._L)(e,"xAxis")?(0,f.Hj)(t,n,!1):(0,f.Hj)(r,i,!1)),E=(0,v.Mz)([m.ld,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"area"===e.type&&t.dataKey===e.dataKey&&(0,f.$8)(t.stackId)===e.stackId&&t.data===e.data))return t}),M=(0,v.Mz)([b.fz,w,P,O,j,(e,t,r,n,i)=>{var a,o,l=(0,b.fz)(e);if(null!=(o=(0,f._L)(l,"xAxis")?(0,m.TC)(e,"yAxis",r,n):(0,m.TC)(e,"xAxis",t,n))){var{dataKey:s,stackId:u}=i;if(null!=u){var c=null==(a=o[u])?void 0:a.stackedData;return null==c?void 0:c.find(e=>e.key===s)}}},x.HS,S,E],(e,t,r,n,i,a,o,l,s)=>{var u,{chartData:c,dataStartIndex:f,dataEndIndex:d}=o;if(null!=s&&("horizontal"===e||"vertical"===e)&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=l){var{data:p}=s;if(null!=(u=p&&p.length>0?p:null==c?void 0:c.slice(f,d+1)))return ei({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataStartIndex:f,areaSettings:s,stackedData:a,displayedData:u,chartBaseValue:void 0,bandSize:l})}}),A=r(71807),k=r(94732),_=r(79020),D=r(81971),C=r(39426),T=r(93389),N=r(78892),z=r(74460),I=["layout","type","stroke","connectNulls","isRange"],R=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function L(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function H(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function $(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?H(Object(r),!0).forEach(function(t){B(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function B(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function W(){return(W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function U(e,t){return e&&"none"!==e?e:t}var F=e=>{var{dataKey:t,name:r,stroke:n,fill:i,legendType:a,hide:o}=e;return[{inactive:o,dataKey:t,type:a,color:U(n,i),value:(0,f.uM)(r,t),payload:e}]};function K(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:s}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:(0,f.uM)(o,t),hide:l,type:e.tooltipType,color:U(n,a),unit:s}}}var G=(e,t)=>{var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var a=(0,i.$)("recharts-area-dot","boolean"!=typeof e?e.className:"");r=n.createElement(o.c,W({},t,{className:a}))}return r};function q(e){var{clipPathId:t,points:r,props:i}=e,{needClip:a,dot:o,dataKey:s}=i;if(null==r||!o&&1!==r.length)return null;var u=(0,d.y$)(o),c=(0,d.J9)(i,!1),f=(0,d.J9)(o,!0),p=r.map((e,t)=>G(o,$($($({key:"dot-".concat(t),r:3},c),f),{},{index:t,cx:e.x,cy:e.y,dataKey:s,value:e.value,payload:e.payload,points:r}))),h={clipPath:a?"url(#clipPath-".concat(u?"":"dots-").concat(t,")"):void 0};return n.createElement(l.W,W({className:"recharts-area-dots"},h),p)}function V(e){var{points:t,baseLine:r,needClip:i,clipPathId:o,props:u,showLabels:c}=e,{layout:f,type:p,stroke:h,connectNulls:y,isRange:g}=u,v=L(u,I);return n.createElement(n.Fragment,null,(null==t?void 0:t.length)>1&&n.createElement(l.W,{clipPath:i?"url(#clipPath-".concat(o,")"):void 0},n.createElement(a.I,W({},(0,d.J9)(v,!0),{points:t,connectNulls:y,type:p,baseLine:r,layout:f,stroke:"none",className:"recharts-area-area"})),"none"!==h&&n.createElement(a.I,W({},(0,d.J9)(u,!1),{className:"recharts-area-curve",layout:f,type:p,connectNulls:y,fill:"none",points:t})),"none"!==h&&g&&n.createElement(a.I,W({},(0,d.J9)(u,!1),{className:"recharts-area-curve",layout:f,type:p,connectNulls:y,fill:"none",points:r}))),n.createElement(q,{points:t,props:u,clipPathId:o}),c&&s.Z.renderCallByParent(u,t))}function Y(e){var{alpha:t,baseLine:r,points:i,strokeWidth:a}=e,o=i[0].y,l=i[i.length-1].y;if(!(0,N.H)(o)||!(0,N.H)(l))return null;var s=t*Math.abs(o-l),u=Math.max(...i.map(e=>e.x||0));return((0,c.Et)(r)?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(...r.map(e=>e.x||0),u)),(0,c.Et)(u))?n.createElement("rect",{x:0,y:o<l?o:o-s,width:u+(a?parseInt("".concat(a),10):1),height:Math.floor(s)}):null}function Z(e){var{alpha:t,baseLine:r,points:i,strokeWidth:a}=e,o=i[0].x,l=i[i.length-1].x;if(!(0,N.H)(o)||!(0,N.H)(l))return null;var s=t*Math.abs(o-l),u=Math.max(...i.map(e=>e.y||0));return((0,c.Et)(r)?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(...r.map(e=>e.y||0),u)),(0,c.Et)(u))?n.createElement("rect",{x:o<l?o:o-s,y:0,width:s,height:Math.floor(u+(a?parseInt("".concat(a),10):1))}):null}function X(e){var{alpha:t,layout:r,points:i,baseLine:a,strokeWidth:o}=e;return"vertical"===r?n.createElement(Y,{alpha:t,points:i,baseLine:a,strokeWidth:o}):n.createElement(Z,{alpha:t,points:i,baseLine:a,strokeWidth:o})}function J(e){var{needClip:t,clipPathId:r,props:i,previousPointsRef:a,previousBaselineRef:o}=e,{points:s,baseLine:u,isAnimationActive:f,animationBegin:d,animationDuration:p,animationEasing:h,onAnimationStart:y,onAnimationEnd:g}=i,v=(0,C.n)(i,"recharts-area-"),[m,b]=(0,n.useState)(!0),x=(0,n.useCallback)(()=>{"function"==typeof g&&g(),b(!1)},[g]),w=(0,n.useCallback)(()=>{"function"==typeof y&&y(),b(!0)},[y]),O=a.current,P=o.current;return n.createElement(z.i,{begin:d,duration:p,isActive:f,easing:h,from:{t:0},to:{t:1},onAnimationEnd:x,onAnimationStart:w,key:v},e=>{var{t:f}=e;if(O){var d,p=O.length/s.length,h=1===f?s:s.map((e,t)=>{var r=Math.floor(t*p);if(O[r]){var n=O[r];return $($({},e),{},{x:(0,c.GW)(n.x,e.x,f),y:(0,c.GW)(n.y,e.y,f)})}return e});return d=(0,c.Et)(u)?(0,c.GW)(P,u,f):(0,c.uy)(u)||(0,c.M8)(u)?(0,c.GW)(P,0,f):u.map((e,t)=>{var r=Math.floor(t*p);if(Array.isArray(P)&&P[r]){var n=P[r];return $($({},e),{},{x:(0,c.GW)(n.x,e.x,f),y:(0,c.GW)(n.y,e.y,f)})}return e}),f>0&&(a.current=h,o.current=d),n.createElement(V,{points:h,baseLine:d,needClip:t,clipPathId:r,props:i,showLabels:!m})}return f>0&&(a.current=s,o.current=u),n.createElement(l.W,null,n.createElement("defs",null,n.createElement("clipPath",{id:"animationClipPath-".concat(r)},n.createElement(X,{alpha:f,points:s,baseLine:u,layout:i.layout,strokeWidth:i.strokeWidth}))),n.createElement(l.W,{clipPath:"url(#animationClipPath-".concat(r,")")},n.createElement(V,{points:s,baseLine:u,needClip:t,clipPathId:r,props:i,showLabels:!0})))})}function Q(e){var{needClip:t,clipPathId:r,props:i}=e,{points:a,baseLine:o,isAnimationActive:l}=i,s=(0,n.useRef)(null),u=(0,n.useRef)(),c=s.current,f=u.current;return l&&a&&a.length&&(c!==a||f!==o)?n.createElement(J,{needClip:t,clipPathId:r,props:i,previousPointsRef:s,previousBaselineRef:u}):n.createElement(V,{points:a,baseLine:o,needClip:t,clipPathId:r,props:i,showLabels:!0})}class ee extends n.PureComponent{render(){var e,{hide:t,dot:r,points:a,className:o,top:s,left:u,needClip:f,xAxisId:h,yAxisId:y,width:v,height:m,id:b,baseLine:x}=this.props;if(t)return null;var w=(0,i.$)("recharts-area",o),O=(0,c.uy)(b)?this.id:b,{r:P=3,strokeWidth:j=2}=null!=(e=(0,d.J9)(r,!1))?e:{r:3,strokeWidth:2},S=(0,d.y$)(r),E=2*P+j;return n.createElement(n.Fragment,null,n.createElement(l.W,{className:w},f&&n.createElement("defs",null,n.createElement(g.Q,{clipPathId:O,xAxisId:h,yAxisId:y}),!S&&n.createElement("clipPath",{id:"clipPath-dots-".concat(O)},n.createElement("rect",{x:u-E/2,y:s-E/2,width:v+E,height:m+E}))),n.createElement(Q,{needClip:f,clipPathId:O,props:this.props})),n.createElement(p.W,{points:a,mainColor:U(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(x)&&n.createElement(p.W,{points:x,mainColor:U(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}constructor(){super(...arguments),B(this,"id",(0,c.NF)("recharts-area-"))}}var et={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!u.m.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function er(e){var t,r=(0,T.e)(e,et),{activeDot:i,animationBegin:a,animationDuration:o,animationEasing:l,connectNulls:s,dot:u,fill:c,fillOpacity:f,hide:d,isAnimationActive:p,legendType:h,stroke:y,xAxisId:v,yAxisId:m}=r,x=L(r,R),w=(0,b.WX)(),O=(0,k.fW)(),{needClip:P}=(0,g.l)(v,m),j=(0,A.r)(),S=(0,n.useMemo)(()=>({baseValue:e.baseValue,stackId:e.stackId,connectNulls:s,data:e.data,dataKey:e.dataKey}),[e.baseValue,e.stackId,s,e.data,e.dataKey]),{points:E,isRange:_,baseLine:C}=null!=(t=(0,D.G)(e=>M(e,v,m,j,S)))?t:{},{height:N,width:z,left:I,top:H}=(0,b.hj)();return"horizontal"!==w&&"vertical"!==w||"AreaChart"!==O&&"ComposedChart"!==O?null:n.createElement(ee,W({},x,{activeDot:i,animationBegin:a,animationDuration:o,animationEasing:l,baseLine:C,connectNulls:s,dot:u,fill:c,fillOpacity:f,height:N,hide:d,layout:w,isAnimationActive:p,isRange:_,legendType:h,needClip:P,points:E,stroke:y,width:z,left:I,top:H,xAxisId:v,yAxisId:m}))}var en=(e,t,r,n,i)=>{var a=null!=r?r:t;if((0,c.Et)(a))return a;var o="horizontal"===e?i:n,l=o.scale.domain();if("number"===o.type){var s=Math.max(l[0],l[1]),u=Math.min(l[0],l[1]);return"dataMin"===a?u:"dataMax"===a||s<0?s:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===a?l[0]:"dataMax"===a?l[1]:l[0]};function ei(e){var t,{areaSettings:{connectNulls:r,baseValue:n,dataKey:i},stackedData:a,layout:o,chartBaseValue:l,xAxis:s,yAxis:u,displayedData:c,dataStartIndex:d,xAxisTicks:p,yAxisTicks:h,bandSize:y}=e,g=a&&a.length,v=en(o,l,n,s,u),m="horizontal"===o,b=!1,x=c.map((e,t)=>{g?n=a[d+t]:Array.isArray(n=(0,f.kr)(e,i))?b=!0:n=[v,n];var n,o=null==n[1]||g&&!r&&null==(0,f.kr)(e,i);return m?{x:(0,f.nb)({axis:s,ticks:p,bandSize:y,entry:e,index:t}),y:o?null:u.scale(n[1]),value:n,payload:e}:{x:o?null:s.scale(n[1]),y:(0,f.nb)({axis:u,ticks:h,bandSize:y,entry:e,index:t}),value:n,payload:e}});return t=g||b?x.map(e=>{var t=Array.isArray(e.value)?e.value[0]:null;return m?{x:e.x,y:null!=t&&null!=e.y?u.scale(t):null}:{x:null!=t?s.scale(t):null,y:e.y}}):m?u.scale(v):s.scale(v),{points:x,baseLine:t,isRange:b}}class ea extends n.PureComponent{render(){return n.createElement(y._S,{type:"area",data:this.props.data,dataKey:this.props.dataKey,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,stackId:this.props.stackId,hide:this.props.hide,barSize:void 0},n.createElement(_.A,{legendPayload:F(this.props)}),n.createElement(h.r,{fn:K,args:this.props}),n.createElement(er,this.props))}}B(ea,"displayName","Area"),B(ea,"defaultProps",et)},62194:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(10921);t.property=function(e){return function(t){return n.get(t,e)}}},62525:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63027:(e,t,r)=>{"use strict";r.d(t,{g:()=>u});var n=r(68924),i=r(97238),a=r(20215),o=r(69449),l=r(94732),s=r(47062),u=(0,n.Mz)([(e,t)=>t,i.fz,s.D0,a.Re,a.gL,a.R4,l.r1,o.GO],l.aX)},63265:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){}},64072:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},64373:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98412),i=r(68179),a=r(82384),o=r(83616);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},64664:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(42694);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},64968:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var n=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var s=o[0],u=null==s?void 0:l(s.positions,a);if(null!=u)return u;var c=null==i?void 0:i[Number(a)];if(c)if("horizontal"===r)return{x:c.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:c.coordinate}}}},66038:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var n=(e,t)=>t},68179:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(19452);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},68924:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>w});var n=e=>Array.isArray(e)?e:[e],i=0,a=null,o=class{revision=i;_value;_lastValue;_isEqual=l;constructor(e,t=l){this._value=this._lastValue=e,this._isEqual=t}get value(){return a?.add(this),this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function l(e,t){return e===t}function s(e){return e instanceof o||console.warn("Not a valid cell! ",e),e.value}var u=(e,t)=>!1;function c(){return function(e,t=l){return new o(null,t)}(0,u)}var f=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=c()),s(t)};Symbol();var d=0,p=Object.getPrototypeOf({}),h=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,y);tag=c();tags={};children={};collectionTag=null;id=d++},y={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in p)return n;if("object"==typeof n&&null!==n){let r=e.children[t];return void 0===r&&(r=e.children[t]=function(e){return Array.isArray(e)?new g(e):new h(e)}(n)),r.tag&&s(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=c()).value=n),s(r),n}})(),ownKeys:e=>(f(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},g=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],v);tag=c();tags={};children={};collectionTag=null;id=d++},v={get:([e],t)=>("length"===t&&f(e),y.get(e,t)),ownKeys:([e])=>y.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>y.getOwnPropertyDescriptor(e,t),has:([e],t)=>y.has(e,t)},m="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function b(){return{s:0,v:void 0,o:null,p:null}}function x(e,t={}){let r,n=b(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=b(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=b(),e.set(t,o)):o=r}}let s=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new m(t):t}return s.s=1,s.v=t,t}return o.clearCache=()=>{n=b(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var w=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,a=0,o={},l=e.pop();"object"==typeof l&&(o=l,l=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:s,memoizeOptions:u=[],argsMemoize:c=x,argsMemoizeOptions:f=[],devModeChecks:d={}}={...r,...o},p=n(u),h=n(f),y=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),g=s(function(){return i++,l.apply(null,arguments)},...p);return Object.assign(c(function(){a++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(y,arguments);return t=g.apply(null,e)},...h),{resultFunc:l,memoizedResultFunc:g,dependencies:y,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:s,argsMemoize:c})};return Object.assign(i,{withTypes:()=>i}),i}(x),O=Object.assign((e,t=w)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>O})},69074:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},69449:(e,t,r)=>{"use strict";r.d(t,{c2:()=>h,GO:()=>d,Ds:()=>p});var n=r(68924),i=r(95672),a=r.n(i);(0,n.Mz)([e=>e.legend.payload],e=>e.flat(1));var o=r(39827),l=r(2589),s=r(96908),u=r(84421);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var d=(0,n.Mz)([l.Lp,l.A$,l.HK,e=>e.brush.height,s.h,s.W,e=>e.legend.settings,e=>e.legend.size],(e,t,r,n,i,l,s,c)=>{var d=l.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:u.tQ;return f(f({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),p=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:f(f({},e),{},{[r]:a()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),h=f(f({},p),d),y=h.bottom;h.bottom+=n;var g=e-(h=(0,o.s0)(h,s,c)).left-h.right,v=t-h.top-h.bottom;return f(f({brushBottom:y},h),{},{width:Math.max(g,0),height:Math.max(v,0)})}),p=(0,n.Mz)(d,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),h=(0,n.Mz)(l.Lp,l.A$,(e,t)=>({x:0,y:0,width:e,height:t}))},70688:(e,t,r)=>{"use strict";r.d(t,{I:()=>U});var n=r(12115);function i(){}function a(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function o(e){this._context=e}function l(e){this._context=e}function s(e){this._context=e}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},s.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class u{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function c(e){this._context=e}function f(e){this._context=e}function d(e){return new f(e)}c.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function p(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function h(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function y(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function g(e){this._context=e}function v(e){this._context=new m(e)}function m(e){this._context=e}function b(e){this._context=e}function x(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function w(e,t){this._context=e,this._t=t}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,h(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,y(this,h(this,r=p(this,e,t)),r);break;default:y(this,this._t0,r=p(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(v.prototype=Object.create(g.prototype)).point=function(e,t){g.prototype.point.call(this,t,e)},m.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=x(e),i=x(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(9819),P=r(85654),j=r(31847);function S(e){return e[0]}function E(e){return e[1]}function M(e,t){var r=(0,P.A)(!0),n=null,i=d,a=null,o=(0,j.i)(l);function l(l){var s,u,c,f=(l=(0,O.A)(l)).length,d=!1;for(null==n&&(a=i(c=o())),s=0;s<=f;++s)!(s<f&&r(u=l[s],s,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(u,s,l),+t(u,s,l));if(c)return a=null,c+""||null}return e="function"==typeof e?e:void 0===e?S:(0,P.A)(e),t="function"==typeof t?t:void 0===t?E:(0,P.A)(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,P.A)(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,P.A)(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,P.A)(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function A(e,t,r){var n=null,i=(0,P.A)(!0),a=null,o=d,l=null,s=(0,j.i)(u);function u(u){var c,f,d,p,h,y=(u=(0,O.A)(u)).length,g=!1,v=Array(y),m=Array(y);for(null==a&&(l=o(h=s())),c=0;c<=y;++c){if(!(c<y&&i(p=u[c],c,u))===g)if(g=!g)f=c,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=c-1;d>=f;--d)l.point(v[d],m[d]);l.lineEnd(),l.areaEnd()}g&&(v[c]=+e(p,c,u),m[c]=+t(p,c,u),l.point(n?+n(p,c,u):v[c],r?+r(p,c,u):m[c]))}if(h)return l=null,h+""||null}function c(){return M().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?S:(0,P.A)(+e),t="function"==typeof t?t:void 0===t?(0,P.A)(0):(0,P.A)(+t),r="function"==typeof r?r:void 0===r?E:(0,P.A)(+r),u.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,P.A)(+t),n=null,u):e},u.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,P.A)(+t),u):e},u.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,P.A)(+e),u):n},u.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,P.A)(+e),r=null,u):t},u.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,P.A)(+e),u):t},u.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,P.A)(+e),u):r},u.lineX0=u.lineY0=function(){return c().x(e).y(t)},u.lineY1=function(){return c().x(e).y(r)},u.lineX1=function(){return c().x(n).y(t)},u.defined=function(e){return arguments.length?(i="function"==typeof e?e:(0,P.A)(!!e),u):i},u.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),u):o},u.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),u):a},u}var k=r(52596),_=r(43597),D=r(70788),C=r(16377),T=r(78892);function N(){return(N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?z(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var R={curveBasisClosed:function(e){return new l(e)},curveBasisOpen:function(e){return new s(e)},curveBasis:function(e){return new o(e)},curveBumpX:function(e){return new u(e,!0)},curveBumpY:function(e){return new u(e,!1)},curveLinearClosed:function(e){return new c(e)},curveLinear:d,curveMonotoneX:function(e){return new g(e)},curveMonotoneY:function(e){return new v(e)},curveNatural:function(e){return new b(e)},curveStep:function(e){return new w(e,.5)},curveStepAfter:function(e){return new w(e,1)},curveStepBefore:function(e){return new w(e,0)}},L=e=>(0,T.H)(e.x)&&(0,T.H)(e.y),H=e=>e.x,$=e=>e.y,B=(e,t)=>{if("function"==typeof e)return e;var r="curve".concat((0,C.Zb)(e));return("curveMonotone"===r||"curveBump"===r)&&t?R["".concat(r).concat("vertical"===t?"Y":"X")]:R[r]||d},W=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=B(r,a),s=o?n.filter(L):n;if(Array.isArray(i)){var u=o?i.filter(e=>L(e)):i,c=s.map((e,t)=>I(I({},e),{},{base:u[t]}));return(t="vertical"===a?A().y($).x1(H).x0(e=>e.base.x):A().x(H).y1($).y0(e=>e.base.y)).defined(L).curve(l),t(c)}return(t="vertical"===a&&(0,C.Et)(i)?A().y($).x1(H).x0(i):(0,C.Et)(i)?A().x(H).y1($).y0(i):M().x(H).y($)).defined(L).curve(l),t(s)},U=e=>{var{className:t,points:r,path:i,pathRef:a}=e;if((!r||!r.length)&&!i)return null;var o=r&&r.length?W(e):i;return n.createElement("path",N({},(0,D.J9)(e,!1),(0,_._U)(e),{className:(0,k.$)("recharts-curve",t),d:null===o?void 0:o,ref:a}))}},70788:(e,t,r)=>{"use strict";r.d(t,{J9:()=>g,aS:()=>p,y$:()=>h});var n=r(95672),i=r.n(n),a=r(12115),o=r(53588),l=r(16377),s=r(43597),u=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",c=null,f=null,d=e=>{if(e===c&&Array.isArray(f))return f;var t=[];return a.Children.forEach(e,e=>{(0,l.uy)(e)||((0,o.zv)(e)?t=t.concat(d(e.props.children)):t.push(e))}),f=t,c=e,t};function p(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>u(e)):[u(t)],d(e).forEach(e=>{var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var h=e=>!e||"object"!=typeof e||!("clipDot"in e)||!!e.clipDot,y=(e,t,r,n)=>{var i,a=null!=(i=n&&(null===s.VU||void 0===s.VU?void 0:s.VU[n]))?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||s.QQ.includes(t))||r&&s.j2.includes(t)},g=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;y(null==(a=n)?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i}},71420:(e,t,r)=>{"use strict";r.d(t,{E:()=>s,O:()=>u});var n=r(81971),i=r(39827),a=r(20215);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var s=()=>(0,n.G)(a.Dn),u=()=>{var e=s(),t=(0,n.G)(a.R4),r=(0,n.G)(a.fl);return(0,i.Hj)(l(l({},e),{},{scale:r}),t)}},71807:(e,t,r)=>{"use strict";r.d(t,{r:()=>a});var n=r(12115),i=(0,n.createContext)(null),a=()=>null!=(0,n.useContext)(i)},72465:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},72744:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),i=r(82384),a=r(36633),o=r(83616);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return s(e,t,r,n);if(t instanceof Map){var i=e,o=t,l=r,c=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(i.get(e),t,e,i,o,c))return!1;return!0}if(t instanceof Set)return u(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function s(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let s=0;s<e.length;s++){if(i.has(s))continue;let u=e[s],c=!1;if(r(u,o,a,e,t,n)&&(c=!0),c){i.add(s),l=!0;break}}if(!l)return!1}return!0}function u(e,t,r,n){return 0===t.size||e instanceof Set&&s([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,s){let u=r(t,n,i,a,o,s);return void 0!==u?!!u:l(t,n,e,s)},new Map)},t.isSetMatch=u},72948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},73433:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var n=r(12115),i=r(60429),a=r(81971);function o(e){var t=(0,a.j)();return(0,n.useEffect)(()=>{t((0,i.mZ)(e))},[t,e]),null}},73666:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraggableCore",{enumerable:!0,get:function(){return c.default}}),t.default=void 0;var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=p(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(n,a,o):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(12115)),i=d(r(38637)),a=d(r(47650)),o=d(r(34259)),l=r(30624),s=r(9055),u=r(34425),c=d(r(19297)),f=d(r(63265));function d(e){return e&&e.__esModule?e:{default:e}}function p(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(p=function(e){return e?r:t})(e)}function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function y(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:String(n))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class g extends n.Component{static getDerivedStateFromProps(e,t){let{position:r}=e,{prevPropsPosition:n}=t;return r&&(!n||r.x!==n.x||r.y!==n.y)?((0,f.default)("Draggable: getDerivedStateFromProps %j",{position:r,prevPropsPosition:n}),{x:r.x,y:r.y,prevPropsPosition:{...r}}):null}constructor(e){super(e),y(this,"onDragStart",(e,t)=>{if((0,f.default)("Draggable: onDragStart: %j",t),!1===this.props.onStart(e,(0,s.createDraggableData)(this,t)))return!1;this.setState({dragging:!0,dragged:!0})}),y(this,"onDrag",(e,t)=>{if(!this.state.dragging)return!1;(0,f.default)("Draggable: onDrag: %j",t);let r=(0,s.createDraggableData)(this,t),n={x:r.x,y:r.y,slackX:0,slackY:0};if(this.props.bounds){let{x:e,y:t}=n;n.x+=this.state.slackX,n.y+=this.state.slackY;let[i,a]=(0,s.getBoundPosition)(this,n.x,n.y);n.x=i,n.y=a,n.slackX=this.state.slackX+(e-n.x),n.slackY=this.state.slackY+(t-n.y),r.x=n.x,r.y=n.y,r.deltaX=n.x-this.state.x,r.deltaY=n.y-this.state.y}if(!1===this.props.onDrag(e,r))return!1;this.setState(n)}),y(this,"onDragStop",(e,t)=>{if(!this.state.dragging||!1===this.props.onStop(e,(0,s.createDraggableData)(this,t)))return!1;(0,f.default)("Draggable: onDragStop: %j",t);let r={dragging:!1,slackX:0,slackY:0};if(this.props.position){let{x:e,y:t}=this.props.position;r.x=e,r.y=t}this.setState(r)}),this.state={dragging:!1,dragged:!1,x:e.position?e.position.x:e.defaultPosition.x,y:e.position?e.position.y:e.defaultPosition.y,prevPropsPosition:{...e.position},slackX:0,slackY:0,isElementSVG:!1},e.position&&!(e.onDrag||e.onStop)&&console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element.")}componentDidMount(){void 0!==window.SVGElement&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}componentWillUnmount(){this.setState({dragging:!1})}findDOMNode(){var e,t;return null!=(e=null==(t=this.props)||null==(t=t.nodeRef)?void 0:t.current)?e:a.default.findDOMNode(this)}render(){let{axis:e,bounds:t,children:r,defaultPosition:i,defaultClassName:a,defaultClassNameDragging:u,defaultClassNameDragged:f,position:d,positionOffset:p,scale:y,...g}=this.props,v={},m=null,b=!d||this.state.dragging,x=d||i,w={x:(0,s.canDragX)(this)&&b?this.state.x:x.x,y:(0,s.canDragY)(this)&&b?this.state.y:x.y};this.state.isElementSVG?m=(0,l.createSVGTransform)(w,p):v=(0,l.createCSSTransform)(w,p);let O=(0,o.default)(r.props.className||"",a,{[u]:this.state.dragging,[f]:this.state.dragged});return n.createElement(c.default,h({},g,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),n.cloneElement(n.Children.only(r),{className:O,style:{...r.props.style,...v},transform:m}))}}t.default=g,y(g,"displayName","Draggable"),y(g,"propTypes",{...c.default.propTypes,axis:i.default.oneOf(["both","x","y","none"]),bounds:i.default.oneOfType([i.default.shape({left:i.default.number,right:i.default.number,top:i.default.number,bottom:i.default.number}),i.default.string,i.default.oneOf([!1])]),defaultClassName:i.default.string,defaultClassNameDragging:i.default.string,defaultClassNameDragged:i.default.string,defaultPosition:i.default.shape({x:i.default.number,y:i.default.number}),positionOffset:i.default.shape({x:i.default.oneOfType([i.default.number,i.default.string]),y:i.default.oneOfType([i.default.number,i.default.string])}),position:i.default.shape({x:i.default.number,y:i.default.number}),className:u.dontSetMe,style:u.dontSetMe,transform:u.dontSetMe}),y(g,"defaultProps",{...c.default.defaultProps,axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1})},74460:(e,t,r)=>{"use strict";r.d(t,{i:()=>T});var n=r(12115),i=r(22188),a=r.n(i),o=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],l=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),s=(e,t)=>r=>l(o(e,t),r),u=(e,t)=>r=>l([...o(e,t).map((e,t)=>e*t).slice(1),0],r),c=function(){for(var e,t,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var c=s(e,t),f=s(r,n),d=u(e,t),p=e=>e>1?1:e<0?0:e,h=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=c(r)-t,a=d(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=p(r-i/a)}return f(r)};return h.isStepper=!1,h},f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},d=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return c(e);case"spring":return f();default:if("cubic-bezier"===e.split("(")[0])return c(e)}return"function"==typeof e?e:null};function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),g=(e,t,r)=>e.map(e=>"".concat(y(e)," ").concat(t,"ms ").concat(r)).join(","),v=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),m=(e,t)=>Object.keys(t).reduce((r,n)=>h(h({},r),{},{[n]:e(n,t[n])}),{});function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var w=(e,t,r)=>e+(t-e)*r,O=e=>{var{from:t,to:r}=e;return t!==r},P=(e,t,r)=>{var n=m((t,r)=>{if(O(r)){var[n,i]=e(r.from,r.to,r.velocity);return x(x({},r),{},{from:n,velocity:i})}return r},t);return r<1?m((e,t)=>O(t)?x(x({},t),{},{velocity:w(t.velocity,n[e].velocity,r),from:w(t.from,n[e].from,r)}):t,t):P(e,n,r-1)};let j=(e,t,r,n,i,a)=>{var o=v(e,t);return!0===r.isStepper?function(e,t,r,n,i,a){var o,l=n.reduce((r,n)=>x(x({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),s=()=>m((e,t)=>t.from,l),u=()=>!Object.values(l).filter(O).length,c=null,f=n=>{o||(o=n);var d=(n-o)/r.dt;l=P(r,l,d),i(x(x(x({},e),t),s())),o=n,u()||(c=a.setTimeout(f))};return()=>(c=a.setTimeout(f),()=>{c()})}(e,t,r,o,i,a):function(e,t,r,n,i,a,o){var l,s=null,u=i.reduce((r,n)=>x(x({},r),{},{[n]:[e[n],t[n]]}),{}),c=i=>{l||(l=i);var f=(i-l)/n,d=m((e,t)=>w(...t,r(f)),u);if(a(x(x(x({},e),t),d)),f<1)s=o.setTimeout(c);else{var p=m((e,t)=>w(...t,r(1)),u);a(x(x(x({},e),t),p))}};return()=>(s=o.setTimeout(c),()=>{s()})}(e,t,r,n,o,i,a)};class S{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var E=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function M(){return(M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function k(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?A(Object(r),!0).forEach(function(t){_(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function _(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class D extends n.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:l}=this.props,{style:s}=this.state;if(r){if(!t){this.state&&s&&(n&&s[n]!==o||!n&&s!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!a()(e.to,o)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var c=u||i?l:e.to;this.state&&s&&(n&&s[n]!==c||!n&&s!==c)&&this.setState({style:n?{[n]:c}:c}),this.runAnimation(k(k({},this.props),{},{from:c,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,s=j(t,r,d(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=s()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:s}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof s||"spring"===a)return void this.runJSAnimation(e);var u=n?{[n]:i}:i,c=g(Object.keys(u),r,a);this.manager.start([o,t,k(k({},u),{},{transition:c}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:a,easing:o,isActive:l,from:s,to:u,canBegin:c,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:p,animationManager:h}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,E),g=n.Children.count(t),v=this.state.style;if("function"==typeof t)return t(v);if(!l||0===g||i<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,k(k({},y),{},{style:k(k({},t),v),className:r}))};return 1===g?m(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>m(e)))}constructor(e,t){super(e,t),_(this,"mounted",!1),_(this,"manager",null),_(this,"stopJSAnimation",null),_(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:s}=this.props;if(this.manager=s,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}_(D,"displayName","Animate"),_(D,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var C=(0,n.createContext)(null);function T(e){var t,r,i,a,o,l,s,u,c=(0,n.useContext)(C);return n.createElement(D,M({},e,{animationManager:null!=(s=null!=(u=e.animationManager)?u:c)?s:(t=new S,i=()=>null,a=!1,o=null,l=e=>{if(!a){if(Array.isArray(e)){if(!e.length)return;var[r,...n]=e;if("number"==typeof r){o=t.setTimeout(l.bind(null,n),r);return}l(r),o=t.setTimeout(l.bind(null,n));return}"object"==typeof e&&i(e),"function"==typeof e&&e()}},{stop:()=>{a=!0},start:e=>{a=!1,o&&(o(),o=null),l(e)},subscribe:e=>(i=e,()=>{i=()=>null}),getTimeoutController:()=>t})}))}},74532:(e,t,r)=>{"use strict";r.d(t,{Qx:()=>u,a6:()=>c,h4:()=>K,jM:()=>F,ss:()=>W});var n,i=Symbol.for("immer-nothing"),a=Symbol.for("immer-draftable"),o=Symbol.for("immer-state");function l(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var s=Object.getPrototypeOf;function u(e){return!!e&&!!e[o]}function c(e){return!!e&&(d(e)||Array.isArray(e)||!!e[a]||!!e.constructor?.[a]||v(e)||m(e))}var f=Object.prototype.constructor.toString();function d(e){if(!e||"object"!=typeof e)return!1;let t=s(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function p(e,t){0===h(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function h(e){let t=e[o];return t?t.type_:Array.isArray(e)?1:v(e)?2:3*!!m(e)}function y(e,t){return 2===h(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function g(e,t,r){let n=h(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function v(e){return e instanceof Map}function m(e){return e instanceof Set}function b(e){return e.copy_||e.base_}function x(e,t){if(v(e))return new Map(e);if(m(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=d(e);if(!0!==t&&("class_only"!==t||r)){let t=s(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[o];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(s(e),t)}}function w(e,t=!1){return P(e)||u(e)||!c(e)||(h(e)>1&&(e.set=e.add=e.clear=e.delete=O),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>w(t,!0))),e}function O(){l(2)}function P(e){return Object.isFrozen(e)}var j={};function S(e){let t=j[e];return t||l(0,e),t}function E(e,t){t&&(S("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function M(e){A(e),e.drafts_.forEach(_),e.drafts_=null}function A(e){e===n&&(n=e.parent_)}function k(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function _(e){let t=e[o];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function D(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[o].modified_&&(M(t),l(4)),c(e)&&(e=C(t,e),t.parent_||N(t,e)),t.patches_&&S("Patches").generateReplacementPatches_(r[o].base_,e,t.patches_,t.inversePatches_)):e=C(t,r,[]),M(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==i?e:void 0}function C(e,t,r){if(P(t))return t;let n=t[o];if(!n)return p(t,(i,a)=>T(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return N(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),p(i,(i,o)=>T(e,n,t,i,o,r,a)),N(e,t,!1),r&&e.patches_&&S("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function T(e,t,r,n,i,a,o){if(u(i)){let o=C(e,i,a&&t&&3!==t.type_&&!y(t.assigned_,n)?a.concat(n):void 0);if(g(r,n,o),!u(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(c(i)&&!P(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;C(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&N(e,i)}}function N(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&w(t,r)}var z={get(e,t){if(t===o)return e;let r=b(e);if(!y(r,t)){var n=e,i=r,a=t;let o=L(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let l=r[t];return e.finalized_||!c(l)?l:l===R(e.base_,t)?($(e),e.copy_[t]=B(l,e)):l},has:(e,t)=>t in b(e),ownKeys:e=>Reflect.ownKeys(b(e)),set(e,t,r){let n=L(b(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=R(b(e),t),i=n?.[o];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||y(e.base_,t)))return!0;$(e),H(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==R(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,$(e),H(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=b(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){l(11)},getPrototypeOf:e=>s(e.base_),setPrototypeOf(){l(12)}},I={};function R(e,t){let r=e[o];return(r?b(r):e)[t]}function L(e,t){if(!(t in e))return;let r=s(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=s(r)}}function H(e){!e.modified_&&(e.modified_=!0,e.parent_&&H(e.parent_))}function $(e){e.copy_||(e.copy_=x(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function B(e,t){let r=v(e)?S("MapSet").proxyMap_(e,t):m(e)?S("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),i={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=z;r&&(a=[i],o=I);let{revoke:l,proxy:s}=Proxy.revocable(a,o);return i.draft_=s,i.revoke_=l,s}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function W(e){return u(e)||l(10,e),function e(t){let r;if(!c(t)||P(t))return t;let n=t[o];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=x(t,n.scope_.immer_.useStrictShallowCopy_)}else r=x(t,!0);return p(r,(t,n)=>{g(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}p(z,(e,t)=>{I[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),I.deleteProperty=function(e,t){return I.set.call(this,e,t,void 0)},I.set=function(e,t,r){return z.set.call(this,e[0],t,r,e[0])};var U=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&l(6),void 0!==r&&"function"!=typeof r&&l(7),c(e)){let i=k(this),a=B(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?M(i):A(i)}return E(i,r),D(n,i)}if(e&&"object"==typeof e)l(1,e);else{if(void 0===(n=t(e))&&(n=e),n===i&&(n=void 0),this.autoFreeze_&&w(n,!0),r){let t=[],i=[];S("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){c(e)||l(8),u(e)&&(e=W(e));let t=k(this),r=B(e,void 0);return r[o].isManual_=!0,A(t),r}finishDraft(e,t){let r=e&&e[o];r&&r.isManual_||l(9);let{scope_:n}=r;return E(n,t),D(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=S("Patches").applyPatches_;return u(e)?n(e,t):this.produce(e,e=>n(e,t))}},F=U.produce;function K(e){return e}U.produceWithPatches.bind(U),U.setAutoFreeze.bind(U),U.setUseStrictShallowCopy.bind(U),U.applyPatches.bind(U),U.createDraft.bind(U),U.finishDraft.bind(U)},75714:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var n=e=>e.tooltip},76725:function(e,t){(function(e){"use strict";function t(e){return function(t,r,n,i,a,o,l){return e(t,r,l)}}function r(e){return function(t,r,n,i){if(!t||!r||"object"!=typeof t||"object"!=typeof r)return e(t,r,n,i);var a=i.get(t),o=i.get(r);if(a&&o)return a===r&&o===t;i.set(t,r),i.set(r,t);var l=e(t,r,n,i);return i.delete(t),i.delete(r),l}}function n(e,t){var r={};for(var n in e)r[n]=e[n];for(var n in t)r[n]=t[n];return r}function i(e){return e.constructor===Object||null==e.constructor}function a(e){return"function"==typeof e.then}function o(e,t){return e===t||e!=e&&t!=t}var l=Object.prototype.toString;function s(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areMapsEqual,s=e.areObjectsEqual,u=e.areRegExpsEqual,c=e.areSetsEqual,f=(0,e.createIsNestedEqual)(d);function d(e,d,p){if(e===d)return!0;if(!e||!d||"object"!=typeof e||"object"!=typeof d)return e!=e&&d!=d;if(i(e)&&i(d))return s(e,d,f,p);var h=Array.isArray(e),y=Array.isArray(d);if(h||y)return h===y&&t(e,d,f,p);var g=l.call(e);return g===l.call(d)&&("[object Date]"===g?r(e,d,f,p):"[object RegExp]"===g?u(e,d,f,p):"[object Map]"===g?n(e,d,f,p):"[object Set]"===g?c(e,d,f,p):"[object Object]"===g||"[object Arguments]"===g?!(a(e)||a(d))&&s(e,d,f,p):("[object Boolean]"===g||"[object Number]"===g||"[object String]"===g)&&o(e.valueOf(),d.valueOf()))}return d}function u(e,t,r,n){var i=e.length;if(t.length!==i)return!1;for(;i-- >0;)if(!r(e[i],t[i],i,i,e,t,n))return!1;return!0}var c=r(u);function f(e,t){return o(e.valueOf(),t.valueOf())}function d(e,t,r,n){var i=e.size===t.size;if(!i)return!1;if(!e.size)return!0;var a={},o=0;return e.forEach(function(l,s){if(i){var u=!1,c=0;t.forEach(function(i,f){!u&&!a[c]&&(u=r(s,f,o,c,e,t,n)&&r(l,i,s,f,e,t,n))&&(a[c]=!0),c++}),o++,i=u}}),i}var p=r(d),h=Object.prototype.hasOwnProperty;function y(e,t,r,n){var i,a=Object.keys(e),o=a.length;if(Object.keys(t).length!==o)return!1;for(;o-- >0;){if("_owner"===(i=a[o])){var l=!!e.$$typeof,s=!!t.$$typeof;if((l||s)&&l!==s)return!1}if(!h.call(t,i)||!r(e[i],t[i],i,i,e,t,n))return!1}return!0}var g=r(y);function v(e,t){return e.source===t.source&&e.flags===t.flags}function m(e,t,r,n){var i=e.size===t.size;if(!i)return!1;if(!e.size)return!0;var a={};return e.forEach(function(o,l){if(i){var s=!1,u=0;t.forEach(function(i,c){!s&&!a[u]&&(s=r(o,i,l,c,e,t,n))&&(a[u]=!0),u++}),i=s}}),i}var b=r(m),x=Object.freeze({areArraysEqual:u,areDatesEqual:f,areMapsEqual:d,areObjectsEqual:y,areRegExpsEqual:v,areSetsEqual:m,createIsNestedEqual:t}),w=Object.freeze({areArraysEqual:c,areDatesEqual:f,areMapsEqual:p,areObjectsEqual:g,areRegExpsEqual:v,areSetsEqual:b,createIsNestedEqual:t}),O=s(x),P=s(n(x,{createIsNestedEqual:function(){return o}})),j=s(w),S=s(n(w,{createIsNestedEqual:function(){return o}}));e.circularDeepEqual=function(e,t){return j(e,t,new WeakMap)},e.circularShallowEqual=function(e,t){return S(e,t,new WeakMap)},e.createCustomCircularEqual=function(e){var t=s(n(w,e(w)));return function(e,r,n){return void 0===n&&(n=new WeakMap),t(e,r,n)}},e.createCustomEqual=function(e){return s(n(x,e(x)))},e.deepEqual=function(e,t){return O(e,t,void 0)},e.sameValueZeroEqual=o,e.shallowEqual=function(e,t){return P(e,t,void 0)},Object.defineProperty(e,"__esModule",{value:!0})})(t)},77283:(e,t,r)=>{"use strict";r.d(t,{h:()=>y});var n=r(12115),i=r(52596),a=r(70788),o=r(25641),l=r(16377),s=r(93389);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var c=(e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),359.999),f=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:l,cornerRadius:s,cornerIsExternal:u}=e,c=s*(l?1:-1)+n,f=Math.asin(s/c)/o.Kg,d=u?i:i+a*f,p=(0,o.IZ)(t,r,c,d);return{center:p,circleTangency:(0,o.IZ)(t,r,n,d),lineTangency:(0,o.IZ)(t,r,c*Math.cos(f*o.Kg),u?i-a*f:i),theta:f}},d=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:l}=e,s=c(a,l),u=a+s,f=(0,o.IZ)(t,r,i,a),d=(0,o.IZ)(t,r,i,u),p="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(s)>180),",").concat(+(a>u),",\n    ").concat(d.x,",").concat(d.y,"\n  ");if(n>0){var h=(0,o.IZ)(t,r,n,a),y=(0,o.IZ)(t,r,n,u);p+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(s)>180),",").concat(+(a<=u),",\n            ").concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(t,",").concat(r," Z");return p},p=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:s,startAngle:u,endAngle:c}=e,p=(0,l.sA)(c-u),{circleTangency:h,lineTangency:y,theta:g}=f({cx:t,cy:r,radius:i,angle:u,sign:p,cornerRadius:a,cornerIsExternal:s}),{circleTangency:v,lineTangency:m,theta:b}=f({cx:t,cy:r,radius:i,angle:c,sign:-p,cornerRadius:a,cornerIsExternal:s}),x=s?Math.abs(u-c):Math.abs(u-c)-g-b;if(x<0)return o?"M ".concat(y.x,",").concat(y.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):d({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:c});var w="M ".concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(x>180),",").concat(+(p<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:P,theta:j}=f({cx:t,cy:r,radius:n,angle:u,sign:p,isExternal:!0,cornerRadius:a,cornerIsExternal:s}),{circleTangency:S,lineTangency:E,theta:M}=f({cx:t,cy:r,radius:n,angle:c,sign:-p,isExternal:!0,cornerRadius:a,cornerIsExternal:s}),A=s?Math.abs(u-c):Math.abs(u-c)-j-M;if(A<0&&0===a)return"".concat(w,"L").concat(t,",").concat(r,"Z");w+="L".concat(E.x,",").concat(E.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(S.x,",").concat(S.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(A>180),",").concat(+(p>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(P.x,",").concat(P.y,"Z")}else w+="L".concat(t,",").concat(r,"Z");return w},h={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},y=e=>{var t,r=(0,s.e)(e,h),{cx:o,cy:c,innerRadius:f,outerRadius:y,cornerRadius:g,forceCornerRadius:v,cornerIsExternal:m,startAngle:b,endAngle:x,className:w}=r;if(y<f||b===x)return null;var O=(0,i.$)("recharts-sector",w),P=y-f,j=(0,l.F4)(g,P,0,!0);return t=j>0&&360>Math.abs(b-x)?p({cx:o,cy:c,innerRadius:f,outerRadius:y,cornerRadius:Math.min(j,P/2),forceCornerRadius:v,cornerIsExternal:m,startAngle:b,endAngle:x}):d({cx:o,cy:c,innerRadius:f,outerRadius:y,startAngle:b,endAngle:x}),n.createElement("path",u({},(0,a.J9)(r,!0),{className:O,d:t}))}},78359:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(40220),i=r(14986),a=r(68179);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},78503:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=f(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(n,a,o):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(12115)),i=r(76725),a=c(r(20749)),o=r(50507),l=r(21383),s=c(r(15073)),u=c(r(61299));function c(e){return e&&e.__esModule?e:{default:e}}function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(f=function(e){return e?r:t})(e)}function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:String(n))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let p="react-grid-layout",h=!1;try{h=/firefox/i.test(navigator.userAgent)}catch(e){}class y extends n.Component{constructor(){super(...arguments),d(this,"state",{activeDrag:null,layout:(0,o.synchronizeLayoutWithChildren)(this.props.layout,this.props.children,this.props.cols,(0,o.compactType)(this.props),this.props.allowOverlap),mounted:!1,oldDragItem:null,oldLayout:null,oldResizeItem:null,resizing:!1,droppingDOMNode:null,children:[]}),d(this,"dragEnterCounter",0),d(this,"onDragStart",(e,t,r,n)=>{let{e:i,node:a}=n,{layout:l}=this.state,s=(0,o.getLayoutItem)(l,e);if(!s)return;let u={w:s.w,h:s.h,x:s.x,y:s.y,placeholder:!0,i:e};return this.setState({oldDragItem:(0,o.cloneLayoutItem)(s),oldLayout:l,activeDrag:u}),this.props.onDragStart(l,s,s,null,i,a)}),d(this,"onDrag",(e,t,r,n)=>{let{e:i,node:a}=n,{oldDragItem:l}=this.state,{layout:s}=this.state,{cols:u,allowOverlap:c,preventCollision:f}=this.props,d=(0,o.getLayoutItem)(s,e);if(!d)return;let p={w:d.w,h:d.h,x:d.x,y:d.y,placeholder:!0,i:e};s=(0,o.moveElement)(s,d,t,r,!0,f,(0,o.compactType)(this.props),u,c),this.props.onDrag(s,l,d,p,i,a),this.setState({layout:c?s:(0,o.compact)(s,(0,o.compactType)(this.props),u),activeDrag:p})}),d(this,"onDragStop",(e,t,r,n)=>{let{e:i,node:a}=n;if(!this.state.activeDrag)return;let{oldDragItem:l}=this.state,{layout:s}=this.state,{cols:u,preventCollision:c,allowOverlap:f}=this.props,d=(0,o.getLayoutItem)(s,e);if(!d)return;s=(0,o.moveElement)(s,d,t,r,!0,c,(0,o.compactType)(this.props),u,f);let p=f?s:(0,o.compact)(s,(0,o.compactType)(this.props),u);this.props.onDragStop(p,l,d,null,i,a);let{oldLayout:h}=this.state;this.setState({activeDrag:null,layout:p,oldDragItem:null,oldLayout:null}),this.onLayoutMaybeChanged(p,h)}),d(this,"onResizeStart",(e,t,r,n)=>{let{e:i,node:a}=n,{layout:l}=this.state,s=(0,o.getLayoutItem)(l,e);s&&(this.setState({oldResizeItem:(0,o.cloneLayoutItem)(s),oldLayout:this.state.layout,resizing:!0}),this.props.onResizeStart(l,s,s,null,i,a))}),d(this,"onResize",(e,t,r,n)=>{let i,a,l,{e:s,node:u,size:c,handle:f}=n,{oldResizeItem:d}=this.state,{layout:p}=this.state,{cols:h,preventCollision:y,allowOverlap:g}=this.props,v=!1,[m,b]=(0,o.withLayoutItem)(p,e,e=>(a=e.x,l=e.y,-1!==["sw","w","nw","n","ne"].indexOf(f)&&(-1!==["sw","nw","w"].indexOf(f)&&(a=e.x+(e.w-t),t=e.x!==a&&a<0?e.w:t,a=a<0?0:a),-1!==["ne","n","nw"].indexOf(f)&&(l=e.y+(e.h-r),r=e.y!==l&&l<0?e.h:r,l=l<0?0:l),v=!0),y&&!g&&(0,o.getAllCollisions)(p,{...e,w:t,h:r,x:a,y:l}).filter(t=>t.i!==e.i).length>0&&(l=e.y,r=e.h,a=e.x,t=e.w,v=!1),e.w=t,e.h=r,e));if(!b)return;i=m,v&&(i=(0,o.moveElement)(m,b,a,l,!0,this.props.preventCollision,(0,o.compactType)(this.props),h,g));let x={w:b.w,h:b.h,x:b.x,y:b.y,static:!0,i:e};this.props.onResize(i,d,b,x,s,u),this.setState({layout:g?i:(0,o.compact)(i,(0,o.compactType)(this.props),h),activeDrag:x})}),d(this,"onResizeStop",(e,t,r,n)=>{let{e:i,node:a}=n,{layout:l,oldResizeItem:s}=this.state,{cols:u,allowOverlap:c}=this.props,f=(0,o.getLayoutItem)(l,e),d=c?l:(0,o.compact)(l,(0,o.compactType)(this.props),u);this.props.onResizeStop(d,s,f,null,i,a);let{oldLayout:p}=this.state;this.setState({activeDrag:null,layout:d,oldResizeItem:null,oldLayout:null,resizing:!1}),this.onLayoutMaybeChanged(d,p)}),d(this,"onDragOver",e=>{if(e.preventDefault(),e.stopPropagation(),h&&!e.nativeEvent.target?.classList.contains(p))return!1;let{droppingItem:t,onDropDragOver:r,margin:i,cols:a,rowHeight:o,maxRows:s,width:u,containerPadding:c,transformScale:f}=this.props,d=r?.(e);if(!1===d)return this.state.droppingDOMNode&&this.removeDroppingPlaceholder(),!1;let y={...t,...d},{layout:g}=this.state,v=e.currentTarget.getBoundingClientRect(),m=e.clientX-v.left,b=e.clientY-v.top,x={left:m/f,top:b/f,e};if(this.state.droppingDOMNode){if(this.state.droppingPosition){let{left:e,top:t}=this.state.droppingPosition;(e!=m||t!=b)&&this.setState({droppingPosition:x})}}else{let e=(0,l.calcXY)({cols:a,margin:i,maxRows:s,rowHeight:o,containerWidth:u,containerPadding:c||i},b,m,y.w,y.h);this.setState({droppingDOMNode:n.createElement("div",{key:y.i}),droppingPosition:x,layout:[...g,{...y,x:e.x,y:e.y,static:!1,isDraggable:!0}]})}}),d(this,"removeDroppingPlaceholder",()=>{let{droppingItem:e,cols:t}=this.props,{layout:r}=this.state,n=(0,o.compact)(r.filter(t=>t.i!==e.i),(0,o.compactType)(this.props),t,this.props.allowOverlap);this.setState({layout:n,droppingDOMNode:null,activeDrag:null,droppingPosition:void 0})}),d(this,"onDragLeave",e=>{e.preventDefault(),e.stopPropagation(),this.dragEnterCounter--,0===this.dragEnterCounter&&this.removeDroppingPlaceholder()}),d(this,"onDragEnter",e=>{e.preventDefault(),e.stopPropagation(),this.dragEnterCounter++}),d(this,"onDrop",e=>{e.preventDefault(),e.stopPropagation();let{droppingItem:t}=this.props,{layout:r}=this.state,n=r.find(e=>e.i===t.i);this.dragEnterCounter=0,this.removeDroppingPlaceholder(),this.props.onDrop(r,n,e)})}componentDidMount(){this.setState({mounted:!0}),this.onLayoutMaybeChanged(this.state.layout,this.props.layout)}static getDerivedStateFromProps(e,t){let r;return t.activeDrag?null:((0,i.deepEqual)(e.layout,t.propsLayout)&&e.compactType===t.compactType?(0,o.childrenEqual)(e.children,t.children)||(r=t.layout):r=e.layout,r)?{layout:(0,o.synchronizeLayoutWithChildren)(r,e.children,e.cols,(0,o.compactType)(e),e.allowOverlap),compactType:e.compactType,children:e.children,propsLayout:e.layout}:null}shouldComponentUpdate(e,t){return this.props.children!==e.children||!(0,o.fastRGLPropsEqual)(this.props,e,i.deepEqual)||this.state.activeDrag!==t.activeDrag||this.state.mounted!==t.mounted||this.state.droppingPosition!==t.droppingPosition}componentDidUpdate(e,t){if(!this.state.activeDrag){let e=this.state.layout,r=t.layout;this.onLayoutMaybeChanged(e,r)}}containerHeight(){if(!this.props.autoSize)return;let e=(0,o.bottom)(this.state.layout),t=this.props.containerPadding?this.props.containerPadding[1]:this.props.margin[1];return e*this.props.rowHeight+(e-1)*this.props.margin[1]+2*t+"px"}onLayoutMaybeChanged(e,t){t||(t=this.state.layout),(0,i.deepEqual)(t,e)||this.props.onLayoutChange(e)}placeholder(){let{activeDrag:e}=this.state;if(!e)return null;let{width:t,cols:r,margin:i,containerPadding:a,rowHeight:o,maxRows:l,useCSSTransforms:u,transformScale:c}=this.props;return n.createElement(s.default,{w:e.w,h:e.h,x:e.x,y:e.y,i:e.i,className:`react-grid-placeholder ${this.state.resizing?"placeholder-resizing":""}`,containerWidth:t,cols:r,margin:i,containerPadding:a||i,maxRows:l,rowHeight:o,isDraggable:!1,isResizable:!1,isBounded:!1,useCSSTransforms:u,transformScale:c},n.createElement("div",null))}processGridItem(e,t){if(!e||!e.key)return;let r=(0,o.getLayoutItem)(this.state.layout,String(e.key));if(!r)return null;let{width:i,cols:a,margin:l,containerPadding:u,rowHeight:c,maxRows:f,isDraggable:d,isResizable:p,isBounded:h,useCSSTransforms:y,transformScale:g,draggableCancel:v,draggableHandle:m,resizeHandles:b,resizeHandle:x}=this.props,{mounted:w,droppingPosition:O}=this.state,P="boolean"==typeof r.isDraggable?r.isDraggable:!r.static&&d,j="boolean"==typeof r.isResizable?r.isResizable:!r.static&&p,S=r.resizeHandles||b,E=P&&h&&!1!==r.isBounded;return n.createElement(s.default,{containerWidth:i,cols:a,margin:l,containerPadding:u||l,maxRows:f,rowHeight:c,cancel:v,handle:m,onDragStop:this.onDragStop,onDragStart:this.onDragStart,onDrag:this.onDrag,onResizeStart:this.onResizeStart,onResize:this.onResize,onResizeStop:this.onResizeStop,isDraggable:P,isResizable:j,isBounded:E,useCSSTransforms:y&&w,usePercentages:!w,transformScale:g,w:r.w,h:r.h,x:r.x,y:r.y,i:r.i,minH:r.minH,minW:r.minW,maxH:r.maxH,maxW:r.maxW,static:r.static,droppingPosition:t?O:void 0,resizeHandles:S,resizeHandle:x},e)}render(){let{className:e,style:t,isDroppable:r,innerRef:i}=this.props,l=(0,a.default)(p,e),s={height:this.containerHeight(),...t};return n.createElement("div",{ref:i,className:l,style:s,onDrop:r?this.onDrop:o.noop,onDragLeave:r?this.onDragLeave:o.noop,onDragEnter:r?this.onDragEnter:o.noop,onDragOver:r?this.onDragOver:o.noop},n.Children.map(this.props.children,e=>this.processGridItem(e)),r&&this.state.droppingDOMNode&&this.processGridItem(this.state.droppingDOMNode,!0),this.placeholder())}}t.default=y,d(y,"displayName","ReactGridLayout"),d(y,"propTypes",u.default),d(y,"defaultProps",{autoSize:!0,cols:12,className:"",style:{},draggableHandle:"",draggableCancel:"",containerPadding:null,rowHeight:150,maxRows:1/0,layout:[],margin:[10,10],isBounded:!1,isDraggable:!0,isResizable:!0,allowOverlap:!1,isDroppable:!1,useCSSTransforms:!0,transformScale:1,verticalCompact:!0,compactType:"vertical",preventCollision:!1,droppingItem:{i:"__dropping-elem__",h:1,w:1},resizeHandles:["se"],onLayoutChange:o.noop,onDragStart:o.noop,onDrag:o.noop,onDragStop:o.noop,onResizeStart:o.noop,onResize:o.noop,onResizeStop:o.noop,onDrop:o.noop,onDropDragOver:o.noop})},78673:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(30564);t.debounce=function(e,t=0,r={}){let i;"object"!=typeof r&&(r={});let{leading:a=!1,trailing:o=!0,maxWait:l}=r,s=[,,];a&&(s[0]="leading"),o&&(s[1]="trailing");let u=null,c=n.debounce(function(...t){i=e.apply(this,t),u=null},t,{edges:s}),f=function(...t){return null!=l&&(null===u&&(u=Date.now()),Date.now()-u>=l)?(i=e.apply(this,t),u=Date.now(),c.cancel(),c.schedule(),i):(c.apply(this,t),i)};return f.cancel=c.cancel,f.flush=()=>(c.flush(),i),f}},78892:(e,t,r)=>{"use strict";function n(e){return Number.isFinite(e)}function i(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}r.d(t,{F:()=>i,H:()=>n})},79020:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,_:()=>c});var n=r(12115),i=r(71807),a=r(97238),o=r(81971),l=r(32634),s=()=>{};function u(e){var{legendPayload:t}=e,r=(0,o.j)(),a=(0,i.r)();return(0,n.useEffect)(()=>a?s:(r((0,l.Lx)(t)),()=>{r((0,l.u3)(t))}),[r,a,t]),null}function c(e){var{legendPayload:t}=e,r=(0,o.j)(),i=(0,o.G)(a.fz);return(0,n.useEffect)(()=>"centric"!==i&&"radial"!==i?s:(r((0,l.Lx)(t)),()=>{r((0,l.u3)(t))}),[r,i,t]),null}},79095:(e,t,r)=>{"use strict";r.d(t,{E:()=>k});var n=r(12115),i=r(52596),a=r(16377),o=r(41643),l=r(70788),s=r(46605),u=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,c=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,d=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,p={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},h=Object.keys(p);class y{static parse(e){var t,[,r,n]=null!=(t=d.exec(e))?t:[];return new y(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,a.M8)(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,a.M8)(e)&&(this.unit=""),""===t||f.test(t)||(this.num=NaN,this.unit=""),h.includes(t)&&(this.num=e*p[t],this.unit="px")}}function g(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!=(r=u.exec(t))?r:[],o=y.parse(null!=n?n:""),l=y.parse(null!=a?a:""),s="*"===i?o.multiply(l):o.divide(l);if(s.isNaN())return"NaN";t=t.replace(u,s.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var f,[,d,p,h]=null!=(f=c.exec(t))?f:[],g=y.parse(null!=d?d:""),v=y.parse(null!=h?h:""),m="+"===p?g.add(v):g.subtract(v);if(m.isNaN())return"NaN";t=t.replace(c,m.toString())}return t}var v=/\(([^()]*)\)/;function m(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=v.exec(r));){var[,n]=t;r=r.replace(v,g(n))}return r}(t),t=g(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],x=["dx","dy","angle","className","breakAll"];function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var P=/[ \f\n\r\t\v\u2028\u2029]+/,j=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];(0,a.uy)(t)||(i=r?t.toString().split(""):t.toString().split(P));var o=i.map(e=>({word:e,width:(0,s.P)(e,n).width})),l=r?0:(0,s.P)("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:l}}catch(e){return null}},S=(e,t,r,n,i)=>{var o,{maxLines:l,children:s,style:u,breakAll:c}=e,f=(0,a.Et)(l),d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},p=d(t),h=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!f||i||!(p.length>l||h(p).width>Number(n)))return p;for(var y=e=>{var t=d(j({breakAll:c,style:u,children:s.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>l||h(t).width>Number(n),t]},g=0,v=s.length-1,m=0;g<=v&&m<=s.length-1;){var b=Math.floor((g+v)/2),[x,w]=y(b-1),[O]=y(b);if(x||O||(g=b+1),x&&O&&(v=b-1),!x&&O){o=w;break}m++}return o||p},E=e=>[{words:(0,a.uy)(e)?[]:e.toString().split(P)}],M=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:l}=e;if((t||r)&&!o.m.isSsr){var s=j({breakAll:a,children:n,style:i});if(!s)return E(n);var{wordsWithComputedWidth:u,spaceWidth:c}=s;return S({breakAll:a,children:n,maxLines:l,style:i},u,c,t,r)}return E(n)},A="#808080",k=(0,n.forwardRef)((e,t)=>{var r,{x:o=0,y:s=0,lineHeight:u="1em",capHeight:c="0.71em",scaleToFit:f=!1,textAnchor:d="start",verticalAnchor:p="end",fill:h=A}=e,y=O(e,b),g=(0,n.useMemo)(()=>M({breakAll:y.breakAll,children:y.children,maxLines:y.maxLines,scaleToFit:f,style:y.style,width:y.width}),[y.breakAll,y.children,y.maxLines,f,y.style,y.width]),{dx:v,dy:P,angle:j,className:S,breakAll:E}=y,k=O(y,x);if(!(0,a.vh)(o)||!(0,a.vh)(s))return null;var _=o+((0,a.Et)(v)?v:0),D=s+((0,a.Et)(P)?P:0);switch(p){case"start":r=m("calc(".concat(c,")"));break;case"middle":r=m("calc(".concat((g.length-1)/2," * -").concat(u," + (").concat(c," / 2))"));break;default:r=m("calc(".concat(g.length-1," * -").concat(u,")"))}var C=[];if(f){var T=g[0].width,{width:N}=y;C.push("scale(".concat((0,a.Et)(N)?N/T:1,")"))}return j&&C.push("rotate(".concat(j,", ").concat(_,", ").concat(D,")")),C.length&&(k.transform=C.join(" ")),n.createElement("text",w({},(0,l.J9)(k,!0),{ref:t,x:_,y:D,className:(0,i.$)("recharts-text",S),textAnchor:d,fill:h.includes("url")?A:h}),g.map((e,t)=>{var i=e.words.join(E?"":" ");return n.createElement("tspan",{x:_,dy:0===t?r:u,key:"".concat(i,"-").concat(t)},i)}))});k.displayName="Text"},79399:(e,t,r)=>{"use strict";var n=r(72948);function i(){}function a(){}a.resetWarningCache=i,e.exports=function(){function e(e,t,r,i,a,o){if(o!==n){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:i};return r.PropTypes=r,r}},79584:(e,t,r)=>{"use strict";r.d(t,{u:()=>O});var n=r(12115),i=r(95672),a=r.n(i),o=r(52596);function l(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var s=r(2348),u=r(79095),c=r(60379),f=r(16377),d=r(43597),p=r(70788),h=r(19035),y=["viewBox"],g=["viewBox"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function w(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class O extends n.Component{shouldComponentUpdate(e,t){var{viewBox:r}=e,n=x(e,y),i=this.props,{viewBox:a}=i,o=x(i,g);return!l(r,a)||!l(n,o)||!l(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:s,width:u,height:c,orientation:d,tickSize:p,mirror:h,tickMargin:y}=this.props,g=h?-1:1,v=e.tickSize||p,m=(0,f.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(d){case"top":t=r=e.coordinate,o=(n=(i=s+!h*c)-g*v)-g*y,a=m;break;case"left":n=i=e.coordinate,a=(t=(r=l+!h*u)-g*v)-g*y,o=m;break;case"right":n=i=e.coordinate,a=(t=(r=l+h*u)+g*v)+g*y,o=m;break;default:t=r=e.coordinate,o=(n=(i=s+h*c)+g*v)+g*y,a=m}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:i,orientation:l,mirror:s,axisLine:u}=this.props,c=b(b(b({},(0,p.J9)(this.props,!1)),(0,p.J9)(u,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var f=+("top"===l&&!s||"bottom"===l&&s);c=b(b({},c),{},{x1:e,y1:t+f*i,x2:e+r,y2:t+f*i})}else{var d=+("left"===l&&!s||"right"===l&&s);c=b(b({},c),{},{x1:e+d*r,y1:t,x2:e+d*r,y2:t+i})}return n.createElement("line",v({},c,{className:(0,o.$)("recharts-cartesian-axis-line",a()(u,"className"))}))}static renderTickItem(e,t,r){var i,a=(0,o.$)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))i=n.cloneElement(e,b(b({},t),{},{className:a}));else if("function"==typeof e)i=e(b(b({},t),{},{className:a}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=(0,o.$)(l,e.className)),i=n.createElement(u.E,v({},t,{className:l}),r)}return i}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:l,tick:u,tickFormatter:c,unit:f}=this.props,y=(0,h.f)(b(b({},this.props),{},{ticks:r}),e,t),g=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),x=(0,p.J9)(this.props,!1),w=(0,p.J9)(u,!1),P=b(b({},x),{},{fill:"none"},(0,p.J9)(i,!1)),j=y.map((e,t)=>{var{line:r,tick:p}=this.getTickLineCoord(e),h=b(b(b(b({textAnchor:g,verticalAnchor:m},x),{},{stroke:"none",fill:l},w),p),{},{index:t,payload:e,visibleTicksCount:y.length,tickFormatter:c});return n.createElement(s.W,v({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,d.XC)(this.props,e,t)),i&&n.createElement("line",v({},P,r,{className:(0,o.$)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),u&&O.renderTickItem(u,h,"".concat("function"==typeof c?c(e.value,t):e.value).concat(f||"")))});return j.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},j):null}render(){var{axisLine:e,width:t,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:l}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(s.W,{className:(0,o.$)("recharts-cartesian-axis",i),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),c.J.renderCallByParent(this.props))}constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}w(O,"displayName","CartesianAxis"),w(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},80885:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},80931:(e,t,r)=>{e.exports=r(86006).isPlainObject},81571:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(72465),i=r(62194),a=r(14804),o=r(24517);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},81971:(e,t,r)=>{"use strict";r.d(t,{G:()=>f,j:()=>l});var n=r(45643),i=r(12115),a=r(15064),o=e=>e,l=()=>{var e=(0,i.useContext)(a.E);return e?e.store.dispatch:o},s=()=>{},u=()=>s,c=(e,t)=>e===t;function f(e){var t=(0,i.useContext)(a.E);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:u,t?t.store.getState:s,t?t.store.getState:s,t?e:s,c)}},82384:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},82396:(e,t,r)=>{"use strict";r.d(t,{P:()=>h});var n=r(12115),i=r(49972),a=r(59068),o=r(95932),l=r(73433),s=r(33725),u=r(93389),c=r(78892),f=["width","height"];function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var p={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},h=(0,n.forwardRef)(function(e,t){var r,h=(0,u.e)(e.categoricalChartProps,p),{width:y,height:g}=h,v=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(h,f);if(!(0,c.F)(y)||!(0,c.F)(g))return null;var{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,categoricalChartProps:O}=e;return n.createElement(i.J,{preloadedState:{options:{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,eventEmitter:void 0}},reduxStoreName:null!=(r=O.id)?r:m},n.createElement(a.TK,{chartData:O.data}),n.createElement(o.s,{width:y,height:g,layout:h.layout,margin:h.margin}),n.createElement(l.p,{accessibilityLayer:h.accessibilityLayer,barCategoryGap:h.barCategoryGap,maxBarSize:h.maxBarSize,stackOffset:h.stackOffset,barGap:h.barGap,barSize:h.barSize,syncId:h.syncId,syncMethod:h.syncMethod,className:h.className}),n.createElement(s.L,d({},v,{width:y,height:g,ref:t})))})},82661:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||e,o),s=r?r+t:t;return e._events[s]?e._events[s].fn?e._events[s]=[e._events[s],l]:e._events[s].push(l):(e._events[s]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var s,u,c=this._events[l],f=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),f){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,n),!0;case 4:return c.fn.call(c.context,t,n,i),!0;case 5:return c.fn.call(c.context,t,n,i,a),!0;case 6:return c.fn.call(c.context,t,n,i,a,o),!0}for(u=1,s=Array(f-1);u<f;u++)s[u-1]=arguments[u];c.fn.apply(c.context,s)}else{var d,p=c.length;for(u=0;u<p;u++)switch(c[u].once&&this.removeListener(e,c[u].fn,void 0,!0),f){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,t);break;case 3:c[u].fn.call(c[u].context,t,n);break;case 4:c[u].fn.call(c[u].context,t,n,i);break;default:if(!s)for(d=1,s=Array(f-1);d<f;d++)s[d-1]=arguments[d];c[u].fn.apply(c[u].context,s)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var s=0,u=[],c=l.length;s<c;s++)(l[s].fn!==t||i&&!l[s].once||n&&l[s].context!==n)&&u.push(l[s]);u.length?this._events[a]=1===u.length?u[0]:u:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},82962:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(78673);t.throttle=function(e,t=0,r={}){"object"!=typeof r&&(r={});let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,trailing:a,maxWait:t})}},83540:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(52596),i=r(12115),a=r(20400),o=r.n(a),l=r(16377),s=r(675);function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f=(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:u="100%",height:f="100%",minWidth:d=0,minHeight:p,maxHeight:h,children:y,debounce:g=0,id:v,className:m,onResize:b,style:x={}}=e,w=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(t,()=>w.current);var[P,j]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),S=(0,i.useCallback)((e,t)=>{j(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;S(r,n),null==(t=O.current)||t.call(O,r,n)};g>0&&(e=o()(e,g,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=w.current.getBoundingClientRect();return S(r,n),t.observe(w.current),()=>{t.disconnect()}},[S,g]);var E=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=P;if(e<0||t<0)return null;(0,s.R)((0,l._3)(u)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",u,f),(0,s.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(u)?e:u,a=(0,l._3)(f)?t:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),h&&a>h&&(a=h)),(0,s.R)(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,u,f,d,p,r),i.Children.map(y,e=>(0,i.cloneElement)(e,{width:n,height:a,style:c({height:"100%",width:"100%",maxHeight:a,maxWidth:n},e.props.style)}))},[r,y,f,h,p,d,P,u]);return i.createElement("div",{id:v?"".concat(v):void 0,className:(0,n.$)("recharts-responsive-container",m),style:c(c({},x),{},{width:u,height:f,minWidth:d,minHeight:p,maxHeight:h}),ref:w},E)})},83616:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},83949:(e,t,r)=>{e.exports=r(49901).range},84421:(e,t,r)=>{"use strict";r.d(t,{F0:()=>n,tQ:()=>a,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",a=60},84895:(e,t,r)=>{"use strict";t.__esModule=!0,t.cloneElement=function(e,t){return t.style&&e.props.style&&(t.style=a(a({},e.props.style),t.style)),t.className&&e.props.className&&(t.className=e.props.className+" "+t.className),n.default.cloneElement(e,t)};var n=function(e){return e&&e.__esModule?e:{default:e}}(r(12115));function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}},85146:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});var n=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})}},85252:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(22520),i=r(2767);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},85654:(e,t,r)=>{"use strict";function n(e){return function(){return e}}r.d(t,{A:()=>n})},86006:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},90170:(e,t,r)=>{"use strict";r.d(t,{r:()=>w});var n=r(12115),i=r(46641),a=r(49972),o=r(59068),l=r(95932),s=r(73433),u=r(81971),c=r(2267);function f(e){var t=(0,u.j)();return(0,n.useEffect)(()=>{t((0,c.U)(e))},[t,e]),null}var d=r(33725),p=r(93389),h=r(78892),y=["width","height","layout"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},m=(0,n.forwardRef)(function(e,t){var r,i=(0,p.e)(e.categoricalChartProps,v),{width:u,height:c,layout:m}=i,b=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,y);if(!(0,h.F)(u)||!(0,h.F)(c))return null;var{chartName:x,defaultTooltipEventType:w,validateTooltipEventTypes:O,tooltipPayloadSearcher:P}=e;return n.createElement(a.J,{preloadedState:{options:{chartName:x,defaultTooltipEventType:w,validateTooltipEventTypes:O,tooltipPayloadSearcher:P,eventEmitter:void 0}},reduxStoreName:null!=(r=i.id)?r:x},n.createElement(o.TK,{chartData:i.data}),n.createElement(l.s,{width:u,height:c,layout:m,margin:i.margin}),n.createElement(s.p,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(f,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),n.createElement(d.L,g({width:u,height:c},b,{ref:t})))}),b=["item"],x={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},w=(0,n.forwardRef)((e,t)=>{var r=(0,p.e)(e,x);return n.createElement(m,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:b,tooltipPayloadSearcher:i.uN,categoricalChartProps:r,ref:t})})},91165:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var n=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}}},93205:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(14545),i=r(98412),a=r(50177),o=r(64072);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},93262:(e,t,r)=>{"use strict";r.d(t,{W:()=>y});var n=r(12115),i=r(43597),a=r(70788),o=r(51172),l=r(2348),s=r(71420),u=r(16377),c=r(81971),f=r(20215);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h=e=>{var t,{point:r,childIndex:s,mainColor:u,activeDot:c,dataKey:f}=e;if(!1===c||null==r.x||null==r.y)return null;var d=p(p({index:s,dataKey:f,cx:r.x,cy:r.y,r:4,fill:null!=u?u:"none",strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,a.J9)(c,!1)),(0,i._U)(c));return t=(0,n.isValidElement)(c)?(0,n.cloneElement)(c,d):"function"==typeof c?c(d):n.createElement(o.c,d),n.createElement(l.W,{className:"recharts-active-dot"},t)};function y(e){var t,{points:r,mainColor:n,activeDot:i,itemDataKey:a}=e,o=(0,s.E)(),l=(0,c.G)(f.A2),d=(0,c.G)(f.BZ);if(!l)return null;var p=o.dataKey;if(p&&!o.allowDuplicatedCategory){var y="function"==typeof p?e=>p(e.payload):"payload.".concat(p);t=(0,u.eP)(r,y,d)}else t=null==r?void 0:r[Number(l)];return(0,u.uy)(t)?null:h({point:t,childIndex:Number(l),mainColor:n,dataKey:a,activeDot:i})}},93389:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}r.d(t,{e:()=>i})},93504:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var n=r(12115),i=r(46641),a=r(82396),o=["axis"],l=(0,n.forwardRef)((e,t)=>n.createElement(a.P,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:o,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))},94685:(e,t,r)=>{"use strict";r.d(t,{YF:()=>u,dj:()=>c,fP:()=>f,ky:()=>s});var n=r(5710),i=r(34890),a=r(63027),o=r(96523),l=r(91165),s=(0,n.VP)("mouseClick"),u=(0,n.Nc)();u.startListening({actionCreator:s,effect:(e,t)=>{var r=e.payload,n=(0,a.g)(t.getState(),(0,l.w)(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch((0,i.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var c=(0,n.VP)("mouseMove"),f=(0,n.Nc)();f.startListening({actionCreator:c,effect:(e,t)=>{var r=e.payload,n=t.getState(),s=(0,o.au)(n,n.tooltip.settings.shared),u=(0,a.g)(n,(0,l.w)(r));"axis"===s&&((null==u?void 0:u.activeIndex)!=null?t.dispatch((0,i.Nt)({activeIndex:u.activeIndex,activeDataKey:void 0,activeCoordinate:u.activeCoordinate})):t.dispatch((0,i.xS)()))}})},94732:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>z,aX:()=>L,dS:()=>N,dp:()=>D,fW:()=>j,pg:()=>T,r1:()=>A,u9:()=>I,yn:()=>R});var n=r(68924),i=r(20241),a=r.n(i),o=r(81971),l=r(39827),s=r(16377),u=r(60356),c=r(20215),f=r(18478),d=r(97238),p=r(69449),h=r(2589),y=r(60530),g=r(11928),v=r(60841),m=r(64968),b=r(85146),x=r(46670),w=r(75714);function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function P(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var j=()=>(0,o.G)(f.iO),S=(e,t)=>t,E=(e,t,r)=>r,M=(e,t,r,n)=>n,A=(0,n.Mz)(c.R4,e=>a()(e,e=>e.coordinate)),k=(0,n.Mz)([w.J,S,E,M],g.i),_=(0,n.Mz)([k,c.n4],v.P),D=(e,t,r)=>{if(null!=t){var n=(0,w.J)(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},C=(0,n.Mz)([w.J,S,E,M],b.q),T=(0,n.Mz)([h.Lp,h.A$,d.fz,p.GO,c.R4,M,C,x.x],m.o),N=(0,n.Mz)([k,T],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),z=(0,n.Mz)(c.R4,_,y.E),I=(0,n.Mz)([C,_,u.LF,c.Dn,z,x.x,S],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:u,computedData:c,dataStartIndex:f,dataEndIndex:d}=r;return e.reduce((e,r)=>{var p,h,y,g,v,{dataDefinedOnItem:m,settings:b}=r,x=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((p=m,h=u,null!=p?p:h),f,d),w=null!=(y=null==b?void 0:b.dataKey)?y:null==n?void 0:n.dataKey,O=null==b?void 0:b.nameKey;return Array.isArray(g=null!=n&&n.dataKey&&!(null!=n&&n.allowDuplicatedCategory)&&Array.isArray(x)&&"axis"===o?(0,s.eP)(x,n.dataKey,i):a(x,t,c,O))?g.forEach(t=>{var r=P(P({},b),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,l.GF)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,l.kr)(t.payload,t.dataKey),name:t.name}))}):e.push((0,l.GF)({tooltipEntrySettings:b,dataKey:w,payload:g,value:(0,l.kr)(g,w),name:null!=(v=(0,l.kr)(g,O))?v:null==b?void 0:b.name})),e},[])}}),R=(0,n.Mz)([k],e=>({isActive:e.active,activeIndex:e.index})),L=(e,t,r,n,i,a,o,s)=>{if(e&&t&&n&&i&&a){var u=(0,l.r4)(e.chartX,e.chartY,t,r,s);if(u){var c=(0,l.SW)(u,t),f=(0,l.gH)(c,o,a,n,i),d=(0,l.bk)(t,a,f,u);return{activeIndex:String(f),activeCoordinate:d}}}}},94754:(e,t,r)=>{"use strict";r.d(t,{d:()=>C});var n=r(12115),i=r(675),a=r(16377),o=r(70788),l=r(39827),s=r(19035),u=r(79584),c=r(97238),f=r(14299),d=r(81971),p=r(71807),h=r(93389),y=["x1","y1","x2","y2","key"],g=["offset"],v=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var P=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:i,y:a,width:o,height:l,ry:s}=e;return n.createElement("rect",{x:i,y:a,ry:s,width:o,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function j(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:i,y1:a,x2:l,y2:s,key:u}=t,c=O(t,y),f=(0,o.J9)(c,!1),{offset:d}=f,p=O(f,g);r=n.createElement("line",w({},p,{x1:i,y1:a,x2:l,y2:s,fill:"none",key:u}))}return r}function S(e){var{x:t,width:r,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,s=O(e,v),u=a.map((e,n)=>j(i,x(x({},s),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function E(e){var{y:t,height:r,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,s=O(e,m),u=a.map((e,n)=>j(i,x(x({},s),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function M(e){var{horizontalFill:t,fillOpacity:r,x:i,y:a,width:o,height:l,horizontalPoints:s,horizontal:u=!0}=e;if(!u||!t||!t.length)return null;var c=s.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==c[0]&&c.unshift(0);var f=c.map((e,s)=>{var u=c[s+1]?c[s+1]-e:a+l-e;if(u<=0)return null;var f=s%t.length;return n.createElement("rect",{key:"react-".concat(s),y:e,x:i,height:u,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function A(e){var{vertical:t=!0,verticalFill:r,fillOpacity:i,x:a,y:o,width:l,height:s,verticalPoints:u}=e;if(!t||!r||!r.length)return null;var c=u.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==c[0]&&c.unshift(0);var f=c.map((e,t)=>{var u=c[t+1]?c[t+1]-e:a+l-e;if(u<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:u,height:s,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var k=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,s.f)(x(x(x({},u.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},_=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,s.f)(x(x(x({},u.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},D={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function C(e){var t=(0,c.yi)(),r=(0,c.rY)(),o=(0,c.hj)(),l=x(x({},(0,h.e)(e,D)),{},{x:(0,a.Et)(e.x)?e.x:o.left,y:(0,a.Et)(e.y)?e.y:o.top,width:(0,a.Et)(e.width)?e.width:o.width,height:(0,a.Et)(e.height)?e.height:o.height}),{xAxisId:s,yAxisId:u,x:y,y:g,width:v,height:m,syncWithTicks:b,horizontalValues:O,verticalValues:j}=l,C=(0,p.r)(),T=(0,d.G)(e=>(0,f.ZB)(e,"xAxis",s,C)),N=(0,d.G)(e=>(0,f.ZB)(e,"yAxis",u,C));if(!(0,a.Et)(v)||v<=0||!(0,a.Et)(m)||m<=0||!(0,a.Et)(y)||y!==+y||!(0,a.Et)(g)||g!==+g)return null;var z=l.verticalCoordinatesGenerator||k,I=l.horizontalCoordinatesGenerator||_,{horizontalPoints:R,verticalPoints:L}=l;if((!R||!R.length)&&"function"==typeof I){var H=O&&O.length,$=I({yAxis:N?x(x({},N),{},{ticks:H?O:N.ticks}):void 0,width:t,height:r,offset:o},!!H||b);(0,i.R)(Array.isArray($),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof $,"]")),Array.isArray($)&&(R=$)}if((!L||!L.length)&&"function"==typeof z){var B=j&&j.length,W=z({xAxis:T?x(x({},T),{},{ticks:B?j:T.ticks}):void 0,width:t,height:r,offset:o},!!B||b);(0,i.R)(Array.isArray(W),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof W,"]")),Array.isArray(W)&&(L=W)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(P,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(M,w({},l,{horizontalPoints:R})),n.createElement(A,w({},l,{verticalPoints:L})),n.createElement(S,w({},l,{offset:o,horizontalPoints:R,xAxis:T,yAxis:N})),n.createElement(E,w({},l,{offset:o,verticalPoints:L,xAxis:T,yAxis:N})))}C.displayName="CartesianGrid"},95672:(e,t,r)=>{e.exports=r(10921).get},95932:(e,t,r)=>{"use strict";r.d(t,{s:()=>l});var n=r(12115),i=r(71807),a=r(48627),o=r(81971);function l(e){var{layout:t,width:r,height:l,margin:s}=e,u=(0,o.j)(),c=(0,i.r)();return(0,n.useEffect)(()=>{c||(u((0,a.JK)(t)),u((0,a.gX)({width:r,height:l})),u((0,a.B_)(s)))},[u,c,t,r,l,s]),null}},96025:(e,t,r)=>{"use strict";r.d(t,{W:()=>v});var n=r(12115),i=r(52596),a=r(79584),o=r(81971),l=r(55306),s=r(14299),u=r(69449),c=r(71807),f=["dangerouslySetInnerHTML","ticks"];function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function h(e){var t=(0,o.j)();return(0,n.useEffect)(()=>(t((0,l.Vi)(e)),()=>{t((0,l.MC)(e))}),[e,t]),null}var y=e=>{var{xAxisId:t,className:r}=e,l=(0,o.G)(u.c2),d=(0,c.r)(),h="xAxis",y=(0,o.G)(e=>(0,s.iV)(e,h,t,d)),g=(0,o.G)(e=>(0,s.Zi)(e,h,t,d)),v=(0,o.G)(e=>(0,s.Lw)(e,t)),m=(0,o.G)(e=>(0,s.L$)(e,t));if(null==v||null==m)return null;var{dangerouslySetInnerHTML:b,ticks:x}=e,w=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,f);return n.createElement(a.u,p({},w,{scale:y,x:m.x,y:m.y,width:v.width,height:v.height,className:(0,i.$)("recharts-".concat(h," ").concat(h),r),viewBox:l,ticks:g}))},g=e=>{var t,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(h,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter}),n.createElement(y,e))};class v extends n.Component{render(){return n.createElement(g,this.props)}}d(v,"displayName","XAxis"),d(v,"defaultProps",{allowDataOverflow:s.PU.allowDataOverflow,allowDecimals:s.PU.allowDecimals,allowDuplicatedCategory:s.PU.allowDuplicatedCategory,height:s.PU.height,hide:!1,mirror:s.PU.mirror,orientation:s.PU.orientation,padding:s.PU.padding,reversed:s.PU.reversed,scale:s.PU.scale,tickCount:s.PU.tickCount,type:s.PU.type,xAxisId:0})},96523:(e,t,r)=>{"use strict";r.d(t,{$g:()=>o,Hw:()=>a,Td:()=>s,au:()=>l,xH:()=>i});var n=r(81971),i=e=>e.options.defaultTooltipEventType,a=e=>e.options.validateTooltipEventTypes;function o(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function l(e,t){return o(t,i(e),a(e))}function s(e){return(0,n.G)(t=>l(t,e))}},96752:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(81971),i=()=>(0,n.G)(e=>e.rootProps.accessibilityLayer)},96908:(e,t,r)=>{"use strict";r.d(t,{W:()=>a,h:()=>i});var n=r(68924),i=(0,n.Mz)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),a=(0,n.Mz)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},97238:(e,t,r)=>{"use strict";r.d(t,{WX:()=>h,fz:()=>p,hj:()=>c,rY:()=>d,sk:()=>s,yi:()=>f}),r(12115);var n=r(81971),i=r(69449),a=r(2589),o=r(71807),l=r(20972),s=()=>{var e,t=(0,o.r)(),r=(0,n.G)(i.Ds),a=(0,n.G)(l.U),s=null==(e=(0,n.G)(l.C))?void 0:e.padding;return t&&a&&s?{width:a.width-s.left-s.right,height:a.height-s.top-s.bottom,x:s.left,y:s.top}:r},u={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},c=()=>{var e;return null!=(e=(0,n.G)(i.GO))?e:u},f=()=>(0,n.G)(a.Lp),d=()=>(0,n.G)(a.A$),p=e=>e.layout.layoutType,h=()=>(0,n.G)(p)},98132:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(72744);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},98221:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},98412:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},99129:(e,t,r)=>{"use strict";r.d(t,{Cj:()=>a,Pg:()=>o,Ub:()=>l});var n=r(81971),i=r(34890),a=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.RD)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},o=e=>{var t=(0,n.j)();return(r,n)=>a=>{null==e||e(r,n,a),t((0,i.oP)())}},l=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.ML)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}}},99279:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},99445:(e,t,r)=>{"use strict";r.d(t,{Q:()=>l});var n=r(12115),i=r(46641),a=r(82396),o=["axis"],l=(0,n.forwardRef)((e,t)=>n.createElement(a.P,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:o,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))}}]);