"""Authentication Service

This module provides services for user authentication and management.
"""

import json
import logging
from datetime import datetime, timedelta
import os
import uuid
import secrets
from typing import Dict, List, Optional, Tuple, Union, Any

from fastapi import Depends, HTTPException, Request, status
from sqlalchemy import or_
from sqlalchemy.orm import Session
import httpx
from authlib.integrations.httpx_client import AsyncOAuth2Client

from app.models.user import User, UserCreate, UserUpdate, AuthProvider, UserResponse, Token, PasswordReset, AuthenticationResponse
from app.models.auth_db import UserDB, OAuthAccountDB, PasswordResetDB, EmailVerificationDB
from app.utils.db import get_db, json_serialize, json_deserialize
from app.utils.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    create_refresh_token,
    store_refresh_token,
    revoke_refresh_token,
    validate_refresh_token,
    blacklist_token,
    decode_token
)
from app.utils.exceptions import (
    TokenExpiredError,
    TokenRevokedError,
    TokenBlacklistedError,
    TokenNotFoundError,
    UserNotFoundError,
    UserInactiveError,
    TokenRotationError,
    EmailAlreadyExistsError
)
from app.utils.audit_logger import audit_logger
from app.config import settings
from app.services.supabase_service import SupabaseService

logger = logging.getLogger(__name__)

class AuthService:
    """Service for user authentication and management with enhanced Supabase integration."""

    def __init__(self, db: Session = Depends(get_db)):
        """Initialize the auth service with Supabase enhancements.

        Args:
            db: The database session
        """
        self.db = db
        self.supabase_service = SupabaseService()

        # Validate Supabase connection on initialization
        if not self.supabase_service.validate_supabase_connection():
            logger.warning("Supabase connection validation failed - falling back to SQLAlchemy only")
        else:
            logger.info("AuthService initialized with enhanced Supabase integration")
    
    def create_user(self, user_create: UserCreate) -> User:
        """Create a new user with comprehensive email uniqueness validation.

        Args:
            user_create: The user creation data

        Returns:
            User: The created user

        Raises:
            EmailAlreadyExistsError: If a user with the given email already exists
        """
        # Validate email uniqueness across all authentication methods
        self.validate_email_uniqueness(user_create.email, auth_provider="local")
        
        # Create user
        hashed_password = get_password_hash(user_create.password)
        user_id = str(uuid.uuid4())
        db_user = UserDB(
            id=user_id,
            email=user_create.email,
            hashed_password=hashed_password,
            full_name=user_create.full_name,
            is_active=True,
            is_email_verified=False,
            role="user",
            auth_provider="local",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        
        # Create email verification token
        verification_token = self._create_email_verification_token(db_user.id)
        
        # TODO: Send verification email
        
        # Convert to Pydantic model
        user = User(
            id=db_user.id,
            email=db_user.email,
            hashed_password=db_user.hashed_password,
            full_name=db_user.full_name,
            is_active=db_user.is_active,
            is_email_verified=db_user.is_email_verified,
            role=db_user.role,
            auth_provider=db_user.auth_provider,
            auth_provider_id=db_user.auth_provider_id,
            created_at=db_user.created_at,
            updated_at=db_user.updated_at,
            last_login=db_user.last_login,
            profile_picture=db_user.profile_picture,
            settings=json_deserialize(db_user.settings)
        )
        
        return user
    
    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authenticate a user with email and password.
        
        Args:
            email: The user's email
            password: The user's password
            
        Returns:
            Optional[User]: The authenticated user, or None if authentication failed
        """
        user = self.db.query(UserDB).filter(UserDB.email == email).first()
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        if not user.is_active:
            return None
            
        # Update last login
        user.last_login = datetime.utcnow()
        self.db.commit()
        
        # Convert to Pydantic model
        user_model = User(
            id=user.id,
            email=user.email,
            hashed_password=user.hashed_password,
            full_name=user.full_name,
            is_active=user.is_active,
            is_email_verified=user.is_email_verified,
            role=user.role,
            auth_provider=user.auth_provider,
            auth_provider_id=user.auth_provider_id,
            created_at=user.created_at,
            updated_at=user.updated_at,
            last_login=user.last_login,
            profile_picture=user.profile_picture,
            settings=json_deserialize(user.settings)
        )
        
        return user_model
    
    def create_tokens(self, user: User, remember_me: bool = False) -> Tuple[str, str, datetime, datetime]:
        """Create access and refresh tokens for a user with enterprise session management.
        
        Args:
            user: The user to create tokens for
            remember_me: Whether to create extended-lifetime tokens
            
        Returns:
            Tuple[str, str, datetime, datetime]: A tuple of (access_token, refresh_token, access_token_expires, refresh_token_expires)
        """
        # Determine token lifetimes based on remember_me preference
        if remember_me and settings.REMEMBER_ME_ENABLED:
            access_token_lifetime = timedelta(hours=8)  # Longer for remember me
            refresh_token_lifetime = timedelta(days=settings.REMEMBER_ME_TOKEN_EXPIRE_DAYS)
        else:
            access_token_lifetime = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            refresh_token_lifetime = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        
        # Create access token
        access_token_expires = datetime.utcnow() + access_token_lifetime
        access_token_data = {
            "sub": user.id,
            "email": user.email,
            "role": user.role,
            "remember_me": remember_me
        }
        access_token = create_access_token(
            data=access_token_data,
            expires_delta=access_token_lifetime
        )

        # Create refresh token
        refresh_token_expires = datetime.utcnow() + refresh_token_lifetime
        refresh_token_data = {
            "sub": user.id,
            "remember_me": remember_me
        }
        refresh_token = create_refresh_token(
            data=refresh_token_data,
            expires_delta=refresh_token_lifetime
        )
        
        # Store refresh token in database
        store_refresh_token(self.db, user.id, refresh_token, refresh_token_expires)
        
        return access_token, refresh_token, access_token_expires, refresh_token_expires

    def should_refresh_token(self, token_expires_at: datetime) -> bool:
        """Check if a token should be refreshed based on enterprise standards.
        
        Args:
            token_expires_at: When the token expires
            
        Returns:
            bool: True if the token should be refreshed
        """
        if not settings.AUTO_REFRESH_THRESHOLD_MINUTES:
            return False
            
        refresh_threshold = datetime.utcnow() + timedelta(minutes=settings.AUTO_REFRESH_THRESHOLD_MINUTES)
        return token_expires_at <= refresh_threshold

    def extend_session_if_needed(self, user: User, current_token_expires: datetime) -> Optional[Tuple[str, datetime]]:
        """Extend session if sliding sessions are enabled and token is close to expiry.
        
        Args:
            user: The current user
            current_token_expires: Current token expiration time
            
        Returns:
            Optional[Tuple[str, datetime]]: New token and expiration if extended, None otherwise
        """
        if not settings.SLIDING_SESSION_ENABLED:
            return None
            
        if not self.should_refresh_token(current_token_expires):
            return None
            
        # Create new access token with extended lifetime
        extend_minutes = settings.SLIDING_SESSION_EXTEND_MINUTES
        new_expires = datetime.utcnow() + timedelta(minutes=extend_minutes)
        
        token_data = {
            "sub": user.id,
            "email": user.email,
            "role": user.role,
            "session_extended": True
        }
        
        new_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=extend_minutes)
        )
        
        return new_token, new_expires
    
    def refresh_token(
        self,
        refresh_token: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Token:
        """Refresh an access token using a refresh token with enhanced security.

        This method implements proper refresh token rotation where each refresh
        operation generates a new refresh token and invalidates the old one.

        Args:
            refresh_token: The refresh token
            ip_address: IP address of the client
            user_agent: User agent string

        Returns:
            Token: The new access and refresh tokens

        Raises:
            TokenExpiredError: If the refresh token has expired
            TokenRevokedError: If the refresh token has been revoked
            TokenBlacklistedError: If the refresh token is blacklisted
            TokenNotFoundError: If the refresh token is not found
            UserNotFoundError: If the user is not found
            UserInactiveError: If the user account is inactive
            TokenRotationError: If token rotation fails
        """
        correlation_id = str(uuid.uuid4())

        try:
            # Comprehensive token validation
            db_token, user = validate_refresh_token(
                db=self.db,
                token=refresh_token,
                ip_address=ip_address,
                correlation_id=correlation_id
            )

            # Convert database user to Pydantic model
            user_model = User(
                id=user.id,
                email=user.email,
                hashed_password=user.hashed_password,
                full_name=user.full_name,
                is_active=user.is_active,
                is_email_verified=user.is_email_verified,
                role=user.role,
                auth_provider=user.auth_provider,
                auth_provider_id=user.auth_provider_id,
                created_at=user.created_at,
                updated_at=user.updated_at,
                last_login=user.last_login,
                profile_picture=user.profile_picture,
                settings=json_deserialize(user.settings) if user.settings else {}
            )

            # Create new tokens (this will store the new refresh token)
            access_token, new_refresh_token, access_token_expires, refresh_token_expires = self.create_tokens(
                user_model
            )

            # Revoke old refresh token with rotation reason
            revoke_success = revoke_refresh_token(
                db=self.db,
                token=refresh_token,
                reason="rotation",
                correlation_id=correlation_id
            )

            if not revoke_success:
                # If we can't revoke the old token, something is wrong
                # Blacklist the new token and raise an error
                blacklist_token(
                    db=self.db,
                    token=new_refresh_token,
                    token_type="refresh",
                    user_id=user.id,
                    expires_at=refresh_token_expires,
                    reason="rotation_failure",
                    correlation_id=correlation_id
                )
                raise TokenRotationError("Failed to revoke old refresh token")

            # Log successful token refresh
            audit_logger.log_token_refreshed(
                user_id=user.id,
                old_token_id=db_token.id,
                correlation_id=correlation_id,
                ip_address=ip_address
            )

            return Token(
                access_token=access_token,
                token_type="bearer",
                expires_at=access_token_expires,
                refresh_token=new_refresh_token,
                user_id=user.id
            )

        except (TokenExpiredError, TokenRevokedError, TokenBlacklistedError,
                TokenNotFoundError, UserNotFoundError, UserInactiveError) as e:
            # These are expected authentication errors, re-raise them
            raise e
        except Exception as e:
            # Log unexpected errors
            audit_logger.log_security_event(
                audit_logger.SecurityEventType.SUSPICIOUS_ACTIVITY,
                correlation_id=correlation_id,
                ip_address=ip_address,
                details={
                    "event": "refresh_token_error",
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
            raise TokenRotationError(f"Token refresh failed: {str(e)}")

    def logout(self, refresh_token: str) -> bool:
        """Logout a user by revoking their refresh token.

        Args:
            refresh_token: The refresh token to revoke

        Returns:
            bool: True if the token was revoked, False otherwise
        """
        return revoke_refresh_token(self.db, refresh_token)
    
    def verify_refresh_token(self, refresh_token: str) -> Optional[User]:
        """Verify a refresh token and return the associated user.
        
        Args:
            refresh_token: The refresh token to verify
            
        Returns:
            Optional[User]: The user associated with the token, or None if the token is invalid
        """
        try:
            payload = decode_token(refresh_token, settings.JWT_REFRESH_SECRET_KEY)
            user_id = payload.get("sub")
            if user_id is None:
                return None

            # Get user
            return self.get_user_by_id(user_id)
        except Exception:
            return None
    
    def update_user(self, user_id: str, user_update: UserUpdate) -> User:
        """Update a user.
        
        Args:
            user_id: The ID of the user to update
            user_update: The user update data
            
        Returns:
            User: The updated user
            
        Raises:
            HTTPException: If the user does not exist
        """
        user = self.db.query(UserDB).filter(UserDB.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update fields if provided
        if user_update.full_name is not None:
            user.full_name = user_update.full_name
        if user_update.password is not None:
            user.hashed_password = get_password_hash(user_update.password)
        if user_update.profile_picture is not None:
            user.profile_picture = user_update.profile_picture
        
        user.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(user)
        
        # Convert to Pydantic model
        user_model = User(
            id=user.id,
            email=user.email,
            hashed_password=user.hashed_password,
            full_name=user.full_name,
            is_active=user.is_active,
            is_email_verified=user.is_email_verified,
            role=user.role,
            auth_provider=user.auth_provider,
            auth_provider_id=user.auth_provider_id,
            created_at=user.created_at,
            updated_at=user.updated_at,
            last_login=user.last_login,
            profile_picture=user.profile_picture,
            settings=json_deserialize(user.settings)
        )
        
        return user_model
    
    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get a user by ID.
        
        Args:
            user_id: The ID of the user
            
        Returns:
            Optional[User]: The user, or None if not found
        """
        user = self.db.query(UserDB).filter(UserDB.id == user_id).first()
        if not user:
            return None
        
        # Convert to Pydantic model
        user_model = User(
            id=user.id,
            email=user.email,
            hashed_password=user.hashed_password,
            full_name=user.full_name,
            is_active=user.is_active,
            is_email_verified=user.is_email_verified,
            role=user.role,
            auth_provider=user.auth_provider,
            auth_provider_id=user.auth_provider_id,
            created_at=user.created_at,
            updated_at=user.updated_at,
            last_login=user.last_login,
            profile_picture=user.profile_picture,
            settings=json_deserialize(user.settings)
        )
        
        return user_model
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get a user by email.
        
        Args:
            email: The email of the user
            
        Returns:
            Optional[User]: The user, or None if not found
        """
        user = self.db.query(UserDB).filter(UserDB.email == email).first()
        if not user:
            return None
        
        # Convert to Pydantic model
        user_model = User(
            id=user.id,
            email=user.email,
            hashed_password=user.hashed_password,
            full_name=user.full_name,
            is_active=user.is_active,
            is_email_verified=user.is_email_verified,
            role=user.role,
            auth_provider=user.auth_provider,
            auth_provider_id=user.auth_provider_id,
            created_at=user.created_at,
            updated_at=user.updated_at,
            last_login=user.last_login,
            profile_picture=user.profile_picture,
            settings=json_deserialize(user.settings)
        )
        
        return user_model

    def check_user_exists(self, email: str, auth_provider: str = None) -> Tuple[bool, Optional[UserDB]]:
        """Universal user existence check across all authentication methods.

        Optimized for Supabase database operations with enhanced error handling and query optimization.

        Args:
            email: User's email address
            auth_provider: Optional auth provider filter ('local', 'google', etc.)

        Returns:
            Tuple[bool, Optional[UserDB]]: (user_exists, user_record)
        """
        try:
            # Get Supabase query optimization hints
            optimization_hints = self.supabase_service.optimize_user_lookup_query(email, auth_provider)

            query = self.db.query(UserDB).filter(UserDB.email == email)

            if auth_provider:
                query = query.filter(UserDB.auth_provider == auth_provider)

            user = query.first()

            # Log user lookup for audit purposes (without sensitive data)
            logger.info(f"User existence check: email_domain={email.split('@')[1] if '@' in email else 'unknown'}, "
                       f"auth_provider={auth_provider}, found={user is not None}")

            # Log query optimization info
            logger.debug(f"Query optimization applied: {optimization_hints.get('cache_strategy', 'none')}")

            return (user is not None, user)

        except Exception as e:
            logger.error(f"Error checking user existence for email domain {email.split('@')[1] if '@' in email else 'unknown'}: {str(e)}")
            # Return False for safety - assume user doesn't exist if we can't check
            return (False, None)

    def validate_email_uniqueness(self, email: str, auth_provider: str = None) -> None:
        """Validate that an email address is unique across all authentication methods.

        This method performs case-insensitive email validation and checks for existing
        users across all authentication providers to prevent duplicate accounts.

        Args:
            email: The email address to validate
            auth_provider: Optional auth provider context for enhanced validation

        Raises:
            EmailAlreadyExistsError: If an account with this email already exists

        Note:
            This method implements security best practices:
            - Case-insensitive email comparison
            - Input sanitization and normalization
            - Cross-provider duplicate detection
            - Audit logging for security monitoring
        """
        try:
            # Normalize email for consistent comparison
            normalized_email = email.strip().lower()
            email_domain = normalized_email.split('@')[1] if '@' in normalized_email else 'unknown'

            # Get Supabase optimization hints for the query
            optimization_hints = self.supabase_service.optimize_user_lookup_query(
                normalized_email,
                auth_provider
            )

            # Check for existing user with case-insensitive email comparison
            # Using func.lower() for database-level case-insensitive comparison
            from sqlalchemy import func
            existing_user = self.db.query(UserDB).filter(
                func.lower(UserDB.email) == normalized_email
            ).first()

            if existing_user:
                # Log security event (without exposing email in logs)
                logger.warning(f"Email uniqueness validation failed: duplicate email attempt for domain {email_domain}")

                # Log query optimization info for performance monitoring
                logger.debug(f"Email validation query optimization: {optimization_hints.get('cache_strategy', 'none')}")

                # Raise specific exception for duplicate email
                raise EmailAlreadyExistsError(email=normalized_email)

            # Log successful validation (without exposing email)
            logger.debug(f"Email uniqueness validation passed for domain: {email_domain}")

        except EmailAlreadyExistsError:
            # Re-raise the specific exception
            raise
        except Exception as e:
            # Log unexpected errors
            email_domain = email.split('@')[1] if '@' in email else 'unknown'
            logger.error(f"Unexpected error during email validation for domain {email_domain}: {str(e)}")
            # For safety, we don't raise here - let the normal flow continue
            # This prevents validation errors from blocking legitimate registrations

    def determine_user_state(self, user: UserDB, auth_method: str) -> bool:
        """Determine if a user is new based on various criteria optimized for Supabase.

        Enhanced with Supabase-specific optimizations and comprehensive logging.

        Priority order (most definitive first):
        1. No user record -> new user
        2. Onboarding completed -> existing user (highest priority)
        3. First login check -> new user
        4. OAuth profile completion -> new user
        5. Email verification (development-friendly) -> new user
        6. Recent creation (24h) -> new user
        7. Enhanced Supabase analysis
        8. Default -> existing user

        Args:
            user: Database user record
            auth_method: Authentication method used ('oauth', 'local')

        Returns:
            bool: True if user is considered new, False if existing
        """
        try:
            if not user:
                logger.debug(f"User state determination: No user record found - marking as new user")
                return True

            # PRIORITY 1: Check onboarding completion status FIRST (most definitive)
            # If onboarding is completed, user is definitely existing regardless of other factors
            if user.settings:
                try:
                    settings_data = json_deserialize(user.settings)
                    onboarding_completed = settings_data.get('onboarding_completed', False)
                    if onboarding_completed:
                        logger.debug(f"User state determination: User {user.id[:8]}... onboarding completed - marking as existing user")
                        return False
                except (ValueError, TypeError) as e:
                    logger.warning(f"User state determination: Settings parsing failed for user {user.id[:8]}... - {str(e)} - continuing with other checks")

            # PRIORITY 2: Check if this is the user's first login
            if user.last_login is None:
                logger.debug(f"User state determination: First login detected for user {user.id[:8]}... - marking as new user")
                return True

            # PRIORITY 3: For OAuth users, check if they have completed profile setup
            if auth_method == 'oauth' and not user.full_name:
                logger.debug(f"User state determination: OAuth user {user.id[:8]}... missing profile info - marking as new user")
                return True

            # PRIORITY 4: Check email verification for local accounts (development-friendly)
            # Only apply this check if we're not in a development/testing environment
            # and the user hasn't been around for a while
            if (not user.is_email_verified and
                user.auth_provider == 'local' and
                user.created_at and
                (datetime.utcnow() - user.created_at).total_seconds() < 604800):  # 7 days instead of immediate
                logger.debug(f"User state determination: Recent local user {user.id[:8]}... email not verified - marking as new user")
                return True

            # PRIORITY 5: Check if user was created very recently (within last 24 hours)
            # Only if they have logged in (to avoid marking old unused accounts as new)
            if user.created_at and user.last_login:
                time_since_creation = user.last_login - user.created_at
                if time_since_creation.total_seconds() < 86400:  # 24 hours
                    logger.debug(f"User state determination: User {user.id[:8]}... created recently - marking as new user")
                    return True

            # PRIORITY 6: Use Supabase service for enhanced state detection if available
            try:
                user_data = {
                    'id': user.id,
                    'email': user.email,
                    'last_login': user.last_login,
                    'full_name': user.full_name,
                    'is_email_verified': user.is_email_verified,
                    'auth_provider': user.auth_provider,
                    'settings': user.settings,
                    'created_at': user.created_at
                }

                enhanced_result = self.supabase_service.enhance_user_state_detection(user_data, auth_method)

                # Log the enhanced analysis
                self.supabase_service.log_user_state_event(
                    user.id,
                    "state_determination",
                    {
                        "auth_method": auth_method,
                        "basic_result": False,  # We reached this point, so basic checks passed
                        "enhanced_result": enhanced_result.get("is_new_user", False),
                        "confidence_score": enhanced_result.get("confidence_score", 0.0),
                        "factors": enhanced_result.get("state_factors", [])
                    }
                )

                # Use enhanced result if confidence is high enough
                if enhanced_result.get("confidence_score", 0.0) > 0.5:
                    logger.debug(f"User state determination: Using enhanced Supabase analysis - "
                               f"confidence: {enhanced_result.get('confidence_score', 0.0):.2f}")
                    return enhanced_result.get("is_new_user", False)

            except Exception as supabase_error:
                logger.warning(f"Enhanced Supabase state detection failed: {str(supabase_error)} - using basic detection")

            # PRIORITY 7: Default to existing user if all checks pass
            logger.debug(f"User state determination: User {user.id[:8]}... determined as existing user")
            return False

        except Exception as e:
            logger.error(f"Error determining user state for auth_method {auth_method}: {str(e)}")
            # Return True for safety - assume new user if we can't determine state
            return True

    def create_enhanced_auth_response(
        self,
        user: User,
        access_token: str,
        refresh_token: str,
        expires_in: int,
        is_new_user: bool,
        client_type: str = None,
        correlation_id: str = None
    ) -> AuthenticationResponse:
        """Create enhanced authentication response with user state detection.

        Args:
            user: Authenticated user
            access_token: JWT access token
            refresh_token: JWT refresh token
            expires_in: Token expiration time in seconds
            is_new_user: Whether the user is new
            client_type: Type of client making the request
            correlation_id: Request correlation ID

        Returns:
            AuthenticationResponse: Enhanced response with user state
        """
        user_data = {
            "id": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "profile_picture": user.profile_picture,
            "is_email_verified": user.is_email_verified,
            "auth_provider": user.auth_provider,
            "role": user.role
        }

        return AuthenticationResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=expires_in,
            user_id=user.id,
            is_new_user=is_new_user,
            user_data=user_data,
            client_type=client_type,
            correlation_id=correlation_id
        )

    def mark_onboarding_complete(self, user_id: str, onboarding_data: Dict = None) -> bool:
        """Mark user onboarding as complete and store onboarding data.

        Args:
            user_id: The ID of the user
            onboarding_data: Optional onboarding data to store

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            user = self.db.query(UserDB).filter(UserDB.id == user_id).first()
            if not user:
                return False

            # Get existing settings or create new
            current_settings = {}
            if user.settings:
                try:
                    current_settings = json_deserialize(user.settings)
                except (ValueError, TypeError):
                    current_settings = {}

            # Mark onboarding as complete
            current_settings['onboarding_completed'] = True
            current_settings['onboarding_completed_at'] = datetime.utcnow().isoformat()

            # Store any additional onboarding data
            if onboarding_data:
                current_settings['onboarding_data'] = onboarding_data

            # Update user settings
            user.settings = json_serialize(current_settings)
            user.updated_at = datetime.utcnow()

            self.db.commit()
            return True

        except Exception as e:
            self.db.rollback()
            audit_logger.log_security_event(
                audit_logger.SecurityEventType.SUSPICIOUS_ACTIVITY,
                correlation_id=str(uuid.uuid4()),
                details={
                    "event": "onboarding_completion_error",
                    "user_id": user_id,
                    "error": str(e)
                }
            )
            return False

    def request_password_reset(self, email: str) -> Optional[str]:
        """Request a password reset for a user.
        
        Args:
            email: The email of the user
            
        Returns:
            Optional[str]: The password reset token, or None if the user was not found
        """
        user = self.db.query(UserDB).filter(UserDB.email == email).first()
        if not user:
            return None
        
        # Create reset token
        token = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(hours=24)
        
        # Store token in database
        reset = PasswordResetDB(
            user_id=user.id,
            token=token,
            expires_at=expires_at
        )
        self.db.add(reset)
        self.db.commit()
        
        # TODO: Send reset email
        
        return token
    
    def reset_password(self, token: str, new_password: str) -> bool:
        """Reset a password with a reset token.
        
        Args:
            token: The password reset token
            new_password: The new password
            
        Returns:
            bool: True if the password was reset, False otherwise
        """
        # Get reset token
        reset = self.db.query(PasswordResetDB).filter(
            PasswordResetDB.token == token,
            PasswordResetDB.expires_at > datetime.utcnow(),
            PasswordResetDB.used == False
        ).first()
        
        if not reset:
            return False
        
        # Get user
        user = self.db.query(UserDB).filter(UserDB.id == reset.user_id).first()
        if not user:
            return False
        
        # Update password
        user.hashed_password = get_password_hash(new_password)
        user.updated_at = datetime.utcnow()
        
        # Mark token as used
        reset.used = True
        
        self.db.commit()
        
        return True
    
    def verify_email(self, token: str) -> bool:
        """Verify an email with a verification token.
        
        Args:
            token: The email verification token
            
        Returns:
            bool: True if the email was verified, False otherwise
        """
        # Get verification token
        verification = self.db.query(EmailVerificationDB).filter(
            EmailVerificationDB.token == token,
            EmailVerificationDB.expires_at > datetime.utcnow(),
            EmailVerificationDB.used == False
        ).first()
        
        if not verification:
            return False
        
        # Get user
        user = self.db.query(UserDB).filter(UserDB.id == verification.user_id).first()
        if not user:
            return False
        
        # Update user
        user.is_email_verified = True
        user.updated_at = datetime.utcnow()
        
        # Mark token as used
        verification.used = True
        
        self.db.commit()
        
        return True
    
    def _create_email_verification_token(self, user_id: str) -> str:
        """Create an email verification token.
        
        Args:
            user_id: The ID of the user
            
        Returns:
            str: The verification token
        """
        token = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(days=7)
        
        # Store token in database
        verification = EmailVerificationDB(
            user_id=user_id,
            token=token,
            expires_at=expires_at
        )
        self.db.add(verification)
        self.db.commit()
        
        return token
    
    async def get_google_auth_url(self, state: str = None) -> str:
        """Get the Google OAuth authorization URL.

        Args:
            state: Optional OAuth state parameter for CSRF protection

        Returns:
            str: The authorization URL
        """
        if not settings.GOOGLE_CLIENT_ID or not settings.GOOGLE_CLIENT_SECRET:
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail="Google OAuth is not configured"
            )

        client = AsyncOAuth2Client(
            client_id=settings.GOOGLE_CLIENT_ID,
            client_secret=settings.GOOGLE_CLIENT_SECRET,
            redirect_uri=settings.GOOGLE_REDIRECT_URI,
            scope="openid email profile"
        )

        if state:
            # Use provided state parameter
            authorization_url = client.create_authorization_url(
                "https://accounts.google.com/o/oauth2/auth",
                state=state
            )[0]
        else:
            # Generate state automatically
            authorization_url, _ = client.create_authorization_url(
                "https://accounts.google.com/o/oauth2/auth"
            )

        return authorization_url
    
    async def authenticate_google(self, code: str) -> User:
        """Authenticate a user with Google OAuth.
        
        Args:
            code: The authorization code from Google
            
        Returns:
            User: The authenticated user
            
        Raises:
            HTTPException: If authentication failed
        """
        if not settings.GOOGLE_CLIENT_ID or not settings.GOOGLE_CLIENT_SECRET:
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail="Google OAuth is not configured"
            )

        # Exchange code for tokens
        client = AsyncOAuth2Client(
            client_id=settings.GOOGLE_CLIENT_ID,
            client_secret=settings.GOOGLE_CLIENT_SECRET,
            redirect_uri=settings.GOOGLE_REDIRECT_URI
        )
        
        try:
            token = await client.fetch_token(
                "https://oauth2.googleapis.com/token",
                code=code,
                grant_type="authorization_code"
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to authenticate with Google: {str(e)}"
            )
        
        # Get user info
        async with httpx.AsyncClient() as http_client:
            response = await http_client.get(
                "https://www.googleapis.com/oauth2/v1/userinfo",
                headers={"Authorization": f"Bearer {token['access_token']}"}
            )
            user_info = response.json()
        
        # Check if the user exists
        google_id = user_info.get("id")
        email = user_info.get("email")
        
        if not google_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to get user info from Google"
            )
        
        oauth_account = self.db.query(OAuthAccountDB).filter(
            OAuthAccountDB.provider == "google",
            OAuthAccountDB.provider_user_id == google_id
        ).first()
        
        is_new_user = False

        if oauth_account:
            # User exists, update tokens
            oauth_account.access_token = token.get("access_token")
            oauth_account.refresh_token = token.get("refresh_token")
            oauth_account.expires_at = datetime.utcnow() + timedelta(seconds=token.get("expires_in", 3600))
            oauth_account.updated_at = datetime.utcnow()

            user = self.db.query(UserDB).filter(UserDB.id == oauth_account.user_id).first()
            if not user:
                # This shouldn't happen, but just in case
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="User not found for OAuth account"
                )

            # Determine if user should be considered new
            is_new_user = self.determine_user_state(user, 'oauth')

            # Update user information if needed
            user.full_name = user_info.get("name", user.full_name)
            user.profile_picture = user_info.get("picture", user.profile_picture)
            user.last_login = datetime.utcnow()

            self.db.commit()
        else:
            # Check if user with email exists
            user = self.db.query(UserDB).filter(UserDB.email == email).first()

            if user:
                # Link existing user to Google account
                oauth_account = OAuthAccountDB(
                    user_id=user.id,
                    provider="google",
                    provider_user_id=google_id,
                    access_token=token.get("access_token"),
                    refresh_token=token.get("refresh_token"),
                    expires_at=datetime.utcnow() + timedelta(seconds=token.get("expires_in", 3600))
                )
                self.db.add(oauth_account)

                # Determine if user should be considered new (linking existing account)
                is_new_user = self.determine_user_state(user, 'oauth')

                # Update user information
                user.auth_provider = "google"
                user.auth_provider_id = google_id
                user.full_name = user_info.get("name", user.full_name)
                user.profile_picture = user_info.get("picture", user.profile_picture)
                user.last_login = datetime.utcnow()
                user.is_email_verified = True  # Google emails are verified

                self.db.commit()
            else:
                # Create new user - definitely new
                is_new_user = True

                user = UserDB(
                    id=str(uuid.uuid4()),
                    email=email,
                    full_name=user_info.get("name"),
                    is_active=True,
                    is_email_verified=True,  # Google emails are verified
                    role="user",
                    auth_provider="google",
                    auth_provider_id=google_id,
                    profile_picture=user_info.get("picture"),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    last_login=datetime.utcnow()
                )
                self.db.add(user)
                self.db.flush()  # Flush to get the user.id

                # Create OAuth account
                oauth_account = OAuthAccountDB(
                    user_id=user.id,
                    provider="google",
                    provider_user_id=google_id,
                    access_token=token.get("access_token"),
                    refresh_token=token.get("refresh_token"),
                    expires_at=datetime.utcnow() + timedelta(seconds=token.get("expires_in", 3600))
                )
                self.db.add(oauth_account)

                self.db.commit()
                self.db.refresh(user)
        
        # Convert to Pydantic model
        user_model = User(
            id=user.id,
            email=user.email,
            hashed_password=user.hashed_password,
            full_name=user.full_name,
            is_active=user.is_active,
            is_email_verified=user.is_email_verified,
            role=user.role,
            auth_provider=user.auth_provider,
            auth_provider_id=user.auth_provider_id,
            created_at=user.created_at,
            updated_at=user.updated_at,
            last_login=user.last_login,
            profile_picture=user.profile_picture,
            settings=json_deserialize(user.settings)
        )

        # Store user state for later use
        user_model.is_new_user = is_new_user

        return user_model
    
    def get_user_response(self, user: User) -> Dict[str, Any]:
        """Convert a User to UserResponse with enhanced user state detection.

        Args:
            user: The user to convert

        Returns:
            Dict[str, Any]: The user response with is_new_user field
        """
        # Get the database user for state detection
        user_db = self.db.query(UserDB).filter(UserDB.id == user.id).first()

        # Determine user state based on auth provider
        auth_method = 'oauth' if user.auth_provider == 'google' else 'local'
        is_new_user = self.determine_user_state(user_db, auth_method)

        # Return enhanced user response
        return {
            "id": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "is_active": user.is_active,
            "is_email_verified": user.is_email_verified,
            "role": user.role,
            "auth_provider": user.auth_provider,
            "created_at": user.created_at,
            "profile_picture": user.profile_picture,
            "is_new_user": is_new_user  # ← Enhanced field for frontend
        }
    
    def register_user(self, user_create: UserCreate) -> Dict[str, Any]:
        """Register a new user with enhanced user state detection.

        Args:
            user_create: The user creation data

        Returns:
            Dict[str, Any]: The created user with is_new_user field

        Raises:
            HTTPException: If a user with the given email already exists
        """
        user = self.create_user(user_create)
        return self.get_user_response(user)
    
    def login(self, email: str, password: str, remember_me: bool = False) -> Token:
        """Login a user with email and password with user state detection and enterprise session management.

        Args:
            email: The user's email
            password: The user's password
            remember_me: Whether to create extended-lifetime tokens

        Returns:
            Token: The access and refresh tokens with user state

        Raises:
            HTTPException: If authentication fails
        """
        user = self.authenticate_user(email, password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Get database user for state detection
        db_user = self.db.query(UserDB).filter(UserDB.email == email).first()
        is_new_user = self.determine_user_state(db_user, 'local')

        access_token, refresh_token, access_token_expires, refresh_token_expires = self.create_tokens(user, remember_me)

        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_at=access_token_expires,
            refresh_token=refresh_token,
            user_id=user.id,
            is_new_user=is_new_user
        )
    
    def refresh_token(self, refresh_token: str) -> Token:
        """Refresh an access token.
        
        Args:
            refresh_token: The refresh token
            
        Returns:
            Token: The new access and refresh tokens
            
        Raises:
            HTTPException: If the refresh token is invalid
        """
        try:
            user = self.verify_refresh_token(refresh_token)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Create new tokens
            access_token, new_refresh_token, access_token_expires, refresh_token_expires = self.create_tokens(user)
            
            # Revoke old refresh token
            self.logout(refresh_token)
            
            return Token(
                access_token=access_token,
                token_type="bearer",
                expires_at=access_token_expires,
                refresh_token=new_refresh_token,
                user_id=user.id
            )
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token",
                headers={"WWW-Authenticate": "Bearer"},
            )
            
    def update_current_user(self, user_id: str, user_update: UserUpdate) -> Dict[str, Any]:
        """Update the current user with enhanced user state detection.

        Args:
            user_id: The ID of the user to update
            user_update: The user update data

        Returns:
            Dict[str, Any]: The updated user with is_new_user field
        """
        user = self.update_user(user_id, user_update)
        return self.get_user_response(user)
    
    def request_password_reset_handler(self, email: str) -> Dict[str, str]:
        """Handle a password reset request.
        
        Args:
            email: The email of the user
            
        Returns:
            Dict: A message
        """
        self.request_password_reset(email)
        
        # Always return success, even if the email doesn't exist
        # This prevents user enumeration
        return {"message": "If the email exists, a password reset link has been sent"}
    
    def reset_password_handler(self, password_reset: PasswordReset) -> Dict[str, str]:
        """Handle a password reset.
        
        Args:
            password_reset: The password reset data
            
        Returns:
            Dict: A message
            
        Raises:
            HTTPException: If the reset token is invalid
        """
        success = self.reset_password(password_reset.token, password_reset.new_password)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired password reset token"
            )
        
        return {"message": "Password reset successful"}
    
    def verify_email_handler(self, token: str) -> Dict[str, str]:
        """Handle an email verification.
        
        Args:
            token: The email verification token
            
        Returns:
            Dict: A message
            
        Raises:
            HTTPException: If the verification token is invalid
        """
        success = self.verify_email(token)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired email verification token"
            )
        
        return {"message": "Email verified successfully"}
    
    async def get_google_login_url(self) -> Dict[str, str]:
        """Get the Google OAuth authorization URL.
        
        Returns:
            Dict: The authorization URL
        """
        authorization_url = await self.get_google_auth_url()
        return {"url": authorization_url}
    
    async def handle_google_callback(self, code: str) -> Token:
        """Handle the Google OAuth callback with user state detection.

        Args:
            code: The authorization code from Google

        Returns:
            Token: The access and refresh tokens with user state
        """
        user = await self.authenticate_google(code)

        access_token, refresh_token, access_token_expires, refresh_token_expires = self.create_tokens(user)

        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_at=access_token_expires,
            refresh_token=refresh_token,
            user_id=user.id,
            is_new_user=getattr(user, 'is_new_user', False)
        )
