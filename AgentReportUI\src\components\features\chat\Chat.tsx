"use client";
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
// import { useApi } from '@/providers/ApiContext';
import { useChatHistory } from '@/providers/ChatHistoryContext';
import { useRouter } from 'next/navigation';
import { usePageTitle } from '@/hooks/usePageTitle';
import { MessageCircle, Plus } from 'lucide-react';


import { MessageInput } from "@/components/ui/message-input";
import { ChatStartScreen } from './ChatStartScreen';
import { tokenStreamingService } from '@/lib/services/tokenStreamingService';
import StreamingMessage from './StreamingMessage';
import DataTable from './DataTable';
import AnalyticalContentRenderer from './AnalyticalContentRenderer';

interface Message {
  role: 'user' | 'agent';
  content: string;
  timestamp?: Date;
  isStreaming?: boolean;
  outputFiles?: Array<{
    database_name: string;
    file_path: string;
    format: string;
  }>;
  sqlQueries?: Record<string, string>;
}

interface ChatProps {
  chatId?: string;
}

// Constants
const RATE_LIMIT_MS = 1000; // Prevent messages within 1 second of each other
const processedFirstMessageChats = new Set<string>();

const Chat: React.FC<ChatProps> = ({ chatId }) => {
  // State
  const [isLoading, setIsLoading] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<Array<{ name: string; type: string }>>([]);
  const [streamingMessage, setStreamingMessage] = useState<Message | null>(null);
  const [currentStreamingId, setCurrentStreamingId] = useState<string | null>(null);
  const [chatNotFound, setChatNotFound] = useState(false);
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const tokenQueueRef = useRef<string[]>([]);
  const streamingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastMessageTime = useRef(0);
  const isSendingRef = useRef(false);
  const processedFirstMessageRef = useRef(false);
  const completionGuardRef = useRef<string | null>(null);
  const {
    chatHistory,
    activeChat,
    chatMessages,
    isLoadingHistory,
    isLoadingChats,
    pendingFirstMessage,
    setPendingFirstMessage,
    setActiveChat,
    addMessageToChat,
    addChat,
    refreshChatList,
  } = useChatHistory();
  const router = useRouter();

  // Messages for the current chat
  const messages = activeChat ? chatMessages[activeChat.session_id] || [] : [];

  // Memoize page title configuration to prevent infinite re-renders
  const pageConfig = useMemo(() => ({
    title: activeChat 
      ? `Data Chat ${activeChat.id.startsWith('chat_sess_') ? activeChat.id.slice(9, 17) : activeChat.id.slice(0, 8)}` 
      : 'New Chat',
    icon: activeChat ? MessageCircle : Plus
  }), [activeChat]);

  // Set page title based on chat state
  usePageTitle(pageConfig);

  // Set active chat when chatId changes - Enhanced for page refresh handling
  useEffect(() => {
    console.log('Chat component useEffect triggered with chatId:', chatId);
    console.log('Current activeChat:', activeChat?.id);
    console.log('Chat history loaded:', chatHistory.length > 0);
    console.log('Is loading chats:', isLoadingChats);

    // If no chatId (new chat scenario), clear active chat
    if (!chatId) {
      if (activeChat) {
        console.log('Clearing active chat for new chat scenario');
        setActiveChat(null);
      }
      return;
    }

    // If the chatId matches the current active chat, no need to reload
    // This prevents clearing the activeChat when URL changes after creating a new chat
    if (activeChat && activeChat.id === chatId) {
      console.log('Chat is already active, no action needed');
      return;
    }

    // Reset chat not found state when trying to load a new chat
    setChatNotFound(false);

    // Check if this is a newly created chat that exists in local state but not backend yet
    // Look for the chat in the current chat history first
    const existingChatInHistory = chatHistory.find(chat => chat.id === chatId);
    if (existingChatInHistory) {
      console.log('Found chat in local history, setting as active:', existingChatInHistory.id);
      setActiveChat(existingChatInHistory);
      return;
    }

    // Don't try to load from backend if we're still loading chats
    // This prevents unnecessary backend calls during initial load
    if (isLoadingChats) {
      console.log('Still loading chats, waiting...');
      return;
    }

    // IMPORTANT: Only mark as not found if we have actually loaded chat history
    // On page refresh, chatHistory is initially empty but that doesn't mean the chat doesn't exist
    // We should only mark as not found if we have loaded the chat list and the chat is still not there
    if (chatHistory.length === 0) {
      console.log('Chat history not loaded yet, waiting for backend data...');
      return;
    }

    // Now we can safely mark as not found since we have loaded chat history and chat is not there
    console.log('Chat not found in loaded history, marking as not found:', chatId);
    setChatNotFound(true);
  }, [chatId, activeChat?.id, isLoadingChats, chatHistory.length]);

  // Process pending first message after chat creation and navigation
  useEffect(() => {
    // Prevent duplicate processing in React strict mode
    if (processedFirstMessageRef.current) {
      return;
    }

    // Existing diagnostic logs
    console.log('🔍 pendingFirstMessage useEffect triggered:', {
      pendingFirstMessage: !!pendingFirstMessage,
      activeChat: !!activeChat,
      activeChatId: activeChat?.id,
      chatId,
      isLoading,
      chatIdMatches: activeChat?.id === chatId
    });
    
    // Wait for all conditions: pending message, active chat set, chatId matches, and they're the same chat
    if (pendingFirstMessage && activeChat && chatId && activeChat.id === chatId && !processedFirstMessageChats.has(chatId)) {
      // Mark as processed to avoid running twice under Strict-Mode
      processedFirstMessageRef.current = true;
      processedFirstMessageChats.add(chatId);

      console.log('✅ Processing pending first message after navigation:', pendingFirstMessage);
      
      // Set loading state
      setIsLoading(true);
      console.log('🔄 pendingFirstMessage: Set isLoading to true');
      
      // Clear any existing streaming message and create new streaming ID
      setStreamingMessage(null);
      const streamingId = `welcome_stream_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      setCurrentStreamingId(streamingId);
      
      // Reset completion guard for new message
      completionGuardRef.current = null;
      
      // Clear the pending message to prevent reprocessing
      const messageToProcess = pendingFirstMessage;
      setPendingFirstMessage(null);

      // Add completion guard to prevent duplicate processing
      let hasCompleted = false;

      // Start streaming
      const startStreaming = async () => {
        try {
          await tokenStreamingService.startStreaming({
            sessionId: activeChat.session_id,
            query: messageToProcess,
            outputFormat: 'excel',
            onTokenReceived: (token) => {
              console.log('🔤 Welcome message token received:', token);
              setIsLoading(false);
              // Prevent processing the same token multiple times
              if (!token || typeof token !== 'string') {
                return;
              }
              addTokenToQueue(token);
            },
            onComplete: async (finalContent, completeResponse) => {
              // Always turn off the typing indicator as soon as we know the stream ended.
              // This guarantees the blue dots disappear even if this completion event
              // belongs to an older (superseded) request.
              setIsLoading(false);

              // Enhanced guard against duplicate completion calls using content hash
              const contentHash = `${finalContent}_${Date.now()}`;
              if (hasCompleted || completionGuardRef.current === contentHash) {
                console.log('⏭️ Skipping duplicate onComplete call');
                return;
              }
              hasCompleted = true;
              completionGuardRef.current = contentHash;
              
              console.log('✅ Welcome message streaming complete:', finalContent);
              console.log('📊 Complete response data:', completeResponse);

              // Clear any remaining timeout and queue
              if (streamingTimeoutRef.current) {
                clearTimeout(streamingTimeoutRef.current);
                streamingTimeoutRef.current = null;
              }
              tokenQueueRef.current = [];

              // Add the final message to chat with structured data
              const finalMessage: Message = {
                role: 'agent',
                content: finalContent,
                timestamp: new Date(),
                isStreaming: false,
                outputFiles: completeResponse?.output_files || [],
                sqlQueries: completeResponse?.sql_queries || {},
              };
              addMessageToChat(activeChat.session_id, finalMessage);
              setStreamingMessage(null);
              setCurrentStreamingId(null);

              // Refresh chat list to get updated title
              setTimeout(async () => {
                await refreshChatList();
              }, 500);
            },
            onError: (error) => {
              console.error('Streaming failed:', error);
              
              // Always ensure cleanup happens
              setIsLoading(false);
              
              // Clear any remaining timeout and queue
              if (streamingTimeoutRef.current) {
                clearTimeout(streamingTimeoutRef.current);
                streamingTimeoutRef.current = null;
              }
              tokenQueueRef.current = [];
              
              // Show error message if we have an active chat
              if (activeChat) {
                const errorMessage: Message = {
                  role: 'agent',
                  content: 'Sorry, there was an error processing your request. Please try again.',
                  timestamp: new Date()
                };
                addMessageToChat(activeChat.session_id, errorMessage);
              }
              setStreamingMessage(null);
              setCurrentStreamingId(null);
            },
          });
        } catch (streamingError) {
          console.error('Failed to start streaming for first message:', streamingError);
          
          // Always ensure cleanup happens
          setIsLoading(false);
          
          // Clear any remaining timeout and queue
          if (streamingTimeoutRef.current) {
            clearTimeout(streamingTimeoutRef.current);
            streamingTimeoutRef.current = null;
          }
          tokenQueueRef.current = [];
          
          // Show error message if we have an active chat
          if (activeChat) {
            const errorMessage: Message = {
              role: 'agent',
              content: 'Sorry, there was an error processing your request. Please try again.',
              timestamp: new Date()
            };
            addMessageToChat(activeChat.session_id, errorMessage);
          }
          setStreamingMessage(null);
          setCurrentStreamingId(null);
        }
      };

      startStreaming();
    }
  }, [pendingFirstMessage, activeChat, chatId]); // Keep the same dependencies

  // Reset the processedFirstMessageRef when chatId changes (e.g., user starts a brand-new chat)
  useEffect(() => {
    // Reset ref for new chatId
    processedFirstMessageRef.current = false;
  }, [chatId]);

  // Cleanup streaming service on unmount
  useEffect(() => {
    return () => {
      tokenStreamingService.stopStreaming();
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
    };
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Configuration for token streaming display speed
  const STREAMING_CONFIG = {
    // Delay between tokens in milliseconds - adjust this to control reading speed
    // 400ms provides a comfortable reading pace (about 150 WPM)
    TOKEN_DELAY: 400,
    // Minimum delay for very short tokens (like single characters or punctuation)
    MIN_DELAY: 200,
    // Maximum delay for very long tokens
    MAX_DELAY: 600,
  };

  // Function to calculate dynamic delay based on token length
  const calculateTokenDelay = (token: string): number => {
    const baseDelay = STREAMING_CONFIG.TOKEN_DELAY;
    const tokenLength = token.trim().length;

    // Shorter tokens (punctuation, single chars) get shorter delays
    if (tokenLength <= 2) {
      return STREAMING_CONFIG.MIN_DELAY;
    }

    // Longer tokens get slightly longer delays for better readability
    if (tokenLength > 8) {
      return STREAMING_CONFIG.MAX_DELAY;
    }

    // Standard delay for normal-length tokens
    return baseDelay;
  };

  // Function to handle throttled token streaming display
  const processTokenQueue = () => {
    // If the queue is empty, clear any lingering timeout and exit early.
    if (tokenQueueRef.current.length === 0) {
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
        streamingTimeoutRef.current = null;
      }
      return;
    }

    const token = tokenQueueRef.current.shift();
    if (token && token.trim()) {
      setStreamingMessage(prev => {
        if (!prev) {
          return {
            role: 'agent',
            content: token,
            timestamp: new Date(),
            isStreaming: true,
          };
        }
        // Only update if the content actually changes
        const newContent = prev.content + token;
        if (newContent === prev.content) {
          return prev; // No change, return same object to prevent re-render
        }
        return {
          ...prev,
          content: newContent,
        };
      });

      // Continue processing queue if there are more tokens
      if (tokenQueueRef.current.length > 0) {
        const delay = calculateTokenDelay(token);
        streamingTimeoutRef.current = setTimeout(processTokenQueue, delay);
      } else {
        // Queue is now empty, clear the timeout reference so that future
        // streaming sessions can start processing immediately.
        if (streamingTimeoutRef.current) {
          clearTimeout(streamingTimeoutRef.current);
          streamingTimeoutRef.current = null;
        }
      }
    } else {
      // If token is empty but queue has more items, continue processing
      if (tokenQueueRef.current.length > 0) {
        streamingTimeoutRef.current = setTimeout(processTokenQueue, 50); // Short delay for empty tokens
      }
    }
  };

  const addTokenToQueue = (token: string) => {
    // Don't add empty tokens to the queue
    if (!token || !token.trim()) {
      return;
    }
    
    // Throttle token additions to prevent overwhelming the queue
    const now = Date.now();
    const lastTokenTime = tokenQueueRef.current.length > 0 ? 
      (tokenQueueRef.current as any).lastAddTime || 0 : 0;
    
    if (now - lastTokenTime < 10) { // Minimum 10ms between tokens
      return;
    }
    
    tokenQueueRef.current.push(token);
    (tokenQueueRef.current as any).lastAddTime = now;
    
    // Start processing if not already running
    if (!streamingTimeoutRef.current) {
      processTokenQueue();
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, streamingMessage]);



  /**
   * Clean message submission handler - Industry standard approach
   * 
   * This function handles the complete message flow from user input to backend streaming:
   * 1. Input validation and rate limiting
   * 2. User message display (optimistic UI)
   * 3. Backend API call with streaming response
   * 4. Token-by-token message building
   * 5. Error handling and cleanup
   * 
   * Follows single responsibility principle and proper error boundaries.
   */
  const handleMessageSubmit = useCallback(async (message: string, file?: File) => {
    console.log('Chat: Message submitted', { message, file });
    
    // Validate input
    if (!message.trim() && !file) {
      console.log('Chat: No message or file provided, skipping submit');
      return;
    }

    // Validate chat state
    if (!activeChat) {
      console.error('Chat: Cannot send message - no active chat');
      return;
    }

    // Prevent concurrent execution
    if (isSendingRef.current || isSendingMessage) {
      console.log('Chat: Message already being sent, ignoring duplicate request');
      return;
    }

    // Rate limiting check
    const now = Date.now();
    if (now - lastMessageTime.current < RATE_LIMIT_MS) {
      console.log('Chat: Rate limit exceeded, ignoring message send');
      return;
    }

    // Set guards and state
    isSendingRef.current = true;
    setIsSendingMessage(true);
    lastMessageTime.current = now;

    try {
      // Handle file attachment if provided
      if (file) {
        console.log('Chat: Processing attached file', file.name);
        setAttachedFiles(prev => [...prev, { name: file.name, type: file.type }]);
      }

      // Create user message
      const userMessage: Message = {
        role: 'user',
        content: message.trim(),
        timestamp: new Date()
      };

      // Add user message to chat immediately for responsive UI
      addMessageToChat(activeChat.session_id, userMessage);
      setIsLoading(true);

      // Save any existing streaming message to permanent chat history before clearing
      if (streamingMessage && streamingMessage.content.trim()) {
        console.log('Chat: Saving previous streaming message to history');
        const permanentMessage: Message = {
          role: 'agent',
          content: streamingMessage.content,
          timestamp: new Date(),
          isStreaming: false,
          outputFiles: streamingMessage.outputFiles || [],
          sqlQueries: streamingMessage.sqlQueries || {},
        };
        addMessageToChat(activeChat.session_id, permanentMessage);
      }

      // Clear any existing streaming message and create new streaming ID
      setStreamingMessage(null);
      const streamingId = `stream_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      setCurrentStreamingId(streamingId);

      // Reset completion guard for new message
      completionGuardRef.current = null;

      // Add completion guard to prevent duplicate processing
      let hasCompleted = false;

      // Start streaming
      console.log('Chat: Starting streaming for session:', activeChat.session_id);
        await tokenStreamingService.startStreaming({
          sessionId: activeChat.session_id,
        query: message.trim(),
          outputFormat: 'excel',
          onTokenReceived: (token) => {
            // Prevent processing invalid tokens
            if (!token || typeof token !== 'string') {
              return;
            }
            // Hide thinking animation as soon as first token arrives
              setIsLoading(false);
            addTokenToQueue(token);
          },
          onComplete: (finalContent, completeResponse) => {
          console.log('Chat: Streaming complete', { finalContent, outputFiles: completeResponse?.output_files });

          // Always turn off the typing indicator
            setIsLoading(false);

          // Enhanced guard against duplicate completion calls
            const contentHash = `${finalContent}_${Date.now()}`;
            if (hasCompleted || completionGuardRef.current === contentHash) {
            console.log('Chat: Skipping duplicate onComplete call');
              return;
            }

            hasCompleted = true;
            completionGuardRef.current = contentHash;

            // Clear any remaining timeout and queue
            if (streamingTimeoutRef.current) {
              clearTimeout(streamingTimeoutRef.current);
              streamingTimeoutRef.current = null;
            }
            tokenQueueRef.current = [];

          // Add final message to chat
              const finalMessage: Message = {
                role: 'agent',
                content: finalContent,
                timestamp: new Date(),
                isStreaming: false,
                outputFiles: completeResponse?.output_files || [],
                sqlQueries: completeResponse?.sql_queries || {},
              };
              addMessageToChat(activeChat.session_id, finalMessage);
          
          // Cleanup streaming state
            setStreamingMessage(null);
            setCurrentStreamingId(null);
          },
          onError: (error) => {
          console.error('Chat: Streaming failed:', error);
            
            // Always ensure cleanup happens
            setIsLoading(false);
            
            // Clear any remaining timeout and queue
            if (streamingTimeoutRef.current) {
              clearTimeout(streamingTimeoutRef.current);
              streamingTimeoutRef.current = null;
            }
            tokenQueueRef.current = [];
            
          // Show error message
              const errorMessage: Message = {
                role: 'agent',
                content: 'Sorry, there was an error processing your request. Please try again.',
                timestamp: new Date()
              };
              addMessageToChat(activeChat.session_id, errorMessage);
          
          // Cleanup streaming state
            setStreamingMessage(null);
            setCurrentStreamingId(null);
          },
        });
      } catch (error) {
      console.error('Chat: Error in message submission:', error);
        setIsLoading(false);
        
      // Show error message
        if (activeChat) {
          const errorMessage: Message = {
            role: 'agent',
            content: 'Sorry, there was an error processing your request. Please try again.',
            timestamp: new Date()
          };
          addMessageToChat(activeChat.session_id, errorMessage);
        }
    } finally {
      // Reset sending state after a delay to prevent rapid re-sending
      setTimeout(() => {
        setIsSendingMessage(false);
        isSendingRef.current = false;
      }, 1500);
    }
  }, [activeChat, isSendingMessage, streamingMessage, addMessageToChat, addTokenToQueue]);

  return (
    <div
      className="flex flex-col items-center justify-center h-full w-full md:p-6 bg-sidebar-bg overflow-y-auto"
      onDragOver={e => {
        e.preventDefault();
        e.stopPropagation();
      }}
      onDrop={e => {
        e.preventDefault();
        e.stopPropagation();
        const file = e.dataTransfer.files?.[0];
        if (file) {
          setAttachedFiles(prev => [...prev, { name: file.name, type: file.type }]);
        }
      }}
    >
          {/* Welcome message with animated input - only show when no active chat AND no chatId */}
          {!activeChat && !chatId && (
            <ChatStartScreen 
              onSendMessage={async (message: string) => {
                console.log('Sending first message to create new chat:', message);

                // 🚀 OPTIMISTIC UI - Create chat immediately for responsive UX
                const newChat = addChat();
                await setActiveChat(newChat);

                // 💬 IMMEDIATELY ADD USER MESSAGE (ChatGPT behavior)
                const userMessage: Message = {
                  role: 'user',
                  content: message,
                  timestamp: new Date()
                };
                addMessageToChat(newChat.session_id, userMessage);

                // Start thinking animation BEFORE navigation to avoid state reset
                setIsLoading(true);
                console.log('🔄 WelcomeMessage: Set isLoading to true');

                // Clear any existing streaming message and create new streaming ID
                setStreamingMessage(null);
                const streamingId = `welcome_stream_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
                setCurrentStreamingId(streamingId);

                // Update URL using Next.js router for proper state sync
                router.replace(`/chat/${newChat.id}`);
                console.log('🔄 WelcomeMessage: Navigated to chat, setting pending message');

                // Set pending first message to be processed after navigation
                setPendingFirstMessage(message);
              }} 
            />
          )}

      <div className="w-full max-w-3xl flex flex-col flex-1 h-full">
        {/* 💬 Scrollable chat messages */}
        <div className="flex-grow space-y-8 pr-2 relative">
          
          {/* Loading state for chat history or when loading specific chat by ID */}
          {((activeChat && isLoadingHistory) || (chatId && !activeChat && (isLoadingChats || !chatNotFound))) && (
            <div className="flex flex-1 items-center justify-center min-h-[400px] h-full w-full">
              <div className="max-w-md w-full text-center mx-auto">
                <div className="mb-6 p-6 rounded-lg bg-blue-50 dark:bg-blue-900 text-blue-900 dark:text-blue-100 border border-blue-200 dark:border-blue-700 shadow flex flex-col items-center justify-center text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-3"></div>
                  <h3 className="text-lg font-bold mb-2">
                    {(activeChat && isLoadingHistory) ? 'Loading conversation...' : 'Loading chat...'}
                  </h3>
                  <p className="text-sm">
                    {(activeChat && isLoadingHistory)
                      ? 'Fetching your chat history from the server.'
                      : 'Finding your chat and loading the conversation.'
                    }
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Chat not found error */}
          {chatId && chatNotFound && !isLoadingChats && (
            <div className="flex flex-1 items-center justify-center min-h-[400px] h-full w-full">
              <div className="max-w-md w-full text-center mx-auto">
                <div className="mb-6 p-6 rounded-lg bg-red-50 dark:bg-red-900 text-red-900 dark:text-red-100 border border-red-200 dark:border-red-700 shadow flex flex-col items-center justify-center text-center">
                  <div className="text-red-500 mb-3">
                    <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold mb-2">Chat Not Found</h3>
                  <p className="text-sm mb-4">The chat you're looking for doesn't exist or may have been deleted.</p>
                  <button
                    onClick={() => router.push('/chat')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Start New Chat
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeChat && !isLoadingHistory && messages.map((message, index) => (
            <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[85%] px-5 py-2.5 ${message.role === 'user' ? 'bg-gray-200/50 dark:bg-gray-700/50 rounded-3xl text-gray-900 dark:text-gray-100' : 'text-gray-900 dark:text-gray-100'}`}>
                {message.role === 'user' ? (
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                ) : (
                  <AnalyticalContentRenderer
                    content={message.content}
                    isStreaming={false}
                    className="text-sm"
                  />
                )}

                {/* Show data table if output files are available */}
                {message.role === 'agent' && message.outputFiles && message.outputFiles.length > 0 && (
                  <div className="mt-3">
                    <DataTable outputFiles={message.outputFiles} />
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Streaming message - Show when we have streaming content */}
          {streamingMessage && activeChat && (
            <div className="flex justify-start">
              <div className="max-w-[85%] px-5 py-2.5 text-gray-900 dark:text-gray-100">
                <StreamingMessage
                  content={streamingMessage.content}
                  isStreaming={streamingMessage.isStreaming || false}
                  className="text-gray-900 dark:text-gray-100"
                />
                
                {/* Show data table if output files are available during streaming */}
                {streamingMessage.outputFiles && streamingMessage.outputFiles.length > 0 && (
                  <div className="mt-3">
                    <DataTable outputFiles={streamingMessage.outputFiles} />
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 🤖 Thinking Animation - Only show when loading and NOT streaming */}
          {isLoading && activeChat && !streamingMessage && (
            <div className="flex justify-start mb-4 animate-in fade-in duration-300">
              <div className="max-w-[70%] px-4 py-3 rounded-2xl bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 shadow-sm border border-gray-100 dark:border-gray-600">
                <div className="flex items-center space-x-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0ms', animationDuration: '1s'}}></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '150ms', animationDuration: '1s'}}></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '300ms', animationDuration: '1s'}}></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">AI is thinking...</span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* 📨 Message input area - Only show when there's an active chat or loading a specific chat */}
        {(activeChat || (chatId && !chatNotFound)) && (
          <div className="mt-6 border-t border-gray-200 dark:border-gray-700 pt-6">
            <MessageInput
              onSubmit={handleMessageSubmit}
              placeholder={activeChat ? "Message..." : "Loading chat..."}
              disabled={!activeChat || isLoading || isSendingMessage}
              isLoading={isLoading || isSendingMessage}
              className="w-full max-w-full"
            />
              </div>
        )}
      </div>
    </div>
  );
};

export default Chat;
