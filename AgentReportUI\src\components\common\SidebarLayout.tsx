import React from 'react';
import { cn } from '@/lib/utils';

interface SidebarLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  showSidebar?: boolean;
  sidebarWidth?: string;
  sidebarPosition?: 'left' | 'right';
  className?: string;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({
  children,
  sidebar,
  showSidebar = false,
  sidebarWidth = '320px',
  sidebarPosition = 'right',
  className,
}) => {
  const gridTemplateColumns = React.useMemo(() => {
    if (!showSidebar) return '1fr';
    
    return sidebarPosition === 'left' 
      ? `${sidebarWidth} 1fr`
      : `1fr ${sidebarWidth}`;
  }, [showSidebar, sidebarWidth, sidebarPosition]);

  return (
    <div 
      className={cn("h-full grid transition-all duration-300 ease-in-out", className)}
      style={{ gridTemplateColumns }}
    >
      {/* Conditionally render sidebar first if left positioned */}
      {showSidebar && sidebarPosition === 'left' && (
        <div className="bg-background border-r overflow-auto">
          {sidebar}
        </div>
      )}
      
      {/* Main Content */}
      <div className="overflow-auto min-w-0">
        {children}
      </div>
      
      {/* Conditionally render sidebar last if right positioned */}
      {showSidebar && sidebarPosition === 'right' && (
        <div className="bg-background border-l overflow-auto">
          {sidebar}
        </div>
      )}
    </div>
  );
};

export default SidebarLayout; 