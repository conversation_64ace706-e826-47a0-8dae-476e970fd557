{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/cn.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/date.ts"], "sourcesContent": ["// Date utility functions\n\nexport const formatDate = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n};\n\nexport const formatDateTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n};\n\nexport const formatRelativeTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n\n  return formatDate(d);\n};\n\nexport const isTokenExpired = (expiresAt: string): boolean => {\n  const expirationTime = new Date(expiresAt).getTime();\n  const currentTime = new Date().getTime();\n  const bufferTime = 5 * 60 * 1000; // 5 minutes buffer\n  \n  return currentTime >= (expirationTime - bufferTime);\n};\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;AAElB,MAAM,aAAa,CAAC;IACzB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,cAAc,CAAC,SAAS;QAC/B,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;IACrE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;IAC5D;IAEA,OAAO,WAAW;AACpB;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,iBAAiB,IAAI,KAAK,WAAW,OAAO;IAClD,MAAM,cAAc,IAAI,OAAO,OAAO;IACtC,MAAM,aAAa,IAAI,KAAK,MAAM,mBAAmB;IAErD,OAAO,eAAgB,iBAAiB;AAC1C", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/format.ts"], "sourcesContent": ["// Formatting utility functions\n\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\nexport const formatNumber = (num: number): string => {\n  return new Intl.NumberFormat('en-US').format(num);\n};\n\nexport const formatCurrency = (amount: number, currency = 'USD'): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n};\n\nexport const truncateText = (text: string, maxLength: number): string => {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n};\n\nexport const capitalizeFirst = (str: string): string => {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n};\n\nexport const camelToTitle = (str: string): string => {\n  return str\n    .replace(/([A-Z])/g, ' $1')\n    .replace(/^./, (str) => str.toUpperCase())\n    .trim();\n};\n\nexport const generateSessionId = (): string => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;AAExB,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,MAAM,iBAAiB,CAAC,QAAgB,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,eAAe,CAAC,MAAc;IACzC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,YAAY,OACpB,OAAO,CAAC,MAAM,CAAC,MAAQ,IAAI,WAAW,IACtC,IAAI;AACT;AAEO,MAAM,oBAAoB;IAC/B,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/validation.ts"], "sourcesContent": ["// Validation utility functions\n\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const isValidPassword = (password: string): boolean => {\n  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number\n  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;\n  return passwordRegex.test(password);\n};\n\nexport const isValidUrl = (url: string): boolean => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const isValidPort = (port: number): boolean => {\n  return port >= 1 && port <= 65535;\n};\n\nexport const isValidDatabaseName = (name: string): boolean => {\n  // Basic validation for database names\n  const dbNameRegex = /^[a-zA-Z][a-zA-Z0-9_-]*$/;\n  return dbNameRegex.test(name) && name.length >= 2 && name.length <= 50;\n};\n\nexport const sanitizeInput = (input: string): string => {\n  return input.trim().replace(/[<>]/g, '');\n};\n\nexport const validateRequired = (value: any): boolean => {\n  if (typeof value === 'string') {\n    return value.trim().length > 0;\n  }\n  return value !== null && value !== undefined;\n};\n\nexport const validateMinLength = (value: string, minLength: number): boolean => {\n  return value.length >= minLength;\n};\n\nexport const validateMaxLength = (value: string, maxLength: number): boolean => {\n  return value.length <= maxLength;\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;AAExB,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,kBAAkB,CAAC;IAC9B,4DAA4D;IAC5D,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,QAAQ,KAAK,QAAQ;AAC9B;AAEO,MAAM,sBAAsB,CAAC;IAClC,sCAAsC;IACtC,MAAM,cAAc;IACpB,OAAO,YAAY,IAAI,CAAC,SAAS,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI;AACtE;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,MAAM,IAAI,GAAG,MAAM,GAAG;IAC/B;IACA,OAAO,UAAU,QAAQ,UAAU;AACrC;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/index.ts"], "sourcesContent": ["// Main utility exports\n\nexport * from './cn';\nexport * from './date';\nexport * from './format';\nexport * from './validation';\n\n// Re-export the original cn function for backward compatibility\nexport { cn } from './cn';\n"], "names": [], "mappings": "AAAA,uBAAuB;;AAEvB;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAAA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/animated-hero.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useMemo, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { MoveRight, PhoneCall } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nfunction Hero() {\r\n  const [titleNumber, setTitleNumber] = useState(0);\r\n  const titles = useMemo(\r\n    () => [\"amazing\", \"new\", \"wonderful\", \"beautiful\", \"smart\"],\r\n    []\r\n  );\r\n\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (titleNumber === titles.length - 1) {\r\n        setTitleNumber(0);\r\n      } else {\r\n        setTitleNumber(titleNumber + 1);\r\n      }\r\n    }, 2000);\r\n    return () => clearTimeout(timeoutId);\r\n  }, [titleNumber, titles]);\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      <div className=\"container mx-auto\">\r\n        <div className=\"flex gap-8 py-20 lg:py-40 items-center justify-center flex-col\">\r\n          <div>\r\n            <Button \r\n              variant=\"secondary\" \r\n              size=\"sm\" \r\n              className=\"gap-4 border-0\"\r\n              style={{\r\n                backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                color: 'var(--sidebar-text-secondary)'\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                e.currentTarget.style.backgroundColor = 'var(--sidebar-surface-secondary)';\r\n              }}\r\n            >\r\n              Read our launch article <MoveRight className=\"w-4 h-4\" />\r\n            </Button>\r\n          </div>\r\n          <div className=\"flex gap-4 flex-col\">\r\n            <h1 className=\"text-5xl md:text-7xl max-w-2xl tracking-tighter text-center font-regular\">\r\n              <span \r\n                className=\"text-center text-5xl md:text-7xl max-w-2xl font-regular\"\r\n                style={{ color: 'var(--sidebar-text-primary)' }}\r\n              >\r\n                This is something\r\n              </span>\r\n              <span className=\"relative flex w-full justify-center overflow-hidden text-center md:pb-4 md:pt-1\">\r\n                &nbsp;\r\n                {titles.map((title, index) => (\r\n                  <motion.span\r\n                    key={index}\r\n                    className=\"absolute font-semibold\"\r\n                    style={{ color: 'var(--sidebar-text-primary)' }}\r\n                    initial={{ opacity: 0, y: -100 }}\r\n                    transition={{ type: \"spring\", stiffness: 50 }}\r\n                    animate={\r\n                      titleNumber === index\r\n                        ? {\r\n                            y: 0,\r\n                            opacity: 1,\r\n                          }\r\n                        : {\r\n                            y: titleNumber > index ? -150 : 150,\r\n                            opacity: 0,\r\n                          }\r\n                    }\r\n                  >\r\n                    {title}\r\n                  </motion.span>\r\n                ))}\r\n              </span>\r\n            </h1>\r\n\r\n            <p \r\n              className=\"text-lg md:text-xl leading-relaxed tracking-tight max-w-2xl text-center\"\r\n              style={{ color: 'var(--sidebar-text-secondary)' }}\r\n            >\r\n              Managing a small business today is already tough. Avoid further\r\n              complications by ditching outdated, tedious trade methods. Our\r\n              goal is to streamline SMB trade, making it easier and faster than\r\n              ever.\r\n            </p>\r\n          </div>\r\n          <div className=\"flex flex-row gap-3\">\r\n            <Button \r\n              size=\"lg\" \r\n              className=\"gap-4 border-0\" \r\n              variant=\"outline\"\r\n              style={{\r\n                backgroundColor: 'transparent',\r\n                color: 'var(--sidebar-text-secondary)',\r\n                border: '1px solid var(--sidebar-border)'\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                e.currentTarget.style.backgroundColor = 'transparent';\r\n              }}\r\n            >\r\n              Jump on a call <PhoneCall className=\"w-4 h-4\" />\r\n            </Button>\r\n            <Button \r\n              size=\"lg\" \r\n              className=\"gap-4 border-0\"\r\n              style={{\r\n                backgroundColor: 'var(--surface-selected)',\r\n                color: 'var(--sidebar-text-primary)'\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n              }}\r\n            >\r\n              Sign up here <MoveRight className=\"w-4 h-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport { Hero };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAOA,SAAS;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACnB,IAAM;YAAC;YAAW;YAAO;YAAa;YAAa;SAAQ,EAC3D,EAAE;IAGJ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,gBAAgB,OAAO,MAAM,GAAG,GAAG;gBACrC,eAAe;YACjB,OAAO;gBACL,eAAe,cAAc;YAC/B;QACF,GAAG;QACH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAa;KAAO;IAExB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCACC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,OAAO;gCACL,iBAAiB;gCACjB,OAAO;4BACT;4BACA,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;4BACA,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;;gCACD;8CACyB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAA8B;kDAC/C;;;;;;kDAGD,8OAAC;wCAAK,WAAU;;4CAAkF;4CAE/F,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oDAEV,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAA8B;oDAC9C,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAI;oDAC/B,YAAY;wDAAE,MAAM;wDAAU,WAAW;oDAAG;oDAC5C,SACE,gBAAgB,QACZ;wDACE,GAAG;wDACH,SAAS;oDACX,IACA;wDACE,GAAG,cAAc,QAAQ,CAAC,MAAM;wDAChC,SAAS;oDACX;8DAGL;mDAjBI;;;;;;;;;;;;;;;;;0CAuBb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAgC;0CACjD;;;;;;;;;;;;kCAOH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,SAAQ;gCACR,OAAO;oCACL,iBAAiB;oCACjB,OAAO;oCACP,QAAQ;gCACV;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;;oCACD;kDACgB,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;0CAEtC,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,OAAO;gCACT;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;;oCACD;kDACc,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhD", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\nimport { useEffect, useState } from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ThemeToggleProps {\n  className?: string\n}\n\nexport function ThemeToggle({ className }: ThemeToggleProps) {\n  const [mounted, setMounted] = useState(false)\n  const { resolvedTheme, setTheme } = useTheme()\n  const isDark = resolvedTheme === \"dark\"\n\n  // useEffect only runs on the client, so now we can safely show the UI\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Render a placeholder during SSR and initial client render\n  if (!mounted) {\n    return (\n      <div\n        className={cn(\n          \"flex w-16 h-8 p-1 rounded-full cursor-pointer transition-all duration-300 bg-gray-200 border border-gray-300\",\n          className\n        )}\n        role=\"button\"\n        tabIndex={0}\n      >\n        <div className=\"flex justify-center items-center w-6 h-6 rounded-full bg-gray-300\">\n          {/* Placeholder icon */}\n          <div className=\"w-4 h-4 bg-gray-400 rounded-full\" />\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div\n      className={cn(\n        \"flex w-16 h-8 p-1 rounded-full cursor-pointer transition-all duration-300\",\n        isDark \n          ? \"bg-zinc-950 border border-zinc-800\" \n          : \"bg-white border border-zinc-200\",\n        className\n      )}\n      onClick={() => setTheme(isDark ? \"light\" : \"dark\")}\n      role=\"button\"\n      tabIndex={0}\n    >\n      <div className=\"flex justify-between items-center w-full\">\n        <div\n          className={cn(\n            \"flex justify-center items-center w-6 h-6 rounded-full transition-transform duration-300\",\n            isDark \n              ? \"transform translate-x-0 bg-zinc-800\" \n              : \"transform translate-x-8 bg-gray-200\"\n          )}\n        >\n          {isDark ? (\n            <Moon \n              className=\"w-4 h-4 text-white\" \n              strokeWidth={1.5}\n            />\n          ) : (\n            <Sun \n              className=\"w-4 h-4 text-gray-700\" \n              strokeWidth={1.5}\n            />\n          )}\n        </div>\n        <div\n          className={cn(\n            \"flex justify-center items-center w-6 h-6 rounded-full transition-transform duration-300\",\n            isDark \n              ? \"bg-transparent\" \n              : \"transform -translate-x-8\"\n          )}\n        >\n          {isDark ? (\n            <Sun \n              className=\"w-4 h-4 text-gray-500\" \n              strokeWidth={1.5}\n            />\n          ) : (\n            <Moon \n              className=\"w-4 h-4 text-black\" \n              strokeWidth={1.5}\n            />\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAWO,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,SAAS,kBAAkB;IAEjC,sEAAsE;IACtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,4DAA4D;IAC5D,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,gHACA;YAEF,MAAK;YACL,UAAU;sBAEV,cAAA,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,6EACA,SACI,uCACA,mCACJ;QAEF,SAAS,IAAM,SAAS,SAAS,UAAU;QAC3C,MAAK;QACL,UAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,2FACA,SACI,wCACA;8BAGL,uBACC,8OAAC,kMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,aAAa;;;;;6CAGf,8OAAC,gMAAA,CAAA,MAAG;wBACF,WAAU;wBACV,aAAa;;;;;;;;;;;8BAInB,8OAAC;oBACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,2FACA,SACI,mBACA;8BAGL,uBACC,8OAAC,gMAAA,CAAA,MAAG;wBACF,WAAU;wBACV,aAAa;;;;;6CAGf,8OAAC,kMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,aAAa;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/app/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport { Hero } from '@/components/ui/animated-hero';\r\nimport { Button } from '@/components/ui/button';\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { ThemeToggle } from '@/components/ui/theme-toggle';\r\n\r\nexport default function LandingPage() {\r\n  const { isAuthenticated, signIn } = useAuth();\r\n  const router = useRouter();\r\n\r\n  // Handle sign in button click\r\n  const handleSignIn = async () => {\r\n    if (isAuthenticated) {\r\n      // If already authenticated, go to dashboard\r\n      router.push('/dashboard');\r\n    } else {\r\n      // Try to sign in with stored tokens, or redirect to login (with redirect enabled)\r\n      await signIn(undefined, true);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen\" style={{ backgroundColor: 'var(--sidebar-bg)' }}>\r\n      {/* Navigation Header */}\r\n      <header \r\n        className=\"backdrop-blur supports-[backdrop-filter]:bg-background/60\"\r\n        style={{ \r\n          borderBottom: '1px solid var(--sidebar-border)',\r\n          backgroundColor: 'var(--sidebar-bg)'\r\n        }}\r\n      >\r\n        <div className=\"container mx-auto px-4 h-16 flex items-center justify-between\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <div \r\n              className=\"w-8 h-8 rounded-lg flex items-center justify-center\"\r\n              style={{ backgroundColor: 'var(--surface-selected)' }}\r\n            >\r\n              <svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-5 h-5\" style={{ color: 'var(--sidebar-icon)' }}>\r\n                <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n              </svg>\r\n            </div>\r\n            <h1 className=\"text-xl font-semibold\" style={{ color: 'var(--sidebar-text-primary)' }}>Agent Platform</h1>\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-4\">\r\n            <ThemeToggle />\r\n            <div className=\"flex items-center gap-2\">\r\n              {isAuthenticated ? (\r\n                <Button \r\n                  onClick={() => router.push('/dashboard')}\r\n                  className=\"border-0\"\r\n                  style={{\r\n                    backgroundColor: 'var(--surface-selected)',\r\n                    color: 'var(--sidebar-text-primary)'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n                  }}\r\n                >\r\n                  Go to Dashboard\r\n                </Button>\r\n              ) : (\r\n                <>\r\n                  <Button \r\n                    variant=\"ghost\" \r\n                    onClick={handleSignIn}\r\n                    className=\"border-0\"\r\n                    style={{\r\n                      color: 'var(--sidebar-text-secondary)',\r\n                      backgroundColor: 'transparent'\r\n                    }}\r\n                    onMouseEnter={(e) => {\r\n                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                    }}\r\n                    onMouseLeave={(e) => {\r\n                      e.currentTarget.style.backgroundColor = 'transparent';\r\n                    }}\r\n                  >\r\n                    Sign In\r\n                  </Button>\r\n                  <Button \r\n                    asChild\r\n                    className=\"border-0\"\r\n                    style={{\r\n                      backgroundColor: 'var(--surface-selected)',\r\n                      color: 'var(--sidebar-text-primary)'\r\n                    }}\r\n                    onMouseEnter={(e) => {\r\n                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                    }}\r\n                    onMouseLeave={(e) => {\r\n                      e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n                    }}\r\n                  >\r\n                    <Link href=\"/register\">Get Started</Link>\r\n                  </Button>\r\n                </>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Hero Section */}\r\n      <main>\r\n        <Hero />\r\n      </main>\r\n\r\n      {/* Footer */}\r\n      <footer \r\n        style={{ \r\n          borderTop: '1px solid var(--sidebar-border)',\r\n          backgroundColor: 'var(--sidebar-bg)'\r\n        }}\r\n      >\r\n        <div className=\"container mx-auto px-4 py-8\">\r\n          <div className=\"text-center text-sm\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n            <p>&copy; 2024 Agent Platform. All rights reserved.</p>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD;IAC1C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,8BAA8B;IAC9B,MAAM,eAAe;QACnB,IAAI,iBAAiB;YACnB,4CAA4C;YAC5C,OAAO,IAAI,CAAC;QACd,OAAO;YACL,kFAAkF;YAClF,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAe,OAAO;YAAE,iBAAiB;QAAoB;;0BAE1E,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,cAAc;oBACd,iBAAiB;gBACnB;0BAEA,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB;oCAA0B;8CAEpD,cAAA,8OAAC;wCAAI,SAAQ;wCAAY,MAAK;wCAAO,OAAM;wCAA6B,WAAU;wCAAU,OAAO;4CAAE,OAAO;wCAAsB;;0DAChI,8OAAC;gDAAK,GAAE;gDAA6B,QAAO;gDAAe,aAAY;gDAAI,eAAc;gDAAQ,gBAAe;;;;;;0DAChH,8OAAC;gDAAK,GAAE;gDAAoB,QAAO;gDAAe,aAAY;gDAAI,eAAc;gDAAQ,gBAAe;;;;;;0DACvG,8OAAC;gDAAK,GAAE;gDAAoB,QAAO;gDAAe,aAAY;gDAAI,eAAc;gDAAQ,gBAAe;;;;;;;;;;;;;;;;;8CAG3G,8OAAC;oCAAG,WAAU;oCAAwB,OAAO;wCAAE,OAAO;oCAA8B;8CAAG;;;;;;;;;;;;sCAGzF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2IAAA,CAAA,cAAW;;;;;8CACZ,8OAAC;oCAAI,WAAU;8CACZ,gCACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,OAAO;wCACT;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;kDACD;;;;;6DAID;;0DACE,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS;gDACT,WAAU;gDACV,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;0DACD;;;;;;0DAGD,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO;gDACP,WAAU;gDACV,OAAO;oDACL,iBAAiB;oDACjB,OAAO;gDACT;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;0DAEA,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrC,8OAAC;0BACC,cAAA,8OAAC,4IAAA,CAAA,OAAI;;;;;;;;;;0BAIP,8OAAC;gBACC,OAAO;oBACL,WAAW;oBACX,iBAAiB;gBACnB;0BAEA,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAsB,OAAO;4BAAE,OAAO;wBAA+B;kCAClF,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}