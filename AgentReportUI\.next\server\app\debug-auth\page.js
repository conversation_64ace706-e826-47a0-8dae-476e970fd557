(()=>{var e={};e.id=759,e.ids=[759],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5346:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),o=s(88170),l=s.n(o),n=s(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);s.d(t,i);let d={children:["",{children:["debug-auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,31979)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\debug-auth\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\debug-auth\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/debug-auth/page",pathname:"/debug-auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25371:(e,t,s)=>{Promise.resolve().then(s.bind(s,31979))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31979:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\NXTHUMANS\\\\internal-solutions-nxtwebsite\\\\AgentReport 2\\\\AgentReportUI\\\\src\\\\app\\\\debug-auth\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\debug-auth\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},39013:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(60687),a=s(43210),o=s(51851),l=s(97616);function n(){let[e,t]=(0,a.useState)({}),[s,n]=(0,a.useState)(!0),[i,d]=(0,a.useState)(null),{getUserProfile:c,completeOnboarding:u}=(0,o.g)(),p=async()=>{n(!0),d(null);try{let e={timestamp:new Date().toISOString(),localStorage:{},profileData:null,profileError:null,completionTest:null,completionError:null};e.localStorage={accessToken:localStorage.getItem(l.d5.ACCESS_TOKEN)?.substring(0,20)+"...",refreshToken:localStorage.getItem(l.d5.REFRESH_TOKEN)?.substring(0,20)+"...",userId:localStorage.getItem(l.d5.USER_ID),expiresAt:localStorage.getItem(l.d5.EXPIRES_AT),tokenType:localStorage.getItem(l.d5.TOKEN_TYPE)};try{console.log("\uD83D\uDD0D Testing getUserProfile...");let t=await c();e.profileData=t,console.log("✅ getUserProfile success:",t)}catch(t){e.profileError={message:t.message,status:t.response?.status,data:t.response?.data},console.error("❌ getUserProfile failed:",t)}try{console.log("\uD83D\uDD0D Testing completion endpoint...");let t=await fetch("http://localhost:8000/api/auth/onboarding/complete",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem(l.d5.ACCESS_TOKEN)}`,"Content-Type":"application/json"},body:JSON.stringify({})}),s=await t.text();e.completionTest={status:t.status,statusText:t.statusText,data:s},console.log("✅ Completion endpoint test:",e.completionTest)}catch(t){e.completionError={message:t.message},console.error("❌ Completion endpoint test failed:",t)}t(e)}catch(e){d(e.message)}finally{n(!1)}},g=async()=>{try{console.log("\uD83D\uDE80 Testing actual completion..."),await u(),console.log("✅ Completion successful!"),alert("Completion successful! Check console for details.")}catch(e){console.error("❌ Completion failed:",e),alert(`Completion failed: ${e.message}`)}};return s?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-6",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Debug Authentication"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),(0,r.jsx)("span",{children:"Loading debug information..."})]})]})})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-6",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"\uD83D\uDD0D Debug Authentication"}),(0,r.jsxs)("div",{className:"space-x-2",children:[(0,r.jsx)("button",{onClick:()=>{p()},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg",children:"Refresh"}),(0,r.jsx)("button",{onClick:g,className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg",children:"Test Completion"})]})]}),i&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:[(0,r.jsx)("h3",{className:"font-semibold text-red-800 mb-2",children:"Error"}),(0,r.jsx)("p",{className:"text-red-700",children:i})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3",children:"\uD83D\uDCE6 localStorage Data"}),(0,r.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(e.localStorage,null,2)})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3",children:"\uD83D\uDC64 Profile Data (POST /auth/me)"}),e.profileData?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsx)("span",{className:"font-medium",children:"Status:"}),(0,r.jsx)("span",{className:"ml-2 text-green-600",children:"✅ Success"})]}),(0,r.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(e.profileData,null,2)})]}):(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsx)("span",{className:"font-medium",children:"Status:"}),(0,r.jsx)("span",{className:"ml-2 text-red-600",children:"❌ Failed"})]}),(0,r.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(e.profileError,null,2)})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3",children:"\uD83C\uDFAF Completion Endpoint Test (POST /auth/onboarding/complete)"}),e.completionTest?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsx)("span",{className:"font-medium",children:"Status:"}),(0,r.jsxs)("span",{className:`ml-2 ${200===e.completionTest.status?"text-green-600":"text-red-600"}`,children:[e.completionTest.status," ",e.completionTest.statusText]})]}),(0,r.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(e.completionTest,null,2)})]}):(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsx)("span",{className:"font-medium",children:"Status:"}),(0,r.jsx)("span",{className:"ml-2 text-red-600",children:"❌ Failed"})]}),(0,r.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(e.completionError,null,2)})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:"\uD83D\uDD0D What to Look For:"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Profile Data:"})," Check if `is_new_user` is true or false"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Completion Test:"})," Check the status code (200 = success, 400 = already completed, 401 = auth issue)"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"localStorage:"})," Verify tokens are present and not expired"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Console:"})," Check browser console for detailed logs"]})]})]})]})]})})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62163:(e,t,s)=>{Promise.resolve().then(s.bind(s,39013))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,255,658,695],()=>s(5346));module.exports=r})();