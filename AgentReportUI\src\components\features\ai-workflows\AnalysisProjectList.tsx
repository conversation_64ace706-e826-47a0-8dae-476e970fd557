"use client";

import React from 'react';
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Plus, 
  Play, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  MoreVertical,
  Brain,
  Database,
  FileText
} from 'lucide-react';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { AnalysisProject } from '@/types';

interface AnalysisProjectListProps {
  projects: AnalysisProject[];
  onSelectProject: (project: AnalysisProject) => void;
  onDeleteProject: (projectId: string) => void;
  isLoading: boolean;
}

const AnalysisProjectList: React.FC<AnalysisProjectListProps> = ({
  projects,
  onSelectProject,
  onDeleteProject,
  isLoading,
}) => {
  // Format timestamp for display
  const formatTimestamp = (timestamp: Date | string) => {
    const now = new Date();
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
    const diffMs = now.getTime() - date.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    const diffDays = diffMs / (1000 * 60 * 60 * 24);

    if (diffHours < 1) {
      return 'Just now';
    } else if (diffHours < 24) {
      return `${Math.floor(diffHours)}h ago`;
    } else if (diffDays < 7) {
      return `${Math.floor(diffDays)}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Get status display configuration
  const getStatusDisplay = (status: AnalysisProject['status']) => {
    const statusConfig = {
      draft: { color: 'var(--sidebar-text-tertiary)', icon: FileText, text: 'Draft' },
      running: { color: '#f59e0b', icon: Play, text: 'Running' },
      completed: { color: '#10b981', icon: CheckCircle, text: 'Completed' },
      error: { color: '#ef4444', icon: AlertCircle, text: 'Error' }
    };
    return statusConfig[status];
  };

  // Render project card
  const renderProjectCard = (project: AnalysisProject) => {
    const status = getStatusDisplay(project.status);
    const StatusIcon = status.icon;

    return (
      <Card
        key={project.id}
        className="group cursor-pointer transition-all duration-200 border hover:shadow-lg"
        style={{
          backgroundColor: 'var(--sidebar-surface-secondary)',
          borderColor: 'var(--sidebar-border)'
        }}
        onClick={() => onSelectProject(project)}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
          e.currentTarget.style.transform = 'translateY(-1px)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'var(--sidebar-surface-secondary)';
          e.currentTarget.style.transform = 'translateY(0)';
        }}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="flex-1 min-w-0">
                <CardTitle 
                  className="text-base mb-1 truncate"
                  style={{ color: 'var(--sidebar-text-primary)' }}
                >
                  {project.name}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <StatusIcon 
                    className="h-3 w-3" 
                    style={{ color: status.color }}
                  />
                  <span 
                    className="text-xs"
                    style={{ color: status.color }}
                  >
                    {status.text}
                    {project.status === 'running' && project.progress && ` (${project.progress}%)`}
                  </span>
                </div>
              </div>
            </div>

            {/* Menu Button */}
            <div onClick={(e) => e.stopPropagation()}>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 border-0"
                    style={{ color: 'var(--sidebar-text-tertiary)' }}
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent 
                  align="end" 
                  className="border-none shadow-xl rounded-xl p-2"
                  style={{
                    backgroundColor: 'var(--sidebar-surface-secondary)',
                    color: 'var(--sidebar-text-primary)'
                  }}
                >
                  <DropdownMenuItem
                    onClick={() => onDeleteProject(project.id)}
                    className="rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200"
                    style={{ 
                      color: '#ff8583',
                      backgroundColor: 'transparent'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    Delete Project
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Description */}
          {project.description && (
            <p 
              className="text-sm mb-3 line-clamp-2"
              style={{ color: 'var(--sidebar-text-secondary)' }}
            >
              {project.description}
            </p>
          )}

          {/* Project details */}
          <div className="space-y-2">
            {project.dataSource && (
              <div className="flex items-center gap-2">
                <Database 
                  className="h-3 w-3" 
                  style={{ color: 'var(--sidebar-text-tertiary)' }} 
                />
                <span 
                  className="text-xs"
                  style={{ color: 'var(--sidebar-text-tertiary)' }}
                >
                  {project.dataSource}
                </span>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span 
                  className="text-xs"
                  style={{ color: 'var(--sidebar-text-tertiary)' }}
                >
                  {project.stepCount} {project.stepCount === 1 ? 'step' : 'steps'}
                </span>
                <div className="flex items-center gap-1">
                  <Clock 
                    className="h-3 w-3" 
                    style={{ color: 'var(--sidebar-text-tertiary)' }} 
                  />
                  <span 
                    className="text-xs"
                    style={{ color: 'var(--sidebar-text-tertiary)' }}
                  >
                    {formatTimestamp(project.updatedAt)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Project Grid */}
      {isLoading ? (
        // Loading skeleton
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <Card
              key={index}
              className="border animate-pulse"
              style={{
                backgroundColor: 'var(--sidebar-surface-secondary)',
                borderColor: 'var(--sidebar-border)'
              }}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div 
                      className="w-9 h-9 rounded-lg"
                      style={{ backgroundColor: 'var(--sidebar-text-tertiary)' }}
                    />
                    <div className="space-y-2">
                      <div 
                        className="h-4 w-24 rounded"
                        style={{ backgroundColor: 'var(--sidebar-text-tertiary)' }}
                      />
                      <div 
                        className="h-3 w-16 rounded"
                        style={{ backgroundColor: 'var(--sidebar-text-tertiary)' }}
                      />
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  <div 
                    className="h-3 w-full rounded"
                    style={{ backgroundColor: 'var(--sidebar-text-tertiary)' }}
                  />
                  <div 
                    className="h-3 w-2/3 rounded"
                    style={{ backgroundColor: 'var(--sidebar-text-tertiary)' }}
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : projects.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map(renderProjectCard)}
        </div>
      ) : (
        // Empty state
        <Card 
          className="text-center py-12 border-dashed border-2"
          style={{
            backgroundColor: 'var(--sidebar-bg)',
            borderColor: 'var(--sidebar-border)'
          }}
        >
          <CardContent className="space-y-4">
            <div 
              className="p-4 rounded-full w-fit mx-auto"
              style={{ backgroundColor: 'var(--surface-selected)' }}
            >
            </div>
            <div>
              <h3 
                className="text-lg font-semibold"
                style={{ color: 'var(--sidebar-text-primary)' }}
              >
                No analysis projects yet
              </h3>
              <p 
                className="mt-2"
                style={{ color: 'var(--sidebar-text-secondary)' }}
              >
                Create your first analysis project using the "Create Analysis" button in the top right corner to get started with AI-powered data insights
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AnalysisProjectList; 