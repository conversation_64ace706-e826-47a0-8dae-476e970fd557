"use strict";exports.id=49,exports.ids=[49],exports.modules={6211:(e,t,r)=>{r.d(t,{A0:()=>o,BF:()=>i,Hj:()=>c,XI:()=>l,nA:()=>u,nd:()=>d,r6:()=>m});var s=r(60687),a=r(43210),n=r(7766);let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:r,className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})}));l.displayName="Table";let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("thead",{ref:r,className:(0,n.cn)("[&_tr]:border-b",e),...t}));o.displayName="TableHeader";let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tbody",{ref:r,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t}));i.displayName="TableBody",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tfoot",{ref:r,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tr",{ref:r,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));c.displayName="TableRow";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("th",{ref:r,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));d.displayName="TableHead";let u=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("td",{ref:r,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));u.displayName="TableCell";let m=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("caption",{ref:r,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...t}));m.displayName="TableCaption"},95490:(e,t,r)=>{r.d(t,{A:()=>et});var s=r(60687),a=r(43210),n=r.n(a),l=r(44297),o=r(16189),i=r(30070),c=r(33872),d=r(96474),u=r(9989),m=r(26134);function h(...e){return e.filter(Boolean).join(" ")}let g=u.Kq,x=u.bL,p=u.l9,f=a.forwardRef(({className:e,sideOffset:t=4,showArrow:r=!1,...a},n)=>(0,s.jsx)(u.ZL,{children:(0,s.jsxs)(u.UC,{ref:n,sideOffset:t,className:h("relative z-50 max-w-[280px] rounded-md bg-popover text-popover-foreground px-1.5 py-1 text-xs animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a,children:[a.children,r&&(0,s.jsx)(u.i3,{className:"-my-px fill-popover"})]})}));f.displayName=u.UC.displayName;let b=m.bL,y=m.ZL;m.l9;let v=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(m.hJ,{ref:r,className:h("fixed inset-0 z-50 bg-black/60 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));v.displayName=m.hJ.displayName;let w=a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(y,{children:[(0,s.jsx)(v,{}),(0,s.jsx)(m.UC,{ref:a,className:h("fixed left-[50%] top-[50%] z-50 grid w-full max-w-[90vw] md:max-w-[800px] translate-x-[-50%] translate-y-[-50%] gap-4 border-none bg-transparent p-0 shadow-none duration-300 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",e),...r,children:(0,s.jsxs)("div",{className:"relative bg-card dark:bg-[#303030] rounded-[28px] overflow-hidden shadow-2xl p-1",children:[t,(0,s.jsxs)(m.bm,{className:"absolute right-3 top-3 z-10 rounded-full bg-background/50 dark:bg-[#303030] p-1 hover:bg-accent dark:hover:bg-[#515151] transition-all",children:[(0,s.jsx)(N,{className:"h-5 w-5 text-muted-foreground dark:text-gray-200 hover:text-foreground dark:hover:text-white"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]}));w.displayName=m.UC.displayName;let j=e=>(0,s.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,s.jsx)("path",{d:"M12 5V19",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,s.jsx)("path",{d:"M5 12H19",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),k=e=>(0,s.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,s.jsx)("path",{d:"M12 5.25L12 18.75",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,s.jsx)("path",{d:"M18.75 12L12 5.25L5.25 12",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),N=e=>(0,s.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[(0,s.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,s.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),S=a.forwardRef(({className:e,onSubmit:t,isLoading:r=!1,...n},l)=>{let o=a.useRef(null),i=a.useRef(null),[c,d]=a.useState(""),[u,m]=a.useState(null),[y,v]=a.useState(null),[S,C]=a.useState(!1);a.useImperativeHandle(l,()=>o.current,[]),a.useLayoutEffect(()=>{let e=o.current;if(e){e.style.height="auto";let t=Math.min(e.scrollHeight,200);e.style.height=`${t}px`}},[c]);let E=(c.trim().length>0||u)&&!r,_=a.useCallback(e=>{d(e.target.value),n.onChange&&n.onChange(e)},[n.onChange]),R=a.useCallback(e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),$()),n.onKeyDown&&n.onKeyDown(e)},[n.onKeyDown]),$=a.useCallback(()=>{if(!E)return;let e=c.trim();console.log("MessageInput: Submitting message",{message:e,file:u}),t?.(e,u||void 0),d(""),m(null),v(null),i.current&&(i.current.value="")},[c,u,E,t]),D=a.useCallback(()=>{i.current?.click()},[]),M=a.useCallback(e=>{let t=e.target.files?.[0];if(t&&t.type.startsWith("image/")){m(t);let e=new FileReader;e.onloadend=()=>{v(e.result)},e.readAsDataURL(t)}e.target.value=""},[]),F=a.useCallback(e=>{e.stopPropagation(),m(null),v(null),i.current&&(i.current.value="")},[]);return(0,s.jsxs)("div",{className:h("flex flex-col rounded-[28px] p-2 shadow-sm transition-colors bg-white border dark:bg-[#303030] dark:border-transparent cursor-text",e),children:[(0,s.jsx)("input",{type:"file",ref:i,onChange:M,className:"hidden",accept:"image/*","aria-label":"Upload image"}),y&&(0,s.jsxs)(b,{open:S,onOpenChange:C,children:[(0,s.jsxs)("div",{className:"relative mb-1 w-fit rounded-[1rem] px-1 pt-1",children:[(0,s.jsx)("button",{type:"button",className:"transition-transform hover:scale-105",onClick:()=>C(!0),"aria-label":"View full size image",children:(0,s.jsx)("img",{src:y,alt:"Attached image preview",className:"h-14 w-14 rounded-[1rem] object-cover"})}),(0,s.jsx)("button",{onClick:F,className:"absolute right-2 top-2 z-10 flex h-4 w-4 items-center justify-center rounded-full bg-white/50 dark:bg-[#303030] text-black dark:text-white transition-colors hover:bg-accent dark:hover:bg-[#515151]","aria-label":"Remove attached image",children:(0,s.jsx)(N,{className:"h-3 w-3"})})]}),(0,s.jsx)(w,{children:(0,s.jsx)("img",{src:y,alt:"Full size attached image",className:"w-full max-h-[95vh] object-contain rounded-[24px]"})})]}),(0,s.jsx)("textarea",{ref:o,rows:1,value:c,onChange:_,onKeyDown:R,placeholder:"Message...",disabled:r,className:"w-full resize-none border-0 bg-transparent p-3 text-foreground dark:text-white placeholder:text-muted-foreground dark:placeholder:text-gray-300 focus:ring-0 focus-visible:outline-none min-h-12 disabled:opacity-50","aria-label":"Type your message",...n}),(0,s.jsx)("div",{className:"mt-0.5 p-1 pt-0",children:(0,s.jsx)(g,{delayDuration:100,children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)("button",{type:"button",onClick:D,disabled:r,className:"flex h-8 w-8 items-center justify-center rounded-full text-foreground dark:text-white transition-colors hover:bg-accent dark:hover:bg-[#515151] focus-visible:outline-none disabled:opacity-50","aria-label":"Attach image",children:(0,s.jsx)(j,{className:"h-5 w-5"})})}),(0,s.jsx)(f,{side:"top",showArrow:!0,children:(0,s.jsx)("p",{children:"Attach image"})})]}),(0,s.jsxs)(x,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)("button",{type:"button",onClick:$,disabled:!E,className:"flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none bg-black text-white hover:bg-black/80 dark:bg-white dark:text-black dark:hover:bg-white/80 disabled:bg-black/40 dark:disabled:bg-[#515151]","aria-label":"Send message",children:(0,s.jsx)(k,{className:"h-5 w-5"})})}),(0,s.jsx)(f,{side:"top",showArrow:!0,children:(0,s.jsx)("p",{children:"Send message"})})]})]})})})]})});S.displayName="MessageInput";var C=r(26001),E=r(88920),_=r(7766);function R({placeholders:e,onChange:t,onSubmit:r,disabled:n=!1}){let[l,o]=(0,a.useState)(0);(0,a.useRef)(null);let i=(0,a.useRef)(null),c=(0,a.useRef)([]),d=(0,a.useRef)(null),[u,m]=(0,a.useState)(""),[h,g]=(0,a.useState)(!1),x=(0,a.useCallback)(()=>{if(!d.current)return;let e=i.current;if(!e)return;let t=e.getContext("2d");if(!t)return;e.width=800,e.height=800,t.clearRect(0,0,800,800);let r=getComputedStyle(d.current),s=parseFloat(r.getPropertyValue("font-size"));t.font=`${2*s}px ${r.fontFamily}`,t.fillStyle="#FFF",t.fillText(u,16,40);let a=t.getImageData(0,0,800,800).data,n=[];for(let e=0;e<800;e++){let t=4*e*800;for(let r=0;r<800;r++){let s=t+4*r;0!==a[s]&&0!==a[s+1]&&0!==a[s+2]&&n.push({x:r,y:e,color:[a[s],a[s+1],a[s+2],a[s+3]]})}}c.current=n.map(({x:e,y:t,color:r})=>({x:e,y:t,r:1,color:`rgba(${r[0]}, ${r[1]}, ${r[2]}, ${r[3]})`}))},[u]),p=e=>{let t=(e=0)=>{requestAnimationFrame(()=>{let r=[];for(let t=0;t<c.current.length;t++){let s=c.current[t];if(s.x<e)r.push(s);else{if(s.r<=0){s.r=0;continue}s.x+=Math.random()>.5?1:-1,s.y+=Math.random()>.5?1:-1,s.r-=.05*Math.random(),r.push(s)}}c.current=r;let s=i.current?.getContext("2d");s&&(s.clearRect(e,0,800,800),c.current.forEach(t=>{let{x:r,y:a,r:n,color:l}=t;r>e&&(s.beginPath(),s.rect(r,a,n,n),s.fillStyle=l,s.strokeStyle=l,s.stroke())})),c.current.length>0?t(e-8):(m(""),g(!1))})};t(e)},f=()=>{!n&&(g(!0),x(),(d.current?.value||0)&&d.current&&p(c.current.reduce((e,t)=>t.x>e?t.x:e,0)))};return(0,s.jsxs)("form",{className:(0,_.cn)("w-full relative max-w-xl mx-auto h-12 rounded-[28px] overflow-hidden transition duration-200","bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700","shadow-sm dark:shadow-gray-900/10",u&&"",n&&"opacity-50 cursor-not-allowed"),onSubmit:e=>{e.preventDefault(),!n&&(f(),r&&r(e))},children:[(0,s.jsx)("canvas",{className:(0,_.cn)("absolute pointer-events-none text-base transform scale-50 top-[20%] left-2 sm:left-8 origin-top-left pr-20",h?"opacity-100":"opacity-0"),ref:i}),(0,s.jsx)("input",{onChange:e=>{h||n||(m(e.target.value),t&&t(e))},onKeyDown:e=>{"Enter"!==e.key||h||n||f()},ref:d,value:u,type:"text",disabled:n,className:(0,_.cn)("w-full relative text-sm sm:text-base z-50 border-none bg-transparent h-full rounded-[28px] focus:outline-none focus:ring-0 pl-4 sm:pl-10 pr-20","text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400",h&&"text-transparent",n&&"cursor-not-allowed")}),(0,s.jsx)("button",{disabled:!u||n,type:"submit",className:(0,_.cn)("absolute right-2 top-1/2 z-50 -translate-y-1/2 h-8 w-8 rounded-full transition duration-200 flex items-center justify-center disabled:opacity-30",u?"bg-white dark:bg-gray-200 text-gray-800 dark:text-gray-900":"bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500"),children:(0,s.jsxs)(C.P.svg,{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:(0,_.cn)("h-4 w-4",u?"text-gray-800 dark:text-gray-900":"text-gray-400 dark:text-gray-500"),children:[(0,s.jsx)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,s.jsx)(C.P.path,{d:"M5 12l14 0",initial:{strokeDasharray:"50%",strokeDashoffset:"50%"},animate:{strokeDashoffset:u?0:"50%"},transition:{duration:.3,ease:"linear"}}),(0,s.jsx)("path",{d:"M13 18l6 -6"}),(0,s.jsx)("path",{d:"M13 6l6 6"})]})}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center rounded-full pointer-events-none",children:(0,s.jsx)(E.N,{mode:"wait",children:!u&&(0,s.jsx)(C.P.p,{initial:{y:5,opacity:0},animate:{y:0,opacity:1},exit:{y:-15,opacity:0},transition:{duration:.3,ease:"linear"},className:"text-sm sm:text-base font-normal pl-4 sm:pl-12 text-left w-[calc(100%-2rem)] truncate text-gray-500 dark:text-gray-400",children:e[l]},`current-placeholder-${l}`)})})]})}function $({onSendMessage:e}){let[t,r]=(0,a.useState)(!1),n=(0,a.useRef)(0),l=(0,a.useMemo)(()=>({title:"New Chat",icon:d.A}),[]);return(0,i.H)(l),(0,s.jsx)("div",{className:"fixed inset-0 flex flex-col overflow-hidden overflow-x-hidden z-50 bg-sidebar-bg",style:{left:"var(--sidebar-width)",top:"var(--header-height)"},children:(0,s.jsx)("div",{className:"flex flex-1 items-center justify-center min-h-0 @lg/thread:items-end",children:(0,s.jsxs)("div",{className:"max-w-2xl w-full mx-auto px-6",children:[(0,s.jsx)("div",{className:"text-center mb-8",children:(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-sidebar-text-primary text-[28px] leading-[34px] font-semibold tracking-[0.38px] mb-3 motion-safe:transition-all duration-200 inline-flex min-h-10.5 items-baseline whitespace-pre-wrap opacity-100",children:"Ready when you are."}),t&&(0,s.jsx)("p",{className:"text-sidebar-text-secondary text-[15px] leading-[18px] tracking-[-0.23px] motion-safe:transition-opacity duration-200",children:"Sending your message..."})]})}),(0,s.jsx)("div",{className:"mb-6 motion-safe:transition-all duration-200",children:(0,s.jsx)(R,{placeholders:["What would you like to know about your data?","Ask me to analyze your database records","Need help with data insights? Ask me anything","What trends should we explore in your data?","Looking for specific metrics or KPIs? Just ask"],onChange:e=>{},onSubmit:s=>{s.preventDefault();let a=Date.now();if(a-n.current<1e3)return void console.log("Rate limit exceeded, ignoring submission");if(t)return void console.log("Message already being sent, ignoring submission");let l=s.currentTarget.querySelector("input");if(l&&l.value.trim()){r(!0),n.current=a;try{e(l.value.trim()),l.value=""}catch(e){console.error("Error sending message:",e),r(!1)}setTimeout(()=>{r(!1)},2e3)}},disabled:t})})]})})})}var D=r(97616);class M{constructor(){this.eventSource=null,this.accumulatedContent="",this.isConnected=!1,this.isCompleted=!1,this.cleanup=this.cleanup.bind(this)}async startStreaming(e){let{sessionId:t,query:r,outputFormat:s="excel",onTokenReceived:a,onComplete:n,onError:l}=e;this.accumulatedContent="",this.isCompleted=!1,this.cleanup();let o=localStorage.getItem(D.d5.ACCESS_TOKEN);if(!o)return void l?.("No authentication token found");try{let e=(0,D.hY)(),i=new FormData;i.append("query",r),i.append("output_format",s),i.append("session_id",t),i.append("enable_token_streaming","true"),console.log("\uD83D\uDD17 Making POST request to:",`${e}/ask/question`),console.log("\uD83D\uDCE4 Request data (FormData):"),console.log("  - query:",r),console.log("  - output_format:",s),console.log("  - session_id:",t),console.log("  - enable_token_streaming: true");let c=await fetch(`${e}/ask/question`,{method:"POST",headers:{Authorization:`Bearer ${o}`},body:i});if(console.log("\uD83D\uDCE5 Response status:",c.status),console.log("\uD83D\uDCE5 Response headers:",Object.fromEntries(c.headers.entries())),!c.ok){let e="";try{let t=await c.text();console.error("❌ Error response body:",t),e=` - ${t}`}catch(e){console.error("❌ Could not read error response body")}throw Error(`Failed to initiate streaming: ${c.status} ${c.statusText}${e}`)}let d=c.headers.get("content-type");if(console.log("\uD83D\uDCCB Response content-type:",d),d?.includes("text/event-stream"))console.log("\uD83D\uDD04 Processing SSE stream from POST response..."),this.handleSSEResponse(c,a,n,l);else{let e=await c.json();if(console.log("\uD83D\uDCC4 JSON response received:",e),e.streaming_url)this.connectToSSE(e.streaming_url,a,n,l);else{let t=e.summary||e.message||e.content||"No response available";n?.(t,e)}}}catch(t){console.error("Error starting streaming connection:",t);let e=t instanceof Error?t.message:"Failed to start streaming connection";l?.(e)}}async handleSSEResponse(e,t,r,s){if(!e.body)return void s?.("No response body available for streaming");let a=e.body.getReader(),n=new TextDecoder;try{for(this.isConnected=!0,console.log("✅ SSE stream connected");;){let{done:e,value:l}=await a.read();if(e){console.log("\uD83D\uDCE1 SSE stream ended");break}let o=n.decode(l,{stream:!0});this.processSSEChunk(o,t,r,s)}}catch(e){console.error("❌ Error reading SSE stream:",e),s?.("Error reading streaming response")}finally{a.releaseLock(),this.isConnected=!1}}connectToSSE(e,t,r,s){console.log("\uD83D\uDD17 Connecting to SSE URL:",e),this.eventSource=new EventSource(e),this.eventSource.onopen=()=>{console.log("✅ SSE connection opened"),this.isConnected=!0},this.eventSource.onmessage=e=>{this.processSSEEvent(e.data,t,r,s)},this.eventSource.onerror=e=>{console.error("❌ SSE connection error:",e),this.isConnected=!1,this.eventSource?.readyState===EventSource.CLOSED&&(s?.("Connection to server lost"),this.cleanup())}}processSSEChunk(e,t,r,s){for(let a of e.split("\n"))if(a.startsWith("data: ")){let e=a.substring(6);this.processSSEEvent(e,t,r,s)}}processSSEEvent(e,t,r,s){try{let a=JSON.parse(e);if(console.log("\uD83D\uDCE8 SSE event received:",a),this.isCompleted&&["token_complete","conversation_complete"].includes(a.type))return void console.log("⏭️ Skipping duplicate completion event:",a.type);switch(a.type){case"token_stream":let n=a.token;console.log("\uD83D\uDD0D Raw token received:",n),n&&"string"==typeof n&&(console.log(`⏰ Processing token: "${n}"`),this.accumulatedContent+=n,t?.(n));break;case"token_complete":let l=a.data?.complete_response||a.data?.message,o=a.data;console.log("\uD83C\uDFAF Processing token_complete event"),console.log("Complete response:",l),console.log("Full response data:",o),!this.isCompleted&&l&&(console.log("✅ Using complete response from token_complete"),this.isCompleted=!0,r?.(l,o),this.cleanup());break;case"conversation_complete":let i=a.data?.message;console.log("\uD83C\uDFAF Processing conversation_complete event"),console.log("Final message:",i),this.isCompleted||(i?(console.log("✅ Using final message from conversation_complete"),this.isCompleted=!0,r?.(i)):this.accumulatedContent&&(console.log("✅ Using accumulated content from tokens"),this.isCompleted=!0,r?.(this.accumulatedContent)),this.cleanup());break;case"error":let c=a.data?.error||a.error||"Unknown streaming error";s?.(c),this.cleanup();break;default:["agent_status","agent_result"].includes(a.type)||console.warn("Unknown SSE event type:",a.type)}}catch(t){console.error("Error parsing SSE event:",t),console.error("Raw event data:",e)}}stopStreaming(){this.cleanup()}extractCleanToken(e){if(console.log(`🔍 Extracting clean token from: "${e}"`),['{\n  "','\n  "message','message": "','",\n}','."\n}','{\n  "message": "','"',"\n","  ","{","}",'": "'].some(t=>e===t))return console.log(`⏭️ Skipping JSON structure token: "${e}"`),null;if(e.includes('": "')){let t=e.match(/": "(.+)/),r=t?t[1]:null;return console.log(`📝 Extracted from JSON key-value: "${r}"`),r}if(e.endsWith('",')||e.endsWith('."\n}')||e.endsWith('"')){let t=e.replace(/[",\n}]+$/,"");return console.log(`🧹 Cleaned trailing JSON: "${t}"`),t}if(e.startsWith('{\n  "')||e.startsWith('"')){let t=e.replace(/^[{\n\s"]+/,"");return console.log(`🧹 Cleaned leading JSON: "${t}"`),t||null}return console.log(`✅ Clean content token: "${e}"`),e}cleanup(){this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.isConnected=!1,this.isCompleted=!1}getConnectionStatus(){return this.isConnected}getAccumulatedContent(){return this.accumulatedContent}}let F=new M;var L=r(35056),T=r(85629),A=r(3832);let I=e=>{if(!e)return"";let t=e;return(t=(t=(t=(t=(t=(t=(t=(t=t.split("###").map(e=>e.trim()).filter(Boolean).map((e,t)=>{let r=e.trim(),s=r.match(/^([🏆📊📋💡🔍📈📉💰🎯⚡🚀💎🔥⭐🎉🎊🏅🎖️💹💲💸💵💴💶💷🔢📝📄📃📑🔎📖📚📓📕📗📘📙📒📰🗞️])\s*([\s\S]+)$/);if(s){let[,e,t]=s,a=t.split("\n"),n=a[0].trim(),l=a.slice(1).join("\n").trim();r=l?`## ${e} ${n}

${l}`:`## ${e} ${n}`}return r}).join("\n\n")).replace(/•\s*/g,"- ")).replace(/(\d+\.\s*\*\*[^*]+\*\*)/g,"\n$1")).replace(/(:)\s*(-\s+)/g,"$1\n\n$2")).replace(/^([^|\n#]+?)\s+\|\s+(.+)$/gm,"### $1\n\n| $2")).replace(/\s-\s\*\*/g,"\n- **")).replace(/\n{3,}/g,"\n\n")).replace(/^\n+/,"")).trim()},q=(()=>{let e="",t="";return(r,s=!1)=>{if(r===e)return t;if(!s||r.length<100){let s=I(r);return e=r,t=s,s}if(50>Math.abs(r.length-e.length)&&s)return t+r.slice(e.length);let a=I(r);return e=r,t=a,a}})(),P=n().memo(({content:e,className:t="",isStreaming:r=!1})=>{let n=(0,a.useRef)(""),l=(0,a.useRef)("");(0,a.useRef)("");let o=(0,a.useRef)(0),i=(0,a.useMemo)(()=>{if(o.current++,r&&o.current>10&&e===n.current||e===n.current)return l.current;let t=q(e,r);return(!r||e.length-n.current.length>100)&&(n.current=e,l.current=t),t},[e,r]);(0,a.useMemo)(()=>{},[e,i,r]);let c=(0,a.useRef)(0);(0,a.useMemo)(()=>{Math.abs(e.length-c.current)>200&&(o.current=0,c.current=e.length)},[e.length]);let d=(0,a.useMemo)(()=>(0,s.jsx)(L.oz,{remarkPlugins:[T.A],rehypePlugins:[A.A],components:{h1:({children:e})=>(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2 mb-4 mt-6",children:e}),h2:({children:e})=>(0,s.jsx)("h2",{className:"text-xl font-semibold text-blue-600 dark:text-blue-400 border-b border-gray-200 dark:border-gray-700 pb-2 mb-4 mt-6",children:e}),h3:({children:e})=>(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-700 dark:text-gray-300 mb-3 mt-5 border-l-4 border-blue-300 dark:border-blue-600 pl-3",children:e}),h4:({children:e})=>(0,s.jsx)("h4",{className:"text-base font-medium text-gray-700 dark:text-gray-300 mb-2 mt-2",children:e}),h5:({children:e})=>(0,s.jsx)("h5",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 mt-2",children:e}),h6:({children:e})=>(0,s.jsx)("h6",{className:"text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 mt-2",children:e}),strong:({children:e})=>(0,s.jsx)("strong",{className:"font-semibold text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 px-1 py-0.5 rounded",children:e}),em:({children:e})=>(0,s.jsx)("em",{className:"italic text-gray-800 dark:text-gray-200",children:e}),ul:({children:e})=>(0,s.jsx)("ul",{className:"list-disc pl-6 my-4 space-y-2 text-gray-800 dark:text-gray-200",children:e}),ol:({children:e})=>(0,s.jsx)("ol",{className:"list-decimal pl-6 my-4 space-y-2 text-gray-800 dark:text-gray-200",children:e}),li:({children:e})=>(0,s.jsx)("li",{className:"leading-relaxed py-0.5 text-sm",children:e}),p:({children:e})=>(0,s.jsx)("p",{className:"mb-4 leading-relaxed text-gray-800 dark:text-gray-200 text-sm",children:e}),code:({children:e,inline:t})=>t?(0,s.jsx)("code",{className:"bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono text-gray-900 dark:text-gray-100",children:e}):(0,s.jsx)("pre",{className:"bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-3 my-3 overflow-x-auto",children:(0,s.jsx)("code",{className:"text-sm font-mono text-gray-900 dark:text-gray-100",children:e})}),table:({children:e})=>(0,s.jsx)("div",{className:"overflow-x-auto my-3",children:(0,s.jsx)("table",{className:"w-full border-collapse border border-gray-300 dark:border-gray-600",children:e})}),th:({children:e})=>(0,s.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 px-3 py-2 text-left font-semibold text-gray-900 dark:text-gray-100",children:e}),td:({children:e})=>(0,s.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-800 dark:text-gray-200",children:e}),blockquote:({children:e})=>(0,s.jsx)("blockquote",{className:"border-l-4 border-blue-500 dark:border-blue-400 pl-4 my-3 italic text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 py-2 rounded-r",children:e}),a:({children:e,href:t})=>(0,s.jsx)("a",{href:t,className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline",target:"_blank",rel:"noopener noreferrer",children:e}),hr:()=>(0,s.jsx)("hr",{className:"my-4 border-gray-300 dark:border-gray-600"})},children:i||"No content available"}),[i]);return(0,s.jsx)("div",{className:(0,_.cn)("markdown-content",t),children:d})});P.displayName="MarkdownRenderer";let z=e=>({hasExecutiveSummary:/## Executive Summary/i.test(e),hasKeyInsights:/### Key Insights/i.test(e),hasBusinessImplications:/### Business Implications/i.test(e),hasRecommendations:/### Recommendation/i.test(e),hasStatisticalOverview:/### Statistical Overview/i.test(e),hasNumberedResults:/^\d+\.\s+\*\*/.test(e.replace(/\n/g," ")),hasBulletPoints:/^\*\s+\*\*/.test(e.replace(/\n/g," "))}),O=e=>{if(!e)return"";let t=e,r=z(e);return r.hasExecutiveSummary&&(t=t.replace(/## Executive Summary/gi,"## \uD83D\uDCCA Executive Summary")),r.hasKeyInsights&&(t=t.replace(/### Key Insights:/gi,"### \uD83D\uDCA1 Key Insights:")),r.hasBusinessImplications&&(t=t.replace(/### Business Implications:/gi,"### \uD83C\uDFAF Business Implications:")),r.hasRecommendations&&(t=t.replace(/### Recommendation:/gi,"### \uD83D\uDE80 Recommendation:")),r.hasStatisticalOverview&&(t=t.replace(/### Statistical Overview/gi,"### \uD83D\uDCC8 Statistical Overview")),t},W=n().memo(({content:e,className:t="",isStreaming:r=!1})=>{let n=(0,a.useMemo)(()=>z(e),[e]),l=(0,a.useMemo)(()=>O(e),[e]),o=(0,a.useMemo)(()=>Object.values(n).some(Boolean),[n]);return(0,s.jsx)("div",{className:(0,_.cn)("analytical-content",t),children:(0,s.jsx)(P,{content:l,isStreaming:r,className:(0,_.cn)(o&&"analytical-markdown",t)})})});W.displayName="AnalyticalContentRenderer";let B={speed:0,enableCursor:!0,cursorChar:"|"},K=n().memo(({content:e,isStreaming:t,className:r="",typewriterConfig:n={}})=>{let[l,o]=(0,a.useState)(""),[i,c]=(0,a.useState)(!1),d=(0,a.useRef)(null),[u,m]=(0,a.useState)(0),h=(0,a.useMemo)(()=>({...B,...n}),[n]);(0,a.useEffect)(()=>{if(0===h.speed)o(e),m(e.length);else{if(!e){o(""),m(0);return}if(e.length<l.length){o(e),m(e.length);return}if(l===e&&!t)return;let r=l.length,s=setInterval(()=>{r<e.length?(r++,o(e.slice(0,r)),m(r)):clearInterval(s)},h.speed);return()=>clearInterval(s)}},[e,h.speed,t,l.length]);let g=(0,a.useMemo)(()=>(0,s.jsx)(W,{content:l,isStreaming:t,className:"inline"}),[l,t]);return(0,a.useEffect)(()=>{if(!h.enableCursor||!t){c(!1),d.current&&(clearInterval(d.current),d.current=null);return}return c(!0),d.current=setInterval(()=>{c(e=>!e)},600),()=>{d.current&&(clearInterval(d.current),d.current=null)}},[t,h.enableCursor]),(0,a.useEffect)(()=>()=>{d.current&&clearInterval(d.current)},[]),(0,s.jsx)("div",{className:`streaming-message ${r}`,children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("div",{className:"text-sm",children:[g,t&&h.enableCursor&&(0,s.jsx)("span",{className:`inline-block ml-0.5 text-blue-500 font-bold transition-opacity duration-150 ${i?"opacity-100":"opacity-0"}`,style:{width:"2px"},children:h.cursorChar})]}),t&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-50/20 to-transparent rounded pointer-events-none animate-pulse"})]})})});K.displayName="StreamingMessage";var U=r(29523),H=r(82164),J=r(31158),Y=r(25334),Q=r(41862),V=r(93613),X=r(6211),Z=r(33103);let G=({outputFiles:e,className:t=""})=>{let[r,n]=(0,a.useState)(null),[l,o]=(0,a.useState)(!1),[i,c]=(0,a.useState)(null),[d,u]=(0,a.useState)(!0),m=e=>{let t=e.trim().split("\n");if(0===t.length)return{headers:[],rows:[]};let r=e=>{let t=[],r="",s=!1;for(let a=0;a<e.length;a++){let n=e[a];'"'===n?s&&'"'===e[a+1]?(r+='"',a++):s=!s:","!==n||s?r+=n:(t.push(r.trim()),r="")}return t.push(r.trim()),t};return{headers:r(t[0]),rows:t.slice(1).map(e=>r(e))}},h=e=>{try{let t=Z.LF(e,{type:"array"}),r=t.SheetNames[0],s=t.Sheets[r],a=Z.Wp.sheet_to_json(s,{header:1});if(0===a.length)return{headers:[],rows:[]};let n=a[0]||[],l=a.slice(1);return{headers:n,rows:l}}catch(e){throw console.error("Error parsing Excel file:",e),Error("Failed to parse Excel file")}},g=async(e,t,r)=>{o(!0),c(null);try{let s,a=await fetch(e);if(!a.ok)throw Error(`Failed to fetch data: ${a.status} ${a.statusText}`);if("csv"===t.toLowerCase()){let e=await a.text();s=m(e)}else if("xlsx"===t.toLowerCase()||"xls"===t.toLowerCase()){let e=await a.arrayBuffer();s=h(e)}else throw Error(`Unsupported file format: ${t}`);n({headers:s.headers,rows:s.rows,fileName:r,format:t})}catch(e){console.error("Failed to fetch file data:",e),c(e instanceof Error?e.message:"Failed to load data")}finally{o(!1)}};(0,a.useEffect)(()=>{if(e&&e.length>0){let t=e[0],r=t.file_path.split("/").pop()||`data.${t.format}`;g(t.file_path,t.format,r)}},[e]);let x=(e,t)=>{let r=document.createElement("a");r.href=e,r.download=t,r.target="_blank",document.body.appendChild(r),r.click(),document.body.removeChild(r)};if(!e||0===e.length)return null;let p=e[0],f=p.file_path.split("/").pop()||`data.${p.format}`;return(0,s.jsxs)("div",{className:`border border-blue-200 dark:border-blue-800 rounded-lg bg-blue-50/30 dark:bg-blue-900/10 p-4 mt-4 ${t}`,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,s.jsx)(H.A,{className:"w-4 h-4 text-blue-600 dark:text-blue-400"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-sm text-blue-900 dark:text-blue-100",children:"Query Results Data"}),(0,s.jsxs)("p",{className:"text-xs text-blue-700 dark:text-blue-300",children:[p.database_name," • ",p.format.toUpperCase()," • ",r?.rows.length||0," rows"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(U.$,{onClick:()=>x(p.file_path,f),size:"sm",variant:"outline",className:"text-xs",children:[(0,s.jsx)(J.A,{className:"w-3 h-3 mr-1"}),"Download"]}),(0,s.jsxs)(U.$,{onClick:()=>window.open(p.file_path,"_blank"),size:"sm",variant:"outline",className:"text-xs",children:[(0,s.jsx)(Y.A,{className:"w-3 h-3 mr-1"}),"Open"]})]})]}),l&&(0,s.jsxs)("div",{className:"flex items-center justify-center py-8 text-gray-600 dark:text-gray-400",children:[(0,s.jsx)(Q.A,{className:"w-4 h-4 animate-spin mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:"Loading table data..."})]}),i&&!l&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-red-600 dark:text-red-400 text-sm mb-3 p-3 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800",children:[(0,s.jsx)(V.A,{className:"w-4 h-4 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Failed to load table data"}),(0,s.jsx)("p",{className:"text-xs mt-1",children:i})]})]}),d&&r&&!i&&!l&&(0,s.jsxs)("div",{className:"max-h-96 overflow-auto border border-blue-200 dark:border-blue-700 rounded-lg bg-white dark:bg-gray-900",children:[(0,s.jsxs)(X.XI,{children:[(0,s.jsxs)(X.r6,{className:"text-xs text-blue-600 dark:text-blue-400 font-medium py-2",children:["Showing ",Math.min(r.rows.length,100)," of ",r.rows.length," rows • ",r.fileName]}),(0,s.jsx)(X.A0,{children:(0,s.jsx)(X.Hj,{children:r.headers.map((e,t)=>(0,s.jsx)(X.nd,{className:"text-xs font-medium",children:e||`Column ${t+1}`},t))})}),(0,s.jsx)(X.BF,{children:r.rows.slice(0,100).map((e,t)=>(0,s.jsx)(X.Hj,{children:r.headers.map((t,r)=>(0,s.jsx)(X.nA,{className:"text-xs",children:e[r]||""},r))},t))})]}),r.rows.length>100&&(0,s.jsxs)("div",{className:"p-2 text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 border-t",children:["Showing first 100 rows of ",r.rows.length," total rows. Download the full file to see all data."]})]}),r&&!l&&(0,s.jsx)("div",{className:"mt-2 flex justify-center",children:(0,s.jsx)(U.$,{onClick:()=>u(!d),size:"sm",variant:"ghost",className:"text-xs",children:d?"Collapse Table":"Expand Table"})}),!d&&r&&!l&&(0,s.jsxs)("div",{className:"text-center py-4 text-gray-600 dark:text-gray-400",children:[(0,s.jsx)(H.A,{className:"w-6 h-6 mx-auto mb-2 opacity-50"}),(0,s.jsxs)("p",{className:"text-sm",children:["Table with ",r.rows.length," rows and ",r.headers.length," columns"]}),(0,s.jsx)("p",{className:"text-xs mt-1",children:'Click "Expand Table" to view data'})]})]})},ee=new Set,et=({chatId:e})=>{let[t,r]=(0,a.useState)(!1),[n,u]=(0,a.useState)([]),[m,h]=(0,a.useState)(null),[g,x]=(0,a.useState)(null),[p,f]=(0,a.useState)(!1),[b,y]=(0,a.useState)(!1),v=(0,a.useRef)(null),w=(0,a.useRef)([]),j=(0,a.useRef)(null),k=(0,a.useRef)(0),N=(0,a.useRef)(!1),C=(0,a.useRef)(!1),E=(0,a.useRef)(null),{chatHistory:_,activeChat:R,chatMessages:D,isLoadingHistory:M,isLoadingChats:L,pendingFirstMessage:T,setPendingFirstMessage:A,setActiveChat:I,addMessageToChat:q,addChat:P,refreshChatList:z}=(0,l.m)(),O=(0,o.useRouter)(),B=R&&D[R.session_id]||[],U=(0,a.useMemo)(()=>({title:R?`Data Chat ${R.id.startsWith("chat_sess_")?R.id.slice(9,17):R.id.slice(0,8)}`:"New Chat",icon:R?c.A:d.A}),[R]);(0,i.H)(U),(0,a.useEffect)(()=>{if(console.log("Chat component useEffect triggered with chatId:",e),console.log("Current activeChat:",R?.id),console.log("Chat history loaded:",_.length>0),console.log("Is loading chats:",L),!e){R&&(console.log("Clearing active chat for new chat scenario"),I(null));return}if(R&&R.id===e)return void console.log("Chat is already active, no action needed");f(!1);let t=_.find(t=>t.id===e);if(t){console.log("Found chat in local history, setting as active:",t.id),I(t);return}return L?void console.log("Still loading chats, waiting..."):0===_.length?void console.log("Chat history not loaded yet, waiting for backend data..."):void(console.log("Chat not found in loaded history, marking as not found:",e),f(!0))},[e,R?.id,L,_.length]),(0,a.useEffect)(()=>{if(!C.current&&(console.log("\uD83D\uDD0D pendingFirstMessage useEffect triggered:",{pendingFirstMessage:!!T,activeChat:!!R,activeChatId:R?.id,chatId:e,isLoading:t,chatIdMatches:R?.id===e}),T&&R&&e&&R.id===e&&!ee.has(e))){C.current=!0,ee.add(e),console.log("✅ Processing pending first message after navigation:",T),r(!0),console.log("\uD83D\uDD04 pendingFirstMessage: Set isLoading to true"),h(null),x(`welcome_stream_${Date.now()}_${Math.random().toString(36).substring(2,11)}`),E.current=null,A(null);let t=!1;(async()=>{try{await F.startStreaming({sessionId:R.session_id,query:T,outputFormat:"excel",onTokenReceived:e=>{console.log("\uD83D\uDD24 Welcome message token received:",e),r(!1),e&&"string"==typeof e&&V(e)},onComplete:async(e,s)=>{r(!1);let a=`${e}_${Date.now()}`;if(t||E.current===a)return void console.log("⏭️ Skipping duplicate onComplete call");t=!0,E.current=a,console.log("✅ Welcome message streaming complete:",e),console.log("\uD83D\uDCCA Complete response data:",s),j.current&&(clearTimeout(j.current),j.current=null),w.current=[];let n={role:"agent",content:e,timestamp:new Date,isStreaming:!1,outputFiles:s?.output_files||[],sqlQueries:s?.sql_queries||{}};q(R.session_id,n),h(null),x(null),setTimeout(async()=>{await z()},500)},onError:e=>{if(console.error("Streaming failed:",e),r(!1),j.current&&(clearTimeout(j.current),j.current=null),w.current=[],R){let e={role:"agent",content:"Sorry, there was an error processing your request. Please try again.",timestamp:new Date};q(R.session_id,e)}h(null),x(null)}})}catch(e){if(console.error("Failed to start streaming for first message:",e),r(!1),j.current&&(clearTimeout(j.current),j.current=null),w.current=[],R){let e={role:"agent",content:"Sorry, there was an error processing your request. Please try again.",timestamp:new Date};q(R.session_id,e)}h(null),x(null)}})()}},[T,R,e]),(0,a.useEffect)(()=>{C.current=!1},[e]),(0,a.useEffect)(()=>()=>{F.stopStreaming(),j.current&&clearTimeout(j.current)},[]);let H=()=>{v.current?.scrollIntoView({behavior:"smooth"})},J={TOKEN_DELAY:400,MIN_DELAY:200,MAX_DELAY:600},Y=e=>{let t=J.TOKEN_DELAY,r=e.trim().length;return r<=2?J.MIN_DELAY:r>8?J.MAX_DELAY:t},Q=()=>{if(0===w.current.length){j.current&&(clearTimeout(j.current),j.current=null);return}let e=w.current.shift();e&&e.trim()?(h(t=>{if(!t)return{role:"agent",content:e,timestamp:new Date,isStreaming:!0};let r=t.content+e;return r===t.content?t:{...t,content:r}}),w.current.length>0?j.current=setTimeout(Q,Y(e)):j.current&&(clearTimeout(j.current),j.current=null)):w.current.length>0&&(j.current=setTimeout(Q,50))},V=e=>{if(!e||!e.trim())return;let t=Date.now();!(t-(w.current.length>0&&w.current.lastAddTime||0)<10)&&(w.current.push(e),w.current.lastAddTime=t,j.current||Q())};(0,a.useEffect)(()=>{H()},[B,m]);let X=(0,a.useCallback)(async(e,t)=>{if(console.log("Chat: Message submitted",{message:e,file:t}),!e.trim()&&!t)return void console.log("Chat: No message or file provided, skipping submit");if(!R)return void console.error("Chat: Cannot send message - no active chat");if(N.current||b)return void console.log("Chat: Message already being sent, ignoring duplicate request");let s=Date.now();if(s-k.current<1e3)return void console.log("Chat: Rate limit exceeded, ignoring message send");N.current=!0,y(!0),k.current=s;try{t&&(console.log("Chat: Processing attached file",t.name),u(e=>[...e,{name:t.name,type:t.type}]));let s={role:"user",content:e.trim(),timestamp:new Date};if(q(R.session_id,s),r(!0),m&&m.content.trim()){console.log("Chat: Saving previous streaming message to history");let e={role:"agent",content:m.content,timestamp:new Date,isStreaming:!1,outputFiles:m.outputFiles||[],sqlQueries:m.sqlQueries||{}};q(R.session_id,e)}h(null);let a=`stream_${Date.now()}_${Math.random().toString(36).substring(2,11)}`;x(a),E.current=null;let n=!1;console.log("Chat: Starting streaming for session:",R.session_id),await F.startStreaming({sessionId:R.session_id,query:e.trim(),outputFormat:"excel",onTokenReceived:e=>{e&&"string"==typeof e&&(r(!1),V(e))},onComplete:(e,t)=>{console.log("Chat: Streaming complete",{finalContent:e,outputFiles:t?.output_files}),r(!1);let s=`${e}_${Date.now()}`;if(n||E.current===s)return void console.log("Chat: Skipping duplicate onComplete call");n=!0,E.current=s,j.current&&(clearTimeout(j.current),j.current=null),w.current=[];let a={role:"agent",content:e,timestamp:new Date,isStreaming:!1,outputFiles:t?.output_files||[],sqlQueries:t?.sql_queries||{}};q(R.session_id,a),h(null),x(null)},onError:e=>{console.error("Chat: Streaming failed:",e),r(!1),j.current&&(clearTimeout(j.current),j.current=null),w.current=[];let t={role:"agent",content:"Sorry, there was an error processing your request. Please try again.",timestamp:new Date};q(R.session_id,t),h(null),x(null)}})}catch(e){if(console.error("Chat: Error in message submission:",e),r(!1),R){let e={role:"agent",content:"Sorry, there was an error processing your request. Please try again.",timestamp:new Date};q(R.session_id,e)}}finally{setTimeout(()=>{y(!1),N.current=!1},1500)}},[R,b,m,q,V]);return(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full w-full md:p-6 bg-sidebar-bg overflow-y-auto",onDragOver:e=>{e.preventDefault(),e.stopPropagation()},onDrop:e=>{e.preventDefault(),e.stopPropagation();let t=e.dataTransfer.files?.[0];t&&u(e=>[...e,{name:t.name,type:t.type}])},children:[!R&&!e&&(0,s.jsx)($,{onSendMessage:async e=>{console.log("Sending first message to create new chat:",e);let t=P();await I(t);let s={role:"user",content:e,timestamp:new Date};q(t.session_id,s),r(!0),console.log("\uD83D\uDD04 WelcomeMessage: Set isLoading to true"),h(null),x(`welcome_stream_${Date.now()}_${Math.random().toString(36).substring(2,11)}`),O.replace(`/chat/${t.id}`),console.log("\uD83D\uDD04 WelcomeMessage: Navigated to chat, setting pending message"),A(e)}}),(0,s.jsxs)("div",{className:"w-full max-w-3xl flex flex-col flex-1 h-full",children:[(0,s.jsxs)("div",{className:"flex-grow space-y-8 pr-2 relative",children:[(R&&M||e&&!R&&(L||!p))&&(0,s.jsx)("div",{className:"flex flex-1 items-center justify-center min-h-[400px] h-full w-full",children:(0,s.jsx)("div",{className:"max-w-md w-full text-center mx-auto",children:(0,s.jsxs)("div",{className:"mb-6 p-6 rounded-lg bg-blue-50 dark:bg-blue-900 text-blue-900 dark:text-blue-100 border border-blue-200 dark:border-blue-700 shadow flex flex-col items-center justify-center text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-3"}),(0,s.jsx)("h3",{className:"text-lg font-bold mb-2",children:R&&M?"Loading conversation...":"Loading chat..."}),(0,s.jsx)("p",{className:"text-sm",children:R&&M?"Fetching your chat history from the server.":"Finding your chat and loading the conversation."})]})})}),e&&p&&!L&&(0,s.jsx)("div",{className:"flex flex-1 items-center justify-center min-h-[400px] h-full w-full",children:(0,s.jsx)("div",{className:"max-w-md w-full text-center mx-auto",children:(0,s.jsxs)("div",{className:"mb-6 p-6 rounded-lg bg-red-50 dark:bg-red-900 text-red-900 dark:text-red-100 border border-red-200 dark:border-red-700 shadow flex flex-col items-center justify-center text-center",children:[(0,s.jsx)("div",{className:"text-red-500 mb-3",children:(0,s.jsx)("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h3",{className:"text-lg font-bold mb-2",children:"Chat Not Found"}),(0,s.jsx)("p",{className:"text-sm mb-4",children:"The chat you're looking for doesn't exist or may have been deleted."}),(0,s.jsx)("button",{onClick:()=>O.push("/chat"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Start New Chat"})]})})}),R&&!M&&B.map((e,t)=>(0,s.jsx)("div",{className:`flex ${"user"===e.role?"justify-end":"justify-start"}`,children:(0,s.jsxs)("div",{className:`max-w-[85%] px-5 py-2.5 ${"user"===e.role?"bg-gray-200/50 dark:bg-gray-700/50 rounded-3xl text-gray-900 dark:text-gray-100":"text-gray-900 dark:text-gray-100"}`,children:["user"===e.role?(0,s.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:e.content}):(0,s.jsx)(W,{content:e.content,isStreaming:!1,className:"text-sm"}),"agent"===e.role&&e.outputFiles&&e.outputFiles.length>0&&(0,s.jsx)("div",{className:"mt-3",children:(0,s.jsx)(G,{outputFiles:e.outputFiles})})]})},t)),m&&R&&(0,s.jsx)("div",{className:"flex justify-start",children:(0,s.jsxs)("div",{className:"max-w-[85%] px-5 py-2.5 text-gray-900 dark:text-gray-100",children:[(0,s.jsx)(K,{content:m.content,isStreaming:m.isStreaming||!1,className:"text-gray-900 dark:text-gray-100"}),m.outputFiles&&m.outputFiles.length>0&&(0,s.jsx)("div",{className:"mt-3",children:(0,s.jsx)(G,{outputFiles:m.outputFiles})})]})}),t&&R&&!m&&(0,s.jsx)("div",{className:"flex justify-start mb-4 animate-in fade-in duration-300",children:(0,s.jsx)("div",{className:"max-w-[70%] px-4 py-3 rounded-2xl bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 shadow-sm border border-gray-100 dark:border-gray-600",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0ms",animationDuration:"1s"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"150ms",animationDuration:"1s"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"300ms",animationDuration:"1s"}})]}),(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-300 font-medium",children:"AI is thinking..."})]})})}),(0,s.jsx)("div",{ref:v})]}),(R||e&&!p)&&(0,s.jsx)("div",{className:"mt-6 border-t border-gray-200 dark:border-gray-700 pt-6",children:(0,s.jsx)(S,{onSubmit:X,placeholder:R?"Message...":"Loading chat...",disabled:!R||t||b,isLoading:t||b,className:"w-full max-w-full"})})]})]})}},99761:(e,t,r)=>{r.d(t,{Ay:()=>o});var s=r(60687),a=r(43210),n=r(91010),l=r(16189);function o({children:e,requireAuth:t=!0,redirectTo:r="/login"}){let{isAuthenticated:o,isLoading:i,isNewUser:c,signIn:d}=(0,n.A)(),u=(0,l.useRouter)();(0,l.usePathname)();let[m,h]=(0,a.useState)(!0),[g,x]=(0,a.useState)(!1);return m||i?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Checking authentication..."})]})}):t&&!o?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Authentication Required"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please sign in to access this page."}),(0,s.jsx)("button",{onClick:()=>u.push(r),className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Sign In"})]})}):(0,s.jsx)(s.Fragment,{children:e})}r(97616)}};