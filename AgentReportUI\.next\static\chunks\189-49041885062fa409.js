"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[189],{45786:(e,t,a)=>{a.d(t,{Sn:()=>r,bw:()=>i,d5:()=>c,hY:()=>s});let r={AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",LOGOUT:"/auth/logout",REFRESH:"/auth/refresh",ME:"/auth/me",GOOGLE:"/auth/google",COMPLETE_ONBOARDING:"/auth/onboarding/complete"},DATABASES:{LIST:"/databases/listdatabases",CONNECT:"/databases/connectdatabase",DISCONNECT:"/databases/disconnectdatabase",SCHEMA:"/databases/schema"},QUERY:{ASK:"/query/ask"},CHAT:{LIST:"/chat/list",HISTORY:"/chat/history",DELETE:"/chat/delete"},REPORTS:{LIST:"/reports/list"},ASK:{QUESTION:"/ask/question"},CHART:{QUERY:"/chart/query",TYPES:"/chart/types",VALIDATE:"/chart/validate",HEALTH:"/chart/health"},DASHBOARD:{LIST:"/dashboard/list",CREATE:"/dashboard/create",GET:"/dashboard/get",UPDATE:"/dashboard/update",DELETE:"/dashboard/delete"},ANALYSIS:{LIST:"/analysis/list",CREATE:"/analysis/create",GET:"/analysis/get",UPDATE:"/analysis/update",DELETE:"/analysis/delete",DATA:"/analysis/data",STEPS:"/analysis/steps",EXECUTE_STEP:"/analysis/execute-step",UPLOAD_FILE:"/analysis/upload-file",DELETE_FILE:"/analysis/delete-file"}},o={development:{defaultApiBase:"http://localhost:8000",name:"Development",enableDebugLogs:!0},production:{defaultApiBase:"https://agentreportbackend.vercel.app",name:"Production",enableDebugLogs:!1}},n=()=>o.production||o.development,s=()=>{let e="http://localhost:8000",t=n();!e&&(e=t.defaultApiBase,t.enableDebugLogs&&console.log("\uD83D\uDD27 Using default ".concat(t.name," API URL: ").concat(e)));let a="".concat(e.replace(/\/+$/,""),"/api");return t.enableDebugLogs&&console.log("\uD83D\uDD17 ".concat(t.name," API Base URL:"),a),a},c={ACCESS_TOKEN:"accessToken",REFRESH_TOKEN:"refreshToken",TOKEN_TYPE:"tokenType",USER_ID:"userId",EXPIRES_AT:"expiresAt"},i={HOME:"/",LOGIN:"/login",REGISTER:"/register",AUTH:{CALLBACK:"/auth/callback"},OAUTH:{CALLBACK:"/oauth/callback"},ONBOARDING:"/onboarding",DASHBOARD:"/dashboard",CHAT:"/chat",DATASOURCES:"/datasources",REPORTS:"/reports",CHAT_WITH_ID:e=>"/chat/".concat(e)};i.HOME,i.LOGIN,i.REGISTER,i.AUTH.CALLBACK,i.OAUTH.CALLBACK,i.ONBOARDING,i.DASHBOARD,i.CHAT,i.DATASOURCES,i.REPORTS,i.DASHBOARD},58189:(e,t,a)=>{a.d(t,{K:()=>i,g:()=>l});var r=a(95155),o=a(12115),n=a(23464),s=a(45786);let c=(0,o.createContext)(void 0),i=e=>{let{children:t}=e,a=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");let a=new FormData;return a.append("query",e.query),a.append("output_format",e.output_format||"excel"),e.session_id&&a.append("session_id",e.session_id),e.target_databases&&a.append("target_databases",JSON.stringify(e.target_databases)),e.target_tables&&a.append("target_tables",JSON.stringify(e.target_tables)),e.target_columns&&a.append("target_columns",JSON.stringify(e.target_columns)),e.enable_token_streaming&&a.append("enable_token_streaming","true"),(await n.A.post("".concat((0,s.hY)(),"/ask/question"),a,{headers:{Authorization:"Bearer ".concat(t)}})).data}catch(e){throw console.error("API error (queryDatabases):",e),e}},[]),i=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN),a={"Content-Type":"application/json"};return t&&(a.Authorization="Bearer ".concat(t)),(await n.A.post("".concat((0,s.hY)(),"/databases/schema"),{db_id:e},{headers:a})).data}catch(e){throw console.error("Error fetching database schema:",e),e}},[]),l=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!e)return console.log("No access token found, skipping database list request"),[];let t=await n.A.post("".concat((0,s.hY)(),"/databases/listdatabases"),{},{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});return t.data.databases||t.data||[]}catch(e){throw console.error("Error listing databases:",e),e}},[]),d=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await n.A.post("".concat((0,s.hY)(),"/databases/connectdatabase"),e,{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}})).data}catch(e){throw console.error("Error connecting new database:",e),e}},[]),u=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN),a={"Content-Type":"application/json"};return t&&(a.Authorization="Bearer ".concat(t)),(await n.A.post("".concat((0,s.hY)(),"/databases/disconnectdatabase"),{db_id:e},{headers:a})).data}catch(e){throw console.error("Error disconnecting database:",e),e}},[]),p=(0,o.useCallback)(async(e,t,a,r,o,c)=>{try{let a=localStorage.getItem(s.d5.ACCESS_TOKEN),i={"Content-Type":"application/json"};return a&&(i.Authorization="Bearer ".concat(a)),(await n.A.post("".concat((0,s.hY)(),"/query/ask"),{query:e,output_format:t,target_databases:r,target_tables:o,target_columns:c},{headers:i})).data}catch(e){throw console.error("Error asking query:",e),e}},[]),h=(0,o.useCallback)(async e=>{try{let t=new URLSearchParams;t.append("username",e.username),t.append("password",e.password);let a=await n.A.post("".concat((0,s.hY)(),"/auth/login"),t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}});return a.data.access_token&&(localStorage.setItem(s.d5.ACCESS_TOKEN,a.data.access_token),console.log("Access token set in localStorage:",a.data.access_token),localStorage.setItem(s.d5.TOKEN_TYPE,a.data.token_type),localStorage.setItem(s.d5.REFRESH_TOKEN,a.data.refresh_token),localStorage.setItem(s.d5.USER_ID,a.data.user_id),localStorage.setItem(s.d5.EXPIRES_AT,a.data.expires_at)),a.data}catch(e){throw console.error("Error during login:",e),e}},[]),g=(0,o.useCallback)(async e=>{try{return(await n.A.post("".concat((0,s.hY)()).concat(s.Sn.AUTH.REGISTER),{email:e.email,password:e.password,full_name:e.full_name},{headers:{"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error registering user:",e),e}},[]),m=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(s.d5.REFRESH_TOKEN);if(!e)throw Error("No refresh token found");let t=await n.A.post("".concat((0,s.hY)(),"/auth/refresh"),{refresh_token:e},{headers:{"Content-Type":"application/json"}});return t.data.access_token&&(localStorage.setItem(s.d5.ACCESS_TOKEN,t.data.access_token),localStorage.setItem(s.d5.TOKEN_TYPE,t.data.token_type),localStorage.setItem(s.d5.REFRESH_TOKEN,t.data.refresh_token),localStorage.setItem(s.d5.USER_ID,t.data.user_id),localStorage.setItem(s.d5.EXPIRES_AT,t.data.expires_at),console.log("Token refreshed successfully")),t.data}catch(e){throw console.error("Error refreshing token:",e),localStorage.removeItem(s.d5.ACCESS_TOKEN),localStorage.removeItem(s.d5.REFRESH_TOKEN),localStorage.removeItem(s.d5.USER_ID),localStorage.removeItem(s.d5.EXPIRES_AT),localStorage.removeItem(s.d5.TOKEN_TYPE),e}},[]),E=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(s.d5.ACCESS_TOKEN);e&&await n.A.post("".concat((0,s.hY)(),"/auth/logout"),{},{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}})}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem(s.d5.ACCESS_TOKEN),localStorage.removeItem(s.d5.REFRESH_TOKEN),localStorage.removeItem(s.d5.USER_ID),localStorage.removeItem(s.d5.EXPIRES_AT),localStorage.removeItem(s.d5.TOKEN_TYPE),console.log("User logged out and tokens cleared")}},[]),S=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!e)throw Error("No authentication token found");return(await n.A.post("".concat((0,s.hY)(),"/auth/me"),{},{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error fetching user profile:",e),e}},[]),y=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!e)return console.log("No access token found, skipping chat list request"),[];let t="".concat((0,s.hY)(),"/chats/listchats");console.log("\uD83D\uDD17 Making request to:",t),console.log("\uD83D\uDD11 Using Bearer token:",e.substring(0,20)+"...");let a=await n.A.post(t,{},{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});return console.log("✅ List chats response received:",a.status),a.data||[]}catch(a){if(console.error("❌ Error listing user chats:",a),n.A.isAxiosError(a)){var e,t;console.error("\uD83D\uDCCD Request URL:",null==(e=a.config)?void 0:e.url),console.error("\uD83D\uDCCB Request headers:",null==(t=a.config)?void 0:t.headers),a.response?(console.error("\uD83D\uDEA8 Response status:",a.response.status),console.error("\uD83D\uDEA8 Response data:",a.response.data),console.error("\uD83D\uDEA8 Response headers:",a.response.headers)):a.request&&console.error("\uD83C\uDF10 Network error - no response received:",a.request)}throw a}},[]),w=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)return console.log("No access token found, skipping chat history request"),[];return(await n.A.post("".concat((0,s.hY)(),"/chats/getchathistory"),{session_id:e},{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}})).data||[]}catch(e){throw console.error("Error getting chat history:",e),n.A.isAxiosError(e)&&e.response&&(console.error("Chat history error response:",e.response.data),console.error("Chat history error status:",e.response.status)),e}},[]),A=(0,o.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)return console.log("No access token found, skipping reports list request"),{reports:[],total_count:0};return(await n.A.post("".concat((0,s.hY)(),"/reports/listreports"),e,{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}})).data}catch(e){throw console.error("Error listing user reports:",e),n.A.isAxiosError(e)&&e.response&&(console.error("List reports error response:",e.response.data),console.error("List reports error status:",e.response.status)),e}},[]),C=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await n.A.post("".concat((0,s.hY)(),"/chats/deletechat"),{session_id:e},{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}})).data}catch(e){throw console.error("Error deleting chat:",e),e}},[]),T=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await n.A.put("".concat((0,s.hY)(),"/auth/profile"),e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error updating user profile:",e),e}},[]),f=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await n.A.post("".concat((0,s.hY)(),"/auth/change-password"),e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error changing password:",e),e}},[]),b=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await n.A.put("".concat((0,s.hY)(),"/auth/email-preferences"),e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error saving email preferences:",e),e}},[]),_=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await n.A.put("".concat((0,s.hY)(),"/auth/privacy-settings"),e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error saving privacy settings:",e),e}},[]),I=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!e)throw Error("No authentication token found");return(await n.A.post("".concat((0,s.hY)(),"/auth/export-data"),{},{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error exporting user data:",e),e}},[]),k=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!e)throw Error("No authentication token found");return(await n.A.delete("".concat((0,s.hY)(),"/auth/account"),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error deleting account:",e),e}},[]),O=(0,o.useCallback)(async()=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");try{return(await n.A.post("".concat((0,s.hY)()).concat(s.Sn.AUTH.COMPLETE_ONBOARDING),{},{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})).data}catch(t){var e;throw null==(e=t.response)||e.status,t}}catch(e){throw console.error("Error completing onboarding:",e),e}},[]),N=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await n.A.post("".concat((0,s.hY)()).concat(s.Sn.CHART.QUERY),e,{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}})).data}catch(e){throw console.error("Error querying chart:",e),e}},[]),D=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!e)throw Error("No authentication token found");return(await n.A.get("".concat((0,s.hY)()).concat(s.Sn.CHART.TYPES),{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}})).data}catch(e){throw console.error("Error getting chart types:",e),e}},[]),R=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await n.A.post("".concat((0,s.hY)()).concat(s.Sn.CHART.VALIDATE),e,{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}})).data}catch(e){throw console.error("Error validating chart query:",e),e}},[]),K=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(s.d5.ACCESS_TOKEN);if(!e)throw Error("No authentication token found");return(await n.A.get("".concat((0,s.hY)()).concat(s.Sn.CHART.HEALTH),{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}})).data}catch(e){throw console.error("Error getting chart health:",e),e}},[]),P=(0,o.useCallback)(async()=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:[]}}catch(e){throw console.error("Error listing dashboards:",e),e}},[]),L=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");let t={id:"dashboard-".concat(Date.now()),name:e.name,description:e.description,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),user_id:"user-1"};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:t}}catch(e){throw console.error("Error creating dashboard:",e),e}},[]),v=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");let t={id:e,name:"Sample Dashboard",description:"A sample dashboard with charts",created_at:new Date(Date.now()-6048e5).toISOString(),updated_at:new Date(Date.now()-864e5).toISOString(),user_id:"user-1",widgets:[]};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:t}}catch(e){throw console.error("Error getting dashboard:",e),e}},[]),B=(0,o.useCallback)(async(e,t)=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");let a={id:e,name:t.name||"Updated Dashboard",description:t.description||"Updated description",created_at:new Date(Date.now()-6048e5).toISOString(),updated_at:new Date().toISOString(),user_id:"user-1"};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:a}}catch(e){throw console.error("Error updating dashboard:",e),e}},[]),H=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,message:"Dashboard deleted successfully"}}catch(e){throw console.error("Error deleting dashboard:",e),e}},[]),Y=(0,o.useCallback)(async()=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");let e=[{id:"analysis-1",name:"Employee Performance Analysis",description:"Analyzing employee performance metrics, salary distributions, and department insights",status:"completed",createdAt:new Date(Date.now()-1728e5).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),dataSource:"HR Database",stepCount:3,userId:"user-1"},{id:"analysis-2",name:"Customer Behavior Study",description:"Understanding customer purchasing patterns and churn prediction",status:"running",createdAt:new Date(Date.now()-432e6).toISOString(),updatedAt:new Date(Date.now()-72e5).toISOString(),dataSource:"Sales Database",stepCount:4,progress:67,userId:"user-1"},{id:"analysis-3",name:"Market Trend Forecasting",description:"Forecasting market trends and demand patterns for Q4",status:"draft",createdAt:new Date(Date.now()-864e5).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),dataSource:"Market Data API",stepCount:0,userId:"user-1"}];return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:{projects:e,totalCount:e.length}}}catch(e){throw console.error("Error listing analysis projects:",e),e}},[]),j=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");let t={id:"analysis-".concat(Date.now()),name:e.name,description:e.description||"",status:"draft",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),dataSource:e.dataSource,stepCount:0,userId:"user-1"};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:t}}catch(e){throw console.error("Error creating analysis project:",e),e}},[]),U=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");let t={id:e,name:"Employee Performance Analysis",description:"Analyzing employee performance metrics, salary distributions, and department insights",status:"completed",createdAt:new Date(Date.now()-1728e5).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),dataSource:"HR Database",stepCount:3,userId:"user-1"};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:t}}catch(e){throw console.error("Error getting analysis project:",e),e}},[]),z=(0,o.useCallback)(async(e,t)=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");let a={id:e,name:t.name||"Updated Analysis Project",description:t.description||"Updated description",status:t.status||"draft",createdAt:new Date(Date.now()-1728e5).toISOString(),updatedAt:new Date().toISOString(),dataSource:"HR Database",stepCount:3,userId:"user-1"};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:a}}catch(e){throw console.error("Error updating analysis project:",e),e}},[]),x=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,message:"Analysis project deleted successfully"}}catch(e){throw console.error("Error deleting analysis project:",e),e}},[]),M=(0,o.useCallback)(async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,300*Math.random()+100)),{success:!0,data:{rows:[],columns:[],totalRows:0,schema:{}}}}catch(e){throw console.error("Error getting project data:",e),e}},[]),q=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");let t=[{id:"step-1",projectId:e,title:"Data Loading",status:"completed",type:"data_loading",order:1,createdAt:new Date(Date.now()-72e5).toISOString(),updatedAt:new Date(Date.now()-36e5).toISOString(),outputs:{summary:"Successfully loaded 5 employee records from the connected database.",code:'import pandas as pd\nimport numpy as np\n\n# Load employee data from connected database\ndf = pd.read_sql_query("""\n    SELECT id, name, age, department, salary, performance_rating as performance\n    FROM employees \n    WHERE active = true\n""", connection)\n\nprint(f"Loaded {len(df)} employee records")\ndf.head()'}},{id:"step-2",projectId:e,title:"Data Analysis",status:"completed",type:"analysis",order:2,createdAt:new Date(Date.now()-36e5).toISOString(),updatedAt:new Date(Date.now()-18e5).toISOString(),outputs:{summary:"Analyzed salary distributions and identified 2 high-performing employees.",code:"# Analyze salary distribution by department\ndept_stats = df.groupby('department').agg({\n    'salary': ['mean', 'median', 'std'],\n    'performance': 'mean',\n    'age': 'mean'\n}).round(2)\n\nprint(\"Department Statistics:\")\nprint(dept_stats)\n\n# Identify high performers\nhigh_performers = df[df['performance'] >= 4.5]\nprint(f\"\\nHigh performers ({len(high_performers)} employees):\")\nprint(high_performers[['name', 'department', 'performance']])"}},{id:"step-3",projectId:e,title:"Visualization",status:"completed",type:"visualization",order:3,createdAt:new Date(Date.now()-18e5).toISOString(),updatedAt:new Date(Date.now()-6e5).toISOString(),outputs:{summary:"Created scatter plot showing relationship between salary and performance across departments.",code:"import matplotlib.pyplot as plt\nimport seaborn as sns\n\n# Create scatter plot of salary vs performance\nplt.figure(figsize=(10, 6))\nsns.scatterplot(data=df, x='performance', y='salary', hue='department', s=100)\nplt.title('Salary vs Performance by Department')\nplt.xlabel('Performance Rating')\nplt.ylabel('Salary ($)')\nplt.legend(title='Department')\nplt.tight_layout()\nplt.show()",visualization:{type:"chart",config:{title:"Salary vs Performance by Department",xAxis:"performance",yAxis:"salary",groupBy:"department"},data:[{x:4.2,y:85e3,group:"Engineering",name:"John Doe"},{x:4.8,y:65e3,group:"Marketing",name:"Jane Smith"},{x:4.1,y:92e3,group:"Engineering",name:"Mike Johnson"},{x:4.6,y:7e4,group:"Design",name:"Sarah Wilson"},{x:3.9,y:98e3,group:"Engineering",name:"David Brown"}]}}}];return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:t}}catch(e){throw console.error("Error getting project steps:",e),e}},[]),G=(0,o.useCallback)(async(e,t)=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,2e3*Math.random()+1e3)),{success:!0,message:"Step execution completed successfully"}}catch(e){throw console.error("Error executing step:",e),e}},[]),F=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");let{file:t,projectId:a,onProgress:r}=e,o=new FormData;if(o.append("file",t),o.append("projectId",a),r)for(let e=0;e<=100;e+=10)await new Promise(e=>setTimeout(e,100)),r({loaded:t.size*e/100,total:t.size,percentage:e});let n={rows:[],columns:[],totalRows:0,schema:{}};return(t.name.toLowerCase().includes("csv")||t.name.toLowerCase().includes("excel"))&&(n={rows:[{id:1,name:"Alice Johnson",age:28,salary:75e3,department:"Engineering"},{id:2,name:"Bob Smith",age:34,salary:82e3,department:"Marketing"},{id:3,name:"Carol White",age:29,salary:78e3,department:"Design"},{id:4,name:"David Brown",age:31,salary:85e3,department:"Engineering"},{id:5,name:"Emma Davis",age:26,salary:72e3,department:"Sales"}],columns:[{name:"id",type:"number",nullable:!1},{name:"name",type:"string",nullable:!1},{name:"age",type:"number",nullable:!1},{name:"salary",type:"number",nullable:!1},{name:"department",type:"string",nullable:!1}],totalRows:5,schema:{id:"INTEGER",name:"VARCHAR(255)",age:"INTEGER",salary:"INTEGER",department:"VARCHAR(100)"}}),await new Promise(e=>setTimeout(e,500)),{success:!0,data:{fileId:"file-".concat(Date.now()),filename:t.name,rowCount:n.totalRows,columnCount:n.columns.length,previewData:n}}}catch(e){throw console.error("Error uploading file:",e),e}},[]),J=(0,o.useCallback)(async(e,t)=>{try{if(!localStorage.getItem(s.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,300)),{success:!0,message:"File deleted successfully"}}catch(e){throw console.error("Error deleting file:",e),e}},[]);return(0,r.jsx)(c.Provider,{value:{queryDatabases:a,getDatabaseSchema:i,listDatabases:l,connectNewDatabase:d,disconnectExistingDatabase:u,askQuery:p,loginUser:h,registerUser:g,refreshToken:m,logoutUser:E,getUserProfile:S,updateUserProfile:T,changePassword:f,saveEmailPreferences:b,savePrivacySettings:_,exportUserData:I,deleteAccount:k,listUserChats:y,getChatHistory:w,listUserReports:A,deleteChat:C,completeOnboarding:O,queryChart:N,getChartTypes:D,validateChartQuery:R,getChartHealth:K,listDashboards:P,createDashboard:L,getDashboard:v,updateDashboard:B,deleteDashboard:H,listAnalysisProjects:Y,createAnalysisProject:j,getAnalysisProject:U,updateAnalysisProject:z,deleteAnalysisProject:x,getProjectData:M,getProjectSteps:q,executeStep:G,uploadProjectFile:F,deleteProjectFile:J},children:t})},l=()=>{let e=(0,o.useContext)(c);if(void 0===e)throw Error("useApi must be used within an ApiProvider");return e}}}]);