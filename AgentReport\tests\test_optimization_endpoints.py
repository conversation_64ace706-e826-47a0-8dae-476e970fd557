import pytest
from datetime import datetime

from fastapi.testclient import TestClient

from app.main import app
from app.utils.security import get_current_user
from app.models.user import User


# ---------------------------------------------------------------------------
# Dependency override helpers
# ---------------------------------------------------------------------------

def _create_stub_user() -> User:
    """Return a stub `User` object for dependency override."""
    return User(
        id="test_user",
        email="<EMAIL>",
        hashed_password="",
        full_name="Test User",
        is_active=True,
        is_email_verified=True,
        role="admin",
        auth_provider="local",
        auth_provider_id=None,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        last_login=None,
        profile_picture=None,
        settings={},
    )


@pytest.fixture(autouse=True)
def override_auth_dependency():
    """Automatically override `get_current_user` with a stub for all tests."""
    app.dependency_overrides[get_current_user] = _create_stub_user
    yield
    app.dependency_overrides.pop(get_current_user, None)


@pytest.fixture(scope="module")
def client():
    """Return a reusable TestClient instance for the FastAPI app."""
    with TestClient(app) as c:
        yield c


# ---------------------------------------------------------------------------
# Endpoint tests
# ---------------------------------------------------------------------------

def test_performance_report_json(client):
    """GET /api/optimization/performance-report should return JSON payload by default."""
    response = client.get("/api/optimization/performance-report")
    assert response.status_code == 200

    payload = response.json()
    assert payload["status"] == "success"
    assert "data" in payload


def test_performance_report_markdown(client):
    """GET /api/optimization/performance-report?format=markdown should return Markdown text."""
    response = client.get("/api/optimization/performance-report", params={"format": "markdown"})
    assert response.status_code == 200
    # Content type is text/markdown via PlainTextResponse
    assert response.headers["content-type"].startswith("text/markdown")
    assert "Database Query Pipeline Optimization Report" in response.text


def test_bottlenecks_endpoint(client):
    """GET /api/optimization/bottlenecks should return structured bottleneck data."""
    response = client.get("/api/optimization/bottlenecks")
    assert response.status_code == 200

    payload = response.json()
    assert payload["status"] == "success"
    assert "data" in payload
    # Ensure expected keys exist in the nested data structure
    bottleneck_data = payload["data"]
    assert "bottlenecks" in bottleneck_data
    assert "pipeline_stages" in bottleneck_data


def test_cache_stats_endpoint(client):
    """GET /api/optimization/cache-stats should return cache statistics."""
    response = client.get("/api/optimization/cache-stats")
    assert response.status_code == 200

    payload = response.json()
    assert payload["status"] == "success"
    assert "data" in payload
    cache_data = payload["data"]
    # Validate presence of top-level cache summary keys
    assert "cache_layers" in cache_data
    assert "overall_performance" in cache_data 