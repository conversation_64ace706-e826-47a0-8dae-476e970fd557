(()=>{var e={};e.id=316,e.ids=[316],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9194:(e,t,s)=>{Promise.resolve().then(s.bind(s,73202))},9868:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});let o=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\NXTHUMANS\\\\internal-solutions-nxtwebsite\\\\AgentReport 2\\\\AgentReportUI\\\\src\\\\app\\\\debug-onboarding\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\debug-onboarding\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44346:(e,t,s)=>{"use strict";function o(e){}function i(){return null}function r(){}function n(e,t){let s=e.slice(0,t).filter(e=>e.isCompleted).map(e=>e.id);return{currentStep:e[t]?.id||"welcome",completedSteps:s,lastUpdated:new Date().toISOString(),canResume:s.length>0,totalSteps:e.length}}s.d(t,{Hg:()=>i,Wr:()=>o,YL:()=>n,ZS:()=>p,qy:()=>r,uE:()=>l});function p(e,t){var s;if(!t||!((s=t)&&"object"==typeof s&&"string"==typeof s.currentStep&&Array.isArray(s.completedSteps)&&"string"==typeof s.lastUpdated&&"boolean"==typeof s.canResume&&"number"==typeof s.totalSteps))return{steps:e,currentStepIndex:0};let o=e.map(e=>({...e,isCompleted:t.completedSteps.includes(e.id)})),i=function(e,t){let s=e.findIndex(e=>e.id===t);return s>=0?s:0}(o,t.currentStep);return{steps:o,currentStepIndex:i}}function l(e){let t=[],s=[],o=["completion","complete","finish","final"],i=e.filter(e=>!e.isOptional&&!o.includes(e.id.toLowerCase()));for(let t of(console.log("\uD83D\uDD0D Validating completion requirements:"),console.log("All steps:",e.map(e=>({id:e.id,title:e.title,completed:e.isCompleted,optional:e.isOptional}))),console.log("Required steps:",i.map(e=>({id:e.id,title:e.title,completed:e.isCompleted}))),i))t.isCompleted||s.push(t.title);console.log("Missing steps:",s),s.length>0&&t.push(`Please complete the following required steps: ${s.join(", ")}`);let r={canComplete:0===t.length,missingSteps:s,errors:t};return console.log("Validation result:",r),r}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68840:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>a,routeModule:()=>u,tree:()=>d});var o=s(65239),i=s(48088),r=s(88170),n=s.n(r),p=s(30893),l={};for(let e in p)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>p[e]);s.d(t,l);let d={children:["",{children:["debug-onboarding",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9868)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\debug-onboarding\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,a=["C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\debug-onboarding\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/debug-onboarding/page",pathname:"/debug-onboarding",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var o=s(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73202:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var o=s(60687),i=s(43210),r=s(44346);let n=()=>(0,o.jsx)("div",{children:"Mock Component"});function p(){let[e,t]=(0,i.useState)([]);return(0,o.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-6",children:(0,o.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:"Debug Onboarding Validation"}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Test the onboarding validation logic to debug completion issues"}),(0,o.jsx)("button",{onClick:()=>{let e=[];e.push("\uD83E\uDDEA Testing Onboarding Validation Logic...\n");let s=(0,r.uE)([{id:"welcome",title:"Welcome",description:"Welcome step",component:n,isCompleted:!1},{id:"profile",title:"Profile Setup",description:"Profile step",component:n,isCompleted:!1},{id:"preferences",title:"Preferences",description:"Preferences step",component:n,isCompleted:!1,isOptional:!0},{id:"completion",title:"Complete",description:"Completion step",component:n,isCompleted:!1}]);e.push("Test 1 - All incomplete:"),e.push(`  Can complete: ${s.canComplete} (expected: false)`),e.push(`  Missing steps: [${s.missingSteps.join(", ")}]`),e.push(`  Errors: ${s.errors.join("; ")}
`);let o=(0,r.uE)([{id:"welcome",title:"Welcome",description:"Welcome step",component:n,isCompleted:!0},{id:"profile",title:"Profile Setup",description:"Profile step",component:n,isCompleted:!0},{id:"preferences",title:"Preferences",description:"Preferences step",component:n,isCompleted:!1,isOptional:!0},{id:"completion",title:"Complete",description:"Completion step",component:n,isCompleted:!1}]);e.push("Test 2 - Required complete, completion incomplete:"),e.push(`  Can complete: ${o.canComplete} (expected: true)`),e.push(`  Missing steps: [${o.missingSteps.join(", ")}]`),e.push(`  Errors: ${o.errors.join("; ")}
`);let i=(0,r.uE)([{id:"welcome",title:"Welcome",description:"Welcome step",component:n,isCompleted:!0},{id:"profile",title:"Profile Setup",description:"Profile step",component:n,isCompleted:!0},{id:"preferences",title:"Preferences",description:"Preferences step",component:n,isCompleted:!0,isOptional:!0},{id:"completion",title:"Complete",description:"Completion step",component:n,isCompleted:!0}]);e.push("Test 3 - All complete:"),e.push(`  Can complete: ${i.canComplete} (expected: true)`),e.push(`  Missing steps: [${i.missingSteps.join(", ")}]`),e.push(`  Errors: ${i.errors.join("; ")}
`);let p=(0,r.uE)([{id:"welcome",title:"Welcome",description:"Welcome step",component:n,isCompleted:!0},{id:"profile",title:"Profile Setup",description:"Profile step",component:n,isCompleted:!1},{id:"preferences",title:"Preferences",description:"Preferences step",component:n,isCompleted:!1,isOptional:!0},{id:"completion",title:"Complete",description:"Completion step",component:n,isCompleted:!1}]);e.push("Test 4 - Missing required step:"),e.push(`  Can complete: ${p.canComplete} (expected: false)`),e.push(`  Missing steps: [${p.missingSteps.join(", ")}]`),e.push(`  Errors: ${p.errors.join("; ")}
`);let l=(0,r.uE)([{id:"welcome",title:"Welcome",description:"Welcome step",component:n,isCompleted:!0},{id:"profile",title:"Profile Setup",description:"Profile step",component:n,isCompleted:!0},{id:"finish",title:"Finish",description:"Finish step",component:n,isCompleted:!1}]);e.push("Test 5 - Different completion ID (finish):"),e.push(`  Can complete: ${l.canComplete} (expected: true)`),e.push(`  Missing steps: [${l.missingSteps.join(", ")}]`),e.push(`  Errors: ${l.errors.join("; ")}
`),e.push("\uD83C\uDF89 Validation tests completed!"),t(e)},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg mb-6",children:"Run Validation Tests"}),e.length>0&&(0,o.jsx)("div",{className:"bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto",children:e.map((e,t)=>(0,o.jsx)("div",{className:"whitespace-pre-wrap",children:e},t))})]})})})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91042:(e,t,s)=>{Promise.resolve().then(s.bind(s,9868))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),o=t.X(0,[447,255,658,695],()=>s(68840));module.exports=o})();