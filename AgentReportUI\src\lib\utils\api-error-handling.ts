// Comprehensive API error handling utilities

import axios from 'axios';

export interface ApiError {
  code: string;
  message: string;
  userMessage: string;
  retryable: boolean;
  timestamp: string;
  statusCode?: number;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffFactor: 2,
};

/**
 * Categorize API errors for better handling
 */
export function categorizeApiError(error: any): ApiError {
  const timestamp = new Date().toISOString();
  
  // Network errors
  if (error.code === 'NETWORK_ERROR' || !error.response) {
    return {
      code: 'NETWORK_ERROR',
      message: error.message || 'Network error occurred',
      userMessage: 'Network connection issue. Please check your internet connection and try again.',
      retryable: true,
      timestamp,
    };
  }
  
  const status = error.response?.status;
  const data = error.response?.data;
  
  // Authentication errors
  if (status === 401) {
    return {
      code: 'AUTHENTICATION_ERROR',
      message: data?.detail || 'Authentication failed',
      userMessage: 'Your session has expired. Please log in again.',
      retryable: false,
      timestamp,
      statusCode: status,
    };
  }
  
  // Authorization errors
  if (status === 403) {
    return {
      code: 'AUTHORIZATION_ERROR',
      message: data?.detail || 'Access denied',
      userMessage: 'You do not have permission to perform this action.',
      retryable: false,
      timestamp,
      statusCode: status,
    };
  }
  
  // Not found errors
  if (status === 404) {
    return {
      code: 'NOT_FOUND_ERROR',
      message: data?.detail || 'Resource not found',
      userMessage: 'The requested resource was not found.',
      retryable: false,
      timestamp,
      statusCode: status,
    };
  }
  
  // Validation errors
  if (status === 422) {
    let validationMessage = 'Validation failed';
    if (Array.isArray(data?.detail)) {
      const validationErrors = data.detail.map((error: any) => {
        const field = error.loc ? error.loc.join('.') : 'unknown field';
        return `${field}: ${error.msg}`;
      }).join(', ');
      validationMessage = `Validation error: ${validationErrors}`;
    } else if (data?.detail) {
      validationMessage = data.detail;
    }
    
    return {
      code: 'VALIDATION_ERROR',
      message: validationMessage,
      userMessage: validationMessage,
      retryable: false,
      timestamp,
      statusCode: status,
    };
  }
  
  // Rate limiting
  if (status === 429) {
    return {
      code: 'RATE_LIMIT_ERROR',
      message: data?.detail || 'Rate limit exceeded',
      userMessage: 'Too many requests. Please wait a moment and try again.',
      retryable: true,
      timestamp,
      statusCode: status,
    };
  }
  
  // Server errors (5xx)
  if (status >= 500) {
    return {
      code: 'SERVER_ERROR',
      message: data?.detail || 'Server error occurred',
      userMessage: 'Server is temporarily unavailable. Please try again in a moment.',
      retryable: true,
      timestamp,
      statusCode: status,
    };
  }
  
  // Client errors (4xx)
  if (status >= 400) {
    return {
      code: 'CLIENT_ERROR',
      message: data?.detail || data?.message || 'Client error occurred',
      userMessage: data?.detail || data?.message || 'There was an issue with your request. Please try again.',
      retryable: false,
      timestamp,
      statusCode: status,
    };
  }
  
  // Generic error
  return {
    code: 'UNKNOWN_ERROR',
    message: error.message || 'An unknown error occurred',
    userMessage: 'An unexpected error occurred. Please try again.',
    retryable: true,
    timestamp,
  };
}

/**
 * Retry API calls with exponential backoff
 */
export async function retryWithBackoff<T>(
  apiCall: () => Promise<T>,
  config: Partial<RetryConfig> = {},
  operationName: string = 'API call'
): Promise<T> {
  const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  let lastError: any;
  
  for (let attempt = 0; attempt <= finalConfig.maxRetries; attempt++) {
    try {
      const result = await apiCall();
      if (attempt > 0) {
        console.log(`${operationName} succeeded on attempt ${attempt + 1}`);
      }
      return result;
    } catch (error) {
      lastError = error;
      const apiError = categorizeApiError(error);
      
      // Don't retry non-retryable errors
      if (!apiError.retryable) {
        console.log(`${operationName} failed with non-retryable error:`, apiError.code);
        throw error;
      }
      
      // Don't retry on the last attempt
      if (attempt === finalConfig.maxRetries) {
        break;
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(
        finalConfig.baseDelay * Math.pow(finalConfig.backoffFactor, attempt),
        finalConfig.maxDelay
      );
      
      console.log(`${operationName} failed on attempt ${attempt + 1}, retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  console.error(`${operationName} failed after ${finalConfig.maxRetries + 1} attempts`);
  throw lastError;
}

/**
 * Safe API call wrapper with error handling and retry logic
 */
export async function safeApiCall<T>(
  apiCall: () => Promise<T>,
  operationName: string,
  retryConfig?: Partial<RetryConfig>
): Promise<{ data?: T; error?: ApiError }> {
  try {
    const data = await retryWithBackoff(apiCall, retryConfig, operationName);
    return { data };
  } catch (error) {
    const apiError = categorizeApiError(error);
    console.error(`Safe API call failed for ${operationName}:`, apiError);
    return { error: apiError };
  }
}

/**
 * Log API errors for debugging (production-safe)
 */
export function logApiError(error: ApiError, context: string): void {
  const logData = {
    context,
    code: error.code,
    message: error.message,
    statusCode: error.statusCode,
    timestamp: error.timestamp,
    retryable: error.retryable,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
    url: typeof window !== 'undefined' ? window.location.href : 'unknown',
  };
  
  // In production, you might want to send this to an error tracking service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to error tracking service
    // errorTrackingService.log(logData);
    console.error('API Error:', logData);
  } else {
    console.error('API Error:', logData);
  }
}

/**
 * Check if an error is a specific type
 */
export function isApiErrorType(error: ApiError, type: string): boolean {
  return error.code === type;
}

/**
 * Check if an error is retryable
 */
export function isRetryableError(error: ApiError): boolean {
  return error.retryable;
}

/**
 * Get user-friendly error message
 */
export function getUserErrorMessage(error: ApiError): string {
  return error.userMessage;
}
