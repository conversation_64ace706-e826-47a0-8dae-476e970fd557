"""
LLM Configuration and Model Management
=====================================
Central control center for managing different LLMs for different agent purposes.
Easy to configure and switch models for different use cases.
"""

from enum import Enum
from typing import Dict, Any, Optional
from dataclasses import dataclass


class ModelPurpose(Enum):
    """Define different purposes/use cases for LLM models."""
    GENERAL = "general"                    # General purpose tasks
    ANALYSIS = "analysis"                  # Data analysis and planning
    CODE_GENERATION = "code_generation"    # Python code generation
    CODE_EXECUTION = "code_execution"      # Code execution and debugging
    DASHBOARD = "dashboard"                # Dashboard and visualization
    CONVERSATION = "conversation"          # Chat and conversation
    ORCHESTRATION = "orchestration"        # Agent orchestration and planning
    FILE_INGESTION = "file_ingestion"      # File processing and metadata


@dataclass
class ModelConfig:
    """Configuration for a specific model."""
    model_id: str
    display_name: str
    max_tokens: int
    temperature: float
    top_p: float
    description: str
    cost_tier: str  # "low", "medium", "high"
    speed_tier: str  # "fast", "medium", "slow"
    quality_tier: str  # "basic", "good", "excellent"


class LLMModelRegistry:
    """Registry of available models and their configurations."""
    
    # Define available models
    MODELS = {
        # Anthropic Claude Models (Updated with correct Bedrock model IDs)
        "claude-3-5-haiku": ModelConfig(
            model_id="us.anthropic.claude-3-5-haiku-20241022-v1:0",
            display_name="Claude 3.5 Haiku",
            max_tokens=200000,
            temperature=0.7,
            top_p=0.9,
            description="Fast, cost-efficient model for simple tasks",
            cost_tier="low",
            speed_tier="fast",
            quality_tier="good"
        ),
        "claude-3-5-sonnet": ModelConfig(
            model_id="anthropic.claude-3-5-sonnet-20241022-v2:0",
            display_name="Claude 3.5 Sonnet",
            max_tokens=200000,
            temperature=0.7,
            top_p=0.9,
            description="Balanced model for most tasks",
            cost_tier="medium",
            speed_tier="medium",
            quality_tier="excellent"
        ),
        "claude-3-opus": ModelConfig(
            model_id="anthropic.claude-3-opus-20240229-v1:0",
            display_name="Claude 3 Opus",
            max_tokens=200000,
            temperature=0.7,
            top_p=0.9,
            description="Most capable model for complex reasoning",
            cost_tier="high",
            speed_tier="slow",
            quality_tier="excellent"
        ),
        "claude-3-7-sonnet": ModelConfig(
            model_id="us.anthropic.claude-3-7-sonnet-20250219-v1:0",
            display_name="Claude 3 Sonnet",
            max_tokens=200000,
            temperature=0.7,
            top_p=0.9,
            description="Balanced model for most tasks",
            cost_tier="medium",
            speed_tier="medium",
            quality_tier="excellent"
        ),
        "claude-4-sonnet": ModelConfig(
            model_id="us.anthropic.claude-sonnet-4-20250514-v1:0",
            display_name="Claude 4 Sonnet",
            max_tokens=1500,  # Optimized for Claude 4 rate limits (1500 + 1500*5 = 9000 tokens)
            temperature=0.7,
            top_p=0.9,
            description="Latest Claude 4 model with cross-region inference profile (rate-limit optimized)",
            cost_tier="high",
            speed_tier="medium",
            quality_tier="excellent"
        ),
        
        # Add support for other providers in the future
        # "gpt-4-turbo": ModelConfig(...),
        # "llama-3-70b": ModelConfig(...),
    }
    
    # Model assignments for different purposes  
    PURPOSE_ASSIGNMENTS = {
        ModelPurpose.GENERAL: "claude-3-5-haiku",           
        ModelPurpose.ANALYSIS: "claude-3-7-sonnet",         
        ModelPurpose.CODE_GENERATION: "claude-3-7-sonnet",  # Fixed model ID, should work now
        ModelPurpose.CODE_EXECUTION: "claude-3-7-sonnet",    
        ModelPurpose.DASHBOARD: "claude-3-7-sonnet",
        ModelPurpose.CONVERSATION: "claude-3-5-haiku", 
        ModelPurpose.ORCHESTRATION: "claude-3-5-haiku",    
        ModelPurpose.FILE_INGESTION: "claude-3-5-haiku",
    }
    
    @classmethod
    def get_model_for_purpose(cls, purpose: ModelPurpose) -> ModelConfig:
        """Get the assigned model configuration for a specific purpose."""
        model_key = cls.PURPOSE_ASSIGNMENTS.get(purpose, "claude-3-5-haiku")
        return cls.MODELS[model_key]
    
    @classmethod
    def get_model_by_key(cls, model_key: str) -> ModelConfig:
        """Get model configuration by its key."""
        if model_key not in cls.MODELS:
            raise ValueError(f"Unknown model key: {model_key}. Available: {list(cls.MODELS.keys())}")
        return cls.MODELS[model_key]
    
    @classmethod
    def list_available_models(cls) -> Dict[str, ModelConfig]:
        """List all available models."""
        return cls.MODELS.copy()
    
    @classmethod
    def update_purpose_assignment(cls, purpose: ModelPurpose, model_key: str) -> None:
        """Update which model is assigned to a specific purpose."""
        if model_key not in cls.MODELS:
            raise ValueError(f"Unknown model key: {model_key}")
        cls.PURPOSE_ASSIGNMENTS[purpose] = model_key
    
    @classmethod
    def get_purpose_assignments(cls) -> Dict[ModelPurpose, str]:
        """Get current purpose-to-model assignments."""
        return cls.PURPOSE_ASSIGNMENTS.copy()


# Convenience functions for easy access
def get_model_for_purpose(purpose: ModelPurpose) -> ModelConfig:
    """Get model config for a specific purpose."""
    return LLMModelRegistry.get_model_for_purpose(purpose)

def get_model_by_key(model_key: str) -> ModelConfig:
    """Get model config by key."""
    return LLMModelRegistry.get_model_by_key(model_key)

def switch_model_for_purpose(purpose: ModelPurpose, model_key: str) -> None:
    """Switch which model is used for a specific purpose."""
    LLMModelRegistry.update_purpose_assignment(purpose, model_key)


# Default fallback model (for backward compatibility)
DEFAULT_MODEL_PURPOSE = ModelPurpose.GENERAL 