{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/react-grid-layout/build/fastRGLPropsEqual.js"], "sourcesContent": ["// this file was prevaled\nmodule.exports = function fastRGLPropsEqual(a, b, isEqualImpl) {\n  if (a === b) return true;\n  return a.className === b.className && isEqualImpl(a.style, b.style) && a.width === b.width && a.autoSize === b.autoSize && a.cols === b.cols && a.draggableCancel === b.draggableCancel && a.draggableHandle === b.draggableHandle && isEqualImpl(a.verticalCompact, b.verticalCompact) && isEqualImpl(a.compactType, b.compactType) && isEqualImpl(a.layout, b.layout) && isEqualImpl(a.margin, b.margin) && isEqualImpl(a.containerPadding, b.containerPadding) && a.rowHeight === b.rowHeight && a.maxRows === b.maxRows && a.isBounded === b.isBounded && a.isDraggable === b.isDraggable && a.isResizable === b.isResizable && a.allowOverlap === b.allowOverlap && a.preventCollision === b.preventCollision && a.useCSSTransforms === b.useCSSTransforms && a.transformScale === b.transformScale && a.isDroppable === b.isDroppable && isEqualImpl(a.resizeHandles, b.resizeHandles) && isEqualImpl(a.resizeHandle, b.resizeHandle) && a.onLayoutChange === b.onLayoutChange && a.onDragStart === b.onDragStart && a.onDrag === b.onDrag && a.onDragStop === b.onDragStop && a.onResizeStart === b.onResizeStart && a.onResize === b.onResize && a.onResizeStop === b.onResizeStop && a.onDrop === b.onDrop && isEqualImpl(a.droppingItem, b.droppingItem) && isEqualImpl(a.innerRef, b.innerRef);\n};"], "names": [], "mappings": "AAAA,yBAAyB;AACzB,OAAO,OAAO,GAAG,SAAS,kBAAkB,CAAC,EAAE,CAAC,EAAE,WAAW;IAC3D,IAAI,MAAM,GAAG,OAAO;IACpB,OAAO,EAAE,SAAS,KAAK,EAAE,SAAS,IAAI,YAAY,EAAE,KAAK,EAAE,EAAE,KAAK,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,IAAI,EAAE,eAAe,KAAK,EAAE,eAAe,IAAI,EAAE,eAAe,KAAK,EAAE,eAAe,IAAI,YAAY,EAAE,eAAe,EAAE,EAAE,eAAe,KAAK,YAAY,EAAE,WAAW,EAAE,EAAE,WAAW,KAAK,YAAY,EAAE,MAAM,EAAE,EAAE,MAAM,KAAK,YAAY,EAAE,MAAM,EAAE,EAAE,MAAM,KAAK,YAAY,EAAE,gBAAgB,EAAE,EAAE,gBAAgB,KAAK,EAAE,SAAS,KAAK,EAAE,SAAS,IAAI,EAAE,OAAO,KAAK,EAAE,OAAO,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,IAAI,EAAE,WAAW,KAAK,EAAE,WAAW,IAAI,EAAE,WAAW,KAAK,EAAE,WAAW,IAAI,EAAE,YAAY,KAAK,EAAE,YAAY,IAAI,EAAE,gBAAgB,KAAK,EAAE,gBAAgB,IAAI,EAAE,gBAAgB,KAAK,EAAE,gBAAgB,IAAI,EAAE,cAAc,KAAK,EAAE,cAAc,IAAI,EAAE,WAAW,KAAK,EAAE,WAAW,IAAI,YAAY,EAAE,aAAa,EAAE,EAAE,aAAa,KAAK,YAAY,EAAE,YAAY,EAAE,EAAE,YAAY,KAAK,EAAE,cAAc,KAAK,EAAE,cAAc,IAAI,EAAE,WAAW,KAAK,EAAE,WAAW,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,IAAI,EAAE,aAAa,KAAK,EAAE,aAAa,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,IAAI,EAAE,YAAY,KAAK,EAAE,YAAY,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,IAAI,YAAY,EAAE,YAAY,EAAE,EAAE,YAAY,KAAK,YAAY,EAAE,QAAQ,EAAE,EAAE,QAAQ;AAC1uC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/react-grid-layout/build/utils.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.bottom = bottom;\nexports.childrenEqual = childrenEqual;\nexports.cloneLayout = cloneLayout;\nexports.cloneLayoutItem = cloneLayoutItem;\nexports.collides = collides;\nexports.compact = compact;\nexports.compactItem = compactItem;\nexports.compactType = compactType;\nexports.correctBounds = correctBounds;\nexports.fastPositionEqual = fastPositionEqual;\nexports.fastRGLPropsEqual = void 0;\nexports.getAllCollisions = getAllCollisions;\nexports.getFirstCollision = getFirstCollision;\nexports.getLayoutItem = getLayoutItem;\nexports.getStatics = getStatics;\nexports.modifyLayout = modifyLayout;\nexports.moveElement = moveElement;\nexports.moveElementAwayFromCollision = moveElementAwayFromCollision;\nexports.noop = void 0;\nexports.perc = perc;\nexports.resizeItemInDirection = resizeItemInDirection;\nexports.setTopLeft = setTopLeft;\nexports.setTransform = setTransform;\nexports.sortLayoutItems = sortLayoutItems;\nexports.sortLayoutItemsByColRow = sortLayoutItemsByColRow;\nexports.sortLayoutItemsByRowCol = sortLayoutItemsByRowCol;\nexports.synchronizeLayoutWithChildren = synchronizeLayoutWithChildren;\nexports.validateLayout = validateLayout;\nexports.withLayoutItem = withLayoutItem;\nvar _fastEquals = require(\"fast-equals\");\nvar _react = _interopRequireDefault(require(\"react\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n/*:: import type {\n  ChildrenArray as ReactChildrenArray,\n  Element as ReactElement\n} from \"react\";*/\n/*:: export type ResizeHandleAxis =\n  | \"s\"\n  | \"w\"\n  | \"e\"\n  | \"n\"\n  | \"sw\"\n  | \"nw\"\n  | \"se\"\n  | \"ne\";*/\n/*:: export type LayoutItem = {\n  w: number,\n  h: number,\n  x: number,\n  y: number,\n  i: string,\n  minW?: number,\n  minH?: number,\n  maxW?: number,\n  maxH?: number,\n  moved?: boolean,\n  static?: boolean,\n  isDraggable?: ?boolean,\n  isResizable?: ?boolean,\n  resizeHandles?: Array<ResizeHandleAxis>,\n  isBounded?: ?boolean\n};*/\n/*:: export type Layout = $ReadOnlyArray<LayoutItem>;*/\n/*:: export type Position = {\n  left: number,\n  top: number,\n  width: number,\n  height: number\n};*/\n/*:: export type ReactDraggableCallbackData = {\n  node: HTMLElement,\n  x?: number,\n  y?: number,\n  deltaX: number,\n  deltaY: number,\n  lastX?: number,\n  lastY?: number\n};*/\n/*:: export type PartialPosition = { left: number, top: number };*/\n/*:: export type DroppingPosition = { left: number, top: number, e: Event };*/\n/*:: export type Size = { width: number, height: number };*/\n/*:: export type GridDragEvent = {\n  e: Event,\n  node: HTMLElement,\n  newPosition: PartialPosition\n};*/\n/*:: export type GridResizeEvent = {\n  e: Event,\n  node: HTMLElement,\n  size: Size,\n  handle: string\n};*/\n/*:: export type DragOverEvent = MouseEvent & {\n  nativeEvent: {\n    layerX: number,\n    layerY: number,\n    ...Event\n  }\n};*/\n/*:: export type Pick<FromType, Properties: { [string]: 0 }> = $Exact<\n  $ObjMapi<Properties, <K, V>(k: K, v: V) => $ElementType<FromType, K>>\n>;*/\n// Helpful port from TS\n/*:: type REl = ReactElement<any>;*/\n/*:: export type ReactChildren = ReactChildrenArray<REl>;*/\n/*:: export type EventCallback = (\n  Layout,\n  oldItem: ?LayoutItem,\n  newItem: ?LayoutItem,\n  placeholder: ?LayoutItem,\n  Event,\n  ?HTMLElement\n) => void;*/\n// All callbacks are of the signature (layout, oldItem, newItem, placeholder, e).\n/*:: export type CompactType = ?(\"horizontal\" | \"vertical\");*/\nconst isProduction = process.env.NODE_ENV === \"production\";\nconst DEBUG = false;\n\n/**\n * Return the bottom coordinate of the layout.\n *\n * @param  {Array} layout Layout array.\n * @return {Number}       Bottom coordinate.\n */\nfunction bottom(layout /*: Layout*/) /*: number*/{\n  let max = 0,\n    bottomY;\n  for (let i = 0, len = layout.length; i < len; i++) {\n    bottomY = layout[i].y + layout[i].h;\n    if (bottomY > max) max = bottomY;\n  }\n  return max;\n}\nfunction cloneLayout(layout /*: Layout*/) /*: Layout*/{\n  const newLayout = Array(layout.length);\n  for (let i = 0, len = layout.length; i < len; i++) {\n    newLayout[i] = cloneLayoutItem(layout[i]);\n  }\n  return newLayout;\n}\n\n// Modify a layoutItem inside a layout. Returns a new Layout,\n// does not mutate. Carries over all other LayoutItems unmodified.\nfunction modifyLayout(layout /*: Layout*/, layoutItem /*: LayoutItem*/) /*: Layout*/{\n  const newLayout = Array(layout.length);\n  for (let i = 0, len = layout.length; i < len; i++) {\n    if (layoutItem.i === layout[i].i) {\n      newLayout[i] = layoutItem;\n    } else {\n      newLayout[i] = layout[i];\n    }\n  }\n  return newLayout;\n}\n\n// Function to be called to modify a layout item.\n// Does defensive clones to ensure the layout is not modified.\nfunction withLayoutItem(layout /*: Layout*/, itemKey /*: string*/, cb /*: LayoutItem => LayoutItem*/) /*: [Layout, ?LayoutItem]*/{\n  let item = getLayoutItem(layout, itemKey);\n  if (!item) return [layout, null];\n  item = cb(cloneLayoutItem(item)); // defensive clone then modify\n  // FIXME could do this faster if we already knew the index\n  layout = modifyLayout(layout, item);\n  return [layout, item];\n}\n\n// Fast path to cloning, since this is monomorphic\nfunction cloneLayoutItem(layoutItem /*: LayoutItem*/) /*: LayoutItem*/{\n  return {\n    w: layoutItem.w,\n    h: layoutItem.h,\n    x: layoutItem.x,\n    y: layoutItem.y,\n    i: layoutItem.i,\n    minW: layoutItem.minW,\n    maxW: layoutItem.maxW,\n    minH: layoutItem.minH,\n    maxH: layoutItem.maxH,\n    moved: Boolean(layoutItem.moved),\n    static: Boolean(layoutItem.static),\n    // These can be null/undefined\n    isDraggable: layoutItem.isDraggable,\n    isResizable: layoutItem.isResizable,\n    resizeHandles: layoutItem.resizeHandles,\n    isBounded: layoutItem.isBounded\n  };\n}\n\n/**\n * Comparing React `children` is a bit difficult. This is a good way to compare them.\n * This will catch differences in keys, order, and length.\n */\nfunction childrenEqual(a /*: ReactChildren*/, b /*: ReactChildren*/) /*: boolean*/{\n  return (0, _fastEquals.deepEqual)(_react.default.Children.map(a, c => c?.key), _react.default.Children.map(b, c => c?.key)) && (0, _fastEquals.deepEqual)(_react.default.Children.map(a, c => c?.props[\"data-grid\"]), _react.default.Children.map(b, c => c?.props[\"data-grid\"]));\n}\n\n/**\n * See `fastRGLPropsEqual.js`.\n * We want this to run as fast as possible - it is called often - and to be\n * resilient to new props that we add. So rather than call lodash.isEqual,\n * which isn't suited to comparing props very well, we use this specialized\n * function in conjunction with preval to generate the fastest possible comparison\n * function, tuned for exactly our props.\n */\n/*:: type FastRGLPropsEqual = (Object, Object, Function) => boolean;*/\nconst fastRGLPropsEqual /*: FastRGLPropsEqual*/ = exports.fastRGLPropsEqual = require(\"./fastRGLPropsEqual\");\n\n// Like the above, but a lot simpler.\nfunction fastPositionEqual(a /*: Position*/, b /*: Position*/) /*: boolean*/{\n  return a.left === b.left && a.top === b.top && a.width === b.width && a.height === b.height;\n}\n\n/**\n * Given two layoutitems, check if they collide.\n */\nfunction collides(l1 /*: LayoutItem*/, l2 /*: LayoutItem*/) /*: boolean*/{\n  if (l1.i === l2.i) return false; // same element\n  if (l1.x + l1.w <= l2.x) return false; // l1 is left of l2\n  if (l1.x >= l2.x + l2.w) return false; // l1 is right of l2\n  if (l1.y + l1.h <= l2.y) return false; // l1 is above l2\n  if (l1.y >= l2.y + l2.h) return false; // l1 is below l2\n  return true; // boxes overlap\n}\n\n/**\n * Given a layout, compact it. This involves going down each y coordinate and removing gaps\n * between items.\n *\n * Does not modify layout items (clones). Creates a new layout array.\n *\n * @param  {Array} layout Layout.\n * @param  {Boolean} verticalCompact Whether or not to compact the layout\n *   vertically.\n * @param  {Boolean} allowOverlap When `true`, allows overlapping grid items.\n * @return {Array}       Compacted Layout.\n */\nfunction compact(layout /*: Layout*/, compactType /*: CompactType*/, cols /*: number*/, allowOverlap /*: ?boolean*/) /*: Layout*/{\n  // Statics go in the compareWith array right away so items flow around them.\n  const compareWith = getStatics(layout);\n  // We go through the items by row and column.\n  const sorted = sortLayoutItems(layout, compactType);\n  // Holding for new items.\n  const out = Array(layout.length);\n  for (let i = 0, len = sorted.length; i < len; i++) {\n    let l = cloneLayoutItem(sorted[i]);\n\n    // Don't move static elements\n    if (!l.static) {\n      l = compactItem(compareWith, l, compactType, cols, sorted, allowOverlap);\n\n      // Add to comparison array. We only collide with items before this one.\n      // Statics are already in this array.\n      compareWith.push(l);\n    }\n\n    // Add to output array to make sure they still come out in the right order.\n    out[layout.indexOf(sorted[i])] = l;\n\n    // Clear moved flag, if it exists.\n    l.moved = false;\n  }\n  return out;\n}\nconst heightWidth = {\n  x: \"w\",\n  y: \"h\"\n};\n/**\n * Before moving item down, it will check if the movement will cause collisions and move those items down before.\n */\nfunction resolveCompactionCollision(layout /*: Layout*/, item /*: LayoutItem*/, moveToCoord /*: number*/, axis /*: \"x\" | \"y\"*/) {\n  const sizeProp = heightWidth[axis];\n  item[axis] += 1;\n  const itemIndex = layout.map(layoutItem => {\n    return layoutItem.i;\n  }).indexOf(item.i);\n\n  // Go through each item we collide with.\n  for (let i = itemIndex + 1; i < layout.length; i++) {\n    const otherItem = layout[i];\n    // Ignore static items\n    if (otherItem.static) continue;\n\n    // Optimization: we can break early if we know we're past this el\n    // We can do this b/c it's a sorted layout\n    if (otherItem.y > item.y + item.h) break;\n    if (collides(item, otherItem)) {\n      resolveCompactionCollision(layout, otherItem, moveToCoord + item[sizeProp], axis);\n    }\n  }\n  item[axis] = moveToCoord;\n}\n\n/**\n * Compact an item in the layout.\n *\n * Modifies item.\n *\n */\nfunction compactItem(compareWith /*: Layout*/, l /*: LayoutItem*/, compactType /*: CompactType*/, cols /*: number*/, fullLayout /*: Layout*/, allowOverlap /*: ?boolean*/) /*: LayoutItem*/{\n  const compactV = compactType === \"vertical\";\n  const compactH = compactType === \"horizontal\";\n  if (compactV) {\n    // Bottom 'y' possible is the bottom of the layout.\n    // This allows you to do nice stuff like specify {y: Infinity}\n    // This is here because the layout must be sorted in order to get the correct bottom `y`.\n    l.y = Math.min(bottom(compareWith), l.y);\n    // Move the element up as far as it can go without colliding.\n    while (l.y > 0 && !getFirstCollision(compareWith, l)) {\n      l.y--;\n    }\n  } else if (compactH) {\n    // Move the element left as far as it can go without colliding.\n    while (l.x > 0 && !getFirstCollision(compareWith, l)) {\n      l.x--;\n    }\n  }\n\n  // Move it down, and keep moving it down if it's colliding.\n  let collides;\n  // Checking the compactType null value to avoid breaking the layout when overlapping is allowed.\n  while ((collides = getFirstCollision(compareWith, l)) && !(compactType === null && allowOverlap)) {\n    if (compactH) {\n      resolveCompactionCollision(fullLayout, l, collides.x + collides.w, \"x\");\n    } else {\n      resolveCompactionCollision(fullLayout, l, collides.y + collides.h, \"y\");\n    }\n    // Since we can't grow without bounds horizontally, if we've overflown, let's move it down and try again.\n    if (compactH && l.x + l.w > cols) {\n      l.x = cols - l.w;\n      l.y++;\n      // ALso move element as left as we can\n      while (l.x > 0 && !getFirstCollision(compareWith, l)) {\n        l.x--;\n      }\n    }\n  }\n\n  // Ensure that there are no negative positions\n  l.y = Math.max(l.y, 0);\n  l.x = Math.max(l.x, 0);\n  return l;\n}\n\n/**\n * Given a layout, make sure all elements fit within its bounds.\n *\n * Modifies layout items.\n *\n * @param  {Array} layout Layout array.\n * @param  {Number} bounds Number of columns.\n */\nfunction correctBounds(layout /*: Layout*/, bounds /*: { cols: number }*/) /*: Layout*/{\n  const collidesWith = getStatics(layout);\n  for (let i = 0, len = layout.length; i < len; i++) {\n    const l = layout[i];\n    // Overflows right\n    if (l.x + l.w > bounds.cols) l.x = bounds.cols - l.w;\n    // Overflows left\n    if (l.x < 0) {\n      l.x = 0;\n      l.w = bounds.cols;\n    }\n    if (!l.static) collidesWith.push(l);else {\n      // If this is static and collides with other statics, we must move it down.\n      // We have to do something nicer than just letting them overlap.\n      while (getFirstCollision(collidesWith, l)) {\n        l.y++;\n      }\n    }\n  }\n  return layout;\n}\n\n/**\n * Get a layout item by ID. Used so we can override later on if necessary.\n *\n * @param  {Array}  layout Layout array.\n * @param  {String} id     ID\n * @return {LayoutItem}    Item at ID.\n */\nfunction getLayoutItem(layout /*: Layout*/, id /*: string*/) /*: ?LayoutItem*/{\n  for (let i = 0, len = layout.length; i < len; i++) {\n    if (layout[i].i === id) return layout[i];\n  }\n}\n\n/**\n * Returns the first item this layout collides with.\n * It doesn't appear to matter which order we approach this from, although\n * perhaps that is the wrong thing to do.\n *\n * @param  {Object} layoutItem Layout item.\n * @return {Object|undefined}  A colliding layout item, or undefined.\n */\nfunction getFirstCollision(layout /*: Layout*/, layoutItem /*: LayoutItem*/) /*: ?LayoutItem*/{\n  for (let i = 0, len = layout.length; i < len; i++) {\n    if (collides(layout[i], layoutItem)) return layout[i];\n  }\n}\nfunction getAllCollisions(layout /*: Layout*/, layoutItem /*: LayoutItem*/) /*: Array<LayoutItem>*/{\n  return layout.filter(l => collides(l, layoutItem));\n}\n\n/**\n * Get all static elements.\n * @param  {Array} layout Array of layout objects.\n * @return {Array}        Array of static layout items..\n */\nfunction getStatics(layout /*: Layout*/) /*: Array<LayoutItem>*/{\n  return layout.filter(l => l.static);\n}\n\n/**\n * Move an element. Responsible for doing cascading movements of other elements.\n *\n * Modifies layout items.\n *\n * @param  {Array}      layout            Full layout to modify.\n * @param  {LayoutItem} l                 element to move.\n * @param  {Number}     [x]               X position in grid units.\n * @param  {Number}     [y]               Y position in grid units.\n */\nfunction moveElement(layout /*: Layout*/, l /*: LayoutItem*/, x /*: ?number*/, y /*: ?number*/, isUserAction /*: ?boolean*/, preventCollision /*: ?boolean*/, compactType /*: CompactType*/, cols /*: number*/, allowOverlap /*: ?boolean*/) /*: Layout*/{\n  // If this is static and not explicitly enabled as draggable,\n  // no move is possible, so we can short-circuit this immediately.\n  if (l.static && l.isDraggable !== true) return layout;\n\n  // Short-circuit if nothing to do.\n  if (l.y === y && l.x === x) return layout;\n  log(`Moving element ${l.i} to [${String(x)},${String(y)}] from [${l.x},${l.y}]`);\n  const oldX = l.x;\n  const oldY = l.y;\n\n  // This is quite a bit faster than extending the object\n  if (typeof x === \"number\") l.x = x;\n  if (typeof y === \"number\") l.y = y;\n  l.moved = true;\n\n  // If this collides with anything, move it.\n  // When doing this comparison, we have to sort the items we compare with\n  // to ensure, in the case of multiple collisions, that we're getting the\n  // nearest collision.\n  let sorted = sortLayoutItems(layout, compactType);\n  const movingUp = compactType === \"vertical\" && typeof y === \"number\" ? oldY >= y : compactType === \"horizontal\" && typeof x === \"number\" ? oldX >= x : false;\n  // $FlowIgnore acceptable modification of read-only array as it was recently cloned\n  if (movingUp) sorted = sorted.reverse();\n  const collisions = getAllCollisions(sorted, l);\n  const hasCollisions = collisions.length > 0;\n\n  // We may have collisions. We can short-circuit if we've turned off collisions or\n  // allowed overlap.\n  if (hasCollisions && allowOverlap) {\n    // Easy, we don't need to resolve collisions. But we *did* change the layout,\n    // so clone it on the way out.\n    return cloneLayout(layout);\n  } else if (hasCollisions && preventCollision) {\n    // If we are preventing collision but not allowing overlap, we need to\n    // revert the position of this element so it goes to where it came from, rather\n    // than the user's desired location.\n    log(`Collision prevented on ${l.i}, reverting.`);\n    l.x = oldX;\n    l.y = oldY;\n    l.moved = false;\n    return layout; // did not change so don't clone\n  }\n\n  // Move each item that collides away from this element.\n  for (let i = 0, len = collisions.length; i < len; i++) {\n    const collision = collisions[i];\n    log(`Resolving collision between ${l.i} at [${l.x},${l.y}] and ${collision.i} at [${collision.x},${collision.y}]`);\n\n    // Short circuit so we can't infinite loop\n    if (collision.moved) continue;\n\n    // Don't move static items - we have to move *this* element away\n    if (collision.static) {\n      layout = moveElementAwayFromCollision(layout, collision, l, isUserAction, compactType, cols);\n    } else {\n      layout = moveElementAwayFromCollision(layout, l, collision, isUserAction, compactType, cols);\n    }\n  }\n  return layout;\n}\n\n/**\n * This is where the magic needs to happen - given a collision, move an element away from the collision.\n * We attempt to move it up if there's room, otherwise it goes below.\n *\n * @param  {Array} layout            Full layout to modify.\n * @param  {LayoutItem} collidesWith Layout item we're colliding with.\n * @param  {LayoutItem} itemToMove   Layout item we're moving.\n */\nfunction moveElementAwayFromCollision(layout /*: Layout*/, collidesWith /*: LayoutItem*/, itemToMove /*: LayoutItem*/, isUserAction /*: ?boolean*/, compactType /*: CompactType*/, cols /*: number*/) /*: Layout*/{\n  const compactH = compactType === \"horizontal\";\n  // Compact vertically if not set to horizontal\n  const compactV = compactType === \"vertical\";\n  const preventCollision = collidesWith.static; // we're already colliding (not for static items)\n\n  // If there is enough space above the collision to put this element, move it there.\n  // We only do this on the main collision as this can get funky in cascades and cause\n  // unwanted swapping behavior.\n  if (isUserAction) {\n    // Reset isUserAction flag because we're not in the main collision anymore.\n    isUserAction = false;\n\n    // Make a mock item so we don't modify the item here, only modify in moveElement.\n    const fakeItem /*: LayoutItem*/ = {\n      x: compactH ? Math.max(collidesWith.x - itemToMove.w, 0) : itemToMove.x,\n      y: compactV ? Math.max(collidesWith.y - itemToMove.h, 0) : itemToMove.y,\n      w: itemToMove.w,\n      h: itemToMove.h,\n      i: \"-1\"\n    };\n    const firstCollision = getFirstCollision(layout, fakeItem);\n    const collisionNorth = firstCollision && firstCollision.y + firstCollision.h > collidesWith.y;\n    const collisionWest = firstCollision && collidesWith.x + collidesWith.w > firstCollision.x;\n\n    // No collision? If so, we can go up there; otherwise, we'll end up moving down as normal\n    if (!firstCollision) {\n      log(`Doing reverse collision on ${itemToMove.i} up to [${fakeItem.x},${fakeItem.y}].`);\n      return moveElement(layout, itemToMove, compactH ? fakeItem.x : undefined, compactV ? fakeItem.y : undefined, isUserAction, preventCollision, compactType, cols);\n    } else if (collisionNorth && compactV) {\n      return moveElement(layout, itemToMove, undefined, collidesWith.y + 1, isUserAction, preventCollision, compactType, cols);\n    } else if (collisionNorth && compactType == null) {\n      collidesWith.y = itemToMove.y;\n      itemToMove.y = itemToMove.y + itemToMove.h;\n      return layout;\n    } else if (collisionWest && compactH) {\n      return moveElement(layout, collidesWith, itemToMove.x, undefined, isUserAction, preventCollision, compactType, cols);\n    }\n  }\n  const newX = compactH ? itemToMove.x + 1 : undefined;\n  const newY = compactV ? itemToMove.y + 1 : undefined;\n  if (newX == null && newY == null) {\n    return layout;\n  }\n  return moveElement(layout, itemToMove, compactH ? itemToMove.x + 1 : undefined, compactV ? itemToMove.y + 1 : undefined, isUserAction, preventCollision, compactType, cols);\n}\n\n/**\n * Helper to convert a number to a percentage string.\n *\n * @param  {Number} num Any number\n * @return {String}     That number as a percentage.\n */\nfunction perc(num /*: number*/) /*: string*/{\n  return num * 100 + \"%\";\n}\n\n/**\n * Helper functions to constrain dimensions of a GridItem\n */\nconst constrainWidth = (left /*: number*/, currentWidth /*: number*/, newWidth /*: number*/, containerWidth /*: number*/) => {\n  return left + newWidth > containerWidth ? currentWidth : newWidth;\n};\nconst constrainHeight = (top /*: number*/, currentHeight /*: number*/, newHeight /*: number*/) => {\n  return top < 0 ? currentHeight : newHeight;\n};\nconst constrainLeft = (left /*: number*/) => Math.max(0, left);\nconst constrainTop = (top /*: number*/) => Math.max(0, top);\nconst resizeNorth = (currentSize, _ref, _containerWidth) => {\n  let {\n    left,\n    height,\n    width\n  } = _ref;\n  const top = currentSize.top - (height - currentSize.height);\n  return {\n    left,\n    width,\n    height: constrainHeight(top, currentSize.height, height),\n    top: constrainTop(top)\n  };\n};\nconst resizeEast = (currentSize, _ref2, containerWidth) => {\n  let {\n    top,\n    left,\n    height,\n    width\n  } = _ref2;\n  return {\n    top,\n    height,\n    width: constrainWidth(currentSize.left, currentSize.width, width, containerWidth),\n    left: constrainLeft(left)\n  };\n};\nconst resizeWest = (currentSize, _ref3, containerWidth) => {\n  let {\n    top,\n    height,\n    width\n  } = _ref3;\n  const left = currentSize.left - (width - currentSize.width);\n  return {\n    height,\n    width: left < 0 ? currentSize.width : constrainWidth(currentSize.left, currentSize.width, width, containerWidth),\n    top: constrainTop(top),\n    left: constrainLeft(left)\n  };\n};\nconst resizeSouth = (currentSize, _ref4, containerWidth) => {\n  let {\n    top,\n    left,\n    height,\n    width\n  } = _ref4;\n  return {\n    width,\n    left,\n    height: constrainHeight(top, currentSize.height, height),\n    top: constrainTop(top)\n  };\n};\nconst resizeNorthEast = function () {\n  return resizeNorth(arguments.length <= 0 ? undefined : arguments[0], resizeEast(...arguments), arguments.length <= 2 ? undefined : arguments[2]);\n};\nconst resizeNorthWest = function () {\n  return resizeNorth(arguments.length <= 0 ? undefined : arguments[0], resizeWest(...arguments), arguments.length <= 2 ? undefined : arguments[2]);\n};\nconst resizeSouthEast = function () {\n  return resizeSouth(arguments.length <= 0 ? undefined : arguments[0], resizeEast(...arguments), arguments.length <= 2 ? undefined : arguments[2]);\n};\nconst resizeSouthWest = function () {\n  return resizeSouth(arguments.length <= 0 ? undefined : arguments[0], resizeWest(...arguments), arguments.length <= 2 ? undefined : arguments[2]);\n};\nconst ordinalResizeHandlerMap = {\n  n: resizeNorth,\n  ne: resizeNorthEast,\n  e: resizeEast,\n  se: resizeSouthEast,\n  s: resizeSouth,\n  sw: resizeSouthWest,\n  w: resizeWest,\n  nw: resizeNorthWest\n};\n\n/**\n * Helper for clamping width and position when resizing an item.\n */\nfunction resizeItemInDirection(direction /*: ResizeHandleAxis*/, currentSize /*: Position*/, newSize /*: Position*/, containerWidth /*: number*/) /*: Position*/{\n  const ordinalHandler = ordinalResizeHandlerMap[direction];\n  // Shouldn't be possible given types; that said, don't fail hard\n  if (!ordinalHandler) return newSize;\n  return ordinalHandler(currentSize, {\n    ...currentSize,\n    ...newSize\n  }, containerWidth);\n}\nfunction setTransform(_ref5 /*:: */) /*: Object*/{\n  let {\n    top,\n    left,\n    width,\n    height\n  } /*: Position*/ = _ref5 /*: Position*/;\n  // Replace unitless items with px\n  const translate = `translate(${left}px,${top}px)`;\n  return {\n    transform: translate,\n    WebkitTransform: translate,\n    MozTransform: translate,\n    msTransform: translate,\n    OTransform: translate,\n    width: `${width}px`,\n    height: `${height}px`,\n    position: \"absolute\"\n  };\n}\nfunction setTopLeft(_ref6 /*:: */) /*: Object*/{\n  let {\n    top,\n    left,\n    width,\n    height\n  } /*: Position*/ = _ref6 /*: Position*/;\n  return {\n    top: `${top}px`,\n    left: `${left}px`,\n    width: `${width}px`,\n    height: `${height}px`,\n    position: \"absolute\"\n  };\n}\n\n/**\n * Get layout items sorted from top left to right and down.\n *\n * @return {Array} Array of layout objects.\n * @return {Array}        Layout, sorted static items first.\n */\nfunction sortLayoutItems(layout /*: Layout*/, compactType /*: CompactType*/) /*: Layout*/{\n  if (compactType === \"horizontal\") return sortLayoutItemsByColRow(layout);\n  if (compactType === \"vertical\") return sortLayoutItemsByRowCol(layout);else return layout;\n}\n\n/**\n * Sort layout items by row ascending and column ascending.\n *\n * Does not modify Layout.\n */\nfunction sortLayoutItemsByRowCol(layout /*: Layout*/) /*: Layout*/{\n  // Slice to clone array as sort modifies\n  return layout.slice(0).sort(function (a, b) {\n    if (a.y > b.y || a.y === b.y && a.x > b.x) {\n      return 1;\n    } else if (a.y === b.y && a.x === b.x) {\n      // Without this, we can get different sort results in IE vs. Chrome/FF\n      return 0;\n    }\n    return -1;\n  });\n}\n\n/**\n * Sort layout items by column ascending then row ascending.\n *\n * Does not modify Layout.\n */\nfunction sortLayoutItemsByColRow(layout /*: Layout*/) /*: Layout*/{\n  return layout.slice(0).sort(function (a, b) {\n    if (a.x > b.x || a.x === b.x && a.y > b.y) {\n      return 1;\n    }\n    return -1;\n  });\n}\n\n/**\n * Generate a layout using the initialLayout and children as a template.\n * Missing entries will be added, extraneous ones will be truncated.\n *\n * Does not modify initialLayout.\n *\n * @param  {Array}  initialLayout Layout passed in through props.\n * @param  {String} breakpoint    Current responsive breakpoint.\n * @param  {?String} compact      Compaction option.\n * @return {Array}                Working layout.\n */\nfunction synchronizeLayoutWithChildren(initialLayout /*: Layout*/, children /*: ReactChildren*/, cols /*: number*/, compactType /*: CompactType*/, allowOverlap /*: ?boolean*/) /*: Layout*/{\n  initialLayout = initialLayout || [];\n\n  // Generate one layout item per child.\n  const layout /*: LayoutItem[]*/ = [];\n  _react.default.Children.forEach(children, (child /*: ReactElement<any>*/) => {\n    // Child may not exist\n    if (child?.key == null) return;\n    const exists = getLayoutItem(initialLayout, String(child.key));\n    const g = child.props[\"data-grid\"];\n    // Don't overwrite the layout item if it's already in the initial layout.\n    // If it has a `data-grid` property, prefer that over what's in the layout.\n    if (exists && g == null) {\n      layout.push(cloneLayoutItem(exists));\n    } else {\n      // Hey, this item has a data-grid property, use it.\n      if (g) {\n        if (!isProduction) {\n          validateLayout([g], \"ReactGridLayout.children\");\n        }\n        // FIXME clone not really necessary here\n        layout.push(cloneLayoutItem({\n          ...g,\n          i: child.key\n        }));\n      } else {\n        // Nothing provided: ensure this is added to the bottom\n        // FIXME clone not really necessary here\n        layout.push(cloneLayoutItem({\n          w: 1,\n          h: 1,\n          x: 0,\n          y: bottom(layout),\n          i: String(child.key)\n        }));\n      }\n    }\n  });\n\n  // Correct the layout.\n  const correctedLayout = correctBounds(layout, {\n    cols: cols\n  });\n  return allowOverlap ? correctedLayout : compact(correctedLayout, compactType, cols);\n}\n\n/**\n * Validate a layout. Throws errors.\n *\n * @param  {Array}  layout        Array of layout items.\n * @param  {String} [contextName] Context name for errors.\n * @throw  {Error}                Validation error.\n */\nfunction validateLayout(layout /*: Layout*/) /*: void*/{\n  let contextName /*: string*/ = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"Layout\";\n  const subProps = [\"x\", \"y\", \"w\", \"h\"];\n  if (!Array.isArray(layout)) throw new Error(contextName + \" must be an array!\");\n  for (let i = 0, len = layout.length; i < len; i++) {\n    const item = layout[i];\n    for (let j = 0; j < subProps.length; j++) {\n      const key = subProps[j];\n      const value = item[key];\n      if (typeof value !== \"number\" || Number.isNaN(value)) {\n        throw new Error(`ReactGridLayout: ${contextName}[${i}].${key} must be a number! Received: ${value} (${typeof value})`);\n      }\n    }\n    if (typeof item.i !== \"undefined\" && typeof item.i !== \"string\") {\n      throw new Error(`ReactGridLayout: ${contextName}[${i}].i must be a string! Received: ${item.i} (${typeof item.i})`);\n    }\n  }\n}\n\n// Legacy support for verticalCompact: false\nfunction compactType(props /*: ?{ verticalCompact: boolean, compactType: CompactType }*/) /*: CompactType*/{\n  const {\n    verticalCompact,\n    compactType\n  } = props || {};\n  return verticalCompact === false ? null : compactType;\n}\nfunction log() {\n  if (!DEBUG) return;\n  // eslint-disable-next-line no-console\n  console.log(...arguments);\n}\nconst noop = () => {};\nexports.noop = noop;"], "names": [], "mappings": "AAwHqB;AAxHrB;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,MAAM,GAAG;AACjB,QAAQ,aAAa,GAAG;AACxB,QAAQ,WAAW,GAAG;AACtB,QAAQ,eAAe,GAAG;AAC1B,QAAQ,QAAQ,GAAG;AACnB,QAAQ,OAAO,GAAG;AAClB,QAAQ,WAAW,GAAG;AACtB,QAAQ,WAAW,GAAG;AACtB,QAAQ,aAAa,GAAG;AACxB,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,iBAAiB,GAAG,KAAK;AACjC,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,aAAa,GAAG;AACxB,QAAQ,UAAU,GAAG;AACrB,QAAQ,YAAY,GAAG;AACvB,QAAQ,WAAW,GAAG;AACtB,QAAQ,4BAA4B,GAAG;AACvC,QAAQ,IAAI,GAAG,KAAK;AACpB,QAAQ,IAAI,GAAG;AACf,QAAQ,qBAAqB,GAAG;AAChC,QAAQ,UAAU,GAAG;AACrB,QAAQ,YAAY,GAAG;AACvB,QAAQ,eAAe,GAAG;AAC1B,QAAQ,uBAAuB,GAAG;AAClC,QAAQ,uBAAuB,GAAG;AAClC,QAAQ,6BAA6B,GAAG;AACxC,QAAQ,cAAc,GAAG;AACzB,QAAQ,cAAc,GAAG;AACzB,IAAI;AACJ,IAAI,SAAS;AACb,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AAAG;AAC9F;;;eAGe,GACf;;;;;;;;SAQS,GACT;;;;;;;;;;;;;;;;EAgBE,GACF,qDAAqD,GACrD;;;;;EAKE,GACF;;;;;;;;EAQE,GACF,iEAAiE,GACjE,4EAA4E,GAC5E,0DAA0D,GAC1D;;;;EAIE,GACF;;;;;EAKE,GACF;;;;;;EAME,GACF;;EAEE,GACF,uBAAuB;AACvB,kCAAkC,GAClC,yDAAyD,GACzD;;;;;;;UAOU,GACV,iFAAiF;AACjF,4DAA4D,GAC5D,MAAM,eAAe,oDAAyB;AAC9C,MAAM,QAAQ;AAEd;;;;;CAKC,GACD,SAAS,OAAO,OAAO,UAAU,GAAX,EAAe,UAAU;IAC7C,IAAI,MAAM,GACR;IACF,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,UAAU,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QACnC,IAAI,UAAU,KAAK,MAAM;IAC3B;IACA,OAAO;AACT;AACA,SAAS,YAAY,OAAO,UAAU,GAAX,EAAe,UAAU;IAClD,MAAM,YAAY,MAAM,OAAO,MAAM;IACrC,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,SAAS,CAAC,EAAE,GAAG,gBAAgB,MAAM,CAAC,EAAE;IAC1C;IACA,OAAO;AACT;AAEA,6DAA6D;AAC7D,kEAAkE;AAClE,SAAS,aAAa,OAAO,UAAU,GAAX,EAAe,WAAW,cAAc,GAAf,EAAmB,UAAU;IAChF,MAAM,YAAY,MAAM,OAAO,MAAM;IACrC,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,IAAI,WAAW,CAAC,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YAChC,SAAS,CAAC,EAAE,GAAG;QACjB,OAAO;YACL,SAAS,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;QAC1B;IACF;IACA,OAAO;AACT;AAEA,iDAAiD;AACjD,8DAA8D;AAC9D,SAAS,eAAe,OAAO,UAAU,GAAX,EAAe,QAAQ,UAAU,GAAX,EAAe,GAAG,4BAA4B,GAA7B,EAAiC,yBAAyB;IAC7H,IAAI,OAAO,cAAc,QAAQ;IACjC,IAAI,CAAC,MAAM,OAAO;QAAC;QAAQ;KAAK;IAChC,OAAO,GAAG,gBAAgB,QAAQ,8BAA8B;IAChE,0DAA0D;IAC1D,SAAS,aAAa,QAAQ;IAC9B,OAAO;QAAC;QAAQ;KAAK;AACvB;AAEA,kDAAkD;AAClD,SAAS,gBAAgB,WAAW,cAAc,GAAf,EAAmB,cAAc;IAClE,OAAO;QACL,GAAG,WAAW,CAAC;QACf,GAAG,WAAW,CAAC;QACf,GAAG,WAAW,CAAC;QACf,GAAG,WAAW,CAAC;QACf,GAAG,WAAW,CAAC;QACf,MAAM,WAAW,IAAI;QACrB,MAAM,WAAW,IAAI;QACrB,MAAM,WAAW,IAAI;QACrB,MAAM,WAAW,IAAI;QACrB,OAAO,QAAQ,WAAW,KAAK;QAC/B,QAAQ,QAAQ,WAAW,MAAM;QACjC,8BAA8B;QAC9B,aAAa,WAAW,WAAW;QACnC,aAAa,WAAW,WAAW;QACnC,eAAe,WAAW,aAAa;QACvC,WAAW,WAAW,SAAS;IACjC;AACF;AAEA;;;CAGC,GACD,SAAS,cAAc,EAAE,iBAAiB,GAAlB,EAAsB,EAAE,iBAAiB,GAAlB,EAAsB,WAAW;IAC9E,OAAO,CAAC,GAAG,YAAY,SAAS,EAAE,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAA,IAAK,GAAG,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAA,IAAK,GAAG,SAAS,CAAC,GAAG,YAAY,SAAS,EAAE,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAA,IAAK,GAAG,KAAK,CAAC,YAAY,GAAG,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAA,IAAK,GAAG,KAAK,CAAC,YAAY;AACjR;AAEA;;;;;;;CAOC,GACD,oEAAoE,GACpE,MAAM,kBAAkB,qBAAqB,MAAK,QAAQ,iBAAiB;AAE3E,qCAAqC;AACrC,SAAS,kBAAkB,EAAE,YAAY,GAAb,EAAiB,EAAE,YAAY,GAAb,EAAiB,WAAW;IACxE,OAAO,EAAE,IAAI,KAAK,EAAE,IAAI,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM;AAC7F;AAEA;;CAEC,GACD,SAAS,SAAS,GAAG,cAAc,GAAf,EAAmB,GAAG,cAAc,GAAf,EAAmB,WAAW;IACrE,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,OAAO,OAAO,eAAe;IAChD,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,OAAO,OAAO,mBAAmB;IAC1D,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,OAAO,oBAAoB;IAC3D,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,OAAO,OAAO,iBAAiB;IACxD,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,OAAO,iBAAiB;IACxD,OAAO,MAAM,gBAAgB;AAC/B;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,QAAQ,OAAO,UAAU,GAAX,EAAe,YAAY,eAAe,GAAhB,EAAoB,KAAK,UAAU,GAAX,EAAe,aAAa,YAAY,GAAb,EAAiB,UAAU;IAC7H,4EAA4E;IAC5E,MAAM,cAAc,WAAW;IAC/B,6CAA6C;IAC7C,MAAM,SAAS,gBAAgB,QAAQ;IACvC,yBAAyB;IACzB,MAAM,MAAM,MAAM,OAAO,MAAM;IAC/B,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,IAAI,IAAI,gBAAgB,MAAM,CAAC,EAAE;QAEjC,6BAA6B;QAC7B,IAAI,CAAC,EAAE,MAAM,EAAE;YACb,IAAI,YAAY,aAAa,GAAG,aAAa,MAAM,QAAQ;YAE3D,uEAAuE;YACvE,qCAAqC;YACrC,YAAY,IAAI,CAAC;QACnB;QAEA,2EAA2E;QAC3E,GAAG,CAAC,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG;QAEjC,kCAAkC;QAClC,EAAE,KAAK,GAAG;IACZ;IACA,OAAO;AACT;AACA,MAAM,cAAc;IAClB,GAAG;IACH,GAAG;AACL;AACA;;CAEC,GACD,SAAS,2BAA2B,OAAO,UAAU,GAAX,EAAe,KAAK,cAAc,GAAf,EAAmB,YAAY,UAAU,GAAX,EAAe,KAAK,aAAa,GAAd;IAC5G,MAAM,WAAW,WAAW,CAAC,KAAK;IAClC,IAAI,CAAC,KAAK,IAAI;IACd,MAAM,YAAY,OAAO,GAAG,CAAC,CAAA;QAC3B,OAAO,WAAW,CAAC;IACrB,GAAG,OAAO,CAAC,KAAK,CAAC;IAEjB,wCAAwC;IACxC,IAAK,IAAI,IAAI,YAAY,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QAClD,MAAM,YAAY,MAAM,CAAC,EAAE;QAC3B,sBAAsB;QACtB,IAAI,UAAU,MAAM,EAAE;QAEtB,iEAAiE;QACjE,0CAA0C;QAC1C,IAAI,UAAU,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE;QACnC,IAAI,SAAS,MAAM,YAAY;YAC7B,2BAA2B,QAAQ,WAAW,cAAc,IAAI,CAAC,SAAS,EAAE;QAC9E;IACF;IACA,IAAI,CAAC,KAAK,GAAG;AACf;AAEA;;;;;CAKC,GACD,SAAS,YAAY,YAAY,UAAU,GAAX,EAAe,EAAE,cAAc,GAAf,EAAmB,YAAY,eAAe,GAAhB,EAAoB,KAAK,UAAU,GAAX,EAAe,WAAW,UAAU,GAAX,EAAe,aAAa,YAAY,GAAb,EAAiB,cAAc;IACvL,MAAM,WAAW,gBAAgB;IACjC,MAAM,WAAW,gBAAgB;IACjC,IAAI,UAAU;QACZ,mDAAmD;QACnD,8DAA8D;QAC9D,yFAAyF;QACzF,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,cAAc,EAAE,CAAC;QACvC,6DAA6D;QAC7D,MAAO,EAAE,CAAC,GAAG,KAAK,CAAC,kBAAkB,aAAa,GAAI;YACpD,EAAE,CAAC;QACL;IACF,OAAO,IAAI,UAAU;QACnB,+DAA+D;QAC/D,MAAO,EAAE,CAAC,GAAG,KAAK,CAAC,kBAAkB,aAAa,GAAI;YACpD,EAAE,CAAC;QACL;IACF;IAEA,2DAA2D;IAC3D,IAAI;IACJ,gGAAgG;IAChG,MAAO,CAAC,WAAW,kBAAkB,aAAa,EAAE,KAAK,CAAC,CAAC,gBAAgB,QAAQ,YAAY,EAAG;QAChG,IAAI,UAAU;YACZ,2BAA2B,YAAY,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,EAAE;QACrE,OAAO;YACL,2BAA2B,YAAY,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,EAAE;QACrE;QACA,yGAAyG;QACzG,IAAI,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM;YAChC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC;YAChB,EAAE,CAAC;YACH,sCAAsC;YACtC,MAAO,EAAE,CAAC,GAAG,KAAK,CAAC,kBAAkB,aAAa,GAAI;gBACpD,EAAE,CAAC;YACL;QACF;IACF;IAEA,8CAA8C;IAC9C,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE;IACpB,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE;IACpB,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,cAAc,OAAO,UAAU,GAAX,EAAe,OAAO,oBAAoB,GAArB,EAAyB,UAAU;IACnF,MAAM,eAAe,WAAW;IAChC,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,MAAM,IAAI,MAAM,CAAC,EAAE;QACnB,kBAAkB;QAClB,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,IAAI,EAAE,EAAE,CAAC,GAAG,OAAO,IAAI,GAAG,EAAE,CAAC;QACpD,iBAAiB;QACjB,IAAI,EAAE,CAAC,GAAG,GAAG;YACX,EAAE,CAAC,GAAG;YACN,EAAE,CAAC,GAAG,OAAO,IAAI;QACnB;QACA,IAAI,CAAC,EAAE,MAAM,EAAE,aAAa,IAAI,CAAC;aAAQ;YACvC,2EAA2E;YAC3E,gEAAgE;YAChE,MAAO,kBAAkB,cAAc,GAAI;gBACzC,EAAE,CAAC;YACL;QACF;IACF;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,cAAc,OAAO,UAAU,GAAX,EAAe,GAAG,UAAU,GAAX,EAAe,eAAe;IAC1E,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,OAAO,MAAM,CAAC,EAAE;IAC1C;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,kBAAkB,OAAO,UAAU,GAAX,EAAe,WAAW,cAAc,GAAf,EAAmB,eAAe;IAC1F,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,IAAI,SAAS,MAAM,CAAC,EAAE,EAAE,aAAa,OAAO,MAAM,CAAC,EAAE;IACvD;AACF;AACA,SAAS,iBAAiB,OAAO,UAAU,GAAX,EAAe,WAAW,cAAc,GAAf,EAAmB,qBAAqB;IAC/F,OAAO,OAAO,MAAM,CAAC,CAAA,IAAK,SAAS,GAAG;AACxC;AAEA;;;;CAIC,GACD,SAAS,WAAW,OAAO,UAAU,GAAX,EAAe,qBAAqB;IAC5D,OAAO,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM;AACpC;AAEA;;;;;;;;;CASC,GACD,SAAS,YAAY,OAAO,UAAU,GAAX,EAAe,EAAE,cAAc,GAAf,EAAmB,EAAE,WAAW,GAAZ,EAAgB,EAAE,WAAW,GAAZ,EAAgB,aAAa,YAAY,GAAb,EAAiB,iBAAiB,YAAY,GAAb,EAAiB,YAAY,eAAe,GAAhB,EAAoB,KAAK,UAAU,GAAX,EAAe,aAAa,YAAY,GAAb,EAAiB,UAAU;IACrP,6DAA6D;IAC7D,iEAAiE;IACjE,IAAI,EAAE,MAAM,IAAI,EAAE,WAAW,KAAK,MAAM,OAAO;IAE/C,kCAAkC;IAClC,IAAI,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,KAAK,GAAG,OAAO;IACnC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/E,MAAM,OAAO,EAAE,CAAC;IAChB,MAAM,OAAO,EAAE,CAAC;IAEhB,uDAAuD;IACvD,IAAI,OAAO,MAAM,UAAU,EAAE,CAAC,GAAG;IACjC,IAAI,OAAO,MAAM,UAAU,EAAE,CAAC,GAAG;IACjC,EAAE,KAAK,GAAG;IAEV,2CAA2C;IAC3C,wEAAwE;IACxE,wEAAwE;IACxE,qBAAqB;IACrB,IAAI,SAAS,gBAAgB,QAAQ;IACrC,MAAM,WAAW,gBAAgB,cAAc,OAAO,MAAM,WAAW,QAAQ,IAAI,gBAAgB,gBAAgB,OAAO,MAAM,WAAW,QAAQ,IAAI;IACvJ,mFAAmF;IACnF,IAAI,UAAU,SAAS,OAAO,OAAO;IACrC,MAAM,aAAa,iBAAiB,QAAQ;IAC5C,MAAM,gBAAgB,WAAW,MAAM,GAAG;IAE1C,iFAAiF;IACjF,mBAAmB;IACnB,IAAI,iBAAiB,cAAc;QACjC,6EAA6E;QAC7E,8BAA8B;QAC9B,OAAO,YAAY;IACrB,OAAO,IAAI,iBAAiB,kBAAkB;QAC5C,sEAAsE;QACtE,+EAA+E;QAC/E,oCAAoC;QACpC,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC;QAC/C,EAAE,CAAC,GAAG;QACN,EAAE,CAAC,GAAG;QACN,EAAE,KAAK,GAAG;QACV,OAAO,QAAQ,gCAAgC;IACjD;IAEA,uDAAuD;IACvD,IAAK,IAAI,IAAI,GAAG,MAAM,WAAW,MAAM,EAAE,IAAI,KAAK,IAAK;QACrD,MAAM,YAAY,UAAU,CAAC,EAAE;QAC/B,IAAI,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;QAEjH,0CAA0C;QAC1C,IAAI,UAAU,KAAK,EAAE;QAErB,gEAAgE;QAChE,IAAI,UAAU,MAAM,EAAE;YACpB,SAAS,6BAA6B,QAAQ,WAAW,GAAG,cAAc,aAAa;QACzF,OAAO;YACL,SAAS,6BAA6B,QAAQ,GAAG,WAAW,cAAc,aAAa;QACzF;IACF;IACA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,6BAA6B,OAAO,UAAU,GAAX,EAAe,aAAa,cAAc,GAAf,EAAmB,WAAW,cAAc,GAAf,EAAmB,aAAa,YAAY,GAAb,EAAiB,YAAY,eAAe,GAAhB,EAAoB,KAAK,UAAU,GAAX,EAAe,UAAU;IAC9M,MAAM,WAAW,gBAAgB;IACjC,8CAA8C;IAC9C,MAAM,WAAW,gBAAgB;IACjC,MAAM,mBAAmB,aAAa,MAAM,EAAE,iDAAiD;IAE/F,mFAAmF;IACnF,oFAAoF;IACpF,8BAA8B;IAC9B,IAAI,cAAc;QAChB,2EAA2E;QAC3E,eAAe;QAEf,iFAAiF;QACjF,MAAM,SAAS,cAAc,MAAK;YAChC,GAAG,WAAW,KAAK,GAAG,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC,EAAE,KAAK,WAAW,CAAC;YACvE,GAAG,WAAW,KAAK,GAAG,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC,EAAE,KAAK,WAAW,CAAC;YACvE,GAAG,WAAW,CAAC;YACf,GAAG,WAAW,CAAC;YACf,GAAG;QACL;QACA,MAAM,iBAAiB,kBAAkB,QAAQ;QACjD,MAAM,iBAAiB,kBAAkB,eAAe,CAAC,GAAG,eAAe,CAAC,GAAG,aAAa,CAAC;QAC7F,MAAM,gBAAgB,kBAAkB,aAAa,CAAC,GAAG,aAAa,CAAC,GAAG,eAAe,CAAC;QAE1F,yFAAyF;QACzF,IAAI,CAAC,gBAAgB;YACnB,IAAI,CAAC,2BAA2B,EAAE,WAAW,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;YACrF,OAAO,YAAY,QAAQ,YAAY,WAAW,SAAS,CAAC,GAAG,WAAW,WAAW,SAAS,CAAC,GAAG,WAAW,cAAc,kBAAkB,aAAa;QAC5J,OAAO,IAAI,kBAAkB,UAAU;YACrC,OAAO,YAAY,QAAQ,YAAY,WAAW,aAAa,CAAC,GAAG,GAAG,cAAc,kBAAkB,aAAa;QACrH,OAAO,IAAI,kBAAkB,eAAe,MAAM;YAChD,aAAa,CAAC,GAAG,WAAW,CAAC;YAC7B,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC;YAC1C,OAAO;QACT,OAAO,IAAI,iBAAiB,UAAU;YACpC,OAAO,YAAY,QAAQ,cAAc,WAAW,CAAC,EAAE,WAAW,cAAc,kBAAkB,aAAa;QACjH;IACF;IACA,MAAM,OAAO,WAAW,WAAW,CAAC,GAAG,IAAI;IAC3C,MAAM,OAAO,WAAW,WAAW,CAAC,GAAG,IAAI;IAC3C,IAAI,QAAQ,QAAQ,QAAQ,MAAM;QAChC,OAAO;IACT;IACA,OAAO,YAAY,QAAQ,YAAY,WAAW,WAAW,CAAC,GAAG,IAAI,WAAW,WAAW,WAAW,CAAC,GAAG,IAAI,WAAW,cAAc,kBAAkB,aAAa;AACxK;AAEA;;;;;CAKC,GACD,SAAS,KAAK,IAAI,UAAU,GAAX,EAAe,UAAU;IACxC,OAAO,MAAM,MAAM;AACrB;AAEA;;CAEC,GACD,MAAM,iBAAiB,CAAC,KAAK,UAAU,KAAI,aAAa,UAAU,KAAI,SAAS,UAAU,KAAI,eAAe,UAAU;IACpH,OAAO,OAAO,WAAW,iBAAiB,eAAe;AAC3D;AACA,MAAM,kBAAkB,CAAC,IAAI,UAAU,KAAI,cAAc,UAAU,KAAI,UAAU,UAAU;IACzF,OAAO,MAAM,IAAI,gBAAgB;AACnC;AACA,MAAM,gBAAgB,CAAC,KAAK,UAAU,MAAO,KAAK,GAAG,CAAC,GAAG;AACzD,MAAM,eAAe,CAAC,IAAI,UAAU,MAAO,KAAK,GAAG,CAAC,GAAG;AACvD,MAAM,cAAc,CAAC,aAAa,MAAM;IACtC,IAAI,EACF,IAAI,EACJ,MAAM,EACN,KAAK,EACN,GAAG;IACJ,MAAM,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,YAAY,MAAM;IAC1D,OAAO;QACL;QACA;QACA,QAAQ,gBAAgB,KAAK,YAAY,MAAM,EAAE;QACjD,KAAK,aAAa;IACpB;AACF;AACA,MAAM,aAAa,CAAC,aAAa,OAAO;IACtC,IAAI,EACF,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACN,GAAG;IACJ,OAAO;QACL;QACA;QACA,OAAO,eAAe,YAAY,IAAI,EAAE,YAAY,KAAK,EAAE,OAAO;QAClE,MAAM,cAAc;IACtB;AACF;AACA,MAAM,aAAa,CAAC,aAAa,OAAO;IACtC,IAAI,EACF,GAAG,EACH,MAAM,EACN,KAAK,EACN,GAAG;IACJ,MAAM,OAAO,YAAY,IAAI,GAAG,CAAC,QAAQ,YAAY,KAAK;IAC1D,OAAO;QACL;QACA,OAAO,OAAO,IAAI,YAAY,KAAK,GAAG,eAAe,YAAY,IAAI,EAAE,YAAY,KAAK,EAAE,OAAO;QACjG,KAAK,aAAa;QAClB,MAAM,cAAc;IACtB;AACF;AACA,MAAM,cAAc,CAAC,aAAa,OAAO;IACvC,IAAI,EACF,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACN,GAAG;IACJ,OAAO;QACL;QACA;QACA,QAAQ,gBAAgB,KAAK,YAAY,MAAM,EAAE;QACjD,KAAK,aAAa;IACpB;AACF;AACA,MAAM,kBAAkB;IACtB,OAAO,YAAY,UAAU,MAAM,IAAI,IAAI,YAAY,SAAS,CAAC,EAAE,EAAE,cAAc,YAAY,UAAU,MAAM,IAAI,IAAI,YAAY,SAAS,CAAC,EAAE;AACjJ;AACA,MAAM,kBAAkB;IACtB,OAAO,YAAY,UAAU,MAAM,IAAI,IAAI,YAAY,SAAS,CAAC,EAAE,EAAE,cAAc,YAAY,UAAU,MAAM,IAAI,IAAI,YAAY,SAAS,CAAC,EAAE;AACjJ;AACA,MAAM,kBAAkB;IACtB,OAAO,YAAY,UAAU,MAAM,IAAI,IAAI,YAAY,SAAS,CAAC,EAAE,EAAE,cAAc,YAAY,UAAU,MAAM,IAAI,IAAI,YAAY,SAAS,CAAC,EAAE;AACjJ;AACA,MAAM,kBAAkB;IACtB,OAAO,YAAY,UAAU,MAAM,IAAI,IAAI,YAAY,SAAS,CAAC,EAAE,EAAE,cAAc,YAAY,UAAU,MAAM,IAAI,IAAI,YAAY,SAAS,CAAC,EAAE;AACjJ;AACA,MAAM,0BAA0B;IAC9B,GAAG;IACH,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,GAAG;IACH,IAAI;AACN;AAEA;;CAEC,GACD,SAAS,sBAAsB,UAAU,oBAAoB,GAArB,EAAyB,YAAY,YAAY,GAAb,EAAiB,QAAQ,YAAY,GAAb,EAAiB,eAAe,UAAU,GAAX,EAAe,YAAY;IAC5J,MAAM,iBAAiB,uBAAuB,CAAC,UAAU;IACzD,gEAAgE;IAChE,IAAI,CAAC,gBAAgB,OAAO;IAC5B,OAAO,eAAe,aAAa;QACjC,GAAG,WAAW;QACd,GAAG,OAAO;IACZ,GAAG;AACL;AACA,SAAS,aAAa,MAAM,KAAK,GAAN,EAAU,UAAU;IAC7C,IAAI,EACF,GAAG,EACH,IAAI,EACJ,KAAK,EACL,MAAM,EACP,CAAC,YAAY,MAAK,MAAM,YAAY;IACrC,iCAAiC;IACjC,MAAM,YAAY,CAAC,UAAU,EAAE,KAAK,GAAG,EAAE,IAAI,GAAG,CAAC;IACjD,OAAO;QACL,WAAW;QACX,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,YAAY;QACZ,OAAO,GAAG,MAAM,EAAE,CAAC;QACnB,QAAQ,GAAG,OAAO,EAAE,CAAC;QACrB,UAAU;IACZ;AACF;AACA,SAAS,WAAW,MAAM,KAAK,GAAN,EAAU,UAAU;IAC3C,IAAI,EACF,GAAG,EACH,IAAI,EACJ,KAAK,EACL,MAAM,EACP,CAAC,YAAY,MAAK,MAAM,YAAY;IACrC,OAAO;QACL,KAAK,GAAG,IAAI,EAAE,CAAC;QACf,MAAM,GAAG,KAAK,EAAE,CAAC;QACjB,OAAO,GAAG,MAAM,EAAE,CAAC;QACnB,QAAQ,GAAG,OAAO,EAAE,CAAC;QACrB,UAAU;IACZ;AACF;AAEA;;;;;CAKC,GACD,SAAS,gBAAgB,OAAO,UAAU,GAAX,EAAe,YAAY,eAAe,GAAhB,EAAoB,UAAU;IACrF,IAAI,gBAAgB,cAAc,OAAO,wBAAwB;IACjE,IAAI,gBAAgB,YAAY,OAAO,wBAAwB;SAAa,OAAO;AACrF;AAEA;;;;CAIC,GACD,SAAS,wBAAwB,OAAO,UAAU,GAAX,EAAe,UAAU;IAC9D,wCAAwC;IACxC,OAAO,OAAO,KAAK,CAAC,GAAG,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;QACxC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE;YACzC,OAAO;QACT,OAAO,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE;YACrC,sEAAsE;YACtE,OAAO;QACT;QACA,OAAO,CAAC;IACV;AACF;AAEA;;;;CAIC,GACD,SAAS,wBAAwB,OAAO,UAAU,GAAX,EAAe,UAAU;IAC9D,OAAO,OAAO,KAAK,CAAC,GAAG,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;QACxC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE;YACzC,OAAO;QACT;QACA,OAAO,CAAC;IACV;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,8BAA8B,cAAc,UAAU,GAAX,EAAe,SAAS,iBAAiB,GAAlB,EAAsB,KAAK,UAAU,GAAX,EAAe,YAAY,eAAe,GAAhB,EAAoB,aAAa,YAAY,GAAb,EAAiB,UAAU;IACxL,gBAAgB,iBAAiB,EAAE;IAEnC,sCAAsC;IACtC,MAAM,OAAO,gBAAgB,MAAK,EAAE;IACpC,OAAO,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,qBAAqB;QACpE,sBAAsB;QACtB,IAAI,OAAO,OAAO,MAAM;QACxB,MAAM,SAAS,cAAc,eAAe,OAAO,MAAM,GAAG;QAC5D,MAAM,IAAI,MAAM,KAAK,CAAC,YAAY;QAClC,yEAAyE;QACzE,2EAA2E;QAC3E,IAAI,UAAU,KAAK,MAAM;YACvB,OAAO,IAAI,CAAC,gBAAgB;QAC9B,OAAO;YACL,mDAAmD;YACnD,IAAI,GAAG;gBACL,wCAAmB;oBACjB,eAAe;wBAAC;qBAAE,EAAE;gBACtB;gBACA,wCAAwC;gBACxC,OAAO,IAAI,CAAC,gBAAgB;oBAC1B,GAAG,CAAC;oBACJ,GAAG,MAAM,GAAG;gBACd;YACF,OAAO;gBACL,uDAAuD;gBACvD,wCAAwC;gBACxC,OAAO,IAAI,CAAC,gBAAgB;oBAC1B,GAAG;oBACH,GAAG;oBACH,GAAG;oBACH,GAAG,OAAO;oBACV,GAAG,OAAO,MAAM,GAAG;gBACrB;YACF;QACF;IACF;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,cAAc,QAAQ;QAC5C,MAAM;IACR;IACA,OAAO,eAAe,kBAAkB,QAAQ,iBAAiB,aAAa;AAChF;AAEA;;;;;;CAMC,GACD,SAAS,eAAe,OAAO,UAAU,GAAX,EAAe,QAAQ;IACnD,IAAI,YAAY,UAAU,MAAK,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACnG,MAAM,WAAW;QAAC;QAAK;QAAK;QAAK;KAAI;IACrC,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,MAAM,IAAI,MAAM,cAAc;IAC1D,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,MAAM,OAAO,MAAM,CAAC,EAAE;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACxC,MAAM,MAAM,QAAQ,CAAC,EAAE;YACvB,MAAM,QAAQ,IAAI,CAAC,IAAI;YACvB,IAAI,OAAO,UAAU,YAAY,OAAO,KAAK,CAAC,QAAQ;gBACpD,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,6BAA6B,EAAE,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,CAAC;YACvH;QACF;QACA,IAAI,OAAO,KAAK,CAAC,KAAK,eAAe,OAAO,KAAK,CAAC,KAAK,UAAU;YAC/D,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,YAAY,CAAC,EAAE,EAAE,gCAAgC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;QACpH;IACF;AACF;AAEA,4CAA4C;AAC5C,SAAS,YAAY,MAAM,2DAA2D,GAA5D,EAAgE,eAAe;IACvG,MAAM,EACJ,eAAe,EACf,WAAW,EACZ,GAAG,SAAS,CAAC;IACd,OAAO,oBAAoB,QAAQ,OAAO;AAC5C;AACA,SAAS;IACP,wCAAY;;AAGd;AACA,MAAM,OAAO,KAAO;AACpB,QAAQ,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/react-grid-layout/build/calculateUtils.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.calcGridColWidth = calcGridColWidth;\nexports.calcGridItemPosition = calcGridItemPosition;\nexports.calcGridItemWHPx = calcGridItemWHPx;\nexports.calcWH = calcWH;\nexports.calcXY = calcXY;\nexports.clamp = clamp;\n/*:: import type { Position } from \"./utils\";*/\n/*:: export type PositionParams = {\n  margin: [number, number],\n  containerPadding: [number, number],\n  containerWidth: number,\n  cols: number,\n  rowHeight: number,\n  maxRows: number\n};*/\n// Helper for generating column width\nfunction calcGridColWidth(positionParams /*: PositionParams*/) /*: number*/{\n  const {\n    margin,\n    containerPadding,\n    containerWidth,\n    cols\n  } = positionParams;\n  return (containerWidth - margin[0] * (cols - 1) - containerPadding[0] * 2) / cols;\n}\n\n// This can either be called:\n// calcGridItemWHPx(w, colWidth, margin[0])\n// or\n// calcGridItemWHPx(h, rowHeight, margin[1])\nfunction calcGridItemWHPx(gridUnits /*: number*/, colOrRowSize /*: number*/, marginPx /*: number*/) /*: number*/{\n  // 0 * Infinity === NaN, which causes problems with resize contraints\n  if (!Number.isFinite(gridUnits)) return gridUnits;\n  return Math.round(colOrRowSize * gridUnits + Math.max(0, gridUnits - 1) * marginPx);\n}\n\n/**\n * Return position on the page given an x, y, w, h.\n * left, top, width, height are all in pixels.\n * @param  {PositionParams} positionParams  Parameters of grid needed for coordinates calculations.\n * @param  {Number}  x                      X coordinate in grid units.\n * @param  {Number}  y                      Y coordinate in grid units.\n * @param  {Number}  w                      W coordinate in grid units.\n * @param  {Number}  h                      H coordinate in grid units.\n * @return {Position}                       Object containing coords.\n */\nfunction calcGridItemPosition(positionParams /*: PositionParams*/, x /*: number*/, y /*: number*/, w /*: number*/, h /*: number*/, state /*: ?Object*/) /*: Position*/{\n  const {\n    margin,\n    containerPadding,\n    rowHeight\n  } = positionParams;\n  const colWidth = calcGridColWidth(positionParams);\n  const out = {};\n\n  // If resizing, use the exact width and height as returned from resizing callbacks.\n  if (state && state.resizing) {\n    out.width = Math.round(state.resizing.width);\n    out.height = Math.round(state.resizing.height);\n  }\n  // Otherwise, calculate from grid units.\n  else {\n    out.width = calcGridItemWHPx(w, colWidth, margin[0]);\n    out.height = calcGridItemWHPx(h, rowHeight, margin[1]);\n  }\n\n  // If dragging, use the exact width and height as returned from dragging callbacks.\n  if (state && state.dragging) {\n    out.top = Math.round(state.dragging.top);\n    out.left = Math.round(state.dragging.left);\n  } else if (state && state.resizing && typeof state.resizing.top === \"number\" && typeof state.resizing.left === \"number\") {\n    out.top = Math.round(state.resizing.top);\n    out.left = Math.round(state.resizing.left);\n  }\n  // Otherwise, calculate from grid units.\n  else {\n    out.top = Math.round((rowHeight + margin[1]) * y + containerPadding[1]);\n    out.left = Math.round((colWidth + margin[0]) * x + containerPadding[0]);\n  }\n  return out;\n}\n\n/**\n * Translate x and y coordinates from pixels to grid units.\n * @param  {PositionParams} positionParams  Parameters of grid needed for coordinates calculations.\n * @param  {Number} top                     Top position (relative to parent) in pixels.\n * @param  {Number} left                    Left position (relative to parent) in pixels.\n * @param  {Number} w                       W coordinate in grid units.\n * @param  {Number} h                       H coordinate in grid units.\n * @return {Object}                         x and y in grid units.\n */\nfunction calcXY(positionParams /*: PositionParams*/, top /*: number*/, left /*: number*/, w /*: number*/, h /*: number*/) /*: { x: number, y: number }*/{\n  const {\n    margin,\n    containerPadding,\n    cols,\n    rowHeight,\n    maxRows\n  } = positionParams;\n  const colWidth = calcGridColWidth(positionParams);\n\n  // left = containerPaddingX + x * (colWidth + marginX)\n  // x * (colWidth + marginX) = left - containerPaddingX\n  // x = (left - containerPaddingX) / (colWidth + marginX)\n  let x = Math.round((left - containerPadding[0]) / (colWidth + margin[0]));\n  let y = Math.round((top - containerPadding[1]) / (rowHeight + margin[1]));\n\n  // Capping\n  x = clamp(x, 0, cols - w);\n  y = clamp(y, 0, maxRows - h);\n  return {\n    x,\n    y\n  };\n}\n\n/**\n * Given a height and width in pixel values, calculate grid units.\n * @param  {PositionParams} positionParams  Parameters of grid needed for coordinates calcluations.\n * @param  {Number} height                  Height in pixels.\n * @param  {Number} width                   Width in pixels.\n * @param  {Number} x                       X coordinate in grid units.\n * @param  {Number} y                       Y coordinate in grid units.\n * @param {String} handle Resize Handle.\n * @return {Object}                         w, h as grid units.\n */\nfunction calcWH(positionParams /*: PositionParams*/, width /*: number*/, height /*: number*/, x /*: number*/, y /*: number*/, handle /*: string*/) /*: { w: number, h: number }*/{\n  const {\n    margin,\n    maxRows,\n    cols,\n    rowHeight\n  } = positionParams;\n  const colWidth = calcGridColWidth(positionParams);\n\n  // width = colWidth * w - (margin * (w - 1))\n  // ...\n  // w = (width + margin) / (colWidth + margin)\n  let w = Math.round((width + margin[0]) / (colWidth + margin[0]));\n  let h = Math.round((height + margin[1]) / (rowHeight + margin[1]));\n\n  // Capping\n  let _w = clamp(w, 0, cols - x);\n  let _h = clamp(h, 0, maxRows - y);\n  if ([\"sw\", \"w\", \"nw\"].indexOf(handle) !== -1) {\n    _w = clamp(w, 0, cols);\n  }\n  if ([\"nw\", \"n\", \"ne\"].indexOf(handle) !== -1) {\n    _h = clamp(h, 0, maxRows);\n  }\n  return {\n    w: _w,\n    h: _h\n  };\n}\n\n// Similar to _.clamp\nfunction clamp(num /*: number*/, lowerBound /*: number*/, upperBound /*: number*/) /*: number*/{\n  return Math.max(Math.min(num, upperBound), lowerBound);\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,oBAAoB,GAAG;AAC/B,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,MAAM,GAAG;AACjB,QAAQ,MAAM,GAAG;AACjB,QAAQ,KAAK,GAAG;AAChB,6CAA6C,GAC7C;;;;;;;EAOE,GACF,qCAAqC;AACrC,SAAS,iBAAiB,eAAe,kBAAkB,GAAnB,EAAuB,UAAU;IACvE,MAAM,EACJ,MAAM,EACN,gBAAgB,EAChB,cAAc,EACd,IAAI,EACL,GAAG;IACJ,OAAO,CAAC,iBAAiB,MAAM,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,EAAE,GAAG,CAAC,IAAI;AAC/E;AAEA,6BAA6B;AAC7B,2CAA2C;AAC3C,KAAK;AACL,4CAA4C;AAC5C,SAAS,iBAAiB,UAAU,UAAU,GAAX,EAAe,aAAa,UAAU,GAAX,EAAe,SAAS,UAAU,GAAX,EAAe,UAAU;IAC5G,qEAAqE;IACrE,IAAI,CAAC,OAAO,QAAQ,CAAC,YAAY,OAAO;IACxC,OAAO,KAAK,KAAK,CAAC,eAAe,YAAY,KAAK,GAAG,CAAC,GAAG,YAAY,KAAK;AAC5E;AAEA;;;;;;;;;CASC,GACD,SAAS,qBAAqB,eAAe,kBAAkB,GAAnB,EAAuB,EAAE,UAAU,GAAX,EAAe,EAAE,UAAU,GAAX,EAAe,EAAE,UAAU,GAAX,EAAe,EAAE,UAAU,GAAX,EAAe,MAAM,WAAW,GAAZ,EAAgB,YAAY;IAClK,MAAM,EACJ,MAAM,EACN,gBAAgB,EAChB,SAAS,EACV,GAAG;IACJ,MAAM,WAAW,iBAAiB;IAClC,MAAM,MAAM,CAAC;IAEb,mFAAmF;IACnF,IAAI,SAAS,MAAM,QAAQ,EAAE;QAC3B,IAAI,KAAK,GAAG,KAAK,KAAK,CAAC,MAAM,QAAQ,CAAC,KAAK;QAC3C,IAAI,MAAM,GAAG,KAAK,KAAK,CAAC,MAAM,QAAQ,CAAC,MAAM;IAC/C,OAEK;QACH,IAAI,KAAK,GAAG,iBAAiB,GAAG,UAAU,MAAM,CAAC,EAAE;QACnD,IAAI,MAAM,GAAG,iBAAiB,GAAG,WAAW,MAAM,CAAC,EAAE;IACvD;IAEA,mFAAmF;IACnF,IAAI,SAAS,MAAM,QAAQ,EAAE;QAC3B,IAAI,GAAG,GAAG,KAAK,KAAK,CAAC,MAAM,QAAQ,CAAC,GAAG;QACvC,IAAI,IAAI,GAAG,KAAK,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI;IAC3C,OAAO,IAAI,SAAS,MAAM,QAAQ,IAAI,OAAO,MAAM,QAAQ,CAAC,GAAG,KAAK,YAAY,OAAO,MAAM,QAAQ,CAAC,IAAI,KAAK,UAAU;QACvH,IAAI,GAAG,GAAG,KAAK,KAAK,CAAC,MAAM,QAAQ,CAAC,GAAG;QACvC,IAAI,IAAI,GAAG,KAAK,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI;IAC3C,OAEK;QACH,IAAI,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC,YAAY,MAAM,CAAC,EAAE,IAAI,IAAI,gBAAgB,CAAC,EAAE;QACtE,IAAI,IAAI,GAAG,KAAK,KAAK,CAAC,CAAC,WAAW,MAAM,CAAC,EAAE,IAAI,IAAI,gBAAgB,CAAC,EAAE;IACxE;IACA,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,SAAS,OAAO,eAAe,kBAAkB,GAAnB,EAAuB,IAAI,UAAU,GAAX,EAAe,KAAK,UAAU,GAAX,EAAe,EAAE,UAAU,GAAX,EAAe,EAAE,UAAU,GAAX,EAAe,4BAA4B;IACpJ,MAAM,EACJ,MAAM,EACN,gBAAgB,EAChB,IAAI,EACJ,SAAS,EACT,OAAO,EACR,GAAG;IACJ,MAAM,WAAW,iBAAiB;IAElC,sDAAsD;IACtD,sDAAsD;IACtD,wDAAwD;IACxD,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,OAAO,gBAAgB,CAAC,EAAE,IAAI,CAAC,WAAW,MAAM,CAAC,EAAE;IACvE,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,MAAM,gBAAgB,CAAC,EAAE,IAAI,CAAC,YAAY,MAAM,CAAC,EAAE;IAEvE,UAAU;IACV,IAAI,MAAM,GAAG,GAAG,OAAO;IACvB,IAAI,MAAM,GAAG,GAAG,UAAU;IAC1B,OAAO;QACL;QACA;IACF;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,OAAO,eAAe,kBAAkB,GAAnB,EAAuB,MAAM,UAAU,GAAX,EAAe,OAAO,UAAU,GAAX,EAAe,EAAE,UAAU,GAAX,EAAe,EAAE,UAAU,GAAX,EAAe,OAAO,UAAU,GAAX,EAAe,4BAA4B;IAC7K,MAAM,EACJ,MAAM,EACN,OAAO,EACP,IAAI,EACJ,SAAS,EACV,GAAG;IACJ,MAAM,WAAW,iBAAiB;IAElC,4CAA4C;IAC5C,MAAM;IACN,6CAA6C;IAC7C,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,QAAQ,MAAM,CAAC,EAAE,IAAI,CAAC,WAAW,MAAM,CAAC,EAAE;IAC9D,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,IAAI,CAAC,YAAY,MAAM,CAAC,EAAE;IAEhE,UAAU;IACV,IAAI,KAAK,MAAM,GAAG,GAAG,OAAO;IAC5B,IAAI,KAAK,MAAM,GAAG,GAAG,UAAU;IAC/B,IAAI;QAAC;QAAM;QAAK;KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG;QAC5C,KAAK,MAAM,GAAG,GAAG;IACnB;IACA,IAAI;QAAC;QAAM;QAAK;KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG;QAC5C,KAAK,MAAM,GAAG,GAAG;IACnB;IACA,OAAO;QACL,GAAG;QACH,GAAG;IACL;AACF;AAEA,qBAAqB;AACrB,SAAS,MAAM,IAAI,UAAU,GAAX,EAAe,WAAW,UAAU,GAAX,EAAe,WAAW,UAAU,GAAX,EAAe,UAAU;IAC3F,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,aAAa;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/react-grid-layout/build/ReactGridLayoutPropTypes.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resizeHandleType = exports.resizeHandleAxesType = exports.default = void 0;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n/*:: import type {\n  Ref,\n  ChildrenArray as ReactChildrenArray,\n  Element as ReactElement\n} from \"react\";*/\n/*:: import type {\n  DragOverEvent,\n  EventCallback,\n  CompactType,\n  Layout,\n  LayoutItem,\n  ResizeHandleAxis\n} from \"./utils\";*/\n/*:: export type ReactRef<T: HTMLElement> = {|\n  +current: T | null\n|};*/\n// util\n/*:: export type ResizeHandle =\n  | ReactElement<any>\n  | ((\n      resizeHandleAxis: ResizeHandleAxis,\n      ref: ReactRef<HTMLElement>\n    ) => ReactElement<any>);*/\n// Defines which resize handles should be rendered (default: 'se')\n// Allows for any combination of:\n// 's' - South handle (bottom-center)\n// 'w' - West handle (left-center)\n// 'e' - East handle (right-center)\n// 'n' - North handle (top-center)\n// 'sw' - Southwest handle (bottom-left)\n// 'nw' - Northwest handle (top-left)\n// 'se' - Southeast handle (bottom-right)\n// 'ne' - Northeast handle (top-right)\nconst resizeHandleAxesType /*: ReactPropsChainableTypeChecker*/ = exports.resizeHandleAxesType = _propTypes.default.arrayOf(_propTypes.default.oneOf([\"s\", \"w\", \"e\", \"n\", \"sw\", \"nw\", \"se\", \"ne\"]));\n// Custom component for resize handles\nconst resizeHandleType /*: ReactPropsChainableTypeChecker*/ = exports.resizeHandleType = _propTypes.default.oneOfType([_propTypes.default.node, _propTypes.default.func]);\n/*:: export type Props = {|\n  className: string,\n  style: Object,\n  width: number,\n  autoSize: boolean,\n  cols: number,\n  draggableCancel: string,\n  draggableHandle: string,\n  verticalCompact: boolean,\n  compactType: CompactType,\n  layout: Layout,\n  margin: [number, number],\n  containerPadding: ?[number, number],\n  rowHeight: number,\n  maxRows: number,\n  isBounded: boolean,\n  isDraggable: boolean,\n  isResizable: boolean,\n  isDroppable: boolean,\n  preventCollision: boolean,\n  useCSSTransforms: boolean,\n  transformScale: number,\n  droppingItem: $Shape<LayoutItem>,\n  resizeHandles: ResizeHandleAxis[],\n  resizeHandle?: ResizeHandle,\n  allowOverlap: boolean,\n\n  // Callbacks\n  onLayoutChange: Layout => void,\n  onDrag: EventCallback,\n  onDragStart: EventCallback,\n  onDragStop: EventCallback,\n  onResize: EventCallback,\n  onResizeStart: EventCallback,\n  onResizeStop: EventCallback,\n  onDropDragOver: (e: DragOverEvent) => ?({| w?: number, h?: number |} | false),\n  onDrop: (layout: Layout, item: ?LayoutItem, e: Event) => void,\n  children: ReactChildrenArray<ReactElement<any>>,\n  innerRef?: Ref<\"div\">\n|};*/\n/*:: export type DefaultProps = $Diff<\n  Props,\n  {\n    children: ReactChildrenArray<ReactElement<any>>,\n    width: number\n  }\n>;*/\nvar _default = exports.default = {\n  //\n  // Basic props\n  //\n  className: _propTypes.default.string,\n  style: _propTypes.default.object,\n  // This can be set explicitly. If it is not set, it will automatically\n  // be set to the container width. Note that resizes will *not* cause this to adjust.\n  // If you need that behavior, use WidthProvider.\n  width: _propTypes.default.number,\n  // If true, the container height swells and contracts to fit contents\n  autoSize: _propTypes.default.bool,\n  // # of cols.\n  cols: _propTypes.default.number,\n  // A selector that will not be draggable.\n  draggableCancel: _propTypes.default.string,\n  // A selector for the draggable handler\n  draggableHandle: _propTypes.default.string,\n  // Deprecated\n  verticalCompact: function (props /*: Props*/) {\n    if (props.verticalCompact === false && process.env.NODE_ENV !== \"production\") {\n      console.warn(\n      // eslint-disable-line no-console\n      \"`verticalCompact` on <ReactGridLayout> is deprecated and will be removed soon. \" + 'Use `compactType`: \"horizontal\" | \"vertical\" | null.');\n    }\n  },\n  // Choose vertical or hotizontal compaction\n  compactType: (_propTypes.default.oneOf([\"vertical\", \"horizontal\"]) /*: ReactPropsChainableTypeChecker*/),\n  // layout is an array of object with the format:\n  // {x: Number, y: Number, w: Number, h: Number, i: String}\n  layout: function (props /*: Props*/) {\n    var layout = props.layout;\n    // I hope you're setting the data-grid property on the grid items\n    if (layout === undefined) return;\n    require(\"./utils\").validateLayout(layout, \"layout\");\n  },\n  //\n  // Grid Dimensions\n  //\n\n  // Margin between items [x, y] in px\n  margin: (_propTypes.default.arrayOf(_propTypes.default.number) /*: ReactPropsChainableTypeChecker*/),\n  // Padding inside the container [x, y] in px\n  containerPadding: (_propTypes.default.arrayOf(_propTypes.default.number) /*: ReactPropsChainableTypeChecker*/),\n  // Rows have a static height, but you can change this based on breakpoints if you like\n  rowHeight: _propTypes.default.number,\n  // Default Infinity, but you can specify a max here if you like.\n  // Note that this isn't fully fleshed out and won't error if you specify a layout that\n  // extends beyond the row capacity. It will, however, not allow users to drag/resize\n  // an item past the barrier. They can push items beyond the barrier, though.\n  // Intentionally not documented for this reason.\n  maxRows: _propTypes.default.number,\n  //\n  // Flags\n  //\n  isBounded: _propTypes.default.bool,\n  isDraggable: _propTypes.default.bool,\n  isResizable: _propTypes.default.bool,\n  // If true, grid can be placed one over the other.\n  allowOverlap: _propTypes.default.bool,\n  // If true, grid items won't change position when being dragged over.\n  preventCollision: _propTypes.default.bool,\n  // Use CSS transforms instead of top/left\n  useCSSTransforms: _propTypes.default.bool,\n  // parent layout transform scale\n  transformScale: _propTypes.default.number,\n  // If true, an external element can trigger onDrop callback with a specific grid position as a parameter\n  isDroppable: _propTypes.default.bool,\n  // Resize handle options\n  resizeHandles: resizeHandleAxesType,\n  resizeHandle: resizeHandleType,\n  //\n  // Callbacks\n  //\n\n  // Callback so you can save the layout. Calls after each drag & resize stops.\n  onLayoutChange: _propTypes.default.func,\n  // Calls when drag starts. Callback is of the signature (layout, oldItem, newItem, placeholder, e, ?node).\n  // All callbacks below have the same signature. 'start' and 'stop' callbacks omit the 'placeholder'.\n  onDragStart: _propTypes.default.func,\n  // Calls on each drag movement.\n  onDrag: _propTypes.default.func,\n  // Calls when drag is complete.\n  onDragStop: _propTypes.default.func,\n  //Calls when resize starts.\n  onResizeStart: _propTypes.default.func,\n  // Calls when resize movement happens.\n  onResize: _propTypes.default.func,\n  // Calls when resize is complete.\n  onResizeStop: _propTypes.default.func,\n  // Calls when some element is dropped.\n  onDrop: _propTypes.default.func,\n  //\n  // Other validations\n  //\n\n  droppingItem: (_propTypes.default.shape({\n    i: _propTypes.default.string.isRequired,\n    w: _propTypes.default.number.isRequired,\n    h: _propTypes.default.number.isRequired\n  }) /*: ReactPropsChainableTypeChecker*/),\n  // Children must not have duplicate keys.\n  children: function (props /*: Props*/, propName /*: string*/) {\n    const children = props[propName];\n\n    // Check children keys for duplicates. Throw if found.\n    const keys = {};\n    _react.default.Children.forEach(children, function (child) {\n      if (child?.key == null) return;\n      if (keys[child.key]) {\n        throw new Error('Duplicate child key \"' + child.key + '\" found! This will cause problems in ReactGridLayout.');\n      }\n      keys[child.key] = true;\n    });\n  },\n  // Optional ref for getting a reference for the wrapping div.\n  innerRef: _propTypes.default.any\n};"], "names": [], "mappings": "AAgH2C;AAhH3C;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,gBAAgB,GAAG,QAAQ,oBAAoB,GAAG,QAAQ,OAAO,GAAG,KAAK;AACjF,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AAAG;AAC9F;;;;eAIe,GACf;;;;;;;iBAOiB,GACjB;;GAEG,GACH,OAAO;AACP;;;;;4BAK4B,GAC5B,kEAAkE;AAClE,iCAAiC;AACjC,qCAAqC;AACrC,kCAAkC;AAClC,mCAAmC;AACnC,kCAAkC;AAClC,wCAAwC;AACxC,qCAAqC;AACrC,yCAAyC;AACzC,sCAAsC;AACtC,MAAM,qBAAqB,kCAAkC,MAAK,QAAQ,oBAAoB,GAAG,WAAW,OAAO,CAAC,OAAO,CAAC,WAAW,OAAO,CAAC,KAAK,CAAC;IAAC;IAAK;IAAK;IAAK;IAAK;IAAM;IAAM;IAAM;CAAK;AACjM,sCAAsC;AACtC,MAAM,iBAAiB,kCAAkC,MAAK,QAAQ,gBAAgB,GAAG,WAAW,OAAO,CAAC,SAAS,CAAC;IAAC,WAAW,OAAO,CAAC,IAAI;IAAE,WAAW,OAAO,CAAC,IAAI;CAAC;AACxK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuCG,GACH;;;;;;EAME,GACF,IAAI,WAAW,QAAQ,OAAO,GAAG;IAC/B,EAAE;IACF,cAAc;IACd,EAAE;IACF,WAAW,WAAW,OAAO,CAAC,MAAM;IACpC,OAAO,WAAW,OAAO,CAAC,MAAM;IAChC,sEAAsE;IACtE,oFAAoF;IACpF,gDAAgD;IAChD,OAAO,WAAW,OAAO,CAAC,MAAM;IAChC,qEAAqE;IACrE,UAAU,WAAW,OAAO,CAAC,IAAI;IACjC,aAAa;IACb,MAAM,WAAW,OAAO,CAAC,MAAM;IAC/B,yCAAyC;IACzC,iBAAiB,WAAW,OAAO,CAAC,MAAM;IAC1C,uCAAuC;IACvC,iBAAiB,WAAW,OAAO,CAAC,MAAM;IAC1C,aAAa;IACb,iBAAiB,SAAU,MAAM,SAAS,GAAV;QAC9B,IAAI,MAAM,eAAe,KAAK,SAAS,oDAAyB,cAAc;YAC5E,QAAQ,IAAI,CACZ,iCAAiC;YACjC,oFAAoF;QACtF;IACF;IACA,2CAA2C;IAC3C,aAAc,WAAW,OAAO,CAAC,KAAK,CAAC;QAAC;QAAY;KAAa;IACjE,gDAAgD;IAChD,0DAA0D;IAC1D,QAAQ,SAAU,MAAM,SAAS,GAAV;QACrB,IAAI,SAAS,MAAM,MAAM;QACzB,iEAAiE;QACjE,IAAI,WAAW,WAAW;QAC1B,6GAAmB,cAAc,CAAC,QAAQ;IAC5C;IACA,EAAE;IACF,kBAAkB;IAClB,EAAE;IAEF,oCAAoC;IACpC,QAAS,WAAW,OAAO,CAAC,OAAO,CAAC,WAAW,OAAO,CAAC,MAAM;IAC7D,4CAA4C;IAC5C,kBAAmB,WAAW,OAAO,CAAC,OAAO,CAAC,WAAW,OAAO,CAAC,MAAM;IACvE,sFAAsF;IACtF,WAAW,WAAW,OAAO,CAAC,MAAM;IACpC,gEAAgE;IAChE,sFAAsF;IACtF,oFAAoF;IACpF,4EAA4E;IAC5E,gDAAgD;IAChD,SAAS,WAAW,OAAO,CAAC,MAAM;IAClC,EAAE;IACF,QAAQ;IACR,EAAE;IACF,WAAW,WAAW,OAAO,CAAC,IAAI;IAClC,aAAa,WAAW,OAAO,CAAC,IAAI;IACpC,aAAa,WAAW,OAAO,CAAC,IAAI;IACpC,kDAAkD;IAClD,cAAc,WAAW,OAAO,CAAC,IAAI;IACrC,qEAAqE;IACrE,kBAAkB,WAAW,OAAO,CAAC,IAAI;IACzC,yCAAyC;IACzC,kBAAkB,WAAW,OAAO,CAAC,IAAI;IACzC,gCAAgC;IAChC,gBAAgB,WAAW,OAAO,CAAC,MAAM;IACzC,wGAAwG;IACxG,aAAa,WAAW,OAAO,CAAC,IAAI;IACpC,wBAAwB;IACxB,eAAe;IACf,cAAc;IACd,EAAE;IACF,YAAY;IACZ,EAAE;IAEF,6EAA6E;IAC7E,gBAAgB,WAAW,OAAO,CAAC,IAAI;IACvC,0GAA0G;IAC1G,oGAAoG;IACpG,aAAa,WAAW,OAAO,CAAC,IAAI;IACpC,+BAA+B;IAC/B,QAAQ,WAAW,OAAO,CAAC,IAAI;IAC/B,+BAA+B;IAC/B,YAAY,WAAW,OAAO,CAAC,IAAI;IACnC,2BAA2B;IAC3B,eAAe,WAAW,OAAO,CAAC,IAAI;IACtC,sCAAsC;IACtC,UAAU,WAAW,OAAO,CAAC,IAAI;IACjC,iCAAiC;IACjC,cAAc,WAAW,OAAO,CAAC,IAAI;IACrC,sCAAsC;IACtC,QAAQ,WAAW,OAAO,CAAC,IAAI;IAC/B,EAAE;IACF,oBAAoB;IACpB,EAAE;IAEF,cAAe,WAAW,OAAO,CAAC,KAAK,CAAC;QACtC,GAAG,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;QACvC,GAAG,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;QACvC,GAAG,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IACzC;IACA,yCAAyC;IACzC,UAAU,SAAU,MAAM,SAAS,GAAV,EAAc,SAAS,UAAU,GAAX;QAC7C,MAAM,WAAW,KAAK,CAAC,SAAS;QAEhC,sDAAsD;QACtD,MAAM,OAAO,CAAC;QACd,OAAO,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,SAAU,KAAK;YACvD,IAAI,OAAO,OAAO,MAAM;YACxB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnB,MAAM,IAAI,MAAM,0BAA0B,MAAM,GAAG,GAAG;YACxD;YACA,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG;QACpB;IACF;IACA,6DAA6D;IAC7D,UAAU,WAAW,OAAO,CAAC,GAAG;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/react-grid-layout/build/GridItem.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _reactDom = require(\"react-dom\");\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _reactDraggable = require(\"react-draggable\");\nvar _reactResizable = require(\"react-resizable\");\nvar _utils = require(\"./utils\");\nvar _calculateUtils = require(\"./calculateUtils\");\nvar _ReactGridLayoutPropTypes = require(\"./ReactGridLayoutPropTypes\");\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/*:: import type { Element as ReactElement, Node as ReactNode } from \"react\";*/\n/*:: import type {\n  ReactDraggableCallbackData,\n  GridDragEvent,\n  GridResizeEvent,\n  DroppingPosition,\n  Position,\n  ResizeHandleAxis\n} from \"./utils\";*/\n/*:: import type { PositionParams } from \"./calculateUtils\";*/\n/*:: import type { ResizeHandle, ReactRef } from \"./ReactGridLayoutPropTypes\";*/\n/*:: type PartialPosition = { top: number, left: number };*/\n/*:: type GridItemCallback<Data: GridDragEvent | GridResizeEvent> = (\n  i: string,\n  w: number,\n  h: number,\n  Data\n) => void;*/\n/*:: type ResizeCallbackData = {\n  node: HTMLElement,\n  size: Position,\n  handle: ResizeHandleAxis\n};*/\n/*:: type GridItemResizeCallback = (\n  e: Event,\n  data: ResizeCallbackData,\n  position: Position\n) => void;*/\n/*:: type State = {\n  resizing: ?{ top: number, left: number, width: number, height: number },\n  dragging: ?{ top: number, left: number },\n  className: string\n};*/\n/*:: type Props = {\n  children: ReactElement<any>,\n  cols: number,\n  containerWidth: number,\n  margin: [number, number],\n  containerPadding: [number, number],\n  rowHeight: number,\n  maxRows: number,\n  isDraggable: boolean,\n  isResizable: boolean,\n  isBounded: boolean,\n  static?: boolean,\n  useCSSTransforms?: boolean,\n  usePercentages?: boolean,\n  transformScale: number,\n  droppingPosition?: DroppingPosition,\n\n  className: string,\n  style?: Object,\n  // Draggability\n  cancel: string,\n  handle: string,\n\n  x: number,\n  y: number,\n  w: number,\n  h: number,\n\n  minW: number,\n  maxW: number,\n  minH: number,\n  maxH: number,\n  i: string,\n\n  resizeHandles?: ResizeHandleAxis[],\n  resizeHandle?: ResizeHandle,\n\n  onDrag?: GridItemCallback<GridDragEvent>,\n  onDragStart?: GridItemCallback<GridDragEvent>,\n  onDragStop?: GridItemCallback<GridDragEvent>,\n  onResize?: GridItemCallback<GridResizeEvent>,\n  onResizeStart?: GridItemCallback<GridResizeEvent>,\n  onResizeStop?: GridItemCallback<GridResizeEvent>\n};*/\n/*:: type DefaultProps = {\n  className: string,\n  cancel: string,\n  handle: string,\n  minH: number,\n  minW: number,\n  maxH: number,\n  maxW: number,\n  transformScale: number\n};*/\n/**\n * An individual item within a ReactGridLayout.\n */\nclass GridItem extends _react.default.Component /*:: <Props, State>*/{\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      resizing: null,\n      dragging: null,\n      className: \"\"\n    });\n    _defineProperty(this, \"elementRef\", /*#__PURE__*/_react.default.createRef());\n    /**\n     * onDragStart event handler\n     * @param  {Event}  e             event data\n     * @param  {Object} callbackData  an object with node, delta and position information\n     */\n    _defineProperty(this, \"onDragStart\", (e, _ref) => {\n      let {\n        node\n      } = _ref;\n      const {\n        onDragStart,\n        transformScale\n      } = this.props;\n      if (!onDragStart) return;\n      const newPosition /*: PartialPosition*/ = {\n        top: 0,\n        left: 0\n      };\n\n      // TODO: this wont work on nested parents\n      const {\n        offsetParent\n      } = node;\n      if (!offsetParent) return;\n      const parentRect = offsetParent.getBoundingClientRect();\n      const clientRect = node.getBoundingClientRect();\n      const cLeft = clientRect.left / transformScale;\n      const pLeft = parentRect.left / transformScale;\n      const cTop = clientRect.top / transformScale;\n      const pTop = parentRect.top / transformScale;\n      newPosition.left = cLeft - pLeft + offsetParent.scrollLeft;\n      newPosition.top = cTop - pTop + offsetParent.scrollTop;\n      this.setState({\n        dragging: newPosition\n      });\n\n      // Call callback with this data\n      const {\n        x,\n        y\n      } = (0, _calculateUtils.calcXY)(this.getPositionParams(), newPosition.top, newPosition.left, this.props.w, this.props.h);\n      return onDragStart.call(this, this.props.i, x, y, {\n        e,\n        node,\n        newPosition\n      });\n    });\n    /**\n     * onDrag event handler\n     * @param  {Event}  e             event data\n     * @param  {Object} callbackData  an object with node, delta and position information\n     * @param  {boolean} dontFlush    if true, will not call flushSync\n     */\n    _defineProperty(this, \"onDrag\", (e, _ref2, dontFlush) => {\n      let {\n        node,\n        deltaX,\n        deltaY\n      } = _ref2;\n      const {\n        onDrag\n      } = this.props;\n      if (!onDrag) return;\n      if (!this.state.dragging) {\n        throw new Error(\"onDrag called before onDragStart.\");\n      }\n      let top = this.state.dragging.top + deltaY;\n      let left = this.state.dragging.left + deltaX;\n      const {\n        isBounded,\n        i,\n        w,\n        h,\n        containerWidth\n      } = this.props;\n      const positionParams = this.getPositionParams();\n\n      // Boundary calculations; keeps items within the grid\n      if (isBounded) {\n        const {\n          offsetParent\n        } = node;\n        if (offsetParent) {\n          const {\n            margin,\n            rowHeight,\n            containerPadding\n          } = this.props;\n          const bottomBoundary = offsetParent.clientHeight - (0, _calculateUtils.calcGridItemWHPx)(h, rowHeight, margin[1]);\n          top = (0, _calculateUtils.clamp)(top - containerPadding[1], 0, bottomBoundary);\n          const colWidth = (0, _calculateUtils.calcGridColWidth)(positionParams);\n          const rightBoundary = containerWidth - (0, _calculateUtils.calcGridItemWHPx)(w, colWidth, margin[0]);\n          left = (0, _calculateUtils.clamp)(left - containerPadding[0], 0, rightBoundary);\n        }\n      }\n      const newPosition /*: PartialPosition*/ = {\n        top,\n        left\n      };\n\n      // dontFlush is set if we're calling from inside\n      if (dontFlush) {\n        this.setState({\n          dragging: newPosition\n        });\n      } else {\n        (0, _reactDom.flushSync)(() => {\n          this.setState({\n            dragging: newPosition\n          });\n        });\n      }\n\n      // Call callback with this data\n      const {\n        x,\n        y\n      } = (0, _calculateUtils.calcXY)(positionParams, top, left, w, h);\n      return onDrag.call(this, i, x, y, {\n        e,\n        node,\n        newPosition\n      });\n    });\n    /**\n     * onDragStop event handler\n     * @param  {Event}  e             event data\n     * @param  {Object} callbackData  an object with node, delta and position information\n     */\n    _defineProperty(this, \"onDragStop\", (e, _ref3) => {\n      let {\n        node\n      } = _ref3;\n      const {\n        onDragStop\n      } = this.props;\n      if (!onDragStop) return;\n      if (!this.state.dragging) {\n        throw new Error(\"onDragEnd called before onDragStart.\");\n      }\n      const {\n        w,\n        h,\n        i\n      } = this.props;\n      const {\n        left,\n        top\n      } = this.state.dragging;\n      const newPosition /*: PartialPosition*/ = {\n        top,\n        left\n      };\n      this.setState({\n        dragging: null\n      });\n      const {\n        x,\n        y\n      } = (0, _calculateUtils.calcXY)(this.getPositionParams(), top, left, w, h);\n      return onDragStop.call(this, i, x, y, {\n        e,\n        node,\n        newPosition\n      });\n    });\n    /**\n     * onResizeStop event handler\n     * @param  {Event}  e             event data\n     * @param  {Object} callbackData  an object with node and size information\n     */\n    _defineProperty(this, \"onResizeStop\", (e, callbackData, position) => this.onResizeHandler(e, callbackData, position, \"onResizeStop\"));\n    // onResizeStart event handler\n    _defineProperty(this, \"onResizeStart\", (e, callbackData, position) => this.onResizeHandler(e, callbackData, position, \"onResizeStart\"));\n    // onResize event handler\n    _defineProperty(this, \"onResize\", (e, callbackData, position) => this.onResizeHandler(e, callbackData, position, \"onResize\"));\n  }\n  shouldComponentUpdate(nextProps /*: Props*/, nextState /*: State*/) /*: boolean*/{\n    // We can't deeply compare children. If the developer memoizes them, we can\n    // use this optimization.\n    if (this.props.children !== nextProps.children) return true;\n    if (this.props.droppingPosition !== nextProps.droppingPosition) return true;\n    // TODO memoize these calculations so they don't take so long?\n    const oldPosition = (0, _calculateUtils.calcGridItemPosition)(this.getPositionParams(this.props), this.props.x, this.props.y, this.props.w, this.props.h, this.state);\n    const newPosition = (0, _calculateUtils.calcGridItemPosition)(this.getPositionParams(nextProps), nextProps.x, nextProps.y, nextProps.w, nextProps.h, nextState);\n    return !(0, _utils.fastPositionEqual)(oldPosition, newPosition) || this.props.useCSSTransforms !== nextProps.useCSSTransforms;\n  }\n  componentDidMount() {\n    this.moveDroppingItem({});\n  }\n  componentDidUpdate(prevProps /*: Props*/) {\n    this.moveDroppingItem(prevProps);\n  }\n\n  // When a droppingPosition is present, this means we should fire a move event, as if we had moved\n  // this element by `x, y` pixels.\n  moveDroppingItem(prevProps /*: Props*/) {\n    const {\n      droppingPosition\n    } = this.props;\n    if (!droppingPosition) return;\n    const node = this.elementRef.current;\n    // Can't find DOM node (are we unmounted?)\n    if (!node) return;\n    const prevDroppingPosition = prevProps.droppingPosition || {\n      left: 0,\n      top: 0\n    };\n    const {\n      dragging\n    } = this.state;\n    const shouldDrag = dragging && droppingPosition.left !== prevDroppingPosition.left || droppingPosition.top !== prevDroppingPosition.top;\n    if (!dragging) {\n      this.onDragStart(droppingPosition.e, {\n        node,\n        deltaX: droppingPosition.left,\n        deltaY: droppingPosition.top\n      });\n    } else if (shouldDrag) {\n      const deltaX = droppingPosition.left - dragging.left;\n      const deltaY = droppingPosition.top - dragging.top;\n      this.onDrag(droppingPosition.e, {\n        node,\n        deltaX,\n        deltaY\n      }, true // dontFLush: avoid flushSync to temper warnings\n      );\n    }\n  }\n\n  getPositionParams() /*: PositionParams*/{\n    let props /*: Props*/ = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.props;\n    return {\n      cols: props.cols,\n      containerPadding: props.containerPadding,\n      containerWidth: props.containerWidth,\n      margin: props.margin,\n      maxRows: props.maxRows,\n      rowHeight: props.rowHeight\n    };\n  }\n\n  /**\n   * This is where we set the grid item's absolute placement. It gets a little tricky because we want to do it\n   * well when server rendering, and the only way to do that properly is to use percentage width/left because\n   * we don't know exactly what the browser viewport is.\n   * Unfortunately, CSS Transforms, which are great for performance, break in this instance because a percentage\n   * left is relative to the item itself, not its container! So we cannot use them on the server rendering pass.\n   *\n   * @param  {Object} pos Position object with width, height, left, top.\n   * @return {Object}     Style object.\n   */\n  createStyle(pos /*: Position*/) /*: { [key: string]: ?string }*/{\n    const {\n      usePercentages,\n      containerWidth,\n      useCSSTransforms\n    } = this.props;\n    let style;\n    // CSS Transforms support (default)\n    if (useCSSTransforms) {\n      style = (0, _utils.setTransform)(pos);\n    } else {\n      // top,left (slow)\n      style = (0, _utils.setTopLeft)(pos);\n\n      // This is used for server rendering.\n      if (usePercentages) {\n        style.left = (0, _utils.perc)(pos.left / containerWidth);\n        style.width = (0, _utils.perc)(pos.width / containerWidth);\n      }\n    }\n    return style;\n  }\n\n  /**\n   * Mix a Draggable instance into a child.\n   * @param  {Element} child    Child element.\n   * @return {Element}          Child wrapped in Draggable.\n   */\n  mixinDraggable(child /*: ReactElement<any>*/, isDraggable /*: boolean*/) /*: ReactElement<any>*/{\n    return /*#__PURE__*/_react.default.createElement(_reactDraggable.DraggableCore, {\n      disabled: !isDraggable,\n      onStart: this.onDragStart,\n      onDrag: this.onDrag,\n      onStop: this.onDragStop,\n      handle: this.props.handle,\n      cancel: \".react-resizable-handle\" + (this.props.cancel ? \",\" + this.props.cancel : \"\"),\n      scale: this.props.transformScale,\n      nodeRef: this.elementRef\n    }, child);\n  }\n\n  /**\n   * Utility function to setup callback handler definitions for\n   * similarily structured resize events.\n   */\n  curryResizeHandler(position /*: Position*/, handler /*: Function*/) /*: Function*/{\n    return (e /*: Event*/, data /*: ResizeCallbackData*/) => /*: Function*/handler(e, data, position);\n  }\n\n  /**\n   * Mix a Resizable instance into a child.\n   * @param  {Element} child    Child element.\n   * @param  {Object} position  Position object (pixel values)\n   * @return {Element}          Child wrapped in Resizable.\n   */\n  mixinResizable(child /*: ReactElement<any>*/, position /*: Position*/, isResizable /*: boolean*/) /*: ReactElement<any>*/{\n    const {\n      cols,\n      minW,\n      minH,\n      maxW,\n      maxH,\n      transformScale,\n      resizeHandles,\n      resizeHandle\n    } = this.props;\n    const positionParams = this.getPositionParams();\n\n    // This is the max possible width - doesn't go to infinity because of the width of the window\n    const maxWidth = (0, _calculateUtils.calcGridItemPosition)(positionParams, 0, 0, cols, 0).width;\n\n    // Calculate min/max constraints using our min & maxes\n    const mins = (0, _calculateUtils.calcGridItemPosition)(positionParams, 0, 0, minW, minH);\n    const maxes = (0, _calculateUtils.calcGridItemPosition)(positionParams, 0, 0, maxW, maxH);\n    const minConstraints = [mins.width, mins.height];\n    const maxConstraints = [Math.min(maxes.width, maxWidth), Math.min(maxes.height, Infinity)];\n    return /*#__PURE__*/_react.default.createElement(_reactResizable.Resizable\n    // These are opts for the resize handle itself\n    , {\n      draggableOpts: {\n        disabled: !isResizable\n      },\n      className: isResizable ? undefined : \"react-resizable-hide\",\n      width: position.width,\n      height: position.height,\n      minConstraints: minConstraints,\n      maxConstraints: maxConstraints,\n      onResizeStop: this.curryResizeHandler(position, this.onResizeStop),\n      onResizeStart: this.curryResizeHandler(position, this.onResizeStart),\n      onResize: this.curryResizeHandler(position, this.onResize),\n      transformScale: transformScale,\n      resizeHandles: resizeHandles,\n      handle: resizeHandle\n    }, child);\n  }\n  /**\n   * Wrapper around resize events to provide more useful data.\n   */\n  onResizeHandler(e /*: Event*/, _ref4 /*:: */,\n  // 'size' is updated position\n  position /*: Position*/,\n  // existing position\n  handlerName /*: string*/) /*: void*/{\n    let {\n      node,\n      size,\n      handle\n    } /*: ResizeCallbackData*/ = _ref4 /*: ResizeCallbackData*/;\n    const handler = this.props[handlerName];\n    if (!handler) return;\n    const {\n      x,\n      y,\n      i,\n      maxH,\n      minH,\n      containerWidth\n    } = this.props;\n    const {\n      minW,\n      maxW\n    } = this.props;\n\n    // Clamping of dimensions based on resize direction\n    let updatedSize = size;\n    if (node) {\n      updatedSize = (0, _utils.resizeItemInDirection)(handle, position, size, containerWidth);\n      (0, _reactDom.flushSync)(() => {\n        this.setState({\n          resizing: handlerName === \"onResizeStop\" ? null : updatedSize\n        });\n      });\n    }\n\n    // Get new XY based on pixel size\n    let {\n      w,\n      h\n    } = (0, _calculateUtils.calcWH)(this.getPositionParams(), updatedSize.width, updatedSize.height, x, y, handle);\n\n    // Min/max capping.\n    // minW should be at least 1 (TODO propTypes validation?)\n    w = (0, _calculateUtils.clamp)(w, Math.max(minW, 1), maxW);\n    h = (0, _calculateUtils.clamp)(h, minH, maxH);\n    handler.call(this, i, w, h, {\n      e,\n      node,\n      size: updatedSize,\n      handle\n    });\n  }\n  render() /*: ReactNode*/{\n    const {\n      x,\n      y,\n      w,\n      h,\n      isDraggable,\n      isResizable,\n      droppingPosition,\n      useCSSTransforms\n    } = this.props;\n    const pos = (0, _calculateUtils.calcGridItemPosition)(this.getPositionParams(), x, y, w, h, this.state);\n    const child = _react.default.Children.only(this.props.children);\n\n    // Create the child element. We clone the existing element but modify its className and style.\n    let newChild = /*#__PURE__*/_react.default.cloneElement(child, {\n      ref: this.elementRef,\n      className: (0, _clsx.default)(\"react-grid-item\", child.props.className, this.props.className, {\n        static: this.props.static,\n        resizing: Boolean(this.state.resizing),\n        \"react-draggable\": isDraggable,\n        \"react-draggable-dragging\": Boolean(this.state.dragging),\n        dropping: Boolean(droppingPosition),\n        cssTransforms: useCSSTransforms\n      }),\n      // We can set the width and height on the child, but unfortunately we can't set the position.\n      style: {\n        ...this.props.style,\n        ...child.props.style,\n        ...this.createStyle(pos)\n      }\n    });\n\n    // Resizable support. This is usually on but the user can toggle it off.\n    newChild = this.mixinResizable(newChild, pos, isResizable);\n\n    // Draggable support. This is always on, except for with placeholders.\n    newChild = this.mixinDraggable(newChild, isDraggable);\n    return newChild;\n  }\n}\nexports.default = GridItem;\n_defineProperty(GridItem, \"propTypes\", {\n  // Children must be only a single element\n  children: _propTypes.default.element,\n  // General grid attributes\n  cols: _propTypes.default.number.isRequired,\n  containerWidth: _propTypes.default.number.isRequired,\n  rowHeight: _propTypes.default.number.isRequired,\n  margin: _propTypes.default.array.isRequired,\n  maxRows: _propTypes.default.number.isRequired,\n  containerPadding: _propTypes.default.array.isRequired,\n  // These are all in grid units\n  x: _propTypes.default.number.isRequired,\n  y: _propTypes.default.number.isRequired,\n  w: _propTypes.default.number.isRequired,\n  h: _propTypes.default.number.isRequired,\n  // All optional\n  minW: function (props /*: Props*/, propName /*: string*/) {\n    const value = props[propName];\n    if (typeof value !== \"number\") return new Error(\"minWidth not Number\");\n    if (value > props.w || value > props.maxW) return new Error(\"minWidth larger than item width/maxWidth\");\n  },\n  maxW: function (props /*: Props*/, propName /*: string*/) {\n    const value = props[propName];\n    if (typeof value !== \"number\") return new Error(\"maxWidth not Number\");\n    if (value < props.w || value < props.minW) return new Error(\"maxWidth smaller than item width/minWidth\");\n  },\n  minH: function (props /*: Props*/, propName /*: string*/) {\n    const value = props[propName];\n    if (typeof value !== \"number\") return new Error(\"minHeight not Number\");\n    if (value > props.h || value > props.maxH) return new Error(\"minHeight larger than item height/maxHeight\");\n  },\n  maxH: function (props /*: Props*/, propName /*: string*/) {\n    const value = props[propName];\n    if (typeof value !== \"number\") return new Error(\"maxHeight not Number\");\n    if (value < props.h || value < props.minH) return new Error(\"maxHeight smaller than item height/minHeight\");\n  },\n  // ID is nice to have for callbacks\n  i: _propTypes.default.string.isRequired,\n  // Resize handle options\n  resizeHandles: _ReactGridLayoutPropTypes.resizeHandleAxesType,\n  resizeHandle: _ReactGridLayoutPropTypes.resizeHandleType,\n  // Functions\n  onDragStop: _propTypes.default.func,\n  onDragStart: _propTypes.default.func,\n  onDrag: _propTypes.default.func,\n  onResizeStop: _propTypes.default.func,\n  onResizeStart: _propTypes.default.func,\n  onResize: _propTypes.default.func,\n  // Flags\n  isDraggable: _propTypes.default.bool.isRequired,\n  isResizable: _propTypes.default.bool.isRequired,\n  isBounded: _propTypes.default.bool.isRequired,\n  static: _propTypes.default.bool,\n  // Use CSS transforms instead of top/left\n  useCSSTransforms: _propTypes.default.bool.isRequired,\n  transformScale: _propTypes.default.number,\n  // Others\n  className: _propTypes.default.string,\n  // Selector for draggable handle\n  handle: _propTypes.default.string,\n  // Selector for draggable cancel (see react-draggable)\n  cancel: _propTypes.default.string,\n  // Current position of a dropping element\n  droppingPosition: _propTypes.default.shape({\n    e: _propTypes.default.object.isRequired,\n    left: _propTypes.default.number.isRequired,\n    top: _propTypes.default.number.isRequired\n  })\n});\n_defineProperty(GridItem, \"defaultProps\", {\n  className: \"\",\n  cancel: \"\",\n  handle: \"\",\n  minH: 1,\n  minW: 1,\n  maxH: Infinity,\n  maxW: Infinity,\n  transformScale: 1\n});"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,SAAS;AACb,IAAI;AACJ,IAAI,aAAa;AACjB,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,QAAQ;AACZ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AAAG;AAC9F,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IAAI,MAAM,eAAe;IAAM,IAAI,OAAO,KAAK;QAAE,OAAO,cAAc,CAAC,KAAK,KAAK;YAAE,OAAO;YAAO,YAAY;YAAM,cAAc;YAAM,UAAU;QAAK;IAAI,OAAO;QAAE,GAAG,CAAC,IAAI,GAAG;IAAO;IAAE,OAAO;AAAK;AAC3O,SAAS,eAAe,GAAG;IAAI,IAAI,MAAM,aAAa,KAAK;IAAW,OAAO,OAAO,QAAQ,WAAW,MAAM,OAAO;AAAM;AAC1H,SAAS,aAAa,KAAK,EAAE,IAAI;IAAI,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM,OAAO;IAAO,IAAI,OAAO,KAAK,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,SAAS,WAAW;QAAE,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,QAAQ;QAAY,IAAI,OAAO,QAAQ,UAAU,OAAO;QAAK,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,SAAS,WAAW,SAAS,MAAM,EAAE;AAAQ;AACxX,6EAA6E,GAC7E;;;;;;;iBAOiB,GACjB,4DAA4D,GAC5D,8EAA8E,GAC9E,0DAA0D,GAC1D;;;;;UAKU,GACV;;;;EAIE,GACF;;;;UAIU,GACV;;;;EAIE,GACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2CE,GACF;;;;;;;;;EASE,GACF;;CAEC,GACD,MAAM,iBAAiB,OAAO,OAAO,CAAC,SAAS,CAAC,mBAAmB;IACjE,aAAc;QACZ,KAAK,IAAI;QACT,gBAAgB,IAAI,EAAE,SAAS;YAC7B,UAAU;YACV,UAAU;YACV,WAAW;QACb;QACA,gBAAgB,IAAI,EAAE,cAAc,WAAW,GAAE,OAAO,OAAO,CAAC,SAAS;QACzE;;;;KAIC,GACD,gBAAgB,IAAI,EAAE,eAAe,CAAC,GAAG;YACvC,IAAI,EACF,IAAI,EACL,GAAG;YACJ,MAAM,EACJ,WAAW,EACX,cAAc,EACf,GAAG,IAAI,CAAC,KAAK;YACd,IAAI,CAAC,aAAa;YAClB,MAAM,YAAY,mBAAmB,MAAK;gBACxC,KAAK;gBACL,MAAM;YACR;YAEA,yCAAyC;YACzC,MAAM,EACJ,YAAY,EACb,GAAG;YACJ,IAAI,CAAC,cAAc;YACnB,MAAM,aAAa,aAAa,qBAAqB;YACrD,MAAM,aAAa,KAAK,qBAAqB;YAC7C,MAAM,QAAQ,WAAW,IAAI,GAAG;YAChC,MAAM,QAAQ,WAAW,IAAI,GAAG;YAChC,MAAM,OAAO,WAAW,GAAG,GAAG;YAC9B,MAAM,OAAO,WAAW,GAAG,GAAG;YAC9B,YAAY,IAAI,GAAG,QAAQ,QAAQ,aAAa,UAAU;YAC1D,YAAY,GAAG,GAAG,OAAO,OAAO,aAAa,SAAS;YACtD,IAAI,CAAC,QAAQ,CAAC;gBACZ,UAAU;YACZ;YAEA,+BAA+B;YAC/B,MAAM,EACJ,CAAC,EACD,CAAC,EACF,GAAG,CAAC,GAAG,gBAAgB,MAAM,EAAE,IAAI,CAAC,iBAAiB,IAAI,YAAY,GAAG,EAAE,YAAY,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACvH,OAAO,YAAY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;gBAChD;gBACA;gBACA;YACF;QACF;QACA;;;;;KAKC,GACD,gBAAgB,IAAI,EAAE,UAAU,CAAC,GAAG,OAAO;YACzC,IAAI,EACF,IAAI,EACJ,MAAM,EACN,MAAM,EACP,GAAG;YACJ,MAAM,EACJ,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;YACd,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACxB,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG;YACpC,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG;YACtC,MAAM,EACJ,SAAS,EACT,CAAC,EACD,CAAC,EACD,CAAC,EACD,cAAc,EACf,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,iBAAiB,IAAI,CAAC,iBAAiB;YAE7C,qDAAqD;YACrD,IAAI,WAAW;gBACb,MAAM,EACJ,YAAY,EACb,GAAG;gBACJ,IAAI,cAAc;oBAChB,MAAM,EACJ,MAAM,EACN,SAAS,EACT,gBAAgB,EACjB,GAAG,IAAI,CAAC,KAAK;oBACd,MAAM,iBAAiB,aAAa,YAAY,GAAG,CAAC,GAAG,gBAAgB,gBAAgB,EAAE,GAAG,WAAW,MAAM,CAAC,EAAE;oBAChH,MAAM,CAAC,GAAG,gBAAgB,KAAK,EAAE,MAAM,gBAAgB,CAAC,EAAE,EAAE,GAAG;oBAC/D,MAAM,WAAW,CAAC,GAAG,gBAAgB,gBAAgB,EAAE;oBACvD,MAAM,gBAAgB,iBAAiB,CAAC,GAAG,gBAAgB,gBAAgB,EAAE,GAAG,UAAU,MAAM,CAAC,EAAE;oBACnG,OAAO,CAAC,GAAG,gBAAgB,KAAK,EAAE,OAAO,gBAAgB,CAAC,EAAE,EAAE,GAAG;gBACnE;YACF;YACA,MAAM,YAAY,mBAAmB,MAAK;gBACxC;gBACA;YACF;YAEA,gDAAgD;YAChD,IAAI,WAAW;gBACb,IAAI,CAAC,QAAQ,CAAC;oBACZ,UAAU;gBACZ;YACF,OAAO;gBACL,CAAC,GAAG,UAAU,SAAS,EAAE;oBACvB,IAAI,CAAC,QAAQ,CAAC;wBACZ,UAAU;oBACZ;gBACF;YACF;YAEA,+BAA+B;YAC/B,MAAM,EACJ,CAAC,EACD,CAAC,EACF,GAAG,CAAC,GAAG,gBAAgB,MAAM,EAAE,gBAAgB,KAAK,MAAM,GAAG;YAC9D,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG;gBAChC;gBACA;gBACA;YACF;QACF;QACA;;;;KAIC,GACD,gBAAgB,IAAI,EAAE,cAAc,CAAC,GAAG;YACtC,IAAI,EACF,IAAI,EACL,GAAG;YACJ,MAAM,EACJ,UAAU,EACX,GAAG,IAAI,CAAC,KAAK;YACd,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACxB,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,EACJ,CAAC,EACD,CAAC,EACD,CAAC,EACF,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,EACJ,IAAI,EACJ,GAAG,EACJ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ;YACvB,MAAM,YAAY,mBAAmB,MAAK;gBACxC;gBACA;YACF;YACA,IAAI,CAAC,QAAQ,CAAC;gBACZ,UAAU;YACZ;YACA,MAAM,EACJ,CAAC,EACD,CAAC,EACF,GAAG,CAAC,GAAG,gBAAgB,MAAM,EAAE,IAAI,CAAC,iBAAiB,IAAI,KAAK,MAAM,GAAG;YACxE,OAAO,WAAW,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG;gBACpC;gBACA;gBACA;YACF;QACF;QACA;;;;KAIC,GACD,gBAAgB,IAAI,EAAE,gBAAgB,CAAC,GAAG,cAAc,WAAa,IAAI,CAAC,eAAe,CAAC,GAAG,cAAc,UAAU;QACrH,8BAA8B;QAC9B,gBAAgB,IAAI,EAAE,iBAAiB,CAAC,GAAG,cAAc,WAAa,IAAI,CAAC,eAAe,CAAC,GAAG,cAAc,UAAU;QACtH,yBAAyB;QACzB,gBAAgB,IAAI,EAAE,YAAY,CAAC,GAAG,cAAc,WAAa,IAAI,CAAC,eAAe,CAAC,GAAG,cAAc,UAAU;IACnH;IACA,sBAAsB,UAAU,SAAS,GAAV,EAAc,UAAU,SAAS,GAAV,EAAc,WAAW,GAAE;QAC/E,2EAA2E;QAC3E,yBAAyB;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,QAAQ,EAAE,OAAO;QACvD,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,UAAU,gBAAgB,EAAE,OAAO;QACvE,8DAA8D;QAC9D,MAAM,cAAc,CAAC,GAAG,gBAAgB,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK;QACpK,MAAM,cAAc,CAAC,GAAG,gBAAgB,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,CAAC,YAAY,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE;QACrJ,OAAO,CAAC,CAAC,GAAG,OAAO,iBAAiB,EAAE,aAAa,gBAAgB,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,UAAU,gBAAgB;IAC/H;IACA,oBAAoB;QAClB,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACzB;IACA,mBAAmB,UAAU,SAAS,GAAV,EAAc;QACxC,IAAI,CAAC,gBAAgB,CAAC;IACxB;IAEA,iGAAiG;IACjG,iCAAiC;IACjC,iBAAiB,UAAU,SAAS,GAAV,EAAc;QACtC,MAAM,EACJ,gBAAgB,EACjB,GAAG,IAAI,CAAC,KAAK;QACd,IAAI,CAAC,kBAAkB;QACvB,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO;QACpC,0CAA0C;QAC1C,IAAI,CAAC,MAAM;QACX,MAAM,uBAAuB,UAAU,gBAAgB,IAAI;YACzD,MAAM;YACN,KAAK;QACP;QACA,MAAM,EACJ,QAAQ,EACT,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,aAAa,YAAY,iBAAiB,IAAI,KAAK,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,KAAK,qBAAqB,GAAG;QACvI,IAAI,CAAC,UAAU;YACb,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAAE;gBACnC;gBACA,QAAQ,iBAAiB,IAAI;gBAC7B,QAAQ,iBAAiB,GAAG;YAC9B;QACF,OAAO,IAAI,YAAY;YACrB,MAAM,SAAS,iBAAiB,IAAI,GAAG,SAAS,IAAI;YACpD,MAAM,SAAS,iBAAiB,GAAG,GAAG,SAAS,GAAG;YAClD,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;gBAC9B;gBACA;gBACA;YACF,GAAG,KAAK,gDAAgD;;QAE1D;IACF;IAEA,oBAAoB,kBAAkB,GAAE;QACtC,IAAI,MAAM,SAAS,MAAK,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK;QACtG,OAAO;YACL,MAAM,MAAM,IAAI;YAChB,kBAAkB,MAAM,gBAAgB;YACxC,gBAAgB,MAAM,cAAc;YACpC,QAAQ,MAAM,MAAM;YACpB,SAAS,MAAM,OAAO;YACtB,WAAW,MAAM,SAAS;QAC5B;IACF;IAEA;;;;;;;;;GASC,GACD,YAAY,IAAI,YAAY,GAAb,EAAiB,8BAA8B,GAAE;QAC9D,MAAM,EACJ,cAAc,EACd,cAAc,EACd,gBAAgB,EACjB,GAAG,IAAI,CAAC,KAAK;QACd,IAAI;QACJ,mCAAmC;QACnC,IAAI,kBAAkB;YACpB,QAAQ,CAAC,GAAG,OAAO,YAAY,EAAE;QACnC,OAAO;YACL,kBAAkB;YAClB,QAAQ,CAAC,GAAG,OAAO,UAAU,EAAE;YAE/B,qCAAqC;YACrC,IAAI,gBAAgB;gBAClB,MAAM,IAAI,GAAG,CAAC,GAAG,OAAO,IAAI,EAAE,IAAI,IAAI,GAAG;gBACzC,MAAM,KAAK,GAAG,CAAC,GAAG,OAAO,IAAI,EAAE,IAAI,KAAK,GAAG;YAC7C;QACF;QACA,OAAO;IACT;IAEA;;;;GAIC,GACD,eAAe,MAAM,qBAAqB,GAAtB,EAA0B,YAAY,WAAW,GAAZ,EAAgB,qBAAqB,GAAE;QAC9F,OAAO,WAAW,GAAE,OAAO,OAAO,CAAC,aAAa,CAAC,gBAAgB,aAAa,EAAE;YAC9E,UAAU,CAAC;YACX,SAAS,IAAI,CAAC,WAAW;YACzB,QAAQ,IAAI,CAAC,MAAM;YACnB,QAAQ,IAAI,CAAC,UAAU;YACvB,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,QAAQ,4BAA4B,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;YACrF,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc;YAChC,SAAS,IAAI,CAAC,UAAU;QAC1B,GAAG;IACL;IAEA;;;GAGC,GACD,mBAAmB,SAAS,YAAY,GAAb,EAAiB,QAAQ,YAAY,GAAb,EAAiB,YAAY,GAAE;QAChF,OAAO,CAAC,EAAE,SAAS,KAAI,KAAK,sBAAsB,MAAO,YAAY,GAAE,QAAQ,GAAG,MAAM;IAC1F;IAEA;;;;;GAKC,GACD,eAAe,MAAM,qBAAqB,GAAtB,EAA0B,SAAS,YAAY,GAAb,EAAiB,YAAY,WAAW,GAAZ,EAAgB,qBAAqB,GAAE;QACvH,MAAM,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,cAAc,EACd,aAAa,EACb,YAAY,EACb,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,iBAAiB,IAAI,CAAC,iBAAiB;QAE7C,6FAA6F;QAC7F,MAAM,WAAW,CAAC,GAAG,gBAAgB,oBAAoB,EAAE,gBAAgB,GAAG,GAAG,MAAM,GAAG,KAAK;QAE/F,sDAAsD;QACtD,MAAM,OAAO,CAAC,GAAG,gBAAgB,oBAAoB,EAAE,gBAAgB,GAAG,GAAG,MAAM;QACnF,MAAM,QAAQ,CAAC,GAAG,gBAAgB,oBAAoB,EAAE,gBAAgB,GAAG,GAAG,MAAM;QACpF,MAAM,iBAAiB;YAAC,KAAK,KAAK;YAAE,KAAK,MAAM;SAAC;QAChD,MAAM,iBAAiB;YAAC,KAAK,GAAG,CAAC,MAAM,KAAK,EAAE;YAAW,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE;SAAU;QAC1F,OAAO,WAAW,GAAE,OAAO,OAAO,CAAC,aAAa,CAAC,gBAAgB,SAAS,EAExE;YACA,eAAe;gBACb,UAAU,CAAC;YACb;YACA,WAAW,cAAc,YAAY;YACrC,OAAO,SAAS,KAAK;YACrB,QAAQ,SAAS,MAAM;YACvB,gBAAgB;YAChB,gBAAgB;YAChB,cAAc,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI,CAAC,YAAY;YACjE,eAAe,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI,CAAC,aAAa;YACnE,UAAU,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI,CAAC,QAAQ;YACzD,gBAAgB;YAChB,eAAe;YACf,QAAQ;QACV,GAAG;IACL;IACA;;GAEC,GACD,gBAAgB,EAAE,SAAS,GAAV,EAAc,MAAM,KAAK,GAAN,EACpC,6BAA6B;IAC7B,SAAS,YAAY,GAAb,EACR,oBAAoB;IACpB,YAAY,UAAU,GAAX,EAAe,QAAQ,GAAE;QAClC,IAAI,EACF,IAAI,EACJ,IAAI,EACJ,MAAM,EACP,CAAC,sBAAsB,MAAK,MAAM,sBAAsB;QACzD,MAAM,UAAU,IAAI,CAAC,KAAK,CAAC,YAAY;QACvC,IAAI,CAAC,SAAS;QACd,MAAM,EACJ,CAAC,EACD,CAAC,EACD,CAAC,EACD,IAAI,EACJ,IAAI,EACJ,cAAc,EACf,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,EACJ,IAAI,EACJ,IAAI,EACL,GAAG,IAAI,CAAC,KAAK;QAEd,mDAAmD;QACnD,IAAI,cAAc;QAClB,IAAI,MAAM;YACR,cAAc,CAAC,GAAG,OAAO,qBAAqB,EAAE,QAAQ,UAAU,MAAM;YACxE,CAAC,GAAG,UAAU,SAAS,EAAE;gBACvB,IAAI,CAAC,QAAQ,CAAC;oBACZ,UAAU,gBAAgB,iBAAiB,OAAO;gBACpD;YACF;QACF;QAEA,iCAAiC;QACjC,IAAI,EACF,CAAC,EACD,CAAC,EACF,GAAG,CAAC,GAAG,gBAAgB,MAAM,EAAE,IAAI,CAAC,iBAAiB,IAAI,YAAY,KAAK,EAAE,YAAY,MAAM,EAAE,GAAG,GAAG;QAEvG,mBAAmB;QACnB,yDAAyD;QACzD,IAAI,CAAC,GAAG,gBAAgB,KAAK,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,IAAI;QACrD,IAAI,CAAC,GAAG,gBAAgB,KAAK,EAAE,GAAG,MAAM;QACxC,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG;YAC1B;YACA;YACA,MAAM;YACN;QACF;IACF;IACA,SAAS,aAAa,GAAE;QACtB,MAAM,EACJ,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,gBAAgB,EACjB,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,MAAM,CAAC,GAAG,gBAAgB,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK;QACtG,MAAM,QAAQ,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;QAE9D,8FAA8F;QAC9F,IAAI,WAAW,WAAW,GAAE,OAAO,OAAO,CAAC,YAAY,CAAC,OAAO;YAC7D,KAAK,IAAI,CAAC,UAAU;YACpB,WAAW,CAAC,GAAG,MAAM,OAAO,EAAE,mBAAmB,MAAM,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBAC5F,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM;gBACzB,UAAU,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ;gBACrC,mBAAmB;gBACnB,4BAA4B,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ;gBACvD,UAAU,QAAQ;gBAClB,eAAe;YACjB;YACA,6FAA6F;YAC7F,OAAO;gBACL,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK;gBACnB,GAAG,MAAM,KAAK,CAAC,KAAK;gBACpB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;YAC1B;QACF;QAEA,wEAAwE;QACxE,WAAW,IAAI,CAAC,cAAc,CAAC,UAAU,KAAK;QAE9C,sEAAsE;QACtE,WAAW,IAAI,CAAC,cAAc,CAAC,UAAU;QACzC,OAAO;IACT;AACF;AACA,QAAQ,OAAO,GAAG;AAClB,gBAAgB,UAAU,aAAa;IACrC,yCAAyC;IACzC,UAAU,WAAW,OAAO,CAAC,OAAO;IACpC,0BAA0B;IAC1B,MAAM,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IAC1C,gBAAgB,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IACpD,WAAW,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IAC/C,QAAQ,WAAW,OAAO,CAAC,KAAK,CAAC,UAAU;IAC3C,SAAS,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IAC7C,kBAAkB,WAAW,OAAO,CAAC,KAAK,CAAC,UAAU;IACrD,8BAA8B;IAC9B,GAAG,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IACvC,GAAG,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IACvC,GAAG,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IACvC,GAAG,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IACvC,eAAe;IACf,MAAM,SAAU,MAAM,SAAS,GAAV,EAAc,SAAS,UAAU,GAAX;QACzC,MAAM,QAAQ,KAAK,CAAC,SAAS;QAC7B,IAAI,OAAO,UAAU,UAAU,OAAO,IAAI,MAAM;QAChD,IAAI,QAAQ,MAAM,CAAC,IAAI,QAAQ,MAAM,IAAI,EAAE,OAAO,IAAI,MAAM;IAC9D;IACA,MAAM,SAAU,MAAM,SAAS,GAAV,EAAc,SAAS,UAAU,GAAX;QACzC,MAAM,QAAQ,KAAK,CAAC,SAAS;QAC7B,IAAI,OAAO,UAAU,UAAU,OAAO,IAAI,MAAM;QAChD,IAAI,QAAQ,MAAM,CAAC,IAAI,QAAQ,MAAM,IAAI,EAAE,OAAO,IAAI,MAAM;IAC9D;IACA,MAAM,SAAU,MAAM,SAAS,GAAV,EAAc,SAAS,UAAU,GAAX;QACzC,MAAM,QAAQ,KAAK,CAAC,SAAS;QAC7B,IAAI,OAAO,UAAU,UAAU,OAAO,IAAI,MAAM;QAChD,IAAI,QAAQ,MAAM,CAAC,IAAI,QAAQ,MAAM,IAAI,EAAE,OAAO,IAAI,MAAM;IAC9D;IACA,MAAM,SAAU,MAAM,SAAS,GAAV,EAAc,SAAS,UAAU,GAAX;QACzC,MAAM,QAAQ,KAAK,CAAC,SAAS;QAC7B,IAAI,OAAO,UAAU,UAAU,OAAO,IAAI,MAAM;QAChD,IAAI,QAAQ,MAAM,CAAC,IAAI,QAAQ,MAAM,IAAI,EAAE,OAAO,IAAI,MAAM;IAC9D;IACA,mCAAmC;IACnC,GAAG,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IACvC,wBAAwB;IACxB,eAAe,0BAA0B,oBAAoB;IAC7D,cAAc,0BAA0B,gBAAgB;IACxD,YAAY;IACZ,YAAY,WAAW,OAAO,CAAC,IAAI;IACnC,aAAa,WAAW,OAAO,CAAC,IAAI;IACpC,QAAQ,WAAW,OAAO,CAAC,IAAI;IAC/B,cAAc,WAAW,OAAO,CAAC,IAAI;IACrC,eAAe,WAAW,OAAO,CAAC,IAAI;IACtC,UAAU,WAAW,OAAO,CAAC,IAAI;IACjC,QAAQ;IACR,aAAa,WAAW,OAAO,CAAC,IAAI,CAAC,UAAU;IAC/C,aAAa,WAAW,OAAO,CAAC,IAAI,CAAC,UAAU;IAC/C,WAAW,WAAW,OAAO,CAAC,IAAI,CAAC,UAAU;IAC7C,QAAQ,WAAW,OAAO,CAAC,IAAI;IAC/B,yCAAyC;IACzC,kBAAkB,WAAW,OAAO,CAAC,IAAI,CAAC,UAAU;IACpD,gBAAgB,WAAW,OAAO,CAAC,MAAM;IACzC,SAAS;IACT,WAAW,WAAW,OAAO,CAAC,MAAM;IACpC,gCAAgC;IAChC,QAAQ,WAAW,OAAO,CAAC,MAAM;IACjC,sDAAsD;IACtD,QAAQ,WAAW,OAAO,CAAC,MAAM;IACjC,yCAAyC;IACzC,kBAAkB,WAAW,OAAO,CAAC,KAAK,CAAC;QACzC,GAAG,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;QACvC,MAAM,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;QAC1C,KAAK,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IAC3C;AACF;AACA,gBAAgB,UAAU,gBAAgB;IACxC,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,gBAAgB;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1665, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/react-grid-layout/build/ReactGridLayout.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _fastEquals = require(\"fast-equals\");\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nvar _utils = require(\"./utils\");\nvar _calculateUtils = require(\"./calculateUtils\");\nvar _GridItem = _interopRequireDefault(require(\"./GridItem\"));\nvar _ReactGridLayoutPropTypes = _interopRequireDefault(require(\"./ReactGridLayoutPropTypes\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/*:: import type {\n  ChildrenArray as ReactChildrenArray,\n  Element as ReactElement\n} from \"react\";*/\n/*:: import type {\n  CompactType,\n  GridResizeEvent,\n  GridDragEvent,\n  DragOverEvent,\n  Layout,\n  DroppingPosition,\n  LayoutItem\n} from \"./utils\";*/\n// Types\n/*:: import type { PositionParams } from \"./calculateUtils\";*/\n/*:: type State = {\n  activeDrag: ?LayoutItem,\n  layout: Layout,\n  mounted: boolean,\n  oldDragItem: ?LayoutItem,\n  oldLayout: ?Layout,\n  oldResizeItem: ?LayoutItem,\n  resizing: boolean,\n  droppingDOMNode: ?ReactElement<any>,\n  droppingPosition?: DroppingPosition,\n  // Mirrored props\n  children: ReactChildrenArray<ReactElement<any>>,\n  compactType?: CompactType,\n  propsLayout?: Layout\n};*/\n/*:: import type { Props, DefaultProps } from \"./ReactGridLayoutPropTypes\";*/\n// End Types\nconst layoutClassName = \"react-grid-layout\";\nlet isFirefox = false;\n// Try...catch will protect from navigator not existing (e.g. node) or a bad implementation of navigator\ntry {\n  isFirefox = /firefox/i.test(navigator.userAgent);\n} catch (e) {\n  /* Ignore */\n}\n\n/**\n * A reactive, fluid grid layout with draggable, resizable components.\n */\n\nclass ReactGridLayout extends React.Component /*:: <Props, State>*/{\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      activeDrag: null,\n      layout: (0, _utils.synchronizeLayoutWithChildren)(this.props.layout, this.props.children, this.props.cols,\n      // Legacy support for verticalCompact: false\n      (0, _utils.compactType)(this.props), this.props.allowOverlap),\n      mounted: false,\n      oldDragItem: null,\n      oldLayout: null,\n      oldResizeItem: null,\n      resizing: false,\n      droppingDOMNode: null,\n      children: []\n    });\n    _defineProperty(this, \"dragEnterCounter\", 0);\n    /**\n     * When dragging starts\n     * @param {String} i Id of the child\n     * @param {Number} x X position of the move\n     * @param {Number} y Y position of the move\n     * @param {Event} e The mousedown event\n     * @param {Element} node The current dragging DOM element\n     */\n    _defineProperty(this, \"onDragStart\", (i /*: string*/, x /*: number*/, y /*: number*/, _ref /*:: */) => {\n      let {\n        e,\n        node\n      } /*: GridDragEvent*/ = _ref /*: GridDragEvent*/;\n      const {\n        layout\n      } = this.state;\n      const l = (0, _utils.getLayoutItem)(layout, i);\n      if (!l) return;\n\n      // Create placeholder (display only)\n      const placeholder = {\n        w: l.w,\n        h: l.h,\n        x: l.x,\n        y: l.y,\n        placeholder: true,\n        i: i\n      };\n      this.setState({\n        oldDragItem: (0, _utils.cloneLayoutItem)(l),\n        oldLayout: layout,\n        activeDrag: placeholder\n      });\n      return this.props.onDragStart(layout, l, l, null, e, node);\n    });\n    /**\n     * Each drag movement create a new dragelement and move the element to the dragged location\n     * @param {String} i Id of the child\n     * @param {Number} x X position of the move\n     * @param {Number} y Y position of the move\n     * @param {Event} e The mousedown event\n     * @param {Element} node The current dragging DOM element\n     */\n    _defineProperty(this, \"onDrag\", (i, x, y, _ref2) => {\n      let {\n        e,\n        node\n      } = _ref2;\n      const {\n        oldDragItem\n      } = this.state;\n      let {\n        layout\n      } = this.state;\n      const {\n        cols,\n        allowOverlap,\n        preventCollision\n      } = this.props;\n      const l = (0, _utils.getLayoutItem)(layout, i);\n      if (!l) return;\n\n      // Create placeholder (display only)\n      const placeholder = {\n        w: l.w,\n        h: l.h,\n        x: l.x,\n        y: l.y,\n        placeholder: true,\n        i: i\n      };\n\n      // Move the element to the dragged location.\n      const isUserAction = true;\n      layout = (0, _utils.moveElement)(layout, l, x, y, isUserAction, preventCollision, (0, _utils.compactType)(this.props), cols, allowOverlap);\n      this.props.onDrag(layout, oldDragItem, l, placeholder, e, node);\n      this.setState({\n        layout: allowOverlap ? layout : (0, _utils.compact)(layout, (0, _utils.compactType)(this.props), cols),\n        activeDrag: placeholder\n      });\n    });\n    /**\n     * When dragging stops, figure out which position the element is closest to and update its x and y.\n     * @param  {String} i Index of the child.\n     * @param {Number} x X position of the move\n     * @param {Number} y Y position of the move\n     * @param {Event} e The mousedown event\n     * @param {Element} node The current dragging DOM element\n     */\n    _defineProperty(this, \"onDragStop\", (i, x, y, _ref3) => {\n      let {\n        e,\n        node\n      } = _ref3;\n      if (!this.state.activeDrag) return;\n      const {\n        oldDragItem\n      } = this.state;\n      let {\n        layout\n      } = this.state;\n      const {\n        cols,\n        preventCollision,\n        allowOverlap\n      } = this.props;\n      const l = (0, _utils.getLayoutItem)(layout, i);\n      if (!l) return;\n\n      // Move the element here\n      const isUserAction = true;\n      layout = (0, _utils.moveElement)(layout, l, x, y, isUserAction, preventCollision, (0, _utils.compactType)(this.props), cols, allowOverlap);\n\n      // Set state\n      const newLayout = allowOverlap ? layout : (0, _utils.compact)(layout, (0, _utils.compactType)(this.props), cols);\n      this.props.onDragStop(newLayout, oldDragItem, l, null, e, node);\n      const {\n        oldLayout\n      } = this.state;\n      this.setState({\n        activeDrag: null,\n        layout: newLayout,\n        oldDragItem: null,\n        oldLayout: null\n      });\n      this.onLayoutMaybeChanged(newLayout, oldLayout);\n    });\n    _defineProperty(this, \"onResizeStart\", (i, w, h, _ref4) => {\n      let {\n        e,\n        node\n      } = _ref4;\n      const {\n        layout\n      } = this.state;\n      const l = (0, _utils.getLayoutItem)(layout, i);\n      if (!l) return;\n      this.setState({\n        oldResizeItem: (0, _utils.cloneLayoutItem)(l),\n        oldLayout: this.state.layout,\n        resizing: true\n      });\n      this.props.onResizeStart(layout, l, l, null, e, node);\n    });\n    _defineProperty(this, \"onResize\", (i, w, h, _ref5) => {\n      let {\n        e,\n        node,\n        size,\n        handle\n      } = _ref5;\n      const {\n        oldResizeItem\n      } = this.state;\n      const {\n        layout\n      } = this.state;\n      const {\n        cols,\n        preventCollision,\n        allowOverlap\n      } = this.props;\n      let shouldMoveItem = false;\n      let finalLayout;\n      let x;\n      let y;\n      const [newLayout, l] = (0, _utils.withLayoutItem)(layout, i, l => {\n        let hasCollisions;\n        x = l.x;\n        y = l.y;\n        if ([\"sw\", \"w\", \"nw\", \"n\", \"ne\"].indexOf(handle) !== -1) {\n          if ([\"sw\", \"nw\", \"w\"].indexOf(handle) !== -1) {\n            x = l.x + (l.w - w);\n            w = l.x !== x && x < 0 ? l.w : w;\n            x = x < 0 ? 0 : x;\n          }\n          if ([\"ne\", \"n\", \"nw\"].indexOf(handle) !== -1) {\n            y = l.y + (l.h - h);\n            h = l.y !== y && y < 0 ? l.h : h;\n            y = y < 0 ? 0 : y;\n          }\n          shouldMoveItem = true;\n        }\n\n        // Something like quad tree should be used\n        // to find collisions faster\n        if (preventCollision && !allowOverlap) {\n          const collisions = (0, _utils.getAllCollisions)(layout, {\n            ...l,\n            w,\n            h,\n            x,\n            y\n          }).filter(layoutItem => layoutItem.i !== l.i);\n          hasCollisions = collisions.length > 0;\n\n          // If we're colliding, we need adjust the placeholder.\n          if (hasCollisions) {\n            // Reset layoutItem dimensions if there were collisions\n            y = l.y;\n            h = l.h;\n            x = l.x;\n            w = l.w;\n            shouldMoveItem = false;\n          }\n        }\n        l.w = w;\n        l.h = h;\n        return l;\n      });\n\n      // Shouldn't ever happen, but typechecking makes it necessary\n      if (!l) return;\n      finalLayout = newLayout;\n      if (shouldMoveItem) {\n        // Move the element to the new position.\n        const isUserAction = true;\n        finalLayout = (0, _utils.moveElement)(newLayout, l, x, y, isUserAction, this.props.preventCollision, (0, _utils.compactType)(this.props), cols, allowOverlap);\n      }\n\n      // Create placeholder element (display only)\n      const placeholder = {\n        w: l.w,\n        h: l.h,\n        x: l.x,\n        y: l.y,\n        static: true,\n        i: i\n      };\n      this.props.onResize(finalLayout, oldResizeItem, l, placeholder, e, node);\n\n      // Re-compact the newLayout and set the drag placeholder.\n      this.setState({\n        layout: allowOverlap ? finalLayout : (0, _utils.compact)(finalLayout, (0, _utils.compactType)(this.props), cols),\n        activeDrag: placeholder\n      });\n    });\n    _defineProperty(this, \"onResizeStop\", (i, w, h, _ref6) => {\n      let {\n        e,\n        node\n      } = _ref6;\n      const {\n        layout,\n        oldResizeItem\n      } = this.state;\n      const {\n        cols,\n        allowOverlap\n      } = this.props;\n      const l = (0, _utils.getLayoutItem)(layout, i);\n\n      // Set state\n      const newLayout = allowOverlap ? layout : (0, _utils.compact)(layout, (0, _utils.compactType)(this.props), cols);\n      this.props.onResizeStop(newLayout, oldResizeItem, l, null, e, node);\n      const {\n        oldLayout\n      } = this.state;\n      this.setState({\n        activeDrag: null,\n        layout: newLayout,\n        oldResizeItem: null,\n        oldLayout: null,\n        resizing: false\n      });\n      this.onLayoutMaybeChanged(newLayout, oldLayout);\n    });\n    // Called while dragging an element. Part of browser native drag/drop API.\n    // Native event target might be the layout itself, or an element within the layout.\n    _defineProperty(this, \"onDragOver\", e => {\n      e.preventDefault(); // Prevent any browser native action\n      e.stopPropagation();\n\n      // we should ignore events from layout's children in Firefox\n      // to avoid unpredictable jumping of a dropping placeholder\n      // FIXME remove this hack\n      if (isFirefox &&\n      // $FlowIgnore can't figure this out\n      !e.nativeEvent.target?.classList.contains(layoutClassName)) {\n        return false;\n      }\n      const {\n        droppingItem,\n        onDropDragOver,\n        margin,\n        cols,\n        rowHeight,\n        maxRows,\n        width,\n        containerPadding,\n        transformScale\n      } = this.props;\n      // Allow user to customize the dropping item or short-circuit the drop based on the results\n      // of the `onDragOver(e: Event)` callback.\n      const onDragOverResult = onDropDragOver?.(e);\n      if (onDragOverResult === false) {\n        if (this.state.droppingDOMNode) {\n          this.removeDroppingPlaceholder();\n        }\n        return false;\n      }\n      const finalDroppingItem = {\n        ...droppingItem,\n        ...onDragOverResult\n      };\n      const {\n        layout\n      } = this.state;\n\n      // $FlowIgnore missing def\n      const gridRect = e.currentTarget.getBoundingClientRect(); // The grid's position in the viewport\n\n      // Calculate the mouse position relative to the grid\n      const layerX = e.clientX - gridRect.left;\n      const layerY = e.clientY - gridRect.top;\n      const droppingPosition = {\n        left: layerX / transformScale,\n        top: layerY / transformScale,\n        e\n      };\n      if (!this.state.droppingDOMNode) {\n        const positionParams /*: PositionParams*/ = {\n          cols,\n          margin,\n          maxRows,\n          rowHeight,\n          containerWidth: width,\n          containerPadding: containerPadding || margin\n        };\n        const calculatedPosition = (0, _calculateUtils.calcXY)(positionParams, layerY, layerX, finalDroppingItem.w, finalDroppingItem.h);\n        this.setState({\n          droppingDOMNode: /*#__PURE__*/React.createElement(\"div\", {\n            key: finalDroppingItem.i\n          }),\n          droppingPosition,\n          layout: [...layout, {\n            ...finalDroppingItem,\n            x: calculatedPosition.x,\n            y: calculatedPosition.y,\n            static: false,\n            isDraggable: true\n          }]\n        });\n      } else if (this.state.droppingPosition) {\n        const {\n          left,\n          top\n        } = this.state.droppingPosition;\n        const shouldUpdatePosition = left != layerX || top != layerY;\n        if (shouldUpdatePosition) {\n          this.setState({\n            droppingPosition\n          });\n        }\n      }\n    });\n    _defineProperty(this, \"removeDroppingPlaceholder\", () => {\n      const {\n        droppingItem,\n        cols\n      } = this.props;\n      const {\n        layout\n      } = this.state;\n      const newLayout = (0, _utils.compact)(layout.filter(l => l.i !== droppingItem.i), (0, _utils.compactType)(this.props), cols, this.props.allowOverlap);\n      this.setState({\n        layout: newLayout,\n        droppingDOMNode: null,\n        activeDrag: null,\n        droppingPosition: undefined\n      });\n    });\n    _defineProperty(this, \"onDragLeave\", e => {\n      e.preventDefault(); // Prevent any browser native action\n      e.stopPropagation();\n      this.dragEnterCounter--;\n\n      // onDragLeave can be triggered on each layout's child.\n      // But we know that count of dragEnter and dragLeave events\n      // will be balanced after leaving the layout's container\n      // so we can increase and decrease count of dragEnter and\n      // when it'll be equal to 0 we'll remove the placeholder\n      if (this.dragEnterCounter === 0) {\n        this.removeDroppingPlaceholder();\n      }\n    });\n    _defineProperty(this, \"onDragEnter\", e => {\n      e.preventDefault(); // Prevent any browser native action\n      e.stopPropagation();\n      this.dragEnterCounter++;\n    });\n    _defineProperty(this, \"onDrop\", (e /*: Event*/) => {\n      e.preventDefault(); // Prevent any browser native action\n      e.stopPropagation();\n      const {\n        droppingItem\n      } = this.props;\n      const {\n        layout\n      } = this.state;\n      const item = layout.find(l => l.i === droppingItem.i);\n\n      // reset dragEnter counter on drop\n      this.dragEnterCounter = 0;\n      this.removeDroppingPlaceholder();\n      this.props.onDrop(layout, item, e);\n    });\n  }\n  componentDidMount() {\n    this.setState({\n      mounted: true\n    });\n    // Possibly call back with layout on mount. This should be done after correcting the layout width\n    // to ensure we don't rerender with the wrong width.\n    this.onLayoutMaybeChanged(this.state.layout, this.props.layout);\n  }\n  static getDerivedStateFromProps(nextProps /*: Props*/, prevState /*: State*/) /*: $Shape<State> | null*/{\n    let newLayoutBase;\n    if (prevState.activeDrag) {\n      return null;\n    }\n\n    // Legacy support for compactType\n    // Allow parent to set layout directly.\n    if (!(0, _fastEquals.deepEqual)(nextProps.layout, prevState.propsLayout) || nextProps.compactType !== prevState.compactType) {\n      newLayoutBase = nextProps.layout;\n    } else if (!(0, _utils.childrenEqual)(nextProps.children, prevState.children)) {\n      // If children change, also regenerate the layout. Use our state\n      // as the base in case because it may be more up to date than\n      // what is in props.\n      newLayoutBase = prevState.layout;\n    }\n\n    // We need to regenerate the layout.\n    if (newLayoutBase) {\n      const newLayout = (0, _utils.synchronizeLayoutWithChildren)(newLayoutBase, nextProps.children, nextProps.cols, (0, _utils.compactType)(nextProps), nextProps.allowOverlap);\n      return {\n        layout: newLayout,\n        // We need to save these props to state for using\n        // getDerivedStateFromProps instead of componentDidMount (in which we would get extra rerender)\n        compactType: nextProps.compactType,\n        children: nextProps.children,\n        propsLayout: nextProps.layout\n      };\n    }\n    return null;\n  }\n  shouldComponentUpdate(nextProps /*: Props*/, nextState /*: State*/) /*: boolean*/{\n    return (\n      // NOTE: this is almost always unequal. Therefore the only way to get better performance\n      // from SCU is if the user intentionally memoizes children. If they do, and they can\n      // handle changes properly, performance will increase.\n      this.props.children !== nextProps.children || !(0, _utils.fastRGLPropsEqual)(this.props, nextProps, _fastEquals.deepEqual) || this.state.activeDrag !== nextState.activeDrag || this.state.mounted !== nextState.mounted || this.state.droppingPosition !== nextState.droppingPosition\n    );\n  }\n  componentDidUpdate(prevProps /*: Props*/, prevState /*: State*/) {\n    if (!this.state.activeDrag) {\n      const newLayout = this.state.layout;\n      const oldLayout = prevState.layout;\n      this.onLayoutMaybeChanged(newLayout, oldLayout);\n    }\n  }\n\n  /**\n   * Calculates a pixel value for the container.\n   * @return {String} Container height in pixels.\n   */\n  containerHeight() /*: ?string*/{\n    if (!this.props.autoSize) return;\n    const nbRow = (0, _utils.bottom)(this.state.layout);\n    const containerPaddingY = this.props.containerPadding ? this.props.containerPadding[1] : this.props.margin[1];\n    return nbRow * this.props.rowHeight + (nbRow - 1) * this.props.margin[1] + containerPaddingY * 2 + \"px\";\n  }\n  onLayoutMaybeChanged(newLayout /*: Layout*/, oldLayout /*: ?Layout*/) {\n    if (!oldLayout) oldLayout = this.state.layout;\n    if (!(0, _fastEquals.deepEqual)(oldLayout, newLayout)) {\n      this.props.onLayoutChange(newLayout);\n    }\n  }\n  /**\n   * Create a placeholder object.\n   * @return {Element} Placeholder div.\n   */\n  placeholder() /*: ?ReactElement<any>*/{\n    const {\n      activeDrag\n    } = this.state;\n    if (!activeDrag) return null;\n    const {\n      width,\n      cols,\n      margin,\n      containerPadding,\n      rowHeight,\n      maxRows,\n      useCSSTransforms,\n      transformScale\n    } = this.props;\n\n    // {...this.state.activeDrag} is pretty slow, actually\n    return /*#__PURE__*/React.createElement(_GridItem.default, {\n      w: activeDrag.w,\n      h: activeDrag.h,\n      x: activeDrag.x,\n      y: activeDrag.y,\n      i: activeDrag.i,\n      className: `react-grid-placeholder ${this.state.resizing ? \"placeholder-resizing\" : \"\"}`,\n      containerWidth: width,\n      cols: cols,\n      margin: margin,\n      containerPadding: containerPadding || margin,\n      maxRows: maxRows,\n      rowHeight: rowHeight,\n      isDraggable: false,\n      isResizable: false,\n      isBounded: false,\n      useCSSTransforms: useCSSTransforms,\n      transformScale: transformScale\n    }, /*#__PURE__*/React.createElement(\"div\", null));\n  }\n\n  /**\n   * Given a grid item, set its style attributes & surround in a <Draggable>.\n   * @param  {Element} child React element.\n   * @return {Element}       Element wrapped in draggable and properly placed.\n   */\n  processGridItem(child /*: ReactElement<any>*/, isDroppingItem /*: boolean*/) /*: ?ReactElement<any>*/{\n    if (!child || !child.key) return;\n    const l = (0, _utils.getLayoutItem)(this.state.layout, String(child.key));\n    if (!l) return null;\n    const {\n      width,\n      cols,\n      margin,\n      containerPadding,\n      rowHeight,\n      maxRows,\n      isDraggable,\n      isResizable,\n      isBounded,\n      useCSSTransforms,\n      transformScale,\n      draggableCancel,\n      draggableHandle,\n      resizeHandles,\n      resizeHandle\n    } = this.props;\n    const {\n      mounted,\n      droppingPosition\n    } = this.state;\n\n    // Determine user manipulations possible.\n    // If an item is static, it can't be manipulated by default.\n    // Any properties defined directly on the grid item will take precedence.\n    const draggable = typeof l.isDraggable === \"boolean\" ? l.isDraggable : !l.static && isDraggable;\n    const resizable = typeof l.isResizable === \"boolean\" ? l.isResizable : !l.static && isResizable;\n    const resizeHandlesOptions = l.resizeHandles || resizeHandles;\n\n    // isBounded set on child if set on parent, and child is not explicitly false\n    const bounded = draggable && isBounded && l.isBounded !== false;\n    return /*#__PURE__*/React.createElement(_GridItem.default, {\n      containerWidth: width,\n      cols: cols,\n      margin: margin,\n      containerPadding: containerPadding || margin,\n      maxRows: maxRows,\n      rowHeight: rowHeight,\n      cancel: draggableCancel,\n      handle: draggableHandle,\n      onDragStop: this.onDragStop,\n      onDragStart: this.onDragStart,\n      onDrag: this.onDrag,\n      onResizeStart: this.onResizeStart,\n      onResize: this.onResize,\n      onResizeStop: this.onResizeStop,\n      isDraggable: draggable,\n      isResizable: resizable,\n      isBounded: bounded,\n      useCSSTransforms: useCSSTransforms && mounted,\n      usePercentages: !mounted,\n      transformScale: transformScale,\n      w: l.w,\n      h: l.h,\n      x: l.x,\n      y: l.y,\n      i: l.i,\n      minH: l.minH,\n      minW: l.minW,\n      maxH: l.maxH,\n      maxW: l.maxW,\n      static: l.static,\n      droppingPosition: isDroppingItem ? droppingPosition : undefined,\n      resizeHandles: resizeHandlesOptions,\n      resizeHandle: resizeHandle\n    }, child);\n  }\n  render() /*: React.Element<\"div\">*/{\n    const {\n      className,\n      style,\n      isDroppable,\n      innerRef\n    } = this.props;\n    const mergedClassName = (0, _clsx.default)(layoutClassName, className);\n    const mergedStyle = {\n      height: this.containerHeight(),\n      ...style\n    };\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: innerRef,\n      className: mergedClassName,\n      style: mergedStyle,\n      onDrop: isDroppable ? this.onDrop : _utils.noop,\n      onDragLeave: isDroppable ? this.onDragLeave : _utils.noop,\n      onDragEnter: isDroppable ? this.onDragEnter : _utils.noop,\n      onDragOver: isDroppable ? this.onDragOver : _utils.noop\n    }, React.Children.map(this.props.children, child => this.processGridItem(child)), isDroppable && this.state.droppingDOMNode && this.processGridItem(this.state.droppingDOMNode, true), this.placeholder());\n  }\n}\nexports.default = ReactGridLayout;\n// TODO publish internal ReactClass displayName transform\n_defineProperty(ReactGridLayout, \"displayName\", \"ReactGridLayout\");\n// Refactored to another module to make way for preval\n_defineProperty(ReactGridLayout, \"propTypes\", _ReactGridLayoutPropTypes.default);\n_defineProperty(ReactGridLayout, \"defaultProps\", {\n  autoSize: true,\n  cols: 12,\n  className: \"\",\n  style: {},\n  draggableHandle: \"\",\n  draggableCancel: \"\",\n  containerPadding: null,\n  rowHeight: 150,\n  maxRows: Infinity,\n  // infinite vertical growth\n  layout: [],\n  margin: [10, 10],\n  isBounded: false,\n  isDraggable: true,\n  isResizable: true,\n  allowOverlap: false,\n  isDroppable: false,\n  useCSSTransforms: true,\n  transformScale: 1,\n  verticalCompact: true,\n  compactType: \"vertical\",\n  preventCollision: false,\n  droppingItem: {\n    i: \"__dropping-elem__\",\n    h: 1,\n    w: 1\n  },\n  resizeHandles: [\"se\"],\n  onLayoutChange: _utils.noop,\n  onDragStart: _utils.noop,\n  onDrag: _utils.noop,\n  onDragStop: _utils.noop,\n  onResizeStart: _utils.noop,\n  onResize: _utils.noop,\n  onResizeStop: _utils.noop,\n  onDrop: _utils.noop,\n  onDropDragOver: _utils.noop\n});"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,QAAQ;AACZ,IAAI;AACJ,IAAI,QAAQ;AACZ,IAAI;AACJ,IAAI;AACJ,IAAI,YAAY;AAChB,IAAI,4BAA4B;AAChC,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AAAG;AAC9F,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAChlB,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IAAI,MAAM,eAAe;IAAM,IAAI,OAAO,KAAK;QAAE,OAAO,cAAc,CAAC,KAAK,KAAK;YAAE,OAAO;YAAO,YAAY;YAAM,cAAc;YAAM,UAAU;QAAK;IAAI,OAAO;QAAE,GAAG,CAAC,IAAI,GAAG;IAAO;IAAE,OAAO;AAAK;AAC3O,SAAS,eAAe,GAAG;IAAI,IAAI,MAAM,aAAa,KAAK;IAAW,OAAO,OAAO,QAAQ,WAAW,MAAM,OAAO;AAAM;AAC1H,SAAS,aAAa,KAAK,EAAE,IAAI;IAAI,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM,OAAO;IAAO,IAAI,OAAO,KAAK,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,SAAS,WAAW;QAAE,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,QAAQ;QAAY,IAAI,OAAO,QAAQ,UAAU,OAAO;QAAK,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,SAAS,WAAW,SAAS,MAAM,EAAE;AAAQ;AACxX;;;eAGe,GACf;;;;;;;;iBAQiB,GACjB,QAAQ;AACR,4DAA4D,GAC5D;;;;;;;;;;;;;;EAcE,GACF,2EAA2E,GAC3E,YAAY;AACZ,MAAM,kBAAkB;AACxB,IAAI,YAAY;AAChB,wGAAwG;AACxG,IAAI;IACF,YAAY,WAAW,IAAI,CAAC,UAAU,SAAS;AACjD,EAAE,OAAO,GAAG;AACV,UAAU,GACZ;AAEA;;CAEC,GAED,MAAM,wBAAwB,MAAM,SAAS,CAAC,mBAAmB;IAC/D,aAAc;QACZ,KAAK,IAAI;QACT,gBAAgB,IAAI,EAAE,SAAS;YAC7B,YAAY;YACZ,QAAQ,CAAC,GAAG,OAAO,6BAA6B,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EACzG,4CAA4C;YAC5C,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY;YAC5D,SAAS;YACT,aAAa;YACb,WAAW;YACX,eAAe;YACf,UAAU;YACV,iBAAiB;YACjB,UAAU,EAAE;QACd;QACA,gBAAgB,IAAI,EAAE,oBAAoB;QAC1C;;;;;;;KAOC,GACD,gBAAgB,IAAI,EAAE,eAAe,CAAC,EAAE,UAAU,KAAI,EAAE,UAAU,KAAI,EAAE,UAAU,KAAI,KAAK,KAAK;YAC9F,IAAI,EACF,CAAC,EACD,IAAI,EACL,CAAC,iBAAiB,MAAK,KAAK,iBAAiB;YAC9C,MAAM,EACJ,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,IAAI,CAAC,GAAG,OAAO,aAAa,EAAE,QAAQ;YAC5C,IAAI,CAAC,GAAG;YAER,oCAAoC;YACpC,MAAM,cAAc;gBAClB,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,CAAC;gBACN,aAAa;gBACb,GAAG;YACL;YACA,IAAI,CAAC,QAAQ,CAAC;gBACZ,aAAa,CAAC,GAAG,OAAO,eAAe,EAAE;gBACzC,WAAW;gBACX,YAAY;YACd;YACA,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG,MAAM,GAAG;QACvD;QACA;;;;;;;KAOC,GACD,gBAAgB,IAAI,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG;YACxC,IAAI,EACF,CAAC,EACD,IAAI,EACL,GAAG;YACJ,MAAM,EACJ,WAAW,EACZ,GAAG,IAAI,CAAC,KAAK;YACd,IAAI,EACF,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,gBAAgB,EACjB,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,IAAI,CAAC,GAAG,OAAO,aAAa,EAAE,QAAQ;YAC5C,IAAI,CAAC,GAAG;YAER,oCAAoC;YACpC,MAAM,cAAc;gBAClB,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,CAAC;gBACN,aAAa;gBACb,GAAG;YACL;YAEA,4CAA4C;YAC5C,MAAM,eAAe;YACrB,SAAS,CAAC,GAAG,OAAO,WAAW,EAAE,QAAQ,GAAG,GAAG,GAAG,cAAc,kBAAkB,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM;YAC7H,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,aAAa,GAAG,aAAa,GAAG;YAC1D,IAAI,CAAC,QAAQ,CAAC;gBACZ,QAAQ,eAAe,SAAS,CAAC,GAAG,OAAO,OAAO,EAAE,QAAQ,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG;gBACjG,YAAY;YACd;QACF;QACA;;;;;;;KAOC,GACD,gBAAgB,IAAI,EAAE,cAAc,CAAC,GAAG,GAAG,GAAG;YAC5C,IAAI,EACF,CAAC,EACD,IAAI,EACL,GAAG;YACJ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;YAC5B,MAAM,EACJ,WAAW,EACZ,GAAG,IAAI,CAAC,KAAK;YACd,IAAI,EACF,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,EACJ,IAAI,EACJ,gBAAgB,EAChB,YAAY,EACb,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,IAAI,CAAC,GAAG,OAAO,aAAa,EAAE,QAAQ;YAC5C,IAAI,CAAC,GAAG;YAER,wBAAwB;YACxB,MAAM,eAAe;YACrB,SAAS,CAAC,GAAG,OAAO,WAAW,EAAE,QAAQ,GAAG,GAAG,GAAG,cAAc,kBAAkB,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM;YAE7H,YAAY;YACZ,MAAM,YAAY,eAAe,SAAS,CAAC,GAAG,OAAO,OAAO,EAAE,QAAQ,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG;YAC3G,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,aAAa,GAAG,MAAM,GAAG;YAC1D,MAAM,EACJ,SAAS,EACV,GAAG,IAAI,CAAC,KAAK;YACd,IAAI,CAAC,QAAQ,CAAC;gBACZ,YAAY;gBACZ,QAAQ;gBACR,aAAa;gBACb,WAAW;YACb;YACA,IAAI,CAAC,oBAAoB,CAAC,WAAW;QACvC;QACA,gBAAgB,IAAI,EAAE,iBAAiB,CAAC,GAAG,GAAG,GAAG;YAC/C,IAAI,EACF,CAAC,EACD,IAAI,EACL,GAAG;YACJ,MAAM,EACJ,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,IAAI,CAAC,GAAG,OAAO,aAAa,EAAE,QAAQ;YAC5C,IAAI,CAAC,GAAG;YACR,IAAI,CAAC,QAAQ,CAAC;gBACZ,eAAe,CAAC,GAAG,OAAO,eAAe,EAAE;gBAC3C,WAAW,IAAI,CAAC,KAAK,CAAC,MAAM;gBAC5B,UAAU;YACZ;YACA,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,GAAG,GAAG,MAAM,GAAG;QAClD;QACA,gBAAgB,IAAI,EAAE,YAAY,CAAC,GAAG,GAAG,GAAG;YAC1C,IAAI,EACF,CAAC,EACD,IAAI,EACJ,IAAI,EACJ,MAAM,EACP,GAAG;YACJ,MAAM,EACJ,aAAa,EACd,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,EACJ,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,EACJ,IAAI,EACJ,gBAAgB,EAChB,YAAY,EACb,GAAG,IAAI,CAAC,KAAK;YACd,IAAI,iBAAiB;YACrB,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,OAAO,cAAc,EAAE,QAAQ,GAAG,CAAA;gBAC3D,IAAI;gBACJ,IAAI,EAAE,CAAC;gBACP,IAAI,EAAE,CAAC;gBACP,IAAI;oBAAC;oBAAM;oBAAK;oBAAM;oBAAK;iBAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG;oBACvD,IAAI;wBAAC;wBAAM;wBAAM;qBAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG;wBAC5C,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC;wBAClB,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,EAAE,CAAC,GAAG;wBAC/B,IAAI,IAAI,IAAI,IAAI;oBAClB;oBACA,IAAI;wBAAC;wBAAM;wBAAK;qBAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG;wBAC5C,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC;wBAClB,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,EAAE,CAAC,GAAG;wBAC/B,IAAI,IAAI,IAAI,IAAI;oBAClB;oBACA,iBAAiB;gBACnB;gBAEA,0CAA0C;gBAC1C,4BAA4B;gBAC5B,IAAI,oBAAoB,CAAC,cAAc;oBACrC,MAAM,aAAa,CAAC,GAAG,OAAO,gBAAgB,EAAE,QAAQ;wBACtD,GAAG,CAAC;wBACJ;wBACA;wBACA;wBACA;oBACF,GAAG,MAAM,CAAC,CAAA,aAAc,WAAW,CAAC,KAAK,EAAE,CAAC;oBAC5C,gBAAgB,WAAW,MAAM,GAAG;oBAEpC,sDAAsD;oBACtD,IAAI,eAAe;wBACjB,uDAAuD;wBACvD,IAAI,EAAE,CAAC;wBACP,IAAI,EAAE,CAAC;wBACP,IAAI,EAAE,CAAC;wBACP,IAAI,EAAE,CAAC;wBACP,iBAAiB;oBACnB;gBACF;gBACA,EAAE,CAAC,GAAG;gBACN,EAAE,CAAC,GAAG;gBACN,OAAO;YACT;YAEA,6DAA6D;YAC7D,IAAI,CAAC,GAAG;YACR,cAAc;YACd,IAAI,gBAAgB;gBAClB,wCAAwC;gBACxC,MAAM,eAAe;gBACrB,cAAc,CAAC,GAAG,OAAO,WAAW,EAAE,WAAW,GAAG,GAAG,GAAG,cAAc,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM;YAClJ;YAEA,4CAA4C;YAC5C,MAAM,cAAc;gBAClB,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,CAAC;gBACN,QAAQ;gBACR,GAAG;YACL;YACA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,eAAe,GAAG,aAAa,GAAG;YAEnE,yDAAyD;YACzD,IAAI,CAAC,QAAQ,CAAC;gBACZ,QAAQ,eAAe,cAAc,CAAC,GAAG,OAAO,OAAO,EAAE,aAAa,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG;gBAC3G,YAAY;YACd;QACF;QACA,gBAAgB,IAAI,EAAE,gBAAgB,CAAC,GAAG,GAAG,GAAG;YAC9C,IAAI,EACF,CAAC,EACD,IAAI,EACL,GAAG;YACJ,MAAM,EACJ,MAAM,EACN,aAAa,EACd,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,EACJ,IAAI,EACJ,YAAY,EACb,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,IAAI,CAAC,GAAG,OAAO,aAAa,EAAE,QAAQ;YAE5C,YAAY;YACZ,MAAM,YAAY,eAAe,SAAS,CAAC,GAAG,OAAO,OAAO,EAAE,QAAQ,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG;YAC3G,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,eAAe,GAAG,MAAM,GAAG;YAC9D,MAAM,EACJ,SAAS,EACV,GAAG,IAAI,CAAC,KAAK;YACd,IAAI,CAAC,QAAQ,CAAC;gBACZ,YAAY;gBACZ,QAAQ;gBACR,eAAe;gBACf,WAAW;gBACX,UAAU;YACZ;YACA,IAAI,CAAC,oBAAoB,CAAC,WAAW;QACvC;QACA,0EAA0E;QAC1E,mFAAmF;QACnF,gBAAgB,IAAI,EAAE,cAAc,CAAA;YAClC,EAAE,cAAc,IAAI,oCAAoC;YACxD,EAAE,eAAe;YAEjB,4DAA4D;YAC5D,2DAA2D;YAC3D,yBAAyB;YACzB,IAAI,aACJ,oCAAoC;YACpC,CAAC,EAAE,WAAW,CAAC,MAAM,EAAE,UAAU,SAAS,kBAAkB;gBAC1D,OAAO;YACT;YACA,MAAM,EACJ,YAAY,EACZ,cAAc,EACd,MAAM,EACN,IAAI,EACJ,SAAS,EACT,OAAO,EACP,KAAK,EACL,gBAAgB,EAChB,cAAc,EACf,GAAG,IAAI,CAAC,KAAK;YACd,2FAA2F;YAC3F,0CAA0C;YAC1C,MAAM,mBAAmB,iBAAiB;YAC1C,IAAI,qBAAqB,OAAO;gBAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;oBAC9B,IAAI,CAAC,yBAAyB;gBAChC;gBACA,OAAO;YACT;YACA,MAAM,oBAAoB;gBACxB,GAAG,YAAY;gBACf,GAAG,gBAAgB;YACrB;YACA,MAAM,EACJ,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;YAEd,0BAA0B;YAC1B,MAAM,WAAW,EAAE,aAAa,CAAC,qBAAqB,IAAI,sCAAsC;YAEhG,oDAAoD;YACpD,MAAM,SAAS,EAAE,OAAO,GAAG,SAAS,IAAI;YACxC,MAAM,SAAS,EAAE,OAAO,GAAG,SAAS,GAAG;YACvC,MAAM,mBAAmB;gBACvB,MAAM,SAAS;gBACf,KAAK,SAAS;gBACd;YACF;YACA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;gBAC/B,MAAM,eAAe,kBAAkB,MAAK;oBAC1C;oBACA;oBACA;oBACA;oBACA,gBAAgB;oBAChB,kBAAkB,oBAAoB;gBACxC;gBACA,MAAM,qBAAqB,CAAC,GAAG,gBAAgB,MAAM,EAAE,gBAAgB,QAAQ,QAAQ,kBAAkB,CAAC,EAAE,kBAAkB,CAAC;gBAC/H,IAAI,CAAC,QAAQ,CAAC;oBACZ,iBAAiB,WAAW,GAAE,MAAM,aAAa,CAAC,OAAO;wBACvD,KAAK,kBAAkB,CAAC;oBAC1B;oBACA;oBACA,QAAQ;2BAAI;wBAAQ;4BAClB,GAAG,iBAAiB;4BACpB,GAAG,mBAAmB,CAAC;4BACvB,GAAG,mBAAmB,CAAC;4BACvB,QAAQ;4BACR,aAAa;wBACf;qBAAE;gBACJ;YACF,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBACtC,MAAM,EACJ,IAAI,EACJ,GAAG,EACJ,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB;gBAC/B,MAAM,uBAAuB,QAAQ,UAAU,OAAO;gBACtD,IAAI,sBAAsB;oBACxB,IAAI,CAAC,QAAQ,CAAC;wBACZ;oBACF;gBACF;YACF;QACF;QACA,gBAAgB,IAAI,EAAE,6BAA6B;YACjD,MAAM,EACJ,YAAY,EACZ,IAAI,EACL,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,EACJ,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,YAAY,CAAC,GAAG,OAAO,OAAO,EAAE,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,CAAC,KAAK,aAAa,CAAC,GAAG,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY;YACpJ,IAAI,CAAC,QAAQ,CAAC;gBACZ,QAAQ;gBACR,iBAAiB;gBACjB,YAAY;gBACZ,kBAAkB;YACpB;QACF;QACA,gBAAgB,IAAI,EAAE,eAAe,CAAA;YACnC,EAAE,cAAc,IAAI,oCAAoC;YACxD,EAAE,eAAe;YACjB,IAAI,CAAC,gBAAgB;YAErB,uDAAuD;YACvD,2DAA2D;YAC3D,wDAAwD;YACxD,yDAAyD;YACzD,wDAAwD;YACxD,IAAI,IAAI,CAAC,gBAAgB,KAAK,GAAG;gBAC/B,IAAI,CAAC,yBAAyB;YAChC;QACF;QACA,gBAAgB,IAAI,EAAE,eAAe,CAAA;YACnC,EAAE,cAAc,IAAI,oCAAoC;YACxD,EAAE,eAAe;YACjB,IAAI,CAAC,gBAAgB;QACvB;QACA,gBAAgB,IAAI,EAAE,UAAU,CAAC,EAAE,SAAS;YAC1C,EAAE,cAAc,IAAI,oCAAoC;YACxD,EAAE,eAAe;YACjB,MAAM,EACJ,YAAY,EACb,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,EACJ,MAAM,EACP,GAAG,IAAI,CAAC,KAAK;YACd,MAAM,OAAO,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,CAAC,KAAK,aAAa,CAAC;YAEpD,kCAAkC;YAClC,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,yBAAyB;YAC9B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,MAAM;QAClC;IACF;IACA,oBAAoB;QAClB,IAAI,CAAC,QAAQ,CAAC;YACZ,SAAS;QACX;QACA,iGAAiG;QACjG,oDAAoD;QACpD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;IAChE;IACA,OAAO,yBAAyB,UAAU,SAAS,GAAV,EAAc,UAAU,SAAS,GAAV,EAAc,wBAAwB,GAAE;QACtG,IAAI;QACJ,IAAI,UAAU,UAAU,EAAE;YACxB,OAAO;QACT;QAEA,iCAAiC;QACjC,uCAAuC;QACvC,IAAI,CAAC,CAAC,GAAG,YAAY,SAAS,EAAE,UAAU,MAAM,EAAE,UAAU,WAAW,KAAK,UAAU,WAAW,KAAK,UAAU,WAAW,EAAE;YAC3H,gBAAgB,UAAU,MAAM;QAClC,OAAO,IAAI,CAAC,CAAC,GAAG,OAAO,aAAa,EAAE,UAAU,QAAQ,EAAE,UAAU,QAAQ,GAAG;YAC7E,gEAAgE;YAChE,6DAA6D;YAC7D,oBAAoB;YACpB,gBAAgB,UAAU,MAAM;QAClC;QAEA,oCAAoC;QACpC,IAAI,eAAe;YACjB,MAAM,YAAY,CAAC,GAAG,OAAO,6BAA6B,EAAE,eAAe,UAAU,QAAQ,EAAE,UAAU,IAAI,EAAE,CAAC,GAAG,OAAO,WAAW,EAAE,YAAY,UAAU,YAAY;YACzK,OAAO;gBACL,QAAQ;gBACR,iDAAiD;gBACjD,+FAA+F;gBAC/F,aAAa,UAAU,WAAW;gBAClC,UAAU,UAAU,QAAQ;gBAC5B,aAAa,UAAU,MAAM;YAC/B;QACF;QACA,OAAO;IACT;IACA,sBAAsB,UAAU,SAAS,GAAV,EAAc,UAAU,SAAS,GAAV,EAAc,WAAW,GAAE;QAC/E,OACE,wFAAwF;QACxF,oFAAoF;QACpF,sDAAsD;QACtD,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,QAAQ,IAAI,CAAC,CAAC,GAAG,OAAO,iBAAiB,EAAE,IAAI,CAAC,KAAK,EAAE,WAAW,YAAY,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,UAAU,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,UAAU,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,UAAU,gBAAgB;IAE1R;IACA,mBAAmB,UAAU,SAAS,GAAV,EAAc,UAAU,SAAS,GAAV,EAAc;QAC/D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;YAC1B,MAAM,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM;YACnC,MAAM,YAAY,UAAU,MAAM;YAClC,IAAI,CAAC,oBAAoB,CAAC,WAAW;QACvC;IACF;IAEA;;;GAGC,GACD,kBAAkB,WAAW,GAAE;QAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;QAC1B,MAAM,QAAQ,CAAC,GAAG,OAAO,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;QAClD,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;QAC7G,OAAO,QAAQ,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,oBAAoB,IAAI;IACrG;IACA,qBAAqB,UAAU,UAAU,GAAX,EAAe,UAAU,WAAW,GAAZ,EAAgB;QACpE,IAAI,CAAC,WAAW,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM;QAC7C,IAAI,CAAC,CAAC,GAAG,YAAY,SAAS,EAAE,WAAW,YAAY;YACrD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QAC5B;IACF;IACA;;;GAGC,GACD,cAAc,sBAAsB,GAAE;QACpC,MAAM,EACJ,UAAU,EACX,GAAG,IAAI,CAAC,KAAK;QACd,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,EACJ,KAAK,EACL,IAAI,EACJ,MAAM,EACN,gBAAgB,EAChB,SAAS,EACT,OAAO,EACP,gBAAgB,EAChB,cAAc,EACf,GAAG,IAAI,CAAC,KAAK;QAEd,sDAAsD;QACtD,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,UAAU,OAAO,EAAE;YACzD,GAAG,WAAW,CAAC;YACf,GAAG,WAAW,CAAC;YACf,GAAG,WAAW,CAAC;YACf,GAAG,WAAW,CAAC;YACf,GAAG,WAAW,CAAC;YACf,WAAW,CAAC,uBAAuB,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,yBAAyB,IAAI;YACxF,gBAAgB;YAChB,MAAM;YACN,QAAQ;YACR,kBAAkB,oBAAoB;YACtC,SAAS;YACT,WAAW;YACX,aAAa;YACb,aAAa;YACb,WAAW;YACX,kBAAkB;YAClB,gBAAgB;QAClB,GAAG,WAAW,GAAE,MAAM,aAAa,CAAC,OAAO;IAC7C;IAEA;;;;GAIC,GACD,gBAAgB,MAAM,qBAAqB,GAAtB,EAA0B,eAAe,WAAW,GAAZ,EAAgB,sBAAsB,GAAE;QACnG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE;QAC1B,MAAM,IAAI,CAAC,GAAG,OAAO,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,GAAG;QACvE,IAAI,CAAC,GAAG,OAAO;QACf,MAAM,EACJ,KAAK,EACL,IAAI,EACJ,MAAM,EACN,gBAAgB,EAChB,SAAS,EACT,OAAO,EACP,WAAW,EACX,WAAW,EACX,SAAS,EACT,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,eAAe,EACf,aAAa,EACb,YAAY,EACb,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,EACJ,OAAO,EACP,gBAAgB,EACjB,GAAG,IAAI,CAAC,KAAK;QAEd,yCAAyC;QACzC,4DAA4D;QAC5D,yEAAyE;QACzE,MAAM,YAAY,OAAO,EAAE,WAAW,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,MAAM,IAAI;QACpF,MAAM,YAAY,OAAO,EAAE,WAAW,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,MAAM,IAAI;QACpF,MAAM,uBAAuB,EAAE,aAAa,IAAI;QAEhD,6EAA6E;QAC7E,MAAM,UAAU,aAAa,aAAa,EAAE,SAAS,KAAK;QAC1D,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,UAAU,OAAO,EAAE;YACzD,gBAAgB;YAChB,MAAM;YACN,QAAQ;YACR,kBAAkB,oBAAoB;YACtC,SAAS;YACT,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,YAAY,IAAI,CAAC,UAAU;YAC3B,aAAa,IAAI,CAAC,WAAW;YAC7B,QAAQ,IAAI,CAAC,MAAM;YACnB,eAAe,IAAI,CAAC,aAAa;YACjC,UAAU,IAAI,CAAC,QAAQ;YACvB,cAAc,IAAI,CAAC,YAAY;YAC/B,aAAa;YACb,aAAa;YACb,WAAW;YACX,kBAAkB,oBAAoB;YACtC,gBAAgB,CAAC;YACjB,gBAAgB;YAChB,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,CAAC;YACN,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,IAAI;YACZ,QAAQ,EAAE,MAAM;YAChB,kBAAkB,iBAAiB,mBAAmB;YACtD,eAAe;YACf,cAAc;QAChB,GAAG;IACL;IACA,SAAS,wBAAwB,GAAE;QACjC,MAAM,EACJ,SAAS,EACT,KAAK,EACL,WAAW,EACX,QAAQ,EACT,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,kBAAkB,CAAC,GAAG,MAAM,OAAO,EAAE,iBAAiB;QAC5D,MAAM,cAAc;YAClB,QAAQ,IAAI,CAAC,eAAe;YAC5B,GAAG,KAAK;QACV;QACA,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,OAAO;YAC7C,KAAK;YACL,WAAW;YACX,OAAO;YACP,QAAQ,cAAc,IAAI,CAAC,MAAM,GAAG,OAAO,IAAI;YAC/C,aAAa,cAAc,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI;YACzD,aAAa,cAAc,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI;YACzD,YAAY,cAAc,IAAI,CAAC,UAAU,GAAG,OAAO,IAAI;QACzD,GAAG,MAAM,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAA,QAAS,IAAI,CAAC,eAAe,CAAC,SAAS,eAAe,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,OAAO,IAAI,CAAC,WAAW;IACzM;AACF;AACA,QAAQ,OAAO,GAAG;AAClB,yDAAyD;AACzD,gBAAgB,iBAAiB,eAAe;AAChD,sDAAsD;AACtD,gBAAgB,iBAAiB,aAAa,0BAA0B,OAAO;AAC/E,gBAAgB,iBAAiB,gBAAgB;IAC/C,UAAU;IACV,MAAM;IACN,WAAW;IACX,OAAO,CAAC;IACR,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,WAAW;IACX,SAAS;IACT,2BAA2B;IAC3B,QAAQ,EAAE;IACV,QAAQ;QAAC;QAAI;KAAG;IAChB,WAAW;IACX,aAAa;IACb,aAAa;IACb,cAAc;IACd,aAAa;IACb,kBAAkB;IAClB,gBAAgB;IAChB,iBAAiB;IACjB,aAAa;IACb,kBAAkB;IAClB,cAAc;QACZ,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,eAAe;QAAC;KAAK;IACrB,gBAAgB,OAAO,IAAI;IAC3B,aAAa,OAAO,IAAI;IACxB,QAAQ,OAAO,IAAI;IACnB,YAAY,OAAO,IAAI;IACvB,eAAe,OAAO,IAAI;IAC1B,UAAU,OAAO,IAAI;IACrB,cAAc,OAAO,IAAI;IACzB,QAAQ,OAAO,IAAI;IACnB,gBAAgB,OAAO,IAAI;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/react-grid-layout/build/responsiveUtils.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.findOrGenerateResponsiveLayout = findOrGenerateResponsiveLayout;\nexports.getBreakpointFromWidth = getBreakpointFromWidth;\nexports.getColsFromBreakpoint = getColsFromBreakpoint;\nexports.sortBreakpoints = sortBreakpoints;\nvar _utils = require(\"./utils\");\n/*:: import type { CompactType, Layout } from \"./utils\";*/\n/*:: export type Breakpoint = string;*/\n/*:: export type DefaultBreakpoints = \"lg\" | \"md\" | \"sm\" | \"xs\" | \"xxs\";*/\n/*:: export type ResponsiveLayout<T: Breakpoint> = {\n  +[breakpoint: T]: Layout\n};*/\n// + indicates read-only\n/*:: export type Breakpoints<T: Breakpoint> = {\n  +[breakpoint: T]: number\n};*/\n/*:: export type OnLayoutChangeCallback = (\n  Layout,\n  { [key: Breakpoint]: Layout }\n) => void;*/\n/**\n * Given a width, find the highest breakpoint that matches is valid for it (width > breakpoint).\n *\n * @param  {Object} breakpoints Breakpoints object (e.g. {lg: 1200, md: 960, ...})\n * @param  {Number} width Screen width.\n * @return {String}       Highest breakpoint that is less than width.\n */\nfunction getBreakpointFromWidth(breakpoints /*: Breakpoints<Breakpoint>*/, width /*: number*/) /*: Breakpoint*/{\n  const sorted = sortBreakpoints(breakpoints);\n  let matching = sorted[0];\n  for (let i = 1, len = sorted.length; i < len; i++) {\n    const breakpointName = sorted[i];\n    if (width > breakpoints[breakpointName]) matching = breakpointName;\n  }\n  return matching;\n}\n\n/**\n * Given a breakpoint, get the # of cols set for it.\n * @param  {String} breakpoint Breakpoint name.\n * @param  {Object} cols       Map of breakpoints to cols.\n * @return {Number}            Number of cols.\n */\nfunction getColsFromBreakpoint(breakpoint /*: Breakpoint*/, cols /*: Breakpoints<Breakpoint>*/) /*: number*/{\n  if (!cols[breakpoint]) {\n    throw new Error(\"ResponsiveReactGridLayout: `cols` entry for breakpoint \" + breakpoint + \" is missing!\");\n  }\n  return cols[breakpoint];\n}\n\n/**\n * Given existing layouts and a new breakpoint, find or generate a new layout.\n *\n * This finds the layout above the new one and generates from it, if it exists.\n *\n * @param  {Object} layouts     Existing layouts.\n * @param  {Array} breakpoints All breakpoints.\n * @param  {String} breakpoint New breakpoint.\n * @param  {String} breakpoint Last breakpoint (for fallback).\n * @param  {Number} cols       Column count at new breakpoint.\n * @param  {Boolean} verticalCompact Whether or not to compact the layout\n *   vertically.\n * @return {Array}             New layout.\n */\nfunction findOrGenerateResponsiveLayout(layouts /*: ResponsiveLayout<Breakpoint>*/, breakpoints /*: Breakpoints<Breakpoint>*/, breakpoint /*: Breakpoint*/, lastBreakpoint /*: Breakpoint*/, cols /*: number*/, compactType /*: CompactType*/) /*: Layout*/{\n  // If it already exists, just return it.\n  if (layouts[breakpoint]) return (0, _utils.cloneLayout)(layouts[breakpoint]);\n  // Find or generate the next layout\n  let layout = layouts[lastBreakpoint];\n  const breakpointsSorted = sortBreakpoints(breakpoints);\n  const breakpointsAbove = breakpointsSorted.slice(breakpointsSorted.indexOf(breakpoint));\n  for (let i = 0, len = breakpointsAbove.length; i < len; i++) {\n    const b = breakpointsAbove[i];\n    if (layouts[b]) {\n      layout = layouts[b];\n      break;\n    }\n  }\n  layout = (0, _utils.cloneLayout)(layout || []); // clone layout so we don't modify existing items\n  return (0, _utils.compact)((0, _utils.correctBounds)(layout, {\n    cols: cols\n  }), compactType, cols);\n}\n\n/**\n * Given breakpoints, return an array of breakpoints sorted by width. This is usually\n * e.g. ['xxs', 'xs', 'sm', ...]\n *\n * @param  {Object} breakpoints Key/value pair of breakpoint names to widths.\n * @return {Array}              Sorted breakpoints.\n */\nfunction sortBreakpoints(breakpoints /*: Breakpoints<Breakpoint>*/) /*: Array<Breakpoint>*/{\n  const keys /*: Array<string>*/ = Object.keys(breakpoints);\n  return keys.sort(function (a, b) {\n    return breakpoints[a] - breakpoints[b];\n  });\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,8BAA8B,GAAG;AACzC,QAAQ,sBAAsB,GAAG;AACjC,QAAQ,qBAAqB,GAAG;AAChC,QAAQ,eAAe,GAAG;AAC1B,IAAI;AACJ,wDAAwD,GACxD,qCAAqC,GACrC,wEAAwE,GACxE;;EAEE,GACF,wBAAwB;AACxB;;EAEE,GACF;;;UAGU,GACV;;;;;;CAMC,GACD,SAAS,uBAAuB,YAAY,2BAA2B,GAA5B,EAAgC,MAAM,UAAU,GAAX,EAAe,cAAc;IAC3G,MAAM,SAAS,gBAAgB;IAC/B,IAAI,WAAW,MAAM,CAAC,EAAE;IACxB,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,MAAM,iBAAiB,MAAM,CAAC,EAAE;QAChC,IAAI,QAAQ,WAAW,CAAC,eAAe,EAAE,WAAW;IACtD;IACA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,sBAAsB,WAAW,cAAc,GAAf,EAAmB,KAAK,2BAA2B,GAA5B,EAAgC,UAAU;IACxG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;QACrB,MAAM,IAAI,MAAM,4DAA4D,aAAa;IAC3F;IACA,OAAO,IAAI,CAAC,WAAW;AACzB;AAEA;;;;;;;;;;;;;CAaC,GACD,SAAS,+BAA+B,QAAQ,gCAAgC,GAAjC,EAAqC,YAAY,2BAA2B,GAA5B,EAAgC,WAAW,cAAc,GAAf,EAAmB,eAAe,cAAc,GAAf,EAAmB,KAAK,UAAU,GAAX,EAAe,YAAY,eAAe,GAAhB,EAAoB,UAAU;IACvP,wCAAwC;IACxC,IAAI,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,OAAO,WAAW,EAAE,OAAO,CAAC,WAAW;IAC3E,mCAAmC;IACnC,IAAI,SAAS,OAAO,CAAC,eAAe;IACpC,MAAM,oBAAoB,gBAAgB;IAC1C,MAAM,mBAAmB,kBAAkB,KAAK,CAAC,kBAAkB,OAAO,CAAC;IAC3E,IAAK,IAAI,IAAI,GAAG,MAAM,iBAAiB,MAAM,EAAE,IAAI,KAAK,IAAK;QAC3D,MAAM,IAAI,gBAAgB,CAAC,EAAE;QAC7B,IAAI,OAAO,CAAC,EAAE,EAAE;YACd,SAAS,OAAO,CAAC,EAAE;YACnB;QACF;IACF;IACA,SAAS,CAAC,GAAG,OAAO,WAAW,EAAE,UAAU,EAAE,GAAG,iDAAiD;IACjG,OAAO,CAAC,GAAG,OAAO,OAAO,EAAE,CAAC,GAAG,OAAO,aAAa,EAAE,QAAQ;QAC3D,MAAM;IACR,IAAI,aAAa;AACnB;AAEA;;;;;;CAMC,GACD,SAAS,gBAAgB,YAAY,2BAA2B,GAA5B,EAAgC,qBAAqB;IACvF,MAAM,KAAK,iBAAiB,MAAK,OAAO,IAAI,CAAC;IAC7C,OAAO,KAAK,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;QAC7B,OAAO,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2419, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/react-grid-layout/build/ResponsiveReactGridLayout.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _fastEquals = require(\"fast-equals\");\nvar _utils = require(\"./utils\");\nvar _responsiveUtils = require(\"./responsiveUtils\");\nvar _ReactGridLayout = _interopRequireDefault(require(\"./ReactGridLayout\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); } /*:: import { type Layout, type Pick } from \"./utils\";*/ /*:: import { type ResponsiveLayout, type OnLayoutChangeCallback, type Breakpoints } from \"./responsiveUtils\";*/\n// $FlowFixMe[method-unbinding]\nconst type = obj => Object.prototype.toString.call(obj);\n\n/**\n * Get a value of margin or containerPadding.\n *\n * @param  {Array | Object} param Margin | containerPadding, e.g. [10, 10] | {lg: [10, 10], ...}.\n * @param  {String} breakpoint   Breakpoint: lg, md, sm, xs and etc.\n * @return {Array}\n */\nfunction getIndentationValue /*:: <T: ?[number, number]>*/(param /*: { [key: string]: T } | T*/, breakpoint /*: string*/) /*: T*/{\n  // $FlowIgnore TODO fix this typedef\n  if (param == null) return null;\n  // $FlowIgnore TODO fix this typedef\n  return Array.isArray(param) ? param : param[breakpoint];\n}\n/*:: type State = {\n  layout: Layout,\n  breakpoint: string,\n  cols: number,\n  layouts?: ResponsiveLayout<string>\n};*/\n/*:: type Props<Breakpoint: string = string> = {|\n  ...React.ElementConfig<typeof ReactGridLayout>,\n\n  // Responsive config\n  breakpoint?: ?Breakpoint,\n  breakpoints: Breakpoints<Breakpoint>,\n  cols: { [key: Breakpoint]: number },\n  layouts: ResponsiveLayout<Breakpoint>,\n  width: number,\n  margin: { [key: Breakpoint]: [number, number] } | [number, number],\n  /* prettier-ignore *-/\n  containerPadding: { [key: Breakpoint]: ?[number, number] } | ?[number, number],\n\n  // Callbacks\n  onBreakpointChange: (Breakpoint, cols: number) => void,\n  onLayoutChange: OnLayoutChangeCallback,\n  onWidthChange: (\n    containerWidth: number,\n    margin: [number, number],\n    cols: number,\n    containerPadding: ?[number, number]\n  ) => void\n|};*/\n/*:: type DefaultProps = Pick<\n  Props<>,\n  {|\n    allowOverlap: 0,\n    breakpoints: 0,\n    cols: 0,\n    containerPadding: 0,\n    layouts: 0,\n    margin: 0,\n    onBreakpointChange: 0,\n    onLayoutChange: 0,\n    onWidthChange: 0\n  |}\n>;*/\nclass ResponsiveReactGridLayout extends React.Component\n/*:: <\n  Props<>,\n  State\n>*/\n{\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", this.generateInitialState());\n    // wrap layouts so we do not need to pass layouts to child\n    _defineProperty(this, \"onLayoutChange\", (layout /*: Layout*/) => {\n      this.props.onLayoutChange(layout, {\n        ...this.props.layouts,\n        [this.state.breakpoint]: layout\n      });\n    });\n  }\n  generateInitialState() /*: State*/{\n    const {\n      width,\n      breakpoints,\n      layouts,\n      cols\n    } = this.props;\n    const breakpoint = (0, _responsiveUtils.getBreakpointFromWidth)(breakpoints, width);\n    const colNo = (0, _responsiveUtils.getColsFromBreakpoint)(breakpoint, cols);\n    // verticalCompact compatibility, now deprecated\n    const compactType = this.props.verticalCompact === false ? null : this.props.compactType;\n    // Get the initial layout. This can tricky; we try to generate one however possible if one doesn't exist\n    // for this layout.\n    const initialLayout = (0, _responsiveUtils.findOrGenerateResponsiveLayout)(layouts, breakpoints, breakpoint, breakpoint, colNo, compactType);\n    return {\n      layout: initialLayout,\n      breakpoint: breakpoint,\n      cols: colNo\n    };\n  }\n  static getDerivedStateFromProps(nextProps /*: Props<*>*/, prevState /*: State*/) /*: ?$Shape<State>*/{\n    if (!(0, _fastEquals.deepEqual)(nextProps.layouts, prevState.layouts)) {\n      // Allow parent to set layouts directly.\n      const {\n        breakpoint,\n        cols\n      } = prevState;\n\n      // Since we're setting an entirely new layout object, we must generate a new responsive layout\n      // if one does not exist.\n      const newLayout = (0, _responsiveUtils.findOrGenerateResponsiveLayout)(nextProps.layouts, nextProps.breakpoints, breakpoint, breakpoint, cols, nextProps.compactType);\n      return {\n        layout: newLayout,\n        layouts: nextProps.layouts\n      };\n    }\n    return null;\n  }\n  componentDidUpdate(prevProps /*: Props<*>*/) {\n    // Allow parent to set width or breakpoint directly.\n    if (this.props.width != prevProps.width || this.props.breakpoint !== prevProps.breakpoint || !(0, _fastEquals.deepEqual)(this.props.breakpoints, prevProps.breakpoints) || !(0, _fastEquals.deepEqual)(this.props.cols, prevProps.cols)) {\n      this.onWidthChange(prevProps);\n    }\n  }\n  /**\n   * When the width changes work through breakpoints and reset state with the new width & breakpoint.\n   * Width changes are necessary to figure out the widget widths.\n   */\n  onWidthChange(prevProps /*: Props<*>*/) {\n    const {\n      breakpoints,\n      cols,\n      layouts,\n      compactType\n    } = this.props;\n    const newBreakpoint = this.props.breakpoint || (0, _responsiveUtils.getBreakpointFromWidth)(this.props.breakpoints, this.props.width);\n    const lastBreakpoint = this.state.breakpoint;\n    const newCols /*: number*/ = (0, _responsiveUtils.getColsFromBreakpoint)(newBreakpoint, cols);\n    const newLayouts = {\n      ...layouts\n    };\n\n    // Breakpoint change\n    if (lastBreakpoint !== newBreakpoint || prevProps.breakpoints !== breakpoints || prevProps.cols !== cols) {\n      // Preserve the current layout if the current breakpoint is not present in the next layouts.\n      if (!(lastBreakpoint in newLayouts)) newLayouts[lastBreakpoint] = (0, _utils.cloneLayout)(this.state.layout);\n\n      // Find or generate a new layout.\n      let layout = (0, _responsiveUtils.findOrGenerateResponsiveLayout)(newLayouts, breakpoints, newBreakpoint, lastBreakpoint, newCols, compactType);\n\n      // This adds missing items.\n      layout = (0, _utils.synchronizeLayoutWithChildren)(layout, this.props.children, newCols, compactType, this.props.allowOverlap);\n\n      // Store the new layout.\n      newLayouts[newBreakpoint] = layout;\n\n      // callbacks\n      this.props.onBreakpointChange(newBreakpoint, newCols);\n      this.props.onLayoutChange(layout, newLayouts);\n      this.setState({\n        breakpoint: newBreakpoint,\n        layout: layout,\n        cols: newCols\n      });\n    }\n    const margin = getIndentationValue(this.props.margin, newBreakpoint);\n    const containerPadding = getIndentationValue(this.props.containerPadding, newBreakpoint);\n\n    //call onWidthChange on every change of width, not only on breakpoint changes\n    this.props.onWidthChange(this.props.width, margin, newCols, containerPadding);\n  }\n  render() /*: React.Element<typeof ReactGridLayout>*/{\n    /* eslint-disable no-unused-vars */\n    const {\n      breakpoint,\n      breakpoints,\n      cols,\n      layouts,\n      margin,\n      containerPadding,\n      onBreakpointChange,\n      onLayoutChange,\n      onWidthChange,\n      ...other\n    } = this.props;\n    /* eslint-enable no-unused-vars */\n\n    return /*#__PURE__*/React.createElement(_ReactGridLayout.default, _extends({}, other, {\n      // $FlowIgnore should allow nullable here due to DefaultProps\n      margin: getIndentationValue(margin, this.state.breakpoint),\n      containerPadding: getIndentationValue(containerPadding, this.state.breakpoint),\n      onLayoutChange: this.onLayoutChange,\n      layout: this.state.layout,\n      cols: this.state.cols\n    }));\n  }\n}\nexports.default = ResponsiveReactGridLayout;\n// This should only include propTypes needed in this code; RGL itself\n// will do validation of the rest props passed to it.\n_defineProperty(ResponsiveReactGridLayout, \"propTypes\", {\n  //\n  // Basic props\n  //\n\n  // Optional, but if you are managing width yourself you may want to set the breakpoint\n  // yourself as well.\n  breakpoint: _propTypes.default.string,\n  // {name: pxVal}, e.g. {lg: 1200, md: 996, sm: 768, xs: 480}\n  breakpoints: _propTypes.default.object,\n  allowOverlap: _propTypes.default.bool,\n  // # of cols. This is a breakpoint -> cols map\n  cols: _propTypes.default.object,\n  // # of margin. This is a breakpoint -> margin map\n  // e.g. { lg: [5, 5], md: [10, 10], sm: [15, 15] }\n  // Margin between items [x, y] in px\n  // e.g. [10, 10]\n  margin: _propTypes.default.oneOfType([_propTypes.default.array, _propTypes.default.object]),\n  // # of containerPadding. This is a breakpoint -> containerPadding map\n  // e.g. { lg: [5, 5], md: [10, 10], sm: [15, 15] }\n  // Padding inside the container [x, y] in px\n  // e.g. [10, 10]\n  containerPadding: _propTypes.default.oneOfType([_propTypes.default.array, _propTypes.default.object]),\n  // layouts is an object mapping breakpoints to layouts.\n  // e.g. {lg: Layout, md: Layout, ...}\n  layouts(props /*: Props<>*/, propName /*: string*/) {\n    if (type(props[propName]) !== \"[object Object]\") {\n      throw new Error(\"Layout property must be an object. Received: \" + type(props[propName]));\n    }\n    Object.keys(props[propName]).forEach(key => {\n      if (!(key in props.breakpoints)) {\n        throw new Error(\"Each key in layouts must align with a key in breakpoints.\");\n      }\n      (0, _utils.validateLayout)(props.layouts[key], \"layouts.\" + key);\n    });\n  },\n  // The width of this component.\n  // Required in this propTypes stanza because generateInitialState() will fail without it.\n  width: _propTypes.default.number.isRequired,\n  //\n  // Callbacks\n  //\n\n  // Calls back with breakpoint and new # cols\n  onBreakpointChange: _propTypes.default.func,\n  // Callback so you can save the layout.\n  // Calls back with (currentLayout, allLayouts). allLayouts are keyed by breakpoint.\n  onLayoutChange: _propTypes.default.func,\n  // Calls back with (containerWidth, margin, cols, containerPadding)\n  onWidthChange: _propTypes.default.func\n});\n_defineProperty(ResponsiveReactGridLayout, \"defaultProps\", {\n  breakpoints: {\n    lg: 1200,\n    md: 996,\n    sm: 768,\n    xs: 480,\n    xxs: 0\n  },\n  cols: {\n    lg: 12,\n    md: 10,\n    sm: 6,\n    xs: 4,\n    xxs: 2\n  },\n  containerPadding: {\n    lg: null,\n    md: null,\n    sm: null,\n    xs: null,\n    xxs: null\n  },\n  layouts: {},\n  margin: [10, 10],\n  allowOverlap: false,\n  onBreakpointChange: _utils.noop,\n  onLayoutChange: _utils.noop,\n  onWidthChange: _utils.noop\n});"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,mBAAmB;AACvB,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AAAG;AAC9F,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAChlB,SAAS;IAAa,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI;IAAuO,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAAY;AAClV,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IAAI,MAAM,eAAe;IAAM,IAAI,OAAO,KAAK;QAAE,OAAO,cAAc,CAAC,KAAK,KAAK;YAAE,OAAO;YAAO,YAAY;YAAM,cAAc;YAAM,UAAU;QAAK;IAAI,OAAO;QAAE,GAAG,CAAC,IAAI,GAAG;IAAO;IAAE,OAAO;AAAK;AAC3O,SAAS,eAAe,GAAG;IAAI,IAAI,MAAM,aAAa,KAAK;IAAW,OAAO,OAAO,QAAQ,WAAW,MAAM,OAAO;AAAM;AAC1H,SAAS,aAAa,KAAK,EAAE,IAAI;IAAI,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM,OAAO;IAAO,IAAI,OAAO,KAAK,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,SAAS,WAAW;QAAE,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,QAAQ;QAAY,IAAI,OAAO,QAAQ,UAAU,OAAO;QAAK,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,SAAS,WAAW,SAAS,MAAM,EAAE;AAAQ,EAAE,sDAAsD,IAAG,8GAA8G;AACjiB,+BAA+B;AAC/B,MAAM,OAAO,CAAA,MAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;AAEnD;;;;;;CAMC,GACD,SAAS,oBAAkD,MAAM,4BAA4B,GAA7B,EAAiC,WAAW,UAAU,GAAX,EAAe,KAAK;IAC7H,oCAAoC;IACpC,IAAI,SAAS,MAAM,OAAO;IAC1B,oCAAoC;IACpC,OAAO,MAAM,OAAO,CAAC,SAAS,QAAQ,KAAK,CAAC,WAAW;AACzD;AACA;;;;;EAKE,GACF;;;;;;;;;;;;;;;;;;;;;;GAsBG,GACH;;;;;;;;;;;;;EAaE,GACF,MAAM,kCAAkC,MAAM,SAAS;IAMrD,aAAc;QACZ,KAAK,IAAI;QACT,gBAAgB,IAAI,EAAE,SAAS,IAAI,CAAC,oBAAoB;QACxD,0DAA0D;QAC1D,gBAAgB,IAAI,EAAE,kBAAkB,CAAC,OAAO,UAAU;YACxD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ;gBAChC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO;gBACrB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;YAC3B;QACF;IACF;IACA,uBAAuB,SAAS,GAAE;QAChC,MAAM,EACJ,KAAK,EACL,WAAW,EACX,OAAO,EACP,IAAI,EACL,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,aAAa,CAAC,GAAG,iBAAiB,sBAAsB,EAAE,aAAa;QAC7E,MAAM,QAAQ,CAAC,GAAG,iBAAiB,qBAAqB,EAAE,YAAY;QACtE,gDAAgD;QAChD,MAAM,cAAc,IAAI,CAAC,KAAK,CAAC,eAAe,KAAK,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW;QACxF,wGAAwG;QACxG,mBAAmB;QACnB,MAAM,gBAAgB,CAAC,GAAG,iBAAiB,8BAA8B,EAAE,SAAS,aAAa,YAAY,YAAY,OAAO;QAChI,OAAO;YACL,QAAQ;YACR,YAAY;YACZ,MAAM;QACR;IACF;IACA,OAAO,yBAAyB,UAAU,YAAY,GAAb,EAAiB,UAAU,SAAS,GAAV,EAAc,kBAAkB,GAAE;QACnG,IAAI,CAAC,CAAC,GAAG,YAAY,SAAS,EAAE,UAAU,OAAO,EAAE,UAAU,OAAO,GAAG;YACrE,wCAAwC;YACxC,MAAM,EACJ,UAAU,EACV,IAAI,EACL,GAAG;YAEJ,8FAA8F;YAC9F,yBAAyB;YACzB,MAAM,YAAY,CAAC,GAAG,iBAAiB,8BAA8B,EAAE,UAAU,OAAO,EAAE,UAAU,WAAW,EAAE,YAAY,YAAY,MAAM,UAAU,WAAW;YACpK,OAAO;gBACL,QAAQ;gBACR,SAAS,UAAU,OAAO;YAC5B;QACF;QACA,OAAO;IACT;IACA,mBAAmB,UAAU,YAAY,GAAb,EAAiB;QAC3C,oDAAoD;QACpD,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,UAAU,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,UAAU,UAAU,IAAI,CAAC,CAAC,GAAG,YAAY,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,UAAU,WAAW,KAAK,CAAC,CAAC,GAAG,YAAY,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,IAAI,GAAG;YACvO,IAAI,CAAC,aAAa,CAAC;QACrB;IACF;IACA;;;GAGC,GACD,cAAc,UAAU,YAAY,GAAb,EAAiB;QACtC,MAAM,EACJ,WAAW,EACX,IAAI,EACJ,OAAO,EACP,WAAW,EACZ,GAAG,IAAI,CAAC,KAAK;QACd,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,GAAG,iBAAiB,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;QACpI,MAAM,iBAAiB,IAAI,CAAC,KAAK,CAAC,UAAU;QAC5C,MAAM,QAAQ,UAAU,MAAK,CAAC,GAAG,iBAAiB,qBAAqB,EAAE,eAAe;QACxF,MAAM,aAAa;YACjB,GAAG,OAAO;QACZ;QAEA,oBAAoB;QACpB,IAAI,mBAAmB,iBAAiB,UAAU,WAAW,KAAK,eAAe,UAAU,IAAI,KAAK,MAAM;YACxG,4FAA4F;YAC5F,IAAI,CAAC,CAAC,kBAAkB,UAAU,GAAG,UAAU,CAAC,eAAe,GAAG,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YAE3G,iCAAiC;YACjC,IAAI,SAAS,CAAC,GAAG,iBAAiB,8BAA8B,EAAE,YAAY,aAAa,eAAe,gBAAgB,SAAS;YAEnI,2BAA2B;YAC3B,SAAS,CAAC,GAAG,OAAO,6BAA6B,EAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,aAAa,IAAI,CAAC,KAAK,CAAC,YAAY;YAE7H,wBAAwB;YACxB,UAAU,CAAC,cAAc,GAAG;YAE5B,YAAY;YACZ,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,eAAe;YAC7C,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ;YAClC,IAAI,CAAC,QAAQ,CAAC;gBACZ,YAAY;gBACZ,QAAQ;gBACR,MAAM;YACR;QACF;QACA,MAAM,SAAS,oBAAoB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACtD,MAAM,mBAAmB,oBAAoB,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;QAE1E,6EAA6E;QAC7E,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,SAAS;IAC9D;IACA,SAAS,yCAAyC,GAAE;QAClD,iCAAiC,GACjC,MAAM,EACJ,UAAU,EACV,WAAW,EACX,IAAI,EACJ,OAAO,EACP,MAAM,EACN,gBAAgB,EAChB,kBAAkB,EAClB,cAAc,EACd,aAAa,EACb,GAAG,OACJ,GAAG,IAAI,CAAC,KAAK;QACd,gCAAgC,GAEhC,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,iBAAiB,OAAO,EAAE,SAAS,CAAC,GAAG,OAAO;YACpF,6DAA6D;YAC7D,QAAQ,oBAAoB,QAAQ,IAAI,CAAC,KAAK,CAAC,UAAU;YACzD,kBAAkB,oBAAoB,kBAAkB,IAAI,CAAC,KAAK,CAAC,UAAU;YAC7E,gBAAgB,IAAI,CAAC,cAAc;YACnC,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;QACvB;IACF;AACF;AACA,QAAQ,OAAO,GAAG;AAClB,qEAAqE;AACrE,qDAAqD;AACrD,gBAAgB,2BAA2B,aAAa;IACtD,EAAE;IACF,cAAc;IACd,EAAE;IAEF,sFAAsF;IACtF,oBAAoB;IACpB,YAAY,WAAW,OAAO,CAAC,MAAM;IACrC,4DAA4D;IAC5D,aAAa,WAAW,OAAO,CAAC,MAAM;IACtC,cAAc,WAAW,OAAO,CAAC,IAAI;IACrC,8CAA8C;IAC9C,MAAM,WAAW,OAAO,CAAC,MAAM;IAC/B,kDAAkD;IAClD,kDAAkD;IAClD,oCAAoC;IACpC,gBAAgB;IAChB,QAAQ,WAAW,OAAO,CAAC,SAAS,CAAC;QAAC,WAAW,OAAO,CAAC,KAAK;QAAE,WAAW,OAAO,CAAC,MAAM;KAAC;IAC1F,sEAAsE;IACtE,kDAAkD;IAClD,4CAA4C;IAC5C,gBAAgB;IAChB,kBAAkB,WAAW,OAAO,CAAC,SAAS,CAAC;QAAC,WAAW,OAAO,CAAC,KAAK;QAAE,WAAW,OAAO,CAAC,MAAM;KAAC;IACpG,uDAAuD;IACvD,qCAAqC;IACrC,SAAQ,MAAM,WAAW,GAAZ,EAAgB,SAAS,UAAU,GAAX;QACnC,IAAI,KAAK,KAAK,CAAC,SAAS,MAAM,mBAAmB;YAC/C,MAAM,IAAI,MAAM,kDAAkD,KAAK,KAAK,CAAC,SAAS;QACxF;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;YACnC,IAAI,CAAC,CAAC,OAAO,MAAM,WAAW,GAAG;gBAC/B,MAAM,IAAI,MAAM;YAClB;YACA,CAAC,GAAG,OAAO,cAAc,EAAE,MAAM,OAAO,CAAC,IAAI,EAAE,aAAa;QAC9D;IACF;IACA,+BAA+B;IAC/B,yFAAyF;IACzF,OAAO,WAAW,OAAO,CAAC,MAAM,CAAC,UAAU;IAC3C,EAAE;IACF,YAAY;IACZ,EAAE;IAEF,4CAA4C;IAC5C,oBAAoB,WAAW,OAAO,CAAC,IAAI;IAC3C,uCAAuC;IACvC,mFAAmF;IACnF,gBAAgB,WAAW,OAAO,CAAC,IAAI;IACvC,mEAAmE;IACnE,eAAe,WAAW,OAAO,CAAC,IAAI;AACxC;AACA,gBAAgB,2BAA2B,gBAAgB;IACzD,aAAa;QACX,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,KAAK;IACP;IACA,MAAM;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,KAAK;IACP;IACA,kBAAkB;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,KAAK;IACP;IACA,SAAS,CAAC;IACV,QAAQ;QAAC;QAAI;KAAG;IAChB,cAAc;IACd,oBAAoB,OAAO,IAAI;IAC/B,gBAAgB,OAAO,IAAI;IAC3B,eAAe,OAAO,IAAI;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2733, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/react-grid-layout/build/components/WidthProvider.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = WidthProvideRGL;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _resizeObserverPolyfill = _interopRequireDefault(require(\"resize-observer-polyfill\"));\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/*:: import type { ReactRef } from \"../ReactGridLayoutPropTypes\";*/\n/*:: type WPDefaultProps = {|\n  measureBeforeMount: boolean\n|};*/\n/*:: type WPProps = {|\n  className?: string,\n  style?: Object,\n  ...WPDefaultProps\n|};*/\n// eslint-disable-next-line no-unused-vars\n/*:: type WPState = {|\n  width: number\n|};*/\n/*:: type ComposedProps<Config> = {|\n  ...Config,\n  measureBeforeMount?: boolean,\n  className?: string,\n  style?: Object,\n  width?: number\n|};*/\nconst layoutClassName = \"react-grid-layout\";\n\n/*\n * A simple HOC that provides facility for listening to container resizes.\n *\n * The Flow type is pretty janky here. I can't just spread `WPProps` into this returned object - I wish I could - but it triggers\n * a flow bug of some sort that causes it to stop typechecking.\n */\nfunction WidthProvideRGL /*:: <Config>*/(ComposedComponent /*: React.AbstractComponent<Config>*/) /*: React.AbstractComponent<ComposedProps<Config>>*/{\n  var _class;\n  return _class = class WidthProvider extends React.Component\n  /*:: <\n      ComposedProps<Config>,\n      WPState\n    >*/\n  {\n    constructor() {\n      super(...arguments);\n      _defineProperty(this, \"state\", {\n        width: 1280\n      });\n      _defineProperty(this, \"elementRef\", /*#__PURE__*/React.createRef());\n      _defineProperty(this, \"mounted\", false);\n      _defineProperty(this, \"resizeObserver\", void 0);\n    }\n    componentDidMount() {\n      this.mounted = true;\n      this.resizeObserver = new _resizeObserverPolyfill.default(entries => {\n        const node = this.elementRef.current;\n        if (node instanceof HTMLElement) {\n          const width = entries[0].contentRect.width;\n          this.setState({\n            width\n          });\n        }\n      });\n      const node = this.elementRef.current;\n      if (node instanceof HTMLElement) {\n        this.resizeObserver.observe(node);\n      }\n    }\n    componentWillUnmount() {\n      this.mounted = false;\n      const node = this.elementRef.current;\n      if (node instanceof HTMLElement) {\n        this.resizeObserver.unobserve(node);\n      }\n      this.resizeObserver.disconnect();\n    }\n    render() {\n      const {\n        measureBeforeMount,\n        ...rest\n      } = this.props;\n      if (measureBeforeMount && !this.mounted) {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: (0, _clsx.default)(this.props.className, layoutClassName),\n          style: this.props.style\n          // $FlowIgnore ref types\n          ,\n          ref: this.elementRef\n        });\n      }\n      return /*#__PURE__*/React.createElement(ComposedComponent, _extends({\n        innerRef: this.elementRef\n      }, rest, this.state));\n    }\n  }, _defineProperty(_class, \"defaultProps\", {\n    measureBeforeMount: false\n  }), _defineProperty(_class, \"propTypes\", {\n    // If true, will not render children until mounted. Useful for getting the exact width before\n    // rendering, to prevent any unsightly resizing.\n    measureBeforeMount: _propTypes.default.bool\n  }), _class;\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,0BAA0B;AAC9B,IAAI,QAAQ;AACZ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AAAG;AAC9F,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAChlB,SAAS;IAAa,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI;IAAuO,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAAY;AAClV,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IAAI,MAAM,eAAe;IAAM,IAAI,OAAO,KAAK;QAAE,OAAO,cAAc,CAAC,KAAK,KAAK;YAAE,OAAO;YAAO,YAAY;YAAM,cAAc;YAAM,UAAU;QAAK;IAAI,OAAO;QAAE,GAAG,CAAC,IAAI,GAAG;IAAO;IAAE,OAAO;AAAK;AAC3O,SAAS,eAAe,GAAG;IAAI,IAAI,MAAM,aAAa,KAAK;IAAW,OAAO,OAAO,QAAQ,WAAW,MAAM,OAAO;AAAM;AAC1H,SAAS,aAAa,KAAK,EAAE,IAAI;IAAI,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM,OAAO;IAAO,IAAI,OAAO,KAAK,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,SAAS,WAAW;QAAE,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,QAAQ;QAAY,IAAI,OAAO,QAAQ,UAAU,OAAO;QAAK,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,SAAS,WAAW,SAAS,MAAM,EAAE;AAAQ;AACxX,iEAAiE,GACjE;;GAEG,GACH;;;;GAIG,GACH,0CAA0C;AAC1C;;GAEG,GACH;;;;;;GAMG,GACH,MAAM,kBAAkB;AAExB;;;;;CAKC,GACD,SAAS,gBAAgC,kBAAkB,mCAAmC,GAApC,EAAwC,kDAAkD;IAClJ,IAAI;IACJ,OAAO,SAAS,MAAM,sBAAsB,MAAM,SAAS;QAMzD,aAAc;YACZ,KAAK,IAAI;YACT,gBAAgB,IAAI,EAAE,SAAS;gBAC7B,OAAO;YACT;YACA,gBAAgB,IAAI,EAAE,cAAc,WAAW,GAAE,MAAM,SAAS;YAChE,gBAAgB,IAAI,EAAE,WAAW;YACjC,gBAAgB,IAAI,EAAE,kBAAkB,KAAK;QAC/C;QACA,oBAAoB;YAClB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,cAAc,GAAG,IAAI,wBAAwB,OAAO,CAAC,CAAA;gBACxD,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO;gBACpC,IAAI,gBAAgB,aAAa;oBAC/B,MAAM,QAAQ,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK;oBAC1C,IAAI,CAAC,QAAQ,CAAC;wBACZ;oBACF;gBACF;YACF;YACA,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO;YACpC,IAAI,gBAAgB,aAAa;gBAC/B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC9B;QACF;QACA,uBAAuB;YACrB,IAAI,CAAC,OAAO,GAAG;YACf,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO;YACpC,IAAI,gBAAgB,aAAa;gBAC/B,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAChC;YACA,IAAI,CAAC,cAAc,CAAC,UAAU;QAChC;QACA,SAAS;YACP,MAAM,EACJ,kBAAkB,EAClB,GAAG,MACJ,GAAG,IAAI,CAAC,KAAK;YACd,IAAI,sBAAsB,CAAC,IAAI,CAAC,OAAO,EAAE;gBACvC,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,OAAO;oBAC7C,WAAW,CAAC,GAAG,MAAM,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;oBACpD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAGvB,KAAK,IAAI,CAAC,UAAU;gBACtB;YACF;YACA,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,mBAAmB,SAAS;gBAClE,UAAU,IAAI,CAAC,UAAU;YAC3B,GAAG,MAAM,IAAI,CAAC,KAAK;QACrB;IACF,GAAG,gBAAgB,QAAQ,gBAAgB;QACzC,oBAAoB;IACtB,IAAI,gBAAgB,QAAQ,aAAa;QACvC,6FAA6F;QAC7F,gDAAgD;QAChD,oBAAoB,WAAW,OAAO,CAAC,IAAI;IAC7C,IAAI;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2885, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/node_modules/react-grid-layout/index.js"], "sourcesContent": ["module.exports = require(\"./build/ReactGridLayout\").default;\nmodule.exports.utils = require(\"./build/utils\");\nmodule.exports.calculateUtils = require(\"./build/calculateUtils\");\nmodule.exports.Responsive =\n  require(\"./build/ResponsiveReactGridLayout\").default;\nmodule.exports.Responsive.utils = require(\"./build/responsiveUtils\");\nmodule.exports.WidthProvider =\n  require(\"./build/components/WidthProvider\").default;\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,uHAAmC,OAAO;AAC3D,OAAO,OAAO,CAAC,KAAK;AACpB,OAAO,OAAO,CAAC,cAAc;AAC7B,OAAO,OAAO,CAAC,UAAU,GACvB,iIAA6C,OAAO;AACtD,OAAO,OAAO,CAAC,UAAU,CAAC,KAAK;AAC/B,OAAO,OAAO,CAAC,aAAa,GAC1B,gIAA4C,OAAO", "ignoreList": [0], "debugId": null}}]}