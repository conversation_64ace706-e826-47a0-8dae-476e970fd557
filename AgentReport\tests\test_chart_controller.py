"""Integration Tests for Chart Selection API Controller

This module contains integration tests for the chart selection API endpoints,
including authentication, request/response validation, and end-to-end workflow testing.
"""

import pytest
import json
from datetime import datetime
from unittest.mock import AsyncMock, patch

from fastapi.testclient import TestClient
from fastapi import status

from app.main import app
from app.models.user import User
from app.models.chart import ChartType, ChartQueryRequest
from app.utils.security import get_current_user


def _create_test_user() -> User:
    """Create a test user for authentication override."""
    return User(
        id="test_user_123",
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Test User",
        is_active=True,
        is_email_verified=True,
        role="user",
        auth_provider="local",
        auth_provider_id=None,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        last_login=None,
        profile_picture=None,
        settings={}
    )


@pytest.fixture(autouse=True)
def override_auth_dependency():
    """Override authentication dependency for testing."""
    app.dependency_overrides[get_current_user] = _create_test_user
    yield
    app.dependency_overrides.pop(get_current_user, None)


@pytest.fixture(scope="module")
def client():
    """Create a test client for the FastAPI app."""
    with TestClient(app) as test_client:
        yield test_client


class TestChartControllerIntegration:
    """Integration test suite for chart controller endpoints."""
    
    def test_query_chart_success(self, client):
        """Test successful chart query endpoint."""
        request_data = {
            "prompt": "Show user registrations by month",
            "user_id": "test_user_123"
        }
        
        with patch('app.services.chart_service.ChartSelectionService.process_chart_query', new_callable=AsyncMock) as mock_service:
            # Mock successful service response
            from app.models.chart import ChartQueryResponse, ChartData, ChartDataPoint, ChartMetadata
            
            mock_response = ChartQueryResponse(
                success=True,
                data=ChartData(
                    title="User Registrations by Month",
                    chartType=ChartType.LINE,
                    data=[
                        ChartDataPoint(label="Jan", value=120),
                        ChartDataPoint(label="Feb", value=150),
                        ChartDataPoint(label="Mar", value=180)
                    ],
                    metadata=ChartMetadata(
                        xAxisLabel="Month",
                        yAxisLabel="Registrations",
                        colors=["#8b5cf6", "#06b6d4"],
                        description="Time-based trend analysis"
                    )
                )
            )
            mock_service.return_value = mock_response
            
            response = client.post("/chart/query", json=request_data)
            
            assert response.status_code == status.HTTP_200_OK
            
            response_data = response.json()
            assert response_data["success"] is True
            assert response_data["data"] is not None
            assert response_data["data"]["chartType"] == "line"
            assert response_data["data"]["title"] == "User Registrations by Month"
            assert len(response_data["data"]["data"]) == 3
            assert response_data["data"]["metadata"]["xAxisLabel"] == "Month"
    
    def test_query_chart_service_error(self, client):
        """Test chart query endpoint when service returns error."""
        request_data = {
            "prompt": "Show invalid data",
            "user_id": "test_user_123"
        }
        
        with patch('app.services.chart_service.ChartSelectionService.process_chart_query', new_callable=AsyncMock) as mock_service:
            # Mock service error response
            from app.models.chart import ChartQueryResponse
            
            mock_response = ChartQueryResponse(
                success=False,
                error="Failed to process query: Invalid data source"
            )
            mock_service.return_value = mock_response
            
            response = client.post("/chart/query", json=request_data)
            
            assert response.status_code == status.HTTP_200_OK
            
            response_data = response.json()
            assert response_data["success"] is False
            assert response_data["error"] == "Failed to process query: Invalid data source"
            assert response_data["data"] is None
    
    def test_query_chart_invalid_request(self, client):
        """Test chart query endpoint with invalid request data."""
        # Missing required prompt field
        request_data = {
            "user_id": "test_user_123"
        }
        
        response = client.post("/chart/query", json=request_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_query_chart_empty_prompt(self, client):
        """Test chart query endpoint with empty prompt."""
        request_data = {
            "prompt": "",
            "user_id": "test_user_123"
        }
        
        response = client.post("/chart/query", json=request_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_query_chart_prompt_too_long(self, client):
        """Test chart query endpoint with prompt that's too long."""
        request_data = {
            "prompt": "x" * 4001,  # Exceeds 4000 character limit
            "user_id": "test_user_123"
        }
        
        response = client.post("/chart/query", json=request_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_query_chart_without_user_id(self, client):
        """Test chart query endpoint without user_id (should use authenticated user)."""
        request_data = {
            "prompt": "Show sales data"
        }
        
        with patch('app.services.chart_service.ChartSelectionService.process_chart_query', new_callable=AsyncMock) as mock_service:
            from app.models.chart import ChartQueryResponse, ChartData, ChartDataPoint, ChartMetadata
            
            mock_response = ChartQueryResponse(
                success=True,
                data=ChartData(
                    title="Sales Data",
                    chartType=ChartType.BAR,
                    data=[ChartDataPoint(label="Q1", value=1000)],
                    metadata=ChartMetadata()
                )
            )
            mock_service.return_value = mock_response
            
            response = client.post("/chart/query", json=request_data)
            
            assert response.status_code == status.HTTP_200_OK
            
            # Verify that the service was called with the authenticated user's ID
            mock_service.assert_called_once()
            call_args = mock_service.call_args[0][0]  # First argument (ChartQueryRequest)
            assert call_args.user_id == "test_user_123"
    
    def test_get_supported_chart_types(self, client):
        """Test get supported chart types endpoint."""
        response = client.get("/chart/types")
        
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert "supported_types" in response_data
        assert "descriptions" in response_data
        assert "default_colors" in response_data
        
        # Check that all expected chart types are present
        expected_types = ["bar", "line", "pie", "area", "table", "timebar", "funnel", "number", "image", "detail", "text", "activity"]
        assert all(chart_type in response_data["supported_types"] for chart_type in expected_types)
        
        # Check that descriptions are provided for each type
        for chart_type in response_data["supported_types"]:
            assert chart_type in response_data["descriptions"]
            assert len(response_data["descriptions"][chart_type]) > 0
        
        # Check default colors
        assert len(response_data["default_colors"]) == 5
        assert all(color.startswith("#") for color in response_data["default_colors"])
    
    def test_validate_chart_query_valid(self, client):
        """Test chart query validation endpoint with valid query."""
        request_data = {
            "prompt": "Show monthly sales trends",
            "user_id": "test_user_123"
        }
        
        with patch('app.services.chart_service.ChartSelectionService._get_chart_type_recommendation', new_callable=AsyncMock) as mock_recommendation:
            from app.models.chart import ChartTypeRecommendation
            
            mock_recommendation.return_value = ChartTypeRecommendation(
                chart_type=ChartType.LINE,
                confidence=0.95,
                reasoning="Time-based trend analysis",
                alternative_types=[ChartType.BAR, ChartType.AREA]
            )
            
            response = client.post("/chart/validate", json=request_data)
            
            assert response.status_code == status.HTTP_200_OK
            
            response_data = response.json()
            assert response_data["valid"] is True
            assert response_data["recommended_chart_type"] == "line"
            assert response_data["confidence"] == 0.95
            assert "trend analysis" in response_data["reasoning"]
            assert "bar" in response_data["alternative_types"]
            assert "area" in response_data["alternative_types"]
    
    def test_validate_chart_query_empty_prompt(self, client):
        """Test chart query validation endpoint with empty prompt."""
        request_data = {
            "prompt": "",
            "user_id": "test_user_123"
        }

        response = client.post("/chart/validate", json=request_data)

        # Empty prompt should trigger Pydantic validation error (422)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        response_data = response.json()
        assert "detail" in response_data
        # Check that the validation error mentions the prompt field
        assert any("prompt" in str(error).lower() for error in response_data["detail"])
    
    def test_validate_chart_query_too_long(self, client):
        """Test chart query validation endpoint with prompt that's too long."""
        request_data = {
            "prompt": "x" * 4001,
            "user_id": "test_user_123"
        }

        response = client.post("/chart/validate", json=request_data)

        # Too long prompt should trigger Pydantic validation error (422)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        response_data = response.json()
        assert "detail" in response_data
        # Check that the validation error mentions the prompt field
        assert any("prompt" in str(error).lower() for error in response_data["detail"])
    
    def test_validate_chart_query_service_error(self, client):
        """Test chart query validation endpoint when service fails."""
        request_data = {
            "prompt": "Show data",
            "user_id": "test_user_123"
        }
        
        with patch('app.services.chart_service.ChartSelectionService._get_chart_type_recommendation', new_callable=AsyncMock) as mock_recommendation:
            mock_recommendation.side_effect = Exception("Service unavailable")
            
            response = client.post("/chart/validate", json=request_data)
            
            assert response.status_code == status.HTTP_200_OK
            
            response_data = response.json()
            assert response_data["valid"] is False
            assert "validation failed" in response_data["error"].lower()
            assert response_data["recommended_chart_type"] is None
    
    def test_chart_service_health(self, client):
        """Test chart service health check endpoint."""
        response = client.get("/chart/health")
        
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert "status" in response_data
        assert "service" in response_data
        assert response_data["service"] == "chart_selection"
        assert "dependencies" in response_data
    
    def test_chart_endpoints_require_authentication(self, client):
        """Test that chart endpoints require authentication."""
        # Remove the auth override
        app.dependency_overrides.pop(get_current_user, None)
        
        try:
            # Test chart query endpoint
            response = client.post("/chart/query", json={"prompt": "test"})
            assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]
            
            # Test chart types endpoint
            response = client.get("/chart/types")
            assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]
            
            # Test validation endpoint
            response = client.post("/chart/validate", json={"prompt": "test"})
            assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]
            
        finally:
            # Restore auth override
            app.dependency_overrides[get_current_user] = _create_test_user
    
    def test_chart_query_end_to_end(self, client):
        """Test complete end-to-end chart query workflow."""
        request_data = {
            "prompt": "Show quarterly revenue breakdown by product category",
            "dashboard_id": "dashboard_123"
        }
        
        # This test uses the actual service (no mocking) to test the full integration
        response = client.post("/chart/query", json=request_data)
        
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["success"] is True
        assert response_data["data"] is not None
        
        chart_data = response_data["data"]
        assert "title" in chart_data
        assert "chartType" in chart_data
        assert "data" in chart_data
        assert "metadata" in chart_data
        
        # Verify chart type is appropriate for the query
        assert chart_data["chartType"] in ["pie", "bar", "area"]  # Appropriate for breakdown/category data
        
        # Verify data structure
        assert len(chart_data["data"]) > 0
        for data_point in chart_data["data"]:
            assert "label" in data_point
            assert "value" in data_point
            assert isinstance(data_point["value"], (int, float))
        
        # Verify metadata
        metadata = chart_data["metadata"]
        assert "colors" in metadata
        assert "dataSource" in metadata
        assert "generatedAt" in metadata
