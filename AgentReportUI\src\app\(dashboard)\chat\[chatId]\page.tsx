"use client";
import React from 'react';
import Layout from '@/components/layout/Layout';
import Chat from '@/components/features/chat/Chat';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

interface ChatPageProps {
  params: Promise<{
    chatId: string;
  }>;
}

export default function ChatPage({ params }: ChatPageProps) {
  // Use React.use() to unwrap the params Promise
  const { chatId } = React.use(params);

  return (
    <ProtectedRoute>
      <Layout>
        <Chat chatId={chatId} />
      </Layout>
    </ProtectedRoute>
  );
}