"""
Table Discussion Agent
=====================

Specialized agent for advanced table discussions, exploration, and metadata analysis.
Handles queries about table structures, relationships, and comparative analysis.
"""

import logging
import json
import re
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime

from app.agents.base import Agent, AgentResponse
from app.services.table_exploration_service import (
    table_exploration_service, 
    ExplorationDepth,
    TableExplorationResult
)
from app.services.conversational_context_service import conversational_context_service
from app.utils.bedrock_client import BedrockClient

logger = logging.getLogger(__name__)


class TableDiscussionAgent(Agent):
    """Agent specialized in table structure discussions and exploration."""

    def __init__(self):
        self.agent_id = "table_discussion_agent"
        self.bedrock_client = BedrockClient()
        self.initialized = False

    async def initialize(self) -> None:
        """Initialize the table discussion agent."""
        self.initialized = True
        logger.info("Table Discussion Agent initialized")
    
    async def process(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process table discussion queries.
        
        Expected message format:
        {
            "query": "user's question about tables",
            "database_id": "database_identifier",
            "table_names": ["table1", "table2"],  # Optional specific tables
            "session_id": "session_identifier",
            "user_id": "user_identifier",
            "exploration_depth": "basic|detailed|comprehensive|analytical"
        }
        """
        if not self.initialized:
            await self.initialize()
        
        query = message.get("query", "")
        database_id = message.get("database_id")
        table_names = message.get("table_names", [])
        session_id = message.get("session_id")
        user_id = message.get("user_id")
        exploration_depth = message.get("exploration_depth", "detailed")
        
        if not query or not database_id:
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error="Query and database_id are required"
            ).to_dict()
        
        try:
            # Analyze the query to determine intent
            query_intent = await self._analyze_table_query_intent(query)
            
            # Get conversational context
            conversation_context = {}
            if session_id:
                conversation_context = await conversational_context_service.extract_context_for_query(
                    session_id, query
                )
            
            # Determine which tables to explore
            target_tables = await self._determine_target_tables(
                query, database_id, table_names, conversation_context
            )
            
            if not target_tables:
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=False,
                    data={
                        "message": "I couldn't identify which tables you'd like to explore. Could you specify the table names?",
                        "suggestions": ["Try asking about specific table names", "Use 'show me all tables' to see available tables"]
                    }
                ).to_dict()
            
            # Perform table exploration based on intent
            exploration_results = []
            depth = ExplorationDepth(exploration_depth)
            
            for table_name in target_tables:
                try:
                    result = await table_exploration_service.explore_table(
                        database_id=database_id,
                        table_name=table_name,
                        depth=depth,
                        session_id=session_id,
                        user_id=user_id
                    )
                    exploration_results.append(result)
                except Exception as e:
                    logger.warning(f"Failed to explore table {table_name}: {e}")
            
            if not exploration_results:
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=False,
                    data={
                        "message": "I couldn't explore the requested tables. They might not exist or be accessible.",
                        "target_tables": target_tables
                    }
                ).to_dict()
            
            # Generate response based on intent
            response_data = await self._generate_table_discussion_response(
                query, query_intent, exploration_results, conversation_context
            )
            
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=True,
                data=response_data
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Error in table discussion processing: {e}")
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error=f"Table discussion processing failed: {str(e)}"
            ).to_dict()
    
    async def _analyze_table_query_intent(self, query: str) -> Dict[str, Any]:
        """Analyze the intent of a table-related query."""
        
        query_lower = query.lower()
        
        intent = {
            "primary_intent": "explore",
            "specific_intents": [],
            "comparison_requested": False,
            "relationship_focus": False,
            "structure_focus": False,
            "metadata_focus": False
        }
        
        # Structure-related keywords
        structure_keywords = ["columns", "structure", "schema", "fields", "attributes", "layout"]
        if any(keyword in query_lower for keyword in structure_keywords):
            intent["specific_intents"].append("structure")
            intent["structure_focus"] = True
        
        # Relationship-related keywords
        relationship_keywords = ["relationships", "connections", "foreign key", "references", "linked", "related"]
        if any(keyword in query_lower for keyword in relationship_keywords):
            intent["specific_intents"].append("relationships")
            intent["relationship_focus"] = True
        
        # Comparison keywords
        comparison_keywords = ["compare", "difference", "similar", "versus", "vs", "between"]
        if any(keyword in query_lower for keyword in comparison_keywords):
            intent["comparison_requested"] = True
            intent["specific_intents"].append("comparison")
        
        # Metadata keywords
        metadata_keywords = ["metadata", "information", "details", "properties", "size", "count"]
        if any(keyword in query_lower for keyword in metadata_keywords):
            intent["specific_intents"].append("metadata")
            intent["metadata_focus"] = True
        
        # Data quality keywords
        quality_keywords = ["quality", "completeness", "nullable", "constraints", "primary key"]
        if any(keyword in query_lower for keyword in quality_keywords):
            intent["specific_intents"].append("data_quality")
        
        # Pattern analysis keywords
        pattern_keywords = ["patterns", "naming", "conventions", "standards"]
        if any(keyword in query_lower for keyword in pattern_keywords):
            intent["specific_intents"].append("patterns")
        
        # Set primary intent based on specific intents
        if "comparison" in intent["specific_intents"]:
            intent["primary_intent"] = "compare"
        elif "relationships" in intent["specific_intents"]:
            intent["primary_intent"] = "analyze_relationships"
        elif "patterns" in intent["specific_intents"]:
            intent["primary_intent"] = "discover_patterns"
        elif "structure" in intent["specific_intents"]:
            intent["primary_intent"] = "explore_structure"
        
        return intent
    
    async def _determine_target_tables(
        self,
        query: str,
        database_id: str,
        explicit_tables: List[str],
        conversation_context: Dict[str, Any]
    ) -> List[str]:
        """Determine which tables the user wants to explore."""
        
        target_tables = []
        
        # Use explicitly provided tables first
        if explicit_tables:
            target_tables.extend(explicit_tables)
        
        # Extract table names from query using regex
        table_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s+table\b'
        matches = re.findall(table_pattern, query, re.IGNORECASE)
        target_tables.extend(matches)
        
        # Check conversation context for recently discussed tables
        if conversation_context:
            tables_discussed = conversation_context.get("tables_discussed", {})
            current_focus = conversation_context.get("current_focus")
            
            if current_focus and current_focus not in target_tables:
                # Extract table name from current focus (format: "database_id.table_name")
                if "." in current_focus:
                    table_name = current_focus.split(".", 1)[1]
                    target_tables.append(table_name)
            
            # Add recently discussed tables if query is vague
            if not target_tables and len(query.split()) < 5:
                for table_key, table_info in tables_discussed.items():
                    table_name = table_info.get("table_name", "")
                    if table_name and table_name not in target_tables:
                        target_tables.append(table_name)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_tables = []
        for table in target_tables:
            if table not in seen:
                seen.add(table)
                unique_tables.append(table)
        
        return unique_tables[:5]  # Limit to 5 tables for performance
    
    async def _generate_table_discussion_response(
        self,
        query: str,
        intent: Dict[str, Any],
        exploration_results: List[TableExplorationResult],
        conversation_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate a comprehensive response for table discussion."""
        
        response_data = {
            "message": "",
            "tables_explored": [result.table_name for result in exploration_results],
            "exploration_depth": exploration_results[0].exploration_depth.value if exploration_results else "basic",
            "insights": [],
            "recommendations": [],
            "follow_up_suggestions": []
        }
        
        # Generate main conversational response
        main_response = await table_exploration_service.generate_table_discussion_response(
            query, exploration_results, conversation_context
        )
        response_data["message"] = main_response
        
        # Add specific insights based on intent
        if intent["comparison_requested"] and len(exploration_results) > 1:
            comparison_result = await table_exploration_service.compare_tables(
                exploration_results[0].database_id,
                [result.table_name for result in exploration_results]
            )
            response_data["comparison_analysis"] = comparison_result
            response_data["insights"].extend(comparison_result.get("insights", []))
        
        # Add relationship insights
        if intent["relationship_focus"]:
            relationship_insights = []
            for result in exploration_results:
                for rel in result.relationships:
                    relationship_insights.append({
                        "from_table": rel.from_table,
                        "to_table": rel.to_table,
                        "type": rel.relationship_type.value,
                        "description": rel.description,
                        "strength": rel.strength
                    })
            response_data["relationship_insights"] = relationship_insights
        
        # Add structure details
        if intent["structure_focus"]:
            structure_details = []
            for result in exploration_results:
                if result.detailed_info:
                    structure_details.append({
                        "table_name": result.table_name,
                        "columns": result.detailed_info["columns"],
                        "data_type_distribution": result.detailed_info["data_type_distribution"]
                    })
            response_data["structure_details"] = structure_details
        
        # Generate follow-up suggestions
        follow_ups = await self._generate_table_follow_up_suggestions(
            query, intent, exploration_results
        )
        response_data["follow_up_suggestions"] = follow_ups
        
        # Add recommendations
        recommendations = self._generate_table_recommendations(exploration_results, intent)
        response_data["recommendations"] = recommendations
        
        return response_data
    
    async def _generate_table_follow_up_suggestions(
        self,
        query: str,
        intent: Dict[str, Any],
        exploration_results: List[TableExplorationResult]
    ) -> List[str]:
        """Generate follow-up suggestions for table exploration."""
        
        suggestions = []
        table_names = [result.table_name for result in exploration_results]
        
        # Structure-based suggestions
        if intent["structure_focus"]:
            suggestions.extend([
                f"Show me sample data from {table_names[0]}",
                f"What are the data types in {table_names[0]}?",
                "Are there any nullable columns I should know about?"
            ])
        
        # Relationship-based suggestions
        if intent["relationship_focus"]:
            suggestions.extend([
                f"What tables are connected to {table_names[0]}?",
                "Show me the foreign key relationships",
                "Are there any potential missing relationships?"
            ])
        
        # Comparison suggestions
        if len(exploration_results) > 1:
            suggestions.extend([
                f"Compare the structures of {table_names[0]} and {table_names[1]}",
                "Which table has more data?",
                "Do these tables have similar column patterns?"
            ])
        
        # General exploration suggestions
        suggestions.extend([
            "What insights can you find in this data?",
            "Are there any data quality issues?",
            "Show me patterns across all tables"
        ])
        
        return suggestions[:5]  # Limit to 5 suggestions
    
    def _generate_table_recommendations(
        self,
        exploration_results: List[TableExplorationResult],
        intent: Dict[str, Any]
    ) -> List[str]:
        """Generate recommendations based on table exploration."""
        
        recommendations = []
        
        for result in exploration_results:
            # Primary key recommendations
            if not result.basic_info["has_primary_key"]:
                recommendations.append(
                    f"Consider adding a primary key to {result.table_name} for better data integrity"
                )
            
            # Large table recommendations
            row_count = result.basic_info.get("row_count", 0)
            if row_count > 1000000:
                recommendations.append(
                    f"{result.table_name} is very large ({row_count:,} rows) - consider indexing for performance"
                )
            
            # Relationship recommendations
            if result.basic_info["foreign_key_count"] == 0 and len(result.relationships) > 0:
                recommendations.append(
                    f"Consider formalizing the potential relationships in {result.table_name} with foreign keys"
                )
            
            # Add insights as recommendations
            for insight in result.insights:
                recommendations.extend(insight.recommendations)
        
        return recommendations[:5]  # Limit to 5 recommendations
