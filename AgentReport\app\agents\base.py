"""
Base Agent Classes
─────────────────
Common interfaces and response formats for all agents.
"""

from __future__ import annotations
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime

class Agent(ABC):
    """Base agent interface."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the agent."""
        pass

    @abstractmethod
    async def process(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process a message and return results."""
        pass


    
class AgentResponse:
    """
    Standardized response format for all agents.
    
    This provides a rich, structured format that allows the orchestrator to:
    - Extract specific results and context
    - Store relevant information in memory
    - Pass appropriate context between agents
    - Handle errors and status properly
    """
    
    def __init__(
        self,
        agent_id: str,
        has_relevant_info: bool,
        # Main results
        data: Optional[Dict[str, Any]] = None,
        
        # Structured outputs for orchestrator parsing
        result_summary: Optional[str] = None,
        key_results: Optional[Dict[str, Any]] = None,
        
        # Context and memory
        context_for_next_agent: Optional[Dict[str, Any]] = None,
        memory_items: Optional[List[Dict[str, Any]]] = None,
        
        # Status and errors
        status: str = "completed",
        error: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None,
        
        # Metadata
        metadata: Optional[Dict[str, Any]] = None,
        execution_time: Optional[float] = None,
        output_files: Optional[List[Dict[str, str]]] = None
    ):
        self.agent_id = agent_id
        self.has_relevant_info = has_relevant_info
        self.data = data or {}
        
        # Structured outputs
        self.result_summary = result_summary
        self.key_results = key_results or {}
        
        # Context and memory
        self.context_for_next_agent = context_for_next_agent or {}
        self.memory_items = memory_items or []
        
        # Status and errors
        self.status = status  # "completed", "error", "partial", "retry_needed"
        self.error = error
        self.error_details = error_details or {}
        
        # Metadata
        self.metadata = metadata or {}
        self.execution_time = execution_time
        self.output_files = output_files or []
        self.timestamp = datetime.utcnow().isoformat()
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the response to a standardized dictionary format."""
        return {
            # Basic info
            "agent_id": self.agent_id,
            "has_relevant_info": self.has_relevant_info,
            "status": self.status,
            "timestamp": self.timestamp,
            
            # Main data (legacy compatibility)
            "data": self.data,
            
            # Structured outputs for orchestrator
            "result_summary": self.result_summary,
            "key_results": self.key_results,
            
            # Context passing
            "context_for_next_agent": self.context_for_next_agent,
            "memory_items": self.memory_items,
            
            # Error handling
            "error": self.error,
            "error_details": self.error_details,
            
            # Metadata
            "metadata": self.metadata,
            "execution_time": self.execution_time,
            "output_files": self.output_files
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentResponse':
        """Create an AgentResponse from a dictionary."""
        return cls(
            agent_id=data.get("agent_id", ""),
            has_relevant_info=data.get("has_relevant_info", False),
            data=data.get("data", {}),
            result_summary=data.get("result_summary"),
            key_results=data.get("key_results", {}),
            context_for_next_agent=data.get("context_for_next_agent", {}),
            memory_items=data.get("memory_items", []),
            status=data.get("status", "completed"),
            error=data.get("error"),
            error_details=data.get("error_details", {}),
            metadata=data.get("metadata", {}),
            execution_time=data.get("execution_time"),
            output_files=data.get("output_files", [])
        )
        
    def is_successful(self) -> bool:
        """Check if the agent execution was successful."""
        return self.status in ["completed", "partial"] and self.error is None
        
    def needs_retry(self) -> bool:
        """Check if this agent result indicates a retry is needed."""
        return self.status == "retry_needed"
        
    def has_errors(self) -> bool:
        """Check if there were errors during execution."""
        return self.error is not None or self.status == "error"

    @classmethod
    def success(cls, agent_id: str, **kwargs) -> 'AgentResponse':
        """Create a successful response."""
        return cls(agent_id=agent_id, has_relevant_info=True, status="completed", **kwargs)
    
    @classmethod 
    def error(cls, agent_id: str, error_message: str, **kwargs) -> 'AgentResponse':
        """Create an error response."""
        return cls(
            agent_id=agent_id, 
            has_relevant_info=False, 
            status="error",
            error=error_message,
            **kwargs
        )
    
    @classmethod
    def retry_needed(cls, agent_id: str, error_message: str, **kwargs) -> 'AgentResponse':
        """Create a response indicating retry is needed."""
        return cls(
            agent_id=agent_id,
            has_relevant_info=False,
            status="retry_needed", 
            error=error_message,
            **kwargs
        )


class StandardizedAgentOutputs:
    """
    Standard output formats for different agent types.
    These provide templates for consistent structured outputs.
    """
    
    @staticmethod
    def data_analysis_output(
        analysis_type: str,
        analysis_goal: str,
        analysis_steps: List[str],
        techniques: List[str],
        user_intent: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Standard output format for data analysis agent."""
        return {
            "analysis_type": analysis_type,
            "analysis_goal": analysis_goal,
            "analysis_steps": analysis_steps,
            "recommended_techniques": techniques,
            "user_intent": user_intent,
            **kwargs
        }
    
    @staticmethod
    def code_generation_output(
        code_type: str,
        code_sections: Dict[str, str],
        dependencies: List[str],
        complexity_score: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Standard output format for code generation agent."""
        return {
            "code_type": code_type,
            "code_sections": code_sections,
            "dependencies": dependencies,
            "complexity_score": complexity_score,
            "code_length": sum(len(code) for code in code_sections.values()),
            **kwargs
        }
    
    @staticmethod
    def code_execution_output(
        return_code: int,
        execution_success: bool,
        stdout: str,
        stderr: str,
        execution_time: float,
        analysis_results: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Standard output format for code execution agent."""
        return {
            "return_code": return_code,
            "execution_success": execution_success,
            "stdout": stdout,
            "stderr": stderr,
            "execution_time": execution_time,
            "analysis_results": analysis_results or {},
            **kwargs
        }
    
    @staticmethod
    def dashboard_output(
        dashboard_url: str,
        dashboard_id: str,
        component_count: int,
        dashboard_type: str = "interactive",
        **kwargs
    ) -> Dict[str, Any]:
        """Standard output format for dashboard agent."""
        return {
            "dashboard_url": dashboard_url,
            "dashboard_id": dashboard_id,
            "component_count": component_count,
            "dashboard_type": dashboard_type,
            **kwargs
        }


class AgentMemoryItem:
    """
    Represents an item to be stored in agent memory/context.
    """
    
    def __init__(
        self,
        item_type: str,  # "analysis_plan", "code", "results", "error", "insight", etc.
        content: Any,
        importance: str = "medium",  # "high", "medium", "low"
        context_hint: Optional[str] = None
    ):
        self.item_type = item_type
        self.content = content
        self.importance = importance
        self.context_hint = context_hint
        self.timestamp = datetime.utcnow().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "item_type": self.item_type,
            "content": self.content,
            "importance": self.importance,
            "context_hint": self.context_hint,
            "timestamp": self.timestamp
        } 