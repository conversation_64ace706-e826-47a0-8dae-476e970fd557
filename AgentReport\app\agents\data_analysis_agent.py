"""
Data-Analysis-Agent v2
──────────────────────
Produces *rich* analysis-plans with explicit requirements that the
Code-Gen stage can validate before writing code.

Now uses standardized response format for better orchestrator integration.
"""

from __future__ import annotations
import logging, json
from typing import Any, Dict, List, Optional
from app.agents.base import Agent, AgentResponse, StandardizedAgentOutputs, AgentMemoryItem
from app.utils.bedrock_client import BedrockClient
from app.config.llm_config import ModelPurpose

logger = logging.getLogger(__name__)


class DataAnalysisAgent(Agent):
    def __init__(self, agent_id: str | None = None) -> None:
        self.agent_id = agent_id or "data_analysis_agent"
        self._llm     = BedrockClient(purpose=ModelPurpose.ANALYSIS)
        self.initialised = False

    async def initialize(self) -> None:
        self.initialised = True

    async def process(self, msg: Dict[str, Any]) -> Dict[str, Any]:
        if not self.initialised:
            await self.initialize()

        query, datasets = msg.get("query"), msg.get("datasets", [])
        if not datasets:
            return AgentResponse.error(
                self.agent_id, 
                "No datasets provided for analysis",
                error_details={"missing_data": "datasets", "provided_keys": list(msg.keys())}
            ).to_dict()

        # Check if this is an initial plan or refinement based on previous results
        planning_mode = msg.get("planning_mode", "initial")
        previous_results = msg.get("previous_results", {})
        
        try:
            if planning_mode == "initial":
                plan = await self._create_initial_plan(query, datasets)
                plan_type = "initial_analysis_plan"
                result_summary = f"Created initial {plan.get('analysis_type', 'unknown')} analysis plan"
            elif planning_mode == "refine_for_analysis":
                plan = await self._refine_plan_for_analysis(query, datasets, previous_results)
                plan_type = "refined_analysis_plan"
                result_summary = f"Refined analysis plan based on exploration results"
            elif planning_mode == "refine_for_visualization":
                plan = await self._refine_plan_for_visualization(query, datasets, previous_results)
                plan_type = "visualization_plan"
                result_summary = f"Created visualization plan based on analysis results"
            else:
                # Fallback to original behavior
                plan = await self._llm_plan(query, datasets)
                plan_type = "fallback_analysis_plan"
                result_summary = f"Created fallback analysis plan"
                
            # Extract key results for orchestrator
            key_results = {
                "analysis_type": plan.get("analysis_type", "unknown"),
                "analysis_goal": plan.get("analysis_goal", "unknown"), 
                "user_intent": plan.get("user_intent", "unknown"),
                "step_count": len(plan.get("analysis_steps", [])),
                "techniques_count": len(plan.get("recommended_techniques", []))
            }
            
            # Create context for next agent (code generation)
            context_for_next_agent = {
                "analysis_plan": plan,
                "planning_mode": planning_mode,
                "dataset_count": len(datasets),
                "ready_for_code_generation": True
            }
            
            # Create memory items
            memory_items = [
                AgentMemoryItem(
                    item_type="analysis_plan",
                    content=plan,
                    importance="high",
                    context_hint=f"Analysis plan for {plan.get('analysis_type', 'unknown')} analysis"
                ).to_dict(),
                AgentMemoryItem(
                    item_type="user_intent",
                    content=plan.get("user_intent", "unknown"),
                    importance="high",
                    context_hint="User's primary intent for this analysis"
                ).to_dict()
            ]
            
            return AgentResponse.success(
                self.agent_id,
                data=plan,  # Legacy compatibility
                result_summary=result_summary,
                key_results=key_results,
                context_for_next_agent=context_for_next_agent,
                memory_items=memory_items,
                metadata={
                    "plan_type": plan_type,
                    "dataset_count": len(datasets),
                    "planning_mode": planning_mode
                }
            ).to_dict()
            
        except Exception as e:
            logger.error(f"❌ Data analysis planning failed: {e}")
            return AgentResponse.error(
                self.agent_id,
                f"Analysis planning failed: {str(e)}",
                error_details={
                    "planning_mode": planning_mode,
                    "dataset_count": len(datasets),
                    "query_length": len(query),
                    "exception_type": type(e).__name__
                }
            ).to_dict()

    async def _create_initial_plan(self, query: str, datasets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create initial high-level analysis plan based on query and schema."""
        return await self._llm_plan(query, datasets)

    async def _refine_plan_for_analysis(self, query: str, datasets: List[Dict[str, Any]], 
                                       exploration_results: Dict[str, Any]) -> Dict[str, Any]:
        """Refine analysis plan based on actual data exploration findings."""
        system = """You are a senior data scientist. You must return ONLY valid JSON in the exact format specified. Do not include any explanations, markdown formatting, or additional text. Your response must be parseable as JSON."""

        # Extract key findings from exploration
        exploration_summary = self._summarize_exploration_findings(exploration_results)
        
        prompt = f"""Original Query: {query}

Dataset Schema:
{json.dumps([{d['dataset_id']: d['columns']} for d in datasets], indent=2)}

EXPLORATION FINDINGS:
{exploration_summary}

Based on these ACTUAL DATA CHARACTERISTICS, create a refined analysis plan that:
1. Leverages the discovered data patterns
2. Focuses on the most promising analytical directions
3. Addresses data quality issues found during exploration
4. Targets specific insights that the data can actually provide

Return ONLY this JSON structure (no other text):
{{
  "analysis_type": "string",
  "analysis_steps": ["step1", "step2"],
  "recommended_statistical_tests": ["test1", "test2"],
  "key_hypotheses_to_test": ["hypothesis1", "hypothesis2"],
  "patterns_to_investigate": ["pattern1", "pattern2"],
  "potential_insights": ["insight1", "insight2"],
  "data_driven_recommendations": ["rec1", "rec2"]
}}"""

        raw = await self._llm.generate_response(prompt=prompt, system_prompt=system, temperature=0.2)
        cleaned = raw.strip().removeprefix("```json").removesuffix("```")
        
        try:
            refined_plan = json.loads(cleaned)
            # Ensure we have the original plan structure plus refinements
            refined_plan["refinement_stage"] = "analysis"
            refined_plan["based_on_exploration"] = True
            return refined_plan
        except Exception:
            logger.warning("Refined analysis plan JSON parse failed, using enhanced fallback.")
            return self._create_fallback_analysis_plan(exploration_results)

    async def _refine_plan_for_visualization(self, query: str, datasets: List[Dict[str, Any]], 
                                           combined_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create visualization plan based on both exploration and analysis results."""
        system = """You are a data visualization expert. You must return ONLY valid JSON in the exact format specified. Do not include any explanations, markdown formatting, or additional text. Your response must be parseable as JSON."""

        # Extract insights from both exploration and analysis
        insights_summary = self._summarize_analytical_insights(combined_results)
        
        prompt = f"""Original Query: {query}

ANALYTICAL INSIGHTS DISCOVERED:
{insights_summary}

Create a visualization strategy that:
1. Tells a compelling story with the data
2. Highlights the most important findings
3. Makes insights actionable for the user
4. Uses appropriate chart types for the data and findings

Return ONLY this JSON structure (no other text):
{{
  "visualization_strategy": "string",
  "key_charts_to_create": ["chart1", "chart2"],
  "dashboard_layout": "string",
  "story_flow": ["step1", "step2"],
  "insights_to_highlight": ["insight1", "insight2"],
  "interactive_elements": ["element1", "element2"]
}}"""

        raw = await self._llm.generate_response(prompt=prompt, system_prompt=system, temperature=0.2)
        cleaned = raw.strip().removeprefix("```json").removesuffix("```")
        
        try:
            viz_plan = json.loads(cleaned)
            viz_plan["refinement_stage"] = "visualization"
            viz_plan["based_on_analysis"] = True
            return viz_plan
        except Exception:
            logger.warning("Visualization plan JSON parse failed, using fallback.")
            return self._create_fallback_visualization_plan()

    def _summarize_exploration_findings(self, exploration_results: Dict[str, Any]) -> str:
        """Extract key findings from exploration results for planning refinement."""
        summary_parts = []
        
        if exploration_results.get("stdout"):
            # Extract key information from stdout
            stdout = exploration_results["stdout"]
            summary_parts.append(f"Exploration Output:\n{stdout[-1000:]}")  # Last 1000 chars
        
        # Try to extract structured data if available
        if "output_files" in exploration_results:
            summary_parts.append(f"Generated {len(exploration_results['output_files'])} exploration artifacts")
        
        return "\n\n".join(summary_parts) if summary_parts else "Limited exploration data available"

    def _summarize_analytical_insights(self, combined_results: Dict[str, Any]) -> str:
        """Extract insights from both exploration and analysis for visualization planning."""
        summary_parts = []
        
        # Extract from exploration
        if combined_results.get("exploration_outputs", {}).get("stdout"):
            summary_parts.append(f"Data Characteristics:\n{combined_results['exploration_outputs']['stdout'][-500:]}")
        
        # Extract from analysis  
        if combined_results.get("analysis_outputs", {}).get("stdout"):
            summary_parts.append(f"Analysis Findings:\n{combined_results['analysis_outputs']['stdout'][-500:]}")
        
        return "\n\n".join(summary_parts) if summary_parts else "Limited analytical data available"

    def _create_fallback_analysis_plan(self, exploration_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create a reasonable analysis plan when LLM fails."""
        return {
            "analysis_type": "data_driven_exploratory",
            "analysis_steps": [
                "load_exploration_results",
                "identify_key_variables",
                "perform_correlation_analysis", 
                "conduct_statistical_tests",
                "identify_patterns_and_outliers"
            ],
            "recommended_statistical_tests": ["correlation_matrix", "distribution_tests"],
            "key_hypotheses_to_test": ["variable_relationships", "data_distribution_patterns"],
            "patterns_to_investigate": ["missing_value_patterns", "outlier_patterns", "correlation_patterns"],
            "potential_insights": ["data_quality_insights", "relationship_insights"],
            "refinement_stage": "analysis",
            "based_on_exploration": True
        }

    def _create_fallback_visualization_plan(self) -> Dict[str, Any]:
        """Create a reasonable visualization plan when LLM fails."""
        return {
            "visualization_strategy": "insight_driven_storytelling",
            "key_charts_to_create": [
                "summary_dashboard",
                "key_findings_charts", 
                "data_quality_overview"
            ],
            "dashboard_layout": "top_to_bottom_narrative",
            "story_flow": ["overview", "key_findings", "detailed_insights", "recommendations"],
            "insights_to_highlight": ["primary_patterns", "data_quality_notes", "actionable_recommendations"],
            "interactive_elements": ["hover_details", "drill_down_capability"],
            "refinement_stage": "visualization", 
            "based_on_analysis": True
        }

    async def _llm_plan(self, query: str, datasets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create analysis plan based on user intent rather than assuming ML goals."""
        system = """You are a senior data analyst who understands user intent and creates appropriate analysis plans. You must return ONLY valid JSON in the exact format specified. Do not include any explanations, markdown formatting, or additional text. Your response must be parseable as JSON.

CRITICAL: Analyze the user's query carefully to understand their actual intent. Different queries require different types of analysis:

- "analyze this data" / "analytics" / "insights", etc. → EXPLORATORY analysis with descriptive statistics, patterns, trends
- "predict" / "model" / "classification" / "regression", etc. → PREDICTIVE analysis with machine learning
- "compare" / "difference" / "relationship", etc. → COMPARATIVE analysis with statistical tests
- "trends" / "time series" / "over time", etc. → TEMPORAL analysis with time-based insights
- "segments" / "groups" / "clusters", etc. → SEGMENTATION analysis with clustering
- "optimize" / "improve" / "maximize", etc. → OPTIMIZATION analysis with recommendations

Do NOT assume machine learning unless explicitly requested by the user."""

        prompt = f"""User Query: {query}

Dataset Schema:
{json.dumps([{d['dataset_id']: d['columns']} for d in datasets], indent=2)}

Based on the user's query, determine their ACTUAL INTENT and create an appropriate analysis plan:

1. UNDERSTAND INTENT: What is the user really asking for?
   - Do they want general insights about their data?
   - Are they asking for predictions/modeling?
   - Do they want to compare groups or find relationships?
   - Are they looking for trends over time?

2. CHOOSE ANALYSIS TYPE:
   - "exploratory": General data exploration, patterns, insights
   - "predictive": Machine learning models for prediction
   - "diagnostic": Understanding why something happened
   - "descriptive": Summarizing and describing the data
   - "comparative": Comparing groups or segments
   - "temporal": Time-based analysis and trends

3. PLAN ACCORDINGLY: Create analysis steps that match the user's actual needs

Return ONLY this JSON structure (no other text):
{{
  "user_intent": "what the user is actually asking for",
  "analysis_type": "exploratory|predictive|diagnostic|descriptive|comparative|temporal",
  "analysis_goal": "specific goal based on user intent",
  "analysis_steps": ["step1", "step2", "step3"],
  "recommended_techniques": ["technique1", "technique2"],
  "key_questions_to_answer": ["question1", "question2"],
  "expected_outputs": ["output1", "output2"],
  "visualization_focus": "what visualizations would be most useful",
  "success_criteria": "how to measure if analysis succeeds"
}}"""

        raw = await self._llm.generate_response(prompt=prompt, system_prompt=system, temperature=0.2)
        cleaned = raw.strip().removeprefix("```json").removesuffix("```")
        
        try:
            plan = json.loads(cleaned)
            
            # Log the analysis plan as requested
            logger.info("📋 ANALYSIS PLAN CREATED:")
            logger.info(f"   🎯 User Intent: {plan.get('user_intent', 'Unknown')}")
            logger.info(f"   🔬 Analysis Type: {plan.get('analysis_type', 'Unknown')}")  
            logger.info(f"   🎪 Goal: {plan.get('analysis_goal', 'Unknown')}")
            logger.info(f"   📊 Steps: {len(plan.get('analysis_steps', []))} planned")
            
            return plan
        except Exception as e:
            logger.warning(f"Plan JSON parse failed: {e}, returning intent-aware fallback.")
            
            # Create intent-aware fallback based on query keywords
            query_lower = query.lower()
            
            if any(keyword in query_lower for keyword in ['predict', 'model', 'classify', 'regression', 'machine learning', 'ml']):
                # User wants ML
                analysis_type = "predictive"
                analysis_goal = "Build predictive model based on user request"
                techniques = ["feature_engineering", "model_training", "model_evaluation"]
                outputs = ["model_performance_metrics", "feature_importance", "predictions"]
            elif any(keyword in query_lower for keyword in ['compare', 'difference', 'vs', 'between']):
                # User wants comparison
                analysis_type = "comparative" 
                analysis_goal = "Compare groups or segments in the data"
                techniques = ["statistical_tests", "group_analysis", "comparative_statistics"]
                outputs = ["group_comparisons", "statistical_test_results", "comparative_charts"]
            elif any(keyword in query_lower for keyword in ['trend', 'time', 'over time', 'temporal', 'series']):
                # User wants time analysis
                analysis_type = "temporal"
                analysis_goal = "Analyze trends and patterns over time"
                techniques = ["time_series_analysis", "trend_detection", "seasonal_patterns"]
                outputs = ["trend_charts", "temporal_insights", "forecasts"]
            else:
                # Default to exploratory for general analytics requests
                analysis_type = "exploratory"
                analysis_goal = "Explore data to uncover insights and patterns"
                techniques = ["descriptive_statistics", "correlation_analysis", "pattern_discovery"]
                outputs = ["data_summary", "correlation_insights", "distribution_charts"]
                
            fallback_plan = {
                "user_intent": f"Intent inferred from query: {query[:100]}",
                "analysis_type": analysis_type,
                "analysis_goal": analysis_goal,
                "analysis_steps": ["load_and_inspect_data", "perform_requested_analysis", "generate_insights"],
                "recommended_techniques": techniques,
                "key_questions_to_answer": ["What patterns exist in the data?", "What insights can help the user?"],
                "expected_outputs": outputs,
                "visualization_focus": "charts that highlight key findings",
                "success_criteria": "User gains actionable insights from their data"
            }
            
            # Log the fallback plan
            logger.info("📋 FALLBACK ANALYSIS PLAN CREATED:")
            logger.info(f"   🎯 User Intent: {fallback_plan['user_intent']}")
            logger.info(f"   🔬 Analysis Type: {fallback_plan['analysis_type']}")
            logger.info(f"   🎪 Goal: {fallback_plan['analysis_goal']}")
            
            return fallback_plan