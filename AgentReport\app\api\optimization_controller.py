"""
Optimization Controller

This controller provides endpoints for performance monitoring, bottleneck analysis,
and optimization recommendations for the database query pipeline.
"""

import logging
from datetime import timedelta
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import PlainTextResponse

from app.services.database_service import DatabaseService
from app.services.query_optimization_service import get_query_optimization_service
from app.services.intelligent_cache_service import intelligent_cache_service
from app.services.async_optimization_service import async_optimization_service
from app.utils.performance_monitor import performance_monitor
from app.utils.security import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/optimization", tags=["optimization"])


@router.get("/performance-report")
async def get_performance_report(
    time_window_hours: Optional[int] = Query(None, description="Time window in hours for analysis"),
    format: str = Query("json", description="Response format: json or markdown"),
    current_user=Depends(get_current_user)
):
    """
    Get comprehensive performance analysis and optimization recommendations.
    
    Args:
        time_window_hours: Optional time window in hours for analysis
        format: Response format (json or markdown)
        
    Returns:
        Performance analysis and optimization recommendations
    """
    try:
        # Convert time window to timedelta if provided
        time_window = timedelta(hours=time_window_hours) if time_window_hours else None
        
        # Get database service instance
        database_service = DatabaseService()
        optimization_service = get_query_optimization_service(database_service)
        
        if format.lower() == "markdown":
            # Generate markdown report
            report = await optimization_service.generate_optimization_report(time_window)
            return PlainTextResponse(content=report, media_type="text/markdown")
        else:
            # Generate JSON analysis
            analysis = await optimization_service.analyze_query_performance(time_window)
            return {
                "status": "success",
                "data": analysis,
                "message": "Performance analysis completed successfully"
            }
            
    except Exception as e:
        logger.error(f"Error generating performance report: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate performance report: {str(e)}")


@router.get("/bottlenecks")
async def get_bottleneck_analysis(
    time_window_hours: Optional[int] = Query(24, description="Time window in hours for analysis"),
    current_user=Depends(get_current_user)
):
    """
    Get detailed bottleneck analysis for the query pipeline.
    
    Args:
        time_window_hours: Time window in hours for analysis
        
    Returns:
        Detailed bottleneck analysis
    """
    try:
        time_window = timedelta(hours=time_window_hours)
        
        # Get bottleneck analysis
        bottlenecks = performance_monitor.detect_bottlenecks(time_window)
        pipeline_analysis = performance_monitor.analyze_pipeline_stages(time_window)
        
        return {
            "status": "success",
            "data": {
                "time_window_hours": time_window_hours,
                "bottlenecks": [
                    {
                        "type": b.bottleneck_type,
                        "severity": b.severity,
                        "metric": b.metric_name,
                        "current_value": b.current_value,
                        "threshold": b.threshold,
                        "description": b.impact_description,
                        "recommendations": b.recommendations
                    }
                    for b in bottlenecks
                ],
                "pipeline_stages": [
                    {
                        "stage": s.stage_name,
                        "avg_duration": s.avg_duration,
                        "percentage_of_total": s.percentage_of_total,
                        "bottleneck_score": s.bottleneck_score
                    }
                    for s in pipeline_analysis
                ],
                "summary": {
                    "total_bottlenecks": len(bottlenecks),
                    "critical_bottlenecks": len([b for b in bottlenecks if b.severity == "critical"]),
                    "high_priority_bottlenecks": len([b for b in bottlenecks if b.severity == "high"]),
                    "slowest_stage": pipeline_analysis[0].stage_name if pipeline_analysis else None
                }
            },
            "message": "Bottleneck analysis completed successfully"
        }
        
    except Exception as e:
        logger.error(f"Error analyzing bottlenecks: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to analyze bottlenecks: {str(e)}")


@router.get("/cache-stats")
async def get_cache_statistics(
    current_user=Depends(get_current_user)
):
    """
    Get comprehensive cache performance statistics.
    
    Returns:
        Cache performance statistics for all cache layers
    """
    try:
        # Get cache statistics from intelligent cache service
        cache_stats = intelligent_cache_service.get_cache_statistics()
        
        # Get performance metrics
        performance_report = performance_monitor.get_performance_report()
        
        return {
            "status": "success",
            "data": {
                "cache_layers": {
                    name: {
                        "total_entries": stats.total_entries,
                        "total_size_kb": round(stats.total_size_bytes / 1024, 2),
                        "hit_rate": round(stats.hit_rate * 100, 2),
                        "miss_rate": round(stats.miss_rate * 100, 2),
                        "eviction_count": stats.eviction_count,
                        "avg_access_time_ms": round(stats.avg_access_time_ms, 2)
                    }
                    for name, stats in cache_stats.items()
                },
                "overall_performance": {
                    "total_cache_layers": len(cache_stats),
                    "overall_hit_rate": round(
                        sum(stats.hit_rate for stats in cache_stats.values()) / len(cache_stats) * 100, 2
                    ) if cache_stats else 0,
                    "total_cached_entries": sum(stats.total_entries for stats in cache_stats.values()),
                    "total_cache_size_mb": round(
                        sum(stats.total_size_bytes for stats in cache_stats.values()) / (1024 * 1024), 2
                    )
                },
                "recommendations": [
                    "Monitor hit rates - rates below 70% may indicate cache tuning needed",
                    "Watch eviction counts - high evictions may indicate cache size limits",
                    "Consider increasing cache TTL for stable data",
                    "Implement cache warming for frequently accessed data"
                ]
            },
            "message": "Cache statistics retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Error retrieving cache statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve cache statistics: {str(e)}")


@router.get("/async-metrics")
async def get_async_optimization_metrics(
    current_user=Depends(get_current_user)
):
    """
    Get asynchronous processing optimization metrics.
    
    Returns:
        Async processing performance metrics and recommendations
    """
    try:
        # Get async optimization metrics
        async_metrics = async_optimization_service.get_optimization_metrics()
        
        return {
            "status": "success",
            "data": {
                "task_queues": {
                    queue_name.replace("_queue", ""): {
                        "active_tasks": metrics["active_tasks"],
                        "completed_tasks": metrics["completed_tasks"],
                        "failed_tasks": metrics["failed_tasks"],
                        "success_rate": round(metrics["success_rate"] * 100, 2),
                        "avg_duration": round(metrics["avg_duration"], 3)
                    }
                    for queue_name, metrics in async_metrics.items()
                    if queue_name.endswith("_queue")
                },
                "resource_metrics": async_metrics.get("resource_metrics", {}),
                "recommendations": [
                    "Monitor task queue success rates - rates below 95% may indicate issues",
                    "Watch for high average durations in critical queues",
                    "Consider increasing concurrency limits for high-throughput queues",
                    "Implement circuit breakers for failing operations"
                ]
            },
            "message": "Async optimization metrics retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Error retrieving async metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve async metrics: {str(e)}")


@router.post("/clear-cache")
async def clear_cache(
    cache_type: str = Query("all", description="Cache type to clear: all, query, sql, schema, result"),
    current_user=Depends(get_current_user)
):
    """
    Clear cache entries for performance testing or troubleshooting.
    
    Args:
        cache_type: Type of cache to clear
        
    Returns:
        Cache clearing confirmation
    """
    try:
        if cache_type.lower() == "all":
            # Clear all cache types
            intelligent_cache_service.invalidate_cache("query")
            intelligent_cache_service.invalidate_cache("sql")
            intelligent_cache_service.invalidate_cache("schema")
            intelligent_cache_service.invalidate_cache("result")
            performance_monitor.clear_metrics()
            message = "All caches cleared successfully"
        else:
            # Clear specific cache type
            intelligent_cache_service.invalidate_cache(cache_type.lower())
            message = f"{cache_type.title()} cache cleared successfully"
        
        return {
            "status": "success",
            "data": {
                "cache_type": cache_type,
                "cleared_at": performance_monitor.get_performance_report()["timestamp"]
            },
            "message": message
        }
        
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")


@router.get("/performance-summary")
async def get_performance_summary(
    current_user=Depends(get_current_user)
):
    """
    Get a quick performance summary for dashboard display.
    
    Returns:
        Quick performance summary with key metrics
    """
    try:
        # Get recent performance data
        time_window = timedelta(hours=1)  # Last hour
        bottlenecks = performance_monitor.detect_bottlenecks(time_window)
        cache_stats = intelligent_cache_service.get_cache_statistics()
        
        # Calculate overall health score (0-100)
        health_score = 100
        
        # Deduct points for bottlenecks
        critical_bottlenecks = len([b for b in bottlenecks if b.severity == "critical"])
        high_bottlenecks = len([b for b in bottlenecks if b.severity == "high"])
        health_score -= (critical_bottlenecks * 20 + high_bottlenecks * 10)
        
        # Deduct points for low cache hit rates
        avg_hit_rate = sum(stats.hit_rate for stats in cache_stats.values()) / len(cache_stats) if cache_stats else 1.0
        if avg_hit_rate < 0.7:  # Below 70%
            health_score -= 15
        
        health_score = max(0, health_score)  # Ensure non-negative
        
        # Determine status
        if health_score >= 90:
            status = "excellent"
        elif health_score >= 75:
            status = "good"
        elif health_score >= 60:
            status = "fair"
        else:
            status = "poor"
        
        return {
            "status": "success",
            "data": {
                "health_score": health_score,
                "status": status,
                "summary": {
                    "total_bottlenecks": len(bottlenecks),
                    "critical_issues": critical_bottlenecks,
                    "cache_hit_rate": round(avg_hit_rate * 100, 1),
                    "total_cached_entries": sum(stats.total_entries for stats in cache_stats.values())
                },
                "recommendations": [
                    "System performance is optimal" if health_score >= 90 else
                    "Minor optimizations recommended" if health_score >= 75 else
                    "Performance improvements needed" if health_score >= 60 else
                    "Immediate attention required for performance issues"
                ]
            },
            "message": "Performance summary retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Error retrieving performance summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve performance summary: {str(e)}")
