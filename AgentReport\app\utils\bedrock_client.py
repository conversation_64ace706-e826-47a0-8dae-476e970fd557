"""AWS Bedrock Client

This module provides a client for interacting with AWS Bedrock.
"""

import json
import asyncio
import time
from typing import Dict, Any, Optional, AsyncGenerator

import aiobotocore.session
from botocore.exceptions import ClientError

from app.config import settings
from app.config.llm_config import ModelPurpose, ModelConfig, get_model_for_purpose, get_model_by_key

# Rate limit tracking for Claude 4 Sonnet
class Claude4RateLimitTracker:
    """Track rate limits specifically for Claude 4 Sonnet based on Anthropic documentation."""
    
    def __init__(self):
        # Tier-based limits (assuming Tier 1/2 initially, will be detected from headers)
        self.detected_tier = 1
        self.requests_per_minute = 50  # Start with Tier 1/2 limits
        self.input_tokens_per_minute = 20000
        self.output_tokens_per_minute = 8000
        
        # Current usage tracking (sliding window)
        self.request_timestamps = []
        self.input_token_usage = []
        self.output_token_usage = []
        
        # Last rate limit headers received
        self.last_headers = {}

    def update_from_headers(self, headers: dict):
        """Update rate limits based on response headers from Anthropic."""
        self.last_headers = headers
        
        # Update limits based on headers (if available)
        if 'anthropic-ratelimit-requests-limit' in headers:
            self.requests_per_minute = int(headers['anthropic-ratelimit-requests-limit'])
        if 'anthropic-ratelimit-input-tokens-limit' in headers:
            self.input_tokens_per_minute = int(headers['anthropic-ratelimit-input-tokens-limit'])
        if 'anthropic-ratelimit-output-tokens-limit' in headers:
            self.output_tokens_per_minute = int(headers['anthropic-ratelimit-output-tokens-limit'])
            
        # Detect tier based on limits
        if self.requests_per_minute >= 2000:
            self.detected_tier = 4
        elif self.requests_per_minute >= 1000:
            self.detected_tier = 3
        else:
            self.detected_tier = max(1, 2)  # Tier 1/2 have same limits
            
        print(f"📊 Claude 4 Rate Limits - Tier {self.detected_tier}: {self.requests_per_minute} RPM, {self.input_tokens_per_minute} ITPM, {self.output_tokens_per_minute} OTPM")

    def should_wait_before_request(self, estimated_input_tokens: int = 1000, estimated_output_tokens: int = 500) -> float:
        """Check if we should wait before making a request. Returns wait time in seconds."""
        now = time.time()
        minute_ago = now - 60
        
        # Clean old entries (sliding window)
        self.request_timestamps = [ts for ts in self.request_timestamps if ts > minute_ago]
        self.input_token_usage = [(ts, tokens) for ts, tokens in self.input_token_usage if ts > minute_ago]
        self.output_token_usage = [(ts, tokens) for ts, tokens in self.output_token_usage if ts > minute_ago]
        
        wait_times = []
        
        # More conservative rate limiting - lower thresholds to prevent hitting limits
        
        # Check request rate limit (50% threshold instead of 90% for more headroom)
        if len(self.request_timestamps) >= self.requests_per_minute * 0.5:
            oldest_request = min(self.request_timestamps)
            wait_time = 60 - (now - oldest_request) + 5  # +5 second buffer
            if wait_time > 0:
                wait_times.append(wait_time)
        
        # Check input token rate limit (50% threshold)
        current_input_tokens = sum(tokens for _, tokens in self.input_token_usage)
        if current_input_tokens + estimated_input_tokens >= self.input_tokens_per_minute * 0.5:
            if self.input_token_usage:
                oldest_token_usage = min(ts for ts, _ in self.input_token_usage)
                wait_time = 60 - (now - oldest_token_usage) + 5
                if wait_time > 0:
                    wait_times.append(wait_time)
        
        # Check output token rate limit (50% threshold) - account for 5:1 burndown for Claude 4
        current_output_tokens = sum(tokens for _, tokens in self.output_token_usage)
        # For Claude 4, output tokens burn 5:1 against quota
        effective_output_tokens = estimated_output_tokens * 5  # 5:1 burndown ratio
        if current_output_tokens + effective_output_tokens >= self.output_tokens_per_minute * 0.5:
            if self.output_token_usage:
                oldest_token_usage = min(ts for ts, _ in self.output_token_usage)
                wait_time = 60 - (now - oldest_token_usage) + 5
                if wait_time > 0:
                    wait_times.append(wait_time)
        
        # Enforce minimum spacing between requests (6 seconds for Claude 4)
        if self.request_timestamps:
            last_request_time = max(self.request_timestamps)
            time_since_last = now - last_request_time
            min_spacing = 6.0  # Minimum 6 seconds between Claude 4 requests (reduced from 10s)
            if time_since_last < min_spacing:
                wait_times.append(min_spacing - time_since_last)
        else:
            # First request - shorter initial wait
            wait_times.append(2.0)
        
        return max(wait_times) if wait_times else 0

    def record_request(self, input_tokens: int = 1000, output_tokens: int = 500):
        """Record a successful request for rate limit tracking."""
        now = time.time()
        self.request_timestamps.append(now)
        self.input_token_usage.append((now, input_tokens))
        self.output_token_usage.append((now, output_tokens))

# Global rate limit tracker for Claude 4
claude4_tracker = Claude4RateLimitTracker()

class BedrockClient:
    """Client for interacting with AWS Bedrock models with rate limiting and retry logic."""
    
    def __init__(self, purpose: Optional[ModelPurpose] = None, model_override: Optional[str] = None):
        """Initialize the Bedrock client.
        
        Args:
            purpose: The purpose/use case for this client (determines which model to use)
            model_override: Override model key to use (bypasses purpose-based selection)
        """
        # Determine which model to use
        if model_override:
            # Use specific model override
            self.model_config = get_model_by_key(model_override)
        elif purpose:
            # Use model assigned to this purpose
            self.model_config = get_model_for_purpose(purpose)
        else:
            # Fallback to environment variable for backward compatibility
            fallback_config = ModelConfig(
                model_id=settings.BEDROCK_MODEL_ID,
                display_name="Environment Default",
                max_tokens=settings.BEDROCK_MAX_TOKENS,
                temperature=0.7,
                top_p=0.9,
                description="Default model from environment",
                cost_tier="unknown",
                speed_tier="unknown",
                quality_tier="unknown"
            )
            self.model_config = fallback_config
        
        # Set up client properties
        self.model_id = self.model_config.model_id
        self.max_tokens = self.model_config.max_tokens
        self.purpose = purpose
        
        # Check if this is Claude 4 Sonnet
        self.is_claude4 = "claude-4" in self.model_id or "claude-sonnet-4" in self.model_id
        
        # Disable streaming for Claude 4 due to capacity issues
        if self.is_claude4:
            self.streaming = False  # Force non-streaming for Claude 4
            print(f"🚫 Disabled streaming for Claude 4 due to capacity constraints")
        else:
            self.streaming = settings.BEDROCK_STREAMING
        
        # Regional configuration for Claude 4
        if self.is_claude4:
            # For Claude 4, we ONLY use the configured region and let the cross-region 
            # inference profile handle routing automatically per AWS documentation
            self.regions_to_try = [settings.AWS_REGION]
            self.current_region_index = 0
            self.region = settings.AWS_REGION
            print(f"🌍 Using region {settings.AWS_REGION} for Claude 4 cross-region inference profile")
        else:
            self.regions_to_try = [settings.AWS_REGION]
            self.current_region_index = 0
            self.region = settings.AWS_REGION
        
        # Rate limiting and retry configuration - balanced for Claude 4
        if self.is_claude4:
            # Claude 4 models have stricter rate limits - balanced settings to avoid timeouts
            self.max_retries = 8  # Reasonable retries for Claude 4
            self.base_delay = 5.0  # Start with 5s delay (reduced from 8s)
            self.max_delay = 300.0  # Allow up to 5 minutes wait (reduced from 10 minutes)
            print(f"🔧 Using Claude 4 balanced settings: {self.max_retries} retries, {self.base_delay}s base delay, 6s minimum spacing")
        else:
            # Standard retry configuration for other models
            self.max_retries = 4
            self.base_delay = 1.0  # Base delay in seconds
            self.max_delay = 60.0  # Maximum delay in seconds
        
        # Longer timeout for Claude 4 due to higher latency
        self.request_timeout = 600 if self.is_claude4 else 300  # 10 minutes for Claude 4, 5 minutes for others
        
        # Set up async session for async operations
        self.async_session = aiobotocore.session.get_session()
        
        # Circuit breaker for Claude 4 - track consecutive failures
        self.consecutive_failures = 0
        self.max_consecutive_failures = 4  # Allow more failures before circuit breaker
        self.fallback_model_used = False
    
    def _compress_prompt_for_claude4(self, prompt: str) -> str:
        """Compress prompts for Claude 4 to avoid token limits."""
        if len(prompt) <= 8000:  # Reasonable length, no compression needed
            return prompt
        
        # For very long prompts, keep the most important parts
        lines = prompt.split('\n')
        
        # Keep system instructions, JSON examples, and requirements
        important_lines = []
        for line in lines:
            line_lower = line.lower()
            # Keep critical instruction lines
            if any(keyword in line_lower for keyword in [
                'requirements:', 'return only', 'json structure', 'must return', 
                'output only', 'system:', 'query:', 'datasets:', 'expected output'
            ]):
                important_lines.append(line)
            # Keep short lines (likely important headers/instructions)
            elif len(line.strip()) < 100 and line.strip():
                important_lines.append(line)
        
        # If we still have too much content, truncate dataset info
        compressed = '\n'.join(important_lines)
        if len(compressed) > 12000:
            # Keep first and last parts, truncate middle
            lines = compressed.split('\n')
            keep_start = len(lines) // 3
            keep_end = len(lines) // 3
            compressed_lines = lines[:keep_start] + ["... [dataset content truncated for Claude 4] ..."] + lines[-keep_end:]
            compressed = '\n'.join(compressed_lines)
        
        if len(compressed) < len(prompt):
            print(f"🗜️ Compressed prompt from {len(prompt)} to {len(compressed)} chars for Claude 4")
        
        return compressed
    
    async def _retry_with_exponential_backoff(self, func, *args, **kwargs):
        """Retry a function with exponential backoff on rate limit errors, following Anthropic best practices."""
        
        # Pre-request rate limiting for Claude 4
        if self.is_claude4:
            # Estimate token usage (rough approximation)
            estimated_input_tokens = len(str(args[0])) // 4 if args else 1000  # ~4 chars per token
            estimated_output_tokens = min(self.max_tokens, 1500)  # Conservative estimate
            
            # Claude 4 has 5:1 output token burndown - calculate true quota usage
            true_quota_usage = estimated_input_tokens + (estimated_output_tokens * 5)
            
            proactive_wait = claude4_tracker.should_wait_before_request(estimated_input_tokens, estimated_output_tokens)
            if proactive_wait > 0:
                print(f"🚦 Proactive rate limiting: waiting {proactive_wait:.1f}s before Claude 4 request (quota usage: {true_quota_usage} tokens)...")
                await asyncio.sleep(proactive_wait)
        
        # Circuit breaker: if Claude 4 has failed too many times, temporarily use fallback
        if self.is_claude4 and self.consecutive_failures >= self.max_consecutive_failures:
            if not self.fallback_model_used:
                print(f"🔄 Claude 4 circuit breaker triggered after {self.consecutive_failures} failures, using Claude 3.5 Sonnet temporarily...")
                # Temporarily switch to Claude 3.5 Sonnet
                original_model_id = self.model_id
                self.model_id = "anthropic.claude-3-5-sonnet-20241022-v2:0"
                self.fallback_model_used = True
                try:
                    result = await func(*args, **kwargs)
                    # Success with fallback - reduce failure count but don't reset completely
                    self.consecutive_failures = max(0, self.consecutive_failures - 2)
                    return result
                finally:
                    # Always restore original model for next attempt
                    self.model_id = original_model_id
        
        for attempt in range(self.max_retries + 1):
            try:
                result = await func(*args, **kwargs)
                # Success - reset failure count and record usage for Claude 4
                self.consecutive_failures = 0
                self.fallback_model_used = False
                
                if self.is_claude4:
                    # Record successful request for rate limiting
                    estimated_input_tokens = len(str(args[0])) // 4 if args else 1000
                    estimated_output_tokens = len(str(result)) // 4 if result else 500
                    claude4_tracker.record_request(estimated_input_tokens, estimated_output_tokens)
                
                return result
                
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', '')
                error_message = str(e).lower()
                response_metadata = e.response.get('ResponseMetadata', {})
                http_headers = response_metadata.get('HTTPHeaders', {})
                
                # Check if this is a throttling/rate limit error
                is_throttling = (
                    error_code in ['ThrottlingException', 'TooManyRequestsException'] or
                    'throttl' in error_message or
                    'too many' in error_message or
                    'rate limit' in error_message or
                    response_metadata.get('HTTPStatusCode') == 429
                )
                
                if is_throttling and attempt < self.max_retries:
                    # Handle retry-after header as per Anthropic documentation
                    retry_after = None
                    if 'retry-after' in http_headers:
                        try:
                            retry_after = float(http_headers['retry-after'])
                            print(f"📋 Anthropic retry-after header: {retry_after}s")
                        except (ValueError, TypeError):
                            pass
                    
                    # Update Claude 4 rate limit tracking from response headers
                    if self.is_claude4:
                        anthropic_headers = {k: v for k, v in http_headers.items() if k.startswith('anthropic-ratelimit')}
                        if anthropic_headers:
                            claude4_tracker.update_from_headers(anthropic_headers)
                            
                        # For Claude 4, we rely on cross-region inference profile 
                        # to handle region routing automatically (no manual region switching)
                    
                    # Calculate delay: use retry-after if provided, otherwise exponential backoff
                    if retry_after is not None:
                        delay = min(retry_after, self.max_delay)
                        print(f"⏰ Using Anthropic retry-after: {delay:.1f}s")
                    else:
                        # Exponential backoff with jitter - more aggressive for Claude 4
                        base_multiplier = 3 if self.is_claude4 else 2
                        delay = min(self.base_delay * (base_multiplier ** attempt), self.max_delay)
                        # Add jitter to prevent thundering herd (±10%)
                        jitter = delay * 0.1 * (2 * (time.time() % 1) - 1)  # -10% to +10%
                        delay = max(1.0, delay + jitter)  # Minimum 1 second
                    
                    print(f"🔄 Rate limit hit (attempt {attempt + 1}/{self.max_retries + 1}), waiting {delay:.1f}s...")
                    await asyncio.sleep(delay)
                    continue
                else:
                    # Not a throttling error or out of retries
                    # Increment failure count for Claude 4 circuit breaker
                    if self.is_claude4:
                        self.consecutive_failures += 1
                        print(f"❌ Claude 4 failure #{self.consecutive_failures}: {error_code} - {error_message}")
                    raise e
        
        # This should never be reached, but just in case
        raise Exception(f"Max retries ({self.max_retries}) exceeded for Claude 4 Sonnet")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model configuration."""
        return {
            "model_id": self.model_id,
            "display_name": self.model_config.display_name,
            "purpose": self.purpose.value if self.purpose else "default",
            "max_tokens": self.max_tokens,
            "description": self.model_config.description,
            "cost_tier": self.model_config.cost_tier,
            "speed_tier": self.model_config.speed_tier,
            "quality_tier": self.model_config.quality_tier
        }
        
    async def generate_response(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
    ) -> str:
        """Generate a complete response from the model with retry logic.
        
        Args:
            prompt: The user's prompt
            system_prompt: Optional system prompt for the model
            temperature: Controls randomness (0.0 to 1.0), uses model default if None
            top_p: Controls diversity (0.0 to 1.0), uses model default if None
            
        Returns:
            The complete generated text
        """
        # Use model defaults if not specified
        temperature = temperature if temperature is not None else self.model_config.temperature
        top_p = top_p if top_p is not None else self.model_config.top_p
        
        if self.streaming:
            # Use streaming API but accumulate the full response
            try:
                full_response = ""
                async for chunk in self.generate_response_stream(prompt, system_prompt, temperature, top_p):
                    full_response += chunk
                return full_response
            except Exception as e:
                # If streaming fails, fall back to non-streaming
                print(f"Streaming failed ({str(e)}), falling back to non-streaming...")
                return await self._retry_with_exponential_backoff(
                    self._generate_response_non_streaming_impl, 
                    prompt, system_prompt, temperature, top_p
                )
        else:
            # Use non-streaming API with retry logic
            return await self._retry_with_exponential_backoff(
                self._generate_response_non_streaming_impl, 
                prompt, system_prompt, temperature, top_p
            )

    async def generate_response_stream(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response from the model with retry logic.
        
        Args:
            prompt: The user's prompt
            system_prompt: Optional system prompt for the model
            temperature: Controls randomness (0.0 to 1.0), uses model default if None
            top_p: Controls diversity (0.0 to 1.0), uses model default if None
            
        Yields:
            Text chunks as they are generated
        """
        # Use model defaults if not specified
        temperature = temperature if temperature is not None else self.model_config.temperature
        top_p = top_p if top_p is not None else self.model_config.top_p
        
        # For streaming, we need to handle retries at the generator level
        for attempt in range(self.max_retries + 1):
            try:
                async for chunk in self._generate_response_stream_impl(prompt, system_prompt, temperature, top_p):
                    yield chunk
                return  # Success, exit the retry loop
                
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', '')
                error_message = str(e).lower()
                
                # Check if this is a throttling/rate limit error
                is_throttling = (
                    error_code in ['ThrottlingException', 'TooManyRequestsException'] or
                    'throttl' in error_message or
                    'too many' in error_message or
                    'rate limit' in error_message
                )
                
                if is_throttling and attempt < self.max_retries:
                    # Calculate delay with exponential backoff and jitter
                    delay = min(self.base_delay * (2 ** attempt), self.max_delay)
                    jitter = delay * 0.1 * (0.5 - time.time() % 1)
                    total_delay = delay + jitter
                    
                    print(f"Streaming rate limit hit (attempt {attempt + 1}/{self.max_retries + 1}), waiting {total_delay:.2f}s...")
                    await asyncio.sleep(total_delay)
                    continue
                else:
                    # Not a throttling error or out of retries - raise the exception instead of yielding error
                    raise e

    async def generate_response_stream_with_context(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate a streaming response with additional context for SSE events.

        Args:
            prompt: The user's prompt
            system_prompt: Optional system prompt for the model
            temperature: Controls randomness (0.0 to 1.0), uses model default if None
            top_p: Controls diversity (0.0 to 1.0), uses model default if None
            context: Additional context to include in streaming events

        Yields:
            Dictionary events with token data and context information
        """
        context = context or {}
        accumulated_response = ""

        try:
            async for token in self.generate_response_stream(prompt, system_prompt, temperature, top_p):
                accumulated_response += token

                # Yield token event with context
                yield {
                    "type": "token_stream",
                    "token": token,
                    "accumulated_text": accumulated_response,
                    "agent": context.get("agent", "unknown"),
                    "step": context.get("step"),
                    "timestamp": context.get("timestamp"),
                    **context
                }

            # Yield completion event
            yield {
                "type": "token_complete",
                "final_text": accumulated_response,
                "agent": context.get("agent", "unknown"),
                "step": context.get("step"),
                "timestamp": context.get("timestamp"),
                **context
            }

        except Exception as e:
            # Yield error event
            yield {
                "type": "token_error",
                "error": str(e),
                "partial_text": accumulated_response,
                "agent": context.get("agent", "unknown"),
                "step": context.get("step"),
                "timestamp": context.get("timestamp"),
                **context
            }

    async def _generate_response_stream_impl(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        top_p: float = 0.9
    ) -> AsyncGenerator[str, None]:
        """Internal implementation of streaming response generation."""
        # Build request body based on the model
        request_body = self._build_request_body(prompt, system_prompt, temperature, top_p)
        body = json.dumps(request_body)

        async with self.async_session.create_client(
            'bedrock-runtime',
            region_name=self.region,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        ) as client:
            # Use streaming API and yield chunks as they arrive
            response = await client.invoke_model_with_response_stream(
                body=body,
                modelId=self.model_id,
                accept="application/json",
                contentType="application/json"
            )

            async for event in response["body"]:
                chunk = event.get("chunk")
                if not chunk:
                    continue

                chunk_obj = json.loads(chunk.get("bytes").decode())

                # Extract text based on model
                text_chunk = ""
                if "anthropic.claude" in self.model_id:
                    if "delta" in chunk_obj and "text" in chunk_obj["delta"]:
                        text_chunk = chunk_obj["delta"]["text"]
                else:
                    # Default handling for other models
                    if "completion" in chunk_obj:
                        text_chunk = chunk_obj["completion"]

                if text_chunk:
                    yield text_chunk

    async def _generate_response_non_streaming_impl(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        top_p: float = 0.9
    ) -> str:
        """Internal implementation of non-streaming response generation."""
        # Build request body based on the model
        request_body = self._build_request_body(prompt, system_prompt, temperature, top_p)
        body = json.dumps(request_body)
        
        async with self.async_session.create_client(
            'bedrock-runtime',
            region_name=self.region,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        ) as client:
            response = await client.invoke_model(
                body=body,
                modelId=self.model_id,
                accept="application/json",
                contentType="application/json"
            )
            
            response_body = await response["body"].read()
            response_json = json.loads(response_body)
            
            # Extract the response based on model
            if "anthropic.claude" in self.model_id:
                return response_json.get("content", [{"text": ""}])[0].get("text", "")
            else:
                return response_json.get("completion", "")

    def _build_request_body(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        top_p: float = 0.9
    ) -> Dict[str, Any]:
        """Build the request body for the Bedrock API based on the model type."""
        # Compress prompts for Claude 4 to avoid token limits
        if "claude-4" in self.model_id or "claude-sonnet-4" in self.model_id:
            prompt = self._compress_prompt_for_claude4(prompt)
            if system_prompt:
                system_prompt = self._compress_prompt_for_claude4(system_prompt)
        
        if "anthropic.claude" in self.model_id:
            request_body = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": min(self.max_tokens, 64000),  # Conservative limit for Claude 4
                "temperature": temperature,
                "top_p": top_p,
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            }
            
            if system_prompt:
                request_body["system"] = system_prompt
                
        else:
            # Default format for other models
            request_body = {
                "prompt": prompt,
                "max_tokens": self.max_tokens,
                "temperature": temperature,
                "top_p": top_p
            }
        
        return request_body 

    def get_claude4_rate_limit_status(self) -> Dict[str, Any]:
        """Get detailed rate limit status for Claude 4 Sonnet including tier advancement guidance."""
        if not self.is_claude4:
            return {"message": "This method is only for Claude 4 Sonnet models"}
        
        current_time = time.time()
        minute_ago = current_time - 60
        
        # Get current usage
        recent_requests = len([ts for ts in claude4_tracker.request_timestamps if ts > minute_ago])
        recent_input_tokens = sum(tokens for ts, tokens in claude4_tracker.input_token_usage if ts > minute_ago)
        recent_output_tokens = sum(tokens for ts, tokens in claude4_tracker.output_token_usage if ts > minute_ago)
        
        # Calculate remaining capacity
        requests_remaining = max(0, claude4_tracker.requests_per_minute - recent_requests)
        input_tokens_remaining = max(0, claude4_tracker.input_tokens_per_minute - recent_input_tokens)
        output_tokens_remaining = max(0, claude4_tracker.output_tokens_per_minute - recent_output_tokens)
        
        # Calculate usage percentages
        request_usage_pct = (recent_requests / claude4_tracker.requests_per_minute) * 100
        input_token_usage_pct = (recent_input_tokens / claude4_tracker.input_tokens_per_minute) * 100
        output_token_usage_pct = (recent_output_tokens / claude4_tracker.output_tokens_per_minute) * 100
        
        # Tier advancement guidance
        tier_guidance = self._get_tier_advancement_guidance(claude4_tracker.detected_tier)
        
        status = {
            "current_tier": claude4_tracker.detected_tier,
            "rate_limits": {
                "requests_per_minute": claude4_tracker.requests_per_minute,
                "input_tokens_per_minute": claude4_tracker.input_tokens_per_minute,
                "output_tokens_per_minute": claude4_tracker.output_tokens_per_minute
            },
            "current_usage": {
                "requests": {
                    "used": recent_requests,
                    "remaining": requests_remaining,
                    "usage_percentage": round(request_usage_pct, 1)
                },
                "input_tokens": {
                    "used": recent_input_tokens,
                    "remaining": input_tokens_remaining,
                    "usage_percentage": round(input_token_usage_pct, 1)
                },
                "output_tokens": {
                    "used": recent_output_tokens,
                    "remaining": output_tokens_remaining,
                    "usage_percentage": round(output_token_usage_pct, 1)
                }
            },
            "tier_advancement": tier_guidance,
            "recent_failures": self.consecutive_failures,
            "circuit_breaker_active": self.consecutive_failures >= self.max_consecutive_failures,
            "last_headers": claude4_tracker.last_headers
        }
        
        return status
    
    def calculate_claude4_quota_usage(self, estimated_input_tokens: int, estimated_output_tokens: int) -> Dict[str, int]:
        """Calculate the actual quota usage for Claude 4 considering the 5:1 output token burndown."""
        if not self.is_claude4:
            return {
                "input_tokens": estimated_input_tokens,
                "output_tokens": estimated_output_tokens,
                "total_quota_usage": estimated_input_tokens + estimated_output_tokens,
                "burndown_multiplier": 1
            }
        
        # Claude 4 has 5:1 output token burndown
        effective_output_quota = estimated_output_tokens * 5
        total_quota_usage = estimated_input_tokens + effective_output_quota
        
        return {
            "input_tokens": estimated_input_tokens,
            "output_tokens": estimated_output_tokens,
            "output_quota_usage": effective_output_quota,
            "total_quota_usage": total_quota_usage,
            "burndown_multiplier": 5,
            "max_tokens_setting": self.max_tokens
        }
    
    def _get_tier_advancement_guidance(self, current_tier: int) -> Dict[str, Any]:
        """Get guidance on how to advance to the next tier for better rate limits."""
        tier_info = {
            1: {
                "current_limits": "50 RPM, 20,000 ITPM, 8,000 OTPM",
                "to_advance": {
                    "deposit_required": "$5",
                    "max_monthly_spend": "$100",
                    "next_tier_limits": "Same as Tier 1 (50 RPM, 20,000 ITPM, 8,000 OTPM)"
                }
            },
            2: {
                "current_limits": "50 RPM, 20,000 ITPM, 8,000 OTPM", 
                "to_advance": {
                    "deposit_required": "$40",
                    "max_monthly_spend": "$500",
                    "next_tier_limits": "1,000 RPM, 40,000 ITPM, 16,000 OTPM"
                }
            },
            3: {
                "current_limits": "1,000 RPM, 40,000 ITPM, 16,000 OTPM",
                "to_advance": {
                    "deposit_required": "$200", 
                    "max_monthly_spend": "$1,000",
                    "next_tier_limits": "2,000 RPM, 80,000 ITPM, 32,000 OTPM"
                }
            },
            4: {
                "current_limits": "2,000 RPM, 80,000 ITPM, 32,000 OTPM",
                "to_advance": {
                    "deposit_required": "$400",
                    "max_monthly_spend": "$5,000", 
                    "next_tier_limits": "Contact sales for enterprise limits"
                }
            }
        }
        
        guidance = tier_info.get(current_tier, {
            "current_limits": "Unknown",
            "to_advance": {
                "deposit_required": "Unknown",
                "max_monthly_spend": "Unknown", 
                "next_tier_limits": "Contact Anthropic support"
            }
        })
        
        guidance["instructions"] = [
            "1. Go to the Anthropic Console (https://console.anthropic.com)",
            "2. Check your current usage tier in the Limits page",
            "3. Make the required deposit to advance to the next tier",
            "4. Higher tiers provide significantly better rate limits for Claude 4",
            "5. If you need enterprise limits, contact sales through the console"
        ]
        
        return guidance

    def print_claude4_status(self):
        """Print a detailed status report for Claude 4 Sonnet rate limiting."""
        if not self.is_claude4:
            print("ℹ️ This status report is only available for Claude 4 Sonnet models")
            return
            
        status = self.get_claude4_rate_limit_status()
        
        print(f"\n📊 Claude 4 Sonnet Rate Limit Status")
        print(f"{'='*50}")
        print(f"Current Tier: {status['current_tier']}")
        print(f"Rate Limits: {status['rate_limits']['requests_per_minute']} RPM, {status['rate_limits']['input_tokens_per_minute']} ITPM, {status['rate_limits']['output_tokens_per_minute']} OTPM")
        print(f"\nCurrent Usage (last 60 seconds):")
        print(f"  Requests: {status['current_usage']['requests']['used']}/{status['rate_limits']['requests_per_minute']} ({status['current_usage']['requests']['usage_percentage']}%)")
        print(f"  Input Tokens: {status['current_usage']['input_tokens']['used']}/{status['rate_limits']['input_tokens_per_minute']} ({status['current_usage']['input_tokens']['usage_percentage']}%)")
        print(f"  Output Tokens: {status['current_usage']['output_tokens']['used']}/{status['rate_limits']['output_tokens_per_minute']} ({status['current_usage']['output_tokens']['usage_percentage']}%)")
        
        if status['circuit_breaker_active']:
            print(f"\n🔴 Circuit Breaker ACTIVE: {status['recent_failures']} consecutive failures")
            print("The system is temporarily using Claude 3.5 Sonnet as fallback")
        elif status['recent_failures'] > 0:
            print(f"\n🟠 Recent Failures: {status['recent_failures']} (Circuit breaker activates at {self.max_consecutive_failures})")
        else:
            print(f"\n🟢 System Status: Healthy (0 recent failures)")
        
        print(f"\n💡 To Improve Rate Limits:")
        guidance = status['tier_advancement']
        if guidance.get('to_advance'):
            print(f"  Current: {guidance['current_limits']}")
            print(f"  Deposit {guidance['to_advance']['deposit_required']} to advance")
            print(f"  Next Tier: {guidance['to_advance']['next_tier_limits']}")
            print(f"  Visit: https://console.anthropic.com")
        
        print(f"{'='*50}\n") 
