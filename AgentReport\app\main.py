"""Main Application Entry Point

This module provides the FastAPI application entry point and REST API endpoints.
"""

import logging
import os
from typing import Dict, Any, Optional

import uvicorn
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware

from app.config import settings

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Create FastAPI application
app = FastAPI(
    title="Database Query Agent Platform",
    description="A platform that allows users to connect and query multiple databases using natural language.",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Include API routers
from app.api import api_router
app.include_router(api_router)



# Initialize the database
try:
    from app.utils.db import init_database
    init_database()
    logger.info("Database initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize database: {e}")

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Welcome to the Database Query Agent Platform"}


if __name__ == "__main__":
    # Run the application
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", "8000")),
        reload=True
    )
