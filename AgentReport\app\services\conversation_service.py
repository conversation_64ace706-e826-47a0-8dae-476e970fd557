"""
ConversationService
-------------------
Simple DynamoDB wrapper for chat history + chat list.

Table schema
------------
PK   user_id            (S)
SK   session_id_ts      (S)   e.g.  "sess_123#1706047200123"
     role               (S)   'user' | 'assistant' | 'meta'
     content            (S)
     ttl                (N)   epoch seconds – Dynamo auto-deletes
     title              (S)   (only on role='meta')
"""

from __future__ import annotations

import time
from typing import List, Dict, Any

import boto3
from boto3.dynamodb.conditions import Key, Attr

from app.config.settings import AWS_REGION, CONVERSATION_TABLE

TTL_SECONDS_DEFAULT = 60 * 60 * 24 * 30  # 30 days


class ConversationService:
    """Lightweight DynamoDB helper."""

    def __init__(self) -> None:
        ddb = boto3.resource("dynamodb", region_name=AWS_REGION)
        self.table = ddb.Table(CONVERSATION_TABLE)

    # --------------------------------------------------------------------- #
    # private util helpers
    # --------------------------------------------------------------------- #
    @staticmethod
    def _sk(session_id: str) -> str:
        """Create a sort-key with millisecond precision."""
        return f"{session_id}#{int(time.time() * 1000)}"

    @staticmethod
    def _derive_title(text: str, max_words: int = 5) -> str:
        """First *max_words* words of the question → chat title."""
        words = text.strip().split()
        snippet = " ".join(words[:max_words])
        return (snippet + ("…" if len(words) > max_words else "")).strip()

    # --------------------------------------------------------------------- #
    # write helpers
    # --------------------------------------------------------------------- #
    async def append_message(
        self,
        *,
        user_id: str,
        session_id: str,
        role: str,
        content: str,
        ttl_seconds: int = TTL_SECONDS_DEFAULT,
    ) -> None:
        ttl = int(time.time()) + ttl_seconds
        self.table.put_item(
            Item={
                "user_id": user_id,
                "session_id_ts": self._sk(session_id),
                "session_id": session_id,
                "role": role,
                "content": content,
                "ttl": ttl,
            }
        )

    async def append_pair(
        self,
        *,
        user_id: str,
        session_id: str,
        user_msg: str,
        assistant_msg: str,
    ) -> None:
        """
        Store the conversational turn.

        If this is the **first** turn of the session we also insert
        a tiny "meta" row that carries the chat title.
        """
        # Detect first turn by asking DynamoDB for any row in this session
        resp = self.table.query(
            KeyConditionExpression=Key("user_id").eq(user_id)
            & Key("session_id_ts").begins_with(session_id),
            Limit=1,
        )
        is_new_session = resp.get("Count", 0) == 0

        # put the two chat messages
        await self.append_message(user_id=user_id, session_id=session_id, role="user", content=user_msg)
        await self.append_message(user_id=user_id, session_id=session_id, role="assistant", content=assistant_msg)

        # meta row only once
        if is_new_session:
            title = self._derive_title(user_msg)
            ttl   = int(time.time()) + TTL_SECONDS_DEFAULT
            self.table.put_item(
                Item={
                    "user_id": user_id,
                    "session_id_ts": f"{session_id}#meta",  # fixed suffix → easy to fetch
                    "session_id": session_id,
                    "role": "meta",
                    "title": title,
                    "last_seen": int(time.time() * 1000),
                    "message_count": 2,
                    "ttl": ttl,
                }
            )
        else:
            # update meta row stats (async – fire & forget)
            self.table.update_item(
                Key={
                    "user_id": user_id,
                    "session_id_ts": f"{session_id}#meta",
                },
                UpdateExpression="SET last_seen = :ls ADD message_count :inc",
                ExpressionAttributeValues={
                    ":ls": int(time.time() * 1000),
                    ":inc": 2,
                },
            )

    # --------------------------------------------------------------------- #
    # read helpers
    # --------------------------------------------------------------------- #
    async def get_history(
        self,
        user_id: str,
        session_id: str,
        limit: int = 20,
    ) -> List[Dict[str, str]]:
        """Return the last *limit* messages (oldest → newest)."""
        resp = self.table.query(
            KeyConditionExpression=Key("user_id").eq(user_id)
            & Key("session_id_ts").begins_with(session_id),
            ScanIndexForward=True,
            Limit=limit,
        )
        items = [
            itm for itm in resp.get("Items", [])
            if itm["role"] in ("user", "assistant")
        ]
        return [{"role": itm["role"], "content": itm["content"]} for itm in items]

    async def list_sessions(
        self, user_id: str, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Return one meta-row per chat for the sidebar.

        [{
          "session_id": "...",
          "title": "First five words…",
          "last_seen": 1706050000123,
          "message_count": 14
        }, …]
        """
        # Query all items for the user and filter for meta rows in Python
        resp = self.table.query(
            KeyConditionExpression=Key("user_id").eq(user_id),
            ProjectionExpression="session_id, title, last_seen, message_count, #r, session_id_ts",
            ExpressionAttributeNames={
                "#r": "role"  # "role" is a reserved keyword in DynamoDB
            }
        )
        
        # Filter for meta rows only (those with role='meta')
        meta_items = [
            {
                "session_id": item["session_id"],
                "title": item.get("title", ""),
                "last_seen": item.get("last_seen", 0),
                "message_count": item.get("message_count", 0)
            }
            for item in resp.get("Items", [])
            if item.get("role") == "meta"
        ]
        
        # Sort by last_seen (most recent first) and limit results
        meta_items.sort(key=lambda x: x["last_seen"], reverse=True)
        return meta_items[:limit]

    async def delete_session(
        self,
        user_id: str,
        session_id: str,
    ) -> bool:
        """
        Delete all messages and meta data for a given session.
        
        Args:
            user_id: The user ID
            session_id: The session ID to delete
            
        Returns:
            bool: True if items were deleted, False if no items found
        """
        try:
            # First, query all items for this session
            resp = self.table.query(
                KeyConditionExpression=Key("user_id").eq(user_id)
                & Key("session_id_ts").begins_with(session_id),
            )
            
            items = resp.get("Items", [])
            if not items:
                return False
            
            # Delete all items for this session
            with self.table.batch_writer() as batch:
                for item in items:
                    batch.delete_item(
                        Key={
                            "user_id": item["user_id"],
                            "session_id_ts": item["session_id_ts"]
                        }
                    )
            
            return True
            
        except Exception as e:
            # Log error but don't raise to allow graceful handling
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error deleting session {session_id} for user {user_id}: {str(e)}")
            return False

    async def rename_session(
        self,
        user_id: str,
        session_id: str,
        new_title: str,
    ) -> bool:
        """
        Rename the title of a chat session.
        
        Args:
            user_id: The user ID
            session_id: The session ID to rename
            new_title: The new title for the session
            
        Returns:
            bool: True if the title was updated, False if no meta row found
        """
        try:
            # Update the meta row for this session
            response = self.table.update_item(
                Key={
                    "user_id": user_id,
                    "session_id_ts": f"{session_id}#meta"
                },
                UpdateExpression="SET title = :new_title",
                ExpressionAttributeValues={
                    ":new_title": new_title
                },
                ConditionExpression=Attr("role").eq("meta"),
                ReturnValues="UPDATED_NEW"
            )
            return "Attributes" in response
            
        except self.table.meta.client.exceptions.ConditionalCheckFailedException:
            # No meta row found for this session
            return False
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error renaming session {session_id} for user {user_id}: {str(e)}")
            return False
