{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/cn.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/date.ts"], "sourcesContent": ["// Date utility functions\n\nexport const formatDate = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n};\n\nexport const formatDateTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n};\n\nexport const formatRelativeTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n\n  return formatDate(d);\n};\n\nexport const isTokenExpired = (expiresAt: string): boolean => {\n  const expirationTime = new Date(expiresAt).getTime();\n  const currentTime = new Date().getTime();\n  const bufferTime = 5 * 60 * 1000; // 5 minutes buffer\n  \n  return currentTime >= (expirationTime - bufferTime);\n};\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;AAElB,MAAM,aAAa,CAAC;IACzB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,cAAc,CAAC,SAAS;QAC/B,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;IACrE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;IAC5D;IAEA,OAAO,WAAW;AACpB;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,iBAAiB,IAAI,KAAK,WAAW,OAAO;IAClD,MAAM,cAAc,IAAI,OAAO,OAAO;IACtC,MAAM,aAAa,IAAI,KAAK,MAAM,mBAAmB;IAErD,OAAO,eAAgB,iBAAiB;AAC1C", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/format.ts"], "sourcesContent": ["// Formatting utility functions\n\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\nexport const formatNumber = (num: number): string => {\n  return new Intl.NumberFormat('en-US').format(num);\n};\n\nexport const formatCurrency = (amount: number, currency = 'USD'): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n};\n\nexport const truncateText = (text: string, maxLength: number): string => {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n};\n\nexport const capitalizeFirst = (str: string): string => {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n};\n\nexport const camelToTitle = (str: string): string => {\n  return str\n    .replace(/([A-Z])/g, ' $1')\n    .replace(/^./, (str) => str.toUpperCase())\n    .trim();\n};\n\nexport const generateSessionId = (): string => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;AAExB,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,MAAM,iBAAiB,CAAC,QAAgB,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,eAAe,CAAC,MAAc;IACzC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,YAAY,OACpB,OAAO,CAAC,MAAM,CAAC,MAAQ,IAAI,WAAW,IACtC,IAAI;AACT;AAEO,MAAM,oBAAoB;IAC/B,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/validation.ts"], "sourcesContent": ["// Validation utility functions\n\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const isValidPassword = (password: string): boolean => {\n  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number\n  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;\n  return passwordRegex.test(password);\n};\n\nexport const isValidUrl = (url: string): boolean => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const isValidPort = (port: number): boolean => {\n  return port >= 1 && port <= 65535;\n};\n\nexport const isValidDatabaseName = (name: string): boolean => {\n  // Basic validation for database names\n  const dbNameRegex = /^[a-zA-Z][a-zA-Z0-9_-]*$/;\n  return dbNameRegex.test(name) && name.length >= 2 && name.length <= 50;\n};\n\nexport const sanitizeInput = (input: string): string => {\n  return input.trim().replace(/[<>]/g, '');\n};\n\nexport const validateRequired = (value: any): boolean => {\n  if (typeof value === 'string') {\n    return value.trim().length > 0;\n  }\n  return value !== null && value !== undefined;\n};\n\nexport const validateMinLength = (value: string, minLength: number): boolean => {\n  return value.length >= minLength;\n};\n\nexport const validateMaxLength = (value: string, maxLength: number): boolean => {\n  return value.length <= maxLength;\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;AAExB,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,kBAAkB,CAAC;IAC9B,4DAA4D;IAC5D,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,QAAQ,KAAK,QAAQ;AAC9B;AAEO,MAAM,sBAAsB,CAAC;IAClC,sCAAsC;IACtC,MAAM,cAAc;IACpB,OAAO,YAAY,IAAI,CAAC,SAAS,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI;AACtE;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,MAAM,IAAI,GAAG,MAAM,GAAG;IAC/B;IACA,OAAO,UAAU,QAAQ,UAAU;AACrC;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/index.ts"], "sourcesContent": ["// Main utility exports\n\nexport * from './cn';\nexport * from './date';\nexport * from './format';\nexport * from './validation';\n\n// Re-export the original cn function for backward compatibility\nexport { cn } from './cn';\n"], "names": [], "mappings": "AAAA,uBAAuB;;AAEvB;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAAA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { ChevronRight } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<\"nav\"> & {\n    separator?: React.ComponentType<{ className?: string }>;\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label=\"breadcrumb\" {...props} />);\nBreadcrumb.displayName = \"Breadcrumb\";\n\nconst BreadcrumbList = React.forwardRef<\n  HTMLOListElement,\n  React.ComponentPropsWithoutRef<\"ol\">\n>(({ className, ...props }, ref) => (\n  <ol\n    ref={ref}\n    className={cn(\n      \"flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5\",\n      className\n    )}\n    {...props}\n  />\n));\nBreadcrumbList.displayName = \"BreadcrumbList\";\n\nconst BreadcrumbItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentPropsWithoutRef<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    className={cn(\"inline-flex items-center gap-1.5\", className)}\n    {...props}\n  />\n));\nBreadcrumbItem.displayName = \"BreadcrumbItem\";\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<\"a\"> & {\n    asChild?: boolean;\n  }\n>(({ asChild, className, ...props }, ref) => {\n  if (asChild) {\n    return <React.Fragment {...props} />;\n  }\n\n  return (\n    <a\n      ref={ref}\n      className={cn(\"transition-colors hover:text-foreground\", className)}\n      {...props}\n    />\n  );\n});\nBreadcrumbLink.displayName = \"BreadcrumbLink\";\n\nconst BreadcrumbPage = React.forwardRef<\n  HTMLSpanElement,\n  React.ComponentPropsWithoutRef<\"span\">\n>(({ className, ...props }, ref) => (\n  <span\n    ref={ref}\n    role=\"link\"\n    aria-disabled=\"true\"\n    aria-current=\"page\"\n    className={cn(\"font-normal text-foreground\", className)}\n    {...props}\n  />\n));\nBreadcrumbPage.displayName = \"BreadcrumbPage\";\n\nconst BreadcrumbSeparator = ({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) => (\n  <li\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"[&>svg]:size-3.5\", className)}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n);\nBreadcrumbSeparator.displayName = \"BreadcrumbSeparator\";\n\nconst BreadcrumbEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <svg\n      width=\"15\"\n      height=\"15\"\n      viewBox=\"0 0 15 15\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        d=\"M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z\"\n        fill=\"currentColor\"\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n      />\n    </svg>\n    <span className=\"sr-only\">More</span>\n  </span>\n);\nBreadcrumbEllipsis.displayName = \"BreadcrumbEllipsis\";\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAKhC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,6LAAC;QAAI,KAAK;QAAK,cAAW;QAAc,GAAG,KAAK;;;;;;;AACzE,WAAW,WAAW,GAAG;AAEzB,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKpC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACnC,IAAI,SAAS;QACX,qBAAO,6LAAC,6JAAA,CAAA,WAAc;YAAE,GAAG,KAAK;;;;;;IAClC;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB,iBAC3B,6LAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;OAXxB;AAcN,oBAAoB,WAAW,GAAG;AAElC,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OAC0B,iBAC7B,6LAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;0BAEN,cAAA,6LAAC;oBACC,GAAE;oBACF,MAAK;oBACL,UAAS;oBACT,UAAS;;;;;;;;;;;0BAGb,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;OAxBxB;AA2BN,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\r\nimport { Menu, LayoutDashboard, ChevronRight, Plus, Brain, Download, MessageSquare } from 'lucide-react';\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { useTheme } from \"@/providers/theme-provider\";\r\nimport { ThemeToggle } from '@/components/ui/theme-toggle';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\nimport {\r\n  Breadcrumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from '@/components/ui/breadcrumb';\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated } = useAuth();\r\n  const { theme, setTheme } = useTheme();\r\n  const { pageInfo, actions } = usePageHeader();\r\n  const pathname = usePathname();\r\n  \r\n  // Check if we have breadcrumbs to show\r\n  const hasBreadcrumbs = pageInfo.breadcrumbs && pageInfo.breadcrumbs.length > 1;\r\n  \r\n  // Get chart-related state from context actions\r\n  const { \r\n    onCreateChart, \r\n    chartCount = 0, \r\n    maxCharts = 12, \r\n    onCreateAnalysis, \r\n    isCreatingAnalysis = false,\r\n    onCreateDashboard,\r\n    onExport,\r\n    onToggleChat,\r\n    isChatOpen = false\r\n  } = actions;\r\n  const canCreateChart = chartCount < maxCharts;\r\n  \r\n  // Show Create Chart Button on dashboard pages when the callback is provided\r\n  const isDashboardPage = pathname === '/dashboard';\r\n  const isAIWorkflowsPage = pathname === '/ai-workflows';\r\n  const showCreateChartButton = isDashboardPage && onCreateChart;\r\n  const showCreateDashboardButton = isDashboardPage && onCreateDashboard;\r\n  const showCreateAnalysisButton = isAIWorkflowsPage && onCreateAnalysis;\r\n  const showProjectActions = isAIWorkflowsPage && (onExport || onToggleChat);\r\n\r\n  // Get the appropriate icon for breadcrumbs\r\n  const getBreadcrumbIcon = () => {\r\n    if (pathname === '/dashboard') return LayoutDashboard;\r\n    if (pathname === '/ai-workflows') return Brain;\r\n    return pageInfo.icon || LayoutDashboard;\r\n  };\r\n\r\n  const BreadcrumbIcon = getBreadcrumbIcon();\r\n\r\n  return (\r\n    <header className=\"flex items-center justify-between whitespace-nowrap border-b border-solid border-sidebar-border bg-sidebar-bg px-4 py-2 h-12\">\r\n      <div className=\"flex items-center gap-3 text-sidebar-text-primary\">\r\n        {isAuthenticated && (\r\n          <div className=\"lg:hidden\">\r\n            <Sheet>\r\n              <SheetTrigger asChild>\r\n                <Button variant=\"outline\" size=\"icon\" className=\"h-7 w-7\">\r\n                  <Menu className=\"h-4 w-4\" />\r\n                </Button>\r\n              </SheetTrigger>\r\n            </Sheet>\r\n          </div>\r\n        )}\r\n        \r\n        {/* Dynamic Breadcrumb Navigation or Page Title */}\r\n        {hasBreadcrumbs ? (\r\n          <Breadcrumb>\r\n            <BreadcrumbList>\r\n              {pageInfo.breadcrumbs!.map((breadcrumb, index) => (\r\n                <React.Fragment key={breadcrumb.label}>\r\n                  <BreadcrumbItem>\r\n                    {index === 0 ? (\r\n                      // First breadcrumb item (with icon)\r\n                      breadcrumb.onClick || breadcrumb.href ? (\r\n                        <BreadcrumbLink \r\n                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}\r\n                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}\r\n                          className=\"flex items-center space-x-1.5 cursor-pointer hover:text-primary text-sm font-medium\"\r\n                        >\r\n                          <BreadcrumbIcon className=\"h-4 w-4\" />\r\n                          <span>{breadcrumb.label}</span>\r\n                        </BreadcrumbLink>\r\n                      ) : (\r\n                        <div className=\"flex items-center space-x-1.5 text-sm font-medium\">\r\n                          <BreadcrumbIcon className=\"h-4 w-4\" />\r\n                          <span>{breadcrumb.label}</span>\r\n                        </div>\r\n                      )\r\n                    ) : (\r\n                      // Subsequent breadcrumb items\r\n                      index === pageInfo.breadcrumbs!.length - 1 ? (\r\n                        <BreadcrumbPage className=\"font-medium text-sm\">\r\n                          {breadcrumb.label}\r\n                        </BreadcrumbPage>\r\n                      ) : (\r\n                        <BreadcrumbLink \r\n                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}\r\n                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}\r\n                          className=\"cursor-pointer hover:text-primary text-sm font-medium\"\r\n                        >\r\n                          {breadcrumb.label}\r\n                        </BreadcrumbLink>\r\n                      )\r\n                    )}\r\n                  </BreadcrumbItem>\r\n                  \r\n                  {/* Separator */}\r\n                  {index < pageInfo.breadcrumbs!.length - 1 && (\r\n                    <BreadcrumbSeparator>\r\n                      <ChevronRight className=\"h-3 w-3\" />\r\n                    </BreadcrumbSeparator>\r\n                  )}\r\n                </React.Fragment>\r\n              ))}\r\n            </BreadcrumbList>\r\n          </Breadcrumb>\r\n        ) : (\r\n          <h1 className=\"text-sm font-medium leading-tight tracking-[-0.015em] text-sidebar-text-primary\">\r\n            {pageInfo.title}\r\n          </h1>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right side actions */}\r\n      <div className=\"flex items-center gap-2\">\r\n        {showCreateChartButton && (\r\n          <Button\r\n            onClick={onCreateChart}\r\n            disabled={!canCreateChart}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            title={!canCreateChart ? `Maximum of ${maxCharts} charts allowed` : undefined}\r\n            onMouseEnter={(e) => {\r\n              if (canCreateChart) {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (canCreateChart) {\r\n                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n              }\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            Chart\r\n          </Button>\r\n        )}\r\n        {showCreateDashboardButton && (\r\n          <Button\r\n            onClick={onCreateDashboard}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            onMouseEnter={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            Dashboard\r\n          </Button>\r\n        )}\r\n        {showCreateAnalysisButton && (\r\n          <Button\r\n            onClick={onCreateAnalysis}\r\n            disabled={isCreatingAnalysis}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            title={isCreatingAnalysis ? \"Analysis is being created\" : undefined}\r\n            onMouseEnter={(e) => {\r\n              if (!isCreatingAnalysis) {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (!isCreatingAnalysis) {\r\n                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n              }\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            {isCreatingAnalysis ? 'Creating...' : 'Analysis'}\r\n          </Button>\r\n        )}\r\n        {showProjectActions && (\r\n          <>\r\n            {onExport && (\r\n              <Button\r\n                onClick={onExport}\r\n                className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n                style={{\r\n                  backgroundColor: 'var(--surface-selected)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n                }}\r\n              >\r\n                <Download className=\"h-3 w-3 mr-1.5\" />\r\n                Export\r\n              </Button>\r\n            )}\r\n            {onToggleChat && (\r\n              <Button\r\n                onClick={onToggleChat}\r\n                className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n                style={{\r\n                  backgroundColor: isChatOpen ? 'var(--interactive-bg-secondary-hover)' : 'var(--surface-selected)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = isChatOpen ? 'var(--interactive-bg-secondary-hover)' : 'var(--surface-selected)';\r\n                }}\r\n              >\r\n                <MessageSquare className=\"h-3 w-3 mr-1.5\" />\r\n                Chat\r\n              </Button>\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;;;AAXA;;;;;;;;;;AAoBA,MAAM,SAAS;;IACb,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAC1C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,uCAAuC;IACvC,MAAM,iBAAiB,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG;IAE7E,+CAA+C;IAC/C,MAAM,EACJ,aAAa,EACb,aAAa,CAAC,EACd,YAAY,EAAE,EACd,gBAAgB,EAChB,qBAAqB,KAAK,EAC1B,iBAAiB,EACjB,QAAQ,EACR,YAAY,EACZ,aAAa,KAAK,EACnB,GAAG;IACJ,MAAM,iBAAiB,aAAa;IAEpC,4EAA4E;IAC5E,MAAM,kBAAkB,aAAa;IACrC,MAAM,oBAAoB,aAAa;IACvC,MAAM,wBAAwB,mBAAmB;IACjD,MAAM,4BAA4B,mBAAmB;IACrD,MAAM,2BAA2B,qBAAqB;IACtD,MAAM,qBAAqB,qBAAqB,CAAC,YAAY,YAAY;IAEzE,2CAA2C;IAC3C,MAAM,oBAAoB;QACxB,IAAI,aAAa,cAAc,OAAO,+NAAA,CAAA,kBAAe;QACrD,IAAI,aAAa,iBAAiB,OAAO,uMAAA,CAAA,QAAK;QAC9C,OAAO,SAAS,IAAI,IAAI,+NAAA,CAAA,kBAAe;IACzC;IAEA,MAAM,iBAAiB;IAEvB,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;oBACZ,iCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;sCACJ,cAAA,6LAAC,oIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAO,WAAU;8CAC9C,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQzB,+BACC,6LAAC,yIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,yIAAA,CAAA,iBAAc;sCACZ,SAAS,WAAW,CAAE,GAAG,CAAC,CAAC,YAAY,sBACtC,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;sDACb,6LAAC,yIAAA,CAAA,iBAAc;sDACZ,UAAU,IACT,oCAAoC;4CACpC,WAAW,OAAO,IAAI,WAAW,IAAI,iBACnC,6LAAC,yIAAA,CAAA,iBAAc;gDACZ,GAAI,WAAW,IAAI,GAAG;oDAAE,MAAM,WAAW,IAAI;gDAAC,IAAI,CAAC,CAAC;gDACpD,GAAI,WAAW,OAAO,GAAG;oDAAE,SAAS,WAAW,OAAO;gDAAC,IAAI,CAAC,CAAC;gDAC9D,WAAU;;kEAEV,6LAAC;wDAAe,WAAU;;;;;;kEAC1B,6LAAC;kEAAM,WAAW,KAAK;;;;;;;;;;;qEAGzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAe,WAAU;;;;;;kEAC1B,6LAAC;kEAAM,WAAW,KAAK;;;;;;;;;;;uDAI3B,8BAA8B;4CAC9B,UAAU,SAAS,WAAW,CAAE,MAAM,GAAG,kBACvC,6LAAC,yIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,WAAW,KAAK;;;;;qEAGnB,6LAAC,yIAAA,CAAA,iBAAc;gDACZ,GAAI,WAAW,IAAI,GAAG;oDAAE,MAAM,WAAW,IAAI;gDAAC,IAAI,CAAC,CAAC;gDACpD,GAAI,WAAW,OAAO,GAAG;oDAAE,SAAS,WAAW,OAAO;gDAAC,IAAI,CAAC,CAAC;gDAC9D,WAAU;0DAET,WAAW,KAAK;;;;;;;;;;;wCAOxB,QAAQ,SAAS,WAAW,CAAE,MAAM,GAAG,mBACtC,6LAAC,yIAAA,CAAA,sBAAmB;sDAClB,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;mCAxCT,WAAW,KAAK;;;;;;;;;;;;;;6CAgD3C,6LAAC;wBAAG,WAAU;kCACX,SAAS,KAAK;;;;;;;;;;;;0BAMrB,6LAAC;gBAAI,WAAU;;oBACZ,uCACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,OAAO,CAAC,iBAAiB,CAAC,WAAW,EAAE,UAAU,eAAe,CAAC,GAAG;wBACpE,cAAc,CAAC;4BACb,IAAI,gBAAgB;gCAClB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,gBAAgB;gCAClB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;;0CAEA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAmB;;;;;;;oBAItC,2CACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC1C;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC1C;;0CAEA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAmB;;;;;;;oBAItC,0CACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,OAAO,qBAAqB,8BAA8B;wBAC1D,cAAc,CAAC;4BACb,IAAI,CAAC,oBAAoB;gCACvB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,oBAAoB;gCACvB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;;0CAEA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,qBAAqB,gBAAgB;;;;;;;oBAGzC,oCACC;;4BACG,0BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,OAAO;gCACT;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;;kDAEA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAmB;;;;;;;4BAI1C,8BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,iBAAiB,aAAa,0CAA0C;oCACxE,OAAO;gCACT;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,aAAa,0CAA0C;gCACjG;;kDAEA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAmB;;;;;;;;;;;;;;;;;;;;;AAS5D;GAtOM;;QACwB,mIAAA,CAAA,UAAO;QACP,yIAAA,CAAA,WAAQ;QACN,yIAAA,CAAA,gBAAa;QAC1B,qIAAA,CAAA,cAAW;;;KAJxB;uCAwOS", "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground text-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+nBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Sidebar.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport Link from 'next/link';\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { MoreVertical, MessageCircle, Database, Plus, BarChart3, MessageCirclePlus, LayoutDashboard, Brain, User, Settings, LogOut } from 'lucide-react';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useChatHistory } from \"@/providers/ChatHistoryContext\";\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface SidebarProps {\r\n  onNewChat: () => void;\r\n  onToggleCollapse?: (collapsed: boolean) => void;\r\n  isCreatingNewChat?: boolean;\r\n}\r\n\r\nconst Sidebar: React.FC<SidebarProps> = ({ onNewChat, onToggleCollapse, isCreatingNewChat = false }) => {\r\n  const { \r\n    chatHistory, \r\n    isLoadingChats, \r\n    deleteChat,\r\n    renameChat, \r\n  } = useChatHistory();\r\n  const { logout, user } = useAuth();\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const [menuOpenId, setMenuOpenId] = useState<string | null>(null);\r\n  const [renameId, setRenameId] = useState<string | null>(null);\r\n  const [renameValue, setRenameValue] = useState<string>(\"\");\r\n  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);\r\n\r\n  console.log('Sidebar component rendered. Current pathname:', pathname);\r\n\r\n  // Extract chatId from pathname (e.g., /chat/123)\r\n  const currentChatId = pathname?.split('/').includes('chat') ? pathname.split('/').pop() : null;\r\n  console.log('Current chatId extracted from pathname:', currentChatId);\r\n\r\n  const handleRename = (id: string, currentTitle: string) => {\r\n    console.log('Renaming chat with id:', id, 'and current title:', currentTitle);\r\n    setRenameId(id);\r\n    setRenameValue(currentTitle);\r\n    setMenuOpenId(null);\r\n  };\r\n\r\n  const handleRenameSubmit = (id: string) => {\r\n    console.log('Submitting rename for chat with id:', id, 'and new title:', renameValue);\r\n    if (renameValue.trim()) {\r\n      renameChat(id, renameValue.trim());\r\n    }\r\n    setRenameId(null);\r\n    setRenameValue(\"\");\r\n  };\r\n\r\n  const handleDelete = async (chatId: string) => {\r\n    try {\r\n      await deleteChat(chatId);\r\n      setMenuOpenId(null);\r\n\r\n      // 🚚 After successful deletion, handle navigation if the deleted chat was active\r\n      if (currentChatId === chatId) {\r\n        // Get the updated chat list (the state will have been updated by deleteChat)\r\n        const remainingChats = chatHistory.filter(chat => chat.id !== chatId);\r\n\r\n        if (remainingChats.length > 0) {\r\n          // Navigate to the most recently updated chat (first in list)\r\n          router.push(`/chat/${remainingChats[0].id}`);\r\n        } else {\r\n          // No chats left – start a fresh chat\r\n          onNewChat();\r\n          // Fallback navigate to generic chat route to trigger new-chat UI\r\n          router.push('/chat');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete chat:', error);\r\n      // You could add a toast notification here if you have one\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (timestamp: Date) => {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - timestamp.getTime();\r\n    const diffHours = diffMs / (1000 * 60 * 60);\r\n    const diffDays = diffMs / (1000 * 60 * 60 * 24);\r\n\r\n    if (diffHours < 1) {\r\n      return 'Just now';\r\n    } else if (diffHours < 24) {\r\n      return `${Math.floor(diffHours)}h ago`;\r\n    } else if (diffDays < 7) {\r\n      return `${Math.floor(diffDays)}d ago`;\r\n    } else {\r\n      return timestamp.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  const handleToggleCollapse = () => {\r\n    const newCollapsedState = !isCollapsed;\r\n    setIsCollapsed(newCollapsedState);\r\n    onToggleCollapse?.(newCollapsedState);\r\n  };\r\n\r\n  return (\r\n    <aside \r\n      className={`hidden lg:flex flex-col ${isCollapsed ? 'w-16' : 'w-sidebar'} h-full min-h-0 transition-all duration-300 flex-shrink-0 relative overflow-y-auto`}\r\n      style={{\r\n        backgroundColor: 'var(--sidebar-bg)',\r\n        borderRight: '1px solid var(--sidebar-border)',\r\n        scrollbarColor: 'var(--sidebar-surface-tertiary) transparent'\r\n      }}\r\n    >\r\n      {/* Toggle Button */}\r\n      <div className={`sticky top-0 z-10 p-2 transition-all duration-300 ease-in-out ${isCollapsed ? 'flex justify-center' : 'flex justify-end'}`}\r\n        style={{ backgroundColor: 'var(--sidebar-bg)' }}\r\n      >\r\n        <Button \r\n          variant=\"ghost\" \r\n          size=\"icon\"\r\n          className=\"w-10 h-10 flex items-center justify-center rounded-lg border-0 transition-all duration-300 ease-in-out transform-gpu\"\r\n          style={{ \r\n            color: 'var(--sidebar-icon) !important',\r\n            backgroundColor: 'transparent !important'\r\n          }}\r\n          onMouseEnter={(e) => {\r\n            e.currentTarget.style.setProperty('background-color', 'var(--interactive-bg-secondary-hover)', 'important');\r\n            e.currentTarget.style.transform = 'scale(1.05)';\r\n          }}\r\n          onMouseLeave={(e) => {\r\n            e.currentTarget.style.setProperty('background-color', 'transparent', 'important');\r\n            e.currentTarget.style.transform = 'scale(1)';\r\n          }}\r\n          onClick={handleToggleCollapse}\r\n        >\r\n          <svg \r\n            className=\"h-5 w-5 transition-transform duration-300 ease-in-out\" \r\n            viewBox=\"0 0 20 20\" \r\n            fill=\"currentColor\" \r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            style={{\r\n              transform: isCollapsed ? 'scaleX(-1)' : 'scaleX(1)'\r\n            }}\r\n          >\r\n            <path d=\"M6.83496 3.99992C6.38353 4.00411 6.01421 4.0122 5.69824 4.03801C5.31232 4.06954 5.03904 4.12266 4.82227 4.20012L4.62207 4.28606C4.18264 4.50996 3.81498 4.85035 3.55859 5.26848L3.45605 5.45207C3.33013 5.69922 3.25006 6.01354 3.20801 6.52824C3.16533 7.05065 3.16504 7.71885 3.16504 8.66301V11.3271C3.16504 12.2712 3.16533 12.9394 3.20801 13.4618C3.25006 13.9766 3.33013 14.2909 3.45605 14.538L3.55859 14.7216C3.81498 15.1397 4.18266 15.4801 4.62207 15.704L4.82227 15.79C5.03904 15.8674 5.31234 15.9205 5.69824 15.9521C6.01398 15.9779 6.383 15.986 6.83398 15.9902L6.83496 3.99992ZM18.165 11.3271C18.165 12.2493 18.1653 12.9811 18.1172 13.5702C18.0745 14.0924 17.9916 14.5472 17.8125 14.9648L17.7295 15.1415C17.394 15.8 16.8834 16.3511 16.2568 16.7353L15.9814 16.8896C15.5157 17.1268 15.0069 17.2285 14.4102 17.2773C13.821 17.3254 13.0893 17.3251 12.167 17.3251H7.83301C6.91071 17.3251 6.17898 17.3254 5.58984 17.2773C5.06757 17.2346 4.61294 17.1508 4.19531 16.9716L4.01855 16.8896C3.36014 16.5541 2.80898 16.0434 2.4248 15.4169L2.27051 15.1415C2.03328 14.6758 1.93158 14.167 1.88281 13.5702C1.83468 12.9811 1.83496 12.2493 1.83496 11.3271V8.66301C1.83496 7.74072 1.83468 7.00898 1.88281 6.41985C1.93157 5.82309 2.03329 5.31432 2.27051 4.84856L2.4248 4.57317C2.80898 3.94666 3.36012 3.436 4.01855 3.10051L4.19531 3.0175C4.61285 2.83843 5.06771 2.75548 5.58984 2.71281C6.17898 2.66468 6.91071 2.66496 7.83301 2.66496H12.17C13.0893 2.66496 13.821 2.66468 14.4102 2.71281C15.0069 2.76157 15.5157 2.86329 15.9814 3.10051L16.2568 3.25481C16.8833 3.63898 17.394 4.19012 17.7295 4.84856L17.8125 5.02531C17.9916 5.44285 18.0745 5.89771 18.1172 6.41985C18.1653 7.00898 18.165 7.74072 18.165 8.66301V11.3271ZM8.16406 15.995H12.167C13.1112 15.995 13.7794 15.9947 14.3018 15.9521C14.8164 15.91 15.1308 15.8299 15.3779 15.704L15.5615 15.6015C15.9797 15.3451 16.32 14.9774 16.5439 14.538L16.6299 14.3378C16.7074 14.121 16.7605 13.8478 16.792 13.4618C16.8347 12.9394 16.835 12.2712 16.835 11.3271V8.66301C16.835 7.71885 16.8347 7.05065 16.792 6.52824C16.7605 6.14232 16.7073 5.86904 16.6299 5.65227L16.5439 5.45207C16.32 5.01264 15.9796 4.64498 15.5615 4.3886L15.3779 4.28606C15.1308 4.16013 14.8165 4.08006 14.3018 4.03801C13.7794 3.99533 13.1112 3.99504 12.167 3.99504H8.16406C8.16407 3.99667 8.16504 3.99829 8.16504 3.99992L8.16406 15.995Z\"></path>\r\n          </svg>\r\n        </Button>\r\n      </div>\r\n      \r\n      {!isCollapsed && (\r\n        <div className=\"flex flex-col h-full px-3\">\r\n          {/* Navigation Links */}\r\n          <div className=\"space-y-1 mb-4\">\r\n            <Link href=\"/dashboard\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/dashboard' ? '' : ''}`}\r\n                style={{\r\n                  color: pathname === '/dashboard' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/dashboard' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/dashboard') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/dashboard') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <LayoutDashboard className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Dashboard\r\n              </Button>\r\n            </Link>\r\n            \r\n            <Link href=\"/reports\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/reports' ? '' : ''}`}\r\n                style={{\r\n                  color: pathname === '/reports' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/reports' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/reports') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/reports') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <BarChart3 className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Reports\r\n              </Button>\r\n            </Link>\r\n            \r\n            <Link href=\"/datasources\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10`}\r\n                style={{\r\n                  color: pathname === '/datasources' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/datasources' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/datasources') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/datasources') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <Database className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Data Sources\r\n              </Button>\r\n            </Link>\r\n\r\n            <Link href=\"/ai-workflows\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10`}\r\n                style={{\r\n                  color: pathname === '/ai-workflows' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/ai-workflows' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/ai-workflows') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/ai-workflows') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <Brain className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                AI Workflows\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n          \r\n          {/* New Chat Button */}\r\n          <div className=\"mb-4\">\r\n            <Button \r\n              variant=\"ghost\" \r\n              className=\"w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10\" \r\n              onClick={onNewChat}\r\n              disabled={isCreatingNewChat}\r\n              style={{\r\n                color: 'var(--sidebar-text-secondary)',\r\n                backgroundColor: 'transparent'\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                if (!isCreatingNewChat) {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                if (!isCreatingNewChat) {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }\r\n              }}\r\n            >\r\n              <Plus className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n              {isCreatingNewChat ? 'Creating...' : 'New Chat'}\r\n            </Button>\r\n          </div>\r\n          \r\n          {/* Chat History Section */}\r\n          <div className=\"flex flex-col gap-1 overflow-y-auto flex-1 pb-4\">\r\n            <h3 \r\n              className=\"text-xs font-medium uppercase tracking-wider px-2 py-2 sticky top-0 z-10\"\r\n              style={{ \r\n                color: 'var(--sidebar-text-tertiary)',\r\n                backgroundColor: 'var(--sidebar-bg)'\r\n              }}\r\n            >\r\n              Chat History\r\n            </h3>\r\n            \r\n            {isLoadingChats && (\r\n              <div className=\"flex items-center justify-center py-8\">\r\n                <div className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  Loading chats...\r\n                </div>\r\n              </div>\r\n            )}\r\n            \r\n            {!isLoadingChats && chatHistory.map((chat) => {\r\n              const isActive = chat.id === currentChatId;\r\n              const isRenaming = renameId === chat.id;\r\n              return (\r\n                <div\r\n                  key={chat.id}\r\n                  className={`group flex items-center justify-between py-2 px-2 rounded-lg cursor-pointer transition-all duration-200 ${isActive ? '' : ''}`}\r\n                  style={{\r\n                    backgroundColor: isActive ? 'var(--surface-selected)' : 'transparent'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'transparent';\r\n                    }\r\n                  }}\r\n                  onClick={() => router.push(`/chat/${chat.id}`)}\r\n                >\r\n                  {isRenaming ? (\r\n                    <form\r\n                      onSubmit={e => { e.preventDefault(); handleRenameSubmit(chat.id); }}\r\n                      className=\"flex items-center gap-2 w-full\"\r\n                    >\r\n                      <input\r\n                        autoFocus\r\n                        className=\"truncate max-w-[120px] text-sm font-normal bg-transparent border-b border-blue-400 outline-none px-1\"\r\n                        style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        value={renameValue}\r\n                        onChange={e => setRenameValue(e.target.value)}\r\n                        onKeyDown={e => { if (e.key === 'Escape') setRenameId(null); }}\r\n                      />\r\n                      <Button \r\n                        type=\"submit\" \r\n                        size=\"sm\" \r\n                        variant=\"ghost\" \r\n                        className=\"text-blue-500 px-2 text-xs rounded border-0\"\r\n                      >\r\n                        Save\r\n                      </Button>\r\n                    </form>\r\n                  ) : (\r\n                    <>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <span \r\n                          className=\"truncate text-sm font-normal block\"\r\n                          style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        >\r\n                          {chat.title}\r\n                        </span>\r\n                      </div>\r\n                      \r\n                      <div className=\"relative ml-2 flex-shrink-0\" onClick={e => e.stopPropagation()}>\r\n                        <DropdownMenu>\r\n                          <DropdownMenuTrigger asChild>\r\n                            <Button\r\n                              type=\"button\"\r\n                              size=\"icon\"\r\n                              variant=\"ghost\"\r\n                              className=\"w-7 h-7 p-0 opacity-0 group-hover:opacity-100 transition-all duration-200 rounded border-0\"\r\n                              style={{ color: 'var(--sidebar-icon)' }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                              aria-label=\"More actions\"\r\n                            >\r\n                              <MoreVertical className=\"w-4 h-4\" />\r\n                            </Button>\r\n                          </DropdownMenuTrigger>\r\n                          <DropdownMenuContent \r\n                            align=\"start\" \r\n                            side=\"bottom\" \r\n                            sideOffset={8} \r\n                            className=\"border-none shadow-xl rounded-xl p-2\"\r\n                            style={{\r\n                              backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                              color: 'var(--sidebar-text-primary)'\r\n                            }}\r\n                          >\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleRename(chat.id, chat.title)}\r\n                              className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                              style={{ \r\n                                color: 'var(--sidebar-text-primary)',\r\n                                backgroundColor: 'transparent'\r\n                              }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                            >\r\n                              Rename\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleDelete(chat.id)}\r\n                              className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                              style={{ \r\n                                color: '#ff8583',\r\n                                backgroundColor: 'transparent'\r\n                              }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                            >\r\n                              Delete\r\n                            </DropdownMenuItem>\r\n                          </DropdownMenuContent>\r\n                        </DropdownMenu>\r\n                      </div>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              );\r\n            })}\r\n            \r\n            {!isLoadingChats && chatHistory.length === 0 && (\r\n              <div className=\"text-center py-8\">\r\n                <MessageCirclePlus \r\n                  className=\"h-12 w-12 mx-auto mb-3\" \r\n                  style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                />\r\n                <p className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  No chats yet\r\n                </p>\r\n                <p className=\"text-xs mt-1\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  Start a new conversation\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          {/* Profile Section */}\r\n          <div className=\"mt-auto pt-2 pb-4 border-t\" style={{ borderColor: 'var(--sidebar-border)' }}>\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <div className=\"flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-all duration-200 hover:bg-opacity-80\" \r\n                     style={{ backgroundColor: 'transparent' }}\r\n                     onMouseEnter={(e) => {\r\n                       e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                     }}\r\n                     onMouseLeave={(e) => {\r\n                       e.currentTarget.style.backgroundColor = 'transparent';\r\n                     }}>\r\n                  <div\r\n                    className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600\"\r\n                    style={{backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y\")'}}\r\n                  />\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <span className=\"text-sm font-medium block truncate\" style={{ color: 'var(--sidebar-text-primary)' }}>\r\n                      {'User'}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent \r\n                align=\"start\" \r\n                side=\"top\" \r\n                sideOffset={8} \r\n                className=\"border-none shadow-xl rounded-xl p-2\"\r\n                style={{\r\n                  backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n              >\r\n                <DropdownMenuItem asChild>\r\n                  <Link href=\"/profile\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                        style={{ \r\n                          color: 'var(--sidebar-text-primary)',\r\n                          backgroundColor: 'transparent'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'transparent';\r\n                        }}>\r\n                    <User className=\"w-4 h-4\" />\r\n                    Profile\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem asChild>\r\n                  <Link href=\"/settings\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                        style={{ \r\n                          color: 'var(--sidebar-text-primary)',\r\n                          backgroundColor: 'transparent'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'transparent';\r\n                        }}>\r\n                    <Settings className=\"w-4 h-4\" />\r\n                    Settings\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem \r\n                  onClick={logout} \r\n                  className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                  style={{ \r\n                    color: '#ff8583',\r\n                    backgroundColor: 'transparent'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }}\r\n                >\r\n                  <LogOut className=\"w-4 h-4\" />\r\n                  Logout\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      {/* Profile Section for Collapsed State */}\r\n      {isCollapsed && (\r\n        <div className=\"mt-auto p-2 pb-4 border-t\" style={{ borderColor: 'var(--sidebar-border)' }}>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <div className=\"flex justify-center\">\r\n                <div\r\n                  className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600 cursor-pointer transition-all duration-200 hover:scale-105\"\r\n                  style={{backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y\")'}}\r\n                />\r\n              </div>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent \r\n              align=\"start\" \r\n              side=\"right\" \r\n              sideOffset={8} \r\n              className=\"border-none shadow-xl rounded-xl p-2\"\r\n              style={{\r\n                backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                color: 'var(--sidebar-text-primary)'\r\n              }}\r\n            >\r\n              <DropdownMenuItem asChild>\r\n                <Link href=\"/profile\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                      style={{ \r\n                        color: 'var(--sidebar-text-primary)',\r\n                        backgroundColor: 'transparent'\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}>\r\n                  <User className=\"w-4 h-4\" />\r\n                  Profile\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem asChild>\r\n                <Link href=\"/settings\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                      style={{ \r\n                        color: 'var(--sidebar-text-primary)',\r\n                        backgroundColor: 'transparent'\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}>\r\n                  <Settings className=\"w-4 h-4\" />\r\n                  Settings\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem \r\n                onClick={logout} \r\n                className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                style={{ \r\n                  color: '#ff8583',\r\n                  backgroundColor: 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }}\r\n              >\r\n                <LogOut className=\"w-4 h-4\" />\r\n                Logout\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      )}\r\n    </aside>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAgBA,MAAM,UAAkC,CAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,oBAAoB,KAAK,EAAE;;IACjG,MAAM,EACJ,WAAW,EACX,cAAc,EACd,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,0IAAA,CAAA,iBAAc,AAAD;IACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAExD,QAAQ,GAAG,CAAC,iDAAiD;IAE7D,iDAAiD;IACjD,MAAM,gBAAgB,UAAU,MAAM,KAAK,SAAS,UAAU,SAAS,KAAK,CAAC,KAAK,GAAG,KAAK;IAC1F,QAAQ,GAAG,CAAC,2CAA2C;IAEvD,MAAM,eAAe,CAAC,IAAY;QAChC,QAAQ,GAAG,CAAC,0BAA0B,IAAI,sBAAsB;QAChE,YAAY;QACZ,eAAe;QACf,cAAc;IAChB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,uCAAuC,IAAI,kBAAkB;QACzE,IAAI,YAAY,IAAI,IAAI;YACtB,WAAW,IAAI,YAAY,IAAI;QACjC;QACA,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,cAAc;YAEd,iFAAiF;YACjF,IAAI,kBAAkB,QAAQ;gBAC5B,6EAA6E;gBAC7E,MAAM,iBAAiB,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAE9D,IAAI,eAAe,MAAM,GAAG,GAAG;oBAC7B,6DAA6D;oBAC7D,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC7C,OAAO;oBACL,qCAAqC;oBACrC;oBACA,iEAAiE;oBACjE,OAAO,IAAI,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,0DAA0D;QAC5D;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,UAAU,OAAO;QAChD,MAAM,YAAY,SAAS,CAAC,OAAO,KAAK,EAAE;QAC1C,MAAM,WAAW,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAE9C,IAAI,YAAY,GAAG;YACjB,OAAO;QACT,OAAO,IAAI,YAAY,IAAI;YACzB,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,KAAK,CAAC;QACxC,OAAO,IAAI,WAAW,GAAG;YACvB,OAAO,GAAG,KAAK,KAAK,CAAC,UAAU,KAAK,CAAC;QACvC,OAAO;YACL,OAAO,UAAU,kBAAkB;QACrC;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,oBAAoB,CAAC;QAC3B,eAAe;QACf,mBAAmB;IACrB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,wBAAwB,EAAE,cAAc,SAAS,YAAY,kFAAkF,CAAC;QAC5J,OAAO;YACL,iBAAiB;YACjB,aAAa;YACb,gBAAgB;QAClB;;0BAGA,6LAAC;gBAAI,WAAW,CAAC,8DAA8D,EAAE,cAAc,wBAAwB,oBAAoB;gBACzI,OAAO;oBAAE,iBAAiB;gBAAoB;0BAE9C,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,OAAO;wBACL,OAAO;wBACP,iBAAiB;oBACnB;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,yCAAyC;wBAC/F,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oBACpC;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,eAAe;wBACrE,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oBACpC;oBACA,SAAS;8BAET,cAAA,6LAAC;wBACC,WAAU;wBACV,SAAQ;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO;4BACL,WAAW,cAAc,eAAe;wBAC1C;kCAEA,cAAA,6LAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;;;;;YAKb,CAAC,6BACA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,oGAAoG,EAAE,aAAa,eAAe,KAAK,IAAI;oCACvJ,OAAO;wCACL,OAAO,aAAa,eAAe,gCAAgC;wCACnE,iBAAiB,aAAa,eAAe,4BAA4B;oCAC3E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,cAAc;4CAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,cAAc;4CAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,+NAAA,CAAA,kBAAe;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAKpF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,oGAAoG,EAAE,aAAa,aAAa,KAAK,IAAI;oCACrJ,OAAO;wCACL,OAAO,aAAa,aAAa,gCAAgC;wCACjE,iBAAiB,aAAa,aAAa,4BAA4B;oCACzE;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,YAAY;4CAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,YAAY;4CAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAK9E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,mGAAmG,CAAC;oCAChH,OAAO;wCACL,OAAO,aAAa,iBAAiB,gCAAgC;wCACrE,iBAAiB,aAAa,iBAAiB,4BAA4B;oCAC7E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,gBAAgB;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,gBAAgB;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAK7E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,mGAAmG,CAAC;oCAChH,OAAO;wCACL,OAAO,aAAa,kBAAkB,gCAAgC;wCACtE,iBAAiB,aAAa,kBAAkB,4BAA4B;oCAC9E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,iBAAiB;4CAChC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,iBAAiB;4CAChC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;;;;;;;kCAO5E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;4BACT,UAAU;4BACV,OAAO;gCACL,OAAO;gCACP,iBAAiB;4BACnB;4BACA,cAAc,CAAC;gCACb,IAAI,CAAC,mBAAmB;oCACtB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;4BACF;4BACA,cAAc,CAAC;gCACb,IAAI,CAAC,mBAAmB;oCACtB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;4BACF;;8CAEA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAU,OAAO;wCAAE,OAAO;oCAAsB;;;;;;gCAC/D,oBAAoB,gBAAgB;;;;;;;;;;;;kCAKzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;0CACD;;;;;;4BAIA,gCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAU,OAAO;wCAAE,OAAO;oCAA+B;8CAAG;;;;;;;;;;;4BAM9E,CAAC,kBAAkB,YAAY,GAAG,CAAC,CAAC;gCACnC,MAAM,WAAW,KAAK,EAAE,KAAK;gCAC7B,MAAM,aAAa,aAAa,KAAK,EAAE;gCACvC,qBACE,6LAAC;oCAEC,WAAW,CAAC,wGAAwG,EAAE,WAAW,KAAK,IAAI;oCAC1I,OAAO;wCACL,iBAAiB,WAAW,4BAA4B;oCAC1D;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;8CAE5C,2BACC,6LAAC;wCACC,UAAU,CAAA;4CAAO,EAAE,cAAc;4CAAI,mBAAmB,KAAK,EAAE;wCAAG;wCAClE,WAAU;;0DAEV,6LAAC;gDACC,SAAS;gDACT,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAA8B;gDAC9C,OAAO;gDACP,UAAU,CAAA,IAAK,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAW,CAAA;oDAAO,IAAI,EAAE,GAAG,KAAK,UAAU,YAAY;gDAAO;;;;;;0DAE/D,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;6DAKH;;0DACE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAA8B;8DAE7C,KAAK,KAAK;;;;;;;;;;;0DAIf,6LAAC;gDAAI,WAAU;gDAA8B,SAAS,CAAA,IAAK,EAAE,eAAe;0DAC1E,cAAA,6LAAC,+IAAA,CAAA,eAAY;;sEACX,6LAAC,+IAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,MAAK;gEACL,SAAQ;gEACR,WAAU;gEACV,OAAO;oEAAE,OAAO;gEAAsB;gEACtC,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEAC1C;gEACA,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEAC1C;gEACA,cAAW;0EAEX,cAAA,6LAAC,6NAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG5B,6LAAC,+IAAA,CAAA,sBAAmB;4DAClB,OAAM;4DACN,MAAK;4DACL,YAAY;4DACZ,WAAU;4DACV,OAAO;gEACL,iBAAiB;gEACjB,OAAO;4DACT;;8EAEA,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,aAAa,KAAK,EAAE,EAAE,KAAK,KAAK;oEAC/C,WAAU;oEACV,OAAO;wEACL,OAAO;wEACP,iBAAiB;oEACnB;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;8EACD;;;;;;8EAGD,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,aAAa,KAAK,EAAE;oEACnC,WAAU;oEACV,OAAO;wEACL,OAAO;wEACP,iBAAiB;oEACnB;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;8EACD;;;;;;;;;;;;;;;;;;;;;;;;;mCA7GN,KAAK,EAAE;;;;;4BAuHlB;4BAEC,CAAC,kBAAkB,YAAY,MAAM,KAAK,mBACzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uOAAA,CAAA,oBAAiB;wCAChB,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAA+B;;;;;;kDAEjD,6LAAC;wCAAE,WAAU;wCAAU,OAAO;4CAAE,OAAO;wCAA+B;kDAAG;;;;;;kDAGzE,6LAAC;wCAAE,WAAU;wCAAe,OAAO;4CAAE,OAAO;wCAA+B;kDAAG;;;;;;;;;;;;;;;;;;kCAQpF,6LAAC;wBAAI,WAAU;wBAA6B,OAAO;4BAAE,aAAa;wBAAwB;kCACxF,cAAA,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC;wCAAI,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAc;wCACxC,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACH,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAC,iBAAiB;gDAA0U;;;;;;0DAErW,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;oDAAqC,OAAO;wDAAE,OAAO;oDAA8B;8DAChG;;;;;;;;;;;;;;;;;;;;;;8CAKT,6LAAC,+IAAA,CAAA,sBAAmB;oCAClB,OAAM;oCACN,MAAK;oCACL,YAAY;oCACZ,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,OAAO;oCACT;;sDAEA,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;gDAC1B,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;;kEACJ,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIhC,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;gDAC3B,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;;kEACJ,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIpC,6LAAC,+IAAA,CAAA,mBAAgB;4CACf,SAAS;4CACT,WAAU;4CACV,OAAO;gDACL,OAAO;gDACP,iBAAiB;4CACnB;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;;8DAEA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUzC,6BACC,6LAAC;gBAAI,WAAU;gBAA4B,OAAO;oBAAE,aAAa;gBAAwB;0BACvF,cAAA,6LAAC,+IAAA,CAAA,eAAY;;sCACX,6LAAC,+IAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAC,iBAAiB;oCAA0U;;;;;;;;;;;;;;;;sCAIzW,6LAAC,+IAAA,CAAA,sBAAmB;4BAClB,OAAM;4BACN,MAAK;4BACL,YAAY;4BACZ,WAAU;4BACV,OAAO;gCACL,iBAAiB;gCACjB,OAAO;4BACT;;8CAEA,6LAAC,+IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;wCAC1B,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACJ,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIhC,6LAAC,+IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;wCAC3B,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACJ,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIpC,6LAAC,+IAAA,CAAA,mBAAgB;oCACf,SAAS;oCACT,WAAU;oCACV,OAAO;wCACL,OAAO;wCACP,iBAAiB;oCACnB;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;;sDAEA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;GA1kBM;;QAMA,0IAAA,CAAA,iBAAc;QACO,mIAAA,CAAA,UAAO;QACf,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;;;KATpB;uCA4kBS", "debugId": null}}, {"offset": {"line": 2419, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Layout.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { ReactNode, useState, useRef, useCallback } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useChatHistory } from \"@/providers/ChatHistoryContext\";\r\nimport { PageHeaderProvider } from '@/providers/PageHeaderContext';\r\nimport Header from './Header';\r\nimport Sidebar from './Sidebar';\r\n\r\ninterface LayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\r\n  const { setActiveChat } = useChatHistory();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);\r\n  const [isCreatingNewChat, setIsCreatingNewChat] = useState<boolean>(false);\r\n  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  const handleNewChat = useCallback(async () => {\r\n    // Prevent multiple simultaneous new chat creations\r\n    if (isCreatingNewChat) {\r\n      console.log('New chat creation already in progress, ignoring click');\r\n      return;\r\n    }\r\n\r\n    // Clear any existing debounce timeout\r\n    if (debounceTimeoutRef.current) {\r\n      clearTimeout(debounceTimeoutRef.current);\r\n    }\r\n\r\n    // Set debounce timeout to prevent rapid clicking\r\n    debounceTimeoutRef.current = setTimeout(async () => {\r\n      setIsCreatingNewChat(true);\r\n      \r\n      try {\r\n        // Clear the active chat to show the welcome message\r\n        await setActiveChat(null);\r\n        \r\n        // Navigate to the base chat route without a specific chat ID\r\n        router.push('/chat');\r\n      } catch (error) {\r\n        console.error('Error creating new chat:', error);\r\n      } finally {\r\n        setIsCreatingNewChat(false);\r\n      }\r\n    }, 300); // 300ms debounce to prevent rapid clicking\r\n  }, [isCreatingNewChat, setActiveChat, router]);\r\n\r\n  const handleToggleCollapse = (collapsed: boolean) => {\r\n    setSidebarCollapsed(collapsed);\r\n  };\r\n\r\n  // Cleanup timeout on unmount\r\n  React.useEffect(() => {\r\n    return () => {\r\n      if (debounceTimeoutRef.current) {\r\n        clearTimeout(debounceTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <PageHeaderProvider>\r\n      <div className=\"flex h-screen bg-sidebar-bg\" style={{ fontFamily: 'var(--font-inter), var(--font-noto-sans), sans-serif' }}>\r\n      <Sidebar \r\n        onNewChat={handleNewChat} \r\n        onToggleCollapse={handleToggleCollapse}\r\n        isCreatingNewChat={isCreatingNewChat}\r\n      />\r\n      <main className=\"flex flex-col flex-1 min-w-0 bg-sidebar-bg overflow-y-auto\">\r\n        <Header />\r\n        <div className=\"flex-1\">\r\n          {children}\r\n        </div>\r\n      </main>\r\n    </div>\r\n    </PageHeaderProvider>\r\n  );\r\n};\r\n\r\nexport default Layout;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAYA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEzD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YAChC,mDAAmD;YACnD,IAAI,mBAAmB;gBACrB,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,sCAAsC;YACtC,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;YAEA,iDAAiD;YACjD,mBAAmB,OAAO,GAAG;qDAAW;oBACtC,qBAAqB;oBAErB,IAAI;wBACF,oDAAoD;wBACpD,MAAM,cAAc;wBAEpB,6DAA6D;wBAC7D,OAAO,IAAI,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C,SAAU;wBACR,qBAAqB;oBACvB;gBACF;oDAAG,MAAM,2CAA2C;QACtD;4CAAG;QAAC;QAAmB;QAAe;KAAO;IAE7C,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,6BAA6B;IAC7B,6JAAA,CAAA,UAAK,CAAC,SAAS;4BAAC;YACd;oCAAO;oBACL,IAAI,mBAAmB,OAAO,EAAE;wBAC9B,aAAa,mBAAmB,OAAO;oBACzC;gBACF;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC,yIAAA,CAAA,qBAAkB;kBACjB,cAAA,6LAAC;YAAI,WAAU;YAA8B,OAAO;gBAAE,YAAY;YAAuD;;8BACzH,6LAAC,0IAAA,CAAA,UAAO;oBACN,WAAW;oBACX,kBAAkB;oBAClB,mBAAmB;;;;;;8BAErB,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,yIAAA,CAAA,UAAM;;;;;sCACP,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMX;GApEM;;QACsB,0IAAA,CAAA,iBAAc;QACzB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHxB;uCAsES", "debugId": null}}, {"offset": {"line": 2564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/hooks/usePageTitle.ts"], "sourcesContent": ["import { useEffect, useMemo } from 'react';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\n\r\ninterface UsePageTitleOptions {\r\n  title: string;\r\n  subtitle?: string;\r\n  icon?: React.ComponentType<{ className?: string }>;\r\n}\r\n\r\n/**\r\n * Custom hook for setting page-specific header information\r\n * \r\n * Industry standard approach for managing page titles and metadata.\r\n * Automatically handles cleanup when component unmounts.\r\n * \r\n * @param options - Page header configuration\r\n */\r\nexport const usePageTitle = (options: UsePageTitleOptions) => {\r\n  const { setPageHeader, resetPageHeader } = usePageHeader();\r\n\r\n  useEffect(() => {\r\n    // Set the page header when component mounts or options change\r\n    setPageHeader(options);\r\n\r\n    // Cleanup: reset to default when component unmounts\r\n    return () => {\r\n      resetPageHeader();\r\n    };\r\n  }, [\r\n    options.title, \r\n    options.subtitle, \r\n    options.icon, \r\n    setPageHeader, \r\n    resetPageHeader\r\n  ]);\r\n};\r\n\r\n/**\r\n * Simplified hook for just setting a page title\r\n * \r\n * @param title - Page title\r\n * @param icon - Optional icon component\r\n */\r\nexport const useSimplePageTitle = (title: string, icon?: React.ComponentType<{ className?: string }>) => {\r\n  usePageTitle({ title, icon });\r\n}; "], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAgBO,MAAM,eAAe,CAAC;;IAC3B,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,8DAA8D;YAC9D,cAAc;YAEd,oDAAoD;YACpD;0CAAO;oBACL;gBACF;;QACF;iCAAG;QACD,QAAQ,KAAK;QACb,QAAQ,QAAQ;QAChB,QAAQ,IAAI;QACZ;QACA;KACD;AACH;GAlBa;;QACgC,yIAAA,CAAA,gBAAa;;;AAyBnD,MAAM,qBAAqB,CAAC,OAAe;;IAChD,aAAa;QAAE;QAAO;IAAK;AAC7B;IAFa;;QACX", "debugId": null}}, {"offset": {"line": 2621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils/index\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold text-white\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6 text-white\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAAA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 2737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gbACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2805, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 2857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/datasources/ConnectDataSourceModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { DatabaseConnectionRequestParams } from '@/providers/ApiContext';\r\nimport { AlertTriangle, Loader2, X } from 'lucide-react';\r\n\r\n// Types\r\ninterface DataSourceField {\r\n  label: string;\r\n  name: keyof DatabaseConnectionRequestParams;\r\n  type: 'text' | 'number' | 'password' | 'checkbox';\r\n  required?: boolean;\r\n  placeholder?: string;\r\n}\r\n\r\ninterface DataSourceType {\r\n  id: string;\r\n  name: string;\r\n  connectionFields: DataSourceField[];\r\n}\r\n\r\ninterface ConnectDataSourceModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  dataSourceType: DataSourceType | null;\r\n  onConnect: (params: DatabaseConnectionRequestParams) => Promise<boolean>;\r\n  apiError?: string | null; \r\n}\r\n\r\n// Field components optimized for performance\r\nconst CheckboxField = React.memo(({ \r\n  field, \r\n  value, \r\n  onChange, \r\n  disabled \r\n}: { \r\n  field: DataSourceField; \r\n  value: boolean; \r\n  onChange: (name: string, value: string | number | boolean) => void; \r\n  disabled: boolean \r\n}) => {\r\n  return (\r\n    <div className=\"flex items-center space-x-3 py-2\">\r\n      <Checkbox\r\n        id={field.name as string}\r\n        name={field.name as string}\r\n        checked={value}\r\n        onCheckedChange={(checked) => {\r\n          onChange(field.name as string, checked === true);\r\n        }}\r\n        disabled={disabled}\r\n        className=\"border-sidebar-border data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600\"\r\n      />\r\n      <label\r\n        htmlFor={field.name as string}\r\n        className=\"text-sm cursor-pointer\"\r\n        style={{ color: 'var(--sidebar-text-primary)' }}\r\n      >\r\n        {field.label}\r\n      </label>\r\n    </div>\r\n  );\r\n});\r\n\r\nCheckboxField.displayName = 'CheckboxField';\r\n\r\nconst InputFieldBasic = React.memo(({ \r\n  field, \r\n  value, \r\n  onChange, \r\n  disabled \r\n}: { \r\n  field: DataSourceField; \r\n  value: string | number; \r\n  onChange: (name: string, value: string | number | boolean) => void; \r\n  disabled: boolean \r\n}) => {\r\n  return (\r\n    <Input\r\n      id={field.name as string}\r\n      name={field.name as string}\r\n      type={field.type}\r\n      placeholder={field.placeholder}\r\n      value={String(value || '')}\r\n      onChange={(e) => onChange(field.name as string, e.target.value)}\r\n      disabled={disabled}\r\n      className=\"border-sidebar-border transition-all duration-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 h-9 text-sm\"\r\n      style={{\r\n        backgroundColor: 'rgba(255, 255, 255, 0.05)',\r\n        color: 'var(--sidebar-text-primary)',\r\n      }}\r\n    />\r\n  );\r\n});\r\n\r\nInputFieldBasic.displayName = 'InputFieldBasic';\r\n\r\nconst ConnectDataSourceModal = React.memo(function ConnectDataSourceModal({\r\n  isOpen,\r\n  onClose,\r\n  dataSourceType,\r\n  onConnect,\r\n  apiError: parentApiError\r\n}: ConnectDataSourceModalProps) {\r\n  // Modal state\r\n  const [formData, setFormData] = useState<Partial<DatabaseConnectionRequestParams>>({});\r\n  const [isConnecting, setIsConnecting] = useState(false);\r\n  const [validationError, setValidationError] = useState<string | null>(null);\r\n  const [currentApiError, setCurrentApiError] = useState<string | null>(null);\r\n\r\n  // Handle field changes without re-rendering the entire form\r\n  const handleFieldChange = useCallback((name: string, value: string | number | boolean) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n    \r\n    // Clear errors when user types\r\n    setValidationError(null);\r\n    setCurrentApiError(null);\r\n  }, []);\r\n\r\n  // Initialize form data when modal opens or dataSourceType changes\r\n  useEffect(() => {\r\n    if (dataSourceType && isOpen) {\r\n      const initialData: Partial<DatabaseConnectionRequestParams> = {};\r\n      dataSourceType.connectionFields.forEach(field => {\r\n        if (field.type === 'checkbox') {\r\n          if (field.name === 'ssl_enabled') {\r\n            (initialData as Record<string, boolean | string | number | undefined>)[field.name] = false;\r\n          }\r\n        } else {\r\n          initialData[field.name] = undefined;\r\n        }\r\n      });\r\n      setFormData(initialData);\r\n      setValidationError(null);\r\n      setCurrentApiError(null);\r\n    }\r\n  }, [dataSourceType?.id, dataSourceType, isOpen]);\r\n\r\n  // Update API error separately\r\n  useEffect(() => {\r\n    setCurrentApiError(parentApiError || null);\r\n  }, [parentApiError]);\r\n\r\n  // Handle form submission\r\n  const handleSubmit = useCallback(async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!dataSourceType) return;\r\n\r\n    // Validation\r\n    const missingFields: string[] = [];\r\n    for (const field of dataSourceType.connectionFields) {\r\n      if (field.required && (formData[field.name] === undefined || formData[field.name] === '')) {\r\n        missingFields.push(field.label);\r\n      }\r\n    }\r\n    \r\n    if (missingFields.length > 0) {\r\n      if (missingFields.length === 1) {\r\n        setValidationError(`Please fill in the ${missingFields[0]} field.`);\r\n      } else {\r\n        setValidationError(`Please fill in the following required fields: ${missingFields.join(', ')}.`);\r\n      }\r\n        return;\r\n    }\r\n\r\n    setValidationError(null);\r\n    setIsConnecting(true);\r\n    setCurrentApiError(null);\r\n\r\n    // Prepare submission data - clean up undefined values\r\n    const paramsToSubmit: Record<string, string | number | boolean> = {};\r\n\r\n    dataSourceType.connectionFields.forEach(field => {\r\n      const value = formData[field.name];\r\n      if (value !== undefined && value !== '') {\r\n        if (field.name === 'port') {\r\n          paramsToSubmit.port = Number(value);\r\n        } else if (field.name === 'ssl_enabled') {\r\n          paramsToSubmit.ssl_enabled = Boolean(value);\r\n        } else {\r\n          paramsToSubmit[field.name] = value;\r\n        }\r\n      }\r\n    });\r\n    \r\n    // Ensure required fields are set\r\n    paramsToSubmit.name = formData.name || dataSourceType.name;\r\n    paramsToSubmit.type = dataSourceType.id.toUpperCase(); // Ensure uppercase for backend\r\n    \r\n    // Set default values for optional fields if not provided\r\n    if (dataSourceType.id === 'postgresql' || dataSourceType.id === 'mysql') {\r\n      if (!paramsToSubmit.ssl_enabled) {\r\n        paramsToSubmit.ssl_enabled = false;\r\n      }\r\n    }\r\n\r\n    console.log('ConnectDataSourceModal: Submitting params:', paramsToSubmit);\r\n    \r\n    try {\r\n      const success = await onConnect(paramsToSubmit as unknown as DatabaseConnectionRequestParams);\r\n      if (success) {\r\n        onClose();\r\n      }\r\n    } catch (error) {\r\n      console.error('Connection error:', error);\r\n    } finally {\r\n      setIsConnecting(false);\r\n    }\r\n  }, [dataSourceType, formData, onConnect, onClose]);\r\n\r\n  // Prevent scrolling of body when modal is open\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = '';\r\n    }\r\n    return () => {\r\n      document.body.style.overflow = '';\r\n    };\r\n  }, [isOpen]);\r\n\r\n  // Close on escape key\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (e.key === 'Escape' && isOpen && !isConnecting) {\r\n        onClose();\r\n      }\r\n    };\r\n    \r\n    window.addEventListener('keydown', handleEscape);\r\n    return () => window.removeEventListener('keydown', handleEscape);\r\n  }, [isOpen, onClose, isConnecting]);\r\n\r\n  if (!isOpen) return null;\r\n  \r\n  // Modern modal design inspired by Linear/Stripe/Vercel\r\n  return (\r\n    <div \r\n      className=\"fixed inset-0 z-50 flex items-center justify-center p-6\"\r\n      style={{ backgroundColor: 'transparent' }}\r\n      onClick={() => !isConnecting && onClose()}\r\n    >\r\n      <div \r\n        className=\"w-full max-w-md flex flex-col max-h-[85vh] overflow-hidden transform-gpu border border-sidebar-border rounded-2xl shadow-2xl\"\r\n        style={{\r\n          backgroundColor: '#323232',\r\n          willChange: 'transform'\r\n        }}\r\n        onClick={e => e.stopPropagation()}\r\n      >\r\n        {/* Close Button */}\r\n        <div className=\"absolute top-4 right-4 z-10\">\r\n          <Button \r\n            type=\"button\" \r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={() => !isConnecting && onClose()}\r\n            disabled={isConnecting}\r\n            className=\"w-8 h-8 rounded-lg border-0 transition-all duration-200\"\r\n            style={{\r\n              color: 'var(--sidebar-text-secondary)',\r\n              backgroundColor: 'transparent'\r\n            }}\r\n            onMouseEnter={(e) => {\r\n              if (!isConnecting) {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                e.currentTarget.style.setProperty('color', 'var(--sidebar-text-primary)', 'important');\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (!isConnecting) {\r\n                e.currentTarget.style.backgroundColor = 'transparent';\r\n                e.currentTarget.style.setProperty('color', 'var(--sidebar-text-secondary)', 'important');\r\n              }\r\n            }}\r\n          >\r\n            <X className=\"w-4 h-4\" />\r\n          </Button>\r\n        </div>\r\n        \r\n        <form onSubmit={handleSubmit} className=\"flex flex-col flex-grow overflow-hidden\">\r\n          {/* Content */}\r\n          <div \r\n            className=\"flex-grow p-6 space-y-4 overflow-y-auto\" \r\n            style={{ \r\n              overscrollBehavior: 'contain',\r\n              WebkitOverflowScrolling: 'touch' \r\n            }}\r\n          >\r\n            {dataSourceType?.connectionFields.map((field) => {\r\n              // Intelligent field grouping and layout\r\n              const isShortField = ['port'].includes(field.name as string);\r\n              \r\n              return (\r\n                <div key={field.name} className=\"space-y-2\">\r\n                <Label \r\n                  htmlFor={field.name as string}\r\n                    className=\"text-sm font-medium\"\r\n                    style={{ color: 'var(--sidebar-text-primary)' }}\r\n                >\r\n                    {field.label}\r\n                </Label>\r\n                \r\n                {field.type === 'checkbox' ? (\r\n                    <div className=\"pt-1\">\r\n                  <CheckboxField\r\n                    field={field}\r\n                    value={Boolean(formData[field.name])}\r\n                    onChange={handleFieldChange}\r\n                    disabled={isConnecting}\r\n                  />\r\n                    </div>\r\n                ) : (\r\n                    <div className={`${isShortField ? 'max-w-[140px]' : ''}`}>\r\n                  <InputFieldBasic\r\n                    field={field}\r\n                    value={typeof formData[field.name] === 'boolean' ? '' : String(formData[field.name] || '')}\r\n                    onChange={handleFieldChange}\r\n                    disabled={isConnecting}\r\n                  />\r\n                    </div>\r\n                )}\r\n              </div>\r\n              );\r\n            })}\r\n            \r\n            {/* Error messages */}\r\n            {(validationError || currentApiError) && (\r\n              <div className=\"p-3 border border-red-200 rounded-lg mt-4\" style={{ backgroundColor: 'rgba(239, 68, 68, 0.05)' }}>\r\n                <div className=\"flex items-start gap-2\">\r\n                  <AlertTriangle className=\"h-4 w-4 text-red-500 flex-shrink-0 mt-0.5\" />\r\n                  <div className=\"text-red-600 text-sm\">\r\n                    {validationError && (\r\n                        <div>{validationError}</div>\r\n                    )}\r\n                    {currentApiError && (\r\n                        <div>{currentApiError}</div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          {/* Footer - Optimized for user flow */}\r\n          <div className=\"p-6 pt-4\">\r\n            <div className=\"flex gap-3\">\r\n            <Button \r\n              type=\"button\" \r\n              variant=\"outline\" \r\n                onClick={() => !isConnecting && onClose()}\r\n              disabled={isConnecting}\r\n                className=\"flex-1 border border-sidebar-border rounded-lg transition-all duration-200 h-9\"\r\n                style={{\r\n                  color: 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (!isConnecting) {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                    e.currentTarget.style.setProperty('color', 'var(--sidebar-text-primary)', 'important');\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (!isConnecting) {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                    e.currentTarget.style.setProperty('color', 'var(--sidebar-text-secondary)', 'important');\r\n                  }\r\n                }}\r\n              >\r\n              Cancel\r\n            </Button>\r\n            <Button \r\n              type=\"submit\" \r\n              disabled={isConnecting}\r\n                variant=\"outline\"\r\n                className=\"flex-1 border border-sidebar-border rounded-lg transition-all duration-200 h-9 font-medium\"\r\n                style={{\r\n                  color: 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (!isConnecting) {\r\n                    e.currentTarget.style.backgroundColor = 'var(--sidebar-text-primary)';\r\n                    e.currentTarget.style.setProperty('color', 'var(--sidebar-bg)', 'important');\r\n                    e.currentTarget.style.transform = 'scale(1.02)';\r\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (!isConnecting) {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                    e.currentTarget.style.setProperty('color', 'var(--sidebar-text-secondary)', 'important');\r\n                    e.currentTarget.style.transform = 'scale(1)';\r\n                    e.currentTarget.style.boxShadow = 'none';\r\n                  }\r\n                }}\r\n            >\r\n              {isConnecting ? (\r\n                <>\r\n                  <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\r\n                  Connecting...\r\n                </>\r\n              ) : (\r\n                'Connect'\r\n              )}\r\n            </Button>\r\n            </div>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n\r\nConnectDataSourceModal.displayName = 'ConnectDataSourceModal';\r\n\r\nexport default ConnectDataSourceModal;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;;;AARA;;;;;;;AAiCA,6CAA6C;AAC7C,MAAM,8BAAgB,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAChC,KAAK,EACL,KAAK,EACL,QAAQ,EACR,QAAQ,EAMT;IACC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,uIAAA,CAAA,WAAQ;gBACP,IAAI,MAAM,IAAI;gBACd,MAAM,MAAM,IAAI;gBAChB,SAAS;gBACT,iBAAiB,CAAC;oBAChB,SAAS,MAAM,IAAI,EAAY,YAAY;gBAC7C;gBACA,UAAU;gBACV,WAAU;;;;;;0BAEZ,6LAAC;gBACC,SAAS,MAAM,IAAI;gBACnB,WAAU;gBACV,OAAO;oBAAE,OAAO;gBAA8B;0BAE7C,MAAM,KAAK;;;;;;;;;;;;AAIpB;KAhCM;AAkCN,cAAc,WAAW,GAAG;AAE5B,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAClC,KAAK,EACL,KAAK,EACL,QAAQ,EACR,QAAQ,EAMT;IACC,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,IAAI,MAAM,IAAI;QACd,MAAM,MAAM,IAAI;QAChB,MAAM,MAAM,IAAI;QAChB,aAAa,MAAM,WAAW;QAC9B,OAAO,OAAO,SAAS;QACvB,UAAU,CAAC,IAAM,SAAS,MAAM,IAAI,EAAY,EAAE,MAAM,CAAC,KAAK;QAC9D,UAAU;QACV,WAAU;QACV,OAAO;YACL,iBAAiB;YACjB,OAAO;QACT;;;;;;AAGN;MA3BM;AA6BN,gBAAgB,WAAW,GAAG;AAE9B,MAAM,uCAAyB,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,UAAC,SAAS,uBAAuB,EACxE,MAAM,EACN,OAAO,EACP,cAAc,EACd,SAAS,EACT,UAAU,cAAc,EACI;;IAC5B,cAAc;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4C,CAAC;IACpF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,4DAA4D;IAC5D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wFAAE,CAAC,MAAc;YACnD;gGAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,CAAC,KAAK,EAAE;oBACV,CAAC;;YAED,+BAA+B;YAC/B,mBAAmB;YACnB,mBAAmB;QACrB;uFAAG,EAAE;IAEL,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mEAAE;YACR,IAAI,kBAAkB,QAAQ;gBAC5B,MAAM,cAAwD,CAAC;gBAC/D,eAAe,gBAAgB,CAAC,OAAO;+EAAC,CAAA;wBACtC,IAAI,MAAM,IAAI,KAAK,YAAY;4BAC7B,IAAI,MAAM,IAAI,KAAK,eAAe;gCAC/B,WAAqE,CAAC,MAAM,IAAI,CAAC,GAAG;4BACvF;wBACF,OAAO;4BACL,WAAW,CAAC,MAAM,IAAI,CAAC,GAAG;wBAC5B;oBACF;;gBACA,YAAY;gBACZ,mBAAmB;gBACnB,mBAAmB;YACrB;QACF;kEAAG;QAAC,gBAAgB;QAAI;QAAgB;KAAO;IAE/C,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mEAAE;YACR,mBAAmB,kBAAkB;QACvC;kEAAG;QAAC;KAAe;IAEnB,yBAAyB;IACzB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mFAAE,OAAO;YACtC,EAAE,cAAc;YAChB,IAAI,CAAC,gBAAgB;YAErB,aAAa;YACb,MAAM,gBAA0B,EAAE;YAClC,KAAK,MAAM,SAAS,eAAe,gBAAgB,CAAE;gBACnD,IAAI,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,aAAa,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,GAAG;oBACzF,cAAc,IAAI,CAAC,MAAM,KAAK;gBAChC;YACF;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,IAAI,cAAc,MAAM,KAAK,GAAG;oBAC9B,mBAAmB,CAAC,mBAAmB,EAAE,aAAa,CAAC,EAAE,CAAC,OAAO,CAAC;gBACpE,OAAO;oBACL,mBAAmB,CAAC,8CAA8C,EAAE,cAAc,IAAI,CAAC,MAAM,CAAC,CAAC;gBACjG;gBACE;YACJ;YAEA,mBAAmB;YACnB,gBAAgB;YAChB,mBAAmB;YAEnB,sDAAsD;YACtD,MAAM,iBAA4D,CAAC;YAEnE,eAAe,gBAAgB,CAAC,OAAO;2FAAC,CAAA;oBACtC,MAAM,QAAQ,QAAQ,CAAC,MAAM,IAAI,CAAC;oBAClC,IAAI,UAAU,aAAa,UAAU,IAAI;wBACvC,IAAI,MAAM,IAAI,KAAK,QAAQ;4BACzB,eAAe,IAAI,GAAG,OAAO;wBAC/B,OAAO,IAAI,MAAM,IAAI,KAAK,eAAe;4BACvC,eAAe,WAAW,GAAG,QAAQ;wBACvC,OAAO;4BACL,cAAc,CAAC,MAAM,IAAI,CAAC,GAAG;wBAC/B;oBACF;gBACF;;YAEA,iCAAiC;YACjC,eAAe,IAAI,GAAG,SAAS,IAAI,IAAI,eAAe,IAAI;YAC1D,eAAe,IAAI,GAAG,eAAe,EAAE,CAAC,WAAW,IAAI,+BAA+B;YAEtF,yDAAyD;YACzD,IAAI,eAAe,EAAE,KAAK,gBAAgB,eAAe,EAAE,KAAK,SAAS;gBACvE,IAAI,CAAC,eAAe,WAAW,EAAE;oBAC/B,eAAe,WAAW,GAAG;gBAC/B;YACF;YAEA,QAAQ,GAAG,CAAC,8CAA8C;YAE1D,IAAI;gBACF,MAAM,UAAU,MAAM,UAAU;gBAChC,IAAI,SAAS;oBACX;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;YACrC,SAAU;gBACR,gBAAgB;YAClB;QACF;kFAAG;QAAC;QAAgB;QAAU;QAAW;KAAQ;IAEjD,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mEAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YACA;2EAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;kEAAG;QAAC;KAAO;IAEX,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mEAAE;YACR,MAAM;wFAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,YAAY,UAAU,CAAC,cAAc;wBACjD;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;2EAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;kEAAG;QAAC;QAAQ;QAAS;KAAa;IAElC,IAAI,CAAC,QAAQ,OAAO;IAEpB,uDAAuD;IACvD,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YAAE,iBAAiB;QAAc;QACxC,SAAS,IAAM,CAAC,gBAAgB;kBAEhC,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBACL,iBAAiB;gBACjB,YAAY;YACd;YACA,SAAS,CAAA,IAAK,EAAE,eAAe;;8BAG/B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,CAAC,gBAAgB;wBAChC,UAAU;wBACV,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,iBAAiB;wBACnB;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,cAAc;gCACjB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,+BAA+B;4BAC5E;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,cAAc;gCACjB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,iCAAiC;4BAC9E;wBACF;kCAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,oBAAoB;gCACpB,yBAAyB;4BAC3B;;gCAEC,gBAAgB,iBAAiB,IAAI,CAAC;oCACrC,wCAAwC;oCACxC,MAAM,eAAe;wCAAC;qCAAO,CAAC,QAAQ,CAAC,MAAM,IAAI;oCAEjD,qBACE,6LAAC;wCAAqB,WAAU;;0DAChC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAS,MAAM,IAAI;gDACjB,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAA8B;0DAE7C,MAAM,KAAK;;;;;;4CAGf,MAAM,IAAI,KAAK,2BACZ,6LAAC;gDAAI,WAAU;0DACjB,cAAA,6LAAC;oDACC,OAAO;oDACP,OAAO,QAAQ,QAAQ,CAAC,MAAM,IAAI,CAAC;oDACnC,UAAU;oDACV,UAAU;;;;;;;;;;qEAIV,6LAAC;gDAAI,WAAW,GAAG,eAAe,kBAAkB,IAAI;0DAC1D,cAAA,6LAAC;oDACC,OAAO;oDACP,OAAO,OAAO,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,YAAY,KAAK,OAAO,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI;oDACvF,UAAU;oDACV,UAAU;;;;;;;;;;;;uCAxBJ,MAAM,IAAI;;;;;gCA8BxB;gCAGC,CAAC,mBAAmB,eAAe,mBAClC,6LAAC;oCAAI,WAAU;oCAA4C,OAAO;wCAAE,iBAAiB;oCAA0B;8CAC7G,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAI,WAAU;;oDACZ,iCACG,6LAAC;kEAAK;;;;;;oDAET,iCACG,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACf,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACN,SAAS,IAAM,CAAC,gBAAgB;wCAClC,UAAU;wCACR,WAAU;wCACV,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,IAAI,CAAC,cAAc;gDACjB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,+BAA+B;4CAC5E;wCACF;wCACA,cAAc,CAAC;4CACb,IAAI,CAAC,cAAc;gDACjB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,iCAAiC;4CAC9E;wCACF;kDACD;;;;;;kDAGH,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU;wCACR,SAAQ;wCACR,WAAU;wCACV,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,IAAI,CAAC,cAAc;gDACjB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,qBAAqB;gDAChE,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CACpC;wCACF;wCACA,cAAc,CAAC;4CACb,IAAI,CAAC,cAAc;gDACjB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,iCAAiC;gDAC5E,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CACpC;wCACF;kDAED,6BACC;;8DACE,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;2DAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShB;;AAEA,uBAAuB,WAAW,GAAG;uCAEtB", "debugId": null}}, {"offset": {"line": 3403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className,\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3447, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/datasources/DataSourcesPageContent.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport { useApi, ConnectedDatabase, DatabaseConnectionRequestParams } from '@/providers/ApiContext';\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { usePageTitle } from '@/hooks/usePageTitle';\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardFooter, CardHeader } from \"@/components/ui/card\";\r\nimport {\r\n  Database,\r\n  RefreshCw,\r\n  AlertTriangle,\r\n  Search,\r\n  Filter,\r\n  MoreVertical\r\n} from 'lucide-react';\r\nimport ConnectDataSourceModal from './ConnectDataSourceModal';\r\n// import { Badge } from \"@/components/ui/badge\";\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from \"@/components/ui/tooltip\";\r\nimport { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from \"@/components/ui/dropdown-menu\";\r\n\r\n// Custom database icon components using the provided SVGs\r\nconst SupabaseIcon = ({ className }: { className?: string }) => (\r\n  <img \r\n    src=\"/icons/supabase-logo-icon.svg\" \r\n    alt=\"Supabase\" \r\n    className={className}\r\n  />\r\n);\r\n\r\nconst MySQLIcon = ({ className }: { className?: string }) => (\r\n  <img \r\n    src=\"/icons/mysql.svg\" \r\n    alt=\"MySQL\" \r\n    className={className}\r\n  />\r\n);\r\n\r\nconst MongoDBIcon = ({ className }: { className?: string }) => (\r\n  <img \r\n    src=\"/icons/mongoDB.svg\" \r\n    alt=\"MongoDB\" \r\n    className={className}\r\n  />\r\n);\r\n\r\nconst PostgreSQLIcon = ({ className }: { className?: string }) => (\r\n  <img \r\n    src=\"/icons/postgres.svg\" \r\n    alt=\"PostgreSQL\" \r\n    className={className}\r\n  />\r\n);\r\n\r\n// Interface for the static list of available data source types\r\ninterface DataSourceField {\r\n  label: string;\r\n  name: keyof DatabaseConnectionRequestParams;\r\n  type: 'text' | 'number' | 'password' | 'checkbox';\r\n  required?: boolean;\r\n  placeholder?: string;\r\n}\r\n\r\ninterface DataSourceType {\r\n  id: string;\r\n  name: string;\r\n  typeCategory: 'database' | 'file' | 'cloud' | 'other';\r\n  description: string;\r\n  icon: React.ElementType;\r\n  connectionFields: DataSourceField[];\r\n}\r\n\r\n// Static list of available data source types\r\nconst AVAILABLE_DATA_SOURCE_TYPES: DataSourceType[] = [\r\n  {\r\n    id: 'postgresql',\r\n    name: 'PostgreSQL',\r\n    typeCategory: 'database',\r\n    description: 'Connect to your PostgreSQL relational database for powerful queries and analytics.',\r\n    icon: PostgreSQLIcon,\r\n    connectionFields: [\r\n      { label: 'Connection Name', name: 'name', type: 'text', required: true, placeholder: 'My PostgreSQL DB' },\r\n      { label: 'Host', name: 'host', type: 'text', required: true, placeholder: 'localhost' },\r\n      { label: 'Port', name: 'port', type: 'number', required: true, placeholder: '5432' },\r\n      { label: 'Username', name: 'username', type: 'text', required: true, placeholder: 'postgres' },\r\n      { label: 'Password', name: 'password', type: 'password', required: true },\r\n      { label: 'Database Name', name: 'database', type: 'text', required: true, placeholder: 'mydatabase' },\r\n      { label: 'Schema (optional)', name: 'db_schema', type: 'text', placeholder: 'public' },\r\n      { label: 'Enable SSL', name: 'ssl_enabled', type: 'checkbox' },\r\n    ],\r\n  },\r\n  {\r\n    id: 'supabase',\r\n    name: 'Supabase',\r\n    typeCategory: 'database',\r\n    description: 'Connect to your Supabase database for real-time data access.',\r\n    icon: SupabaseIcon,\r\n    connectionFields: [\r\n      { label: 'Connection Name', name: 'name', type: 'text', required: true, placeholder: 'My Supabase DB' },\r\n      { label: 'Host', name: 'host', type: 'text', required: true, placeholder: 'aws-0-us-east-2.pooler.supabase.com' },\r\n      { label: 'Port', name: 'port', type: 'number', required: true, placeholder: '6543' },\r\n      { label: 'Username', name: 'username', type: 'text', required: true, placeholder: 'postgres.fnwrnsojpsmleatmvmjw' },\r\n      { label: 'Password', name: 'password', type: 'password', required: true },\r\n      { label: 'Database Name', name: 'database', type: 'text', required: true, placeholder: 'postgres' },\r\n      { label: 'Enable SSL', name: 'ssl_enabled', type: 'checkbox', required: true },\r\n    ],\r\n  },\r\n  {\r\n    id: 'mongodb',\r\n    name: 'MongoDB',\r\n    typeCategory: 'database',\r\n    description: 'Connect to your MongoDB NoSQL document database for flexible data operations.',\r\n    icon: MongoDBIcon,\r\n    connectionFields: [\r\n      { label: 'Connection Name', name: 'name', type: 'text', required: true, placeholder: 'My MongoDB Atlas' },\r\n      { label: 'Connection String', name: 'connection_string', type: 'text', required: true, placeholder: 'mongodb+srv://user:<EMAIL>/dbname' },\r\n    ],\r\n  },\r\n  {\r\n    id: 'mysql',\r\n    name: 'MySQL',\r\n    typeCategory: 'database',\r\n    description: 'Connect to your MySQL open-source relational database for reliable data access.',\r\n    icon: MySQLIcon,\r\n    connectionFields: [\r\n      { label: 'Connection Name', name: 'name', type: 'text', required: true, placeholder: 'My MySQL DB' },\r\n      { label: 'Host', name: 'host', type: 'text', required: true, placeholder: 'localhost' },\r\n      { label: 'Port', name: 'port', type: 'number', required: true, placeholder: '3306' },\r\n      { label: 'Username', name: 'username', type: 'text', required: true, placeholder: 'root' },\r\n      { label: 'Password', name: 'password', type: 'password', required: true },\r\n      { label: 'Database Name', name: 'database', type: 'text', required: true, placeholder: 'mydatabase' },\r\n    ],\r\n      },\r\n  ];\r\n\r\n\r\n\r\n// Helper component for connection status badge (unused)\r\n// const ConnectionStatusBadge = ({ isConnected }: { isConnected: boolean }) => {\r\n//   if (isConnected) {\r\n//     return (\r\n//       <Badge variant=\"secondary\" className=\"bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800\">\r\n//         <Wifi className=\"w-3 h-3 mr-1\" />\r\n//         Connected\r\n//       </Badge>\r\n//     );\r\n//   }\r\n\r\n//   return (\r\n//     <Badge variant=\"outline\" className=\"text-slate-500 dark:text-slate-400\">\r\n//       <WifiOff className=\"w-3 h-3 mr-1\" />\r\n//       Disconnected\r\n//     </Badge>\r\n//   );\r\n// };\r\n\r\nconst DataSourcesPageContent = () => {\r\n  const { listDatabases, connectNewDatabase, disconnectExistingDatabase } = useApi();\r\n  const { isAuthenticated } = useAuth();\r\n\r\n  // Set page title - memoized to prevent re-renders\r\n  const pageConfig = useMemo(() => ({\r\n    title: 'Data Sources',\r\n    icon: Database\r\n  }), []);\r\n\r\n  usePageTitle(pageConfig);\r\n\r\n  const [connectedSources, setConnectedSources] = useState<ConnectedDatabase[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n  const [apiError, setApiError] = useState<string | null>(null);\r\n\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [dataSourceToConnect, setDataSourceToConnect] = useState<DataSourceType | null>(null);\r\n  \r\n  // State for managing dropdown menus\r\n  const [menuOpenId, setMenuOpenId] = useState<string | null>(null);\r\n\r\n  // New state for enhanced features\r\n  const [searchQuery] = useState('');\r\n  const [selectedCategory] = useState<string>('all');\r\n\r\n  console.log('DataSourcesPageContent: Component render');\r\n\r\n  // Load data when authenticated - handles page refresh and auth state changes\r\n  useEffect(() => {\r\n    console.log('DataSourcesPageContent: Auth state changed, isAuthenticated:', isAuthenticated);\r\n\r\n    if (isAuthenticated) {\r\n      const fetchInitialData = async () => {\r\n        console.log('DataSourcesPageContent: Starting initial fetch');\r\n        setIsLoading(true);\r\n        setApiError(null);\r\n        try {\r\n          const sources = await listDatabases();\r\n          console.log('DataSourcesPageContent: Initial fetch completed, sources:', sources.length);\r\n          setConnectedSources(sources);\r\n        } catch (err) {\r\n          console.error('Failed to fetch connected data sources', err);\r\n          setApiError('Could not load connected data sources. Please try again.');\r\n          setConnectedSources([]);\r\n        } finally {\r\n          setIsLoading(false);\r\n        }\r\n      };\r\n\r\n      fetchInitialData();\r\n    } else {\r\n      // Clear data when not authenticated\r\n      setConnectedSources([]);\r\n      setApiError(null);\r\n      setIsLoading(false);\r\n    }\r\n  }, [isAuthenticated, listDatabases]); // Depend on auth state\r\n\r\n  // Separate refresh function for manual refresh button\r\n  const handleRefresh = useCallback(async () => {\r\n    if (!isAuthenticated) {\r\n      setApiError('Please log in to refresh data sources.');\r\n      return;\r\n    }\r\n\r\n    setIsRefreshing(true);\r\n    setApiError(null);\r\n    try {\r\n      const sources = await listDatabases();\r\n      setConnectedSources(sources);\r\n    } catch (err) {\r\n      console.error('Failed to refresh connected data sources', err);\r\n      setApiError('Could not refresh data sources. Please try again.');\r\n    } finally {\r\n      setIsRefreshing(false);\r\n    }\r\n  }, [isAuthenticated, listDatabases]);\r\n\r\n  // Retry function for error recovery\r\n  const retryLoadDataSources = useCallback(async () => {\r\n    if (!isAuthenticated) return;\r\n\r\n    setApiError(null);\r\n    setIsLoading(true);\r\n    try {\r\n      const sources = await listDatabases();\r\n      setConnectedSources(sources);\r\n    } catch (err) {\r\n      console.error('Failed to retry loading data sources', err);\r\n      setApiError('Could not load data sources. Please try again.');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [isAuthenticated, listDatabases]);\r\n\r\n  // Filter data sources based on search and category\r\n  const filteredAvailableDataSources = AVAILABLE_DATA_SOURCE_TYPES.filter(source => {\r\n    const matchesSearch = source.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n      source.description.toLowerCase().includes(searchQuery.toLowerCase());\r\n    const matchesCategory = selectedCategory === 'all' || source.typeCategory === selectedCategory;\r\n    return matchesSearch && matchesCategory;\r\n  });\r\n\r\n  const filteredConnectedSources = connectedSources.filter(source => {\r\n    const matchesSearch = source.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n      source.type.toLowerCase().includes(searchQuery.toLowerCase());\r\n    return matchesSearch;\r\n  });\r\n\r\n  // Helper function to refresh after successful connect/disconnect operations\r\n  const refreshAfterChange = useCallback(async () => {\r\n    setApiError(null);\r\n    try {\r\n      const sources = await listDatabases();\r\n      setConnectedSources(sources);\r\n    } catch (err) {\r\n      console.error('Failed to refresh after change', err);\r\n      setApiError('Could not refresh data sources after the operation.');\r\n    }\r\n  }, [listDatabases]);\r\n\r\n  const handleOpenConnectModal = useCallback((sourceType: DataSourceType) => {\r\n    setDataSourceToConnect(sourceType);\r\n    setIsModalOpen(true);\r\n  }, []);\r\n\r\n  const handleCloseModal = useCallback(() => {\r\n    setIsModalOpen(false);\r\n    setDataSourceToConnect(null);\r\n  }, []);\r\n\r\n  const handleEditConnection = useCallback((connection: ConnectedDatabase) => {\r\n    console.log('Edit functionality not yet implemented for connection:', connection.name);\r\n    setMenuOpenId(null);\r\n    // TODO: Implement edit functionality when API supports it\r\n  }, []);\r\n\r\n  const handleDeleteConnection = useCallback(async (connectionId: string, connectionName: string) => {\r\n    console.log('Deleting connection:', connectionName);\r\n    setMenuOpenId(null);\r\n    \r\n    // Show confirmation dialog\r\n    const confirmed = window.confirm(`Are you sure you want to delete the connection \"${connectionName}\"? This action cannot be undone.`);\r\n    if (!confirmed) return;\r\n\r\n    setApiError(null);\r\n    try {\r\n      console.log('Disconnecting database with ID:', connectionId);\r\n      const result = await disconnectExistingDatabase(connectionId);\r\n      console.log('Successfully disconnected database:', result.message);\r\n      \r\n      // Refresh the list after successful deletion\r\n      await refreshAfterChange();\r\n    } catch (err: unknown) {\r\n      console.error('Failed to delete connection', err);\r\n      let errorMessage = 'Failed to delete connection. Please try again.';\r\n      \r\n      if (err && typeof err === 'object' && 'response' in err) {\r\n        const axiosErr = err as { response?: { status?: number; data?: { detail?: unknown; message?: string } } };\r\n        console.log('Error response:', axiosErr.response?.data);\r\n\r\n        if (axiosErr.response?.status === 422) {\r\n          // Handle FastAPI validation errors\r\n          const detail = axiosErr.response.data?.detail;\r\n          if (Array.isArray(detail)) {\r\n            // Pydantic validation errors are arrays\r\n            const validationErrors = detail.map((error: unknown) => {\r\n              if (error && typeof error === 'object' && 'loc' in error && 'msg' in error) {\r\n                const field = Array.isArray((error as { loc: unknown }).loc) \r\n                  ? ((error as { loc: string[] }).loc).join('.') \r\n                  : 'unknown field';\r\n                return `${field}: ${(error as { msg: string }).msg}`;\r\n              }\r\n              return 'validation error';\r\n            }).join(', ');\r\n            errorMessage = `Validation error: ${validationErrors}`;\r\n          } else if (typeof detail === 'string') {\r\n            errorMessage = detail;\r\n          } else {\r\n            errorMessage = 'Validation failed. Please check your input.';\r\n          }\r\n        } else if (axiosErr.response?.data?.detail && typeof axiosErr.response.data.detail === 'string') {\r\n          errorMessage = axiosErr.response.data.detail;\r\n        } else if (axiosErr.response?.data?.message) {\r\n          errorMessage = axiosErr.response.data.message;\r\n        }\r\n      } else if (err && typeof err === 'object' && 'message' in err && typeof (err as { message: unknown }).message === 'string') {\r\n        errorMessage = (err as { message: string }).message;\r\n      }\r\n\r\n      setApiError(errorMessage);\r\n    }\r\n  }, [disconnectExistingDatabase, refreshAfterChange]);\r\n\r\n  const handleConnect = useCallback(async (params: DatabaseConnectionRequestParams) => {\r\n    if (!dataSourceToConnect) return false;\r\n\r\n    setApiError(null);\r\n    try {\r\n      const fullParams = { ...params, type: dataSourceToConnect.id };\r\n      console.log('DataSourcesPageContent: Sending connect request with params:', fullParams);\r\n      await connectNewDatabase(fullParams);\r\n      await refreshAfterChange(); // Use the helper function\r\n      handleCloseModal();\r\n      return true;\r\n    } catch (err: unknown) {\r\n      console.error('Failed to connect data source', err);\r\n\r\n      // Handle different types of errors\r\n      let errorMessage = 'Failed to connect. Please check details and try again.';\r\n\r\n      if (err && typeof err === 'object' && 'response' in err) {\r\n        const axiosErr = err as { response?: { status?: number; data?: { detail?: unknown; message?: string } } };\r\n        console.log('Error response:', axiosErr.response?.data);\r\n\r\n        if (axiosErr.response?.status === 422) {\r\n          // Handle FastAPI validation errors\r\n          const detail = axiosErr.response.data?.detail;\r\n          if (Array.isArray(detail)) {\r\n            // Pydantic validation errors are arrays\r\n            const validationErrors = detail.map((error: unknown) => {\r\n              if (error && typeof error === 'object' && 'loc' in error && 'msg' in error) {\r\n                const field = Array.isArray((error as { loc: unknown }).loc) \r\n                  ? ((error as { loc: string[] }).loc).join('.') \r\n                  : 'unknown field';\r\n                return `${field}: ${(error as { msg: string }).msg}`;\r\n              }\r\n              return 'validation error';\r\n            }).join(', ');\r\n            errorMessage = `Validation error: ${validationErrors}`;\r\n          } else if (typeof detail === 'string') {\r\n            errorMessage = detail;\r\n          } else {\r\n            errorMessage = 'Validation failed. Please check your input.';\r\n          }\r\n        } else if (axiosErr.response?.data?.detail && typeof axiosErr.response.data.detail === 'string') {\r\n          errorMessage = axiosErr.response.data.detail;\r\n        } else if (axiosErr.response?.data?.message) {\r\n          errorMessage = axiosErr.response.data.message;\r\n        }\r\n      } else if (err && typeof err === 'object' && 'message' in err && typeof (err as { message: unknown }).message === 'string') {\r\n        errorMessage = (err as { message: string }).message;\r\n      }\r\n\r\n      setApiError(errorMessage);\r\n      return false;\r\n    }\r\n  }, [dataSourceToConnect, connectNewDatabase, refreshAfterChange, handleCloseModal]);\r\n\r\n  // const handleDisconnect = useCallback(async (dbId: string) => {\r\n  //   setApiError(null);\r\n  //   try {\r\n  //     await disconnectExistingDatabase(dbId);\r\n  //     await refreshAfterChange(); // Use the helper function\r\n  //   } catch (err: unknown) {\r\n  //     console.error('Failed to disconnect data source', err);\r\n  //     let errorMessage = 'Failed to disconnect. Please try again.';\r\n  //     if (err && typeof err === 'object' && 'response' in err) {\r\n  //       const axiosErr = err as { response?: { data?: { detail?: string } } };\r\n  //       errorMessage = axiosErr.response?.data?.detail || errorMessage;\r\n  //     }\r\n  //     setApiError(errorMessage);\r\n  //   }\r\n  // }, [disconnectExistingDatabase, refreshAfterChange]);\r\n\r\n  const renderDataSourceTypeCard = (sourceType: DataSourceType) => {\r\n    const Icon = sourceType.icon;\r\n\r\n    return (\r\n      <Card\r\n        key={sourceType.id}\r\n        className=\"group h-full border border-sidebar-border bg-sidebar-bg\"\r\n        style={{\r\n          backgroundColor: 'var(--interactive-bg-secondary-hover)'\r\n        }}\r\n      >\r\n        <CardContent className=\"flex flex-col items-center justify-center p-6 text-center space-y-4\">\r\n          {/* Logo */}\r\n          <div className=\"w-12 h-12 flex items-center justify-center\">\r\n            <Icon className=\"h-10 w-10\" />\r\n          </div>\r\n          \r\n          {/* Database Name */}\r\n          <h3 \r\n            className=\"text-lg font-medium\"\r\n            style={{ color: 'var(--sidebar-text-primary)' }}\r\n          >\r\n            {sourceType.name}\r\n          </h3>\r\n          \r\n          {/* Connect Button */}\r\n          <Button\r\n            onClick={() => handleOpenConnectModal(sourceType)}\r\n            variant=\"outline\"\r\n            className=\"w-full justify-center text-sm font-normal border border-sidebar-border rounded-lg transition-all duration-200 h-9\"\r\n            size=\"sm\"\r\n            disabled={isLoading || isRefreshing}\r\n            aria-label={`Connect to ${sourceType.name}`}\r\n            style={{\r\n              color: 'var(--sidebar-text-secondary)',\r\n              backgroundColor: 'transparent'\r\n            }}\r\n            onMouseEnter={(e) => {\r\n              if (!isLoading && !isRefreshing) {\r\n                e.currentTarget.style.backgroundColor = 'var(--sidebar-text-primary)';\r\n                e.currentTarget.style.setProperty('color', 'var(--sidebar-bg)', 'important');\r\n                e.currentTarget.style.transform = 'scale(1.02)';\r\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (!isLoading && !isRefreshing) {\r\n                e.currentTarget.style.backgroundColor = 'transparent';\r\n                e.currentTarget.style.setProperty('color', 'var(--sidebar-text-secondary)', 'important');\r\n                e.currentTarget.style.transform = 'scale(1)';\r\n                e.currentTarget.style.boxShadow = 'none';\r\n              }\r\n            }}\r\n          >\r\n            Connect\r\n          </Button>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  };\r\n\r\n  const renderConnectedDataSourceCard = (connectedDb: ConnectedDatabase) => {\r\n    const dsType = AVAILABLE_DATA_SOURCE_TYPES.find(type => type.id.toUpperCase() === connectedDb.type.toUpperCase());\r\n    const Icon = dsType?.icon || Database;\r\n\r\n    return (\r\n      <div\r\n        key={connectedDb.id}\r\n        className=\"group flex items-center gap-3 p-3 rounded-lg transition-all duration-200 border border-sidebar-border min-w-fit\"\r\n        style={{\r\n          backgroundColor: 'var(--sidebar-bg)'\r\n        }}\r\n        onMouseEnter={(e) => {\r\n          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n        }}\r\n        onMouseLeave={(e) => {\r\n          e.currentTarget.style.backgroundColor = 'var(--sidebar-bg)';\r\n        }}\r\n      >\r\n        {/* Icon Container */}\r\n        <div className=\"flex-shrink-0 w-8 h-8 rounded flex items-center justify-center\">\r\n          <Icon className=\"h-5 w-5\" />\r\n        </div>\r\n        \r\n        {/* Database Name */}\r\n        <div \r\n          className=\"cursor-pointer\"\r\n          onClick={() => {\r\n            // Add click handler for navigation/configuration if needed\r\n            console.log('Clicked on database:', connectedDb.name);\r\n          }}\r\n        >\r\n          <span \r\n            className=\"text-sm font-medium whitespace-nowrap block\"\r\n            style={{ color: 'var(--sidebar-text-primary)' }}\r\n          >\r\n            {connectedDb.name}\r\n          </span>\r\n        </div>\r\n\r\n        {/* Three-dots Menu */}\r\n        <div className=\"relative ml-2 flex-shrink-0\" onClick={e => e.stopPropagation()}>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                type=\"button\"\r\n                size=\"icon\"\r\n                variant=\"ghost\"\r\n                className=\"w-7 h-7 p-0 transition-all duration-200 rounded border-0\"\r\n                style={{ \r\n                  color: 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.setProperty('background-color', 'var(--interactive-bg-secondary-hover)', 'important');\r\n                  e.currentTarget.style.setProperty('color', 'var(--sidebar-text-primary)', 'important');\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.setProperty('background-color', 'transparent', 'important');\r\n                  e.currentTarget.style.setProperty('color', 'var(--sidebar-text-secondary)', 'important');\r\n                }}\r\n                aria-label={`More actions for ${connectedDb.name}`}\r\n              >\r\n                <MoreVertical className=\"w-4 h-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent \r\n              align=\"start\" \r\n              side=\"bottom\" \r\n              sideOffset={8} \r\n              className=\"border-none shadow-xl rounded-xl p-2\"\r\n              style={{\r\n                backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                color: 'var(--sidebar-text-primary)'\r\n              }}\r\n            >\r\n              <DropdownMenuItem\r\n                onClick={() => handleEditConnection(connectedDb)}\r\n                className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                style={{ \r\n                  color: 'var(--sidebar-text-primary)',\r\n                  backgroundColor: 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }}\r\n              >\r\n                Edit Connection\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem\r\n                onClick={() => handleDeleteConnection(connectedDb.id, connectedDb.name)}\r\n                className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                style={{ \r\n                  color: '#ff8583',\r\n                  backgroundColor: 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }}\r\n              >\r\n                Delete Connection\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <TooltipProvider>\r\n      <div\r\n        className=\"min-h-screen bg-sidebar-bg\"\r\n        role=\"main\"\r\n        aria-label=\"Data Sources Management\"\r\n      >\r\n        <div className=\"container mx-auto space-y-6 sm:space-y-8 p-4 sm:p-6 lg:p-8\">\r\n\r\n          {/* Error Alert */}\r\n          {apiError && (\r\n            <div className=\"p-4 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg\">\r\n              <div className=\"flex items-start justify-between gap-3\">\r\n                <div className=\"flex items-start gap-3\">\r\n                  <AlertTriangle className=\"h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5\" />\r\n                  <div className=\"text-red-800 dark:text-red-200\">\r\n                    <div className=\"font-medium\">Error</div>\r\n                    <div className=\"text-sm\">{apiError}</div>\r\n                  </div>\r\n                </div>\r\n                <Button\r\n                  onClick={retryLoadDataSources}\r\n                  disabled={isLoading || isRefreshing}\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"ml-4 border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900\"\r\n                >\r\n                  Try Again\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Connected Data Sources Section */}\r\n          <div className=\"space-y-6\">\r\n            <div>\r\n              <h2 className=\"text-xl sm:text-2xl font-semibold text-sidebar-text-primary flex items-center gap-2\">\r\n                Connected Sources\r\n              </h2>\r\n              <p className=\"text-sm sm:text-base text-sidebar-text-secondary mt-1\">\r\n                {filteredConnectedSources.length} of {connectedSources.length} {connectedSources.length === 1 ? 'source' : 'sources'}\r\n                {searchQuery && ' matching your search'}\r\n              </p>\r\n            </div>\r\n\r\n            {isLoading ? (\r\n              // Loading skeleton\r\n              <div className=\"flex flex-wrap gap-4\">\r\n                {[...Array(4)].map((_, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className=\"flex items-center gap-3 p-3 rounded-lg border border-sidebar-border animate-pulse min-w-fit\"\r\n                    style={{\r\n                      backgroundColor: 'var(--sidebar-bg)'\r\n                    }}\r\n                  >\r\n                    {/* Icon skeleton */}\r\n                    <div className=\"flex-shrink-0 w-8 h-8 rounded bg-sidebar-text-secondary opacity-20\"></div>\r\n                    \r\n                    {/* Text skeleton */}\r\n                    <div className=\"min-w-0\">\r\n                      <div className=\"h-4 bg-sidebar-text-secondary opacity-20 rounded w-24\"></div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            ) : filteredConnectedSources.length > 0 ? (\r\n              <div className=\"flex flex-wrap gap-4\">\r\n                {filteredConnectedSources.map(renderConnectedDataSourceCard)}\r\n              </div>\r\n            ) : connectedSources.length > 0 ? (\r\n              <Card className=\"text-center py-12 border-dashed border-2 border-sidebar-border bg-sidebar-bg\">\r\n                <CardContent className=\"space-y-4\">\r\n                  <div className=\"p-4 rounded-full bg-sidebar-text-secondary w-fit mx-auto\">\r\n                    <Search className=\"h-8 w-8 text-sidebar-text-secondary\" />\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-sidebar-text-primary\">\r\n                      No sources match your search\r\n                    </h3>\r\n                    <p className=\"text-sidebar-text-secondary mt-2\">\r\n                      Try adjusting your search terms or filters\r\n                    </p>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            ) : (\r\n              <Card className=\"text-center py-12 border-dashed border-2 border-sidebar-border bg-sidebar-bg\">\r\n                <CardContent className=\"space-y-4\">\r\n                  <div className=\"p-4 rounded-full bg-sidebar-text-secondary w-fit mx-auto\">\r\n                    <Database className=\"h-8 w-8 text-sidebar-text-secondary\" />\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-sidebar-text-primary\">\r\n                      No data sources connected\r\n                    </h3>\r\n                    <p className=\"text-sidebar-text-secondary mt-2\">\r\n                      Connect your first data source to get started with AI-powered queries\r\n                    </p>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            )}\r\n          </div>\r\n\r\n          {/* Separator */}\r\n          <div className=\"border-t border-sidebar-border my-8\"></div>\r\n\r\n          {/* Available Data Source Types Section */}\r\n          <div className=\"space-y-6\">\r\n            <div>\r\n              <h2 className=\"text-xl sm:text-2xl font-semibold text-sidebar-text-primary flex items-center gap-2\">\r\n                Add New Connection\r\n              </h2>\r\n              <p className=\"text-sm sm:text-base text-sidebar-text-secondary mt-1\">\r\n                {filteredAvailableDataSources.length} available data source{filteredAvailableDataSources.length !== 1 ? 's' : ''}\r\n                {searchQuery && ' matching your search'}\r\n              </p>\r\n            </div>\r\n\r\n            {filteredAvailableDataSources.length > 0 ? (\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4\">\r\n                {filteredAvailableDataSources.map(renderDataSourceTypeCard)}\r\n              </div>\r\n            ) : (\r\n              <Card className=\"text-center py-12 border-dashed border-2 border-sidebar-border bg-sidebar-bg\">\r\n                <CardContent className=\"space-y-4\">\r\n                  <div className=\"p-4 rounded-full bg-sidebar-text-secondary w-fit mx-auto\">\r\n                    <Filter className=\"h-8 w-8 text-sidebar-text-secondary\" />\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-sidebar-text-primary\">\r\n                      No data sources match your filters\r\n                    </h3>\r\n                    <p className=\"text-sidebar-text-secondary mt-2\">\r\n                      Try adjusting your search terms or category filters\r\n                    </p>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            )}\r\n          </div>\r\n\r\n          {/* Connection Modal */}\r\n          {dataSourceToConnect && (\r\n            <ConnectDataSourceModal\r\n              isOpen={isModalOpen}\r\n              onClose={handleCloseModal}\r\n              dataSourceType={dataSourceToConnect}\r\n              onConnect={handleConnect}\r\n            />\r\n          )}\r\n        </div>\r\n      </div>\r\n    </TooltipProvider>\r\n  );\r\n};\r\n\r\nexport default DataSourcesPageContent; "], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA,iDAAiD;AACjD;AACA;;;AAlBA;;;;;;;;;;;AAoBA,0DAA0D;AAC1D,MAAM,eAAe,CAAC,EAAE,SAAS,EAA0B,iBACzD,6LAAC;QACC,KAAI;QACJ,KAAI;QACJ,WAAW;;;;;;KAJT;AAQN,MAAM,YAAY,CAAC,EAAE,SAAS,EAA0B,iBACtD,6LAAC;QACC,KAAI;QACJ,KAAI;QACJ,WAAW;;;;;;MAJT;AAQN,MAAM,cAAc,CAAC,EAAE,SAAS,EAA0B,iBACxD,6LAAC;QACC,KAAI;QACJ,KAAI;QACJ,WAAW;;;;;;MAJT;AAQN,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAA0B,iBAC3D,6LAAC;QACC,KAAI;QACJ,KAAI;QACJ,WAAW;;;;;;MAJT;AA0BN,6CAA6C;AAC7C,MAAM,8BAAgD;IACpD;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,aAAa;QACb,MAAM;QACN,kBAAkB;YAChB;gBAAE,OAAO;gBAAmB,MAAM;gBAAQ,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAmB;YACxG;gBAAE,OAAO;gBAAQ,MAAM;gBAAQ,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAY;YACtF;gBAAE,OAAO;gBAAQ,MAAM;gBAAQ,MAAM;gBAAU,UAAU;gBAAM,aAAa;YAAO;YACnF;gBAAE,OAAO;gBAAY,MAAM;gBAAY,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAW;YAC7F;gBAAE,OAAO;gBAAY,MAAM;gBAAY,MAAM;gBAAY,UAAU;YAAK;YACxE;gBAAE,OAAO;gBAAiB,MAAM;gBAAY,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAa;YACpG;gBAAE,OAAO;gBAAqB,MAAM;gBAAa,MAAM;gBAAQ,aAAa;YAAS;YACrF;gBAAE,OAAO;gBAAc,MAAM;gBAAe,MAAM;YAAW;SAC9D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,aAAa;QACb,MAAM;QACN,kBAAkB;YAChB;gBAAE,OAAO;gBAAmB,MAAM;gBAAQ,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAiB;YACtG;gBAAE,OAAO;gBAAQ,MAAM;gBAAQ,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAsC;YAChH;gBAAE,OAAO;gBAAQ,MAAM;gBAAQ,MAAM;gBAAU,UAAU;gBAAM,aAAa;YAAO;YACnF;gBAAE,OAAO;gBAAY,MAAM;gBAAY,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAgC;YAClH;gBAAE,OAAO;gBAAY,MAAM;gBAAY,MAAM;gBAAY,UAAU;YAAK;YACxE;gBAAE,OAAO;gBAAiB,MAAM;gBAAY,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAW;YAClG;gBAAE,OAAO;gBAAc,MAAM;gBAAe,MAAM;gBAAY,UAAU;YAAK;SAC9E;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,aAAa;QACb,MAAM;QACN,kBAAkB;YAChB;gBAAE,OAAO;gBAAmB,MAAM;gBAAQ,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAmB;YACxG;gBAAE,OAAO;gBAAqB,MAAM;gBAAqB,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAqD;SAC1J;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,aAAa;QACb,MAAM;QACN,kBAAkB;YAChB;gBAAE,OAAO;gBAAmB,MAAM;gBAAQ,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAc;YACnG;gBAAE,OAAO;gBAAQ,MAAM;gBAAQ,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAY;YACtF;gBAAE,OAAO;gBAAQ,MAAM;gBAAQ,MAAM;gBAAU,UAAU;gBAAM,aAAa;YAAO;YACnF;gBAAE,OAAO;gBAAY,MAAM;gBAAY,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAO;YACzF;gBAAE,OAAO;gBAAY,MAAM;gBAAY,MAAM;gBAAY,UAAU;YAAK;YACxE;gBAAE,OAAO;gBAAiB,MAAM;gBAAY,MAAM;gBAAQ,UAAU;gBAAM,aAAa;YAAa;SACrG;IACC;CACH;AAIH,wDAAwD;AACxD,iFAAiF;AACjF,uBAAuB;AACvB,eAAe;AACf,yJAAyJ;AACzJ,4CAA4C;AAC5C,oBAAoB;AACpB,iBAAiB;AACjB,SAAS;AACT,MAAM;AAEN,aAAa;AACb,+EAA+E;AAC/E,6CAA6C;AAC7C,qBAAqB;AACrB,eAAe;AACf,OAAO;AACP,KAAK;AAEL,MAAM,yBAAyB;;IAC7B,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,0BAA0B,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,SAAM,AAAD;IAC/E,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAElC,kDAAkD;IAClD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sDAAE,IAAM,CAAC;gBAChC,OAAO;gBACP,MAAM,6MAAA,CAAA,WAAQ;YAChB,CAAC;qDAAG,EAAE;IAEN,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE;IAEb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAEtF,oCAAoC;IACpC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,kCAAkC;IAClC,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE5C,QAAQ,GAAG,CAAC;IAEZ,6EAA6E;IAC7E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,QAAQ,GAAG,CAAC,gEAAgE;YAE5E,IAAI,iBAAiB;gBACnB,MAAM;yEAAmB;wBACvB,QAAQ,GAAG,CAAC;wBACZ,aAAa;wBACb,YAAY;wBACZ,IAAI;4BACF,MAAM,UAAU,MAAM;4BACtB,QAAQ,GAAG,CAAC,6DAA6D,QAAQ,MAAM;4BACvF,oBAAoB;wBACtB,EAAE,OAAO,KAAK;4BACZ,QAAQ,KAAK,CAAC,0CAA0C;4BACxD,YAAY;4BACZ,oBAAoB,EAAE;wBACxB,SAAU;4BACR,aAAa;wBACf;oBACF;;gBAEA;YACF,OAAO;gBACL,oCAAoC;gBACpC,oBAAoB,EAAE;gBACtB,YAAY;gBACZ,aAAa;YACf;QACF;2CAAG;QAAC;QAAiB;KAAc,GAAG,uBAAuB;IAE7D,sDAAsD;IACtD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YAChC,IAAI,CAAC,iBAAiB;gBACpB,YAAY;gBACZ;YACF;YAEA,gBAAgB;YAChB,YAAY;YACZ,IAAI;gBACF,MAAM,UAAU,MAAM;gBACtB,oBAAoB;YACtB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,4CAA4C;gBAC1D,YAAY;YACd,SAAU;gBACR,gBAAgB;YAClB;QACF;4DAAG;QAAC;QAAiB;KAAc;IAEnC,oCAAoC;IACpC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oEAAE;YACvC,IAAI,CAAC,iBAAiB;YAEtB,YAAY;YACZ,aAAa;YACb,IAAI;gBACF,MAAM,UAAU,MAAM;gBACtB,oBAAoB;YACtB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,YAAY;YACd,SAAU;gBACR,aAAa;YACf;QACF;mEAAG;QAAC;QAAiB;KAAc;IAEnC,mDAAmD;IACnD,MAAM,+BAA+B,4BAA4B,MAAM,CAAC,CAAA;QACtE,MAAM,gBAAgB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC9E,OAAO,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QACnE,MAAM,kBAAkB,qBAAqB,SAAS,OAAO,YAAY,KAAK;QAC9E,OAAO,iBAAiB;IAC1B;IAEA,MAAM,2BAA2B,iBAAiB,MAAM,CAAC,CAAA;QACvD,MAAM,gBAAgB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC9E,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAC5D,OAAO;IACT;IAEA,4EAA4E;IAC5E,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAAE;YACrC,YAAY;YACZ,IAAI;gBACF,MAAM,UAAU,MAAM;gBACtB,oBAAoB;YACtB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,YAAY;YACd;QACF;iEAAG;QAAC;KAAc;IAElB,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sEAAE,CAAC;YAC1C,uBAAuB;YACvB,eAAe;QACjB;qEAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE;YACnC,eAAe;YACf,uBAAuB;QACzB;+DAAG,EAAE;IAEL,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oEAAE,CAAC;YACxC,QAAQ,GAAG,CAAC,0DAA0D,WAAW,IAAI;YACrF,cAAc;QACd,0DAA0D;QAC5D;mEAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sEAAE,OAAO,cAAsB;YACtE,QAAQ,GAAG,CAAC,wBAAwB;YACpC,cAAc;YAEd,2BAA2B;YAC3B,MAAM,YAAY,OAAO,OAAO,CAAC,CAAC,gDAAgD,EAAE,eAAe,gCAAgC,CAAC;YACpI,IAAI,CAAC,WAAW;YAEhB,YAAY;YACZ,IAAI;gBACF,QAAQ,GAAG,CAAC,mCAAmC;gBAC/C,MAAM,SAAS,MAAM,2BAA2B;gBAChD,QAAQ,GAAG,CAAC,uCAAuC,OAAO,OAAO;gBAEjE,6CAA6C;gBAC7C,MAAM;YACR,EAAE,OAAO,KAAc;gBACrB,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,IAAI,eAAe;gBAEnB,IAAI,OAAO,OAAO,QAAQ,YAAY,cAAc,KAAK;oBACvD,MAAM,WAAW;oBACjB,QAAQ,GAAG,CAAC,mBAAmB,SAAS,QAAQ,EAAE;oBAElD,IAAI,SAAS,QAAQ,EAAE,WAAW,KAAK;wBACrC,mCAAmC;wBACnC,MAAM,SAAS,SAAS,QAAQ,CAAC,IAAI,EAAE;wBACvC,IAAI,MAAM,OAAO,CAAC,SAAS;4BACzB,wCAAwC;4BACxC,MAAM,mBAAmB,OAAO,GAAG;+GAAC,CAAC;oCACnC,IAAI,SAAS,OAAO,UAAU,YAAY,SAAS,SAAS,SAAS,OAAO;wCAC1E,MAAM,QAAQ,MAAM,OAAO,CAAC,AAAC,MAA2B,GAAG,IACvD,AAAC,AAAC,MAA4B,GAAG,CAAE,IAAI,CAAC,OACxC;wCACJ,OAAO,GAAG,MAAM,EAAE,EAAE,AAAC,MAA0B,GAAG,EAAE;oCACtD;oCACA,OAAO;gCACT;8GAAG,IAAI,CAAC;4BACR,eAAe,CAAC,kBAAkB,EAAE,kBAAkB;wBACxD,OAAO,IAAI,OAAO,WAAW,UAAU;4BACrC,eAAe;wBACjB,OAAO;4BACL,eAAe;wBACjB;oBACF,OAAO,IAAI,SAAS,QAAQ,EAAE,MAAM,UAAU,OAAO,SAAS,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,UAAU;wBAC/F,eAAe,SAAS,QAAQ,CAAC,IAAI,CAAC,MAAM;oBAC9C,OAAO,IAAI,SAAS,QAAQ,EAAE,MAAM,SAAS;wBAC3C,eAAe,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO;oBAC/C;gBACF,OAAO,IAAI,OAAO,OAAO,QAAQ,YAAY,aAAa,OAAO,OAAO,AAAC,IAA6B,OAAO,KAAK,UAAU;oBAC1H,eAAe,AAAC,IAA4B,OAAO;gBACrD;gBAEA,YAAY;YACd;QACF;qEAAG;QAAC;QAA4B;KAAmB;IAEnD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,OAAO;YACvC,IAAI,CAAC,qBAAqB,OAAO;YAEjC,YAAY;YACZ,IAAI;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM;oBAAE,MAAM,oBAAoB,EAAE;gBAAC;gBAC7D,QAAQ,GAAG,CAAC,gEAAgE;gBAC5E,MAAM,mBAAmB;gBACzB,MAAM,sBAAsB,0BAA0B;gBACtD;gBACA,OAAO;YACT,EAAE,OAAO,KAAc;gBACrB,QAAQ,KAAK,CAAC,iCAAiC;gBAE/C,mCAAmC;gBACnC,IAAI,eAAe;gBAEnB,IAAI,OAAO,OAAO,QAAQ,YAAY,cAAc,KAAK;oBACvD,MAAM,WAAW;oBACjB,QAAQ,GAAG,CAAC,mBAAmB,SAAS,QAAQ,EAAE;oBAElD,IAAI,SAAS,QAAQ,EAAE,WAAW,KAAK;wBACrC,mCAAmC;wBACnC,MAAM,SAAS,SAAS,QAAQ,CAAC,IAAI,EAAE;wBACvC,IAAI,MAAM,OAAO,CAAC,SAAS;4BACzB,wCAAwC;4BACxC,MAAM,mBAAmB,OAAO,GAAG;sGAAC,CAAC;oCACnC,IAAI,SAAS,OAAO,UAAU,YAAY,SAAS,SAAS,SAAS,OAAO;wCAC1E,MAAM,QAAQ,MAAM,OAAO,CAAC,AAAC,MAA2B,GAAG,IACvD,AAAC,AAAC,MAA4B,GAAG,CAAE,IAAI,CAAC,OACxC;wCACJ,OAAO,GAAG,MAAM,EAAE,EAAE,AAAC,MAA0B,GAAG,EAAE;oCACtD;oCACA,OAAO;gCACT;qGAAG,IAAI,CAAC;4BACR,eAAe,CAAC,kBAAkB,EAAE,kBAAkB;wBACxD,OAAO,IAAI,OAAO,WAAW,UAAU;4BACrC,eAAe;wBACjB,OAAO;4BACL,eAAe;wBACjB;oBACF,OAAO,IAAI,SAAS,QAAQ,EAAE,MAAM,UAAU,OAAO,SAAS,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,UAAU;wBAC/F,eAAe,SAAS,QAAQ,CAAC,IAAI,CAAC,MAAM;oBAC9C,OAAO,IAAI,SAAS,QAAQ,EAAE,MAAM,SAAS;wBAC3C,eAAe,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO;oBAC/C;gBACF,OAAO,IAAI,OAAO,OAAO,QAAQ,YAAY,aAAa,OAAO,OAAO,AAAC,IAA6B,OAAO,KAAK,UAAU;oBAC1H,eAAe,AAAC,IAA4B,OAAO;gBACrD;gBAEA,YAAY;gBACZ,OAAO;YACT;QACF;4DAAG;QAAC;QAAqB;QAAoB;QAAoB;KAAiB;IAElF,iEAAiE;IACjE,uBAAuB;IACvB,UAAU;IACV,8CAA8C;IAC9C,6DAA6D;IAC7D,6BAA6B;IAC7B,8DAA8D;IAC9D,oEAAoE;IACpE,iEAAiE;IACjE,+EAA+E;IAC/E,wEAAwE;IACxE,QAAQ;IACR,iCAAiC;IACjC,MAAM;IACN,wDAAwD;IAExD,MAAM,2BAA2B,CAAC;QAChC,MAAM,OAAO,WAAW,IAAI;QAE5B,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAEH,WAAU;YACV,OAAO;gBACL,iBAAiB;YACnB;sBAEA,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;;;;;;;;;;kCAIlB,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAA8B;kCAE7C,WAAW,IAAI;;;;;;kCAIlB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,uBAAuB;wBACtC,SAAQ;wBACR,WAAU;wBACV,MAAK;wBACL,UAAU,aAAa;wBACvB,cAAY,CAAC,WAAW,EAAE,WAAW,IAAI,EAAE;wBAC3C,OAAO;4BACL,OAAO;4BACP,iBAAiB;wBACnB;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,aAAa,CAAC,cAAc;gCAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,qBAAqB;gCAChE,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gCAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4BACpC;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,aAAa,CAAC,cAAc;gCAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,iCAAiC;gCAC5E,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gCAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4BACpC;wBACF;kCACD;;;;;;;;;;;;WAhDE,WAAW,EAAE;;;;;IAsDxB;IAEA,MAAM,gCAAgC,CAAC;QACrC,MAAM,SAAS,4BAA4B,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,CAAC,WAAW,OAAO,YAAY,IAAI,CAAC,WAAW;QAC9G,MAAM,OAAO,QAAQ,QAAQ,6MAAA,CAAA,WAAQ;QAErC,qBACE,6LAAC;YAEC,WAAU;YACV,OAAO;gBACL,iBAAiB;YACnB;YACA,cAAc,CAAC;gBACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;YAC1C;YACA,cAAc,CAAC;gBACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;YAC1C;;8BAGA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;;;;;;;;;;;8BAIlB,6LAAC;oBACC,WAAU;oBACV,SAAS;wBACP,2DAA2D;wBAC3D,QAAQ,GAAG,CAAC,wBAAwB,YAAY,IAAI;oBACtD;8BAEA,cAAA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAA8B;kCAE7C,YAAY,IAAI;;;;;;;;;;;8BAKrB,6LAAC;oBAAI,WAAU;oBAA8B,SAAS,CAAA,IAAK,EAAE,eAAe;8BAC1E,cAAA,6LAAC,+IAAA,CAAA,eAAY;;0CACX,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,OAAO;wCACL,OAAO;wCACP,iBAAiB;oCACnB;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,yCAAyC;wCAC/F,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,+BAA+B;oCAC5E;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,eAAe;wCACrE,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,iCAAiC;oCAC9E;oCACA,cAAY,CAAC,iBAAiB,EAAE,YAAY,IAAI,EAAE;8CAElD,cAAA,6LAAC,6NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAG5B,6LAAC,+IAAA,CAAA,sBAAmB;gCAClB,OAAM;gCACN,MAAK;gCACL,YAAY;gCACZ,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,OAAO;gCACT;;kDAEA,6LAAC,+IAAA,CAAA,mBAAgB;wCACf,SAAS,IAAM,qBAAqB;wCACpC,WAAU;wCACV,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;kDACD;;;;;;kDAGD,6LAAC,+IAAA,CAAA,mBAAgB;wCACf,SAAS,IAAM,uBAAuB,YAAY,EAAE,EAAE,YAAY,IAAI;wCACtE,WAAU;wCACV,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;kDACD;;;;;;;;;;;;;;;;;;;;;;;;WAlGF,YAAY,EAAE;;;;;IA0GzB;IAIA,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YACC,WAAU;YACV,MAAK;YACL,cAAW;sBAEX,cAAA,6LAAC;gBAAI,WAAU;;oBAGZ,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAc;;;;;;8DAC7B,6LAAC;oDAAI,WAAU;8DAAW;;;;;;;;;;;;;;;;;;8CAG9B,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,aAAa;oCACvB,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsF;;;;;;kDAGpG,6LAAC;wCAAE,WAAU;;4CACV,yBAAyB,MAAM;4CAAC;4CAAK,iBAAiB,MAAM;4CAAC;4CAAE,iBAAiB,MAAM,KAAK,IAAI,WAAW;4CAC1G,eAAe;;;;;;;;;;;;;4BAInB,YACC,mBAAmB;0CACnB,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,6LAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,iBAAiB;wCACnB;;0DAGA,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;;uCAXZ;;;;;;;;;uCAgBT,yBAAyB,MAAM,GAAG,kBACpC,6LAAC;gCAAI,WAAU;0CACZ,yBAAyB,GAAG,CAAC;;;;;uCAE9B,iBAAiB,MAAM,GAAG,kBAC5B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;8DAGhE,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;qDAOtD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;8DAGhE,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU1D,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsF;;;;;;kDAGpG,6LAAC;wCAAE,WAAU;;4CACV,6BAA6B,MAAM;4CAAC;4CAAuB,6BAA6B,MAAM,KAAK,IAAI,MAAM;4CAC7G,eAAe;;;;;;;;;;;;;4BAInB,6BAA6B,MAAM,GAAG,kBACrC,6LAAC;gCAAI,WAAU;0CACZ,6BAA6B,GAAG,CAAC;;;;;qDAGpC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;8DAGhE,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUzD,qCACC,6LAAC,0KAAA,CAAA,UAAsB;wBACrB,QAAQ;wBACR,SAAS;wBACT,gBAAgB;wBAChB,WAAW;;;;;;;;;;;;;;;;;;;;;;AAOzB;GAxlBM;;QACsE,kIAAA,CAAA,SAAM;QACpD,mIAAA,CAAA,UAAO;QAQnC,+HAAA,CAAA,eAAY;;;MAVR;uCA0lBS", "debugId": null}}, {"offset": {"line": 4680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/onboarding.ts"], "sourcesContent": ["// Onboarding utility functions\r\n\r\nimport { ROUTES } from '@/lib/constants';\r\n\r\n/**\r\n * Determines the appropriate redirect path based on user authentication and onboarding status\r\n */\r\nexport function getRedirectPath(\r\n  isAuthenticated: boolean,\r\n  isNewUser: boolean,\r\n  currentPath: string\r\n): string | null {\r\n  // Not authenticated - redirect to login (except for public routes)\r\n  if (!isAuthenticated) {\r\n    const publicRoutes = [\r\n      ROUTES.HOME,\r\n      ROUTES.LOGIN,\r\n      ROUTES.REGISTER,\r\n      ROUTES.AUTH.CALLBACK,\r\n      ROUTES.OAUTH.CALLBACK,\r\n      ROUTES.ONBOARDING,\r\n    ];\r\n\r\n    if (!publicRoutes.includes(currentPath as any)) {\r\n      return ROUTES.LOGIN;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // Authenticated user scenarios\r\n  if (isAuthenticated) {\r\n    // New user not on onboarding page - redirect to onboarding\r\n    if (isNewUser && currentPath !== ROUTES.ONBOARDING) {\r\n      const publicRoutes = [\r\n        ROUTES.HOME,\r\n        ROUTES.LOGIN,\r\n        ROUTES.REGISTER,\r\n        ROUTES.AUTH.CALLBACK,\r\n        ROUTES.OAUTH.CALLBACK,\r\n      ];\r\n\r\n      // Don't redirect if on public routes (except login)\r\n      if (!publicRoutes.includes(currentPath as any) || currentPath === ROUTES.LOGIN) {\r\n        return ROUTES.ONBOARDING;\r\n      }\r\n    }\r\n    \r\n    // Existing user on onboarding page - redirect to dashboard\r\n    if (!isNewUser && currentPath === ROUTES.ONBOARDING) {\r\n      return ROUTES.DASHBOARD;\r\n    }\r\n    \r\n    // Authenticated user on login or register page - redirect based on onboarding status\r\n    if (currentPath === ROUTES.LOGIN || currentPath === ROUTES.REGISTER) {\r\n      return isNewUser ? ROUTES.ONBOARDING : ROUTES.DASHBOARD;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Checks if a route requires authentication\r\n */\r\nexport function isProtectedRoute(path: string): boolean {\r\n  const publicRoutes = [\r\n    ROUTES.HOME,\r\n    ROUTES.LOGIN,\r\n    ROUTES.REGISTER,\r\n    ROUTES.AUTH.CALLBACK,\r\n    ROUTES.OAUTH.CALLBACK,\r\n    ROUTES.ONBOARDING,\r\n  ];\r\n\r\n  return !publicRoutes.includes(path as any);\r\n}\r\n\r\n/**\r\n * Checks if a route is accessible to new users\r\n */\r\nexport function isNewUserAccessibleRoute(path: string): boolean {\r\n  const newUserRoutes = [\r\n    ROUTES.HOME,\r\n    ROUTES.LOGIN,\r\n    ROUTES.REGISTER,\r\n    ROUTES.AUTH.CALLBACK,\r\n    ROUTES.OAUTH.CALLBACK,\r\n    ROUTES.ONBOARDING,\r\n  ];\r\n\r\n  return newUserRoutes.includes(path as any);\r\n}\r\n\r\n/**\r\n * Gets the next step in the onboarding flow after completion\r\n */\r\nexport function getPostOnboardingRedirect(): string {\r\n  return ROUTES.DASHBOARD;\r\n}\r\n\r\n/**\r\n * Validates if onboarding completion is allowed from the current state\r\n */\r\nexport function canCompleteOnboarding(\r\n  isAuthenticated: boolean,\r\n  isNewUser: boolean,\r\n  currentPath: string\r\n): boolean {\r\n  return isAuthenticated && isNewUser && currentPath === ROUTES.ONBOARDING;\r\n}\r\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;AAE/B;AAAA;;AAKO,SAAS,gBACd,eAAwB,EACxB,SAAkB,EAClB,WAAmB;IAEnB,mEAAmE;IACnE,IAAI,CAAC,iBAAiB;QACpB,MAAM,eAAe;YACnB,oIAAA,CAAA,SAAM,CAAC,IAAI;YACX,oIAAA,CAAA,SAAM,CAAC,KAAK;YACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;YACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;YACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YACrB,oIAAA,CAAA,SAAM,CAAC,UAAU;SAClB;QAED,IAAI,CAAC,aAAa,QAAQ,CAAC,cAAqB;YAC9C,OAAO,oIAAA,CAAA,SAAM,CAAC,KAAK;QACrB;QACA,OAAO;IACT;IAEA,+BAA+B;IAC/B,IAAI,iBAAiB;QACnB,2DAA2D;QAC3D,IAAI,aAAa,gBAAgB,oIAAA,CAAA,SAAM,CAAC,UAAU,EAAE;YAClD,MAAM,eAAe;gBACnB,oIAAA,CAAA,SAAM,CAAC,IAAI;gBACX,oIAAA,CAAA,SAAM,CAAC,KAAK;gBACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;gBACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;gBACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;aACtB;YAED,oDAAoD;YACpD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAuB,gBAAgB,oIAAA,CAAA,SAAM,CAAC,KAAK,EAAE;gBAC9E,OAAO,oIAAA,CAAA,SAAM,CAAC,UAAU;YAC1B;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAAC,aAAa,gBAAgB,oIAAA,CAAA,SAAM,CAAC,UAAU,EAAE;YACnD,OAAO,oIAAA,CAAA,SAAM,CAAC,SAAS;QACzB;QAEA,qFAAqF;QACrF,IAAI,gBAAgB,oIAAA,CAAA,SAAM,CAAC,KAAK,IAAI,gBAAgB,oIAAA,CAAA,SAAM,CAAC,QAAQ,EAAE;YACnE,OAAO,YAAY,oIAAA,CAAA,SAAM,CAAC,UAAU,GAAG,oIAAA,CAAA,SAAM,CAAC,SAAS;QACzD;IACF;IAEA,OAAO;AACT;AAKO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,eAAe;QACnB,oIAAA,CAAA,SAAM,CAAC,IAAI;QACX,oIAAA,CAAA,SAAM,CAAC,KAAK;QACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;QACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;QACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QACrB,oIAAA,CAAA,SAAM,CAAC,UAAU;KAClB;IAED,OAAO,CAAC,aAAa,QAAQ,CAAC;AAChC;AAKO,SAAS,yBAAyB,IAAY;IACnD,MAAM,gBAAgB;QACpB,oIAAA,CAAA,SAAM,CAAC,IAAI;QACX,oIAAA,CAAA,SAAM,CAAC,KAAK;QACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;QACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;QACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QACrB,oIAAA,CAAA,SAAM,CAAC,UAAU;KAClB;IAED,OAAO,cAAc,QAAQ,CAAC;AAChC;AAKO,SAAS;IACd,OAAO,oIAAA,CAAA,SAAM,CAAC,SAAS;AACzB;AAKO,SAAS,sBACd,eAAwB,EACxB,SAAkB,EAClB,WAAmB;IAEnB,OAAO,mBAAmB,aAAa,gBAAgB,oIAAA,CAAA,SAAM,CAAC,UAAU;AAC1E", "debugId": null}}, {"offset": {"line": 4771, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useAuth } from '@/providers/AuthContext';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { isProtectedRoute } from '@/lib/utils/onboarding';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requireAuth?: boolean;\n  redirectTo?: string;\n}\n\nexport default function ProtectedRoute({ \n  children, \n  requireAuth = true, \n  redirectTo = '/login' \n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading, isNewUser, signIn } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isCheckingAuth, setIsCheckingAuth] = useState(true);\n  const [hasTriedAutoAuth, setHasTriedAutoAuth] = useState(false);\n\n  useEffect(() => {\n    const checkAuthentication = async () => {\n      // Skip auth check for public routes\n      if (!requireAuth || !isProtectedRoute(pathname)) {\n        setIsCheckingAuth(false);\n        return;\n      }\n\n      // If already authenticated, handle onboarding redirect\n      if (isAuthenticated) {\n        if (isNewUser && pathname !== '/onboarding') {\n          console.log('New user on protected route, redirecting to onboarding');\n          router.push('/onboarding');\n          return;\n        }\n        if (!isNewUser && pathname === '/onboarding') {\n          console.log('Existing user on onboarding, redirecting to dashboard');\n          router.push('/dashboard');\n          return;\n        }\n        setIsCheckingAuth(false);\n        return;\n      }\n\n      // If not authenticated and haven't tried auto-auth yet, try it once\n      if (!isAuthenticated && !hasTriedAutoAuth && !isLoading) {\n        setHasTriedAutoAuth(true);\n        console.log('Attempting automatic authentication from stored tokens...');\n\n        try {\n          await signIn(undefined, false); // Don't redirect, just authenticate\n          // If successful, the useEffect will re-run due to isAuthenticated change\n        } catch (error) {\n          console.log('Auto-authentication failed, redirecting to login');\n          router.push(redirectTo);\n        }\n        return;\n      }\n\n      // If not authenticated and already tried auto-auth, redirect to login\n      if (!isAuthenticated && hasTriedAutoAuth && !isLoading) {\n        console.log('Not authenticated, redirecting to login');\n        router.push(redirectTo);\n        return;\n      }\n\n      setIsCheckingAuth(false);\n    };\n\n    checkAuthentication();\n  }, [isAuthenticated, isLoading, isNewUser, pathname, requireAuth, redirectTo, router, signIn, hasTriedAutoAuth]);\n\n  // Show loading state while checking authentication\n  if (isCheckingAuth || isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">Checking authentication...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // For protected routes, only render children if authenticated\n  if (requireAuth && !isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold mb-4\">Authentication Required</h2>\n          <p className=\"text-muted-foreground mb-4\">Please sign in to access this page.</p>\n          <button\n            onClick={() => router.push(redirectTo)}\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n          >\n            Sign In\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return <>{children}</>;\n}\n\n// Higher-order component for easy wrapping\nexport function withProtectedRoute<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: Omit<ProtectedRouteProps, 'children'>\n) {\n  return function ProtectedComponent(props: P) {\n    return (\n      <ProtectedRoute {...options}>\n        <Component {...props} />\n      </ProtectedRoute>\n    );\n  };\n}\n\n// Hook for manual authentication checks\nexport function useRequireAuth(redirectTo: string = '/login') {\n  const { isAuthenticated, isLoading, signIn } = useAuth();\n  const router = useRouter();\n  const [isChecking, setIsChecking] = useState(true);\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (isLoading) return;\n\n      if (!isAuthenticated) {\n        try {\n          await signIn(undefined, false); // Don't redirect, just authenticate\n        } catch {\n          router.push(redirectTo);\n        }\n      }\n      setIsChecking(false);\n    };\n\n    checkAuth();\n  }, [isAuthenticated, isLoading, signIn, router, redirectTo]);\n\n  return { isAuthenticated, isLoading: isLoading || isChecking };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAae,SAAS,eAAe,EACrC,QAAQ,EACR,cAAc,IAAI,EAClB,aAAa,QAAQ,EACD;;IACpB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAChE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;gEAAsB;oBAC1B,oCAAoC;oBACpC,IAAI,CAAC,eAAe,CAAC,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;wBAC/C,kBAAkB;wBAClB;oBACF;oBAEA,uDAAuD;oBACvD,IAAI,iBAAiB;wBACnB,IAAI,aAAa,aAAa,eAAe;4BAC3C,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;4BACZ;wBACF;wBACA,IAAI,CAAC,aAAa,aAAa,eAAe;4BAC5C,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;4BACZ;wBACF;wBACA,kBAAkB;wBAClB;oBACF;oBAEA,oEAAoE;oBACpE,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,WAAW;wBACvD,oBAAoB;wBACpB,QAAQ,GAAG,CAAC;wBAEZ,IAAI;4BACF,MAAM,OAAO,WAAW,QAAQ,oCAAoC;wBACpE,yEAAyE;wBAC3E,EAAE,OAAO,OAAO;4BACd,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;wBACd;wBACA;oBACF;oBAEA,sEAAsE;oBACtE,IAAI,CAAC,mBAAmB,oBAAoB,CAAC,WAAW;wBACtD,QAAQ,GAAG,CAAC;wBACZ,OAAO,IAAI,CAAC;wBACZ;oBACF;oBAEA,kBAAkB;gBACpB;;YAEA;QACF;mCAAG;QAAC;QAAiB;QAAW;QAAW;QAAU;QAAa;QAAY;QAAQ;QAAQ;KAAiB;IAE/G,mDAAmD;IACnD,IAAI,kBAAkB,WAAW;QAC/B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,8DAA8D;IAC9D,IAAI,eAAe,CAAC,iBAAiB;QACnC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBAAO;kBAAG;;AACZ;GA9FwB;;QAKoC,mIAAA,CAAA,UAAO;QAClD,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAPN;AAiGjB,SAAS,mBACd,SAAiC,EACjC,OAA+C;IAE/C,OAAO,SAAS,mBAAmB,KAAQ;QACzC,qBACE,6LAAC;YAAgB,GAAG,OAAO;sBACzB,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,aAAqB,QAAQ;;IAC1D,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACrD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;sDAAY;oBAChB,IAAI,WAAW;oBAEf,IAAI,CAAC,iBAAiB;wBACpB,IAAI;4BACF,MAAM,OAAO,WAAW,QAAQ,oCAAoC;wBACtE,EAAE,OAAM;4BACN,OAAO,IAAI,CAAC;wBACd;oBACF;oBACA,cAAc;gBAChB;;YAEA;QACF;mCAAG;QAAC;QAAiB;QAAW;QAAQ;QAAQ;KAAW;IAE3D,OAAO;QAAE;QAAiB,WAAW,aAAa;IAAW;AAC/D;IAvBgB;;QACiC,mIAAA,CAAA,UAAO;QACvC,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 5013, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/app/%28dashboard%29/datasources/page.tsx"], "sourcesContent": ["\"use client\";\nimport React from 'react';\nimport Layout from '@/components/layout/Layout';\nimport DataSourcesPageContent from '@/components/features/datasources/DataSourcesPageContent';\nimport ProtectedRoute from '@/components/auth/ProtectedRoute';\n\nconst DataSourcesPage = () => {\n  return (\n    <ProtectedRoute>\n      <Layout>\n        <DataSourcesPageContent />\n      </Layout>\n    </ProtectedRoute>\n  );\n};\n\nexport default DataSourcesPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,kBAAkB;IACtB,qBACE,6LAAC,+IAAA,CAAA,UAAc;kBACb,cAAA,6LAAC,yIAAA,CAAA,UAAM;sBACL,cAAA,6LAAC,0KAAA,CAAA,UAAsB;;;;;;;;;;;;;;;AAI/B;KARM;uCAUS", "debugId": null}}]}