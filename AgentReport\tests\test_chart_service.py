"""Unit Tests for Chart Selection Service

This module contains comprehensive unit tests for the chart selection service,
covering various query types, edge cases, error scenarios, and LLM response parsing.
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from app.models.chart import (
    ChartType, ChartQueryRequest, ChartQueryResponse, ChartData,
    ChartDataPoint, ChartMetadata, ChartGenerationContext,
    ChartTypeRecommendation, MockDataGenerationConfig
)
from app.services.chart_service import ChartSelectionService


class TestChartSelectionService:
    """Test suite for ChartSelectionService."""
    
    @pytest.fixture
    def chart_service(self):
        """Create a chart service instance for testing."""
        return ChartSelectionService()
    
    @pytest.fixture
    def sample_request(self):
        """Create a sample chart query request."""
        return ChartQueryRequest(
            prompt="Show user registrations by month",
            user_id="test_user_123"
        )
    
    @pytest.fixture
    def mock_bedrock_response(self):
        """Mock LLM response for chart type recommendation."""
        return json.dumps({
            "chart_type": "line",
            "confidence": 0.95,
            "reasoning": "Query indicates time-based trend analysis, line chart is most appropriate",
            "alternative_types": ["bar", "area"]
        })
    
    @pytest.mark.asyncio
    async def test_process_chart_query_success(self, chart_service, sample_request, mock_bedrock_response):
        """Test successful chart query processing."""
        with patch.object(chart_service.bedrock_client, 'generate_response', new_callable=AsyncMock) as mock_bedrock:
            # Mock LLM responses
            mock_bedrock.side_effect = [
                mock_bedrock_response,  # Chart type recommendation
                "User Registrations Over Time"  # Chart title
            ]
            
            result = await chart_service.process_chart_query(sample_request)
            
            assert isinstance(result, ChartQueryResponse)
            assert result.success is True
            assert result.data is not None
            assert result.error is None
            assert result.data.chartType == ChartType.LINE
            assert result.data.title == "User Registrations Over Time"
            assert len(result.data.data) > 0
            assert result.data.metadata is not None
    
    @pytest.mark.asyncio
    async def test_process_chart_query_llm_failure(self, chart_service, sample_request):
        """Test chart query processing when LLM fails."""
        with patch.object(chart_service.bedrock_client, 'generate_response', new_callable=AsyncMock) as mock_bedrock:
            # Mock LLM failure
            mock_bedrock.side_effect = Exception("LLM service unavailable")
            
            result = await chart_service.process_chart_query(sample_request)
            
            # Should still succeed using fallback logic
            assert isinstance(result, ChartQueryResponse)
            assert result.success is True
            assert result.data is not None
            assert result.data.chartType in [ChartType.BAR, ChartType.LINE, ChartType.PIE]
    
    @pytest.mark.asyncio
    async def test_get_chart_type_recommendation_success(self, chart_service, mock_bedrock_response):
        """Test successful chart type recommendation."""
        context = ChartGenerationContext(user_query="Show sales trends over time")
        
        with patch.object(chart_service.bedrock_client, 'generate_response', new_callable=AsyncMock) as mock_bedrock:
            mock_bedrock.return_value = mock_bedrock_response
            
            recommendation = await chart_service._get_chart_type_recommendation(context)
            
            assert isinstance(recommendation, ChartTypeRecommendation)
            assert recommendation.chart_type == ChartType.LINE
            assert recommendation.confidence == 0.95
            assert "time-based trend" in recommendation.reasoning
            assert ChartType.BAR in recommendation.alternative_types
    
    @pytest.mark.asyncio
    async def test_get_chart_type_recommendation_invalid_json(self, chart_service):
        """Test chart type recommendation with invalid JSON response."""
        context = ChartGenerationContext(user_query="Show data breakdown")
        
        with patch.object(chart_service.bedrock_client, 'generate_response', new_callable=AsyncMock) as mock_bedrock:
            mock_bedrock.return_value = "invalid json response"
            
            recommendation = await chart_service._get_chart_type_recommendation(context)
            
            # Should fall back to keyword analysis
            assert isinstance(recommendation, ChartTypeRecommendation)
            assert recommendation.chart_type == ChartType.PIE  # "breakdown" keyword
            assert recommendation.confidence == 0.7
    
    def test_fallback_chart_type_recommendation_pie(self, chart_service):
        """Test fallback recommendation for pie chart keywords."""
        context = ChartGenerationContext(user_query="Show revenue breakdown by department")

        recommendation = chart_service._fallback_chart_type_recommendation(context)

        assert recommendation.chart_type == ChartType.PIE
        assert "composition" in recommendation.reasoning.lower() or "percentage" in recommendation.reasoning.lower()
        assert recommendation.confidence == 0.7
    
    def test_fallback_chart_type_recommendation_line(self, chart_service):
        """Test fallback recommendation for line chart keywords."""
        context = ChartGenerationContext(user_query="Show user growth over time")
        
        recommendation = chart_service._fallback_chart_type_recommendation(context)
        
        assert recommendation.chart_type == ChartType.LINE
        assert "time-based trend" in recommendation.reasoning.lower()
    
    def test_fallback_chart_type_recommendation_bar(self, chart_service):
        """Test fallback recommendation for bar chart keywords."""
        context = ChartGenerationContext(user_query="Compare sales between regions")
        
        recommendation = chart_service._fallback_chart_type_recommendation(context)
        
        assert recommendation.chart_type == ChartType.BAR
        assert "comparison" in recommendation.reasoning.lower()
    
    def test_fallback_chart_type_recommendation_default(self, chart_service):
        """Test fallback recommendation for generic queries."""
        context = ChartGenerationContext(user_query="Show me some data")
        
        recommendation = chart_service._fallback_chart_type_recommendation(context)
        
        assert recommendation.chart_type == ChartType.BAR
        assert "default" in recommendation.reasoning.lower()
    
    @pytest.mark.asyncio
    async def test_generate_chart_title_success(self, chart_service):
        """Test successful chart title generation."""
        context = ChartGenerationContext(
            user_query="Show monthly sales data",
            recommended_chart_type=ChartType.LINE
        )
        
        with patch.object(chart_service.bedrock_client, 'generate_response', new_callable=AsyncMock) as mock_bedrock:
            mock_bedrock.return_value = "Monthly Sales Trends"
            
            title = await chart_service._generate_chart_title(context)
            
            assert title == "Monthly Sales Trends"
    
    @pytest.mark.asyncio
    async def test_generate_chart_title_fallback(self, chart_service):
        """Test chart title generation fallback."""
        context = ChartGenerationContext(user_query="Show sales data")
        
        with patch.object(chart_service.bedrock_client, 'generate_response', new_callable=AsyncMock) as mock_bedrock:
            mock_bedrock.side_effect = Exception("LLM unavailable")
            
            title = await chart_service._generate_chart_title(context)
            
            assert title == "Show Sales Data"  # Fallback to title case
    
    @pytest.mark.asyncio
    async def test_generate_pie_chart_data(self, chart_service):
        """Test pie chart data generation."""
        context = ChartGenerationContext(user_query="Show product distribution")
        
        data_points = chart_service._generate_pie_chart_data(context)
        
        assert len(data_points) > 0
        assert all(isinstance(point, ChartDataPoint) for point in data_points)
        assert all(point.value > 0 for point in data_points)
        assert all(point.category == "percentage" for point in data_points)
        
        # Check that percentages roughly add up to 100
        total = sum(point.value for point in data_points)
        assert 90 <= total <= 110  # Allow some variance due to randomness
    
    @pytest.mark.asyncio
    async def test_generate_line_chart_data(self, chart_service):
        """Test line chart data generation."""
        context = ChartGenerationContext(user_query="Show monthly trends")
        
        data_points = chart_service._generate_line_chart_data(context)
        
        assert len(data_points) > 0
        assert all(isinstance(point, ChartDataPoint) for point in data_points)
        assert all(point.value >= 0 for point in data_points)
        # Check that we have time-based labels
        assert any("Jan" in point.label or "Q" in point.label or "/" in point.label for point in data_points)
    
    @pytest.mark.asyncio
    async def test_generate_bar_chart_data(self, chart_service):
        """Test bar chart data generation."""
        context = ChartGenerationContext(user_query="Compare department performance")
        
        data_points = chart_service._generate_bar_chart_data(context)
        
        assert len(data_points) > 0
        assert all(isinstance(point, ChartDataPoint) for point in data_points)
        assert all(point.value > 0 for point in data_points)
    
    @pytest.mark.asyncio
    async def test_generate_funnel_chart_data(self, chart_service):
        """Test funnel chart data generation."""
        context = ChartGenerationContext(user_query="Show conversion funnel")
        
        data_points = chart_service._generate_funnel_chart_data(context)
        
        assert len(data_points) > 0
        assert all(isinstance(point, ChartDataPoint) for point in data_points)
        
        # Check that values decrease (funnel pattern)
        values = [point.value for point in data_points]
        for i in range(1, len(values)):
            assert values[i] <= values[i-1], "Funnel values should decrease"
    
    @pytest.mark.asyncio
    async def test_generate_number_data(self, chart_service):
        """Test number display data generation."""
        context = ChartGenerationContext(user_query="Show total users")
        
        data_points = chart_service._generate_number_data(context)
        
        assert len(data_points) == 1
        assert data_points[0].label == "Total"
        assert data_points[0].value > 0
    
    def test_get_relevant_categories_products(self, chart_service):
        """Test category generation for product-related queries."""
        categories = chart_service._get_relevant_categories("Show product sales")
        
        assert "Product" in categories[0]
        assert len(categories) == 4
    
    def test_get_relevant_categories_departments(self, chart_service):
        """Test category generation for department-related queries."""
        categories = chart_service._get_relevant_categories("Show team performance")
        
        expected_departments = ["Sales", "Marketing", "Engineering", "Support"]
        assert categories == expected_departments
    
    def test_get_time_labels_monthly(self, chart_service):
        """Test time label generation for monthly queries."""
        labels = chart_service._get_time_labels("Show monthly data")
        
        assert len(labels) == 6
        assert all(label in ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"] for label in labels)
    
    def test_get_time_labels_yearly(self, chart_service):
        """Test time label generation for yearly queries."""
        labels = chart_service._get_time_labels("Show yearly trends")
        
        assert len(labels) == 5
        assert all(label.isdigit() and len(label) == 4 for label in labels)
    
    def test_create_chart_metadata(self, chart_service):
        """Test chart metadata creation."""
        context = ChartGenerationContext(user_query="Show monthly revenue")
        recommendation = ChartTypeRecommendation(
            chart_type=ChartType.LINE,
            confidence=0.9,
            reasoning="Time-based analysis"
        )
        
        metadata = chart_service._create_chart_metadata(context, recommendation)
        
        assert isinstance(metadata, ChartMetadata)
        assert metadata.xAxisLabel == "Time Period"
        assert metadata.yAxisLabel == "Value"
        assert len(metadata.colors) == 6
        assert metadata.description == "Time-based analysis"
        assert metadata.dataSource == "Mock Data Generator"
    
    def test_generate_axis_label_time_x(self, chart_service):
        """Test X-axis label generation for time-based queries."""
        label = chart_service._generate_axis_label("Show monthly data", "x", ChartType.LINE)
        
        assert label == "Time Period"
    
    def test_generate_axis_label_value_y(self, chart_service):
        """Test Y-axis label generation for value-based queries."""
        label = chart_service._generate_axis_label("Show revenue data", "y", ChartType.BAR)
        
        assert label == "Value"
    
    def test_generate_axis_label_pie_chart(self, chart_service):
        """Test axis label generation for pie charts (should return None)."""
        label = chart_service._generate_axis_label("Show distribution", "x", ChartType.PIE)
        
        assert label is None
