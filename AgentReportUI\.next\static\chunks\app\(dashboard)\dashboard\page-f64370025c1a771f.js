(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[337],{1085:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>eo});var r=a(95155),s=a(12115),l=a(74677);let i={DEFAULT_WIDGET_SIZE:{w:4,h:5},MIN_WIDGET_SIZE:{w:3,h:4},MAX_WIDGET_SIZE:{w:12,h:15},MAX_WIDGETS:12,GRID_COLS:{lg:12,md:10,sm:6,xs:4,xxs:2},BREAKPOINTS:{lg:1200,md:996,sm:768,xs:480,xxs:0},ROW_HEIGHT:85},n=["#8b5cf6","#06b6d4","#10b981","#f59e0b","#ef4444","#8b5cf6","#06b6d4","#10b981","#f59e0b","#ef4444"];var o=a(58189),d=a(87914),c=a(10071),u=a(73783),h=a(66695),x=a(30285),m=a(72713),g=a(5623),b=a(13717),f=a(62525),p=a(69074),v=a(44838);let y=e=>{let{dashboard:t,chartCount:a,onSelect:s,onEdit:l,onDelete:i,className:n=""}=e;return(0,r.jsxs)(h.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow duration-200 ".concat(n),onClick:e=>{e.target.closest("[data-dropdown-trigger]")||s(t)},children:[(0,r.jsxs)(h.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 text-primary"}),(0,r.jsx)("h3",{className:"font-semibold text-lg truncate",children:t.name})]}),(0,r.jsxs)(v.rI,{children:[(0,r.jsx)(v.ty,{asChild:!0,"data-dropdown-trigger":!0,children:(0,r.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(v.SQ,{align:"end",children:[l&&(0,r.jsxs)(v._2,{onClick:()=>l(t),children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Edit Dashboard"]}),(0,r.jsxs)(v._2,{onClick:()=>i(t.id),className:"text-destructive focus:text-destructive",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Delete Dashboard"]})]})]})]}),(0,r.jsxs)(h.Wu,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-muted-foreground text-sm line-clamp-2",children:t.description||"No description provided"}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-muted-foreground",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[a," chart",1!==a?"s":""]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-muted-foreground",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:new Date(t.updated_at).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})})]})]})]})]})},j=e=>{let{dashboards:t,dashboardStats:a,onSelectDashboard:s,onDeleteDashboard:l,className:i=""}=e;return(0,r.jsx)("div",{className:"space-y-6 ".concat(i),children:(0,r.jsx)("div",{children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(e=>(0,r.jsx)(y,{dashboard:e,chartCount:a[e.id]||0,onSelect:s,onDelete:l},e.id))})})})};var w=a(22897),N=a(88539),D=a(51154),k=a(12486),C=a(54213),E=a(83540),S=a(3401),_=a(94754),I=a(96025),A=a(52071),T=a(5638),L=a(56690),W=a(93504),M=a(13279),G=a(99445),R=a(61667),z=a(90170),Z=a(18357),F=a(54811),O=a(51362);let B=(e,t)=>{if(!e||!t)return 10;let a=Math.min(e,t);return a<200?8:a<300?9:a<400?10:11},H=(e,t)=>e&&t?t<150?{xHeight:12,yWidth:15}:t<200?{xHeight:15,yWidth:18}:t<300?{xHeight:18,yWidth:22}:{xHeight:20,yWidth:25}:{xHeight:20,yWidth:25},K=(e,t,a)=>e&&t?"bar"===a&&t<150?{top:2,right:2,left:2,bottom:2}:t<200?{top:3,right:3,left:3,bottom:3}:{top:5,right:5,left:5,bottom:5}:{top:5,right:5,left:5,bottom:5},P=e=>{let{chartData:t,width:a,height:s,className:l=""}=e,{chartType:i,data:o,metadata:d}=t,{resolvedTheme:c}=(0,O.D)(),u="dark"===c,h=B(a,s),{xHeight:x,yWidth:m}=H(a,s),g=K(a,s,i),b={grid:u?"hsl(217.2 32.6% 17.5%)":"hsl(214.3 31.8% 91.4%)",text:u?"hsl(215 20.2% 65.1%)":"hsl(215.4 16.3% 46.9%)",tooltipBg:u?"hsl(222.2 84% 4.9%)":"hsl(0 0% 100%)",tooltipBorder:u?"hsl(217.2 32.6% 17.5%)":"hsl(214.3 31.8% 91.4%)",tooltipText:u?"hsl(210 40% 98%)":"hsl(222.2 84% 4.9%)",primary:u?"hsl(217.2 91.2% 59.8%)":"hsl(221.2 83.2% 53.3%)"},f=!s||s>120,p=!s||s>100,v=!s||s>100;return(0,r.jsx)("div",{className:"w-full h-full",children:(()=>{var e,t,l,c,u,y;switch(i){case"bar":return(0,r.jsx)(E.u,{width:"100%",height:"100%",children:(0,r.jsxs)(S.E,{data:o,margin:g,children:[f&&(0,r.jsx)(_.d,{strokeDasharray:"3 3",stroke:b.grid}),p&&(0,r.jsx)(I.W,{dataKey:"label",axisLine:!1,tickLine:!1,tick:{fill:b.text,fontSize:h},height:x,interval:a&&a<200?"preserveStartEnd":0}),v&&(0,r.jsx)(A.h,{axisLine:!1,tickLine:!1,tick:{fill:b.text,fontSize:h},width:m,tickCount:s&&s<150?3:5}),(0,r.jsx)(T.m,{contentStyle:{backgroundColor:b.tooltipBg,border:"1px solid ".concat(b.tooltipBorder),borderRadius:"8px",color:b.tooltipText,boxShadow:"0 4px 6px -1px rgb(0 0 0 / 0.1)"}}),(0,r.jsx)(L.y,{dataKey:"value",fill:(null==(e=d.colors)?void 0:e[0])||b.primary,radius:s&&s<150?[1,1,0,0]:[2,2,0,0]})]})});case"line":return(0,r.jsx)(E.u,{width:"100%",height:"100%",children:(0,r.jsxs)(W.b,{data:o,margin:g,children:[f&&(0,r.jsx)(_.d,{strokeDasharray:"3 3",stroke:b.grid}),p&&(0,r.jsx)(I.W,{dataKey:"label",axisLine:!1,tickLine:!1,tick:{fill:b.text,fontSize:h},height:x}),v&&(0,r.jsx)(A.h,{axisLine:!1,tickLine:!1,tick:{fill:b.text,fontSize:h},width:m}),(0,r.jsx)(T.m,{contentStyle:{backgroundColor:b.tooltipBg,border:"1px solid ".concat(b.tooltipBorder),borderRadius:"8px",color:b.tooltipText,boxShadow:"0 4px 6px -1px rgb(0 0 0 / 0.1)"}}),(0,r.jsx)(M.N,{type:"monotone",dataKey:"value",stroke:(null==(t=d.colors)?void 0:t[0])||b.primary,strokeWidth:2,dot:{fill:(null==(l=d.colors)?void 0:l[0])||b.primary,strokeWidth:0,r:3},activeDot:{r:5,fill:(null==(c=d.colors)?void 0:c[0])||b.primary}})]})});case"area":return(0,r.jsx)(E.u,{width:"100%",height:"100%",children:(0,r.jsxs)(G.Q,{data:o,margin:g,children:[f&&(0,r.jsx)(_.d,{strokeDasharray:"3 3",stroke:b.grid}),p&&(0,r.jsx)(I.W,{dataKey:"label",axisLine:!1,tickLine:!1,tick:{fill:b.text,fontSize:h},height:x}),v&&(0,r.jsx)(A.h,{axisLine:!1,tickLine:!1,tick:{fill:b.text,fontSize:h},width:m}),(0,r.jsx)(T.m,{contentStyle:{backgroundColor:b.tooltipBg,border:"1px solid ".concat(b.tooltipBorder),borderRadius:"8px",color:b.tooltipText,boxShadow:"0 4px 6px -1px rgb(0 0 0 / 0.1)"}}),(0,r.jsx)(R.Gk,{type:"monotone",dataKey:"value",stroke:(null==(u=d.colors)?void 0:u[0])||b.primary,fill:(null==(y=d.colors)?void 0:y[0])||b.primary,fillOpacity:.3,strokeWidth:2})]})});case"pie":return(0,r.jsx)(E.u,{width:"100%",height:"100%",children:(0,r.jsxs)(z.r,{margin:{top:0,right:0,left:0,bottom:0},children:[(0,r.jsx)(Z.F,{data:o,cx:"50%",cy:"50%",labelLine:!1,label:(!a||!(a<250))&&(e=>{let{label:t,percent:a}=e;return"".concat(t," ").concat(a?(100*a).toFixed(0):0,"%")}),outerRadius:"90%",innerRadius:"35%",fill:b.primary,dataKey:"value",stroke:b.grid,strokeWidth:1,children:o.map((e,t)=>{var a;return(0,r.jsx)(F.f,{fill:(null==(a=d.colors)?void 0:a[t])||n[t%n.length]},"cell-".concat(t))})}),(0,r.jsx)(T.m,{contentStyle:{backgroundColor:b.tooltipBg,border:"1px solid ".concat(b.tooltipBorder),borderRadius:"8px",color:b.tooltipText,boxShadow:"0 4px 6px -1px rgb(0 0 0 / 0.1)"}})]})});default:return(0,r.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:(0,r.jsxs)("p",{children:["Unsupported chart type: ",i]})})}})()})},U=e=>{let t=(0,s.useRef)(null),[a,l]=(0,s.useState)(null);return(0,s.useEffect)(()=>{let e=()=>{if(t.current){let{width:e,height:a}=t.current.getBoundingClientRect();l({width:e,height:a})}};e();let a=new ResizeObserver(()=>{requestAnimationFrame(e)});return t.current&&a.observe(t.current),window.addEventListener("resize",e),()=>{a.disconnect(),window.removeEventListener("resize",e)}},[]),(0,s.useEffect)(()=>{t.current&&setTimeout(()=>{if(t.current){let{width:e,height:a}=t.current.getBoundingClientRect();l({width:e,height:a})}},50)},[e.chartData]),(0,r.jsx)("div",{ref:t,className:"w-full h-full ".concat(e.className||""),children:a&&(0,r.jsx)(P,{...e,width:a.width,height:a.height,className:""})})};var V=a(62523),X=a(59409),$=a(84783);let q=e=>{let{onSubmit:t,isLoading:a=!1,className:l="",placeholder:i="Describe your chart..."}=e,[n,o]=(0,s.useState)(""),[d,c]=(0,s.useState)(""),u=(0,s.useRef)(null),{connectedDataSources:h,isLoading:m}=(0,$.G)(),g=(0,s.useCallback)(async e=>{if((e.preventDefault(),n.trim()&&!a)&&(!(h.length>0)||d))try{await t(n.trim(),d||void 0),o("")}catch(e){console.error("Failed to create chart:",e)}},[n,d,t,a,h.length]),b=(0,s.useCallback)(e=>{"Enter"===e.key&&(e.preventDefault(),g(e))},[g]);(0,s.useEffect)(()=>{u.current&&u.current.focus(),h.length>0&&!d&&c(h[0].id)},[h,d]);let f=n.trim()&&!a&&(0===h.length||d),p=h.length>1;return(0,r.jsx)("div",{className:"w-full h-full flex flex-col justify-end p-3 pb-2 ".concat(l),children:(0,r.jsxs)("div",{className:"w-full max-w-lg mx-auto space-y-3",children:[h.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[p&&(0,r.jsxs)("div",{className:"flex items-center gap-2 text-xs",style:{color:"var(--sidebar-text-secondary)"},children:[(0,r.jsx)(C.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"Select Database:"})]}),(0,r.jsxs)(X.l6,{value:d,onValueChange:c,disabled:a||m,children:[(0,r.jsx)(X.bq,{className:"h-8 text-xs border react-grid-no-drag",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)",color:"var(--sidebar-text-primary)"},onMouseDown:e=>e.stopPropagation(),children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(C.A,{className:"h-3 w-3",style:{color:"var(--sidebar-text-secondary)"}}),(0,r.jsx)(X.yv,{placeholder:"Choose database..."})]})}),(0,r.jsx)(X.gC,{children:h.map(e=>(0,r.jsx)(X.eb,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(C.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:e.name})]})},e.id))})]})]}),(0,r.jsx)("form",{onSubmit:g,children:(0,r.jsxs)("div",{className:"relative rounded border react-grid-no-drag",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)"},onMouseDown:e=>e.stopPropagation(),children:[(0,r.jsx)(V.p,{ref:u,value:n,onChange:e=>o(e.target.value),onKeyDown:b,disabled:a,placeholder:i,className:"border-0 bg-transparent text-xs leading-tight placeholder:text-gray-500 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 h-7 pr-8",style:{color:"var(--sidebar-text-primary)"},maxLength:1e3}),(0,r.jsx)("div",{className:"absolute right-1 top-1/2 transform -translate-y-1/2",children:(0,r.jsx)(x.$,{type:"submit",disabled:!f,size:"sm",className:"h-5 w-5 p-0 rounded border-0 transition-all duration-200 ".concat(f?"hover:scale-105":"opacity-40"),style:{backgroundColor:f?"rgba(59, 130, 246, 1)":"var(--sidebar-surface-tertiary)",color:f?"white":"var(--sidebar-text-tertiary)"},onMouseEnter:e=>{f&&(e.currentTarget.style.backgroundColor="rgba(59, 130, 246, 0.9)")},onMouseLeave:e=>{f&&(e.currentTarget.style.backgroundColor="rgba(59, 130, 246, 1)")},children:(0,r.jsx)(k.A,{className:"h-2.5 w-2.5"})})})]})}),a&&(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-1 py-1 mt-1",children:[(0,r.jsx)("div",{className:"h-0.5 w-0.5 rounded-full animate-pulse",style:{backgroundColor:"rgba(59, 130, 246, 0.8)"}}),(0,r.jsx)("div",{className:"h-0.5 w-0.5 rounded-full animate-pulse",style:{backgroundColor:"rgba(59, 130, 246, 0.8)",animationDelay:"0.2s"}}),(0,r.jsx)("div",{className:"h-0.5 w-0.5 rounded-full animate-pulse",style:{backgroundColor:"rgba(59, 130, 246, 0.8)",animationDelay:"0.4s"}})]}),0===h.length&&!m&&(0,r.jsx)("div",{className:"text-center py-2",children:(0,r.jsx)("p",{className:"text-xs",style:{color:"var(--sidebar-text-secondary)"},children:"No databases connected. Charts will use mock data."})})]})})},Q=e=>{let{widget:t,onDelete:a,onUpdate:l,className:i=""}=e,{queryChart:n}=(0,o.g)(),[d,c]=(0,s.useState)(!1),[u,m]=(0,s.useState)(""),[b,f]=(0,s.useState)(!1),p=(0,s.useRef)(null),v=(0,s.useCallback)(async(e,a)=>{l(t.id,{isLoading:!0,error:null,title:"Generating..."});try{let r={prompt:e};a&&(r.database_id=a);let s=await n(r);if(s.success&&s.data)l(t.id,{chartData:s.data,isLoading:!1,error:null,title:s.data.title});else throw Error(s.error||"Failed to generate chart")}catch(a){let e=a instanceof Error?a.message:"Failed to create chart";l(t.id,{isLoading:!1,error:e,title:"Generation Failed"})}},[t.id,n,l]),y=(0,s.useCallback)(async e=>{e.preventDefault(),u.trim()&&(await v(u.trim()),c(!1),m(""))},[u,v]),j=(0,s.useCallback)(e=>{"Enter"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),y(e)),"Escape"===e.key&&(c(!1),m(""))},[y]);(0,s.useEffect)(()=>{d&&p.current&&p.current.focus()},[d]);let w=t.isLoading,E=t.error,S=!!t.chartData,_=u.trim()&&!w;return(0,r.jsxs)(h.Zp,{className:"h-full overflow-hidden chart-card ".concat(i),style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)"},children:[(0,r.jsxs)(h.aR,{className:"flex flex-row items-center justify-between p-2 pb-1 react-grid-no-drag",onMouseDown:e=>e.stopPropagation(),children:[(0,r.jsx)("div",{}),(0,r.jsx)("div",{className:"flex items-center gap-1",children:S&&!w&&(0,r.jsx)(x.$,{variant:"ghost",size:"sm",onClick:()=>c(!0),className:"h-6 w-6 p-0",children:(0,r.jsx)(g.A,{className:"h-3 w-3"})})})]}),(0,r.jsxs)(h.Wu,{className:"flex-1 h-full p-2",children:[w&&(0,r.jsx)("div",{className:"h-full flex flex-col items-center justify-center space-y-4",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"text-lg font-medium text-foreground",children:"Generating"}),(0,r.jsx)(D.A,{className:"h-8 w-8 animate-spin text-primary mx-auto"})]})}),d&&(0,r.jsx)("div",{className:"h-full flex flex-col items-center justify-center p-6 react-grid-no-drag",onMouseDown:e=>e.stopPropagation(),children:(0,r.jsxs)("div",{className:"w-full max-w-md space-y-4",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h3",{className:"text-base font-medium",style:{color:"var(--sidebar-text-primary)"},children:"Modify your chart"}),(0,r.jsx)("p",{className:"text-sm",style:{color:"var(--sidebar-text-secondary)"},children:"Describe changes you want to make"})]}),(0,r.jsxs)("form",{onSubmit:y,className:"space-y-3",children:[(0,r.jsxs)("div",{className:"relative rounded-xl border transition-all duration-200 ".concat(b?"ring-2 ring-blue-500 ring-opacity-20":""),style:{backgroundColor:"var(--sidebar-bg)",borderColor:b?"rgba(59, 130, 246, 0.5)":"var(--sidebar-border)"},children:[(0,r.jsx)(N.T,{ref:p,value:u,onChange:e=>{m(e.target.value),p.current&&(p.current.style.height="auto",p.current.style.height="".concat(p.current.scrollHeight,"px"))},onKeyDown:j,onFocus:()=>f(!0),onBlur:()=>f(!1),placeholder:"Change the chart type to bar chart...",className:"min-h-[80px] max-h-[120px] resize-none border-0 bg-transparent text-sm leading-relaxed placeholder:text-gray-500 focus:ring-0 focus:outline-none p-3 pr-12",style:{color:"var(--sidebar-text-primary)"},maxLength:500}),(0,r.jsx)("div",{className:"absolute bottom-2 right-2",children:(0,r.jsx)(x.$,{type:"submit",disabled:!_,size:"sm",className:"h-7 w-7 p-0 rounded-lg border-0 transition-all duration-200 ".concat(_?"hover:scale-105":"opacity-50"),style:{backgroundColor:_?"rgba(59, 130, 246, 1)":"var(--sidebar-surface-tertiary)",color:_?"white":"var(--sidebar-text-tertiary)"},children:(0,r.jsx)(k.A,{className:"h-3 w-3"})})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex gap-2",children:(0,r.jsx)(x.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>{c(!1),m("")},className:"h-8 px-3 text-xs rounded-lg transition-all duration-200",style:{color:"var(--sidebar-text-secondary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:"Cancel"})}),(0,r.jsx)("span",{className:"text-xs",style:{color:"var(--sidebar-text-tertiary)"},children:"⌘+Enter to send"})]})]})]})}),S&&!w&&!E&&!d&&t.chartData&&(0,r.jsx)("div",{className:"h-full",children:(0,r.jsx)(U,{chartData:t.chartData,className:"h-full"},"".concat(t.id,"-").concat(t.layout.w,"-").concat(t.layout.h))}),!S&&!w&&!E&&!d&&(0,r.jsx)("div",{className:"h-full flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-4",children:[(0,r.jsx)(C.A,{className:"h-8 w-8 mx-auto text-gray-400 mb-2"}),(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-1",children:"Create Chart with Real Data"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Type your question to generate a chart from your database"})]}),(0,r.jsx)(q,{onSubmit:v,isLoading:w,placeholder:"e.g., Show me sales by region this month"})]})}),E&&!w&&!d&&(0,r.jsx)("div",{className:"h-full flex flex-col items-center justify-center space-y-4",children:(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("div",{className:"text-destructive font-medium",children:"Generation Failed"}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:E}),(0,r.jsx)(x.$,{onClick:()=>l(t.id,{error:null}),variant:"outline",children:"Try Again"})]})})]})]})};var Y=a(5196),J=a(54416),ee=a(56287),et=a(46486);let ea=e=>{let{value:t,onSave:a,placeholder:l="Click to edit...",multiline:i=!1,className:n="",editClassName:o="",displayClassName:d="",maxLength:c,disabled:u=!1,showEditIcon:h=!0,hideButtons:m=!1}=e,[g,b]=(0,s.useState)(!1),[f,p]=(0,s.useState)(t),[v,y]=(0,s.useState)(!1),j=(0,s.useRef)(null),w=(0,s.useRef)(null);(0,s.useEffect)(()=>{p(t)},[t]),(0,s.useEffect)(()=>{g&&j.current&&(j.current.focus(),(j.current instanceof HTMLInputElement||j.current instanceof HTMLTextAreaElement)&&j.current.select())},[g]),(0,s.useEffect)(()=>{if(!m||!g)return;let e=e=>{w.current&&!w.current.contains(e.target)&&D()};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[g,m,f,t]);let D=async()=>{if(f.trim()===t.trim())return void b(!1);y(!0);try{await a(f.trim()),b(!1)}catch(e){console.error("Failed to save:",e),p(t)}finally{y(!1)}},k=()=>{p(t),b(!1)};if(g){let e=i?N.T:V.p;return(0,r.jsxs)("div",{ref:w,className:(0,et.cn)("flex items-start gap-2",n),children:[(0,r.jsx)("div",{className:"max-w-md",children:(0,r.jsx)(e,{ref:j,value:f,onChange:e=>p(e.target.value),onKeyDown:e=>{"Enter"!==e.key||i?"Escape"===e.key?k():"Enter"===e.key&&i&&(e.ctrlKey||e.metaKey)&&(e.preventDefault(),D()):(e.preventDefault(),D())},placeholder:l,maxLength:c,disabled:v,className:(0,et.cn)("min-w-0",o),rows:i?3:void 0,style:{width:"".concat(Math.max(f.length+2,10),"ch")}})}),!m&&(0,r.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,r.jsx)(x.$,{size:"sm",variant:"ghost",onClick:D,disabled:v||!f.trim(),className:"h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50",children:(0,r.jsx)(Y.A,{className:"h-4 w-4"})}),(0,r.jsx)(x.$,{size:"sm",variant:"ghost",onClick:k,disabled:v,className:"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,r.jsx)(J.A,{className:"h-4 w-4"})})]})]})}return(0,r.jsx)("div",{className:(0,et.cn)("group cursor-pointer rounded px-2 py-1 -mx-2 -my-1 hover:bg-muted/50 transition-colors",u&&"cursor-not-allowed opacity-50",n),onClick:()=>{u||(b(!0),p(t))},children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:(0,et.cn)("flex-1",!t&&"text-muted-foreground italic",d),children:t||l}),h&&!u&&(0,r.jsx)(ee.A,{className:"h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity"})]})})},er=e=>{let{isVisible:t,isHovered:a}=e;return t?(0,r.jsxs)("div",{className:(0,et.cn)("fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50","transition-all duration-300 ease-in-out",t?"opacity-100 scale-100":"opacity-0 scale-95"),children:[(0,r.jsx)("div",{className:(0,et.cn)("flex items-center justify-center","w-20 h-20 rounded-full","border-2 border-dashed","transition-all duration-200 ease-in-out","shadow-lg backdrop-blur-sm","drag-delete-zone",a&&"hovered",a?"bg-destructive/20 border-destructive text-destructive scale-110":"bg-muted/80 border-muted-foreground/50 text-muted-foreground hover:bg-muted/90"),children:(0,r.jsx)(f.A,{className:(0,et.cn)("transition-all duration-200",a?"w-8 h-8":"w-6 h-6")})}),(0,r.jsx)("div",{className:"absolute top-full mt-2 left-1/2 transform -translate-x-1/2 whitespace-nowrap",children:(0,r.jsx)("div",{className:(0,et.cn)("text-xs font-medium px-2 py-1 rounded","bg-background/90 border shadow-sm",a?"text-destructive border-destructive/20":"text-muted-foreground border-border"),children:a?"Release to delete":"Drop here to delete"})})]}):null};a(39195),a(47127);let es=(0,w.WidthProvider)(w.Responsive),el=e=>{let{dashboard:t,widgets:a,onCreateChart:l,onDeleteWidget:n,onUpdateWidget:o,onLayoutChange:d,onUpdateDashboard:c,className:u=""}=e,[h,x]=(0,s.useState)({isDragging:!1,draggedWidgetId:null,isOverDeleteZone:!1}),m=(0,s.useMemo)(()=>{let e=a.map(e=>({i:e.id,x:e.layout.x,y:e.layout.y,w:e.layout.w,h:e.layout.h,minW:i.MIN_WIDGET_SIZE.w,minH:i.MIN_WIDGET_SIZE.h,maxW:i.MAX_WIDGET_SIZE.w,maxH:i.MAX_WIDGET_SIZE.h}));return{lg:e,md:e,sm:e.map(e=>({...e,w:Math.min(e.w,6),maxW:6})),xs:e.map(e=>({...e,w:Math.min(e.w,4),maxW:4})),xxs:e.map(e=>({...e,w:2,maxW:2}))}},[a]),g=(0,s.useCallback)((e,t,a,r,s,l)=>{console.log("Drag started for widget:",a.i),x({isDragging:!0,draggedWidgetId:a.i,isOverDeleteZone:!1})},[]),b=(0,s.useCallback)(e=>{if(!h.isDragging)return;let t=document.querySelector(".drag-delete-zone");if(!t)return;let a=t.getBoundingClientRect(),r=e.clientX>=a.left&&e.clientX<=a.right&&e.clientY>=a.top&&e.clientY<=a.bottom;r!==h.isOverDeleteZone&&(x(e=>({...e,isOverDeleteZone:r})),console.log("Delete zone hover:",r))},[h.isDragging,h.isOverDeleteZone]),f=(0,s.useCallback)((e,t,a,r,s,l)=>{let i=h.isOverDeleteZone,o=h.draggedWidgetId;if(console.log("Drag stopped - Over delete zone:",i,"Widget ID:",o),x({isDragging:!1,draggedWidgetId:null,isOverDeleteZone:!1}),i&&o){console.log("Deleting widget:",o),n(o);return}},[h.isOverDeleteZone,h.draggedWidgetId,n]);return s.useEffect(()=>{if(h.isDragging)return document.addEventListener("mousemove",b),()=>document.removeEventListener("mousemove",b)},[h.isDragging,b]),a.length,i.MAX_WIDGETS,(0,r.jsxs)("div",{className:"space-y-6 ".concat(u),children:[(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(ea,{value:t.name,onSave:e=>c(t.id,{name:e}),placeholder:"Dashboard name...",displayClassName:"text-2xl font-bold text-foreground",editClassName:"text-3xl font-bold",maxLength:100,hideButtons:!0})})}),a.length>0&&(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(es,{className:"layout",layouts:m,breakpoints:i.BREAKPOINTS,cols:i.GRID_COLS,rowHeight:i.ROW_HEIGHT,onLayoutChange:d,onDragStart:g,onDragStop:f,isDraggable:!0,isResizable:!0,margin:[4,4],containerPadding:[0,0],children:a.map(e=>(0,r.jsx)("div",{className:"grid-item",children:(0,r.jsx)(Q,{widget:e,onDelete:n,onUpdate:o,className:"h-full"})},e.id))})}),(0,r.jsx)(er,{isVisible:h.isDragging,isHovered:h.isOverDeleteZone})]})},ei=()=>{let[e,t]=(0,s.useState)({currentView:"list",selectedDashboard:null,breadcrumbs:[{label:"Dashboards"}]}),[a,l]=(0,s.useState)([]),[n,h]=(0,s.useState)({}),[x,m]=(0,s.useState)(!1),[g,b]=(0,s.useState)([]),[f,p]=(0,s.useState)(1),[v,y]=(0,s.useState)(!1),{listDashboards:w,createDashboard:N,updateDashboard:D,deleteDashboard:k,queryChart:C}=(0,o.g)(),{setPageActions:E}=(0,c.s)();(0,s.useEffect)(()=>{S()},[]);let S=(0,s.useCallback)(async()=>{m(!0);try{let e=await w();if(e.success){l(e.data);let t={};e.data.forEach(e=>{t[e.id]=Math.floor(8*Math.random())}),h(t)}}catch(e){console.error("Failed to load dashboards:",e)}finally{m(!1)}},[w]),_=(0,s.useCallback)(e=>{t({currentView:"dashboard",selectedDashboard:e,breadcrumbs:[{label:"Dashboards",onClick:()=>I()},{label:e.name}]}),b([]),p(1)},[]),I=(0,s.useCallback)(()=>{t({currentView:"list",selectedDashboard:null,breadcrumbs:[{label:"Dashboards"}]}),b([])},[]),A=(0,s.useMemo)(()=>({title:"dashboard"===e.currentView&&e.selectedDashboard?e.selectedDashboard.name:"Dashboard",icon:u.A,breadcrumbs:"dashboard"===e.currentView&&e.selectedDashboard?[{label:"Dashboard",onClick:()=>I()},{label:e.selectedDashboard.name}]:[{label:"Dashboard"}]}),[e.currentView,e.selectedDashboard,I]);(0,d.H)(A);let T=(0,s.useCallback)(async()=>{y(!0);try{let e="Dashboard ".concat(a.length+1),t=await N({name:e,description:""});if(t.success){let e=t.data;l(t=>[...t,e]),h(t=>({...t,[e.id]:0})),_(e)}}catch(e){console.error("Failed to create dashboard:",e)}finally{y(!1)}},[a.length,N,_]),L=(0,s.useCallback)(async(a,r)=>{try{let i=await D(a,r);if(i.success){var s;l(e=>e.map(e=>e.id===a?i.data:e)),(null==(s=e.selectedDashboard)?void 0:s.id)===a&&t(e=>({...e,selectedDashboard:i.data,breadcrumbs:[{label:"Dashboards",onClick:()=>I()},{label:i.data.name}]}))}}catch(e){console.error("Failed to update dashboard:",e)}},[D,e.selectedDashboard,I]),W=(0,s.useCallback)(async t=>{if(confirm("Are you sure you want to delete this dashboard? This action cannot be undone."))try{if((await k(t)).success){var a;l(e=>e.filter(e=>e.id!==t)),h(e=>{let a={...e};return delete a[t],a}),(null==(a=e.selectedDashboard)?void 0:a.id)===t&&I()}}catch(e){console.error("Failed to delete dashboard:",e)}},[k,e.selectedDashboard,I]),M=(0,s.useCallback)(()=>{var t;if(g.length>=i.MAX_WIDGETS)return;let a=0,r=0,s=Math.floor(12/i.DEFAULT_WIDGET_SIZE.w),l=Math.floor(g.length/s);a=g.length%s*i.DEFAULT_WIDGET_SIZE.w,r=l*i.DEFAULT_WIDGET_SIZE.h;let n={id:"widget-".concat(f),title:"",chartData:null,isLoading:!1,error:null,dashboard_id:null==(t=e.selectedDashboard)?void 0:t.id,layout:{x:a,y:r,w:i.DEFAULT_WIDGET_SIZE.w,h:i.DEFAULT_WIDGET_SIZE.h}};b(e=>[...e,n]),p(e=>e+1)},[g,f,e.selectedDashboard]),G=(0,s.useCallback)(e=>{b(t=>t.filter(t=>t.id!==e))},[]),R=(0,s.useCallback)((e,t)=>{b(a=>a.map(a=>a.id===e?{...a,...t}:a))},[]),z=(0,s.useCallback)(e=>{b(t=>t.map(t=>{let a=e.find(e=>e.i===t.id);if(a){let e=Math.max(i.MIN_WIDGET_SIZE.w,Math.min(a.w,i.MAX_WIDGET_SIZE.w)),r=Math.max(i.MIN_WIDGET_SIZE.h,Math.min(a.h,i.MAX_WIDGET_SIZE.h));return{...t,layout:{...t.layout,x:a.x,y:a.y,w:e,h:r}}}return t}))},[]);return(0,s.useEffect)(()=>{E({onCreateDashboard:"list"===e.currentView?T:void 0,onCreateChart:"dashboard"===e.currentView&&e.selectedDashboard?M:void 0,chartCount:g.length,maxCharts:i.MAX_WIDGETS})},[e.currentView,e.selectedDashboard,g.length,E,T,M]),(0,r.jsx)("div",{className:"container mx-auto p-6 space-y-6",children:"list"===e.currentView?(0,r.jsx)(j,{dashboards:a,dashboardStats:n,onSelectDashboard:_,onDeleteDashboard:W}):e.selectedDashboard?(0,r.jsx)(el,{dashboard:e.selectedDashboard,widgets:g,onCreateChart:M,onDeleteWidget:G,onUpdateWidget:R,onLayoutChange:z,onUpdateDashboard:L}):null})};var en=a(30095);function eo(){return(0,r.jsx)(en.Ay,{children:(0,r.jsx)(l.A,{children:(0,r.jsx)(ei,{})})})}},30095:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>o});var r=a(95155),s=a(12115),l=a(74045),i=a(35695),n=a(45786);function o(e){let{children:t,requireAuth:a=!0,redirectTo:o="/login"}=e,{isAuthenticated:d,isLoading:c,isNewUser:u,signIn:h}=(0,l.A)(),x=(0,i.useRouter)(),m=(0,i.usePathname)(),[g,b]=(0,s.useState)(!0),[f,p]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{(async()=>{if(!a||[n.bw.HOME,n.bw.LOGIN,n.bw.REGISTER,n.bw.AUTH.CALLBACK,n.bw.OAUTH.CALLBACK,n.bw.ONBOARDING].includes(m))return b(!1);if(d){if(u&&"/onboarding"!==m){console.log("New user on protected route, redirecting to onboarding"),x.push("/onboarding");return}if(!u&&"/onboarding"===m){console.log("Existing user on onboarding, redirecting to dashboard"),x.push("/dashboard");return}b(!1);return}if(!d&&!f&&!c){p(!0),console.log("Attempting automatic authentication from stored tokens...");try{await h(void 0,!1)}catch(e){console.log("Auto-authentication failed, redirecting to login"),x.push(o)}return}if(!d&&f&&!c){console.log("Not authenticated, redirecting to login"),x.push(o);return}b(!1)})()},[d,c,u,m,a,o,x,h,f]),g||c)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Checking authentication..."})]})}):a&&!d?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please sign in to access this page."}),(0,r.jsx)("button",{onClick:()=>x.push(o),className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Sign In"})]})}):(0,r.jsx)(r.Fragment,{children:t})}},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>x,gC:()=>h,l6:()=>d,yv:()=>c});var r=a(95155);a(12115);var s=a(14582),l=a(66474),i=a(5196),n=a(47863),o=a(46486);function d(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:i,...n}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[i,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function h(e){let{className:t,children:a,position:l="popper",...i}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...i,children:[(0,r.jsx)(m,{}),(0,r.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(g,{})]})})}function x(e){let{className:t,children:a,...l}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.A,{className:"size-4"})})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(l.A,{className:"size-4"})})}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var r=a(95155);a(12115);var s=a(46486);function l(e){let{className:t,type:a,...l}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...l})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>n,Zp:()=>l,aR:()=>i});var r=a(95155);a(12115);var s=a(46486);function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold text-white",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}},69262:(e,t,a)=>{Promise.resolve().then(a.bind(a,1085))},84783:(e,t,a)=>{"use strict";a.d(t,{G:()=>n,M:()=>o});var r=a(95155),s=a(12115),l=a(58189);let i=(0,s.createContext)(void 0),n=()=>{let e=(0,s.useContext)(i);if(!e)throw Error("useDataSources must be used within a DataSourcesProvider");return e},o=e=>{let{children:t}=e,{listDatabases:a,disconnectExistingDatabase:n}=(0,l.g)(),[o,d]=(0,s.useState)([]),[c,u]=(0,s.useState)(!1),[h,x]=(0,s.useState)(null),[m,g]=(0,s.useState)(!1),b=(0,s.useCallback)(e=>({id:e.id,name:e.name,type:"database",description:e.description||"".concat(e.type," database"),isConnected:!0}),[]),f=(0,s.useCallback)(async()=>{u(!0),x(null);try{console.log("Fetching connected databases...");let e=(await a()).map(b);d(e),g(!0)}catch(e){console.error("Failed to fetch connected databases:",e),x(e instanceof Error?e.message:"Failed to fetch connected databases"),d([]),g(!0)}finally{u(!1)}},[a,b]);(0,s.useEffect)(()=>{m||f()},[f,m]);let p=o.filter(e=>e.isConnected),v=(0,s.useCallback)(async e=>{console.log("Connect data source not implemented - use the Data Sources page"),x("Please use the Data Sources page to connect new databases")},[]),y=(0,s.useCallback)(async e=>{u(!0),x(null);try{await n(e),await f()}catch(e){x(e instanceof Error?e.message:"Failed to disconnect data source"),u(!1)}},[n,f]),j=(0,s.useCallback)(e=>o.find(t=>t.id===e),[o]),w=(0,s.useCallback)(async()=>{await f()},[f]);return(0,r.jsx)(i.Provider,{value:{dataSources:o,connectedDataSources:p,isLoading:c,error:h,connectDataSource:v,disconnectDataSource:y,getDataSource:j,refreshDataSources:w},children:t})}},88539:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var r=a(95155),s=a(12115),l=a(46486);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...s})});i.displayName="Textarea"}},e=>{var t=t=>e(e.s=t);e.O(0,[649,464,817,874,786,826,913,326,189,45,179,30,441,684,358],()=>t(69262)),_N_E=e.O()}]);