{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/cn.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/date.ts"], "sourcesContent": ["// Date utility functions\n\nexport const formatDate = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n};\n\nexport const formatDateTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n};\n\nexport const formatRelativeTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n\n  return formatDate(d);\n};\n\nexport const isTokenExpired = (expiresAt: string): boolean => {\n  const expirationTime = new Date(expiresAt).getTime();\n  const currentTime = new Date().getTime();\n  const bufferTime = 5 * 60 * 1000; // 5 minutes buffer\n  \n  return currentTime >= (expirationTime - bufferTime);\n};\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;AAElB,MAAM,aAAa,CAAC;IACzB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,cAAc,CAAC,SAAS;QAC/B,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;IACrE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;IAC5D;IAEA,OAAO,WAAW;AACpB;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,iBAAiB,IAAI,KAAK,WAAW,OAAO;IAClD,MAAM,cAAc,IAAI,OAAO,OAAO;IACtC,MAAM,aAAa,IAAI,KAAK,MAAM,mBAAmB;IAErD,OAAO,eAAgB,iBAAiB;AAC1C", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/format.ts"], "sourcesContent": ["// Formatting utility functions\n\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\nexport const formatNumber = (num: number): string => {\n  return new Intl.NumberFormat('en-US').format(num);\n};\n\nexport const formatCurrency = (amount: number, currency = 'USD'): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n};\n\nexport const truncateText = (text: string, maxLength: number): string => {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n};\n\nexport const capitalizeFirst = (str: string): string => {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n};\n\nexport const camelToTitle = (str: string): string => {\n  return str\n    .replace(/([A-Z])/g, ' $1')\n    .replace(/^./, (str) => str.toUpperCase())\n    .trim();\n};\n\nexport const generateSessionId = (): string => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;AAExB,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,MAAM,iBAAiB,CAAC,QAAgB,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,eAAe,CAAC,MAAc;IACzC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,YAAY,OACpB,OAAO,CAAC,MAAM,CAAC,MAAQ,IAAI,WAAW,IACtC,IAAI;AACT;AAEO,MAAM,oBAAoB;IAC/B,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/validation.ts"], "sourcesContent": ["// Validation utility functions\n\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const isValidPassword = (password: string): boolean => {\n  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number\n  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;\n  return passwordRegex.test(password);\n};\n\nexport const isValidUrl = (url: string): boolean => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const isValidPort = (port: number): boolean => {\n  return port >= 1 && port <= 65535;\n};\n\nexport const isValidDatabaseName = (name: string): boolean => {\n  // Basic validation for database names\n  const dbNameRegex = /^[a-zA-Z][a-zA-Z0-9_-]*$/;\n  return dbNameRegex.test(name) && name.length >= 2 && name.length <= 50;\n};\n\nexport const sanitizeInput = (input: string): string => {\n  return input.trim().replace(/[<>]/g, '');\n};\n\nexport const validateRequired = (value: any): boolean => {\n  if (typeof value === 'string') {\n    return value.trim().length > 0;\n  }\n  return value !== null && value !== undefined;\n};\n\nexport const validateMinLength = (value: string, minLength: number): boolean => {\n  return value.length >= minLength;\n};\n\nexport const validateMaxLength = (value: string, maxLength: number): boolean => {\n  return value.length <= maxLength;\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;AAExB,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,kBAAkB,CAAC;IAC9B,4DAA4D;IAC5D,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,QAAQ,KAAK,QAAQ;AAC9B;AAEO,MAAM,sBAAsB,CAAC;IAClC,sCAAsC;IACtC,MAAM,cAAc;IACpB,OAAO,YAAY,IAAI,CAAC,SAAS,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI;AACtE;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,MAAM,IAAI,GAAG,MAAM,GAAG;IAC/B;IACA,OAAO,UAAU,QAAQ,UAAU;AACrC;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/index.ts"], "sourcesContent": ["// Main utility exports\n\nexport * from './cn';\nexport * from './date';\nexport * from './format';\nexport * from './validation';\n\n// Re-export the original cn function for backward compatibility\nexport { cn } from './cn';\n"], "names": [], "mappings": "AAAA,uBAAuB;;AAEvB;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAAA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { ChevronRight } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<\"nav\"> & {\n    separator?: React.ComponentType<{ className?: string }>;\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label=\"breadcrumb\" {...props} />);\nBreadcrumb.displayName = \"Breadcrumb\";\n\nconst BreadcrumbList = React.forwardRef<\n  HTMLOListElement,\n  React.ComponentPropsWithoutRef<\"ol\">\n>(({ className, ...props }, ref) => (\n  <ol\n    ref={ref}\n    className={cn(\n      \"flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5\",\n      className\n    )}\n    {...props}\n  />\n));\nBreadcrumbList.displayName = \"BreadcrumbList\";\n\nconst BreadcrumbItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentPropsWithoutRef<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    className={cn(\"inline-flex items-center gap-1.5\", className)}\n    {...props}\n  />\n));\nBreadcrumbItem.displayName = \"BreadcrumbItem\";\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<\"a\"> & {\n    asChild?: boolean;\n  }\n>(({ asChild, className, ...props }, ref) => {\n  if (asChild) {\n    return <React.Fragment {...props} />;\n  }\n\n  return (\n    <a\n      ref={ref}\n      className={cn(\"transition-colors hover:text-foreground\", className)}\n      {...props}\n    />\n  );\n});\nBreadcrumbLink.displayName = \"BreadcrumbLink\";\n\nconst BreadcrumbPage = React.forwardRef<\n  HTMLSpanElement,\n  React.ComponentPropsWithoutRef<\"span\">\n>(({ className, ...props }, ref) => (\n  <span\n    ref={ref}\n    role=\"link\"\n    aria-disabled=\"true\"\n    aria-current=\"page\"\n    className={cn(\"font-normal text-foreground\", className)}\n    {...props}\n  />\n));\nBreadcrumbPage.displayName = \"BreadcrumbPage\";\n\nconst BreadcrumbSeparator = ({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) => (\n  <li\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"[&>svg]:size-3.5\", className)}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n);\nBreadcrumbSeparator.displayName = \"BreadcrumbSeparator\";\n\nconst BreadcrumbEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <svg\n      width=\"15\"\n      height=\"15\"\n      viewBox=\"0 0 15 15\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        d=\"M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z\"\n        fill=\"currentColor\"\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n      />\n    </svg>\n    <span className=\"sr-only\">More</span>\n  </span>\n);\nBreadcrumbEllipsis.displayName = \"BreadcrumbEllipsis\";\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKhC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,8OAAC;QAAI,KAAK;QAAK,cAAW;QAAc,GAAG,KAAK;;;;;;AACzE,WAAW,WAAW,GAAG;AAEzB,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKpC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACnC,IAAI,SAAS;QACX,qBAAO,8OAAC,qMAAA,CAAA,WAAc;YAAE,GAAG,KAAK;;;;;;IAClC;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB,iBAC3B,8OAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;;;;;;;;;;AAG9B,oBAAoB,WAAW,GAAG;AAElC,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OAC0B,iBAC7B,8OAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;0BAEN,cAAA,8OAAC;oBACC,GAAE;oBACF,MAAK;oBACL,UAAS;oBACT,UAAS;;;;;;;;;;;0BAGb,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\r\nimport { Menu, LayoutDashboard, ChevronRight, Plus, Brain, Download, MessageSquare } from 'lucide-react';\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { useTheme } from \"@/providers/theme-provider\";\r\nimport { ThemeToggle } from '@/components/ui/theme-toggle';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\nimport {\r\n  Breadcrumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from '@/components/ui/breadcrumb';\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated } = useAuth();\r\n  const { theme, setTheme } = useTheme();\r\n  const { pageInfo, actions } = usePageHeader();\r\n  const pathname = usePathname();\r\n  \r\n  // Check if we have breadcrumbs to show\r\n  const hasBreadcrumbs = pageInfo.breadcrumbs && pageInfo.breadcrumbs.length > 1;\r\n  \r\n  // Get chart-related state from context actions\r\n  const { \r\n    onCreateChart, \r\n    chartCount = 0, \r\n    maxCharts = 12, \r\n    onCreateAnalysis, \r\n    isCreatingAnalysis = false,\r\n    onCreateDashboard,\r\n    onExport,\r\n    onToggleChat,\r\n    isChatOpen = false\r\n  } = actions;\r\n  const canCreateChart = chartCount < maxCharts;\r\n  \r\n  // Show Create Chart Button on dashboard pages when the callback is provided\r\n  const isDashboardPage = pathname === '/dashboard';\r\n  const isAIWorkflowsPage = pathname === '/ai-workflows';\r\n  const showCreateChartButton = isDashboardPage && onCreateChart;\r\n  const showCreateDashboardButton = isDashboardPage && onCreateDashboard;\r\n  const showCreateAnalysisButton = isAIWorkflowsPage && onCreateAnalysis;\r\n  const showProjectActions = isAIWorkflowsPage && (onExport || onToggleChat);\r\n\r\n  // Get the appropriate icon for breadcrumbs\r\n  const getBreadcrumbIcon = () => {\r\n    if (pathname === '/dashboard') return LayoutDashboard;\r\n    if (pathname === '/ai-workflows') return Brain;\r\n    return pageInfo.icon || LayoutDashboard;\r\n  };\r\n\r\n  const BreadcrumbIcon = getBreadcrumbIcon();\r\n\r\n  return (\r\n    <header className=\"flex items-center justify-between whitespace-nowrap border-b border-solid border-sidebar-border bg-sidebar-bg px-4 py-2 h-12\">\r\n      <div className=\"flex items-center gap-3 text-sidebar-text-primary\">\r\n        {isAuthenticated && (\r\n          <div className=\"lg:hidden\">\r\n            <Sheet>\r\n              <SheetTrigger asChild>\r\n                <Button variant=\"outline\" size=\"icon\" className=\"h-7 w-7\">\r\n                  <Menu className=\"h-4 w-4\" />\r\n                </Button>\r\n              </SheetTrigger>\r\n            </Sheet>\r\n          </div>\r\n        )}\r\n        \r\n        {/* Dynamic Breadcrumb Navigation or Page Title */}\r\n        {hasBreadcrumbs ? (\r\n          <Breadcrumb>\r\n            <BreadcrumbList>\r\n              {pageInfo.breadcrumbs!.map((breadcrumb, index) => (\r\n                <React.Fragment key={breadcrumb.label}>\r\n                  <BreadcrumbItem>\r\n                    {index === 0 ? (\r\n                      // First breadcrumb item (with icon)\r\n                      breadcrumb.onClick || breadcrumb.href ? (\r\n                        <BreadcrumbLink \r\n                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}\r\n                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}\r\n                          className=\"flex items-center space-x-1.5 cursor-pointer hover:text-primary text-sm font-medium\"\r\n                        >\r\n                          <BreadcrumbIcon className=\"h-4 w-4\" />\r\n                          <span>{breadcrumb.label}</span>\r\n                        </BreadcrumbLink>\r\n                      ) : (\r\n                        <div className=\"flex items-center space-x-1.5 text-sm font-medium\">\r\n                          <BreadcrumbIcon className=\"h-4 w-4\" />\r\n                          <span>{breadcrumb.label}</span>\r\n                        </div>\r\n                      )\r\n                    ) : (\r\n                      // Subsequent breadcrumb items\r\n                      index === pageInfo.breadcrumbs!.length - 1 ? (\r\n                        <BreadcrumbPage className=\"font-medium text-sm\">\r\n                          {breadcrumb.label}\r\n                        </BreadcrumbPage>\r\n                      ) : (\r\n                        <BreadcrumbLink \r\n                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}\r\n                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}\r\n                          className=\"cursor-pointer hover:text-primary text-sm font-medium\"\r\n                        >\r\n                          {breadcrumb.label}\r\n                        </BreadcrumbLink>\r\n                      )\r\n                    )}\r\n                  </BreadcrumbItem>\r\n                  \r\n                  {/* Separator */}\r\n                  {index < pageInfo.breadcrumbs!.length - 1 && (\r\n                    <BreadcrumbSeparator>\r\n                      <ChevronRight className=\"h-3 w-3\" />\r\n                    </BreadcrumbSeparator>\r\n                  )}\r\n                </React.Fragment>\r\n              ))}\r\n            </BreadcrumbList>\r\n          </Breadcrumb>\r\n        ) : (\r\n          <h1 className=\"text-sm font-medium leading-tight tracking-[-0.015em] text-sidebar-text-primary\">\r\n            {pageInfo.title}\r\n          </h1>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right side actions */}\r\n      <div className=\"flex items-center gap-2\">\r\n        {showCreateChartButton && (\r\n          <Button\r\n            onClick={onCreateChart}\r\n            disabled={!canCreateChart}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            title={!canCreateChart ? `Maximum of ${maxCharts} charts allowed` : undefined}\r\n            onMouseEnter={(e) => {\r\n              if (canCreateChart) {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (canCreateChart) {\r\n                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n              }\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            Chart\r\n          </Button>\r\n        )}\r\n        {showCreateDashboardButton && (\r\n          <Button\r\n            onClick={onCreateDashboard}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            onMouseEnter={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            Dashboard\r\n          </Button>\r\n        )}\r\n        {showCreateAnalysisButton && (\r\n          <Button\r\n            onClick={onCreateAnalysis}\r\n            disabled={isCreatingAnalysis}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            title={isCreatingAnalysis ? \"Analysis is being created\" : undefined}\r\n            onMouseEnter={(e) => {\r\n              if (!isCreatingAnalysis) {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (!isCreatingAnalysis) {\r\n                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n              }\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            {isCreatingAnalysis ? 'Creating...' : 'Analysis'}\r\n          </Button>\r\n        )}\r\n        {showProjectActions && (\r\n          <>\r\n            {onExport && (\r\n              <Button\r\n                onClick={onExport}\r\n                className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n                style={{\r\n                  backgroundColor: 'var(--surface-selected)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n                }}\r\n              >\r\n                <Download className=\"h-3 w-3 mr-1.5\" />\r\n                Export\r\n              </Button>\r\n            )}\r\n            {onToggleChat && (\r\n              <Button\r\n                onClick={onToggleChat}\r\n                className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n                style={{\r\n                  backgroundColor: isChatOpen ? 'var(--interactive-bg-secondary-hover)' : 'var(--surface-selected)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = isChatOpen ? 'var(--interactive-bg-secondary-hover)' : 'var(--surface-selected)';\r\n                }}\r\n              >\r\n                <MessageSquare className=\"h-3 w-3 mr-1.5\" />\r\n                Chat\r\n              </Button>\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAXA;;;;;;;;;;;AAoBA,MAAM,SAAS;IACb,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAC1C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,uCAAuC;IACvC,MAAM,iBAAiB,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG;IAE7E,+CAA+C;IAC/C,MAAM,EACJ,aAAa,EACb,aAAa,CAAC,EACd,YAAY,EAAE,EACd,gBAAgB,EAChB,qBAAqB,KAAK,EAC1B,iBAAiB,EACjB,QAAQ,EACR,YAAY,EACZ,aAAa,KAAK,EACnB,GAAG;IACJ,MAAM,iBAAiB,aAAa;IAEpC,4EAA4E;IAC5E,MAAM,kBAAkB,aAAa;IACrC,MAAM,oBAAoB,aAAa;IACvC,MAAM,wBAAwB,mBAAmB;IACjD,MAAM,4BAA4B,mBAAmB;IACrD,MAAM,2BAA2B,qBAAqB;IACtD,MAAM,qBAAqB,qBAAqB,CAAC,YAAY,YAAY;IAEzE,2CAA2C;IAC3C,MAAM,oBAAoB;QACxB,IAAI,aAAa,cAAc,OAAO,4NAAA,CAAA,kBAAe;QACrD,IAAI,aAAa,iBAAiB,OAAO,oMAAA,CAAA,QAAK;QAC9C,OAAO,SAAS,IAAI,IAAI,4NAAA,CAAA,kBAAe;IACzC;IAEA,MAAM,iBAAiB;IAEvB,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;oBACZ,iCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;sCACJ,cAAA,8OAAC,iIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAO,WAAU;8CAC9C,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQzB,+BACC,8OAAC,sIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,sIAAA,CAAA,iBAAc;sCACZ,SAAS,WAAW,CAAE,GAAG,CAAC,CAAC,YAAY,sBACtC,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;sDACb,8OAAC,sIAAA,CAAA,iBAAc;sDACZ,UAAU,IACT,oCAAoC;4CACpC,WAAW,OAAO,IAAI,WAAW,IAAI,iBACnC,8OAAC,sIAAA,CAAA,iBAAc;gDACZ,GAAI,WAAW,IAAI,GAAG;oDAAE,MAAM,WAAW,IAAI;gDAAC,IAAI,CAAC,CAAC;gDACpD,GAAI,WAAW,OAAO,GAAG;oDAAE,SAAS,WAAW,OAAO;gDAAC,IAAI,CAAC,CAAC;gDAC9D,WAAU;;kEAEV,8OAAC;wDAAe,WAAU;;;;;;kEAC1B,8OAAC;kEAAM,WAAW,KAAK;;;;;;;;;;;qEAGzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAe,WAAU;;;;;;kEAC1B,8OAAC;kEAAM,WAAW,KAAK;;;;;;;;;;;uDAI3B,8BAA8B;4CAC9B,UAAU,SAAS,WAAW,CAAE,MAAM,GAAG,kBACvC,8OAAC,sIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,WAAW,KAAK;;;;;qEAGnB,8OAAC,sIAAA,CAAA,iBAAc;gDACZ,GAAI,WAAW,IAAI,GAAG;oDAAE,MAAM,WAAW,IAAI;gDAAC,IAAI,CAAC,CAAC;gDACpD,GAAI,WAAW,OAAO,GAAG;oDAAE,SAAS,WAAW,OAAO;gDAAC,IAAI,CAAC,CAAC;gDAC9D,WAAU;0DAET,WAAW,KAAK;;;;;;;;;;;wCAOxB,QAAQ,SAAS,WAAW,CAAE,MAAM,GAAG,mBACtC,8OAAC,sIAAA,CAAA,sBAAmB;sDAClB,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;mCAxCT,WAAW,KAAK;;;;;;;;;;;;;;6CAgD3C,8OAAC;wBAAG,WAAU;kCACX,SAAS,KAAK;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;;oBACZ,uCACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,OAAO,CAAC,iBAAiB,CAAC,WAAW,EAAE,UAAU,eAAe,CAAC,GAAG;wBACpE,cAAc,CAAC;4BACb,IAAI,gBAAgB;gCAClB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,gBAAgB;gCAClB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;;0CAEA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAmB;;;;;;;oBAItC,2CACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC1C;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC1C;;0CAEA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAmB;;;;;;;oBAItC,0CACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,OAAO,qBAAqB,8BAA8B;wBAC1D,cAAc,CAAC;4BACb,IAAI,CAAC,oBAAoB;gCACvB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,oBAAoB;gCACvB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;;0CAEA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,qBAAqB,gBAAgB;;;;;;;oBAGzC,oCACC;;4BACG,0BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,OAAO;gCACT;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;;kDAEA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAmB;;;;;;;4BAI1C,8BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,iBAAiB,aAAa,0CAA0C;oCACxE,OAAO;gCACT;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,aAAa,0CAA0C;gCACjG;;kDAEA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAmB;;;;;;;;;;;;;;;;;;;;;AAS5D;uCAEe", "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground text-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,+nBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Sidebar.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport Link from 'next/link';\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { MoreVertical, MessageCircle, Database, Plus, BarChart3, MessageCirclePlus, LayoutDashboard, Brain, User, Settings, LogOut } from 'lucide-react';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useChatHistory } from \"@/providers/ChatHistoryContext\";\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface SidebarProps {\r\n  onNewChat: () => void;\r\n  onToggleCollapse?: (collapsed: boolean) => void;\r\n  isCreatingNewChat?: boolean;\r\n}\r\n\r\nconst Sidebar: React.FC<SidebarProps> = ({ onNewChat, onToggleCollapse, isCreatingNewChat = false }) => {\r\n  const { \r\n    chatHistory, \r\n    isLoadingChats, \r\n    deleteChat,\r\n    renameChat, \r\n  } = useChatHistory();\r\n  const { logout, user } = useAuth();\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const [menuOpenId, setMenuOpenId] = useState<string | null>(null);\r\n  const [renameId, setRenameId] = useState<string | null>(null);\r\n  const [renameValue, setRenameValue] = useState<string>(\"\");\r\n  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);\r\n\r\n  console.log('Sidebar component rendered. Current pathname:', pathname);\r\n\r\n  // Extract chatId from pathname (e.g., /chat/123)\r\n  const currentChatId = pathname?.split('/').includes('chat') ? pathname.split('/').pop() : null;\r\n  console.log('Current chatId extracted from pathname:', currentChatId);\r\n\r\n  const handleRename = (id: string, currentTitle: string) => {\r\n    console.log('Renaming chat with id:', id, 'and current title:', currentTitle);\r\n    setRenameId(id);\r\n    setRenameValue(currentTitle);\r\n    setMenuOpenId(null);\r\n  };\r\n\r\n  const handleRenameSubmit = (id: string) => {\r\n    console.log('Submitting rename for chat with id:', id, 'and new title:', renameValue);\r\n    if (renameValue.trim()) {\r\n      renameChat(id, renameValue.trim());\r\n    }\r\n    setRenameId(null);\r\n    setRenameValue(\"\");\r\n  };\r\n\r\n  const handleDelete = async (chatId: string) => {\r\n    try {\r\n      await deleteChat(chatId);\r\n      setMenuOpenId(null);\r\n\r\n      // 🚚 After successful deletion, handle navigation if the deleted chat was active\r\n      if (currentChatId === chatId) {\r\n        // Get the updated chat list (the state will have been updated by deleteChat)\r\n        const remainingChats = chatHistory.filter(chat => chat.id !== chatId);\r\n\r\n        if (remainingChats.length > 0) {\r\n          // Navigate to the most recently updated chat (first in list)\r\n          router.push(`/chat/${remainingChats[0].id}`);\r\n        } else {\r\n          // No chats left – start a fresh chat\r\n          onNewChat();\r\n          // Fallback navigate to generic chat route to trigger new-chat UI\r\n          router.push('/chat');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete chat:', error);\r\n      // You could add a toast notification here if you have one\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (timestamp: Date) => {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - timestamp.getTime();\r\n    const diffHours = diffMs / (1000 * 60 * 60);\r\n    const diffDays = diffMs / (1000 * 60 * 60 * 24);\r\n\r\n    if (diffHours < 1) {\r\n      return 'Just now';\r\n    } else if (diffHours < 24) {\r\n      return `${Math.floor(diffHours)}h ago`;\r\n    } else if (diffDays < 7) {\r\n      return `${Math.floor(diffDays)}d ago`;\r\n    } else {\r\n      return timestamp.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  const handleToggleCollapse = () => {\r\n    const newCollapsedState = !isCollapsed;\r\n    setIsCollapsed(newCollapsedState);\r\n    onToggleCollapse?.(newCollapsedState);\r\n  };\r\n\r\n  return (\r\n    <aside \r\n      className={`hidden lg:flex flex-col ${isCollapsed ? 'w-16' : 'w-sidebar'} h-full min-h-0 transition-all duration-300 flex-shrink-0 relative overflow-y-auto`}\r\n      style={{\r\n        backgroundColor: 'var(--sidebar-bg)',\r\n        borderRight: '1px solid var(--sidebar-border)',\r\n        scrollbarColor: 'var(--sidebar-surface-tertiary) transparent'\r\n      }}\r\n    >\r\n      {/* Toggle Button */}\r\n      <div className={`sticky top-0 z-10 p-2 transition-all duration-300 ease-in-out ${isCollapsed ? 'flex justify-center' : 'flex justify-end'}`}\r\n        style={{ backgroundColor: 'var(--sidebar-bg)' }}\r\n      >\r\n        <Button \r\n          variant=\"ghost\" \r\n          size=\"icon\"\r\n          className=\"w-10 h-10 flex items-center justify-center rounded-lg border-0 transition-all duration-300 ease-in-out transform-gpu\"\r\n          style={{ \r\n            color: 'var(--sidebar-icon) !important',\r\n            backgroundColor: 'transparent !important'\r\n          }}\r\n          onMouseEnter={(e) => {\r\n            e.currentTarget.style.setProperty('background-color', 'var(--interactive-bg-secondary-hover)', 'important');\r\n            e.currentTarget.style.transform = 'scale(1.05)';\r\n          }}\r\n          onMouseLeave={(e) => {\r\n            e.currentTarget.style.setProperty('background-color', 'transparent', 'important');\r\n            e.currentTarget.style.transform = 'scale(1)';\r\n          }}\r\n          onClick={handleToggleCollapse}\r\n        >\r\n          <svg \r\n            className=\"h-5 w-5 transition-transform duration-300 ease-in-out\" \r\n            viewBox=\"0 0 20 20\" \r\n            fill=\"currentColor\" \r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            style={{\r\n              transform: isCollapsed ? 'scaleX(-1)' : 'scaleX(1)'\r\n            }}\r\n          >\r\n            <path d=\"M6.83496 3.99992C6.38353 4.00411 6.01421 4.0122 5.69824 4.03801C5.31232 4.06954 5.03904 4.12266 4.82227 4.20012L4.62207 4.28606C4.18264 4.50996 3.81498 4.85035 3.55859 5.26848L3.45605 5.45207C3.33013 5.69922 3.25006 6.01354 3.20801 6.52824C3.16533 7.05065 3.16504 7.71885 3.16504 8.66301V11.3271C3.16504 12.2712 3.16533 12.9394 3.20801 13.4618C3.25006 13.9766 3.33013 14.2909 3.45605 14.538L3.55859 14.7216C3.81498 15.1397 4.18266 15.4801 4.62207 15.704L4.82227 15.79C5.03904 15.8674 5.31234 15.9205 5.69824 15.9521C6.01398 15.9779 6.383 15.986 6.83398 15.9902L6.83496 3.99992ZM18.165 11.3271C18.165 12.2493 18.1653 12.9811 18.1172 13.5702C18.0745 14.0924 17.9916 14.5472 17.8125 14.9648L17.7295 15.1415C17.394 15.8 16.8834 16.3511 16.2568 16.7353L15.9814 16.8896C15.5157 17.1268 15.0069 17.2285 14.4102 17.2773C13.821 17.3254 13.0893 17.3251 12.167 17.3251H7.83301C6.91071 17.3251 6.17898 17.3254 5.58984 17.2773C5.06757 17.2346 4.61294 17.1508 4.19531 16.9716L4.01855 16.8896C3.36014 16.5541 2.80898 16.0434 2.4248 15.4169L2.27051 15.1415C2.03328 14.6758 1.93158 14.167 1.88281 13.5702C1.83468 12.9811 1.83496 12.2493 1.83496 11.3271V8.66301C1.83496 7.74072 1.83468 7.00898 1.88281 6.41985C1.93157 5.82309 2.03329 5.31432 2.27051 4.84856L2.4248 4.57317C2.80898 3.94666 3.36012 3.436 4.01855 3.10051L4.19531 3.0175C4.61285 2.83843 5.06771 2.75548 5.58984 2.71281C6.17898 2.66468 6.91071 2.66496 7.83301 2.66496H12.17C13.0893 2.66496 13.821 2.66468 14.4102 2.71281C15.0069 2.76157 15.5157 2.86329 15.9814 3.10051L16.2568 3.25481C16.8833 3.63898 17.394 4.19012 17.7295 4.84856L17.8125 5.02531C17.9916 5.44285 18.0745 5.89771 18.1172 6.41985C18.1653 7.00898 18.165 7.74072 18.165 8.66301V11.3271ZM8.16406 15.995H12.167C13.1112 15.995 13.7794 15.9947 14.3018 15.9521C14.8164 15.91 15.1308 15.8299 15.3779 15.704L15.5615 15.6015C15.9797 15.3451 16.32 14.9774 16.5439 14.538L16.6299 14.3378C16.7074 14.121 16.7605 13.8478 16.792 13.4618C16.8347 12.9394 16.835 12.2712 16.835 11.3271V8.66301C16.835 7.71885 16.8347 7.05065 16.792 6.52824C16.7605 6.14232 16.7073 5.86904 16.6299 5.65227L16.5439 5.45207C16.32 5.01264 15.9796 4.64498 15.5615 4.3886L15.3779 4.28606C15.1308 4.16013 14.8165 4.08006 14.3018 4.03801C13.7794 3.99533 13.1112 3.99504 12.167 3.99504H8.16406C8.16407 3.99667 8.16504 3.99829 8.16504 3.99992L8.16406 15.995Z\"></path>\r\n          </svg>\r\n        </Button>\r\n      </div>\r\n      \r\n      {!isCollapsed && (\r\n        <div className=\"flex flex-col h-full px-3\">\r\n          {/* Navigation Links */}\r\n          <div className=\"space-y-1 mb-4\">\r\n            <Link href=\"/dashboard\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/dashboard' ? '' : ''}`}\r\n                style={{\r\n                  color: pathname === '/dashboard' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/dashboard' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/dashboard') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/dashboard') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <LayoutDashboard className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Dashboard\r\n              </Button>\r\n            </Link>\r\n            \r\n            <Link href=\"/reports\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/reports' ? '' : ''}`}\r\n                style={{\r\n                  color: pathname === '/reports' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/reports' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/reports') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/reports') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <BarChart3 className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Reports\r\n              </Button>\r\n            </Link>\r\n            \r\n            <Link href=\"/datasources\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10`}\r\n                style={{\r\n                  color: pathname === '/datasources' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/datasources' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/datasources') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/datasources') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <Database className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Data Sources\r\n              </Button>\r\n            </Link>\r\n\r\n            <Link href=\"/ai-workflows\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10`}\r\n                style={{\r\n                  color: pathname === '/ai-workflows' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/ai-workflows' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/ai-workflows') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/ai-workflows') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <Brain className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                AI Workflows\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n          \r\n          {/* New Chat Button */}\r\n          <div className=\"mb-4\">\r\n            <Button \r\n              variant=\"ghost\" \r\n              className=\"w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10\" \r\n              onClick={onNewChat}\r\n              disabled={isCreatingNewChat}\r\n              style={{\r\n                color: 'var(--sidebar-text-secondary)',\r\n                backgroundColor: 'transparent'\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                if (!isCreatingNewChat) {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                if (!isCreatingNewChat) {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }\r\n              }}\r\n            >\r\n              <Plus className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n              {isCreatingNewChat ? 'Creating...' : 'New Chat'}\r\n            </Button>\r\n          </div>\r\n          \r\n          {/* Chat History Section */}\r\n          <div className=\"flex flex-col gap-1 overflow-y-auto flex-1 pb-4\">\r\n            <h3 \r\n              className=\"text-xs font-medium uppercase tracking-wider px-2 py-2 sticky top-0 z-10\"\r\n              style={{ \r\n                color: 'var(--sidebar-text-tertiary)',\r\n                backgroundColor: 'var(--sidebar-bg)'\r\n              }}\r\n            >\r\n              Chat History\r\n            </h3>\r\n            \r\n            {isLoadingChats && (\r\n              <div className=\"flex items-center justify-center py-8\">\r\n                <div className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  Loading chats...\r\n                </div>\r\n              </div>\r\n            )}\r\n            \r\n            {!isLoadingChats && chatHistory.map((chat) => {\r\n              const isActive = chat.id === currentChatId;\r\n              const isRenaming = renameId === chat.id;\r\n              return (\r\n                <div\r\n                  key={chat.id}\r\n                  className={`group flex items-center justify-between py-2 px-2 rounded-lg cursor-pointer transition-all duration-200 ${isActive ? '' : ''}`}\r\n                  style={{\r\n                    backgroundColor: isActive ? 'var(--surface-selected)' : 'transparent'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'transparent';\r\n                    }\r\n                  }}\r\n                  onClick={() => router.push(`/chat/${chat.id}`)}\r\n                >\r\n                  {isRenaming ? (\r\n                    <form\r\n                      onSubmit={e => { e.preventDefault(); handleRenameSubmit(chat.id); }}\r\n                      className=\"flex items-center gap-2 w-full\"\r\n                    >\r\n                      <input\r\n                        autoFocus\r\n                        className=\"truncate max-w-[120px] text-sm font-normal bg-transparent border-b border-blue-400 outline-none px-1\"\r\n                        style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        value={renameValue}\r\n                        onChange={e => setRenameValue(e.target.value)}\r\n                        onKeyDown={e => { if (e.key === 'Escape') setRenameId(null); }}\r\n                      />\r\n                      <Button \r\n                        type=\"submit\" \r\n                        size=\"sm\" \r\n                        variant=\"ghost\" \r\n                        className=\"text-blue-500 px-2 text-xs rounded border-0\"\r\n                      >\r\n                        Save\r\n                      </Button>\r\n                    </form>\r\n                  ) : (\r\n                    <>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <span \r\n                          className=\"truncate text-sm font-normal block\"\r\n                          style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        >\r\n                          {chat.title}\r\n                        </span>\r\n                      </div>\r\n                      \r\n                      <div className=\"relative ml-2 flex-shrink-0\" onClick={e => e.stopPropagation()}>\r\n                        <DropdownMenu>\r\n                          <DropdownMenuTrigger asChild>\r\n                            <Button\r\n                              type=\"button\"\r\n                              size=\"icon\"\r\n                              variant=\"ghost\"\r\n                              className=\"w-7 h-7 p-0 opacity-0 group-hover:opacity-100 transition-all duration-200 rounded border-0\"\r\n                              style={{ color: 'var(--sidebar-icon)' }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                              aria-label=\"More actions\"\r\n                            >\r\n                              <MoreVertical className=\"w-4 h-4\" />\r\n                            </Button>\r\n                          </DropdownMenuTrigger>\r\n                          <DropdownMenuContent \r\n                            align=\"start\" \r\n                            side=\"bottom\" \r\n                            sideOffset={8} \r\n                            className=\"border-none shadow-xl rounded-xl p-2\"\r\n                            style={{\r\n                              backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                              color: 'var(--sidebar-text-primary)'\r\n                            }}\r\n                          >\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleRename(chat.id, chat.title)}\r\n                              className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                              style={{ \r\n                                color: 'var(--sidebar-text-primary)',\r\n                                backgroundColor: 'transparent'\r\n                              }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                            >\r\n                              Rename\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleDelete(chat.id)}\r\n                              className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                              style={{ \r\n                                color: '#ff8583',\r\n                                backgroundColor: 'transparent'\r\n                              }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                            >\r\n                              Delete\r\n                            </DropdownMenuItem>\r\n                          </DropdownMenuContent>\r\n                        </DropdownMenu>\r\n                      </div>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              );\r\n            })}\r\n            \r\n            {!isLoadingChats && chatHistory.length === 0 && (\r\n              <div className=\"text-center py-8\">\r\n                <MessageCirclePlus \r\n                  className=\"h-12 w-12 mx-auto mb-3\" \r\n                  style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                />\r\n                <p className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  No chats yet\r\n                </p>\r\n                <p className=\"text-xs mt-1\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  Start a new conversation\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          {/* Profile Section */}\r\n          <div className=\"mt-auto pt-2 pb-4 border-t\" style={{ borderColor: 'var(--sidebar-border)' }}>\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <div className=\"flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-all duration-200 hover:bg-opacity-80\" \r\n                     style={{ backgroundColor: 'transparent' }}\r\n                     onMouseEnter={(e) => {\r\n                       e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                     }}\r\n                     onMouseLeave={(e) => {\r\n                       e.currentTarget.style.backgroundColor = 'transparent';\r\n                     }}>\r\n                  <div\r\n                    className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600\"\r\n                    style={{backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y\")'}}\r\n                  />\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <span className=\"text-sm font-medium block truncate\" style={{ color: 'var(--sidebar-text-primary)' }}>\r\n                      {'User'}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent \r\n                align=\"start\" \r\n                side=\"top\" \r\n                sideOffset={8} \r\n                className=\"border-none shadow-xl rounded-xl p-2\"\r\n                style={{\r\n                  backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n              >\r\n                <DropdownMenuItem asChild>\r\n                  <Link href=\"/profile\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                        style={{ \r\n                          color: 'var(--sidebar-text-primary)',\r\n                          backgroundColor: 'transparent'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'transparent';\r\n                        }}>\r\n                    <User className=\"w-4 h-4\" />\r\n                    Profile\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem asChild>\r\n                  <Link href=\"/settings\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                        style={{ \r\n                          color: 'var(--sidebar-text-primary)',\r\n                          backgroundColor: 'transparent'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'transparent';\r\n                        }}>\r\n                    <Settings className=\"w-4 h-4\" />\r\n                    Settings\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem \r\n                  onClick={logout} \r\n                  className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                  style={{ \r\n                    color: '#ff8583',\r\n                    backgroundColor: 'transparent'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }}\r\n                >\r\n                  <LogOut className=\"w-4 h-4\" />\r\n                  Logout\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      {/* Profile Section for Collapsed State */}\r\n      {isCollapsed && (\r\n        <div className=\"mt-auto p-2 pb-4 border-t\" style={{ borderColor: 'var(--sidebar-border)' }}>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <div className=\"flex justify-center\">\r\n                <div\r\n                  className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600 cursor-pointer transition-all duration-200 hover:scale-105\"\r\n                  style={{backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y\")'}}\r\n                />\r\n              </div>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent \r\n              align=\"start\" \r\n              side=\"right\" \r\n              sideOffset={8} \r\n              className=\"border-none shadow-xl rounded-xl p-2\"\r\n              style={{\r\n                backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                color: 'var(--sidebar-text-primary)'\r\n              }}\r\n            >\r\n              <DropdownMenuItem asChild>\r\n                <Link href=\"/profile\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                      style={{ \r\n                        color: 'var(--sidebar-text-primary)',\r\n                        backgroundColor: 'transparent'\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}>\r\n                  <User className=\"w-4 h-4\" />\r\n                  Profile\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem asChild>\r\n                <Link href=\"/settings\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                      style={{ \r\n                        color: 'var(--sidebar-text-primary)',\r\n                        backgroundColor: 'transparent'\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}>\r\n                  <Settings className=\"w-4 h-4\" />\r\n                  Settings\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem \r\n                onClick={logout} \r\n                className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                style={{ \r\n                  color: '#ff8583',\r\n                  backgroundColor: 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }}\r\n              >\r\n                <LogOut className=\"w-4 h-4\" />\r\n                Logout\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      )}\r\n    </aside>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;;AAgBA,MAAM,UAAkC,CAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,oBAAoB,KAAK,EAAE;IACjG,MAAM,EACJ,WAAW,EACX,cAAc,EACd,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,uIAAA,CAAA,iBAAc,AAAD;IACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAExD,QAAQ,GAAG,CAAC,iDAAiD;IAE7D,iDAAiD;IACjD,MAAM,gBAAgB,UAAU,MAAM,KAAK,SAAS,UAAU,SAAS,KAAK,CAAC,KAAK,GAAG,KAAK;IAC1F,QAAQ,GAAG,CAAC,2CAA2C;IAEvD,MAAM,eAAe,CAAC,IAAY;QAChC,QAAQ,GAAG,CAAC,0BAA0B,IAAI,sBAAsB;QAChE,YAAY;QACZ,eAAe;QACf,cAAc;IAChB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,uCAAuC,IAAI,kBAAkB;QACzE,IAAI,YAAY,IAAI,IAAI;YACtB,WAAW,IAAI,YAAY,IAAI;QACjC;QACA,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,cAAc;YAEd,iFAAiF;YACjF,IAAI,kBAAkB,QAAQ;gBAC5B,6EAA6E;gBAC7E,MAAM,iBAAiB,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAE9D,IAAI,eAAe,MAAM,GAAG,GAAG;oBAC7B,6DAA6D;oBAC7D,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC7C,OAAO;oBACL,qCAAqC;oBACrC;oBACA,iEAAiE;oBACjE,OAAO,IAAI,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,0DAA0D;QAC5D;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,UAAU,OAAO;QAChD,MAAM,YAAY,SAAS,CAAC,OAAO,KAAK,EAAE;QAC1C,MAAM,WAAW,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAE9C,IAAI,YAAY,GAAG;YACjB,OAAO;QACT,OAAO,IAAI,YAAY,IAAI;YACzB,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,KAAK,CAAC;QACxC,OAAO,IAAI,WAAW,GAAG;YACvB,OAAO,GAAG,KAAK,KAAK,CAAC,UAAU,KAAK,CAAC;QACvC,OAAO;YACL,OAAO,UAAU,kBAAkB;QACrC;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,oBAAoB,CAAC;QAC3B,eAAe;QACf,mBAAmB;IACrB;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,wBAAwB,EAAE,cAAc,SAAS,YAAY,kFAAkF,CAAC;QAC5J,OAAO;YACL,iBAAiB;YACjB,aAAa;YACb,gBAAgB;QAClB;;0BAGA,8OAAC;gBAAI,WAAW,CAAC,8DAA8D,EAAE,cAAc,wBAAwB,oBAAoB;gBACzI,OAAO;oBAAE,iBAAiB;gBAAoB;0BAE9C,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,OAAO;wBACL,OAAO;wBACP,iBAAiB;oBACnB;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,yCAAyC;wBAC/F,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oBACpC;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,eAAe;wBACrE,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oBACpC;oBACA,SAAS;8BAET,cAAA,8OAAC;wBACC,WAAU;wBACV,SAAQ;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO;4BACL,WAAW,cAAc,eAAe;wBAC1C;kCAEA,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;;;;;YAKb,CAAC,6BACA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,oGAAoG,EAAE,aAAa,eAAe,KAAK,IAAI;oCACvJ,OAAO;wCACL,OAAO,aAAa,eAAe,gCAAgC;wCACnE,iBAAiB,aAAa,eAAe,4BAA4B;oCAC3E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,cAAc;4CAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,cAAc;4CAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,8OAAC,4NAAA,CAAA,kBAAe;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAKpF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,oGAAoG,EAAE,aAAa,aAAa,KAAK,IAAI;oCACrJ,OAAO;wCACL,OAAO,aAAa,aAAa,gCAAgC;wCACjE,iBAAiB,aAAa,aAAa,4BAA4B;oCACzE;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,YAAY;4CAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,YAAY;4CAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,8OAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAK9E,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,mGAAmG,CAAC;oCAChH,OAAO;wCACL,OAAO,aAAa,iBAAiB,gCAAgC;wCACrE,iBAAiB,aAAa,iBAAiB,4BAA4B;oCAC7E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,gBAAgB;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,gBAAgB;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAK7E,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,mGAAmG,CAAC;oCAChH,OAAO;wCACL,OAAO,aAAa,kBAAkB,gCAAgC;wCACtE,iBAAiB,aAAa,kBAAkB,4BAA4B;oCAC9E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,iBAAiB;4CAChC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,iBAAiB;4CAChC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;;;;;;;kCAO5E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;4BACT,UAAU;4BACV,OAAO;gCACL,OAAO;gCACP,iBAAiB;4BACnB;4BACA,cAAc,CAAC;gCACb,IAAI,CAAC,mBAAmB;oCACtB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;4BACF;4BACA,cAAc,CAAC;gCACb,IAAI,CAAC,mBAAmB;oCACtB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;4BACF;;8CAEA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAU,OAAO;wCAAE,OAAO;oCAAsB;;;;;;gCAC/D,oBAAoB,gBAAgB;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;0CACD;;;;;;4BAIA,gCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAU,OAAO;wCAAE,OAAO;oCAA+B;8CAAG;;;;;;;;;;;4BAM9E,CAAC,kBAAkB,YAAY,GAAG,CAAC,CAAC;gCACnC,MAAM,WAAW,KAAK,EAAE,KAAK;gCAC7B,MAAM,aAAa,aAAa,KAAK,EAAE;gCACvC,qBACE,8OAAC;oCAEC,WAAW,CAAC,wGAAwG,EAAE,WAAW,KAAK,IAAI;oCAC1I,OAAO;wCACL,iBAAiB,WAAW,4BAA4B;oCAC1D;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;8CAE5C,2BACC,8OAAC;wCACC,UAAU,CAAA;4CAAO,EAAE,cAAc;4CAAI,mBAAmB,KAAK,EAAE;wCAAG;wCAClE,WAAU;;0DAEV,8OAAC;gDACC,SAAS;gDACT,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAA8B;gDAC9C,OAAO;gDACP,UAAU,CAAA,IAAK,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAW,CAAA;oDAAO,IAAI,EAAE,GAAG,KAAK,UAAU,YAAY;gDAAO;;;;;;0DAE/D,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;6DAKH;;0DACE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAA8B;8DAE7C,KAAK,KAAK;;;;;;;;;;;0DAIf,8OAAC;gDAAI,WAAU;gDAA8B,SAAS,CAAA,IAAK,EAAE,eAAe;0DAC1E,cAAA,8OAAC,4IAAA,CAAA,eAAY;;sEACX,8OAAC,4IAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,MAAK;gEACL,SAAQ;gEACR,WAAU;gEACV,OAAO;oEAAE,OAAO;gEAAsB;gEACtC,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEAC1C;gEACA,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEAC1C;gEACA,cAAW;0EAEX,cAAA,8OAAC,0NAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG5B,8OAAC,4IAAA,CAAA,sBAAmB;4DAClB,OAAM;4DACN,MAAK;4DACL,YAAY;4DACZ,WAAU;4DACV,OAAO;gEACL,iBAAiB;gEACjB,OAAO;4DACT;;8EAEA,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,aAAa,KAAK,EAAE,EAAE,KAAK,KAAK;oEAC/C,WAAU;oEACV,OAAO;wEACL,OAAO;wEACP,iBAAiB;oEACnB;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;8EACD;;;;;;8EAGD,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,aAAa,KAAK,EAAE;oEACnC,WAAU;oEACV,OAAO;wEACL,OAAO;wEACP,iBAAiB;oEACnB;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;8EACD;;;;;;;;;;;;;;;;;;;;;;;;;mCA7GN,KAAK,EAAE;;;;;4BAuHlB;4BAEC,CAAC,kBAAkB,YAAY,MAAM,KAAK,mBACzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oOAAA,CAAA,oBAAiB;wCAChB,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAA+B;;;;;;kDAEjD,8OAAC;wCAAE,WAAU;wCAAU,OAAO;4CAAE,OAAO;wCAA+B;kDAAG;;;;;;kDAGzE,8OAAC;wCAAE,WAAU;wCAAe,OAAO;4CAAE,OAAO;wCAA+B;kDAAG;;;;;;;;;;;;;;;;;;kCAQpF,8OAAC;wBAAI,WAAU;wBAA6B,OAAO;4BAAE,aAAa;wBAAwB;kCACxF,cAAA,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC;wCAAI,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAc;wCACxC,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACH,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAC,iBAAiB;gDAA0U;;;;;;0DAErW,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;oDAAqC,OAAO;wDAAE,OAAO;oDAA8B;8DAChG;;;;;;;;;;;;;;;;;;;;;;8CAKT,8OAAC,4IAAA,CAAA,sBAAmB;oCAClB,OAAM;oCACN,MAAK;oCACL,YAAY;oCACZ,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,OAAO;oCACT;;sDAEA,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;gDAC1B,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;;kEACJ,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIhC,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;gDAC3B,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;;kEACJ,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIpC,8OAAC,4IAAA,CAAA,mBAAgB;4CACf,SAAS;4CACT,WAAU;4CACV,OAAO;gDACL,OAAO;gDACP,iBAAiB;4CACnB;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;;8DAEA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUzC,6BACC,8OAAC;gBAAI,WAAU;gBAA4B,OAAO;oBAAE,aAAa;gBAAwB;0BACvF,cAAA,8OAAC,4IAAA,CAAA,eAAY;;sCACX,8OAAC,4IAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAC,iBAAiB;oCAA0U;;;;;;;;;;;;;;;;sCAIzW,8OAAC,4IAAA,CAAA,sBAAmB;4BAClB,OAAM;4BACN,MAAK;4BACL,YAAY;4BACZ,WAAU;4BACV,OAAO;gCACL,iBAAiB;gCACjB,OAAO;4BACT;;8CAEA,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;wCAC1B,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACJ,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIhC,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;wCAC3B,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACJ,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIpC,8OAAC,4IAAA,CAAA,mBAAgB;oCACf,SAAS;oCACT,WAAU;oCACV,OAAO;wCACL,OAAO;wCACP,iBAAiB;oCACnB;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;;sDAEA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;uCAEe", "debugId": null}}, {"offset": {"line": 2284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Layout.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { ReactNode, useState, useRef, useCallback } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useChatHistory } from \"@/providers/ChatHistoryContext\";\r\nimport { PageHeaderProvider } from '@/providers/PageHeaderContext';\r\nimport Header from './Header';\r\nimport Sidebar from './Sidebar';\r\n\r\ninterface LayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\r\n  const { setActiveChat } = useChatHistory();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);\r\n  const [isCreatingNewChat, setIsCreatingNewChat] = useState<boolean>(false);\r\n  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  const handleNewChat = useCallback(async () => {\r\n    // Prevent multiple simultaneous new chat creations\r\n    if (isCreatingNewChat) {\r\n      console.log('New chat creation already in progress, ignoring click');\r\n      return;\r\n    }\r\n\r\n    // Clear any existing debounce timeout\r\n    if (debounceTimeoutRef.current) {\r\n      clearTimeout(debounceTimeoutRef.current);\r\n    }\r\n\r\n    // Set debounce timeout to prevent rapid clicking\r\n    debounceTimeoutRef.current = setTimeout(async () => {\r\n      setIsCreatingNewChat(true);\r\n      \r\n      try {\r\n        // Clear the active chat to show the welcome message\r\n        await setActiveChat(null);\r\n        \r\n        // Navigate to the base chat route without a specific chat ID\r\n        router.push('/chat');\r\n      } catch (error) {\r\n        console.error('Error creating new chat:', error);\r\n      } finally {\r\n        setIsCreatingNewChat(false);\r\n      }\r\n    }, 300); // 300ms debounce to prevent rapid clicking\r\n  }, [isCreatingNewChat, setActiveChat, router]);\r\n\r\n  const handleToggleCollapse = (collapsed: boolean) => {\r\n    setSidebarCollapsed(collapsed);\r\n  };\r\n\r\n  // Cleanup timeout on unmount\r\n  React.useEffect(() => {\r\n    return () => {\r\n      if (debounceTimeoutRef.current) {\r\n        clearTimeout(debounceTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <PageHeaderProvider>\r\n      <div className=\"flex h-screen bg-sidebar-bg\" style={{ fontFamily: 'var(--font-inter), var(--font-noto-sans), sans-serif' }}>\r\n      <Sidebar \r\n        onNewChat={handleNewChat} \r\n        onToggleCollapse={handleToggleCollapse}\r\n        isCreatingNewChat={isCreatingNewChat}\r\n      />\r\n      <main className=\"flex flex-col flex-1 min-w-0 bg-sidebar-bg overflow-y-auto\">\r\n        <Header />\r\n        <div className=\"flex-1\">\r\n          {children}\r\n        </div>\r\n      </main>\r\n    </div>\r\n    </PageHeaderProvider>\r\n  );\r\n};\r\n\r\nexport default Layout;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAYA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAEzD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,mDAAmD;QACnD,IAAI,mBAAmB;YACrB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,sCAAsC;QACtC,IAAI,mBAAmB,OAAO,EAAE;YAC9B,aAAa,mBAAmB,OAAO;QACzC;QAEA,iDAAiD;QACjD,mBAAmB,OAAO,GAAG,WAAW;YACtC,qBAAqB;YAErB,IAAI;gBACF,oDAAoD;gBACpD,MAAM,cAAc;gBAEpB,6DAA6D;gBAC7D,OAAO,IAAI,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C,SAAU;gBACR,qBAAqB;YACvB;QACF,GAAG,MAAM,2CAA2C;IACtD,GAAG;QAAC;QAAmB;QAAe;KAAO;IAE7C,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,6BAA6B;IAC7B,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,OAAO;YACL,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,sIAAA,CAAA,qBAAkB;kBACjB,cAAA,8OAAC;YAAI,WAAU;YAA8B,OAAO;gBAAE,YAAY;YAAuD;;8BACzH,8OAAC,uIAAA,CAAA,UAAO;oBACN,WAAW;oBACX,kBAAkB;oBAClB,mBAAmB;;;;;;8BAErB,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,sIAAA,CAAA,UAAM;;;;;sCACP,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMX;uCAEe", "debugId": null}}, {"offset": {"line": 2406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/hooks/usePageTitle.ts"], "sourcesContent": ["import { useEffect, useMemo } from 'react';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\n\r\ninterface UsePageTitleOptions {\r\n  title: string;\r\n  subtitle?: string;\r\n  icon?: React.ComponentType<{ className?: string }>;\r\n}\r\n\r\n/**\r\n * Custom hook for setting page-specific header information\r\n * \r\n * Industry standard approach for managing page titles and metadata.\r\n * Automatically handles cleanup when component unmounts.\r\n * \r\n * @param options - Page header configuration\r\n */\r\nexport const usePageTitle = (options: UsePageTitleOptions) => {\r\n  const { setPageHeader, resetPageHeader } = usePageHeader();\r\n\r\n  useEffect(() => {\r\n    // Set the page header when component mounts or options change\r\n    setPageHeader(options);\r\n\r\n    // Cleanup: reset to default when component unmounts\r\n    return () => {\r\n      resetPageHeader();\r\n    };\r\n  }, [\r\n    options.title, \r\n    options.subtitle, \r\n    options.icon, \r\n    setPageHeader, \r\n    resetPageHeader\r\n  ]);\r\n};\r\n\r\n/**\r\n * Simplified hook for just setting a page title\r\n * \r\n * @param title - Page title\r\n * @param icon - Optional icon component\r\n */\r\nexport const useSimplePageTitle = (title: string, icon?: React.ComponentType<{ className?: string }>) => {\r\n  usePageTitle({ title, icon });\r\n}; "], "names": [], "mappings": ";;;;AAAA;AACA;;;AAgBO,MAAM,eAAe,CAAC;IAC3B,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8DAA8D;QAC9D,cAAc;QAEd,oDAAoD;QACpD,OAAO;YACL;QACF;IACF,GAAG;QACD,QAAQ,KAAK;QACb,QAAQ,QAAQ;QAChB,QAAQ,IAAI;QACZ;QACA;KACD;AACH;AAQO,MAAM,qBAAqB,CAAC,OAAe;IAChD,aAAa;QAAE;QAAO;IAAK;AAC7B", "debugId": null}}, {"offset": {"line": 2443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils/index\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold text-white\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6 text-white\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAAA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/ai-workflows/AnalysisProjectList.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { \r\n  Plus, \r\n  Play, \r\n  Clock, \r\n  CheckCircle, \r\n  AlertCircle, \r\n  MoreVertical,\r\n  Brain,\r\n  Database,\r\n  FileText\r\n} from 'lucide-react';\r\nimport { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from \"@/components/ui/dropdown-menu\";\r\nimport { AnalysisProject } from '@/types';\r\n\r\ninterface AnalysisProjectListProps {\r\n  projects: AnalysisProject[];\r\n  onSelectProject: (project: AnalysisProject) => void;\r\n  onDeleteProject: (projectId: string) => void;\r\n  isLoading: boolean;\r\n}\r\n\r\nconst AnalysisProjectList: React.FC<AnalysisProjectListProps> = ({\r\n  projects,\r\n  onSelectProject,\r\n  onDeleteProject,\r\n  isLoading,\r\n}) => {\r\n  // Format timestamp for display\r\n  const formatTimestamp = (timestamp: Date | string) => {\r\n    const now = new Date();\r\n    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\r\n    const diffMs = now.getTime() - date.getTime();\r\n    const diffHours = diffMs / (1000 * 60 * 60);\r\n    const diffDays = diffMs / (1000 * 60 * 60 * 24);\r\n\r\n    if (diffHours < 1) {\r\n      return 'Just now';\r\n    } else if (diffHours < 24) {\r\n      return `${Math.floor(diffHours)}h ago`;\r\n    } else if (diffDays < 7) {\r\n      return `${Math.floor(diffDays)}d ago`;\r\n    } else {\r\n      return date.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  // Get status display configuration\r\n  const getStatusDisplay = (status: AnalysisProject['status']) => {\r\n    const statusConfig = {\r\n      draft: { color: 'var(--sidebar-text-tertiary)', icon: FileText, text: 'Draft' },\r\n      running: { color: '#f59e0b', icon: Play, text: 'Running' },\r\n      completed: { color: '#10b981', icon: CheckCircle, text: 'Completed' },\r\n      error: { color: '#ef4444', icon: AlertCircle, text: 'Error' }\r\n    };\r\n    return statusConfig[status];\r\n  };\r\n\r\n  // Render project card\r\n  const renderProjectCard = (project: AnalysisProject) => {\r\n    const status = getStatusDisplay(project.status);\r\n    const StatusIcon = status.icon;\r\n\r\n    return (\r\n      <Card\r\n        key={project.id}\r\n        className=\"group cursor-pointer transition-all duration-200 border hover:shadow-lg\"\r\n        style={{\r\n          backgroundColor: 'var(--sidebar-surface-secondary)',\r\n          borderColor: 'var(--sidebar-border)'\r\n        }}\r\n        onClick={() => onSelectProject(project)}\r\n        onMouseEnter={(e) => {\r\n          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n          e.currentTarget.style.transform = 'translateY(-1px)';\r\n        }}\r\n        onMouseLeave={(e) => {\r\n          e.currentTarget.style.backgroundColor = 'var(--sidebar-surface-secondary)';\r\n          e.currentTarget.style.transform = 'translateY(0)';\r\n        }}\r\n      >\r\n        <CardHeader className=\"pb-3\">\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"flex-1 min-w-0\">\r\n                <CardTitle \r\n                  className=\"text-base mb-1 truncate\"\r\n                  style={{ color: 'var(--sidebar-text-primary)' }}\r\n                >\r\n                  {project.name}\r\n                </CardTitle>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <StatusIcon \r\n                    className=\"h-3 w-3\" \r\n                    style={{ color: status.color }}\r\n                  />\r\n                  <span \r\n                    className=\"text-xs\"\r\n                    style={{ color: status.color }}\r\n                  >\r\n                    {status.text}\r\n                    {project.status === 'running' && project.progress && ` (${project.progress}%)`}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Menu Button */}\r\n            <div onClick={(e) => e.stopPropagation()}>\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    className=\"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 border-0\"\r\n                    style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                  >\r\n                    <MoreVertical className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent \r\n                  align=\"end\" \r\n                  className=\"border-none shadow-xl rounded-xl p-2\"\r\n                  style={{\r\n                    backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                    color: 'var(--sidebar-text-primary)'\r\n                  }}\r\n                >\r\n                  <DropdownMenuItem\r\n                    onClick={() => onDeleteProject(project.id)}\r\n                    className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                    style={{ \r\n                      color: '#ff8583',\r\n                      backgroundColor: 'transparent'\r\n                    }}\r\n                    onMouseEnter={(e) => {\r\n                      e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                    }}\r\n                    onMouseLeave={(e) => {\r\n                      e.currentTarget.style.backgroundColor = 'transparent';\r\n                    }}\r\n                  >\r\n                    Delete Project\r\n                  </DropdownMenuItem>\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n\r\n        <CardContent className=\"pt-0\">\r\n          {/* Description */}\r\n          {project.description && (\r\n            <p \r\n              className=\"text-sm mb-3 line-clamp-2\"\r\n              style={{ color: 'var(--sidebar-text-secondary)' }}\r\n            >\r\n              {project.description}\r\n            </p>\r\n          )}\r\n\r\n          {/* Project details */}\r\n          <div className=\"space-y-2\">\r\n            {project.dataSource && (\r\n              <div className=\"flex items-center gap-2\">\r\n                <Database \r\n                  className=\"h-3 w-3\" \r\n                  style={{ color: 'var(--sidebar-text-tertiary)' }} \r\n                />\r\n                <span \r\n                  className=\"text-xs\"\r\n                  style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                >\r\n                  {project.dataSource}\r\n                </span>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-4\">\r\n                <span \r\n                  className=\"text-xs\"\r\n                  style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                >\r\n                  {project.stepCount} {project.stepCount === 1 ? 'step' : 'steps'}\r\n                </span>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <Clock \r\n                    className=\"h-3 w-3\" \r\n                    style={{ color: 'var(--sidebar-text-tertiary)' }} \r\n                  />\r\n                  <span \r\n                    className=\"text-xs\"\r\n                    style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                  >\r\n                    {formatTimestamp(project.updatedAt)}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Project Grid */}\r\n      {isLoading ? (\r\n        // Loading skeleton\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          {[...Array(6)].map((_, index) => (\r\n            <Card\r\n              key={index}\r\n              className=\"border animate-pulse\"\r\n              style={{\r\n                backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                borderColor: 'var(--sidebar-border)'\r\n              }}\r\n            >\r\n              <CardHeader className=\"pb-3\">\r\n                <div className=\"flex items-start justify-between\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <div \r\n                      className=\"w-9 h-9 rounded-lg\"\r\n                      style={{ backgroundColor: 'var(--sidebar-text-tertiary)' }}\r\n                    />\r\n                    <div className=\"space-y-2\">\r\n                      <div \r\n                        className=\"h-4 w-24 rounded\"\r\n                        style={{ backgroundColor: 'var(--sidebar-text-tertiary)' }}\r\n                      />\r\n                      <div \r\n                        className=\"h-3 w-16 rounded\"\r\n                        style={{ backgroundColor: 'var(--sidebar-text-tertiary)' }}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent className=\"pt-0\">\r\n                <div className=\"space-y-2\">\r\n                  <div \r\n                    className=\"h-3 w-full rounded\"\r\n                    style={{ backgroundColor: 'var(--sidebar-text-tertiary)' }}\r\n                  />\r\n                  <div \r\n                    className=\"h-3 w-2/3 rounded\"\r\n                    style={{ backgroundColor: 'var(--sidebar-text-tertiary)' }}\r\n                  />\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          ))}\r\n        </div>\r\n      ) : projects.length > 0 ? (\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          {projects.map(renderProjectCard)}\r\n        </div>\r\n      ) : (\r\n        // Empty state\r\n        <Card \r\n          className=\"text-center py-12 border-dashed border-2\"\r\n          style={{\r\n            backgroundColor: 'var(--sidebar-bg)',\r\n            borderColor: 'var(--sidebar-border)'\r\n          }}\r\n        >\r\n          <CardContent className=\"space-y-4\">\r\n            <div \r\n              className=\"p-4 rounded-full w-fit mx-auto\"\r\n              style={{ backgroundColor: 'var(--surface-selected)' }}\r\n            >\r\n            </div>\r\n            <div>\r\n              <h3 \r\n                className=\"text-lg font-semibold\"\r\n                style={{ color: 'var(--sidebar-text-primary)' }}\r\n              >\r\n                No analysis projects yet\r\n              </h3>\r\n              <p \r\n                className=\"mt-2\"\r\n                style={{ color: 'var(--sidebar-text-secondary)' }}\r\n              >\r\n                Create your first analysis project using the \"Create Analysis\" button in the top right corner to get started with AI-powered data insights\r\n              </p>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AnalysisProjectList; "], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAhBA;;;;;;AA0BA,MAAM,sBAA0D,CAAC,EAC/D,QAAQ,EACR,eAAe,EACf,eAAe,EACf,SAAS,EACV;IACC,+BAA+B;IAC/B,MAAM,kBAAkB,CAAC;QACvB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,qBAAqB,OAAO,YAAY,IAAI,KAAK;QAC9D,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,YAAY,SAAS,CAAC,OAAO,KAAK,EAAE;QAC1C,MAAM,WAAW,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAE9C,IAAI,YAAY,GAAG;YACjB,OAAO;QACT,OAAO,IAAI,YAAY,IAAI;YACzB,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,KAAK,CAAC;QACxC,OAAO,IAAI,WAAW,GAAG;YACvB,OAAO,GAAG,KAAK,KAAK,CAAC,UAAU,KAAK,CAAC;QACvC,OAAO;YACL,OAAO,KAAK,kBAAkB;QAChC;IACF;IAEA,mCAAmC;IACnC,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe;YACnB,OAAO;gBAAE,OAAO;gBAAgC,MAAM,8MAAA,CAAA,WAAQ;gBAAE,MAAM;YAAQ;YAC9E,SAAS;gBAAE,OAAO;gBAAW,MAAM,kMAAA,CAAA,OAAI;gBAAE,MAAM;YAAU;YACzD,WAAW;gBAAE,OAAO;gBAAW,MAAM,2NAAA,CAAA,cAAW;gBAAE,MAAM;YAAY;YACpE,OAAO;gBAAE,OAAO;gBAAW,MAAM,oNAAA,CAAA,cAAW;gBAAE,MAAM;YAAQ;QAC9D;QACA,OAAO,YAAY,CAAC,OAAO;IAC7B;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,MAAM,SAAS,iBAAiB,QAAQ,MAAM;QAC9C,MAAM,aAAa,OAAO,IAAI;QAE9B,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAEH,WAAU;YACV,OAAO;gBACL,iBAAiB;gBACjB,aAAa;YACf;YACA,SAAS,IAAM,gBAAgB;YAC/B,cAAc,CAAC;gBACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gBACxC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;YACpC;YACA,cAAc,CAAC;gBACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gBACxC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;YACpC;;8BAEA,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gIAAA,CAAA,YAAS;4CACR,WAAU;4CACV,OAAO;gDAAE,OAAO;4CAA8B;sDAE7C,QAAQ,IAAI;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,OAAO,KAAK;oDAAC;;;;;;8DAE/B,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,OAAO,KAAK;oDAAC;;wDAE5B,OAAO,IAAI;wDACX,QAAQ,MAAM,KAAK,aAAa,QAAQ,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,QAAQ,CAAC,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAOtF,8OAAC;gCAAI,SAAS,CAAC,IAAM,EAAE,eAAe;0CACpC,cAAA,8OAAC,4IAAA,CAAA,eAAY;;sDACX,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,OAAO;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAA+B;0DAE/C,cAAA,8OAAC,0NAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG5B,8OAAC,4IAAA,CAAA,sBAAmB;4CAClB,OAAM;4CACN,WAAU;4CACV,OAAO;gDACL,iBAAiB;gDACjB,OAAO;4CACT;sDAEA,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,gBAAgB,QAAQ,EAAE;gDACzC,WAAU;gDACV,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;0DACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASX,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;wBAEpB,QAAQ,WAAW,kBAClB,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO;4BAAgC;sCAE/C,QAAQ,WAAW;;;;;;sCAKxB,8OAAC;4BAAI,WAAU;;gCACZ,QAAQ,UAAU,kBACjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CACP,WAAU;4CACV,OAAO;gDAAE,OAAO;4CAA+B;;;;;;sDAEjD,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO;4CAA+B;sDAE9C,QAAQ,UAAU;;;;;;;;;;;;8CAKzB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAA+B;;oDAE9C,QAAQ,SAAS;oDAAC;oDAAE,QAAQ,SAAS,KAAK,IAAI,SAAS;;;;;;;0DAE1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDACJ,WAAU;wDACV,OAAO;4DAAE,OAAO;wDAA+B;;;;;;kEAEjD,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO;wDAA+B;kEAE9C,gBAAgB,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WAlIzC,QAAQ,EAAE;;;;;IA2IrB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBAEZ,YACC,mBAAmB;sBACnB,8OAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC,gIAAA,CAAA,OAAI;oBAEH,WAAU;oBACV,OAAO;wBACL,iBAAiB;wBACjB,aAAa;oBACf;;sCAEA,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAA+B;;;;;;sDAE3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAA+B;;;;;;8DAE3D,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMnE,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAA+B;;;;;;kDAE3D,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAA+B;;;;;;;;;;;;;;;;;;mBAnC1D;;;;;;;;;mBA0CT,SAAS,MAAM,GAAG,kBACpB,8OAAC;YAAI,WAAU;sBACZ,SAAS,GAAG,CAAC;;;;;mBAGhB,cAAc;sBACd,8OAAC,gIAAA,CAAA,OAAI;YACH,WAAU;YACV,OAAO;gBACL,iBAAiB;gBACjB,aAAa;YACf;sBAEA,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAA0B;;;;;;kCAGtD,8OAAC;;0CACC,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAA8B;0CAC/C;;;;;;0CAGD,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAgC;0CACjD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 3103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,gbACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-2 w-full overflow-hidden rounded-full bg-slate-100 dark:bg-slate-800\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-slate-900 transition-all dark:bg-slate-50\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,mFACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/ai-workflows/AnalysisProjectView.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useMemo, useEffect, useCallback, useRef } from 'react';\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { \r\n  MessageSquare,\r\n  Code2,\r\n  BarChart3,\r\n  Table,\r\n  Send,\r\n  X,\r\n  ChevronRight,\r\n  Download,\r\n  Copy,\r\n  Maximize2,\r\n  Database,\r\n  Eye,\r\n  Layers,\r\n  Edit3,\r\n  Filter,\r\n  ChevronDown,\r\n  Play,\r\n  Upload,\r\n  File,\r\n  Trash2\r\n} from 'lucide-react';\r\nimport { AnalysisProject, PipelineStep, DataOutput, FileUploadProgress, UploadedFile } from '@/types';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\nimport { useApi } from '@/providers/ApiContext';\r\nimport Chat from '../chat/Chat';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Progress } from '@/components/ui/progress';\r\n\r\ninterface AnalysisProjectViewProps {\r\n  project: AnalysisProject;\r\n  onUpdateProject: (projectId: string, updates: Partial<AnalysisProject>) => void;\r\n  isChatOpen: boolean;\r\n  onToggleChat: () => void;\r\n}\r\n\r\n// Using imported types from @/types: AnalysisProject, PipelineStep, DataOutput\r\n\r\nconst AnalysisProjectView: React.FC<AnalysisProjectViewProps> = ({\r\n  project,\r\n  onUpdateProject,\r\n  isChatOpen,\r\n  onToggleChat\r\n}) => {\r\n  // API context\r\n  const { getProjectData, getProjectSteps, uploadProjectFile, deleteProjectFile } = useApi();\r\n\r\n  // State\r\n  const [activeTab, setActiveTab] = useState<'overview' | 'data' | 'charts' | 'code'>('data');\r\n  const [pipelineSteps, setPipelineSteps] = useState<PipelineStep[]>([]);\r\n  const [projectData, setProjectData] = useState<DataOutput | null>(null);\r\n  const [isLoadingSteps, setIsLoadingSteps] = useState(false);\r\n  const [isLoadingData, setIsLoadingData] = useState(false);\r\n  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());\r\n  const [chatMessage, setChatMessage] = useState('');\r\n\r\n  // File upload state\r\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\r\n  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress | null>(null);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [dragActive, setDragActive] = useState(false);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const { setPageActions } = usePageHeader();\r\n\r\n  // Set page actions for the header\r\n  useEffect(() => {\r\n    setPageActions({\r\n      // Export action\r\n      onExport: () => {\r\n        console.log('Exporting results...');\r\n        // TODO: Implement export functionality\r\n      },\r\n      // Chat toggle action  \r\n      onToggleChat: () => onToggleChat(),\r\n      isChatOpen: isChatOpen,\r\n    });\r\n  }, [isChatOpen, setPageActions]);\r\n\r\n  // Load pipeline steps and data on component mount or project change\r\n  useEffect(() => {\r\n    if (project.id) {\r\n      loadPipelineSteps();\r\n      if (activeTab === 'data') {\r\n        loadProjectData();\r\n      }\r\n    }\r\n  }, [project.id]);\r\n\r\n  // Load data when switching to data tab\r\n  useEffect(() => {\r\n    if (activeTab === 'data' && project.id && !projectData) {\r\n      loadProjectData();\r\n    }\r\n  }, [activeTab, project.id]);\r\n\r\n  const loadPipelineSteps = useCallback(async () => {\r\n    setIsLoadingSteps(true);\r\n    try {\r\n      const response = await getProjectSteps(project.id);\r\n      if (response.success && response.data) {\r\n        setPipelineSteps(response.data);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load pipeline steps:', error);\r\n      setPipelineSteps([]);\r\n    } finally {\r\n      setIsLoadingSteps(false);\r\n    }\r\n  }, [project.id, getProjectSteps]);\r\n\r\n  const loadProjectData = useCallback(async () => {\r\n    setIsLoadingData(true);\r\n    try {\r\n      const response = await getProjectData(project.id);\r\n      if (response.success && response.data) {\r\n        setProjectData(response.data);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load project data:', error);\r\n      setProjectData(null);\r\n    } finally {\r\n      setIsLoadingData(false);\r\n    }\r\n  }, [project.id, getProjectData]);\r\n\r\n  const toggleStepExpansion = (stepId: string) => {\r\n    setExpandedSteps(prev => {\r\n      const newSet = new Set(prev);\r\n      if (newSet.has(stepId)) {\r\n        newSet.delete(stepId);\r\n      } else {\r\n        newSet.add(stepId);\r\n      }\r\n      return newSet;\r\n    });\r\n  };\r\n\r\n  const getStatusIcon = (status: AnalysisProject['status']) => {\r\n    switch (status) {\r\n      case 'completed':\r\n        return <div className=\"w-2 h-2 bg-green-500 rounded-full\" />;\r\n      case 'running':\r\n        return <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\" />;\r\n      case 'error':\r\n        return <div className=\"w-2 h-2 bg-red-500 rounded-full\" />;\r\n      default:\r\n        return <div className=\"w-2 h-2 bg-gray-400 rounded-full\" />;\r\n    }\r\n  };\r\n\r\n  const getStepStatusIcon = (status: PipelineStep['status']) => {\r\n    switch (status) {\r\n      case 'completed':\r\n        return <div className=\"w-2 h-2 bg-green-500 rounded-full\" />;\r\n      case 'running':\r\n        return <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\" />;\r\n      case 'error':\r\n        return <div className=\"w-2 h-2 bg-red-500 rounded-full\" />;\r\n      default:\r\n        return <div className=\"w-2 h-2 bg-gray-400 rounded-full\" />;\r\n    }\r\n  };\r\n\r\n  const completedSteps = pipelineSteps.filter(step => step.status === 'completed').length;\r\n\r\n  const formatCurrency = (value: number): string => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD',\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 0,\r\n    }).format(value);\r\n  };\r\n\r\n  const formatCellValue = (value: any, columnName: string): string => {\r\n    if (value === null || value === undefined) return 'N/A';\r\n    \r\n    if (columnName.toLowerCase().includes('salary') && typeof value === 'number') {\r\n      return formatCurrency(value);\r\n    }\r\n    \r\n    if (typeof value === 'number' && value % 1 !== 0) {\r\n      return value.toFixed(2);\r\n    }\r\n    \r\n    return String(value);\r\n  };\r\n\r\n  // Handle chat functionality\r\n  const handleSendMessage = () => {\r\n    if (!chatMessage.trim()) return;\r\n    console.log('Sending message:', chatMessage);\r\n    setChatMessage('');\r\n    // TODO: Implement chat functionality\r\n  };\r\n\r\n  // Copy code to clipboard\r\n  const copyCode = (code: string) => {\r\n    navigator.clipboard.writeText(code);\r\n    // TODO: Show toast notification\r\n  };\r\n\r\n  // File upload handlers\r\n  const handleFileSelect = (files: FileList | null) => {\r\n    if (!files || files.length === 0) return;\r\n    \r\n    const file = files[0];\r\n    handleFileUpload(file);\r\n  };\r\n\r\n  const handleFileUpload = async (file: File) => {\r\n    if (!file) return;\r\n\r\n    // Validate file type\r\n    const allowedTypes = ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];\r\n    const allowedExtensions = ['.csv', '.xls', '.xlsx'];\r\n    const hasValidExtension = allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));\r\n    \r\n    if (!allowedTypes.includes(file.type) && !hasValidExtension) {\r\n      console.error('Invalid file type. Please upload CSV or Excel files only.');\r\n      return;\r\n    }\r\n\r\n    // Check file size (max 10MB)\r\n    if (file.size > 10 * 1024 * 1024) {\r\n      console.error('File size too large. Please upload files smaller than 10MB.');\r\n      return;\r\n    }\r\n\r\n    setIsUploading(true);\r\n    setUploadProgress(null);\r\n\r\n    try {\r\n      const response = await uploadProjectFile({\r\n        file,\r\n        projectId: project.id,\r\n        onProgress: (progress: FileUploadProgress) => {\r\n          setUploadProgress(progress);\r\n        }\r\n      });\r\n\r\n      if (response.success && response.data) {\r\n        // Add uploaded file to list\r\n        const newFile: UploadedFile = {\r\n          id: response.data.fileId,\r\n          filename: response.data.filename,\r\n          size: file.size,\r\n          type: file.type,\r\n          uploadedAt: new Date().toISOString(),\r\n          status: 'completed'\r\n        };\r\n\r\n        setUploadedFiles(prev => [...prev, newFile]);\r\n\r\n        // Update project data with the uploaded file's preview data\r\n        setProjectData(response.data.previewData);\r\n\r\n        console.log('File uploaded successfully:', response.data);\r\n      }\r\n    } catch (error) {\r\n      console.error('File upload failed:', error);\r\n    } finally {\r\n      setIsUploading(false);\r\n      setUploadProgress(null);\r\n    }\r\n  };\r\n\r\n  const handleDeleteFile = async (fileId: string) => {\r\n    try {\r\n      const response = await deleteProjectFile(project.id, fileId);\r\n      \r\n      if (response.success) {\r\n        setUploadedFiles(prev => prev.filter(f => f.id !== fileId));\r\n        \r\n        // If this was the only file, clear project data\r\n        if (uploadedFiles.length === 1) {\r\n          setProjectData(null);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('File deletion failed:', error);\r\n    }\r\n  };\r\n\r\n  // Drag and drop handlers\r\n  const handleDrag = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    if (e.type === \"dragenter\" || e.type === \"dragover\") {\r\n      setDragActive(true);\r\n    } else if (e.type === \"dragleave\") {\r\n      setDragActive(false);\r\n    }\r\n  }, []);\r\n\r\n  const handleDrop = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setDragActive(false);\r\n    \r\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\r\n      handleFileUpload(e.dataTransfer.files[0]);\r\n    }\r\n  }, []);\r\n\r\n  const formatFileSize = (bytes: number): string => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"min-h-screen flex\"\r\n      style={{ backgroundColor: 'var(--sidebar-bg)' }}\r\n      role=\"main\"\r\n      aria-label=\"Analysis Project View\"\r\n    >\r\n      {/* Main Content Area */}\r\n      <div className={`flex-1 transition-all duration-300 ${isChatOpen ? 'mr-96' : 'mr-0'}`}>\r\n        <div className=\"h-full flex flex-col p-6\">\r\n          \r\n          {/* Simplified Header */}\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <div className=\"flex items-center gap-4\">\r\n              {/* Status indicator */}\r\n              <div className=\"flex items-center gap-2\">\r\n                {getStatusIcon(project.status)}\r\n                <span className=\"text-sm\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  {completedSteps} of {pipelineSteps.length} steps complete\r\n                </span>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Filter dropdown */}\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"h-8 px-3 text-xs border-0\"\r\n                style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }}\r\n              >\r\n                <Filter className=\"h-3 w-3 mr-1.5\" />\r\n                {activeTab === 'overview' ? 'All Steps' : activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}\r\n              </Button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Tab Navigation */}\r\n          <div className=\"flex items-center gap-1 mb-6\">\r\n            {[\r\n              { id: 'overview', label: 'Overview', icon: Layers },\r\n              { id: 'data', label: 'Data', icon: Table },\r\n              { id: 'charts', label: 'Charts', icon: BarChart3 },\r\n              { id: 'code', label: 'Code', icon: Code2 }\r\n            ].map((tab) => {\r\n              const Icon = tab.icon;\r\n              const isActive = activeTab === tab.id;\r\n              \r\n              return (\r\n                <Button\r\n                  key={tab.id}\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={() => setActiveTab(tab.id as any)}\r\n                  className=\"h-8 px-3 text-xs border-0\"\r\n                  style={{\r\n                    color: isActive ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                    backgroundColor: isActive ? 'var(--surface-selected)' : 'transparent'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'transparent';\r\n                    }\r\n                  }}\r\n                >\r\n                  <Icon className=\"h-3 w-3 mr-1.5\" />\r\n                  {tab.label}\r\n                </Button>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {/* Content Area */}\r\n          <div className=\"flex-1 overflow-auto\">\r\n            \r\n            {/* Overview Tab */}\r\n            {activeTab === 'overview' && (\r\n              <div className=\"space-y-6\">\r\n                {/* Pipeline Steps */}\r\n                <div className=\"grid gap-4\">\r\n                  {pipelineSteps.map((step, index) => (\r\n                    <Card\r\n                      key={step.id}\r\n                      className=\"border cursor-pointer transition-all duration-200\"\r\n                      style={{\r\n                        backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                        borderColor: expandedSteps.has(step.id) ? '#3b82f6' : 'var(--sidebar-border)'\r\n                      }}\r\n                      onClick={() => toggleStepExpansion(step.id)}\r\n                      onMouseEnter={(e) => {\r\n                        if (!expandedSteps.has(step.id)) {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        if (!expandedSteps.has(step.id)) {\r\n                          e.currentTarget.style.backgroundColor = 'var(--sidebar-surface-secondary)';\r\n                        }\r\n                      }}\r\n                    >\r\n                      <CardHeader className=\"pb-3\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"flex items-center gap-3\">\r\n                            <div \r\n                              className=\"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium\"\r\n                              style={{ \r\n                                backgroundColor: 'var(--surface-selected)',\r\n                                color: 'var(--sidebar-text-primary)'\r\n                              }}\r\n                            >\r\n                              {index + 1}\r\n                            </div>\r\n                            <div>\r\n                              <CardTitle \r\n                                className=\"text-base\"\r\n                                style={{ color: 'var(--sidebar-text-primary)' }}\r\n                              >\r\n                                {step.title}\r\n                              </CardTitle>\r\n                            </div>\r\n                          </div>\r\n                          <div className=\"flex items-center gap-3\">\r\n                            {getStepStatusIcon(step.status)}\r\n                            <ChevronRight \r\n                              className={`h-4 w-4 transition-transform duration-200 ${\r\n                                expandedSteps.has(step.id) ? 'rotate-90' : ''\r\n                              }`}\r\n                              style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                            />\r\n                          </div>\r\n                        </div>\r\n                      </CardHeader>\r\n                      \r\n                      {/* Expanded Step Content */}\r\n                      {expandedSteps.has(step.id) && step.outputs && (\r\n                        <CardContent className=\"pt-0 space-y-4\">\r\n                          {step.outputs.summary && (\r\n                            <p \r\n                              className=\"text-sm\"\r\n                              style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                            >\r\n                              {step.outputs.summary}\r\n                            </p>\r\n                          )}\r\n                          \r\n                          {/* Step Data */}\r\n                          {step.outputs.data && (\r\n                            <div>\r\n                              <h4 \r\n                                className=\"text-sm font-medium mb-2 flex items-center gap-2\"\r\n                                style={{ color: 'var(--sidebar-text-primary)' }}\r\n                              >\r\n                                <Table className=\"h-4 w-4\" />\r\n                                Data Output\r\n                              </h4>\r\n                              <div \r\n                                className=\"border rounded-lg p-3 text-center\"\r\n                                style={{ \r\n                                  backgroundColor: 'var(--sidebar-bg)',\r\n                                  borderColor: 'var(--sidebar-border)',\r\n                                  color: 'var(--sidebar-text-secondary)'\r\n                                }}\r\n                              >\r\n                                <p className=\"text-sm\">Data output available</p>\r\n                                <p className=\"text-xs mt-1\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                                  View in Data tab for detailed table\r\n                                </p>\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                          \r\n                          {/* Step Visualization */}\r\n                          {step.outputs.visualization && (\r\n                            <div>\r\n                              <h4 \r\n                                className=\"text-sm font-medium mb-2 flex items-center gap-2\"\r\n                                style={{ color: 'var(--sidebar-text-primary)' }}\r\n                              >\r\n                                <BarChart3 className=\"h-4 w-4\" />\r\n                                Visualization\r\n                              </h4>\r\n                              <div \r\n                                className=\"border rounded-lg p-4 text-center\"\r\n                                style={{ \r\n                                  backgroundColor: 'var(--sidebar-bg)',\r\n                                  borderColor: 'var(--sidebar-border)',\r\n                                  color: 'var(--sidebar-text-secondary)'\r\n                                }}\r\n                              >\r\n                                <p className=\"text-sm\">{step.outputs.visualization.config?.title || 'Visualization'}</p>\r\n                                <p className=\"text-xs mt-1\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                                  View in Charts tab for interactive visualization\r\n                                </p>\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                          \r\n                          {/* Step Code */}\r\n                          {step.outputs.code && (\r\n                            <div>\r\n                              <div className=\"flex items-center justify-between mb-2\">\r\n                                <h4 \r\n                                  className=\"text-sm font-medium flex items-center gap-2\"\r\n                                  style={{ color: 'var(--sidebar-text-primary)' }}\r\n                                >\r\n                                  <Code2 className=\"h-4 w-4\" />\r\n                                  Generated Code\r\n                                </h4>\r\n                                <div className=\"flex items-center gap-2\">\r\n                                  <Button\r\n                                    variant=\"ghost\"\r\n                                    size=\"sm\"\r\n                                    onClick={(e) => {\r\n                                      e.stopPropagation();\r\n                                      copyCode(step.outputs!.code!);\r\n                                    }}\r\n                                    className=\"h-7 px-2 border-0\"\r\n                                    style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                                  >\r\n                                    <Copy className=\"h-3 w-3\" />\r\n                                  </Button>\r\n                                  <Button\r\n                                    variant=\"ghost\"\r\n                                    size=\"sm\"\r\n                                    onClick={(e) => {\r\n                                      e.stopPropagation();\r\n                                      toggleStepExpansion(step.id);\r\n                                    }}\r\n                                    className=\"h-7 px-2 border-0\"\r\n                                    style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                                  >\r\n                                    <Maximize2 className=\"h-3 w-3\" />\r\n                                  </Button>\r\n                                </div>\r\n                              </div>\r\n                              <div \r\n                                className={`border rounded-lg p-3 font-mono text-xs overflow-x-auto transition-all duration-200 ${\r\n                                  expandedSteps.has(step.id) ? 'max-h-96' : 'max-h-32'\r\n                                }`}\r\n                                style={{ \r\n                                  backgroundColor: 'var(--sidebar-bg)',\r\n                                  borderColor: 'var(--sidebar-border)',\r\n                                  color: 'var(--sidebar-text-secondary)'\r\n                                }}\r\n                              >\r\n                                <pre>{step.outputs.code}</pre>\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                        </CardContent>\r\n                      )}\r\n                    </Card>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Data Tab */}\r\n            {activeTab === 'data' && (\r\n              <div className=\"space-y-4\">\r\n                {/* Compact File Upload Section */}\r\n                <div className=\"space-y-3\">\r\n                  {/* Upload Area - Compact */}\r\n                  <div\r\n                    className={`border border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer ${\r\n                      dragActive \r\n                        ? 'border-blue-400' \r\n                        : ''\r\n                    }`}\r\n                    style={{\r\n                      backgroundColor: dragActive ? 'rgba(59, 130, 246, 0.05)' : 'var(--sidebar-surface-secondary)',\r\n                      borderColor: dragActive ? '#3b82f6' : 'var(--sidebar-border)',\r\n                    }}\r\n                    onDragEnter={handleDrag}\r\n                    onDragLeave={handleDrag}\r\n                    onDragOver={handleDrag}\r\n                    onDrop={handleDrop}\r\n                    onClick={() => fileInputRef.current?.click()}\r\n                  >\r\n                    <Upload \r\n                      className=\"h-5 w-5 mx-auto mb-2\" \r\n                      style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                    />\r\n                    <p className=\"text-xs\" style={{ color: 'var(--sidebar-text-primary)' }}>\r\n                      Drop files here or <span className=\"text-blue-500 hover:text-blue-600\">browse</span>\r\n                    </p>\r\n                    <p className=\"text-xs mt-1\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                      CSV, Excel up to 10MB\r\n                    </p>\r\n                    <input\r\n                      ref={fileInputRef}\r\n                      type=\"file\"\r\n                      accept=\".csv,.xls,.xlsx\"\r\n                      onChange={(e) => handleFileSelect(e.target.files)}\r\n                      className=\"hidden\"\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Upload Progress - Compact */}\r\n                  {uploadProgress && (\r\n                    <div \r\n                      className=\"px-3 py-2 rounded-lg border\"\r\n                      style={{\r\n                        backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                        borderColor: 'var(--sidebar-border)'\r\n                      }}\r\n                    >\r\n                      <div className=\"flex items-center justify-between mb-1\">\r\n                        <span className=\"text-xs\" style={{ color: 'var(--sidebar-text-secondary)' }}>\r\n                          Uploading...\r\n                        </span>\r\n                        <span className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                          {uploadProgress.percentage}%\r\n                        </span>\r\n                      </div>\r\n                      <Progress value={uploadProgress.percentage} className=\"h-1\" />\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Uploaded Files List - Compact (Cursor-style) */}\r\n                  {uploadedFiles.length > 0 && (\r\n                    <div className=\"space-y-1\">\r\n                      {uploadedFiles.map((file) => (\r\n                        <div\r\n                          key={file.id}\r\n                          className=\"flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors duration-200\"\r\n                          style={{\r\n                            backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                            borderColor: 'var(--sidebar-border)'\r\n                          }}\r\n                          onMouseEnter={(e) => {\r\n                            e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                          }}\r\n                          onMouseLeave={(e) => {\r\n                            e.currentTarget.style.backgroundColor = 'var(--sidebar-surface-secondary)';\r\n                          }}\r\n                        >\r\n                          <File className=\"h-3 w-3 flex-shrink-0\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                          <div className=\"flex-1 min-w-0\">\r\n                            <p className=\"text-xs font-medium truncate\" style={{ color: 'var(--sidebar-text-primary)' }}>\r\n                              {file.filename}\r\n                            </p>\r\n                            <p className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                              {formatFileSize(file.size)}\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"flex items-center gap-1 flex-shrink-0\">\r\n                            {file.status === 'completed' && (\r\n                              <div \r\n                                className=\"w-2 h-2 rounded-full\"\r\n                                style={{ backgroundColor: '#10b981' }}\r\n                              />\r\n                            )}\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              size=\"sm\"\r\n                              onClick={() => handleDeleteFile(file.id)}\r\n                              className=\"h-6 w-6 p-0 border-0 opacity-60 hover:opacity-100\"\r\n                              style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.color = '#ef4444';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.color = 'var(--sidebar-text-tertiary)';\r\n                              }}\r\n                            >\r\n                              <Trash2 className=\"h-3 w-3\" />\r\n                            </Button>\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Data Preview Section */}\r\n                {isLoadingData ? (\r\n                  <Card \r\n                    className=\"animate-pulse border\"\r\n                    style={{\r\n                      backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                      borderColor: 'var(--sidebar-border)'\r\n                    }}\r\n                  >\r\n                    <CardHeader>\r\n                      <div \r\n                        className=\"h-4 rounded w-1/4\"\r\n                        style={{ backgroundColor: 'var(--interactive-bg-secondary-hover)' }}\r\n                      ></div>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                      <div className=\"space-y-3\">\r\n                        {[1, 2, 3, 4, 5].map(i => (\r\n                          <div \r\n                            key={i} \r\n                            className=\"h-4 rounded\"\r\n                            style={{ backgroundColor: 'var(--sidebar-bg)' }}\r\n                          ></div>\r\n                        ))}\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                ) : projectData && projectData.totalRows > 0 ? (\r\n                  <Card \r\n                    className=\"border\"\r\n                    style={{\r\n                      backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                      borderColor: 'var(--sidebar-border)'\r\n                    }}\r\n                  >\r\n                    <CardHeader className=\"pb-3\">\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <CardTitle \r\n                          className=\"text-sm font-medium\"\r\n                          style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        >\r\n                          Data Preview\r\n                        </CardTitle>\r\n                        <div className=\"flex gap-2\">\r\n                          <Button \r\n                            variant=\"ghost\" \r\n                            size=\"sm\" \r\n                            className=\"h-6 px-2 text-xs border-0\"\r\n                            style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                            onMouseEnter={(e) => {\r\n                              e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                            }}\r\n                            onMouseLeave={(e) => {\r\n                              e.currentTarget.style.backgroundColor = 'transparent';\r\n                            }}\r\n                          >\r\n                            <Download className=\"h-3 w-3 mr-1\" />\r\n                            Export\r\n                          </Button>\r\n                        </div>\r\n                      </div>\r\n                      <p className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                        {projectData.totalRows} rows • {projectData.columns.length} columns\r\n                      </p>\r\n                    </CardHeader>\r\n                    <CardContent className=\"pt-0\">\r\n                      <div className=\"overflow-x-auto\">\r\n                        <table className=\"w-full text-sm\">\r\n                          <thead>\r\n                            <tr style={{ borderBottom: `1px solid var(--sidebar-border)` }}>\r\n                              {projectData.columns.map((column) => (\r\n                                <th \r\n                                  key={column.name} \r\n                                  className=\"text-left p-2 text-xs font-medium\"\r\n                                  style={{ \r\n                                    color: 'var(--sidebar-text-primary)',\r\n                                    backgroundColor: 'var(--sidebar-bg)'\r\n                                  }}\r\n                                >\r\n                                  {column.name}\r\n                                  <span className=\"ml-1\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                                    ({column.type})\r\n                                  </span>\r\n                                </th>\r\n                              ))}\r\n                            </tr>\r\n                          </thead>\r\n                          <tbody>\r\n                            {projectData.rows.map((row, index) => (\r\n                              <tr \r\n                                key={index} \r\n                                className=\"transition-colors duration-150\"\r\n                                style={{ borderBottom: `1px solid var(--sidebar-border)` }}\r\n                                onMouseEnter={(e) => {\r\n                                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                                }}\r\n                                onMouseLeave={(e) => {\r\n                                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                                }}\r\n                              >\r\n                                {projectData.columns.map((column) => (\r\n                                  <td \r\n                                    key={column.name} \r\n                                    className=\"p-2 text-xs\"\r\n                                    style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                                  >\r\n                                    {formatCellValue(row[column.name], column.name)}\r\n                                  </td>\r\n                                ))}\r\n                              </tr>\r\n                            ))}\r\n                          </tbody>\r\n                        </table>\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                ) : uploadedFiles.length === 0 ? (\r\n                  <Card \r\n                    className=\"border\"\r\n                    style={{\r\n                      backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                      borderColor: 'var(--sidebar-border)'\r\n                    }}\r\n                  >\r\n                    <CardContent className=\"p-8 text-center\">\r\n                      <Upload \r\n                        className=\"h-12 w-12 mx-auto mb-4\" \r\n                        style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                      />\r\n                      <h3 \r\n                        className=\"text-lg font-medium mb-2\"\r\n                        style={{ color: 'var(--sidebar-text-primary)' }}\r\n                      >\r\n                        No data uploaded yet\r\n                      </h3>\r\n                      <p \r\n                        className=\"mb-4\"\r\n                        style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                      >\r\n                        Upload a CSV or Excel file to start analyzing your data\r\n                      </p>\r\n                      <Button \r\n                        onClick={() => fileInputRef.current?.click()}\r\n                        className=\"border-0\"\r\n                        style={{\r\n                          backgroundColor: 'var(--surface-selected)',\r\n                          color: 'var(--sidebar-text-primary)'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n                        }}\r\n                      >\r\n                        <Upload className=\"h-4 w-4 mr-2\" />\r\n                        Upload Your First File\r\n                      </Button>\r\n                    </CardContent>\r\n                  </Card>\r\n                ) : (\r\n                  <Card \r\n                    className=\"border\"\r\n                    style={{\r\n                      backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                      borderColor: 'var(--sidebar-border)'\r\n                    }}\r\n                  >\r\n                    <CardContent className=\"p-8 text-center\">\r\n                      <p style={{ color: 'var(--sidebar-text-secondary)' }}>\r\n                        Processing uploaded files...\r\n                      </p>\r\n                    </CardContent>\r\n                  </Card>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            {/* Charts Tab */}\r\n            {activeTab === 'charts' && (\r\n              <Card>\r\n                <CardContent className=\"p-8 text-center\">\r\n                  <p className=\"text-gray-500\">Charts and visualizations will be displayed here.</p>\r\n                  <p className=\"text-xs text-gray-400 mt-1\">Feature coming soon</p>\r\n                </CardContent>\r\n              </Card>\r\n            )}\r\n\r\n            {/* Code Tab */}\r\n            {activeTab === 'code' && (\r\n              <div className=\"space-y-6\">\r\n                {pipelineSteps\r\n                  .filter(step => step.outputs?.code)\r\n                  .map((step) => (\r\n                    <div key={step.id}>\r\n                      <div className=\"flex items-center justify-between mb-4\">\r\n                        <h3 \r\n                          className=\"text-lg font-medium flex items-center gap-2\"\r\n                          style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        >\r\n                          <Code2 className=\"h-5 w-5\" />\r\n                          {step.title} - Generated Code\r\n                        </h3>\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"sm\"\r\n                          onClick={() => copyCode(step.outputs!.code!)}\r\n                          className=\"border\"\r\n                          style={{\r\n                            borderColor: 'var(--sidebar-border)',\r\n                            color: 'var(--sidebar-text-secondary)'\r\n                          }}\r\n                        >\r\n                          <Copy className=\"h-4 w-4 mr-2\" />\r\n                          Copy Code\r\n                        </Button>\r\n                      </div>\r\n                      <div \r\n                        className=\"border rounded-lg p-4 font-mono text-sm overflow-x-auto\"\r\n                        style={{ \r\n                          backgroundColor: 'var(--sidebar-bg)',\r\n                          borderColor: 'var(--sidebar-border)',\r\n                          color: 'var(--sidebar-text-secondary)'\r\n                        }}\r\n                      >\r\n                        <pre>{step.outputs!.code}</pre>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Chat Panel */}\r\n      {isChatOpen && (\r\n        <div \r\n          className=\"fixed right-0 top-0 w-96 h-full border-l flex flex-col\"\r\n          style={{\r\n            backgroundColor: 'var(--sidebar-surface-secondary)',\r\n            borderColor: 'var(--sidebar-border)'\r\n          }}\r\n        >\r\n          {/* Chat Header */}\r\n          <div \r\n            className=\"p-4 border-b flex items-center justify-between\"\r\n            style={{ borderColor: 'var(--sidebar-border)' }}\r\n          >\r\n            <h3 \r\n              className=\"font-medium\"\r\n              style={{ color: 'var(--sidebar-text-primary)' }}\r\n            >\r\n              AI Assistant\r\n            </h3>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => onToggleChat()}\r\n              className=\"h-8 w-8 p-0 border-0\"\r\n              style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n            >\r\n              <X className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Chat Messages */}\r\n          <div className=\"flex-1 p-4 overflow-auto\">\r\n            <div \r\n              className=\"text-sm text-center\"\r\n              style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n            >\r\n              Start a conversation to modify your analysis, ask questions, or generate new insights.\r\n            </div>\r\n          </div>\r\n\r\n          {/* Chat Input */}\r\n          <div \r\n            className=\"p-4 border-t\"\r\n            style={{ borderColor: 'var(--sidebar-border)' }}\r\n          >\r\n            <div className=\"flex gap-2\">\r\n              <Input\r\n                placeholder=\"Ask about your data or request changes...\"\r\n                value={chatMessage}\r\n                onChange={(e) => setChatMessage(e.target.value)}\r\n                className=\"flex-1 text-sm border\"\r\n                style={{\r\n                  backgroundColor: 'var(--sidebar-bg)',\r\n                  borderColor: 'var(--sidebar-border)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n                onKeyDown={(e) => {\r\n                  if (e.key === 'Enter' && chatMessage.trim()) {\r\n                    handleSendMessage();\r\n                  }\r\n                }}\r\n              />\r\n              <Button\r\n                onClick={handleSendMessage}\r\n                disabled={!chatMessage.trim()}\r\n                size=\"sm\"\r\n                className=\"px-3 border-0\"\r\n                style={{\r\n                  backgroundColor: chatMessage.trim() ? 'var(--surface-selected)' : 'transparent',\r\n                  color: chatMessage.trim() ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-tertiary)'\r\n                }}\r\n              >\r\n                <Send className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AnalysisProjectView; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AACA;AAGA;AAjCA;;;;;;;;;;AA0CA,+EAA+E;AAE/E,MAAM,sBAA0D,CAAC,EAC/D,OAAO,EACP,eAAe,EACf,UAAU,EACV,YAAY,EACb;IACC,cAAc;IACd,MAAM,EAAE,cAAc,EAAE,eAAe,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD;IAEvF,QAAQ;IACR,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2C;IACpF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,oBAAoB;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAChF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAEvC,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;YACb,gBAAgB;YAChB,UAAU;gBACR,QAAQ,GAAG,CAAC;YACZ,uCAAuC;YACzC;YACA,uBAAuB;YACvB,cAAc,IAAM;YACpB,YAAY;QACd;IACF,GAAG;QAAC;QAAY;KAAe;IAE/B,oEAAoE;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,EAAE,EAAE;YACd;YACA,IAAI,cAAc,QAAQ;gBACxB;YACF;QACF;IACF,GAAG;QAAC,QAAQ,EAAE;KAAC;IAEf,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,UAAU,QAAQ,EAAE,IAAI,CAAC,aAAa;YACtD;QACF;IACF,GAAG;QAAC;QAAW,QAAQ,EAAE;KAAC;IAE1B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,kBAAkB;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,gBAAgB,QAAQ,EAAE;YACjD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,iBAAiB,SAAS,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,iBAAiB,EAAE;QACrB,SAAU;YACR,kBAAkB;QACpB;IACF,GAAG;QAAC,QAAQ,EAAE;QAAE;KAAgB;IAEhC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,iBAAiB;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,eAAe,QAAQ,EAAE;YAChD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,eAAe,SAAS,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,eAAe;QACjB,SAAU;YACR,iBAAiB;QACnB;IACF,GAAG;QAAC,QAAQ,EAAE;QAAE;KAAe;IAE/B,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB,CAAA;YACf,MAAM,SAAS,IAAI,IAAI;YACvB,IAAI,OAAO,GAAG,CAAC,SAAS;gBACtB,OAAO,MAAM,CAAC;YAChB,OAAO;gBACL,OAAO,GAAG,CAAC;YACb;YACA,OAAO;QACT;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;;;;;YACxB;gBACE,qBAAO,8OAAC;oBAAI,WAAU;;;;;;QAC1B;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;;;;;YACxB;gBACE,qBAAO,8OAAC;oBAAI,WAAU;;;;;;QAC1B;IACF;IAEA,MAAM,iBAAiB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,MAAM;IAEvF,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,kBAAkB,CAAC,OAAY;QACnC,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAElD,IAAI,WAAW,WAAW,GAAG,QAAQ,CAAC,aAAa,OAAO,UAAU,UAAU;YAC5E,OAAO,eAAe;QACxB;QAEA,IAAI,OAAO,UAAU,YAAY,QAAQ,MAAM,GAAG;YAChD,OAAO,MAAM,OAAO,CAAC;QACvB;QAEA,OAAO,OAAO;IAChB;IAEA,4BAA4B;IAC5B,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,IAAI,IAAI;QACzB,QAAQ,GAAG,CAAC,oBAAoB;QAChC,eAAe;IACf,qCAAqC;IACvC;IAEA,yBAAyB;IACzB,MAAM,WAAW,CAAC;QAChB,UAAU,SAAS,CAAC,SAAS,CAAC;IAC9B,gCAAgC;IAClC;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,iBAAiB;IACnB;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,MAAM;QAEX,qBAAqB;QACrB,MAAM,eAAe;YAAC;YAAY;YAA4B;SAAoE;QAClI,MAAM,oBAAoB;YAAC;YAAQ;YAAQ;SAAQ;QACnD,MAAM,oBAAoB,kBAAkB,IAAI,CAAC,CAAA,MAAO,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAEzF,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,KAAK,CAAC,mBAAmB;YAC3D,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,6BAA6B;QAC7B,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,eAAe;QACf,kBAAkB;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,kBAAkB;gBACvC;gBACA,WAAW,QAAQ,EAAE;gBACrB,YAAY,CAAC;oBACX,kBAAkB;gBACpB;YACF;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,4BAA4B;gBAC5B,MAAM,UAAwB;oBAC5B,IAAI,SAAS,IAAI,CAAC,MAAM;oBACxB,UAAU,SAAS,IAAI,CAAC,QAAQ;oBAChC,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,YAAY,IAAI,OAAO,WAAW;oBAClC,QAAQ;gBACV;gBAEA,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM;qBAAQ;gBAE3C,4DAA4D;gBAC5D,eAAe,SAAS,IAAI,CAAC,WAAW;gBAExC,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI;YAC1D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,eAAe;YACf,kBAAkB;QACpB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,kBAAkB,QAAQ,EAAE,EAAE;YAErD,IAAI,SAAS,OAAO,EAAE;gBACpB,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAEnD,gDAAgD;gBAChD,IAAI,cAAc,MAAM,KAAK,GAAG;oBAC9B,eAAe;gBACjB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,yBAAyB;IACzB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,iBAAiB,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;QAC1C;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YAAE,iBAAiB;QAAoB;QAC9C,MAAK;QACL,cAAW;;0BAGX,8OAAC;gBAAI,WAAW,CAAC,mCAAmC,EAAE,aAAa,UAAU,QAAQ;0BACnF,cAAA,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,cAAc,QAAQ,MAAM;0DAC7B,8OAAC;gDAAK,WAAU;gDAAU,OAAO;oDAAE,OAAO;gDAA+B;;oDACtE;oDAAe;oDAAK,cAAc,MAAM;oDAAC;;;;;;;;;;;;;;;;;;8CAMhD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAgC;wCAChD,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DAEA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,cAAc,aAAa,cAAc,UAAU,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,KAAK,CAAC;;;;;;;;;;;;;;;;;;sCAMpG,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,OAAO;oCAAY,MAAM,sMAAA,CAAA,SAAM;gCAAC;gCAClD;oCAAE,IAAI;oCAAQ,OAAO;oCAAQ,MAAM,oMAAA,CAAA,QAAK;gCAAC;gCACzC;oCAAE,IAAI;oCAAU,OAAO;oCAAU,MAAM,kNAAA,CAAA,YAAS;gCAAC;gCACjD;oCAAE,IAAI;oCAAQ,OAAO;oCAAQ,MAAM,0MAAA,CAAA,QAAK;gCAAC;6BAC1C,CAAC,GAAG,CAAC,CAAC;gCACL,MAAM,OAAO,IAAI,IAAI;gCACrB,MAAM,WAAW,cAAc,IAAI,EAAE;gCAErC,qBACE,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAU;oCACV,OAAO;wCACL,OAAO,WAAW,gCAAgC;wCAClD,iBAAiB,WAAW,4BAA4B;oCAC1D;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,8OAAC;4CAAK,WAAU;;;;;;wCACf,IAAI,KAAK;;mCArBL,IAAI,EAAE;;;;;4BAwBjB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;gCAGZ,cAAc,4BACb,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC,gIAAA,CAAA,OAAI;gDAEH,WAAU;gDACV,OAAO;oDACL,iBAAiB;oDACjB,aAAa,cAAc,GAAG,CAAC,KAAK,EAAE,IAAI,YAAY;gDACxD;gDACA,SAAS,IAAM,oBAAoB,KAAK,EAAE;gDAC1C,cAAc,CAAC;oDACb,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,EAAE,GAAG;wDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDAC1C;gDACF;gDACA,cAAc,CAAC;oDACb,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,EAAE,GAAG;wDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDAC1C;gDACF;;kEAEA,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,WAAU;4EACV,OAAO;gFACL,iBAAiB;gFACjB,OAAO;4EACT;sFAEC,QAAQ;;;;;;sFAEX,8OAAC;sFACC,cAAA,8OAAC,gIAAA,CAAA,YAAS;gFACR,WAAU;gFACV,OAAO;oFAAE,OAAO;gFAA8B;0FAE7C,KAAK,KAAK;;;;;;;;;;;;;;;;;8EAIjB,8OAAC;oEAAI,WAAU;;wEACZ,kBAAkB,KAAK,MAAM;sFAC9B,8OAAC,sNAAA,CAAA,eAAY;4EACX,WAAW,CAAC,0CAA0C,EACpD,cAAc,GAAG,CAAC,KAAK,EAAE,IAAI,cAAc,IAC3C;4EACF,OAAO;gFAAE,OAAO;4EAA+B;;;;;;;;;;;;;;;;;;;;;;;oDAOtD,cAAc,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK,OAAO,kBACzC,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;;4DACpB,KAAK,OAAO,CAAC,OAAO,kBACnB,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO;gEAAgC;0EAE/C,KAAK,OAAO,CAAC,OAAO;;;;;;4DAKxB,KAAK,OAAO,CAAC,IAAI,kBAChB,8OAAC;;kFACC,8OAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,OAAO;wEAA8B;;0FAE9C,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAAY;;;;;;;kFAG/B,8OAAC;wEACC,WAAU;wEACV,OAAO;4EACL,iBAAiB;4EACjB,aAAa;4EACb,OAAO;wEACT;;0FAEA,8OAAC;gFAAE,WAAU;0FAAU;;;;;;0FACvB,8OAAC;gFAAE,WAAU;gFAAe,OAAO;oFAAE,OAAO;gFAA+B;0FAAG;;;;;;;;;;;;;;;;;;4DAQnF,KAAK,OAAO,CAAC,aAAa,kBACzB,8OAAC;;kFACC,8OAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,OAAO;wEAA8B;;0FAE9C,8OAAC,kNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;4EAAY;;;;;;;kFAGnC,8OAAC;wEACC,WAAU;wEACV,OAAO;4EACL,iBAAiB;4EACjB,aAAa;4EACb,OAAO;wEACT;;0FAEA,8OAAC;gFAAE,WAAU;0FAAW,KAAK,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS;;;;;;0FACpE,8OAAC;gFAAE,WAAU;gFAAe,OAAO;oFAAE,OAAO;gFAA+B;0FAAG;;;;;;;;;;;;;;;;;;4DAQnF,KAAK,OAAO,CAAC,IAAI,kBAChB,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,WAAU;gFACV,OAAO;oFAAE,OAAO;gFAA8B;;kGAE9C,8OAAC,0MAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAAY;;;;;;;0FAG/B,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,kIAAA,CAAA,SAAM;wFACL,SAAQ;wFACR,MAAK;wFACL,SAAS,CAAC;4FACR,EAAE,eAAe;4FACjB,SAAS,KAAK,OAAO,CAAE,IAAI;wFAC7B;wFACA,WAAU;wFACV,OAAO;4FAAE,OAAO;wFAA+B;kGAE/C,cAAA,8OAAC,kMAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;;;;;;kGAElB,8OAAC,kIAAA,CAAA,SAAM;wFACL,SAAQ;wFACR,MAAK;wFACL,SAAS,CAAC;4FACR,EAAE,eAAe;4FACjB,oBAAoB,KAAK,EAAE;wFAC7B;wFACA,WAAU;wFACV,OAAO;4FAAE,OAAO;wFAA+B;kGAE/C,cAAA,8OAAC,gNAAA,CAAA,YAAS;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kFAI3B,8OAAC;wEACC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,GAAG,CAAC,KAAK,EAAE,IAAI,aAAa,YAC1C;wEACF,OAAO;4EACL,iBAAiB;4EACjB,aAAa;4EACb,OAAO;wEACT;kFAEA,cAAA,8OAAC;sFAAK,KAAK,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;+CAnK5B,KAAK,EAAE;;;;;;;;;;;;;;;gCAgLrB,cAAc,wBACb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDACC,WAAW,CAAC,iFAAiF,EAC3F,aACI,oBACA,IACJ;oDACF,OAAO;wDACL,iBAAiB,aAAa,6BAA6B;wDAC3D,aAAa,aAAa,YAAY;oDACxC;oDACA,aAAa;oDACb,aAAa;oDACb,YAAY;oDACZ,QAAQ;oDACR,SAAS,IAAM,aAAa,OAAO,EAAE;;sEAErC,8OAAC,sMAAA,CAAA,SAAM;4DACL,WAAU;4DACV,OAAO;gEAAE,OAAO;4DAA+B;;;;;;sEAEjD,8OAAC;4DAAE,WAAU;4DAAU,OAAO;gEAAE,OAAO;4DAA8B;;gEAAG;8EACnD,8OAAC;oEAAK,WAAU;8EAAoC;;;;;;;;;;;;sEAEzE,8OAAC;4DAAE,WAAU;4DAAe,OAAO;gEAAE,OAAO;4DAA+B;sEAAG;;;;;;sEAG9E,8OAAC;4DACC,KAAK;4DACL,MAAK;4DACL,QAAO;4DACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4DAChD,WAAU;;;;;;;;;;;;gDAKb,gCACC,8OAAC;oDACC,WAAU;oDACV,OAAO;wDACL,iBAAiB;wDACjB,aAAa;oDACf;;sEAEA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;oEAAU,OAAO;wEAAE,OAAO;oEAAgC;8EAAG;;;;;;8EAG7E,8OAAC;oEAAK,WAAU;oEAAU,OAAO;wEAAE,OAAO;oEAA+B;;wEACtE,eAAe,UAAU;wEAAC;;;;;;;;;;;;;sEAG/B,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,OAAO,eAAe,UAAU;4DAAE,WAAU;;;;;;;;;;;;gDAKzD,cAAc,MAAM,GAAG,mBACtB,8OAAC;oDAAI,WAAU;8DACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;4DAEC,WAAU;4DACV,OAAO;gEACL,iBAAiB;gEACjB,aAAa;4DACf;4DACA,cAAc,CAAC;gEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4DAC1C;4DACA,cAAc,CAAC;gEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4DAC1C;;8EAEA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;oEAAwB,OAAO;wEAAE,OAAO;oEAAsB;;;;;;8EAC9E,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;4EAA+B,OAAO;gFAAE,OAAO;4EAA8B;sFACvF,KAAK,QAAQ;;;;;;sFAEhB,8OAAC;4EAAE,WAAU;4EAAU,OAAO;gFAAE,OAAO;4EAA+B;sFACnE,eAAe,KAAK,IAAI;;;;;;;;;;;;8EAG7B,8OAAC;oEAAI,WAAU;;wEACZ,KAAK,MAAM,KAAK,6BACf,8OAAC;4EACC,WAAU;4EACV,OAAO;gFAAE,iBAAiB;4EAAU;;;;;;sFAGxC,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,iBAAiB,KAAK,EAAE;4EACvC,WAAU;4EACV,OAAO;gFAAE,OAAO;4EAA+B;4EAC/C,cAAc,CAAC;gFACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4EAChC;4EACA,cAAc,CAAC;gFACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4EAChC;sFAEA,cAAA,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;;2DA1CjB,KAAK,EAAE;;;;;;;;;;;;;;;;wCAoDrB,8BACC,8OAAC,gIAAA,CAAA,OAAI;4CACH,WAAU;4CACV,OAAO;gDACL,iBAAiB;gDACjB,aAAa;4CACf;;8DAEA,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAwC;;;;;;;;;;;8DAGtE,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;kEACZ;4DAAC;4DAAG;4DAAG;4DAAG;4DAAG;yDAAE,CAAC,GAAG,CAAC,CAAA,kBACnB,8OAAC;gEAEC,WAAU;gEACV,OAAO;oEAAE,iBAAiB;gEAAoB;+DAFzC;;;;;;;;;;;;;;;;;;;;mDAQb,eAAe,YAAY,SAAS,GAAG,kBACzC,8OAAC,gIAAA,CAAA,OAAI;4CACH,WAAU;4CACV,OAAO;gDACL,iBAAiB;gDACjB,aAAa;4CACf;;8DAEA,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;;sEACpB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gIAAA,CAAA,YAAS;oEACR,WAAU;oEACV,OAAO;wEAAE,OAAO;oEAA8B;8EAC/C;;;;;;8EAGD,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,OAAO;4EAAE,OAAO;wEAAgC;wEAChD,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wEAC1C;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wEAC1C;;0FAEA,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;sEAK3C,8OAAC;4DAAE,WAAU;4DAAU,OAAO;gEAAE,OAAO;4DAA+B;;gEACnE,YAAY,SAAS;gEAAC;gEAAS,YAAY,OAAO,CAAC,MAAM;gEAAC;;;;;;;;;;;;;8DAG/D,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;8EACC,cAAA,8OAAC;wEAAG,OAAO;4EAAE,cAAc,CAAC,+BAA+B,CAAC;wEAAC;kFAC1D,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,8OAAC;gFAEC,WAAU;gFACV,OAAO;oFACL,OAAO;oFACP,iBAAiB;gFACnB;;oFAEC,OAAO,IAAI;kGACZ,8OAAC;wFAAK,WAAU;wFAAO,OAAO;4FAAE,OAAO;wFAA+B;;4FAAG;4FACrE,OAAO,IAAI;4FAAC;;;;;;;;+EATX,OAAO,IAAI;;;;;;;;;;;;;;;8EAexB,8OAAC;8EACE,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC1B,8OAAC;4EAEC,WAAU;4EACV,OAAO;gFAAE,cAAc,CAAC,+BAA+B,CAAC;4EAAC;4EACzD,cAAc,CAAC;gFACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4EAC1C;4EACA,cAAc,CAAC;gFACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4EAC1C;sFAEC,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,8OAAC;oFAEC,WAAU;oFACV,OAAO;wFAAE,OAAO;oFAAgC;8FAE/C,gBAAgB,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI;mFAJzC,OAAO,IAAI;;;;;2EAZf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDA0BjB,cAAc,MAAM,KAAK,kBAC3B,8OAAC,gIAAA,CAAA,OAAI;4CACH,WAAU;4CACV,OAAO;gDACL,iBAAiB;gDACjB,aAAa;4CACf;sDAEA,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC,sMAAA,CAAA,SAAM;wDACL,WAAU;wDACV,OAAO;4DAAE,OAAO;wDAA+B;;;;;;kEAEjD,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO;wDAA8B;kEAC/C;;;;;;kEAGD,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO;wDAAgC;kEACjD;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,IAAM,aAAa,OAAO,EAAE;wDACrC,WAAU;wDACV,OAAO;4DACL,iBAAiB;4DACjB,OAAO;wDACT;wDACA,cAAc,CAAC;4DACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wDAC1C;wDACA,cAAc,CAAC;4DACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wDAC1C;;0EAEA,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;iEAMzC,8OAAC,gIAAA,CAAA,OAAI;4CACH,WAAU;4CACV,OAAO;gDACL,iBAAiB;gDACjB,aAAa;4CACf;sDAEA,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAE,OAAO;wDAAE,OAAO;oDAAgC;8DAAG;;;;;;;;;;;;;;;;;;;;;;gCAU/D,cAAc,0BACb,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;gCAM/C,cAAc,wBACb,8OAAC;oCAAI,WAAU;8CACZ,cACE,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,EAAE,MAC7B,GAAG,CAAC,CAAC,qBACJ,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO;4DAA8B;;8EAE9C,8OAAC,0MAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,KAAK,KAAK;gEAAC;;;;;;;sEAEd,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,SAAS,KAAK,OAAO,CAAE,IAAI;4DAC1C,WAAU;4DACV,OAAO;gEACL,aAAa;gEACb,OAAO;4DACT;;8EAEA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAIrC,8OAAC;oDACC,WAAU;oDACV,OAAO;wDACL,iBAAiB;wDACjB,aAAa;wDACb,OAAO;oDACT;8DAEA,cAAA,8OAAC;kEAAK,KAAK,OAAO,CAAE,IAAI;;;;;;;;;;;;2CA/BlB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YA0C9B,4BACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB;oBACjB,aAAa;gBACf;;kCAGA,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,aAAa;wBAAwB;;0CAE9C,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAA8B;0CAC/C;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM;gCACf,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAA+B;0CAE/C,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO;4BAA+B;sCAChD;;;;;;;;;;;kCAMH,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,aAAa;wBAAwB;kCAE9C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,aAAa;wCACb,OAAO;oCACT;oCACA,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,WAAW,YAAY,IAAI,IAAI;4CAC3C;wCACF;oCACF;;;;;;8CAEF,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC,YAAY,IAAI;oCAC3B,MAAK;oCACL,WAAU;oCACV,OAAO;wCACL,iBAAiB,YAAY,IAAI,KAAK,4BAA4B;wCAClE,OAAO,YAAY,IAAI,KAAK,gCAAgC;oCAC9D;8CAEA,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;uCAEe", "debugId": null}}, {"offset": {"line": 4936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/ai-workflows/AIWorkflowsPageContent.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback, useEffect, useMemo } from 'react';\r\nimport { usePageTitle } from '@/hooks/usePageTitle';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\nimport { Brain } from 'lucide-react';\r\nimport AnalysisProjectList from './AnalysisProjectList';\r\nimport AnalysisProjectView from './AnalysisProjectView';\r\nimport { useApi } from '@/providers/ApiContext';\r\nimport { AnalysisProject } from '@/types';\r\n\r\ninterface AnalysisNavigationState {\r\n  currentView: 'list' | 'project';\r\n  selectedProject: AnalysisProject | null;\r\n  breadcrumbs: Array<{\r\n    label: string;\r\n    onClick?: () => void;\r\n  }>;\r\n}\r\n\r\nconst AIWorkflowsPageContent = () => {\r\n  // API context\r\n  const {\r\n    listAnalysisProjects,\r\n    createAnalysisProject,\r\n    updateAnalysisProject,\r\n    deleteAnalysisProject,\r\n  } = useApi();\r\n\r\n  // Navigation state\r\n  const [navigationState, setNavigationState] = useState<AnalysisNavigationState>({\r\n    currentView: 'list',\r\n    selectedProject: null,\r\n    breadcrumbs: [{ label: 'Analysis Projects' }],\r\n  });\r\n\r\n  // Analysis projects data\r\n  const [analysisProjects, setAnalysisProjects] = useState<AnalysisProject[]>([]);\r\n  const [isLoadingProjects, setIsLoadingProjects] = useState(false);\r\n  const [isCreatingProject, setIsCreatingProject] = useState(false);\r\n\r\n  // Chat state for project view\r\n  const [isChatOpen, setIsChatOpen] = useState(false);\r\n\r\n  const { setPageActions } = usePageHeader();\r\n\r\n  // Load analysis projects on component mount\r\n  useEffect(() => {\r\n    loadAnalysisProjects();\r\n  }, []);\r\n\r\n  const loadAnalysisProjects = useCallback(async () => {\r\n    setIsLoadingProjects(true);\r\n    try {\r\n      const response = await listAnalysisProjects();\r\n      if (response.success && response.data) {\r\n        // Convert date strings to Date objects for compatibility\r\n        const projects = response.data.projects.map((project: any) => ({\r\n          ...project,\r\n          createdAt: new Date(project.createdAt),\r\n          updatedAt: new Date(project.updatedAt),\r\n        }));\r\n        setAnalysisProjects(projects);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load analysis projects:', error);\r\n      setAnalysisProjects([]); // Set to empty array on error\r\n    } finally {\r\n      setIsLoadingProjects(false);\r\n    }\r\n  }, [listAnalysisProjects]);\r\n\r\n  // Navigation handlers\r\n  const handleSelectProject = useCallback((project: AnalysisProject) => {\r\n    setNavigationState({\r\n      currentView: 'project',\r\n      selectedProject: project,\r\n      breadcrumbs: [\r\n        { label: 'Analysis Projects', onClick: () => handleNavigateBack() },\r\n        { label: project.name },\r\n      ],\r\n    });\r\n  }, []);\r\n\r\n  const handleNavigateBack = useCallback(() => {\r\n    setNavigationState({\r\n      currentView: 'list',\r\n      selectedProject: null,\r\n      breadcrumbs: [{ label: 'Analysis Projects' }],\r\n    });\r\n  }, []);\r\n\r\n  // Set page title dynamically based on navigation state\r\n  const pageConfig = useMemo(() => ({\r\n    title: navigationState.currentView === 'project' && navigationState.selectedProject \r\n      ? navigationState.selectedProject.name\r\n      : 'Analysis Projects',\r\n    icon: Brain,\r\n    breadcrumbs: navigationState.currentView === 'project' && navigationState.selectedProject\r\n      ? [\r\n          { label: 'Analysis Projects', onClick: () => handleNavigateBack() },\r\n          { label: navigationState.selectedProject.name },\r\n        ]\r\n      : [{ label: 'Analysis Projects' }],\r\n  }), [navigationState.currentView, navigationState.selectedProject, handleNavigateBack]);\r\n\r\n  usePageTitle(pageConfig);\r\n\r\n  // Project CRUD handlers\r\n  const handleCreateProject = useCallback(async () => {\r\n    setIsCreatingProject(true);\r\n    try {\r\n      // Create project with default name\r\n      const defaultName = `Analysis Project ${analysisProjects.length + 1}`;\r\n      const request = {\r\n        name: defaultName,\r\n        description: '',\r\n      };\r\n\r\n      const response = await createAnalysisProject(request);\r\n      \r\n      if (response.success && response.data) {\r\n        // Convert date strings to Date objects\r\n        const newProject = {\r\n          ...response.data,\r\n          createdAt: new Date(response.data.createdAt),\r\n          updatedAt: new Date(response.data.updatedAt),\r\n        };\r\n\r\n        setAnalysisProjects(prev => [...prev, newProject]);\r\n\r\n        // Immediately navigate to the new project\r\n        handleSelectProject(newProject);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to create analysis project:', error);\r\n    } finally {\r\n      setIsCreatingProject(false);\r\n    }\r\n  }, [analysisProjects.length, createAnalysisProject, handleSelectProject]);\r\n\r\n  const handleUpdateProject = useCallback(async (projectId: string, updates: Partial<AnalysisProject>) => {\r\n    try {\r\n      const response = await updateAnalysisProject(projectId, updates);\r\n      \r\n      if (response.success && response.data) {\r\n        // Convert date strings to Date objects\r\n        const updatedProject = {\r\n          ...response.data,\r\n          createdAt: new Date(response.data.createdAt),\r\n          updatedAt: new Date(response.data.updatedAt),\r\n        };\r\n\r\n        setAnalysisProjects(prev => prev.map(p =>\r\n          p.id === projectId ? updatedProject : p\r\n        ));\r\n\r\n        // Update the selected project if it's the one being edited\r\n        if (navigationState.selectedProject?.id === projectId) {\r\n          setNavigationState(prev => ({\r\n            ...prev,\r\n            selectedProject: updatedProject,\r\n            breadcrumbs: [\r\n              { label: 'Analysis Projects', onClick: () => handleNavigateBack() },\r\n              { label: updatedProject.name },\r\n            ],\r\n          }));\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to update analysis project:', error);\r\n    }\r\n  }, [updateAnalysisProject, navigationState.selectedProject, handleNavigateBack]);\r\n\r\n  const handleDeleteProject = useCallback(async (projectId: string) => {\r\n    if (!confirm('Are you sure you want to delete this analysis project? This action cannot be undone.')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await deleteAnalysisProject(projectId);\r\n      \r\n      if (response.success) {\r\n        setAnalysisProjects(prev => prev.filter(p => p.id !== projectId));\r\n\r\n        // If we're currently viewing the deleted project, navigate back\r\n        if (navigationState.selectedProject?.id === projectId) {\r\n          handleNavigateBack();\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete analysis project:', error);\r\n    }\r\n  }, [deleteAnalysisProject, navigationState.selectedProject, handleNavigateBack]);\r\n\r\n  // Update page actions when navigation state changes\r\n  useEffect(() => {\r\n    const isListView = navigationState.currentView === 'list';\r\n    const isProjectView = navigationState.currentView === 'project' && navigationState.selectedProject;\r\n    \r\n    setPageActions({\r\n      // Show New Analysis button only on list view\r\n      onCreateAnalysis: isListView ? handleCreateProject : undefined,\r\n      isCreatingAnalysis: isListView ? isCreatingProject : false,\r\n      \r\n      // Show export and chat actions only on project view\r\n      onExport: isProjectView ? () => {\r\n        console.log('Exporting project data...');\r\n        // TODO: Implement export functionality\r\n      } : undefined,\r\n      onToggleChat: isProjectView ? () => setIsChatOpen(!isChatOpen) : undefined,\r\n      isChatOpen: isProjectView ? isChatOpen : false,\r\n    });\r\n  }, [navigationState.currentView, navigationState.selectedProject, setPageActions, handleCreateProject, isCreatingProject, isChatOpen]);\r\n\r\n  return (\r\n    <div\r\n      className=\"min-h-screen\"\r\n      style={{ backgroundColor: 'var(--sidebar-bg)' }}\r\n    >\r\n      <div className=\"container mx-auto p-6 space-y-6\">\r\n\r\n        {/* Main Content */}\r\n        {navigationState.currentView === 'list' ? (\r\n          <AnalysisProjectList\r\n            projects={analysisProjects}\r\n            onSelectProject={handleSelectProject}\r\n            onDeleteProject={handleDeleteProject}\r\n            isLoading={isLoadingProjects}\r\n          />\r\n        ) : navigationState.selectedProject ? (\r\n          <AnalysisProjectView\r\n            project={navigationState.selectedProject}\r\n            onUpdateProject={handleUpdateProject}\r\n            isChatOpen={isChatOpen}\r\n            onToggleChat={() => setIsChatOpen(!isChatOpen)}\r\n          />\r\n        ) : null}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AIWorkflowsPageContent;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAoBA,MAAM,yBAAyB;IAC7B,cAAc;IACd,MAAM,EACJ,oBAAoB,EACpB,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACtB,GAAG,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD;IAET,mBAAmB;IACnB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;QAC9E,aAAa;QACb,iBAAiB;QACjB,aAAa;YAAC;gBAAE,OAAO;YAAoB;SAAE;IAC/C;IAEA,yBAAyB;IACzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,8BAA8B;IAC9B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAEvC,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,qBAAqB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM;YACvB,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,yDAAyD;gBACzD,MAAM,WAAW,SAAS,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,UAAiB,CAAC;wBAC7D,GAAG,OAAO;wBACV,WAAW,IAAI,KAAK,QAAQ,SAAS;wBACrC,WAAW,IAAI,KAAK,QAAQ,SAAS;oBACvC,CAAC;gBACD,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,oBAAoB,EAAE,GAAG,8BAA8B;QACzD,SAAU;YACR,qBAAqB;QACvB;IACF,GAAG;QAAC;KAAqB;IAEzB,sBAAsB;IACtB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,mBAAmB;YACjB,aAAa;YACb,iBAAiB;YACjB,aAAa;gBACX;oBAAE,OAAO;oBAAqB,SAAS,IAAM;gBAAqB;gBAClE;oBAAE,OAAO,QAAQ,IAAI;gBAAC;aACvB;QACH;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,mBAAmB;YACjB,aAAa;YACb,iBAAiB;YACjB,aAAa;gBAAC;oBAAE,OAAO;gBAAoB;aAAE;QAC/C;IACF,GAAG,EAAE;IAEL,uDAAuD;IACvD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAChC,OAAO,gBAAgB,WAAW,KAAK,aAAa,gBAAgB,eAAe,GAC/E,gBAAgB,eAAe,CAAC,IAAI,GACpC;YACJ,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa,gBAAgB,WAAW,KAAK,aAAa,gBAAgB,eAAe,GACrF;gBACE;oBAAE,OAAO;oBAAqB,SAAS,IAAM;gBAAqB;gBAClE;oBAAE,OAAO,gBAAgB,eAAe,CAAC,IAAI;gBAAC;aAC/C,GACD;gBAAC;oBAAE,OAAO;gBAAoB;aAAE;QACtC,CAAC,GAAG;QAAC,gBAAgB,WAAW;QAAE,gBAAgB,eAAe;QAAE;KAAmB;IAEtF,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;IAEb,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,qBAAqB;QACrB,IAAI;YACF,mCAAmC;YACnC,MAAM,cAAc,CAAC,iBAAiB,EAAE,iBAAiB,MAAM,GAAG,GAAG;YACrE,MAAM,UAAU;gBACd,MAAM;gBACN,aAAa;YACf;YAEA,MAAM,WAAW,MAAM,sBAAsB;YAE7C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,uCAAuC;gBACvC,MAAM,aAAa;oBACjB,GAAG,SAAS,IAAI;oBAChB,WAAW,IAAI,KAAK,SAAS,IAAI,CAAC,SAAS;oBAC3C,WAAW,IAAI,KAAK,SAAS,IAAI,CAAC,SAAS;gBAC7C;gBAEA,oBAAoB,CAAA,OAAQ;2BAAI;wBAAM;qBAAW;gBAEjD,0CAA0C;gBAC1C,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD,SAAU;YACR,qBAAqB;QACvB;IACF,GAAG;QAAC,iBAAiB,MAAM;QAAE;QAAuB;KAAoB;IAExE,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB;QAChE,IAAI;YACF,MAAM,WAAW,MAAM,sBAAsB,WAAW;YAExD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,uCAAuC;gBACvC,MAAM,iBAAiB;oBACrB,GAAG,SAAS,IAAI;oBAChB,WAAW,IAAI,KAAK,SAAS,IAAI,CAAC,SAAS;oBAC3C,WAAW,IAAI,KAAK,SAAS,IAAI,CAAC,SAAS;gBAC7C;gBAEA,oBAAoB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACnC,EAAE,EAAE,KAAK,YAAY,iBAAiB;gBAGxC,2DAA2D;gBAC3D,IAAI,gBAAgB,eAAe,EAAE,OAAO,WAAW;oBACrD,mBAAmB,CAAA,OAAQ,CAAC;4BAC1B,GAAG,IAAI;4BACP,iBAAiB;4BACjB,aAAa;gCACX;oCAAE,OAAO;oCAAqB,SAAS,IAAM;gCAAqB;gCAClE;oCAAE,OAAO,eAAe,IAAI;gCAAC;6BAC9B;wBACH,CAAC;gBACH;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF,GAAG;QAAC;QAAuB,gBAAgB,eAAe;QAAE;KAAmB;IAE/E,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC7C,IAAI,CAAC,QAAQ,yFAAyF;YACpG;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,sBAAsB;YAE7C,IAAI,SAAS,OAAO,EAAE;gBACpB,oBAAoB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAEtD,gEAAgE;gBAChE,IAAI,gBAAgB,eAAe,EAAE,OAAO,WAAW;oBACrD;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF,GAAG;QAAC;QAAuB,gBAAgB,eAAe;QAAE;KAAmB;IAE/E,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,gBAAgB,WAAW,KAAK;QACnD,MAAM,gBAAgB,gBAAgB,WAAW,KAAK,aAAa,gBAAgB,eAAe;QAElG,eAAe;YACb,6CAA6C;YAC7C,kBAAkB,aAAa,sBAAsB;YACrD,oBAAoB,aAAa,oBAAoB;YAErD,oDAAoD;YACpD,UAAU,gBAAgB;gBACxB,QAAQ,GAAG,CAAC;YACZ,uCAAuC;YACzC,IAAI;YACJ,cAAc,gBAAgB,IAAM,cAAc,CAAC,cAAc;YACjE,YAAY,gBAAgB,aAAa;QAC3C;IACF,GAAG;QAAC,gBAAgB,WAAW;QAAE,gBAAgB,eAAe;QAAE;QAAgB;QAAqB;QAAmB;KAAW;IAErI,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YAAE,iBAAiB;QAAoB;kBAE9C,cAAA,8OAAC;YAAI,WAAU;sBAGZ,gBAAgB,WAAW,KAAK,uBAC/B,8OAAC,wKAAA,CAAA,UAAmB;gBAClB,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;gBACjB,WAAW;;;;;uBAEX,gBAAgB,eAAe,iBACjC,8OAAC,wKAAA,CAAA,UAAmB;gBAClB,SAAS,gBAAgB,eAAe;gBACxC,iBAAiB;gBACjB,YAAY;gBACZ,cAAc,IAAM,cAAc,CAAC;;;;;uBAEnC;;;;;;;;;;;AAIZ;uCAEe", "debugId": null}}, {"offset": {"line": 5212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/onboarding.ts"], "sourcesContent": ["// Onboarding utility functions\r\n\r\nimport { ROUTES } from '@/lib/constants';\r\n\r\n/**\r\n * Determines the appropriate redirect path based on user authentication and onboarding status\r\n */\r\nexport function getRedirectPath(\r\n  isAuthenticated: boolean,\r\n  isNewUser: boolean,\r\n  currentPath: string\r\n): string | null {\r\n  // Not authenticated - redirect to login (except for public routes)\r\n  if (!isAuthenticated) {\r\n    const publicRoutes = [\r\n      ROUTES.HOME,\r\n      ROUTES.LOGIN,\r\n      ROUTES.REGISTER,\r\n      ROUTES.AUTH.CALLBACK,\r\n      ROUTES.OAUTH.CALLBACK,\r\n      ROUTES.ONBOARDING,\r\n    ];\r\n\r\n    if (!publicRoutes.includes(currentPath as any)) {\r\n      return ROUTES.LOGIN;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // Authenticated user scenarios\r\n  if (isAuthenticated) {\r\n    // New user not on onboarding page - redirect to onboarding\r\n    if (isNewUser && currentPath !== ROUTES.ONBOARDING) {\r\n      const publicRoutes = [\r\n        ROUTES.HOME,\r\n        ROUTES.LOGIN,\r\n        ROUTES.REGISTER,\r\n        ROUTES.AUTH.CALLBACK,\r\n        ROUTES.OAUTH.CALLBACK,\r\n      ];\r\n\r\n      // Don't redirect if on public routes (except login)\r\n      if (!publicRoutes.includes(currentPath as any) || currentPath === ROUTES.LOGIN) {\r\n        return ROUTES.ONBOARDING;\r\n      }\r\n    }\r\n    \r\n    // Existing user on onboarding page - redirect to dashboard\r\n    if (!isNewUser && currentPath === ROUTES.ONBOARDING) {\r\n      return ROUTES.DASHBOARD;\r\n    }\r\n    \r\n    // Authenticated user on login or register page - redirect based on onboarding status\r\n    if (currentPath === ROUTES.LOGIN || currentPath === ROUTES.REGISTER) {\r\n      return isNewUser ? ROUTES.ONBOARDING : ROUTES.DASHBOARD;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Checks if a route requires authentication\r\n */\r\nexport function isProtectedRoute(path: string): boolean {\r\n  const publicRoutes = [\r\n    ROUTES.HOME,\r\n    ROUTES.LOGIN,\r\n    ROUTES.REGISTER,\r\n    ROUTES.AUTH.CALLBACK,\r\n    ROUTES.OAUTH.CALLBACK,\r\n    ROUTES.ONBOARDING,\r\n  ];\r\n\r\n  return !publicRoutes.includes(path as any);\r\n}\r\n\r\n/**\r\n * Checks if a route is accessible to new users\r\n */\r\nexport function isNewUserAccessibleRoute(path: string): boolean {\r\n  const newUserRoutes = [\r\n    ROUTES.HOME,\r\n    ROUTES.LOGIN,\r\n    ROUTES.REGISTER,\r\n    ROUTES.AUTH.CALLBACK,\r\n    ROUTES.OAUTH.CALLBACK,\r\n    ROUTES.ONBOARDING,\r\n  ];\r\n\r\n  return newUserRoutes.includes(path as any);\r\n}\r\n\r\n/**\r\n * Gets the next step in the onboarding flow after completion\r\n */\r\nexport function getPostOnboardingRedirect(): string {\r\n  return ROUTES.DASHBOARD;\r\n}\r\n\r\n/**\r\n * Validates if onboarding completion is allowed from the current state\r\n */\r\nexport function canCompleteOnboarding(\r\n  isAuthenticated: boolean,\r\n  isNewUser: boolean,\r\n  currentPath: string\r\n): boolean {\r\n  return isAuthenticated && isNewUser && currentPath === ROUTES.ONBOARDING;\r\n}\r\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;AAE/B;AAAA;;AAKO,SAAS,gBACd,eAAwB,EACxB,SAAkB,EAClB,WAAmB;IAEnB,mEAAmE;IACnE,IAAI,CAAC,iBAAiB;QACpB,MAAM,eAAe;YACnB,iIAAA,CAAA,SAAM,CAAC,IAAI;YACX,iIAAA,CAAA,SAAM,CAAC,KAAK;YACZ,iIAAA,CAAA,SAAM,CAAC,QAAQ;YACf,iIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;YACpB,iIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YACrB,iIAAA,CAAA,SAAM,CAAC,UAAU;SAClB;QAED,IAAI,CAAC,aAAa,QAAQ,CAAC,cAAqB;YAC9C,OAAO,iIAAA,CAAA,SAAM,CAAC,KAAK;QACrB;QACA,OAAO;IACT;IAEA,+BAA+B;IAC/B,IAAI,iBAAiB;QACnB,2DAA2D;QAC3D,IAAI,aAAa,gBAAgB,iIAAA,CAAA,SAAM,CAAC,UAAU,EAAE;YAClD,MAAM,eAAe;gBACnB,iIAAA,CAAA,SAAM,CAAC,IAAI;gBACX,iIAAA,CAAA,SAAM,CAAC,KAAK;gBACZ,iIAAA,CAAA,SAAM,CAAC,QAAQ;gBACf,iIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;gBACpB,iIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;aACtB;YAED,oDAAoD;YACpD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAuB,gBAAgB,iIAAA,CAAA,SAAM,CAAC,KAAK,EAAE;gBAC9E,OAAO,iIAAA,CAAA,SAAM,CAAC,UAAU;YAC1B;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAAC,aAAa,gBAAgB,iIAAA,CAAA,SAAM,CAAC,UAAU,EAAE;YACnD,OAAO,iIAAA,CAAA,SAAM,CAAC,SAAS;QACzB;QAEA,qFAAqF;QACrF,IAAI,gBAAgB,iIAAA,CAAA,SAAM,CAAC,KAAK,IAAI,gBAAgB,iIAAA,CAAA,SAAM,CAAC,QAAQ,EAAE;YACnE,OAAO,YAAY,iIAAA,CAAA,SAAM,CAAC,UAAU,GAAG,iIAAA,CAAA,SAAM,CAAC,SAAS;QACzD;IACF;IAEA,OAAO;AACT;AAKO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,eAAe;QACnB,iIAAA,CAAA,SAAM,CAAC,IAAI;QACX,iIAAA,CAAA,SAAM,CAAC,KAAK;QACZ,iIAAA,CAAA,SAAM,CAAC,QAAQ;QACf,iIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;QACpB,iIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QACrB,iIAAA,CAAA,SAAM,CAAC,UAAU;KAClB;IAED,OAAO,CAAC,aAAa,QAAQ,CAAC;AAChC;AAKO,SAAS,yBAAyB,IAAY;IACnD,MAAM,gBAAgB;QACpB,iIAAA,CAAA,SAAM,CAAC,IAAI;QACX,iIAAA,CAAA,SAAM,CAAC,KAAK;QACZ,iIAAA,CAAA,SAAM,CAAC,QAAQ;QACf,iIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;QACpB,iIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QACrB,iIAAA,CAAA,SAAM,CAAC,UAAU;KAClB;IAED,OAAO,cAAc,QAAQ,CAAC;AAChC;AAKO,SAAS;IACd,OAAO,iIAAA,CAAA,SAAM,CAAC,SAAS;AACzB;AAKO,SAAS,sBACd,eAAwB,EACxB,SAAkB,EAClB,WAAmB;IAEnB,OAAO,mBAAmB,aAAa,gBAAgB,iIAAA,CAAA,SAAM,CAAC,UAAU;AAC1E", "debugId": null}}, {"offset": {"line": 5300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useAuth } from '@/providers/AuthContext';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { isProtectedRoute } from '@/lib/utils/onboarding';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requireAuth?: boolean;\n  redirectTo?: string;\n}\n\nexport default function ProtectedRoute({ \n  children, \n  requireAuth = true, \n  redirectTo = '/login' \n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading, isNewUser, signIn } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isCheckingAuth, setIsCheckingAuth] = useState(true);\n  const [hasTriedAutoAuth, setHasTriedAutoAuth] = useState(false);\n\n  useEffect(() => {\n    const checkAuthentication = async () => {\n      // Skip auth check for public routes\n      if (!requireAuth || !isProtectedRoute(pathname)) {\n        setIsCheckingAuth(false);\n        return;\n      }\n\n      // If already authenticated, handle onboarding redirect\n      if (isAuthenticated) {\n        if (isNewUser && pathname !== '/onboarding') {\n          console.log('New user on protected route, redirecting to onboarding');\n          router.push('/onboarding');\n          return;\n        }\n        if (!isNewUser && pathname === '/onboarding') {\n          console.log('Existing user on onboarding, redirecting to dashboard');\n          router.push('/dashboard');\n          return;\n        }\n        setIsCheckingAuth(false);\n        return;\n      }\n\n      // If not authenticated and haven't tried auto-auth yet, try it once\n      if (!isAuthenticated && !hasTriedAutoAuth && !isLoading) {\n        setHasTriedAutoAuth(true);\n        console.log('Attempting automatic authentication from stored tokens...');\n\n        try {\n          await signIn(undefined, false); // Don't redirect, just authenticate\n          // If successful, the useEffect will re-run due to isAuthenticated change\n        } catch (error) {\n          console.log('Auto-authentication failed, redirecting to login');\n          router.push(redirectTo);\n        }\n        return;\n      }\n\n      // If not authenticated and already tried auto-auth, redirect to login\n      if (!isAuthenticated && hasTriedAutoAuth && !isLoading) {\n        console.log('Not authenticated, redirecting to login');\n        router.push(redirectTo);\n        return;\n      }\n\n      setIsCheckingAuth(false);\n    };\n\n    checkAuthentication();\n  }, [isAuthenticated, isLoading, isNewUser, pathname, requireAuth, redirectTo, router, signIn, hasTriedAutoAuth]);\n\n  // Show loading state while checking authentication\n  if (isCheckingAuth || isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">Checking authentication...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // For protected routes, only render children if authenticated\n  if (requireAuth && !isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold mb-4\">Authentication Required</h2>\n          <p className=\"text-muted-foreground mb-4\">Please sign in to access this page.</p>\n          <button\n            onClick={() => router.push(redirectTo)}\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n          >\n            Sign In\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return <>{children}</>;\n}\n\n// Higher-order component for easy wrapping\nexport function withProtectedRoute<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: Omit<ProtectedRouteProps, 'children'>\n) {\n  return function ProtectedComponent(props: P) {\n    return (\n      <ProtectedRoute {...options}>\n        <Component {...props} />\n      </ProtectedRoute>\n    );\n  };\n}\n\n// Hook for manual authentication checks\nexport function useRequireAuth(redirectTo: string = '/login') {\n  const { isAuthenticated, isLoading, signIn } = useAuth();\n  const router = useRouter();\n  const [isChecking, setIsChecking] = useState(true);\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (isLoading) return;\n\n      if (!isAuthenticated) {\n        try {\n          await signIn(undefined, false); // Don't redirect, just authenticate\n        } catch {\n          router.push(redirectTo);\n        }\n      }\n      setIsChecking(false);\n    };\n\n    checkAuth();\n  }, [isAuthenticated, isLoading, signIn, router, redirectTo]);\n\n  return { isAuthenticated, isLoading: isLoading || isChecking };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAae,SAAS,eAAe,EACrC,QAAQ,EACR,cAAc,IAAI,EAClB,aAAa,QAAQ,EACD;IACpB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD;IAChE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,oCAAoC;YACpC,IAAI,CAAC,eAAe,CAAC,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;gBAC/C,kBAAkB;gBAClB;YACF;YAEA,uDAAuD;YACvD,IAAI,iBAAiB;gBACnB,IAAI,aAAa,aAAa,eAAe;oBAC3C,QAAQ,GAAG,CAAC;oBACZ,OAAO,IAAI,CAAC;oBACZ;gBACF;gBACA,IAAI,CAAC,aAAa,aAAa,eAAe;oBAC5C,QAAQ,GAAG,CAAC;oBACZ,OAAO,IAAI,CAAC;oBACZ;gBACF;gBACA,kBAAkB;gBAClB;YACF;YAEA,oEAAoE;YACpE,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,WAAW;gBACvD,oBAAoB;gBACpB,QAAQ,GAAG,CAAC;gBAEZ,IAAI;oBACF,MAAM,OAAO,WAAW,QAAQ,oCAAoC;gBACpE,yEAAyE;gBAC3E,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CAAC;oBACZ,OAAO,IAAI,CAAC;gBACd;gBACA;YACF;YAEA,sEAAsE;YACtE,IAAI,CAAC,mBAAmB,oBAAoB,CAAC,WAAW;gBACtD,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,kBAAkB;QACpB;QAEA;IACF,GAAG;QAAC;QAAiB;QAAW;QAAW;QAAU;QAAa;QAAY;QAAQ;QAAQ;KAAiB;IAE/G,mDAAmD;IACnD,IAAI,kBAAkB,WAAW;QAC/B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,8DAA8D;IAC9D,IAAI,eAAe,CAAC,iBAAiB;QACnC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBAAO;kBAAG;;AACZ;AAGO,SAAS,mBACd,SAAiC,EACjC,OAA+C;IAE/C,OAAO,SAAS,mBAAmB,KAAQ;QACzC,qBACE,8OAAC;YAAgB,GAAG,OAAO;sBACzB,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,aAAqB,QAAQ;IAC1D,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD;IACrD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI,WAAW;YAEf,IAAI,CAAC,iBAAiB;gBACpB,IAAI;oBACF,MAAM,OAAO,WAAW,QAAQ,oCAAoC;gBACtE,EAAE,OAAM;oBACN,OAAO,IAAI,CAAC;gBACd;YACF;YACA,cAAc;QAChB;QAEA;IACF,GAAG;QAAC;QAAiB;QAAW;QAAQ;QAAQ;KAAW;IAE3D,OAAO;QAAE;QAAiB,WAAW,aAAa;IAAW;AAC/D", "debugId": null}}, {"offset": {"line": 5512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/app/%28dashboard%29/ai-workflows/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport Layout from '@/components/layout/Layout';\r\nimport AIWorkflowsPageContent from '@/components/features/ai-workflows/AIWorkflowsPageContent';\r\nimport ProtectedRoute from '@/components/auth/ProtectedRoute';\r\n\r\nexport default function AIWorkflowsPage() {\r\n  return (\r\n    <ProtectedRoute>\r\n      <Layout>\r\n        <AIWorkflowsPageContent />\r\n      </Layout>\r\n    </ProtectedRoute>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAOe,SAAS;IACtB,qBACE,8OAAC,4IAAA,CAAA,UAAc;kBACb,cAAA,8OAAC,sIAAA,CAAA,UAAM;sBACL,cAAA,8OAAC,2KAAA,CAAA,UAAsB;;;;;;;;;;;;;;;AAI/B", "debugId": null}}]}