from typing import List, Optional, Dict, Any
from enum import Enum


class QueryType(Enum):
    """Types of queries for specialized formatting."""
    LIST_RANKING = "list_ranking"  # Top 10, most, least, etc.
    ANALYTICAL = "analytical"      # Patterns, trends, insights
    COMPARISON = "comparison"      # Compare A vs B
    SUMMARY = "summary"           # General data summary
    EXPLORATION = "exploration"   # What's in this table/database


class ResponseFormatter:
    """Enhanced utility class for ChatGPT-style response formatting.

    Provides structured, visually appealing responses with proper hierarchy,
    executive summaries, and engaging presentation similar to ChatGPT.
    """

    # Visual elements for better engagement
    _SECTION_SEPARATOR = "\n" + "─" * 40 + "\n"
    _SUBSECTION_SEPARATOR = "\n" + "·" * 25 + "\n"

    # Conversational elements
    _GREETINGS = {
        "conversational": "Here's what I found:",
        "professional": "Analysis Results:",
        "analytical": "Data Analysis Summary:",
        "friendly": "Great question! Here's what I discovered:"
    }

    _SIGN_OFFS = {
        "conversational": "\n\n**Want to explore further?** Feel free to ask follow-up questions or dive deeper into any specific aspect.",
        "professional": "\n\nPlease let me know if you need additional analysis.",
        "analytical": "\n\nFurther analysis can be provided upon request.",
        "friendly": "\n\n**Ready for more insights?** Just ask if you'd like to explore any part of this data further!"
    }

    @classmethod
    def format_data_response(
        cls,
        summary_text: str,
        query_type: QueryType = QueryType.SUMMARY,
        tone: str = "conversational",
        executive_summary: Optional[str] = None,
        key_insights: Optional[List[str]] = None,
        follow_up_suggestions: Optional[List[str]] = None,
        statistical_summary: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a comprehensive, ChatGPT-style data response.

        Args:
            summary_text: The main analysis content
            query_type: Type of query for specialized formatting
            tone: Response tone (conversational, professional, analytical, friendly)
            executive_summary: Brief overview of key findings
            key_insights: List of important insights
            follow_up_suggestions: Suggested follow-up questions
            statistical_summary: Statistical insights about the data

        Returns:
            Beautifully formatted markdown response
        """
        response_parts = []

        # 1. Greeting
        greeting = cls._GREETINGS.get(tone, cls._GREETINGS["conversational"])
        response_parts.append(f"{greeting}\n")

        # 2. Executive Summary (if provided and meaningful)
        if executive_summary and executive_summary.strip() and executive_summary != "Analysis Complete":
            response_parts.append("## Executive Summary")
            response_parts.append(f"{executive_summary}\n")

        # 3. Main Content with query-specific formatting
        if query_type == QueryType.LIST_RANKING:
            response_parts.append("## Results")
        elif query_type == QueryType.ANALYTICAL:
            response_parts.append("## Analysis & Insights")
        elif query_type == QueryType.COMPARISON:
            response_parts.append("## Comparison Results")
        else:
            response_parts.append("## Summary")

        response_parts.append(f"{summary_text}\n")

        # 4. Statistical Summary (if provided)
        if statistical_summary:
            response_parts.append("### Statistical Overview")
            stats_parts = []
            if statistical_summary.get("total_records"):
                stats_parts.append(f"**Total Records:** {statistical_summary['total_records']}")
            if statistical_summary.get("range"):
                stats_parts.append(f"**Range:** {statistical_summary['range']}")
            if statistical_summary.get("average"):
                stats_parts.append(f"**Average:** {statistical_summary['average']}")
            if statistical_summary.get("distribution"):
                stats_parts.append(f"**Distribution:** {statistical_summary['distribution']}")

            if stats_parts:
                response_parts.extend(stats_parts)
                response_parts.append("")

        # 5. Key Insights Section (if provided and meaningful)
        if key_insights and any(insight.strip() and insight != "Data retrieved successfully" for insight in key_insights):
            response_parts.append("### Key Insights")
            meaningful_insights = [insight for insight in key_insights[:3]
                                 if insight.strip() and insight != "Data retrieved successfully"]
            for i, insight in enumerate(meaningful_insights, 1):
                response_parts.append(f"{i}. {insight}")
            response_parts.append("")

        # 6. Follow-up Suggestions (if provided and specific)
        if follow_up_suggestions and len(follow_up_suggestions) > 0:
            # Filter out generic suggestions
            specific_suggestions = [s for s in follow_up_suggestions[:3]
                                  if not any(generic in s.lower() for generic in
                                           ["what genres", "what is the average", "from a specific year"])]
            if specific_suggestions:
                response_parts.append("### You might also want to explore:")
                for suggestion in specific_suggestions:
                    response_parts.append(f"• {suggestion}")
                response_parts.append("")

        # 7. Sign-off
        sign_off = cls._SIGN_OFFS.get(tone, cls._SIGN_OFFS["conversational"])
        response_parts.append(sign_off)

        return "\n".join(response_parts)

    @classmethod
    def success(cls, summary_text: str, *, tone: str = "conversational") -> str:
        """Legacy method - now uses enhanced formatting."""
        return cls.format_data_response(
            summary_text=summary_text,
            tone=tone
        )

    @classmethod
    def no_results(cls, query: str, explanation: str, tone: str = "conversational") -> str:
        """Create an enhanced no-results response."""
        response_parts = []

        # Header with appropriate emoji
        response_parts.append("## 🔍 Search Results")
        response_parts.append(f"I searched thoroughly for data related to **\"{query}\"**, but didn't find any matching results.\n")

        # Explanation section
        response_parts.append("### 🤔 Possible reasons:")
        response_parts.append(f"{explanation}\n")

        # Helpful suggestions
        response_parts.append("### 💡 What you can try:")
        response_parts.append("• **Rephrase your question** - try different keywords or terms")
        response_parts.append("• **Check database connections** - ensure the relevant databases are connected")
        response_parts.append("• **Broaden your search** - try a more general query first")
        response_parts.append("• **Check spelling** - verify table names, column names, or search terms")

        # Encouraging sign-off
        if tone == "conversational":
            response_parts.append("\n🚀 **Don't give up!** I'm here to help you find the data you need. Try rephrasing your question or let me know if you'd like suggestions for exploring your databases.")
        else:
            response_parts.append("\nPlease try a different approach or contact support if you continue to experience issues.")

        return "\n".join(response_parts)

    @classmethod
    def error(cls, action: str, error_message: str, tone: str = "conversational") -> str:
        """Create an enhanced error response."""
        response_parts = []

        # Header
        response_parts.append("## ⚠️ Something Went Wrong")

        if tone == "conversational":
            response_parts.append(f"I encountered an issue while **{action}**. Don't worry - let's get this sorted out! 🛠️\n")
        else:
            response_parts.append(f"An error occurred while **{action}**.\n")

        # Error details
        response_parts.append("### 🔧 Technical Details:")
        response_parts.append(f"```\n{error_message}\n```\n")

        # Next steps
        response_parts.append("### 🎯 What to do next:")
        response_parts.append("• **Try again** - sometimes temporary issues resolve themselves")
        response_parts.append("• **Simplify your request** - try a more basic version of your query")
        response_parts.append("• **Check your data** - verify database connections and permissions")

        # Sign-off
        if tone == "conversational":
            response_parts.append("\n💪 **I'm here to help!** Feel free to try a different approach or ask me to attempt something else.")
        else:
            response_parts.append("\nPlease try again or contact support if the issue persists.")

        return "\n".join(response_parts)

    @classmethod
    def ask_clarification(cls, questions: List[str], *, tone: str = "conversational") -> str:
        """Create an enhanced clarification request."""
        if not questions:
            return "Could you please provide more details about what you're looking for?"

        response_parts = []

        # Header
        response_parts.append("## 🤔 I need a bit more information")

        if tone == "conversational":
            response_parts.append("To give you the most accurate and helpful results, I have a few quick questions:\n")
        else:
            response_parts.append("Please clarify the following:\n")

        # Questions
        for i, question in enumerate(questions, 1):
            response_parts.append(f"**{i}.** {question}")

        # Encouraging close
        if tone == "conversational":
            response_parts.append("\n🎯 **The more specific you can be, the better I can help you!** These details will help me find exactly what you're looking for.")
        else:
            response_parts.append("\nProviding these details will help ensure accurate results.")

        return "\n".join(response_parts)

    @classmethod
    def detect_query_type(cls, query: str) -> QueryType:
        """Detect the type of query for specialized formatting."""
        query_lower = query.lower()

        # Comparison indicators (check first as they're most specific)
        comparison_indicators = [
            ' vs ', ' versus ', 'compare ', 'difference between', 'better than',
            'worse than', 'higher than', 'lower than', 'vs.', 'compared to'
        ]

        # Exploration indicators (check second)
        exploration_indicators = [
            'what is in', 'describe', 'tell me about', 'what does',
            'structure of', 'columns in', 'tables in', 'schema of', 'what\'s in'
        ]

        # List/Ranking indicators (check third)
        list_indicators = [
            'top ', 'bottom ', 'most ', 'least ', 'highest ', 'lowest ',
            'best ', 'worst ', 'first ', 'last ', 'list of',
            'show me all', 'give me all', 'top 10', 'top 5',
            'top ten', 'top five', 'rank', 'ranking'
        ]

        # Analytical indicators (patterns, trends, analysis)
        analytical_indicators = [
            'trend', 'pattern', 'analysis', 'insight', 'correlation',
            'over time', 'growth', 'decline', 'change', 'performance',
            'how many', 'count', 'average', 'sum', 'total', 'percentage',
            'sales trends', 'revenue trends'
        ]

        if any(indicator in query_lower for indicator in comparison_indicators):
            return QueryType.COMPARISON
        elif any(indicator in query_lower for indicator in exploration_indicators):
            return QueryType.EXPLORATION
        elif any(indicator in query_lower for indicator in list_indicators):
            return QueryType.LIST_RANKING
        elif any(indicator in query_lower for indicator in analytical_indicators):
            return QueryType.ANALYTICAL
        else:
            # Default to analytical for general queries
            return QueryType.ANALYTICAL


__all__ = ["ResponseFormatter", "QueryType"]