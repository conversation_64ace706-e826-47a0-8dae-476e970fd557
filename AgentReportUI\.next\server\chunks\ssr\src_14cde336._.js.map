{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/cn.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/date.ts"], "sourcesContent": ["// Date utility functions\n\nexport const formatDate = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n};\n\nexport const formatDateTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n};\n\nexport const formatRelativeTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n\n  return formatDate(d);\n};\n\nexport const isTokenExpired = (expiresAt: string): boolean => {\n  const expirationTime = new Date(expiresAt).getTime();\n  const currentTime = new Date().getTime();\n  const bufferTime = 5 * 60 * 1000; // 5 minutes buffer\n  \n  return currentTime >= (expirationTime - bufferTime);\n};\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;AAElB,MAAM,aAAa,CAAC;IACzB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,cAAc,CAAC,SAAS;QAC/B,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;IACrE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;IAC5D;IAEA,OAAO,WAAW;AACpB;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,iBAAiB,IAAI,KAAK,WAAW,OAAO;IAClD,MAAM,cAAc,IAAI,OAAO,OAAO;IACtC,MAAM,aAAa,IAAI,KAAK,MAAM,mBAAmB;IAErD,OAAO,eAAgB,iBAAiB;AAC1C", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/format.ts"], "sourcesContent": ["// Formatting utility functions\n\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\nexport const formatNumber = (num: number): string => {\n  return new Intl.NumberFormat('en-US').format(num);\n};\n\nexport const formatCurrency = (amount: number, currency = 'USD'): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n};\n\nexport const truncateText = (text: string, maxLength: number): string => {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n};\n\nexport const capitalizeFirst = (str: string): string => {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n};\n\nexport const camelToTitle = (str: string): string => {\n  return str\n    .replace(/([A-Z])/g, ' $1')\n    .replace(/^./, (str) => str.toUpperCase())\n    .trim();\n};\n\nexport const generateSessionId = (): string => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;AAExB,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,MAAM,iBAAiB,CAAC,QAAgB,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,eAAe,CAAC,MAAc;IACzC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,YAAY,OACpB,OAAO,CAAC,MAAM,CAAC,MAAQ,IAAI,WAAW,IACtC,IAAI;AACT;AAEO,MAAM,oBAAoB;IAC/B,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/validation.ts"], "sourcesContent": ["// Validation utility functions\n\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const isValidPassword = (password: string): boolean => {\n  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number\n  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;\n  return passwordRegex.test(password);\n};\n\nexport const isValidUrl = (url: string): boolean => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const isValidPort = (port: number): boolean => {\n  return port >= 1 && port <= 65535;\n};\n\nexport const isValidDatabaseName = (name: string): boolean => {\n  // Basic validation for database names\n  const dbNameRegex = /^[a-zA-Z][a-zA-Z0-9_-]*$/;\n  return dbNameRegex.test(name) && name.length >= 2 && name.length <= 50;\n};\n\nexport const sanitizeInput = (input: string): string => {\n  return input.trim().replace(/[<>]/g, '');\n};\n\nexport const validateRequired = (value: any): boolean => {\n  if (typeof value === 'string') {\n    return value.trim().length > 0;\n  }\n  return value !== null && value !== undefined;\n};\n\nexport const validateMinLength = (value: string, minLength: number): boolean => {\n  return value.length >= minLength;\n};\n\nexport const validateMaxLength = (value: string, maxLength: number): boolean => {\n  return value.length <= maxLength;\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;AAExB,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,kBAAkB,CAAC;IAC9B,4DAA4D;IAC5D,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,QAAQ,KAAK,QAAQ;AAC9B;AAEO,MAAM,sBAAsB,CAAC;IAClC,sCAAsC;IACtC,MAAM,cAAc;IACpB,OAAO,YAAY,IAAI,CAAC,SAAS,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI;AACtE;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,MAAM,IAAI,GAAG,MAAM,GAAG;IAC/B;IACA,OAAO,UAAU,QAAQ,UAAU;AACrC;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/index.ts"], "sourcesContent": ["// Main utility exports\n\nexport * from './cn';\nexport * from './date';\nexport * from './format';\nexport * from './validation';\n\n// Re-export the original cn function for backward compatibility\nexport { cn } from './cn';\n"], "names": [], "mappings": "AAAA,uBAAuB;;AAEvB;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAAA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { ChevronRight } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<\"nav\"> & {\n    separator?: React.ComponentType<{ className?: string }>;\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label=\"breadcrumb\" {...props} />);\nBreadcrumb.displayName = \"Breadcrumb\";\n\nconst BreadcrumbList = React.forwardRef<\n  HTMLOListElement,\n  React.ComponentPropsWithoutRef<\"ol\">\n>(({ className, ...props }, ref) => (\n  <ol\n    ref={ref}\n    className={cn(\n      \"flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5\",\n      className\n    )}\n    {...props}\n  />\n));\nBreadcrumbList.displayName = \"BreadcrumbList\";\n\nconst BreadcrumbItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentPropsWithoutRef<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    className={cn(\"inline-flex items-center gap-1.5\", className)}\n    {...props}\n  />\n));\nBreadcrumbItem.displayName = \"BreadcrumbItem\";\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<\"a\"> & {\n    asChild?: boolean;\n  }\n>(({ asChild, className, ...props }, ref) => {\n  if (asChild) {\n    return <React.Fragment {...props} />;\n  }\n\n  return (\n    <a\n      ref={ref}\n      className={cn(\"transition-colors hover:text-foreground\", className)}\n      {...props}\n    />\n  );\n});\nBreadcrumbLink.displayName = \"BreadcrumbLink\";\n\nconst BreadcrumbPage = React.forwardRef<\n  HTMLSpanElement,\n  React.ComponentPropsWithoutRef<\"span\">\n>(({ className, ...props }, ref) => (\n  <span\n    ref={ref}\n    role=\"link\"\n    aria-disabled=\"true\"\n    aria-current=\"page\"\n    className={cn(\"font-normal text-foreground\", className)}\n    {...props}\n  />\n));\nBreadcrumbPage.displayName = \"BreadcrumbPage\";\n\nconst BreadcrumbSeparator = ({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) => (\n  <li\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"[&>svg]:size-3.5\", className)}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n);\nBreadcrumbSeparator.displayName = \"BreadcrumbSeparator\";\n\nconst BreadcrumbEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <svg\n      width=\"15\"\n      height=\"15\"\n      viewBox=\"0 0 15 15\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        d=\"M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z\"\n        fill=\"currentColor\"\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n      />\n    </svg>\n    <span className=\"sr-only\">More</span>\n  </span>\n);\nBreadcrumbEllipsis.displayName = \"BreadcrumbEllipsis\";\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKhC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,8OAAC;QAAI,KAAK;QAAK,cAAW;QAAc,GAAG,KAAK;;;;;;AACzE,WAAW,WAAW,GAAG;AAEzB,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKpC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACnC,IAAI,SAAS;QACX,qBAAO,8OAAC,qMAAA,CAAA,WAAc;YAAE,GAAG,KAAK;;;;;;IAClC;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB,iBAC3B,8OAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;;;;;;;;;;AAG9B,oBAAoB,WAAW,GAAG;AAElC,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OAC0B,iBAC7B,8OAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;0BAEN,cAAA,8OAAC;oBACC,GAAE;oBACF,MAAK;oBACL,UAAS;oBACT,UAAS;;;;;;;;;;;0BAGb,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\r\nimport { Menu, LayoutDashboard, ChevronRight, Plus, Brain, Download, MessageSquare } from 'lucide-react';\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { useTheme } from \"@/providers/theme-provider\";\r\nimport { ThemeToggle } from '@/components/ui/theme-toggle';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\nimport {\r\n  Breadcrumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from '@/components/ui/breadcrumb';\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated } = useAuth();\r\n  const { theme, setTheme } = useTheme();\r\n  const { pageInfo, actions } = usePageHeader();\r\n  const pathname = usePathname();\r\n  \r\n  // Check if we have breadcrumbs to show\r\n  const hasBreadcrumbs = pageInfo.breadcrumbs && pageInfo.breadcrumbs.length > 1;\r\n  \r\n  // Get chart-related state from context actions\r\n  const { \r\n    onCreateChart, \r\n    chartCount = 0, \r\n    maxCharts = 12, \r\n    onCreateAnalysis, \r\n    isCreatingAnalysis = false,\r\n    onCreateDashboard,\r\n    onExport,\r\n    onToggleChat,\r\n    isChatOpen = false\r\n  } = actions;\r\n  const canCreateChart = chartCount < maxCharts;\r\n  \r\n  // Show Create Chart Button on dashboard pages when the callback is provided\r\n  const isDashboardPage = pathname === '/dashboard';\r\n  const isAIWorkflowsPage = pathname === '/ai-workflows';\r\n  const showCreateChartButton = isDashboardPage && onCreateChart;\r\n  const showCreateDashboardButton = isDashboardPage && onCreateDashboard;\r\n  const showCreateAnalysisButton = isAIWorkflowsPage && onCreateAnalysis;\r\n  const showProjectActions = isAIWorkflowsPage && (onExport || onToggleChat);\r\n\r\n  // Get the appropriate icon for breadcrumbs\r\n  const getBreadcrumbIcon = () => {\r\n    if (pathname === '/dashboard') return LayoutDashboard;\r\n    if (pathname === '/ai-workflows') return Brain;\r\n    return pageInfo.icon || LayoutDashboard;\r\n  };\r\n\r\n  const BreadcrumbIcon = getBreadcrumbIcon();\r\n\r\n  return (\r\n    <header className=\"flex items-center justify-between whitespace-nowrap border-b border-solid border-sidebar-border bg-sidebar-bg px-4 py-2 h-12\">\r\n      <div className=\"flex items-center gap-3 text-sidebar-text-primary\">\r\n        {isAuthenticated && (\r\n          <div className=\"lg:hidden\">\r\n            <Sheet>\r\n              <SheetTrigger asChild>\r\n                <Button variant=\"outline\" size=\"icon\" className=\"h-7 w-7\">\r\n                  <Menu className=\"h-4 w-4\" />\r\n                </Button>\r\n              </SheetTrigger>\r\n            </Sheet>\r\n          </div>\r\n        )}\r\n        \r\n        {/* Dynamic Breadcrumb Navigation or Page Title */}\r\n        {hasBreadcrumbs ? (\r\n          <Breadcrumb>\r\n            <BreadcrumbList>\r\n              {pageInfo.breadcrumbs!.map((breadcrumb, index) => (\r\n                <React.Fragment key={breadcrumb.label}>\r\n                  <BreadcrumbItem>\r\n                    {index === 0 ? (\r\n                      // First breadcrumb item (with icon)\r\n                      breadcrumb.onClick || breadcrumb.href ? (\r\n                        <BreadcrumbLink \r\n                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}\r\n                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}\r\n                          className=\"flex items-center space-x-1.5 cursor-pointer hover:text-primary text-sm font-medium\"\r\n                        >\r\n                          <BreadcrumbIcon className=\"h-4 w-4\" />\r\n                          <span>{breadcrumb.label}</span>\r\n                        </BreadcrumbLink>\r\n                      ) : (\r\n                        <div className=\"flex items-center space-x-1.5 text-sm font-medium\">\r\n                          <BreadcrumbIcon className=\"h-4 w-4\" />\r\n                          <span>{breadcrumb.label}</span>\r\n                        </div>\r\n                      )\r\n                    ) : (\r\n                      // Subsequent breadcrumb items\r\n                      index === pageInfo.breadcrumbs!.length - 1 ? (\r\n                        <BreadcrumbPage className=\"font-medium text-sm\">\r\n                          {breadcrumb.label}\r\n                        </BreadcrumbPage>\r\n                      ) : (\r\n                        <BreadcrumbLink \r\n                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}\r\n                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}\r\n                          className=\"cursor-pointer hover:text-primary text-sm font-medium\"\r\n                        >\r\n                          {breadcrumb.label}\r\n                        </BreadcrumbLink>\r\n                      )\r\n                    )}\r\n                  </BreadcrumbItem>\r\n                  \r\n                  {/* Separator */}\r\n                  {index < pageInfo.breadcrumbs!.length - 1 && (\r\n                    <BreadcrumbSeparator>\r\n                      <ChevronRight className=\"h-3 w-3\" />\r\n                    </BreadcrumbSeparator>\r\n                  )}\r\n                </React.Fragment>\r\n              ))}\r\n            </BreadcrumbList>\r\n          </Breadcrumb>\r\n        ) : (\r\n          <h1 className=\"text-sm font-medium leading-tight tracking-[-0.015em] text-sidebar-text-primary\">\r\n            {pageInfo.title}\r\n          </h1>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right side actions */}\r\n      <div className=\"flex items-center gap-2\">\r\n        {showCreateChartButton && (\r\n          <Button\r\n            onClick={onCreateChart}\r\n            disabled={!canCreateChart}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            title={!canCreateChart ? `Maximum of ${maxCharts} charts allowed` : undefined}\r\n            onMouseEnter={(e) => {\r\n              if (canCreateChart) {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (canCreateChart) {\r\n                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n              }\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            Chart\r\n          </Button>\r\n        )}\r\n        {showCreateDashboardButton && (\r\n          <Button\r\n            onClick={onCreateDashboard}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            onMouseEnter={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            Dashboard\r\n          </Button>\r\n        )}\r\n        {showCreateAnalysisButton && (\r\n          <Button\r\n            onClick={onCreateAnalysis}\r\n            disabled={isCreatingAnalysis}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            title={isCreatingAnalysis ? \"Analysis is being created\" : undefined}\r\n            onMouseEnter={(e) => {\r\n              if (!isCreatingAnalysis) {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (!isCreatingAnalysis) {\r\n                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n              }\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            {isCreatingAnalysis ? 'Creating...' : 'Analysis'}\r\n          </Button>\r\n        )}\r\n        {showProjectActions && (\r\n          <>\r\n            {onExport && (\r\n              <Button\r\n                onClick={onExport}\r\n                className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n                style={{\r\n                  backgroundColor: 'var(--surface-selected)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n                }}\r\n              >\r\n                <Download className=\"h-3 w-3 mr-1.5\" />\r\n                Export\r\n              </Button>\r\n            )}\r\n            {onToggleChat && (\r\n              <Button\r\n                onClick={onToggleChat}\r\n                className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n                style={{\r\n                  backgroundColor: isChatOpen ? 'var(--interactive-bg-secondary-hover)' : 'var(--surface-selected)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = isChatOpen ? 'var(--interactive-bg-secondary-hover)' : 'var(--surface-selected)';\r\n                }}\r\n              >\r\n                <MessageSquare className=\"h-3 w-3 mr-1.5\" />\r\n                Chat\r\n              </Button>\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAXA;;;;;;;;;;;AAoBA,MAAM,SAAS;IACb,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAC1C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,uCAAuC;IACvC,MAAM,iBAAiB,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG;IAE7E,+CAA+C;IAC/C,MAAM,EACJ,aAAa,EACb,aAAa,CAAC,EACd,YAAY,EAAE,EACd,gBAAgB,EAChB,qBAAqB,KAAK,EAC1B,iBAAiB,EACjB,QAAQ,EACR,YAAY,EACZ,aAAa,KAAK,EACnB,GAAG;IACJ,MAAM,iBAAiB,aAAa;IAEpC,4EAA4E;IAC5E,MAAM,kBAAkB,aAAa;IACrC,MAAM,oBAAoB,aAAa;IACvC,MAAM,wBAAwB,mBAAmB;IACjD,MAAM,4BAA4B,mBAAmB;IACrD,MAAM,2BAA2B,qBAAqB;IACtD,MAAM,qBAAqB,qBAAqB,CAAC,YAAY,YAAY;IAEzE,2CAA2C;IAC3C,MAAM,oBAAoB;QACxB,IAAI,aAAa,cAAc,OAAO,4NAAA,CAAA,kBAAe;QACrD,IAAI,aAAa,iBAAiB,OAAO,oMAAA,CAAA,QAAK;QAC9C,OAAO,SAAS,IAAI,IAAI,4NAAA,CAAA,kBAAe;IACzC;IAEA,MAAM,iBAAiB;IAEvB,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;oBACZ,iCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;sCACJ,cAAA,8OAAC,iIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAO,WAAU;8CAC9C,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQzB,+BACC,8OAAC,sIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,sIAAA,CAAA,iBAAc;sCACZ,SAAS,WAAW,CAAE,GAAG,CAAC,CAAC,YAAY,sBACtC,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;sDACb,8OAAC,sIAAA,CAAA,iBAAc;sDACZ,UAAU,IACT,oCAAoC;4CACpC,WAAW,OAAO,IAAI,WAAW,IAAI,iBACnC,8OAAC,sIAAA,CAAA,iBAAc;gDACZ,GAAI,WAAW,IAAI,GAAG;oDAAE,MAAM,WAAW,IAAI;gDAAC,IAAI,CAAC,CAAC;gDACpD,GAAI,WAAW,OAAO,GAAG;oDAAE,SAAS,WAAW,OAAO;gDAAC,IAAI,CAAC,CAAC;gDAC9D,WAAU;;kEAEV,8OAAC;wDAAe,WAAU;;;;;;kEAC1B,8OAAC;kEAAM,WAAW,KAAK;;;;;;;;;;;qEAGzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAe,WAAU;;;;;;kEAC1B,8OAAC;kEAAM,WAAW,KAAK;;;;;;;;;;;uDAI3B,8BAA8B;4CAC9B,UAAU,SAAS,WAAW,CAAE,MAAM,GAAG,kBACvC,8OAAC,sIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,WAAW,KAAK;;;;;qEAGnB,8OAAC,sIAAA,CAAA,iBAAc;gDACZ,GAAI,WAAW,IAAI,GAAG;oDAAE,MAAM,WAAW,IAAI;gDAAC,IAAI,CAAC,CAAC;gDACpD,GAAI,WAAW,OAAO,GAAG;oDAAE,SAAS,WAAW,OAAO;gDAAC,IAAI,CAAC,CAAC;gDAC9D,WAAU;0DAET,WAAW,KAAK;;;;;;;;;;;wCAOxB,QAAQ,SAAS,WAAW,CAAE,MAAM,GAAG,mBACtC,8OAAC,sIAAA,CAAA,sBAAmB;sDAClB,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;mCAxCT,WAAW,KAAK;;;;;;;;;;;;;;6CAgD3C,8OAAC;wBAAG,WAAU;kCACX,SAAS,KAAK;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;;oBACZ,uCACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,OAAO,CAAC,iBAAiB,CAAC,WAAW,EAAE,UAAU,eAAe,CAAC,GAAG;wBACpE,cAAc,CAAC;4BACb,IAAI,gBAAgB;gCAClB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,gBAAgB;gCAClB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;;0CAEA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAmB;;;;;;;oBAItC,2CACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC1C;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC1C;;0CAEA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAmB;;;;;;;oBAItC,0CACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,OAAO,qBAAqB,8BAA8B;wBAC1D,cAAc,CAAC;4BACb,IAAI,CAAC,oBAAoB;gCACvB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,oBAAoB;gCACvB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;;0CAEA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,qBAAqB,gBAAgB;;;;;;;oBAGzC,oCACC;;4BACG,0BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,OAAO;gCACT;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;;kDAEA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAmB;;;;;;;4BAI1C,8BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,iBAAiB,aAAa,0CAA0C;oCACxE,OAAO;gCACT;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,aAAa,0CAA0C;gCACjG;;kDAEA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAmB;;;;;;;;;;;;;;;;;;;;;AAS5D;uCAEe", "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground text-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,+nBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Sidebar.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport Link from 'next/link';\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { MoreVertical, MessageCircle, Database, Plus, BarChart3, MessageCirclePlus, LayoutDashboard, Brain, User, Settings, LogOut } from 'lucide-react';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useChatHistory } from \"@/providers/ChatHistoryContext\";\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface SidebarProps {\r\n  onNewChat: () => void;\r\n  onToggleCollapse?: (collapsed: boolean) => void;\r\n  isCreatingNewChat?: boolean;\r\n}\r\n\r\nconst Sidebar: React.FC<SidebarProps> = ({ onNewChat, onToggleCollapse, isCreatingNewChat = false }) => {\r\n  const { \r\n    chatHistory, \r\n    isLoadingChats, \r\n    deleteChat,\r\n    renameChat, \r\n  } = useChatHistory();\r\n  const { logout, user } = useAuth();\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const [menuOpenId, setMenuOpenId] = useState<string | null>(null);\r\n  const [renameId, setRenameId] = useState<string | null>(null);\r\n  const [renameValue, setRenameValue] = useState<string>(\"\");\r\n  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);\r\n\r\n  console.log('Sidebar component rendered. Current pathname:', pathname);\r\n\r\n  // Extract chatId from pathname (e.g., /chat/123)\r\n  const currentChatId = pathname?.split('/').includes('chat') ? pathname.split('/').pop() : null;\r\n  console.log('Current chatId extracted from pathname:', currentChatId);\r\n\r\n  const handleRename = (id: string, currentTitle: string) => {\r\n    console.log('Renaming chat with id:', id, 'and current title:', currentTitle);\r\n    setRenameId(id);\r\n    setRenameValue(currentTitle);\r\n    setMenuOpenId(null);\r\n  };\r\n\r\n  const handleRenameSubmit = (id: string) => {\r\n    console.log('Submitting rename for chat with id:', id, 'and new title:', renameValue);\r\n    if (renameValue.trim()) {\r\n      renameChat(id, renameValue.trim());\r\n    }\r\n    setRenameId(null);\r\n    setRenameValue(\"\");\r\n  };\r\n\r\n  const handleDelete = async (chatId: string) => {\r\n    try {\r\n      await deleteChat(chatId);\r\n      setMenuOpenId(null);\r\n\r\n      // 🚚 After successful deletion, handle navigation if the deleted chat was active\r\n      if (currentChatId === chatId) {\r\n        // Get the updated chat list (the state will have been updated by deleteChat)\r\n        const remainingChats = chatHistory.filter(chat => chat.id !== chatId);\r\n\r\n        if (remainingChats.length > 0) {\r\n          // Navigate to the most recently updated chat (first in list)\r\n          router.push(`/chat/${remainingChats[0].id}`);\r\n        } else {\r\n          // No chats left – start a fresh chat\r\n          onNewChat();\r\n          // Fallback navigate to generic chat route to trigger new-chat UI\r\n          router.push('/chat');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete chat:', error);\r\n      // You could add a toast notification here if you have one\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (timestamp: Date) => {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - timestamp.getTime();\r\n    const diffHours = diffMs / (1000 * 60 * 60);\r\n    const diffDays = diffMs / (1000 * 60 * 60 * 24);\r\n\r\n    if (diffHours < 1) {\r\n      return 'Just now';\r\n    } else if (diffHours < 24) {\r\n      return `${Math.floor(diffHours)}h ago`;\r\n    } else if (diffDays < 7) {\r\n      return `${Math.floor(diffDays)}d ago`;\r\n    } else {\r\n      return timestamp.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  const handleToggleCollapse = () => {\r\n    const newCollapsedState = !isCollapsed;\r\n    setIsCollapsed(newCollapsedState);\r\n    onToggleCollapse?.(newCollapsedState);\r\n  };\r\n\r\n  return (\r\n    <aside \r\n      className={`hidden lg:flex flex-col ${isCollapsed ? 'w-16' : 'w-sidebar'} h-full min-h-0 transition-all duration-300 flex-shrink-0 relative overflow-y-auto`}\r\n      style={{\r\n        backgroundColor: 'var(--sidebar-bg)',\r\n        borderRight: '1px solid var(--sidebar-border)',\r\n        scrollbarColor: 'var(--sidebar-surface-tertiary) transparent'\r\n      }}\r\n    >\r\n      {/* Toggle Button */}\r\n      <div className={`sticky top-0 z-10 p-2 transition-all duration-300 ease-in-out ${isCollapsed ? 'flex justify-center' : 'flex justify-end'}`}\r\n        style={{ backgroundColor: 'var(--sidebar-bg)' }}\r\n      >\r\n        <Button \r\n          variant=\"ghost\" \r\n          size=\"icon\"\r\n          className=\"w-10 h-10 flex items-center justify-center rounded-lg border-0 transition-all duration-300 ease-in-out transform-gpu\"\r\n          style={{ \r\n            color: 'var(--sidebar-icon) !important',\r\n            backgroundColor: 'transparent !important'\r\n          }}\r\n          onMouseEnter={(e) => {\r\n            e.currentTarget.style.setProperty('background-color', 'var(--interactive-bg-secondary-hover)', 'important');\r\n            e.currentTarget.style.transform = 'scale(1.05)';\r\n          }}\r\n          onMouseLeave={(e) => {\r\n            e.currentTarget.style.setProperty('background-color', 'transparent', 'important');\r\n            e.currentTarget.style.transform = 'scale(1)';\r\n          }}\r\n          onClick={handleToggleCollapse}\r\n        >\r\n          <svg \r\n            className=\"h-5 w-5 transition-transform duration-300 ease-in-out\" \r\n            viewBox=\"0 0 20 20\" \r\n            fill=\"currentColor\" \r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            style={{\r\n              transform: isCollapsed ? 'scaleX(-1)' : 'scaleX(1)'\r\n            }}\r\n          >\r\n            <path d=\"M6.83496 3.99992C6.38353 4.00411 6.01421 4.0122 5.69824 4.03801C5.31232 4.06954 5.03904 4.12266 4.82227 4.20012L4.62207 4.28606C4.18264 4.50996 3.81498 4.85035 3.55859 5.26848L3.45605 5.45207C3.33013 5.69922 3.25006 6.01354 3.20801 6.52824C3.16533 7.05065 3.16504 7.71885 3.16504 8.66301V11.3271C3.16504 12.2712 3.16533 12.9394 3.20801 13.4618C3.25006 13.9766 3.33013 14.2909 3.45605 14.538L3.55859 14.7216C3.81498 15.1397 4.18266 15.4801 4.62207 15.704L4.82227 15.79C5.03904 15.8674 5.31234 15.9205 5.69824 15.9521C6.01398 15.9779 6.383 15.986 6.83398 15.9902L6.83496 3.99992ZM18.165 11.3271C18.165 12.2493 18.1653 12.9811 18.1172 13.5702C18.0745 14.0924 17.9916 14.5472 17.8125 14.9648L17.7295 15.1415C17.394 15.8 16.8834 16.3511 16.2568 16.7353L15.9814 16.8896C15.5157 17.1268 15.0069 17.2285 14.4102 17.2773C13.821 17.3254 13.0893 17.3251 12.167 17.3251H7.83301C6.91071 17.3251 6.17898 17.3254 5.58984 17.2773C5.06757 17.2346 4.61294 17.1508 4.19531 16.9716L4.01855 16.8896C3.36014 16.5541 2.80898 16.0434 2.4248 15.4169L2.27051 15.1415C2.03328 14.6758 1.93158 14.167 1.88281 13.5702C1.83468 12.9811 1.83496 12.2493 1.83496 11.3271V8.66301C1.83496 7.74072 1.83468 7.00898 1.88281 6.41985C1.93157 5.82309 2.03329 5.31432 2.27051 4.84856L2.4248 4.57317C2.80898 3.94666 3.36012 3.436 4.01855 3.10051L4.19531 3.0175C4.61285 2.83843 5.06771 2.75548 5.58984 2.71281C6.17898 2.66468 6.91071 2.66496 7.83301 2.66496H12.17C13.0893 2.66496 13.821 2.66468 14.4102 2.71281C15.0069 2.76157 15.5157 2.86329 15.9814 3.10051L16.2568 3.25481C16.8833 3.63898 17.394 4.19012 17.7295 4.84856L17.8125 5.02531C17.9916 5.44285 18.0745 5.89771 18.1172 6.41985C18.1653 7.00898 18.165 7.74072 18.165 8.66301V11.3271ZM8.16406 15.995H12.167C13.1112 15.995 13.7794 15.9947 14.3018 15.9521C14.8164 15.91 15.1308 15.8299 15.3779 15.704L15.5615 15.6015C15.9797 15.3451 16.32 14.9774 16.5439 14.538L16.6299 14.3378C16.7074 14.121 16.7605 13.8478 16.792 13.4618C16.8347 12.9394 16.835 12.2712 16.835 11.3271V8.66301C16.835 7.71885 16.8347 7.05065 16.792 6.52824C16.7605 6.14232 16.7073 5.86904 16.6299 5.65227L16.5439 5.45207C16.32 5.01264 15.9796 4.64498 15.5615 4.3886L15.3779 4.28606C15.1308 4.16013 14.8165 4.08006 14.3018 4.03801C13.7794 3.99533 13.1112 3.99504 12.167 3.99504H8.16406C8.16407 3.99667 8.16504 3.99829 8.16504 3.99992L8.16406 15.995Z\"></path>\r\n          </svg>\r\n        </Button>\r\n      </div>\r\n      \r\n      {!isCollapsed && (\r\n        <div className=\"flex flex-col h-full px-3\">\r\n          {/* Navigation Links */}\r\n          <div className=\"space-y-1 mb-4\">\r\n            <Link href=\"/dashboard\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/dashboard' ? '' : ''}`}\r\n                style={{\r\n                  color: pathname === '/dashboard' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/dashboard' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/dashboard') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/dashboard') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <LayoutDashboard className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Dashboard\r\n              </Button>\r\n            </Link>\r\n            \r\n            <Link href=\"/reports\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/reports' ? '' : ''}`}\r\n                style={{\r\n                  color: pathname === '/reports' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/reports' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/reports') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/reports') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <BarChart3 className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Reports\r\n              </Button>\r\n            </Link>\r\n            \r\n            <Link href=\"/datasources\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10`}\r\n                style={{\r\n                  color: pathname === '/datasources' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/datasources' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/datasources') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/datasources') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <Database className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Data Sources\r\n              </Button>\r\n            </Link>\r\n\r\n            <Link href=\"/ai-workflows\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10`}\r\n                style={{\r\n                  color: pathname === '/ai-workflows' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/ai-workflows' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/ai-workflows') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/ai-workflows') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <Brain className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                AI Workflows\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n          \r\n          {/* New Chat Button */}\r\n          <div className=\"mb-4\">\r\n            <Button \r\n              variant=\"ghost\" \r\n              className=\"w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10\" \r\n              onClick={onNewChat}\r\n              disabled={isCreatingNewChat}\r\n              style={{\r\n                color: 'var(--sidebar-text-secondary)',\r\n                backgroundColor: 'transparent'\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                if (!isCreatingNewChat) {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                if (!isCreatingNewChat) {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }\r\n              }}\r\n            >\r\n              <Plus className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n              {isCreatingNewChat ? 'Creating...' : 'New Chat'}\r\n            </Button>\r\n          </div>\r\n          \r\n          {/* Chat History Section */}\r\n          <div className=\"flex flex-col gap-1 overflow-y-auto flex-1 pb-4\">\r\n            <h3 \r\n              className=\"text-xs font-medium uppercase tracking-wider px-2 py-2 sticky top-0 z-10\"\r\n              style={{ \r\n                color: 'var(--sidebar-text-tertiary)',\r\n                backgroundColor: 'var(--sidebar-bg)'\r\n              }}\r\n            >\r\n              Chat History\r\n            </h3>\r\n            \r\n            {isLoadingChats && (\r\n              <div className=\"flex items-center justify-center py-8\">\r\n                <div className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  Loading chats...\r\n                </div>\r\n              </div>\r\n            )}\r\n            \r\n            {!isLoadingChats && chatHistory.map((chat) => {\r\n              const isActive = chat.id === currentChatId;\r\n              const isRenaming = renameId === chat.id;\r\n              return (\r\n                <div\r\n                  key={chat.id}\r\n                  className={`group flex items-center justify-between py-2 px-2 rounded-lg cursor-pointer transition-all duration-200 ${isActive ? '' : ''}`}\r\n                  style={{\r\n                    backgroundColor: isActive ? 'var(--surface-selected)' : 'transparent'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'transparent';\r\n                    }\r\n                  }}\r\n                  onClick={() => router.push(`/chat/${chat.id}`)}\r\n                >\r\n                  {isRenaming ? (\r\n                    <form\r\n                      onSubmit={e => { e.preventDefault(); handleRenameSubmit(chat.id); }}\r\n                      className=\"flex items-center gap-2 w-full\"\r\n                    >\r\n                      <input\r\n                        autoFocus\r\n                        className=\"truncate max-w-[120px] text-sm font-normal bg-transparent border-b border-blue-400 outline-none px-1\"\r\n                        style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        value={renameValue}\r\n                        onChange={e => setRenameValue(e.target.value)}\r\n                        onKeyDown={e => { if (e.key === 'Escape') setRenameId(null); }}\r\n                      />\r\n                      <Button \r\n                        type=\"submit\" \r\n                        size=\"sm\" \r\n                        variant=\"ghost\" \r\n                        className=\"text-blue-500 px-2 text-xs rounded border-0\"\r\n                      >\r\n                        Save\r\n                      </Button>\r\n                    </form>\r\n                  ) : (\r\n                    <>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <span \r\n                          className=\"truncate text-sm font-normal block\"\r\n                          style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        >\r\n                          {chat.title}\r\n                        </span>\r\n                      </div>\r\n                      \r\n                      <div className=\"relative ml-2 flex-shrink-0\" onClick={e => e.stopPropagation()}>\r\n                        <DropdownMenu>\r\n                          <DropdownMenuTrigger asChild>\r\n                            <Button\r\n                              type=\"button\"\r\n                              size=\"icon\"\r\n                              variant=\"ghost\"\r\n                              className=\"w-7 h-7 p-0 opacity-0 group-hover:opacity-100 transition-all duration-200 rounded border-0\"\r\n                              style={{ color: 'var(--sidebar-icon)' }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                              aria-label=\"More actions\"\r\n                            >\r\n                              <MoreVertical className=\"w-4 h-4\" />\r\n                            </Button>\r\n                          </DropdownMenuTrigger>\r\n                          <DropdownMenuContent \r\n                            align=\"start\" \r\n                            side=\"bottom\" \r\n                            sideOffset={8} \r\n                            className=\"border-none shadow-xl rounded-xl p-2\"\r\n                            style={{\r\n                              backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                              color: 'var(--sidebar-text-primary)'\r\n                            }}\r\n                          >\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleRename(chat.id, chat.title)}\r\n                              className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                              style={{ \r\n                                color: 'var(--sidebar-text-primary)',\r\n                                backgroundColor: 'transparent'\r\n                              }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                            >\r\n                              Rename\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleDelete(chat.id)}\r\n                              className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                              style={{ \r\n                                color: '#ff8583',\r\n                                backgroundColor: 'transparent'\r\n                              }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                            >\r\n                              Delete\r\n                            </DropdownMenuItem>\r\n                          </DropdownMenuContent>\r\n                        </DropdownMenu>\r\n                      </div>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              );\r\n            })}\r\n            \r\n            {!isLoadingChats && chatHistory.length === 0 && (\r\n              <div className=\"text-center py-8\">\r\n                <MessageCirclePlus \r\n                  className=\"h-12 w-12 mx-auto mb-3\" \r\n                  style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                />\r\n                <p className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  No chats yet\r\n                </p>\r\n                <p className=\"text-xs mt-1\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  Start a new conversation\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          {/* Profile Section */}\r\n          <div className=\"mt-auto pt-2 pb-4 border-t\" style={{ borderColor: 'var(--sidebar-border)' }}>\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <div className=\"flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-all duration-200 hover:bg-opacity-80\" \r\n                     style={{ backgroundColor: 'transparent' }}\r\n                     onMouseEnter={(e) => {\r\n                       e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                     }}\r\n                     onMouseLeave={(e) => {\r\n                       e.currentTarget.style.backgroundColor = 'transparent';\r\n                     }}>\r\n                  <div\r\n                    className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600\"\r\n                    style={{backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y\")'}}\r\n                  />\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <span className=\"text-sm font-medium block truncate\" style={{ color: 'var(--sidebar-text-primary)' }}>\r\n                      {'User'}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent \r\n                align=\"start\" \r\n                side=\"top\" \r\n                sideOffset={8} \r\n                className=\"border-none shadow-xl rounded-xl p-2\"\r\n                style={{\r\n                  backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n              >\r\n                <DropdownMenuItem asChild>\r\n                  <Link href=\"/profile\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                        style={{ \r\n                          color: 'var(--sidebar-text-primary)',\r\n                          backgroundColor: 'transparent'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'transparent';\r\n                        }}>\r\n                    <User className=\"w-4 h-4\" />\r\n                    Profile\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem asChild>\r\n                  <Link href=\"/settings\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                        style={{ \r\n                          color: 'var(--sidebar-text-primary)',\r\n                          backgroundColor: 'transparent'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'transparent';\r\n                        }}>\r\n                    <Settings className=\"w-4 h-4\" />\r\n                    Settings\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem \r\n                  onClick={logout} \r\n                  className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                  style={{ \r\n                    color: '#ff8583',\r\n                    backgroundColor: 'transparent'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }}\r\n                >\r\n                  <LogOut className=\"w-4 h-4\" />\r\n                  Logout\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      {/* Profile Section for Collapsed State */}\r\n      {isCollapsed && (\r\n        <div className=\"mt-auto p-2 pb-4 border-t\" style={{ borderColor: 'var(--sidebar-border)' }}>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <div className=\"flex justify-center\">\r\n                <div\r\n                  className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600 cursor-pointer transition-all duration-200 hover:scale-105\"\r\n                  style={{backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y\")'}}\r\n                />\r\n              </div>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent \r\n              align=\"start\" \r\n              side=\"right\" \r\n              sideOffset={8} \r\n              className=\"border-none shadow-xl rounded-xl p-2\"\r\n              style={{\r\n                backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                color: 'var(--sidebar-text-primary)'\r\n              }}\r\n            >\r\n              <DropdownMenuItem asChild>\r\n                <Link href=\"/profile\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                      style={{ \r\n                        color: 'var(--sidebar-text-primary)',\r\n                        backgroundColor: 'transparent'\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}>\r\n                  <User className=\"w-4 h-4\" />\r\n                  Profile\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem asChild>\r\n                <Link href=\"/settings\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                      style={{ \r\n                        color: 'var(--sidebar-text-primary)',\r\n                        backgroundColor: 'transparent'\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}>\r\n                  <Settings className=\"w-4 h-4\" />\r\n                  Settings\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem \r\n                onClick={logout} \r\n                className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                style={{ \r\n                  color: '#ff8583',\r\n                  backgroundColor: 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }}\r\n              >\r\n                <LogOut className=\"w-4 h-4\" />\r\n                Logout\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      )}\r\n    </aside>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;;AAgBA,MAAM,UAAkC,CAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,oBAAoB,KAAK,EAAE;IACjG,MAAM,EACJ,WAAW,EACX,cAAc,EACd,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,uIAAA,CAAA,iBAAc,AAAD;IACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAExD,QAAQ,GAAG,CAAC,iDAAiD;IAE7D,iDAAiD;IACjD,MAAM,gBAAgB,UAAU,MAAM,KAAK,SAAS,UAAU,SAAS,KAAK,CAAC,KAAK,GAAG,KAAK;IAC1F,QAAQ,GAAG,CAAC,2CAA2C;IAEvD,MAAM,eAAe,CAAC,IAAY;QAChC,QAAQ,GAAG,CAAC,0BAA0B,IAAI,sBAAsB;QAChE,YAAY;QACZ,eAAe;QACf,cAAc;IAChB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,uCAAuC,IAAI,kBAAkB;QACzE,IAAI,YAAY,IAAI,IAAI;YACtB,WAAW,IAAI,YAAY,IAAI;QACjC;QACA,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,cAAc;YAEd,iFAAiF;YACjF,IAAI,kBAAkB,QAAQ;gBAC5B,6EAA6E;gBAC7E,MAAM,iBAAiB,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAE9D,IAAI,eAAe,MAAM,GAAG,GAAG;oBAC7B,6DAA6D;oBAC7D,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC7C,OAAO;oBACL,qCAAqC;oBACrC;oBACA,iEAAiE;oBACjE,OAAO,IAAI,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,0DAA0D;QAC5D;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,UAAU,OAAO;QAChD,MAAM,YAAY,SAAS,CAAC,OAAO,KAAK,EAAE;QAC1C,MAAM,WAAW,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAE9C,IAAI,YAAY,GAAG;YACjB,OAAO;QACT,OAAO,IAAI,YAAY,IAAI;YACzB,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,KAAK,CAAC;QACxC,OAAO,IAAI,WAAW,GAAG;YACvB,OAAO,GAAG,KAAK,KAAK,CAAC,UAAU,KAAK,CAAC;QACvC,OAAO;YACL,OAAO,UAAU,kBAAkB;QACrC;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,oBAAoB,CAAC;QAC3B,eAAe;QACf,mBAAmB;IACrB;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,wBAAwB,EAAE,cAAc,SAAS,YAAY,kFAAkF,CAAC;QAC5J,OAAO;YACL,iBAAiB;YACjB,aAAa;YACb,gBAAgB;QAClB;;0BAGA,8OAAC;gBAAI,WAAW,CAAC,8DAA8D,EAAE,cAAc,wBAAwB,oBAAoB;gBACzI,OAAO;oBAAE,iBAAiB;gBAAoB;0BAE9C,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,OAAO;wBACL,OAAO;wBACP,iBAAiB;oBACnB;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,yCAAyC;wBAC/F,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oBACpC;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,eAAe;wBACrE,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oBACpC;oBACA,SAAS;8BAET,cAAA,8OAAC;wBACC,WAAU;wBACV,SAAQ;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO;4BACL,WAAW,cAAc,eAAe;wBAC1C;kCAEA,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;;;;;YAKb,CAAC,6BACA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,oGAAoG,EAAE,aAAa,eAAe,KAAK,IAAI;oCACvJ,OAAO;wCACL,OAAO,aAAa,eAAe,gCAAgC;wCACnE,iBAAiB,aAAa,eAAe,4BAA4B;oCAC3E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,cAAc;4CAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,cAAc;4CAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,8OAAC,4NAAA,CAAA,kBAAe;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAKpF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,oGAAoG,EAAE,aAAa,aAAa,KAAK,IAAI;oCACrJ,OAAO;wCACL,OAAO,aAAa,aAAa,gCAAgC;wCACjE,iBAAiB,aAAa,aAAa,4BAA4B;oCACzE;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,YAAY;4CAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,YAAY;4CAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,8OAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAK9E,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,mGAAmG,CAAC;oCAChH,OAAO;wCACL,OAAO,aAAa,iBAAiB,gCAAgC;wCACrE,iBAAiB,aAAa,iBAAiB,4BAA4B;oCAC7E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,gBAAgB;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,gBAAgB;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAK7E,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,mGAAmG,CAAC;oCAChH,OAAO;wCACL,OAAO,aAAa,kBAAkB,gCAAgC;wCACtE,iBAAiB,aAAa,kBAAkB,4BAA4B;oCAC9E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,iBAAiB;4CAChC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,iBAAiB;4CAChC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;;;;;;;kCAO5E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;4BACT,UAAU;4BACV,OAAO;gCACL,OAAO;gCACP,iBAAiB;4BACnB;4BACA,cAAc,CAAC;gCACb,IAAI,CAAC,mBAAmB;oCACtB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;4BACF;4BACA,cAAc,CAAC;gCACb,IAAI,CAAC,mBAAmB;oCACtB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;4BACF;;8CAEA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAU,OAAO;wCAAE,OAAO;oCAAsB;;;;;;gCAC/D,oBAAoB,gBAAgB;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;0CACD;;;;;;4BAIA,gCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAU,OAAO;wCAAE,OAAO;oCAA+B;8CAAG;;;;;;;;;;;4BAM9E,CAAC,kBAAkB,YAAY,GAAG,CAAC,CAAC;gCACnC,MAAM,WAAW,KAAK,EAAE,KAAK;gCAC7B,MAAM,aAAa,aAAa,KAAK,EAAE;gCACvC,qBACE,8OAAC;oCAEC,WAAW,CAAC,wGAAwG,EAAE,WAAW,KAAK,IAAI;oCAC1I,OAAO;wCACL,iBAAiB,WAAW,4BAA4B;oCAC1D;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;8CAE5C,2BACC,8OAAC;wCACC,UAAU,CAAA;4CAAO,EAAE,cAAc;4CAAI,mBAAmB,KAAK,EAAE;wCAAG;wCAClE,WAAU;;0DAEV,8OAAC;gDACC,SAAS;gDACT,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAA8B;gDAC9C,OAAO;gDACP,UAAU,CAAA,IAAK,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAW,CAAA;oDAAO,IAAI,EAAE,GAAG,KAAK,UAAU,YAAY;gDAAO;;;;;;0DAE/D,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;6DAKH;;0DACE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAA8B;8DAE7C,KAAK,KAAK;;;;;;;;;;;0DAIf,8OAAC;gDAAI,WAAU;gDAA8B,SAAS,CAAA,IAAK,EAAE,eAAe;0DAC1E,cAAA,8OAAC,4IAAA,CAAA,eAAY;;sEACX,8OAAC,4IAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,MAAK;gEACL,SAAQ;gEACR,WAAU;gEACV,OAAO;oEAAE,OAAO;gEAAsB;gEACtC,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEAC1C;gEACA,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEAC1C;gEACA,cAAW;0EAEX,cAAA,8OAAC,0NAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG5B,8OAAC,4IAAA,CAAA,sBAAmB;4DAClB,OAAM;4DACN,MAAK;4DACL,YAAY;4DACZ,WAAU;4DACV,OAAO;gEACL,iBAAiB;gEACjB,OAAO;4DACT;;8EAEA,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,aAAa,KAAK,EAAE,EAAE,KAAK,KAAK;oEAC/C,WAAU;oEACV,OAAO;wEACL,OAAO;wEACP,iBAAiB;oEACnB;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;8EACD;;;;;;8EAGD,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,aAAa,KAAK,EAAE;oEACnC,WAAU;oEACV,OAAO;wEACL,OAAO;wEACP,iBAAiB;oEACnB;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;8EACD;;;;;;;;;;;;;;;;;;;;;;;;;mCA7GN,KAAK,EAAE;;;;;4BAuHlB;4BAEC,CAAC,kBAAkB,YAAY,MAAM,KAAK,mBACzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oOAAA,CAAA,oBAAiB;wCAChB,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAA+B;;;;;;kDAEjD,8OAAC;wCAAE,WAAU;wCAAU,OAAO;4CAAE,OAAO;wCAA+B;kDAAG;;;;;;kDAGzE,8OAAC;wCAAE,WAAU;wCAAe,OAAO;4CAAE,OAAO;wCAA+B;kDAAG;;;;;;;;;;;;;;;;;;kCAQpF,8OAAC;wBAAI,WAAU;wBAA6B,OAAO;4BAAE,aAAa;wBAAwB;kCACxF,cAAA,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC;wCAAI,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAc;wCACxC,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACH,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAC,iBAAiB;gDAA0U;;;;;;0DAErW,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;oDAAqC,OAAO;wDAAE,OAAO;oDAA8B;8DAChG;;;;;;;;;;;;;;;;;;;;;;8CAKT,8OAAC,4IAAA,CAAA,sBAAmB;oCAClB,OAAM;oCACN,MAAK;oCACL,YAAY;oCACZ,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,OAAO;oCACT;;sDAEA,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;gDAC1B,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;;kEACJ,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIhC,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;gDAC3B,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;;kEACJ,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIpC,8OAAC,4IAAA,CAAA,mBAAgB;4CACf,SAAS;4CACT,WAAU;4CACV,OAAO;gDACL,OAAO;gDACP,iBAAiB;4CACnB;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;;8DAEA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUzC,6BACC,8OAAC;gBAAI,WAAU;gBAA4B,OAAO;oBAAE,aAAa;gBAAwB;0BACvF,cAAA,8OAAC,4IAAA,CAAA,eAAY;;sCACX,8OAAC,4IAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAC,iBAAiB;oCAA0U;;;;;;;;;;;;;;;;sCAIzW,8OAAC,4IAAA,CAAA,sBAAmB;4BAClB,OAAM;4BACN,MAAK;4BACL,YAAY;4BACZ,WAAU;4BACV,OAAO;gCACL,iBAAiB;gCACjB,OAAO;4BACT;;8CAEA,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;wCAC1B,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACJ,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIhC,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;wCAC3B,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACJ,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIpC,8OAAC,4IAAA,CAAA,mBAAgB;oCACf,SAAS;oCACT,WAAU;oCACV,OAAO;wCACL,OAAO;wCACP,iBAAiB;oCACnB;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;;sDAEA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;uCAEe", "debugId": null}}, {"offset": {"line": 2284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Layout.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { ReactNode, useState, useRef, useCallback } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useChatHistory } from \"@/providers/ChatHistoryContext\";\r\nimport { PageHeaderProvider } from '@/providers/PageHeaderContext';\r\nimport Header from './Header';\r\nimport Sidebar from './Sidebar';\r\n\r\ninterface LayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\r\n  const { setActiveChat } = useChatHistory();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);\r\n  const [isCreatingNewChat, setIsCreatingNewChat] = useState<boolean>(false);\r\n  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  const handleNewChat = useCallback(async () => {\r\n    // Prevent multiple simultaneous new chat creations\r\n    if (isCreatingNewChat) {\r\n      console.log('New chat creation already in progress, ignoring click');\r\n      return;\r\n    }\r\n\r\n    // Clear any existing debounce timeout\r\n    if (debounceTimeoutRef.current) {\r\n      clearTimeout(debounceTimeoutRef.current);\r\n    }\r\n\r\n    // Set debounce timeout to prevent rapid clicking\r\n    debounceTimeoutRef.current = setTimeout(async () => {\r\n      setIsCreatingNewChat(true);\r\n      \r\n      try {\r\n        // Clear the active chat to show the welcome message\r\n        await setActiveChat(null);\r\n        \r\n        // Navigate to the base chat route without a specific chat ID\r\n        router.push('/chat');\r\n      } catch (error) {\r\n        console.error('Error creating new chat:', error);\r\n      } finally {\r\n        setIsCreatingNewChat(false);\r\n      }\r\n    }, 300); // 300ms debounce to prevent rapid clicking\r\n  }, [isCreatingNewChat, setActiveChat, router]);\r\n\r\n  const handleToggleCollapse = (collapsed: boolean) => {\r\n    setSidebarCollapsed(collapsed);\r\n  };\r\n\r\n  // Cleanup timeout on unmount\r\n  React.useEffect(() => {\r\n    return () => {\r\n      if (debounceTimeoutRef.current) {\r\n        clearTimeout(debounceTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <PageHeaderProvider>\r\n      <div className=\"flex h-screen bg-sidebar-bg\" style={{ fontFamily: 'var(--font-inter), var(--font-noto-sans), sans-serif' }}>\r\n      <Sidebar \r\n        onNewChat={handleNewChat} \r\n        onToggleCollapse={handleToggleCollapse}\r\n        isCreatingNewChat={isCreatingNewChat}\r\n      />\r\n      <main className=\"flex flex-col flex-1 min-w-0 bg-sidebar-bg overflow-y-auto\">\r\n        <Header />\r\n        <div className=\"flex-1\">\r\n          {children}\r\n        </div>\r\n      </main>\r\n    </div>\r\n    </PageHeaderProvider>\r\n  );\r\n};\r\n\r\nexport default Layout;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAYA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAEzD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,mDAAmD;QACnD,IAAI,mBAAmB;YACrB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,sCAAsC;QACtC,IAAI,mBAAmB,OAAO,EAAE;YAC9B,aAAa,mBAAmB,OAAO;QACzC;QAEA,iDAAiD;QACjD,mBAAmB,OAAO,GAAG,WAAW;YACtC,qBAAqB;YAErB,IAAI;gBACF,oDAAoD;gBACpD,MAAM,cAAc;gBAEpB,6DAA6D;gBAC7D,OAAO,IAAI,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C,SAAU;gBACR,qBAAqB;YACvB;QACF,GAAG,MAAM,2CAA2C;IACtD,GAAG;QAAC;QAAmB;QAAe;KAAO;IAE7C,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,6BAA6B;IAC7B,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,OAAO;YACL,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,sIAAA,CAAA,qBAAkB;kBACjB,cAAA,8OAAC;YAAI,WAAU;YAA8B,OAAO;gBAAE,YAAY;YAAuD;;8BACzH,8OAAC,uIAAA,CAAA,UAAO;oBACN,WAAW;oBACX,kBAAkB;oBAClB,mBAAmB;;;;;;8BAErB,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,sIAAA,CAAA,UAAM;;;;;sCACP,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMX;uCAEe", "debugId": null}}, {"offset": {"line": 2406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/types/api.ts"], "sourcesContent": ["// API-related type definitions\r\n\r\nimport {\r\n  ChartQueryRequest,\r\n  ChartQueryResponse,\r\n  ChartTypesResponse,\r\n  ChartValidationResponse,\r\n  ChartHealthResponse\r\n} from './chart';\r\n\r\nexport interface Message {\r\n  role: 'user' | 'agent';\r\n  content: string;\r\n}\r\n\r\nexport interface QueryRequest {\r\n  query: string;\r\n  output_format?: string;\r\n  session_id?: string;\r\n  conversation_history?: Message[];\r\n  target_databases?: string[];\r\n  target_tables?: Record<string, string[]>;\r\n  target_columns?: Record<string, Record<string, string[]>>;\r\n  enable_token_streaming?: boolean;\r\n}\r\n\r\n// Database connection types\r\nexport interface DatabaseConnectionRequestParams {\r\n  name: string;\r\n  description?: string;\r\n  type: string; // Corresponds to DatabaseType enum on backend (e.g., \"POSTGRESQL\", \"MONGODB\")\r\n  host?: string;\r\n  port?: number;\r\n  username?: string;\r\n  password?: string;\r\n  database?: string;\r\n  db_schema?: string;\r\n  ssl_enabled?: boolean;\r\n  connection_string?: string;\r\n}\r\n\r\nexport interface DatabaseConnectionResponse {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n  message: string;\r\n  table_count?: number; \r\n}\r\n\r\nexport interface ConnectedDatabase {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n  description?: string;\r\n  host?: string;\r\n  database?: string;\r\n}\r\n\r\n// Chat-related types\r\nexport interface ChatListItem {\r\n  session_id: string;\r\n  title: string;\r\n  last_seen: number; // timestamp in milliseconds\r\n  message_count: number;\r\n}\r\n\r\nexport interface ApiChatMessage {\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n}\r\n\r\nexport interface ChatHistoryRequest {\r\n  session_id: string;\r\n}\r\n\r\n// Report types\r\nexport interface ReportInfo {\r\n  key: string;\r\n  session_id: string;\r\n  file_name: string;\r\n  size: number;\r\n  last_modified: string;\r\n  download_url: string;\r\n  content_type: string;\r\n  format: string;\r\n}\r\n\r\n// Streaming types\r\nexport interface TokenStreamEvent {\r\n  type: 'token_stream' | 'conversation_complete' | 'error';\r\n  agent: string;\r\n  data: {\r\n    token?: string;\r\n    message?: string;\r\n    error?: string;\r\n  };\r\n  timestamp: string;\r\n}\r\n\r\nexport interface StreamingResponse {\r\n  session_id: string;\r\n  conversation_id?: string;\r\n  status: 'streaming' | 'complete' | 'error';\r\n}\r\n\r\nexport interface ListReportsRequest {\r\n  max_reports?: number;\r\n}\r\n\r\nexport interface ListReportsResponse {\r\n  reports: ReportInfo[];\r\n  total_count: number;\r\n}\r\n\r\nexport interface DeleteChatRequest {\r\n  session_id: string;\r\n}\r\n\r\n// Forward declarations for types from other modules\r\nexport interface LoginRequest {\r\n  username: string;\r\n  password: string;\r\n}\r\n\r\nexport interface RegisterRequest {\r\n  email: string;\r\n  password: string;\r\n  full_name: string;\r\n}\r\n\r\nexport interface RegisterResponse {\r\n  message: string;\r\n  user_id?: string;\r\n  email?: string;\r\n}\r\n\r\nexport interface TokenResponse {\r\n  access_token: string;\r\n  token_type: string;\r\n  expires_at: string; // ISO 8601 date string\r\n  refresh_token: string;\r\n  user_id: string;\r\n  is_new_user?: boolean; // Flag indicating if user needs onboarding\r\n}\r\n\r\n// API Context type\r\nexport interface ApiContextType {\r\n  queryDatabases: (request: QueryRequest) => Promise<any>;\r\n  getDatabaseSchema: (dbId: string) => Promise<any>;\r\n  listDatabases: () => Promise<ConnectedDatabase[]>;\r\n  connectNewDatabase: (params: DatabaseConnectionRequestParams) => Promise<DatabaseConnectionResponse>;\r\n  disconnectExistingDatabase: (dbId: string) => Promise<{ message: string }>;\r\n  askQuery: (\r\n    query: string,\r\n    outputFormat: string,\r\n    conversationHistory: any[],\r\n    targetDatabases?: string[],\r\n    targetTables?: string[],\r\n    targetColumns?: string[]\r\n  ) => Promise<any>;\r\n  loginUser: (credentials: LoginRequest) => Promise<TokenResponse>;\r\n  registerUser: (credentials: RegisterRequest) => Promise<RegisterResponse>;\r\n  refreshToken: () => Promise<TokenResponse>;\r\n  logoutUser: () => Promise<void>;\r\n  getUserProfile: () => Promise<any>;\r\n  completeOnboarding: () => Promise<any>;\r\n  updateUserProfile: (data: any) => Promise<any>;\r\n  changePassword: (data: any) => Promise<any>;\r\n  saveEmailPreferences: (data: any) => Promise<any>;\r\n  savePrivacySettings: (data: any) => Promise<any>;\r\n  exportUserData: () => Promise<any>;\r\n  deleteAccount: () => Promise<any>;\r\n  listUserChats: () => Promise<ChatListItem[]>;\r\n  getChatHistory: (sessionId: string) => Promise<ApiChatMessage[]>;\r\n  listUserReports: (request?: ListReportsRequest) => Promise<ListReportsResponse>;\r\n  deleteChat: (sessionId: string) => Promise<{ message: string }>;\r\n  queryChart: (request: ChartQueryRequest) => Promise<ChartQueryResponse>;\r\n  getChartTypes: () => Promise<ChartTypesResponse>;\r\n  validateChartQuery: (request: ChartQueryRequest) => Promise<ChartValidationResponse>;\r\n  getChartHealth: () => Promise<ChartHealthResponse>;\r\n  // Dashboard management methods\r\n  listDashboards: () => Promise<any>;\r\n  createDashboard: (request: any) => Promise<any>;\r\n  getDashboard: (dashboardId: string) => Promise<any>;\r\n  updateDashboard: (dashboardId: string, request: any) => Promise<any>;\r\n  deleteDashboard: (dashboardId: string) => Promise<any>;\r\n  \r\n  // Analysis project management methods\r\n  listAnalysisProjects: () => Promise<any>;\r\n  createAnalysisProject: (request: any) => Promise<any>;\r\n  getAnalysisProject: (projectId: string) => Promise<any>;\r\n  updateAnalysisProject: (projectId: string, request: any) => Promise<any>;\r\n  deleteAnalysisProject: (projectId: string) => Promise<any>;\r\n  getProjectData: (projectId: string, options?: any) => Promise<any>;\r\n  getProjectSteps: (projectId: string) => Promise<any>;\r\n  executeStep: (projectId: string, stepId: string) => Promise<any>;\r\n  uploadProjectFile: (request: any) => Promise<any>;\r\n  deleteProjectFile: (projectId: string, fileId: string) => Promise<any>;\r\n}\r\n"], "names": [], "mappings": "AAAA,+BAA+B", "debugId": null}}, {"offset": {"line": 2415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/types/auth.ts"], "sourcesContent": ["// Authentication-related type definitions\r\n\r\nimport { LoginRequest, RegisterRequest, TokenResponse } from './api';\r\n\r\nexport interface OAuthTokens {\r\n  access_token: string;\r\n  refresh_token: string;\r\n  expires_in: number;\r\n  user_id: string;\r\n  token_type?: string;\r\n}\r\n\r\nexport interface AuthContextType {\r\n  isAuthenticated: boolean;\r\n  user: TokenResponse | null;\r\n  isLoading: boolean;\r\n  isNewUser: boolean;\r\n  isCompletingOnboarding: boolean;\r\n  login: (credentials: LoginRequest) => Promise<void>;\r\n  signIn: (credentials?: LoginRequest, shouldRedirect?: boolean) => Promise<void>;\r\n  initializeAuthFromStorage: () => Promise<boolean>;\r\n  register: (credentials: RegisterRequest) => Promise<void>;\r\n  logout: () => Promise<void>;\r\n  setUser: (user: TokenResponse | null) => void;\r\n  refreshUserToken: () => Promise<void>;\r\n  handleOAuthCallback: (tokens: OAuthTokens) => Promise<void>;\r\n  completeOnboarding: () => Promise<void>;\r\n}\r\n\r\nexport interface User {\r\n  id: string;\r\n  username: string;\r\n  email?: string;\r\n  is_new_user?: boolean;\r\n  // Add other user properties as needed\r\n}\r\n\r\n// Onboarding-related types\r\nexport interface OnboardingStep {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  component: React.ComponentType<OnboardingStepProps>;\r\n  isCompleted: boolean;\r\n  isOptional?: boolean;\r\n}\r\n\r\nexport interface OnboardingStepProps {\r\n  onNext: () => void;\r\n  onPrevious: () => void;\r\n  onComplete: () => Promise<void>;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport interface OnboardingData {\r\n  welcome_completed?: boolean;\r\n  profile_setup_completed?: boolean;\r\n  preferences_completed?: boolean;\r\n  tutorial_completed?: boolean;\r\n}\r\n\r\nexport interface OnboardingProgress {\r\n  currentStep: string;\r\n  completedSteps: string[];\r\n  lastUpdated: string;\r\n  canResume: boolean;\r\n  totalSteps: number;\r\n}\r\n\r\nexport interface OnboardingState {\r\n  isNewUser: boolean;\r\n  progress?: OnboardingProgress;\r\n  isCompleting: boolean;\r\n  hasError: boolean;\r\n  errorMessage?: string;\r\n  canRetry: boolean;\r\n}\r\n"], "names": [], "mappings": "AAAA,0CAA0C", "debugId": null}}, {"offset": {"line": 2424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/types/profile.ts"], "sourcesContent": ["// Profile and Settings related type definitions\r\n\r\nexport interface UserProfile {\r\n  id: string;\r\n  username: string;\r\n  email?: string;\r\n  full_name?: string;\r\n  bio?: string;\r\n  profile_picture_url?: string;\r\n  created_at: string;\r\n  last_login?: string;\r\n  auth_method?: 'email' | 'google_oauth' | 'github_oauth';\r\n  email_verified?: boolean;\r\n  is_active?: boolean;\r\n}\r\n\r\nexport interface UpdateProfileRequest {\r\n  full_name?: string;\r\n  bio?: string;\r\n  email?: string;\r\n  profile_picture_url?: string;\r\n}\r\n\r\nexport interface ChangePasswordRequest {\r\n  current_password: string;\r\n  new_password: string;\r\n}\r\n\r\nexport interface EmailPreferencesRequest {\r\n  marketing_emails: boolean;\r\n  security_alerts: boolean;\r\n  product_updates: boolean;\r\n  weekly_digest: boolean;\r\n}\r\n\r\nexport interface PrivacySettingsRequest {\r\n  profile_visibility: 'public' | 'private';\r\n  data_collection: boolean;\r\n  analytics_opt_out: boolean;\r\n  third_party_sharing: boolean;\r\n}\r\n\r\nexport interface OAuthConnection {\r\n  provider: 'google' | 'github';\r\n  connected: boolean;\r\n  connected_at?: string;\r\n  email?: string;\r\n}\r\n\r\nexport interface AccountDeletionRequest {\r\n  confirmation_text: string;\r\n  reason?: string;\r\n}\r\n\r\n// API Response types\r\nexport interface UpdateProfileResponse {\r\n  success: boolean;\r\n  profile: UserProfile;\r\n  message?: string;\r\n}\r\n\r\nexport interface ChangePasswordResponse {\r\n  success: boolean;\r\n  message: string;\r\n}\r\n\r\nexport interface DataExportResponse {\r\n  download_url: string;\r\n  expires_at: string;\r\n  file_size: number;\r\n}\r\n\r\nexport interface PreferencesResponse {\r\n  success: boolean;\r\n  message: string;\r\n} "], "names": [], "mappings": "AAAA,gDAAgD", "debugId": null}}, {"offset": {"line": 2433, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/types/streaming.ts"], "sourcesContent": ["// Streaming-related type definitions\n\nexport interface StreamingState {\n  isStreaming: boolean;\n  streamingContent: string;\n  error: string | null;\n  isComplete: boolean;\n}\n\nexport interface StreamingMessage {\n  id: string;\n  role: 'user' | 'agent';\n  content: string;\n  isStreaming?: boolean;\n  streamingContent?: string;\n  timestamp?: Date;\n}\n\nexport interface UseTokenStreamingOptions {\n  sessionId: string;\n  query: string;\n  outputFormat?: string;\n  onTokenReceived?: (token: string) => void;\n  onComplete?: (finalContent: string) => void;\n  onError?: (error: string) => void;\n}\n\nexport interface UseTokenStreamingReturn {\n  startStreaming: () => void;\n  stopStreaming: () => void;\n  streamingState: StreamingState;\n  isConnected: boolean;\n}\n\n// SSE Event types from backend\nexport interface SSETokenEvent {\n  type: 'token_stream';\n  agent: string;\n  data: {\n    token: string;\n  };\n  timestamp: string;\n}\n\nexport interface SSECompleteEvent {\n  type: 'conversation_complete';\n  agent: string;\n  data: {\n    message: string;\n  };\n  timestamp: string;\n}\n\nexport interface SSEErrorEvent {\n  type: 'error';\n  agent: string;\n  data: {\n    error: string;\n  };\n  timestamp: string;\n}\n\nexport type SSEEvent = SSETokenEvent | SSECompleteEvent | SSEErrorEvent;\n\n// Typewriter effect configuration\nexport interface TypewriterConfig {\n  speed: number; // milliseconds per character\n  enableCursor: boolean;\n  cursorChar: string;\n}\n"], "names": [], "mappings": "AAAA,qCAAqC", "debugId": null}}, {"offset": {"line": 2442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/types/chart.ts"], "sourcesContent": ["// Chart-related TypeScript interfaces\r\n\r\nexport type ChartType = 'bar' | 'line' | 'pie' | 'area' | 'table' | 'timebar' | 'funnel' | 'number' | 'image' | 'detail' | 'text' | 'activity';\r\n\r\nexport interface ChartDataPoint {\r\n  label: string;\r\n  value: number;\r\n  category?: string;\r\n}\r\n\r\nexport interface ChartMetadata {\r\n  xAxisLabel?: string;\r\n  yAxisLabel?: string;\r\n  colors?: string[];\r\n  sqlQuery?: string;\r\n  description?: string;\r\n  dataSource?: string;\r\n  generatedAt?: string;\r\n}\r\n\r\nexport interface ChartData {\r\n  title: string;\r\n  chartType: ChartType;\r\n  data: ChartDataPoint[];\r\n  metadata: ChartMetadata;\r\n}\r\n\r\nexport interface ChartQueryRequest {\r\n  prompt: string;\r\n  user_id?: string;\r\n  dashboard_id?: string;\r\n  database_id?: string;\r\n}\r\n\r\nexport interface ChartQueryResponse {\r\n  success: boolean;\r\n  data: ChartData;\r\n  error?: string;\r\n}\r\n\r\nexport interface ChartTypeRecommendation {\r\n  chart_type: ChartType;\r\n  confidence: number;\r\n  reasoning: string;\r\n  alternative_types?: ChartType[];\r\n}\r\n\r\nexport interface ChartTypesResponse {\r\n  supported_types: ChartType[];\r\n  descriptions: Record<ChartType, string>;\r\n  default_colors: string[];\r\n}\r\n\r\nexport interface ChartValidationResponse {\r\n  valid: boolean;\r\n  recommended_chart_type?: ChartType;\r\n  confidence?: number;\r\n  reasoning?: string;\r\n  alternative_types?: ChartType[];\r\n  error?: string;\r\n}\r\n\r\nexport interface ChartHealthResponse {\r\n  status: 'healthy' | 'unhealthy';\r\n  service: string;\r\n  timestamp: string;\r\n  dependencies?: Record<string, string>;\r\n  error?: string;\r\n}\r\n\r\nexport interface ChartWidget {\r\n  id: string;\r\n  title: string;\r\n  chartData: ChartData | null;\r\n  isLoading: boolean;\r\n  error: string | null;\r\n  dashboard_id?: string; // Optional dashboard association\r\n  pendingPrompt?: string; // Stores user prompt when waiting for database selection\r\n  layout: {\r\n    x: number;\r\n    y: number;\r\n    w: number;\r\n    h: number;\r\n  };\r\n}\r\n\r\nexport interface DashboardLayout {\r\n  widgets: ChartWidget[];\r\n  gridCols: number;\r\n  gridRows: number;\r\n}\r\n\r\n// Grid layout types for react-grid-layout\r\nexport interface GridLayoutItem {\r\n  i: string;\r\n  x: number;\r\n  y: number;\r\n  w: number;\r\n  h: number;\r\n  minW?: number;\r\n  minH?: number;\r\n  maxW?: number;\r\n  maxH?: number;\r\n  static?: boolean;\r\n  isDraggable?: boolean;\r\n  isResizable?: boolean;\r\n}\r\n\r\nexport interface GridLayoutBreakpoint {\r\n  lg: GridLayoutItem[];\r\n  md: GridLayoutItem[];\r\n  sm: GridLayoutItem[];\r\n  xs: GridLayoutItem[];\r\n  xxs: GridLayoutItem[];\r\n}\r\n\r\n// Chart configuration constants\r\nexport const CHART_CONFIG = {\r\n  DEFAULT_WIDGET_SIZE: { w: 4, h: 5 },\r\n  MIN_WIDGET_SIZE: { w: 3, h: 4 },\r\n  MAX_WIDGET_SIZE: { w: 12, h: 15 }, // Max height is 3x default height (5 * 3 = 15)\r\n  MAX_WIDGETS: 12,\r\n  GRID_COLS: { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 },\r\n  BREAKPOINTS: { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 },\r\n  ROW_HEIGHT: 85,\r\n} as const;\r\n\r\nexport const DEFAULT_CHART_COLORS = [\r\n  '#8b5cf6', // Purple\r\n  '#06b6d4', // Cyan\r\n  '#10b981', // Emerald\r\n  '#f59e0b', // Amber\r\n  '#ef4444', // Red\r\n  '#8b5cf6', // Purple variant\r\n  '#06b6d4', // Cyan variant\r\n  '#10b981', // Emerald variant\r\n  '#f59e0b', // Amber variant\r\n  '#ef4444', // Red variant\r\n] as const;\r\n\r\n// Drag-to-delete state types\r\nexport interface DragState {\r\n  isDragging: boolean;\r\n  draggedWidgetId: string | null;\r\n  isOverDeleteZone: boolean;\r\n}\r\n\r\nexport interface DragToDeleteZoneProps {\r\n  isVisible: boolean;\r\n  isHovered: boolean;\r\n}\r\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;AAqH/B,MAAM,eAAe;IAC1B,qBAAqB;QAAE,GAAG;QAAG,GAAG;IAAE;IAClC,iBAAiB;QAAE,GAAG;QAAG,GAAG;IAAE;IAC9B,iBAAiB;QAAE,GAAG;QAAI,GAAG;IAAG;IAChC,aAAa;IACb,WAAW;QAAE,IAAI;QAAI,IAAI;QAAI,IAAI;QAAG,IAAI;QAAG,KAAK;IAAE;IAClD,aAAa;QAAE,IAAI;QAAM,IAAI;QAAK,IAAI;QAAK,IAAI;QAAK,KAAK;IAAE;IAC3D,YAAY;AACd;AAEO,MAAM,uBAAuB;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 2495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/types/dashboard.ts"], "sourcesContent": ["// Dashboard-related TypeScript interfaces\n\nimport { ChartWidget } from './chart';\n\nexport interface Dashboard {\n  id: string;\n  name: string;\n  description: string;\n  created_at: string;\n  updated_at: string;\n  user_id: string;\n}\n\nexport interface DashboardWithCharts extends Dashboard {\n  widgets: ChartWidget[];\n}\n\nexport interface CreateDashboardRequest {\n  name: string;\n  description: string;\n}\n\nexport interface UpdateDashboardRequest {\n  name?: string;\n  description?: string;\n}\n\nexport interface DashboardListResponse {\n  success: boolean;\n  data: Dashboard[];\n  error?: string;\n}\n\nexport interface DashboardResponse {\n  success: boolean;\n  data: DashboardWithCharts;\n  error?: string;\n}\n\nexport interface CreateDashboardResponse {\n  success: boolean;\n  data: Dashboard;\n  error?: string;\n}\n\nexport interface UpdateDashboardResponse {\n  success: boolean;\n  data: Dashboard;\n  error?: string;\n}\n\nexport interface DeleteDashboardResponse {\n  success: boolean;\n  message: string;\n  error?: string;\n}\n\n// Dashboard view states\nexport type DashboardViewState = 'list' | 'dashboard';\n\nexport interface DashboardNavigationState {\n  currentView: DashboardViewState;\n  selectedDashboard: Dashboard | null;\n  breadcrumbs: Array<{\n    label: string;\n    onClick?: () => void;\n  }>;\n}\n\n// Dashboard statistics\nexport interface DashboardStats {\n  totalDashboards: number;\n  totalCharts: number;\n  activeCharts: number;\n  recentActivity: string;\n}\n"], "names": [], "mappings": "AAAA,0CAA0C", "debugId": null}}, {"offset": {"line": 2504, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/types/chat.ts"], "sourcesContent": ["// Chat-related type definitions\r\n\r\nexport interface ChatHistoryItem {\r\n  id: string;\r\n  session_id: string; // Backend session ID\r\n  title: string;\r\n  created_at: Date;\r\n  last_updated: Date;\r\n  message_count: number;\r\n}\r\n\r\nexport interface ChatMessage {\r\n  role: 'user' | 'agent';\r\n  content: string;\r\n  timestamp?: Date;\r\n  outputFiles?: Array<{\r\n    database_name: string;\r\n    file_path: string;\r\n    format: string;\r\n  }>;\r\n  sqlQueries?: Record<string, string>;\r\n}\r\n\r\nexport interface ChatHistoryContextType {\r\n  chatHistory: ChatHistoryItem[];\r\n  activeChat: ChatHistoryItem | null;\r\n  chatMessages: { [sessionId: string]: ChatMessage[] };\r\n  isLoadingChats: boolean;\r\n  isLoadingHistory: boolean;\r\n  pendingFirstMessage: string | null;\r\n  setPendingFirstMessage: (message: string | null) => void;\r\n  addChat: (title?: string) => ChatHistoryItem;\r\n  updateChatSessionId: (chatId: string, newSessionId: string) => ChatHistoryItem | null;\r\n  deleteChat: (id: string) => Promise<void>;\r\n  renameChat: (id: string, newTitle: string) => void;\r\n  setActiveChat: (chat: ChatHistoryItem | null) => Promise<void>;\r\n  loadChatById: (chatId: string) => Promise<ChatHistoryItem | null>;\r\n  addMessageToChat: (sessionId: string, message: ChatMessage) => void;\r\n  loadChatHistory: (sessionId: string) => Promise<void>;\r\n  refreshChatList: () => Promise<void>;\r\n  generateSessionId: () => string;\r\n}\r\n\r\nexport interface ChatProps {\r\n  chatId?: string;\r\n}\r\n"], "names": [], "mappings": "AAAA,gCAAgC", "debugId": null}}, {"offset": {"line": 2513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/types/index.ts"], "sourcesContent": ["// Main type exports - barrel file for easy imports\r\n\r\n// Re-export all types from individual modules\r\nexport * from './api';\r\nexport * from './auth';\r\nexport * from './profile';\r\nexport * from './streaming';\r\nexport * from './chart';\r\nexport * from './dashboard';\r\nexport * from './chat';\r\n\r\n// Common utility types\r\nexport interface BaseEntity {\r\n  id: string;\r\n  created_at: Date;\r\n  updated_at: Date;\r\n}\r\n\r\nexport interface PaginationParams {\r\n  page?: number;\r\n  limit?: number;\r\n  offset?: number;\r\n}\r\n\r\nexport interface PaginationResponse<T> {\r\n  data: T[];\r\n  total: number;\r\n  page: number;\r\n  limit: number;\r\n  hasNext: boolean;\r\n  hasPrev: boolean;\r\n}\r\n\r\nexport interface ApiResponse<T = any> {\r\n  success: boolean;\r\n  data?: T;\r\n  message?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface LoadingState {\r\n  isLoading: boolean;\r\n  error?: string | null;\r\n}\r\n\r\n// Theme types\r\nexport type Theme = 'light' | 'dark' | 'system';\r\n\r\nexport interface ThemeContextType {\r\n  theme: Theme;\r\n  setTheme: (theme: Theme) => void;\r\n  resolvedTheme?: Theme;\r\n  mounted: boolean;\r\n}\r\n\r\n// Analysis project types\r\nexport interface AnalysisProject {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  status: 'draft' | 'running' | 'completed' | 'error';\r\n  createdAt: string; // ISO date string\r\n  updatedAt: string; // ISO date string\r\n  dataSource?: string;\r\n  stepCount: number;\r\n  progress?: number;\r\n  userId: string;\r\n}\r\n\r\nexport interface PipelineStep {\r\n  id: string;\r\n  projectId: string;\r\n  title: string;\r\n  status: 'pending' | 'running' | 'completed' | 'error';\r\n  type: 'data_loading' | 'processing' | 'analysis' | 'visualization' | 'export';\r\n  order: number;\r\n  createdAt: string; // ISO date string\r\n  updatedAt: string; // ISO date string\r\n  outputs?: PipelineStepOutput;\r\n}\r\n\r\nexport interface PipelineStepOutput {\r\n  data?: DataOutput;\r\n  code?: string;\r\n  visualization?: VisualizationOutput;\r\n  summary?: string;\r\n}\r\n\r\nexport interface DataOutput {\r\n  rows: Record<string, any>[];\r\n  columns: DataColumn[];\r\n  totalRows: number;\r\n  schema?: Record<string, string>;\r\n}\r\n\r\nexport interface DataColumn {\r\n  name: string;\r\n  type: 'string' | 'number' | 'boolean' | 'date';\r\n  nullable: boolean;\r\n}\r\n\r\nexport interface VisualizationOutput {\r\n  type: 'chart' | 'graph' | 'table' | 'heatmap';\r\n  config: Record<string, any>;\r\n  data: Record<string, any>;\r\n}\r\n\r\n// API Request/Response types\r\nexport interface CreateAnalysisProjectRequest {\r\n  name: string;\r\n  description?: string;\r\n  dataSource?: string;\r\n}\r\n\r\nexport interface UpdateAnalysisProjectRequest {\r\n  name?: string;\r\n  description?: string;\r\n  status?: AnalysisProject['status'];\r\n}\r\n\r\nexport interface ListAnalysisProjectsResponse {\r\n  projects: AnalysisProject[];\r\n  totalCount: number;\r\n}\r\n\r\nexport interface GetProjectDataRequest {\r\n  projectId: string;\r\n  stepId?: string;\r\n  page?: number;\r\n  pageSize?: number;\r\n}\r\n\r\nexport interface GetProjectDataResponse {\r\n  project: AnalysisProject;\r\n  steps: PipelineStep[];\r\n  data?: DataOutput;\r\n}\r\n\r\n// File upload types\r\nexport interface FileUploadProgress {\r\n  loaded: number;\r\n  total: number;\r\n  percentage: number;\r\n}\r\n\r\nexport interface UploadedFile {\r\n  id: string;\r\n  filename: string;\r\n  size: number;\r\n  type: string;\r\n  uploadedAt: string;\r\n  status: 'uploading' | 'processing' | 'completed' | 'error';\r\n  errorMessage?: string;\r\n}\r\n\r\nexport interface FileUploadRequest {\r\n  file: File;\r\n  projectId: string;\r\n  onProgress?: (progress: FileUploadProgress) => void;\r\n}\r\n\r\nexport interface FileUploadResponse {\r\n  success: boolean;\r\n  data?: {\r\n    fileId: string;\r\n    filename: string;\r\n    rowCount: number;\r\n    columnCount: number;\r\n    previewData: DataOutput;\r\n  };\r\n  error?: string;\r\n}\r\n"], "names": [], "mappings": "AAAA,mDAAmD;AAEnD,8CAA8C;;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/hooks/usePageTitle.ts"], "sourcesContent": ["import { useEffect, useMemo } from 'react';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\n\r\ninterface UsePageTitleOptions {\r\n  title: string;\r\n  subtitle?: string;\r\n  icon?: React.ComponentType<{ className?: string }>;\r\n}\r\n\r\n/**\r\n * Custom hook for setting page-specific header information\r\n * \r\n * Industry standard approach for managing page titles and metadata.\r\n * Automatically handles cleanup when component unmounts.\r\n * \r\n * @param options - Page header configuration\r\n */\r\nexport const usePageTitle = (options: UsePageTitleOptions) => {\r\n  const { setPageHeader, resetPageHeader } = usePageHeader();\r\n\r\n  useEffect(() => {\r\n    // Set the page header when component mounts or options change\r\n    setPageHeader(options);\r\n\r\n    // Cleanup: reset to default when component unmounts\r\n    return () => {\r\n      resetPageHeader();\r\n    };\r\n  }, [\r\n    options.title, \r\n    options.subtitle, \r\n    options.icon, \r\n    setPageHeader, \r\n    resetPageHeader\r\n  ]);\r\n};\r\n\r\n/**\r\n * Simplified hook for just setting a page title\r\n * \r\n * @param title - Page title\r\n * @param icon - Optional icon component\r\n */\r\nexport const useSimplePageTitle = (title: string, icon?: React.ComponentType<{ className?: string }>) => {\r\n  usePageTitle({ title, icon });\r\n}; "], "names": [], "mappings": ";;;;AAAA;AACA;;;AAgBO,MAAM,eAAe,CAAC;IAC3B,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8DAA8D;QAC9D,cAAc;QAEd,oDAAoD;QACpD,OAAO;YACL;QACF;IACF,GAAG;QACD,QAAQ,KAAK;QACb,QAAQ,QAAQ;QAChB,QAAQ,IAAI;QACZ;QACA;KACD;AACH;AAQO,MAAM,qBAAqB,CAAC,OAAe;IAChD,aAAa;QAAE;QAAO;IAAK;AAC7B", "debugId": null}}, {"offset": {"line": 2588, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils/index\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold text-white\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6 text-white\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAAA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/dashboard/DashboardCard.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { MoreHorizontal, BarChart3, Calendar, Trash2, Edit } from 'lucide-react';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Dashboard } from '@/types';\n\ninterface DashboardCardProps {\n  dashboard: Dashboard;\n  chartCount: number;\n  onSelect: (dashboard: Dashboard) => void;\n  onEdit?: (dashboard: Dashboard) => void;\n  onDelete: (dashboardId: string) => void;\n  className?: string;\n}\n\nconst DashboardCard: React.FC<DashboardCardProps> = ({\n  dashboard,\n  chartCount,\n  onSelect,\n  onEdit,\n  onDelete,\n  className = '',\n}) => {\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const handleCardClick = (e: React.MouseEvent) => {\n    // Prevent card click when dropdown is clicked\n    if ((e.target as HTMLElement).closest('[data-dropdown-trigger]')) {\n      return;\n    }\n    onSelect(dashboard);\n  };\n\n  return (\n    <Card \n      className={`cursor-pointer hover:shadow-md transition-shadow duration-200 ${className}`}\n      onClick={handleCardClick}\n    >\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n        <div className=\"flex items-center space-x-2\">\n          <BarChart3 className=\"h-5 w-5 text-primary\" />\n          <h3 className=\"font-semibold text-lg truncate\">{dashboard.name}</h3>\n        </div>\n        \n        <DropdownMenu>\n          <DropdownMenuTrigger asChild data-dropdown-trigger>\n            <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\n              <MoreHorizontal className=\"h-4 w-4\" />\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent align=\"end\">\n            {onEdit && (\n              <DropdownMenuItem onClick={() => onEdit(dashboard)}>\n                <Edit className=\"h-4 w-4 mr-2\" />\n                Edit Dashboard\n              </DropdownMenuItem>\n            )}\n            <DropdownMenuItem\n              onClick={() => onDelete(dashboard.id)}\n              className=\"text-destructive focus:text-destructive\"\n            >\n              <Trash2 className=\"h-4 w-4 mr-2\" />\n              Delete Dashboard\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </CardHeader>\n\n      <CardContent className=\"space-y-4\">\n        <p className=\"text-muted-foreground text-sm line-clamp-2\">\n          {dashboard.description || 'No description provided'}\n        </p>\n        \n        <div className=\"flex items-center justify-between text-sm\">\n          <div className=\"flex items-center space-x-1 text-muted-foreground\">\n            <BarChart3 className=\"h-4 w-4\" />\n            <span>{chartCount} chart{chartCount !== 1 ? 's' : ''}</span>\n          </div>\n          \n          <div className=\"flex items-center space-x-1 text-muted-foreground\">\n            <Calendar className=\"h-4 w-4\" />\n            <span>{formatDate(dashboard.updated_at)}</span>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default DashboardCard;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;AAuBA,MAAM,gBAA8C,CAAC,EACnD,SAAS,EACT,UAAU,EACV,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,YAAY,EAAE,EACf;IACC,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,8CAA8C;QAC9C,IAAI,AAAC,EAAE,MAAM,CAAiB,OAAO,CAAC,4BAA4B;YAChE;QACF;QACA,SAAS;IACX;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QACH,WAAW,CAAC,8DAA8D,EAAE,WAAW;QACvF,SAAS;;0BAET,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAG,WAAU;0CAAkC,UAAU,IAAI;;;;;;;;;;;;kCAGhE,8OAAC,4IAAA,CAAA,eAAY;;0CACX,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAO;gCAAC,uBAAqB;0CAChD,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAC1C,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAM;;oCACxB,wBACC,8OAAC,4IAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,OAAO;;0DACtC,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIrC,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS,IAAM,SAAS,UAAU,EAAE;wCACpC,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAE,WAAU;kCACV,UAAU,WAAW,IAAI;;;;;;kCAG5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;;4CAAM;4CAAW;4CAAO,eAAe,IAAI,MAAM;;;;;;;;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAM,WAAW,UAAU,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlD;uCAEe", "debugId": null}}, {"offset": {"line": 2922, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/dashboard/DashboardList.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Plus } from 'lucide-react';\r\nimport { Dashboard } from '@/types';\r\nimport DashboardCard from './DashboardCard';\r\n\r\ninterface DashboardListProps {\r\n  dashboards: Dashboard[];\r\n  dashboardStats: Record<string, number>;\r\n  onSelectDashboard: (dashboard: Dashboard) => void;\r\n  onDeleteDashboard: (dashboardId: string) => void;\r\n  className?: string;\r\n}\r\n\r\nconst DashboardList: React.FC<DashboardListProps> = ({\r\n  dashboards,\r\n  dashboardStats,\r\n  onSelectDashboard,\r\n  onDeleteDashboard,\r\n  className = '',\r\n}) => {\r\n  return (\r\n    <div className={`space-y-6 ${className}`}>\r\n      {/* Dashboard Grid */}\r\n      <div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          {dashboards.map((dashboard) => (\r\n            <DashboardCard\r\n              key={dashboard.id}\r\n              dashboard={dashboard}\r\n              chartCount={dashboardStats[dashboard.id] || 0}\r\n              onSelect={onSelectDashboard}\r\n              onDelete={onDeleteDashboard}\r\n            />\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardList;\r\n"], "names": [], "mappings": ";;;;AAOA;AAPA;;;AAiBA,MAAM,gBAA8C,CAAC,EACnD,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,iBAAiB,EACjB,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;kBAEtC,cAAA,8OAAC;sBAEC,cAAA,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,0BACf,8OAAC,4JAAA,CAAA,UAAa;wBAEZ,WAAW;wBACX,YAAY,cAAc,CAAC,UAAU,EAAE,CAAC,IAAI;wBAC5C,UAAU;wBACV,UAAU;uBAJL,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;AAW/B;uCAEe", "debugId": null}}, {"offset": {"line": 2969, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/dashboard/ChartRenderer.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useRef, useEffect, useState } from 'react';\nimport {\n  Bar,\n  Bar<PERSON>hart,\n  Line,\n  LineChart,\n  Pie,\n  PieChart,\n  Area,\n  AreaChart,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  ResponsiveContainer,\n  Cell,\n  Tooltip,\n} from 'recharts';\nimport { ChartData, DEFAULT_CHART_COLORS } from '@/types';\nimport { useTheme } from 'next-themes';\n\ninterface ChartRendererProps {\n  chartData: ChartData;\n  width?: number;\n  height?: number;\n  className?: string;\n}\n\n// Responsive font size based on container dimensions\nconst getResponsiveFontSize = (width?: number, height?: number) => {\n  if (!width || !height) return 10;\n  const minDimension = Math.min(width, height);\n  if (minDimension < 200) return 8;\n  if (minDimension < 300) return 9;\n  if (minDimension < 400) return 10;\n  return 11;\n};\n\n// Responsive axis dimensions with better handling for small heights\nconst getAxisDimensions = (width?: number, height?: number) => {\n  if (!width || !height) return { xHeight: 20, yWidth: 25 };\n  \n  // For very small heights, reduce axis dimensions significantly\n  if (height < 150) return { xHeight: 12, yWidth: 15 };\n  if (height < 200) return { xHeight: 15, yWidth: 18 };\n  if (height < 300) return { xHeight: 18, yWidth: 22 };\n  return { xHeight: 20, yWidth: 25 };\n};\n\n// Calculate optimal margins based on container size\nconst getOptimalMargins = (width?: number, height?: number, chartType?: string) => {\n  if (!width || !height) return { top: 5, right: 5, left: 5, bottom: 5 };\n  \n  // For bar charts in very small containers, use minimal margins\n  if (chartType === 'bar' && height < 150) {\n    return { top: 2, right: 2, left: 2, bottom: 2 };\n  }\n  \n  if (height < 200) {\n    return { top: 3, right: 3, left: 3, bottom: 3 };\n  }\n  \n  return { top: 5, right: 5, left: 5, bottom: 5 };\n};\n\nconst ChartRenderer: React.FC<ChartRendererProps> = ({\n  chartData,\n  width,\n  height,\n  className = '',\n}) => {\n  const { chartType, data, metadata } = chartData;\n  const { resolvedTheme } = useTheme();\n  const isDark = resolvedTheme === 'dark';\n\n  // Responsive sizing\n  const fontSize = getResponsiveFontSize(width, height);\n  const { xHeight, yWidth } = getAxisDimensions(width, height);\n  const margins = getOptimalMargins(width, height, chartType);\n\n  // Theme-aware colors\n  const getThemeColors = () => ({\n    grid: isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)',\n    text: isDark ? 'hsl(215 20.2% 65.1%)' : 'hsl(215.4 16.3% 46.9%)',\n    tooltipBg: isDark ? 'hsl(222.2 84% 4.9%)' : 'hsl(0 0% 100%)',\n    tooltipBorder: isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)',\n    tooltipText: isDark ? 'hsl(210 40% 98%)' : 'hsl(222.2 84% 4.9%)',\n    primary: isDark ? 'hsl(217.2 91.2% 59.8%)' : 'hsl(221.2 83.2% 53.3%)',\n  });\n\n  const themeColors = getThemeColors();\n\n  // For very small heights, consider hiding some elements\n  const showGrid = height ? height > 120 : true;\n  const showXAxis = height ? height > 100 : true;\n  const showYAxis = height ? height > 100 : true;\n\n  const renderChart = () => {\n    switch (chartType) {\n      case 'bar':\n        return (\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <BarChart data={data} margin={margins}>\n              {showGrid && <CartesianGrid strokeDasharray=\"3 3\" stroke={themeColors.grid} />}\n              {showXAxis && (\n                <XAxis\n                  dataKey=\"label\"\n                  axisLine={false}\n                  tickLine={false}\n                  tick={{ fill: themeColors.text, fontSize }}\n                  height={xHeight}\n                  interval={width && width < 200 ? 'preserveStartEnd' : 0}\n                />\n              )}\n              {showYAxis && (\n                <YAxis\n                  axisLine={false}\n                  tickLine={false}\n                  tick={{ fill: themeColors.text, fontSize }}\n                  width={yWidth}\n                  tickCount={height && height < 150 ? 3 : 5}\n                />\n              )}\n              <Tooltip\n                contentStyle={{\n                  backgroundColor: themeColors.tooltipBg,\n                  border: `1px solid ${themeColors.tooltipBorder}`,\n                  borderRadius: '8px',\n                  color: themeColors.tooltipText,\n                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'\n                }}\n              />\n              <Bar\n                dataKey=\"value\"\n                fill={metadata.colors?.[0] || themeColors.primary}\n                radius={height && height < 150 ? [1, 1, 0, 0] : [2, 2, 0, 0]}\n              />\n            </BarChart>\n          </ResponsiveContainer>\n        );\n\n      case 'line':\n        return (\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <LineChart data={data} margin={margins}>\n              {showGrid && <CartesianGrid strokeDasharray=\"3 3\" stroke={themeColors.grid} />}\n              {showXAxis && (\n                <XAxis\n                  dataKey=\"label\"\n                  axisLine={false}\n                  tickLine={false}\n                  tick={{ fill: themeColors.text, fontSize }}\n                  height={xHeight}\n                />\n              )}\n              {showYAxis && (\n                <YAxis\n                  axisLine={false}\n                  tickLine={false}\n                  tick={{ fill: themeColors.text, fontSize }}\n                  width={yWidth}\n                />\n              )}\n              <Tooltip\n                contentStyle={{\n                  backgroundColor: themeColors.tooltipBg,\n                  border: `1px solid ${themeColors.tooltipBorder}`,\n                  borderRadius: '8px',\n                  color: themeColors.tooltipText,\n                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'\n                }}\n              />\n              <Line\n                type=\"monotone\"\n                dataKey=\"value\"\n                stroke={metadata.colors?.[0] || themeColors.primary}\n                strokeWidth={2}\n                dot={{ fill: metadata.colors?.[0] || themeColors.primary, strokeWidth: 0, r: 3 }}\n                activeDot={{ r: 5, fill: metadata.colors?.[0] || themeColors.primary }}\n              />\n            </LineChart>\n          </ResponsiveContainer>\n        );\n\n      case 'area':\n        return (\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <AreaChart data={data} margin={margins}>\n              {showGrid && <CartesianGrid strokeDasharray=\"3 3\" stroke={themeColors.grid} />}\n              {showXAxis && (\n                <XAxis\n                  dataKey=\"label\"\n                  axisLine={false}\n                  tickLine={false}\n                  tick={{ fill: themeColors.text, fontSize }}\n                  height={xHeight}\n                />\n              )}\n              {showYAxis && (\n                <YAxis\n                  axisLine={false}\n                  tickLine={false}\n                  tick={{ fill: themeColors.text, fontSize }}\n                  width={yWidth}\n                />\n              )}\n              <Tooltip\n                contentStyle={{\n                  backgroundColor: themeColors.tooltipBg,\n                  border: `1px solid ${themeColors.tooltipBorder}`,\n                  borderRadius: '8px',\n                  color: themeColors.tooltipText,\n                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'\n                }}\n              />\n              <Area\n                type=\"monotone\"\n                dataKey=\"value\"\n                stroke={metadata.colors?.[0] || themeColors.primary}\n                fill={metadata.colors?.[0] || themeColors.primary}\n                fillOpacity={0.3}\n                strokeWidth={2}\n              />\n            </AreaChart>\n          </ResponsiveContainer>\n        );\n\n      case 'pie':\n        return (\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <PieChart margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>\n              <Pie\n                data={data}\n                cx=\"50%\"\n                cy=\"50%\"\n                labelLine={false}\n                label={width && width < 250 ? false : ({ label, percent }) => `${label} ${percent ? (percent * 100).toFixed(0) : 0}%`}\n                outerRadius=\"90%\"\n                innerRadius=\"35%\"\n                fill={themeColors.primary}\n                dataKey=\"value\"\n                stroke={themeColors.grid}\n                strokeWidth={1}\n              >\n                {data.map((_, index) => (\n                  <Cell\n                    key={`cell-${index}`}\n                    fill={metadata.colors?.[index] || DEFAULT_CHART_COLORS[index % DEFAULT_CHART_COLORS.length]}\n                  />\n                ))}\n              </Pie>\n              <Tooltip\n                contentStyle={{\n                  backgroundColor: themeColors.tooltipBg,\n                  border: `1px solid ${themeColors.tooltipBorder}`,\n                  borderRadius: '8px',\n                  color: themeColors.tooltipText,\n                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'\n                }}\n              />\n            </PieChart>\n          </ResponsiveContainer>\n        );\n\n      default:\n        return (\n          <div className=\"flex items-center justify-center h-full text-muted-foreground\">\n            <p>Unsupported chart type: {chartType}</p>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className=\"w-full h-full\">\n      {renderChart()}\n    </div>\n  );\n};\n\n// Wrapper component that measures container size for responsive behavior\nconst ResponsiveChartRenderer: React.FC<Omit<ChartRendererProps, 'width' | 'height'>> = (props) => {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const [dimensions, setDimensions] = useState<{ width: number; height: number } | null>(null);\n\n  useEffect(() => {\n    const updateDimensions = () => {\n      if (containerRef.current) {\n        const { width, height } = containerRef.current.getBoundingClientRect();\n        setDimensions({ width, height });\n      }\n    };\n\n    // Initial measurement\n    updateDimensions();\n\n    // Set up ResizeObserver for dynamic updates\n    const resizeObserver = new ResizeObserver(() => {\n      // Use requestAnimationFrame to debounce rapid resize events\n      requestAnimationFrame(updateDimensions);\n    });\n    \n    if (containerRef.current) {\n      resizeObserver.observe(containerRef.current);\n    }\n\n    // Also listen for window resize as a fallback\n    window.addEventListener('resize', updateDimensions);\n\n    return () => {\n      resizeObserver.disconnect();\n      window.removeEventListener('resize', updateDimensions);\n    };\n  }, []);\n\n  // Force re-render when props change to ensure chart updates\n  useEffect(() => {\n    if (containerRef.current) {\n      const updateDimensions = () => {\n        if (containerRef.current) {\n          const { width, height } = containerRef.current.getBoundingClientRect();\n          setDimensions({ width, height });\n        }\n      };\n      // Small delay to ensure DOM has updated\n      setTimeout(updateDimensions, 50);\n    }\n  }, [props.chartData]);\n\n  return (\n    <div ref={containerRef} className={`w-full h-full ${props.className || ''}`}>\n      {dimensions && (\n        <ChartRenderer\n          {...props}\n          width={dimensions.width}\n          height={dimensions.height}\n          className=\"\"\n        />\n      )}\n    </div>\n  );\n};\n\nexport default ResponsiveChartRenderer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AACA;AApBA;;;;;;AA6BA,qDAAqD;AACrD,MAAM,wBAAwB,CAAC,OAAgB;IAC7C,IAAI,CAAC,SAAS,CAAC,QAAQ,OAAO;IAC9B,MAAM,eAAe,KAAK,GAAG,CAAC,OAAO;IACrC,IAAI,eAAe,KAAK,OAAO;IAC/B,IAAI,eAAe,KAAK,OAAO;IAC/B,IAAI,eAAe,KAAK,OAAO;IAC/B,OAAO;AACT;AAEA,oEAAoE;AACpE,MAAM,oBAAoB,CAAC,OAAgB;IACzC,IAAI,CAAC,SAAS,CAAC,QAAQ,OAAO;QAAE,SAAS;QAAI,QAAQ;IAAG;IAExD,+DAA+D;IAC/D,IAAI,SAAS,KAAK,OAAO;QAAE,SAAS;QAAI,QAAQ;IAAG;IACnD,IAAI,SAAS,KAAK,OAAO;QAAE,SAAS;QAAI,QAAQ;IAAG;IACnD,IAAI,SAAS,KAAK,OAAO;QAAE,SAAS;QAAI,QAAQ;IAAG;IACnD,OAAO;QAAE,SAAS;QAAI,QAAQ;IAAG;AACnC;AAEA,oDAAoD;AACpD,MAAM,oBAAoB,CAAC,OAAgB,QAAiB;IAC1D,IAAI,CAAC,SAAS,CAAC,QAAQ,OAAO;QAAE,KAAK;QAAG,OAAO;QAAG,MAAM;QAAG,QAAQ;IAAE;IAErE,+DAA+D;IAC/D,IAAI,cAAc,SAAS,SAAS,KAAK;QACvC,OAAO;YAAE,KAAK;YAAG,OAAO;YAAG,MAAM;YAAG,QAAQ;QAAE;IAChD;IAEA,IAAI,SAAS,KAAK;QAChB,OAAO;YAAE,KAAK;YAAG,OAAO;YAAG,MAAM;YAAG,QAAQ;QAAE;IAChD;IAEA,OAAO;QAAE,KAAK;QAAG,OAAO;QAAG,MAAM;QAAG,QAAQ;IAAE;AAChD;AAEA,MAAM,gBAA8C,CAAC,EACnD,SAAS,EACT,KAAK,EACL,MAAM,EACN,YAAY,EAAE,EACf;IACC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;IACtC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,SAAS,kBAAkB;IAEjC,oBAAoB;IACpB,MAAM,WAAW,sBAAsB,OAAO;IAC9C,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,kBAAkB,OAAO;IACrD,MAAM,UAAU,kBAAkB,OAAO,QAAQ;IAEjD,qBAAqB;IACrB,MAAM,iBAAiB,IAAM,CAAC;YAC5B,MAAM,SAAS,2BAA2B;YAC1C,MAAM,SAAS,yBAAyB;YACxC,WAAW,SAAS,wBAAwB;YAC5C,eAAe,SAAS,2BAA2B;YACnD,aAAa,SAAS,qBAAqB;YAC3C,SAAS,SAAS,2BAA2B;QAC/C,CAAC;IAED,MAAM,cAAc;IAEpB,wDAAwD;IACxD,MAAM,WAAW,SAAS,SAAS,MAAM;IACzC,MAAM,YAAY,SAAS,SAAS,MAAM;IAC1C,MAAM,YAAY,SAAS,SAAS,MAAM;IAE1C,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;wBAAC,MAAM;wBAAM,QAAQ;;4BAC3B,0BAAY,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,QAAQ,YAAY,IAAI;;;;;;4BACzE,2BACC,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,MAAM,YAAY,IAAI;oCAAE;gCAAS;gCACzC,QAAQ;gCACR,UAAU,SAAS,QAAQ,MAAM,qBAAqB;;;;;;4BAGzD,2BACC,8OAAC,qJAAA,CAAA,QAAK;gCACJ,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,MAAM,YAAY,IAAI;oCAAE;gCAAS;gCACzC,OAAO;gCACP,WAAW,UAAU,SAAS,MAAM,IAAI;;;;;;0CAG5C,8OAAC,uJAAA,CAAA,UAAO;gCACN,cAAc;oCACZ,iBAAiB,YAAY,SAAS;oCACtC,QAAQ,CAAC,UAAU,EAAE,YAAY,aAAa,EAAE;oCAChD,cAAc;oCACd,OAAO,YAAY,WAAW;oCAC9B,WAAW;gCACb;;;;;;0CAEF,8OAAC,mJAAA,CAAA,MAAG;gCACF,SAAQ;gCACR,MAAM,SAAS,MAAM,EAAE,CAAC,EAAE,IAAI,YAAY,OAAO;gCACjD,QAAQ,UAAU,SAAS,MAAM;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE,GAAG;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE;;;;;;;;;;;;;;;;;YAMtE,KAAK;gBACH,qBACE,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;wBAAC,MAAM;wBAAM,QAAQ;;4BAC5B,0BAAY,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,QAAQ,YAAY,IAAI;;;;;;4BACzE,2BACC,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,MAAM,YAAY,IAAI;oCAAE;gCAAS;gCACzC,QAAQ;;;;;;4BAGX,2BACC,8OAAC,qJAAA,CAAA,QAAK;gCACJ,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,MAAM,YAAY,IAAI;oCAAE;gCAAS;gCACzC,OAAO;;;;;;0CAGX,8OAAC,uJAAA,CAAA,UAAO;gCACN,cAAc;oCACZ,iBAAiB,YAAY,SAAS;oCACtC,QAAQ,CAAC,UAAU,EAAE,YAAY,aAAa,EAAE;oCAChD,cAAc;oCACd,OAAO,YAAY,WAAW;oCAC9B,WAAW;gCACb;;;;;;0CAEF,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAQ,SAAS,MAAM,EAAE,CAAC,EAAE,IAAI,YAAY,OAAO;gCACnD,aAAa;gCACb,KAAK;oCAAE,MAAM,SAAS,MAAM,EAAE,CAAC,EAAE,IAAI,YAAY,OAAO;oCAAE,aAAa;oCAAG,GAAG;gCAAE;gCAC/E,WAAW;oCAAE,GAAG;oCAAG,MAAM,SAAS,MAAM,EAAE,CAAC,EAAE,IAAI,YAAY,OAAO;gCAAC;;;;;;;;;;;;;;;;;YAM/E,KAAK;gBACH,qBACE,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;wBAAC,MAAM;wBAAM,QAAQ;;4BAC5B,0BAAY,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,QAAQ,YAAY,IAAI;;;;;;4BACzE,2BACC,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,MAAM,YAAY,IAAI;oCAAE;gCAAS;gCACzC,QAAQ;;;;;;4BAGX,2BACC,8OAAC,qJAAA,CAAA,QAAK;gCACJ,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,MAAM,YAAY,IAAI;oCAAE;gCAAS;gCACzC,OAAO;;;;;;0CAGX,8OAAC,uJAAA,CAAA,UAAO;gCACN,cAAc;oCACZ,iBAAiB,YAAY,SAAS;oCACtC,QAAQ,CAAC,UAAU,EAAE,YAAY,aAAa,EAAE;oCAChD,cAAc;oCACd,OAAO,YAAY,WAAW;oCAC9B,WAAW;gCACb;;;;;;0CAEF,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAQ,SAAS,MAAM,EAAE,CAAC,EAAE,IAAI,YAAY,OAAO;gCACnD,MAAM,SAAS,MAAM,EAAE,CAAC,EAAE,IAAI,YAAY,OAAO;gCACjD,aAAa;gCACb,aAAa;;;;;;;;;;;;;;;;;YAMvB,KAAK;gBACH,qBACE,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;wBAAC,QAAQ;4BAAE,KAAK;4BAAG,OAAO;4BAAG,MAAM;4BAAG,QAAQ;wBAAE;;0CACvD,8OAAC,+IAAA,CAAA,MAAG;gCACF,MAAM;gCACN,IAAG;gCACH,IAAG;gCACH,WAAW;gCACX,OAAO,SAAS,QAAQ,MAAM,QAAQ,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,GAAK,GAAG,MAAM,CAAC,EAAE,UAAU,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;gCACrH,aAAY;gCACZ,aAAY;gCACZ,MAAM,YAAY,OAAO;gCACzB,SAAQ;gCACR,QAAQ,YAAY,IAAI;gCACxB,aAAa;0CAEZ,KAAK,GAAG,CAAC,CAAC,GAAG,sBACZ,8OAAC,oJAAA,CAAA,OAAI;wCAEH,MAAM,SAAS,MAAM,EAAE,CAAC,MAAM,IAAI,qHAAA,CAAA,uBAAoB,CAAC,QAAQ,qHAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC;uCADtF,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;0CAK1B,8OAAC,uJAAA,CAAA,UAAO;gCACN,cAAc;oCACZ,iBAAiB,YAAY,SAAS;oCACtC,QAAQ,CAAC,UAAU,EAAE,YAAY,aAAa,EAAE;oCAChD,cAAc;oCACd,OAAO,YAAY,WAAW;oCAC9B,WAAW;gCACb;;;;;;;;;;;;;;;;;YAMV;gBACE,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;4BAAyB;;;;;;;;;;;;QAGpC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;AAEA,yEAAyE;AACzE,MAAM,0BAAkF,CAAC;IACvF,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IAEvF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,aAAa,OAAO,EAAE;gBACxB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,aAAa,OAAO,CAAC,qBAAqB;gBACpE,cAAc;oBAAE;oBAAO;gBAAO;YAChC;QACF;QAEA,sBAAsB;QACtB;QAEA,4CAA4C;QAC5C,MAAM,iBAAiB,IAAI,eAAe;YACxC,4DAA4D;YAC5D,sBAAsB;QACxB;QAEA,IAAI,aAAa,OAAO,EAAE;YACxB,eAAe,OAAO,CAAC,aAAa,OAAO;QAC7C;QAEA,8CAA8C;QAC9C,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,eAAe,UAAU;YACzB,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,4DAA4D;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,OAAO,EAAE;YACxB,MAAM,mBAAmB;gBACvB,IAAI,aAAa,OAAO,EAAE;oBACxB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,aAAa,OAAO,CAAC,qBAAqB;oBACpE,cAAc;wBAAE;wBAAO;oBAAO;gBAChC;YACF;YACA,wCAAwC;YACxC,WAAW,kBAAkB;QAC/B;IACF,GAAG;QAAC,MAAM,SAAS;KAAC;IAEpB,qBACE,8OAAC;QAAI,KAAK;QAAc,WAAW,CAAC,cAAc,EAAE,MAAM,SAAS,IAAI,IAAI;kBACxE,4BACC,8OAAC;YACE,GAAG,KAAK;YACT,OAAO,WAAW,KAAK;YACvB,QAAQ,WAAW,MAAM;YACzB,WAAU;;;;;;;;;;;AAKpB;uCAEe", "debugId": null}}, {"offset": {"line": 3532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/dashboard/ChartWidget.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback, useRef, useEffect } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\r\nimport { Loader2, X, MoreHorizontal, Send, AlertCircle, Sparkles, Database } from 'lucide-react';\r\nimport { ChartWidget as ChartWidgetType } from '@/types';\r\nimport { useApi } from '@/providers/ApiContext';\r\nimport ResponsiveChartRenderer from './ChartRenderer';\r\nimport CreateChartInput from './CreateChartInput';\r\n\r\ninterface ChartWidgetProps {\r\n  widget: ChartWidgetType;\r\n  onDelete: (widgetId: string) => void;\r\n  onUpdate: (widgetId: string, updates: Partial<ChartWidgetType>) => void;\r\n  onOpenConfigSidebar?: (widget: ChartWidgetType) => void;\r\n  className?: string;\r\n}\r\n\r\nconst ChartWidget: React.FC<ChartWidgetProps> = ({\r\n  widget,\r\n  onDelete,\r\n  onUpdate,\r\n  onOpenConfigSidebar,\r\n  className = '',\r\n}) => {\r\n  const { queryChart } = useApi();\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [editPrompt, setEditPrompt] = useState('');\r\n  const [isFocused, setIsFocused] = useState(false);\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n\r\n  const handleCreateChart = useCallback(async (prompt: string) => {\r\n    // Store the prompt and open the config sidebar to select database\r\n    if (onOpenConfigSidebar) {\r\n      // Store the prompt in the widget for later use\r\n      onUpdate(widget.id, {\r\n        pendingPrompt: prompt,\r\n        title: 'Select Database...'\r\n      });\r\n      onOpenConfigSidebar(widget);\r\n    } else {\r\n      // Fallback to mock data if no sidebar available\r\n      onUpdate(widget.id, {\r\n        isLoading: true,\r\n        error: null,\r\n        title: 'Generating...'\r\n      });\r\n\r\n      try {\r\n        const response = await queryChart({ prompt });\r\n\r\n        if (response.success && response.data) {\r\n          onUpdate(widget.id, {\r\n            chartData: response.data,\r\n            isLoading: false,\r\n            error: null,\r\n            title: response.data.title,\r\n          });\r\n        } else {\r\n          throw new Error(response.error || 'Failed to generate chart');\r\n        }\r\n      } catch (error) {\r\n        const errorMessage = error instanceof Error ? error.message : 'Failed to create chart';\r\n        onUpdate(widget.id, {\r\n          isLoading: false,\r\n          error: errorMessage,\r\n          title: 'Generation Failed',\r\n        });\r\n      }\r\n    }\r\n  }, [widget.id, queryChart, onUpdate, onOpenConfigSidebar]);\r\n\r\n  const handleEdit = useCallback(async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!editPrompt.trim()) return;\r\n\r\n    await handleCreateChart(editPrompt.trim());\r\n    setIsEditing(false);\r\n    setEditPrompt('');\r\n  }, [editPrompt, handleCreateChart]);\r\n\r\n  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {\r\n      e.preventDefault();\r\n      handleEdit(e as any);\r\n    }\r\n    if (e.key === 'Escape') {\r\n      setIsEditing(false);\r\n      setEditPrompt('');\r\n    }\r\n  }, [handleEdit]);\r\n\r\n  const handleChartClick = useCallback(() => {\r\n    if (!isEditing && onOpenConfigSidebar) {\r\n      onOpenConfigSidebar(widget);\r\n    }\r\n  }, [isEditing, onOpenConfigSidebar, widget]);\r\n\r\n  const handleConfigureChart = useCallback(async (chartType: string, dataSourceId: string, userPrompt?: string) => {\r\n    console.log('Configuring chart:', { chartType, dataSourceId, userPrompt });\r\n\r\n    // Use the user's prompt or create a default one\r\n    const prompt = userPrompt || `Create a ${chartType} chart showing relevant data from the connected database`;\r\n\r\n    onUpdate(widget.id, {\r\n      isLoading: true,\r\n      error: null,\r\n      title: 'Generating...'\r\n    });\r\n\r\n    try {\r\n      // Pass the database_id to get real data instead of mock data\r\n      const response = await queryChart({\r\n        prompt: prompt,\r\n        database_id: dataSourceId  // This will tell the backend to use real database data\r\n      });\r\n\r\n      if (response.success && response.data) {\r\n        onUpdate(widget.id, {\r\n          chartData: response.data,\r\n          isLoading: false,\r\n          error: null,\r\n          title: response.data.title,\r\n        });\r\n      } else {\r\n        throw new Error(response.error || 'Failed to generate chart');\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create chart';\r\n      onUpdate(widget.id, {\r\n        isLoading: false,\r\n        error: errorMessage,\r\n        title: 'Generation Failed',\r\n      });\r\n    }\r\n  }, [widget.id, queryChart, onUpdate]);\r\n\r\n  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    setEditPrompt(e.target.value);\r\n    \r\n    // Auto-resize textarea\r\n    if (textareaRef.current) {\r\n      textareaRef.current.style.height = 'auto';\r\n      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;\r\n    }\r\n  };\r\n\r\n  // Auto-focus when entering edit mode\r\n  useEffect(() => {\r\n    if (isEditing && textareaRef.current) {\r\n      textareaRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  const isLoading = widget.isLoading;\r\n  const error = widget.error;\r\n  const hasChart = !!widget.chartData;\r\n  const canSubmit = editPrompt.trim() && !isLoading;\r\n\r\n  return (\r\n    <Card \r\n      className={`h-full overflow-hidden chart-card ${className}`}\r\n      style={{\r\n        backgroundColor: 'var(--sidebar-surface-secondary)',\r\n        borderColor: 'var(--sidebar-border)',\r\n      }}\r\n    >\r\n      {/* Header */}\r\n      <CardHeader\r\n        className=\"flex flex-row items-center justify-between p-2 pb-1 react-grid-no-drag\"\r\n        onMouseDown={(e) => e.stopPropagation()}\r\n      >\r\n        <div></div>\r\n        <div className=\"flex items-center gap-1\">\r\n          {hasChart && !isLoading && (\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => setIsEditing(true)}\r\n              className=\"h-6 w-6 p-0\"\r\n            >\r\n              <MoreHorizontal className=\"h-3 w-3\" />\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </CardHeader>\r\n\r\n      {/* Content */}\r\n      <CardContent className=\"flex-1 h-full p-2\">\r\n        {/* Loading State */}\r\n        {isLoading && (\r\n          <div className=\"h-full flex flex-col items-center justify-center space-y-4\">\r\n            <div className=\"text-center space-y-4\">\r\n              <div className=\"text-lg font-medium text-foreground\">Generating</div>\r\n              <Loader2 className=\"h-8 w-8 animate-spin text-primary mx-auto\" />\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Edit Mode */}\r\n        {isEditing && (\r\n          <div \r\n            className=\"h-full flex flex-col items-center justify-center p-6 react-grid-no-drag\" \r\n            onMouseDown={(e) => e.stopPropagation()}\r\n          >\r\n            <div className=\"w-full max-w-md space-y-4\">\r\n              <div className=\"text-center space-y-2\">\r\n                <h3 \r\n                  className=\"text-base font-medium\"\r\n                  style={{ color: 'var(--sidebar-text-primary)' }}\r\n                >\r\n                  Modify your chart\r\n                </h3>\r\n                <p \r\n                  className=\"text-sm\"\r\n                  style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                >\r\n                  Describe changes you want to make\r\n                </p>\r\n              </div>\r\n              \r\n              <form onSubmit={handleEdit} className=\"space-y-3\">\r\n                <div \r\n                  className={`relative rounded-xl border transition-all duration-200 ${\r\n                    isFocused ? 'ring-2 ring-blue-500 ring-opacity-20' : ''\r\n                  }`}\r\n                  style={{\r\n                    backgroundColor: 'var(--sidebar-bg)',\r\n                    borderColor: isFocused ? 'rgba(59, 130, 246, 0.5)' : 'var(--sidebar-border)',\r\n                  }}\r\n                >\r\n                  <Textarea\r\n                    ref={textareaRef}\r\n                    value={editPrompt}\r\n                    onChange={handleTextareaChange}\r\n                    onKeyDown={handleKeyDown}\r\n                    onFocus={() => setIsFocused(true)}\r\n                    onBlur={() => setIsFocused(false)}\r\n                    placeholder=\"Change the chart type to bar chart...\"\r\n                    className=\"min-h-[80px] max-h-[120px] resize-none border-0 bg-transparent text-sm leading-relaxed placeholder:text-gray-500 focus:ring-0 focus:outline-none p-3 pr-12\"\r\n                    style={{ color: 'var(--sidebar-text-primary)' }}\r\n                    maxLength={500}\r\n                  />\r\n                  \r\n                  <div className=\"absolute bottom-2 right-2\">\r\n                    <Button\r\n                      type=\"submit\"\r\n                      disabled={!canSubmit}\r\n                      size=\"sm\"\r\n                      className={`h-7 w-7 p-0 rounded-lg border-0 transition-all duration-200 ${\r\n                        canSubmit ? 'hover:scale-105' : 'opacity-50'\r\n                      }`}\r\n                      style={{\r\n                        backgroundColor: canSubmit ? 'rgba(59, 130, 246, 1)' : 'var(--sidebar-surface-tertiary)',\r\n                        color: canSubmit ? 'white' : 'var(--sidebar-text-tertiary)',\r\n                      }}\r\n                    >\r\n                      <Send className=\"h-3 w-3\" />\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex gap-2\">\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      onClick={() => {\r\n                        setIsEditing(false);\r\n                        setEditPrompt('');\r\n                      }}\r\n                      className=\"h-8 px-3 text-xs rounded-lg transition-all duration-200\"\r\n                      style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}\r\n                    >\r\n                      Cancel\r\n                    </Button>\r\n                  </div>\r\n                  <span \r\n                    className=\"text-xs\"\r\n                    style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                  >\r\n                    ⌘+Enter to send\r\n                  </span>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Chart Display */}\r\n        {hasChart && !isLoading && !error && !isEditing && widget.chartData && (\r\n          <div \r\n            className=\"h-full cursor-pointer\"\r\n            onClick={handleChartClick}\r\n          >\r\n            <ResponsiveChartRenderer\r\n              key={`${widget.id}-${widget.layout.w}-${widget.layout.h}`}\r\n              chartData={widget.chartData}\r\n              className=\"h-full\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Empty State */}\r\n        {!hasChart && !isLoading && !error && !isEditing && (\r\n          <div\r\n            className=\"h-full cursor-pointer flex items-center justify-center\"\r\n            onClick={handleChartClick}\r\n          >\r\n            <div className=\"text-center p-6\">\r\n              <div className=\"mb-4\">\r\n                <Database className=\"h-12 w-12 mx-auto text-gray-400\" />\r\n              </div>\r\n              <h3 className=\"text-sm font-medium text-gray-700 mb-2\">\r\n                Create Chart with Real Data\r\n              </h3>\r\n              <p className=\"text-xs text-gray-500 mb-4\">\r\n                Click to select a database and configure your chart\r\n              </p>\r\n              <Button\r\n                onClick={handleChartClick}\r\n                className=\"text-xs\"\r\n                style={{\r\n                  backgroundColor: 'rgba(59, 130, 246, 1)',\r\n                  color: 'white',\r\n                }}\r\n              >\r\n                Configure Chart\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Error State */}\r\n        {error && !isLoading && !isEditing && (\r\n          <div className=\"h-full flex flex-col items-center justify-center space-y-4\">\r\n            <div className=\"text-center space-y-2\">\r\n              <div className=\"text-destructive font-medium\">Generation Failed</div>\r\n              <p className=\"text-muted-foreground text-sm\">{error}</p>\r\n              <Button\r\n                onClick={() => onUpdate(widget.id, { error: null })}\r\n                variant=\"outline\"\r\n              >\r\n                Try Again\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default ChartWidget;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AATA;;;;;;;;;AAoBA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,mBAAmB,EACnB,YAAY,EAAE,EACf;IACC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD;IAC5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAEhD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC3C,kEAAkE;QAClE,IAAI,qBAAqB;YACvB,+CAA+C;YAC/C,SAAS,OAAO,EAAE,EAAE;gBAClB,eAAe;gBACf,OAAO;YACT;YACA,oBAAoB;QACtB,OAAO;YACL,gDAAgD;YAChD,SAAS,OAAO,EAAE,EAAE;gBAClB,WAAW;gBACX,OAAO;gBACP,OAAO;YACT;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,WAAW;oBAAE;gBAAO;gBAE3C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,SAAS,OAAO,EAAE,EAAE;wBAClB,WAAW,SAAS,IAAI;wBACxB,WAAW;wBACX,OAAO;wBACP,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC5B;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,SAAS,OAAO,EAAE,EAAE;oBAClB,WAAW;oBACX,OAAO;oBACP,OAAO;gBACT;YACF;QACF;IACF,GAAG;QAAC,OAAO,EAAE;QAAE;QAAY;QAAU;KAAoB;IAEzD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,kBAAkB,WAAW,IAAI;QACvC,aAAa;QACb,cAAc;IAChB,GAAG;QAAC;QAAY;KAAkB;IAElC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,GAAG;YACjD,EAAE,cAAc;YAChB,WAAW;QACb;QACA,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB,aAAa;YACb,cAAc;QAChB;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,CAAC,aAAa,qBAAqB;YACrC,oBAAoB;QACtB;IACF,GAAG;QAAC;QAAW;QAAqB;KAAO;IAE3C,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB,cAAsB;QACvF,QAAQ,GAAG,CAAC,sBAAsB;YAAE;YAAW;YAAc;QAAW;QAExE,gDAAgD;QAChD,MAAM,SAAS,cAAc,CAAC,SAAS,EAAE,UAAU,wDAAwD,CAAC;QAE5G,SAAS,OAAO,EAAE,EAAE;YAClB,WAAW;YACX,OAAO;YACP,OAAO;QACT;QAEA,IAAI;YACF,6DAA6D;YAC7D,MAAM,WAAW,MAAM,WAAW;gBAChC,QAAQ;gBACR,aAAa,aAAc,uDAAuD;YACpF;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,SAAS,OAAO,EAAE,EAAE;oBAClB,WAAW,SAAS,IAAI;oBACxB,WAAW;oBACX,OAAO;oBACP,OAAO,SAAS,IAAI,CAAC,KAAK;gBAC5B;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,OAAO,EAAE,EAAE;gBAClB,WAAW;gBACX,OAAO;gBACP,OAAO;YACT;QACF;IACF,GAAG;QAAC,OAAO,EAAE;QAAE;QAAY;KAAS;IAEpC,MAAM,uBAAuB,CAAC;QAC5B,cAAc,EAAE,MAAM,CAAC,KAAK;QAE5B,uBAAuB;QACvB,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACnC,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QAC5E;IACF;IAEA,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,YAAY,OAAO,EAAE;YACpC,YAAY,OAAO,CAAC,KAAK;QAC3B;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,YAAY,OAAO,SAAS;IAClC,MAAM,QAAQ,OAAO,KAAK;IAC1B,MAAM,WAAW,CAAC,CAAC,OAAO,SAAS;IACnC,MAAM,YAAY,WAAW,IAAI,MAAM,CAAC;IAExC,qBACE,8OAAC,gIAAA,CAAA,OAAI;QACH,WAAW,CAAC,kCAAkC,EAAE,WAAW;QAC3D,OAAO;YACL,iBAAiB;YACjB,aAAa;QACf;;0BAGA,8OAAC,gIAAA,CAAA,aAAU;gBACT,WAAU;gBACV,aAAa,CAAC,IAAM,EAAE,eAAe;;kCAErC,8OAAC;;;;;kCACD,8OAAC;wBAAI,WAAU;kCACZ,YAAY,CAAC,2BACZ,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa;4BAC5B,WAAU;sCAEV,cAAA,8OAAC,gNAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAOlC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAsC;;;;;;8CACrD,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMxB,2BACC,8OAAC;wBACC,WAAU;wBACV,aAAa,CAAC,IAAM,EAAE,eAAe;kCAErC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO;4CAA8B;sDAC/C;;;;;;sDAGD,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO;4CAAgC;sDACjD;;;;;;;;;;;;8CAKH,8OAAC;oCAAK,UAAU;oCAAY,WAAU;;sDACpC,8OAAC;4CACC,WAAW,CAAC,uDAAuD,EACjE,YAAY,yCAAyC,IACrD;4CACF,OAAO;gDACL,iBAAiB;gDACjB,aAAa,YAAY,4BAA4B;4CACvD;;8DAEA,8OAAC,oIAAA,CAAA,WAAQ;oDACP,KAAK;oDACL,OAAO;oDACP,UAAU;oDACV,WAAW;oDACX,SAAS,IAAM,aAAa;oDAC5B,QAAQ,IAAM,aAAa;oDAC3B,aAAY;oDACZ,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAA8B;oDAC9C,WAAW;;;;;;8DAGb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,UAAU,CAAC;wDACX,MAAK;wDACL,WAAW,CAAC,4DAA4D,EACtE,YAAY,oBAAoB,cAChC;wDACF,OAAO;4DACL,iBAAiB,YAAY,0BAA0B;4DACvD,OAAO,YAAY,UAAU;wDAC/B;kEAEA,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAKtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,aAAa;4DACb,cAAc;wDAChB;wDACA,WAAU;wDACV,OAAO;4DAAE,OAAO;wDAAgC;wDAChD,cAAc,CAAC;4DACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wDAC1C;wDACA,cAAc,CAAC;4DACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wDAC1C;kEACD;;;;;;;;;;;8DAIH,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAA+B;8DAChD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUV,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,aAAa,OAAO,SAAS,kBACjE,8OAAC;wBACC,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC,4JAAA,CAAA,UAAuB;4BAEtB,WAAW,OAAO,SAAS;4BAC3B,WAAU;2BAFL,GAAG,OAAO,EAAE,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,EAAE;;;;;;;;;;oBAQ9D,CAAC,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,2BACrC,8OAAC;wBACC,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,OAAO;oCACT;8CACD;;;;;;;;;;;;;;;;;oBAQN,SAAS,CAAC,aAAa,CAAC,2BACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA+B;;;;;;8CAC9C,8OAAC;oCAAE,WAAU;8CAAiC;;;;;;8CAC9C,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,SAAS,OAAO,EAAE,EAAE;4CAAE,OAAO;wCAAK;oCACjD,SAAQ;8CACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUf;uCAEe", "debugId": null}}, {"offset": {"line": 4086, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,gbACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/inline-edit.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Check, X, Edit2 } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface InlineEditProps {\n  value: string;\n  onSave: (value: string) => Promise<void> | void;\n  placeholder?: string;\n  multiline?: boolean;\n  className?: string;\n  editClassName?: string;\n  displayClassName?: string;\n  maxLength?: number;\n  disabled?: boolean;\n  showEditIcon?: boolean;\n  hideButtons?: boolean;\n}\n\nconst InlineEdit: React.FC<InlineEditProps> = ({\n  value,\n  onSave,\n  placeholder = \"Click to edit...\",\n  multiline = false,\n  className = '',\n  editClassName = '',\n  displayClassName = '',\n  maxLength,\n  disabled = false,\n  showEditIcon = true,\n  hideButtons = false,\n}) => {\n  const [isEditing, setIsEditing] = useState(false);\n  const [editValue, setEditValue] = useState(value);\n  const [isLoading, setIsLoading] = useState(false);\n  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    setEditValue(value);\n  }, [value]);\n\n  useEffect(() => {\n    if (isEditing && inputRef.current) {\n      inputRef.current.focus();\n      if (inputRef.current instanceof HTMLInputElement || inputRef.current instanceof HTMLTextAreaElement) {\n        inputRef.current.select();\n      }\n    }\n  }, [isEditing]);\n\n  // Handle click outside to save when hideButtons is true\n  useEffect(() => {\n    if (!hideButtons || !isEditing) return;\n\n    const handleClickOutside = (event: MouseEvent) => {\n      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {\n        handleSave();\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [isEditing, hideButtons, editValue, value]);\n\n  const handleEdit = () => {\n    if (disabled) return;\n    setIsEditing(true);\n    setEditValue(value);\n  };\n\n  const handleSave = async () => {\n    if (editValue.trim() === value.trim()) {\n      setIsEditing(false);\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      await onSave(editValue.trim());\n      setIsEditing(false);\n    } catch (error) {\n      console.error('Failed to save:', error);\n      // Reset to original value on error\n      setEditValue(value);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setEditValue(value);\n    setIsEditing(false);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !multiline) {\n      e.preventDefault();\n      handleSave();\n    } else if (e.key === 'Escape') {\n      handleCancel();\n    } else if (e.key === 'Enter' && multiline && (e.ctrlKey || e.metaKey)) {\n      e.preventDefault();\n      handleSave();\n    }\n  };\n\n  if (isEditing) {\n    const InputComponent = multiline ? Textarea : Input;\n    \n    return (\n      <div ref={containerRef} className={cn(\"flex items-start gap-2\", className)}>\n        <div className=\"max-w-md\">\n          <InputComponent\n            ref={inputRef as any}\n            value={editValue}\n            onChange={(e) => setEditValue(e.target.value)}\n            onKeyDown={handleKeyDown}\n            placeholder={placeholder}\n            maxLength={maxLength}\n            disabled={isLoading}\n            className={cn(\"min-w-0\", editClassName)}\n            rows={multiline ? 3 : undefined}\n            style={{ width: `${Math.max(editValue.length + 2, 10)}ch` }}\n          />\n        </div>\n        {!hideButtons && (\n          <div className=\"flex items-center gap-1 mt-1\">\n            <Button\n              size=\"sm\"\n              variant=\"ghost\"\n              onClick={handleSave}\n              disabled={isLoading || !editValue.trim()}\n              className=\"h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50\"\n            >\n              <Check className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              size=\"sm\"\n              variant=\"ghost\"\n              onClick={handleCancel}\n              disabled={isLoading}\n              className=\"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div \n      className={cn(\n        \"group cursor-pointer rounded px-2 py-1 -mx-2 -my-1 hover:bg-muted/50 transition-colors\",\n        disabled && \"cursor-not-allowed opacity-50\",\n        className\n      )}\n      onClick={handleEdit}\n    >\n      <div className=\"flex items-center gap-2\">\n        <span className={cn(\n          \"flex-1\",\n          !value && \"text-muted-foreground italic\",\n          displayClassName\n        )}>\n          {value || placeholder}\n        </span>\n        {showEditIcon && !disabled && (\n          <Edit2 className=\"h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity\" />\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default InlineEdit;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAPA;;;;;;;;AAuBA,MAAM,aAAwC,CAAC,EAC7C,KAAK,EACL,MAAM,EACN,cAAc,kBAAkB,EAChC,YAAY,KAAK,EACjB,YAAY,EAAE,EACd,gBAAgB,EAAE,EAClB,mBAAmB,EAAE,EACrB,SAAS,EACT,WAAW,KAAK,EAChB,eAAe,IAAI,EACnB,cAAc,KAAK,EACpB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0C;IAChE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG;QAAC;KAAM;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,SAAS,OAAO,EAAE;YACjC,SAAS,OAAO,CAAC,KAAK;YACtB,IAAI,SAAS,OAAO,YAAY,oBAAoB,SAAS,OAAO,YAAY,qBAAqB;gBACnG,SAAS,OAAO,CAAC,MAAM;YACzB;QACF;IACF,GAAG;QAAC;KAAU;IAEd,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,WAAW;QAEhC,MAAM,qBAAqB,CAAC;YAC1B,IAAI,aAAa,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAChF;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;QAAW;QAAa;QAAW;KAAM;IAE7C,MAAM,aAAa;QACjB,IAAI,UAAU;QACd,aAAa;QACb,aAAa;IACf;IAEA,MAAM,aAAa;QACjB,IAAI,UAAU,IAAI,OAAO,MAAM,IAAI,IAAI;YACrC,aAAa;YACb;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,OAAO,UAAU,IAAI;YAC3B,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,mCAAmC;YACnC,aAAa;QACf,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,aAAa;QACb,aAAa;IACf;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,WAAW;YACnC,EAAE,cAAc;YAChB;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW,aAAa,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,GAAG;YACrE,EAAE,cAAc;YAChB;QACF;IACF;IAEA,IAAI,WAAW;QACb,MAAM,iBAAiB,YAAY,oIAAA,CAAA,WAAQ,GAAG,iIAAA,CAAA,QAAK;QAEnD,qBACE,8OAAC;YAAI,KAAK;YAAc,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;;8BAC9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,KAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wBAC5C,WAAW;wBACX,aAAa;wBACb,WAAW;wBACX,UAAU;wBACV,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;wBACzB,MAAM,YAAY,IAAI;wBACtB,OAAO;4BAAE,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,MAAM,GAAG,GAAG,IAAI,EAAE,CAAC;wBAAC;;;;;;;;;;;gBAG7D,CAAC,6BACA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,aAAa,CAAC,UAAU,IAAI;4BACtC,WAAU;sCAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMzB;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,0FACA,YAAY,iCACZ;QAEF,SAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAChB,UACA,CAAC,SAAS,gCACV;8BAEC,SAAS;;;;;;gBAEX,gBAAgB,CAAC,0BAChB,8OAAC,kMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK3B;uCAEe", "debugId": null}}, {"offset": {"line": 4333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/dashboard/DragToDeleteZone.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { Trash2 } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { DragToDeleteZoneProps } from '@/types';\n\nconst DragToDeleteZone: React.FC<DragToDeleteZoneProps> = ({\n  isVisible,\n  isHovered,\n}) => {\n  if (!isVisible) return null;\n\n  return (\n    <div\n      className={cn(\n        \"fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50\",\n        \"transition-all duration-300 ease-in-out\",\n        isVisible ? \"opacity-100 scale-100\" : \"opacity-0 scale-95\"\n      )}\n    >\n      <div\n        className={cn(\n          \"flex items-center justify-center\",\n          \"w-20 h-20 rounded-full\",\n          \"border-2 border-dashed\",\n          \"transition-all duration-200 ease-in-out\",\n          \"shadow-lg backdrop-blur-sm\",\n          \"drag-delete-zone\", // Add class for collision detection\n          isHovered && \"hovered\", // Add hovered class for CSS animation\n          isHovered\n            ? \"bg-destructive/20 border-destructive text-destructive scale-110\"\n            : \"bg-muted/80 border-muted-foreground/50 text-muted-foreground hover:bg-muted/90\"\n        )}\n      >\n        <Trash2 \n          className={cn(\n            \"transition-all duration-200\",\n            isHovered ? \"w-8 h-8\" : \"w-6 h-6\"\n          )} \n        />\n      </div>\n      \n      {/* Helper text */}\n      <div className=\"absolute top-full mt-2 left-1/2 transform -translate-x-1/2 whitespace-nowrap\">\n        <div className={cn(\n          \"text-xs font-medium px-2 py-1 rounded\",\n          \"bg-background/90 border shadow-sm\",\n          isHovered \n            ? \"text-destructive border-destructive/20\" \n            : \"text-muted-foreground border-border\"\n        )}>\n          {isHovered ? \"Release to delete\" : \"Drop here to delete\"}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DragToDeleteZone;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAJA;;;;AAOA,MAAM,mBAAoD,CAAC,EACzD,SAAS,EACT,SAAS,EACV;IACC,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,2DACA,2CACA,YAAY,0BAA0B;;0BAGxC,8OAAC;gBACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,oCACA,0BACA,0BACA,2CACA,8BACA,oBACA,aAAa,WACb,YACI,oEACA;0BAGN,cAAA,8OAAC,0MAAA,CAAA,SAAM;oBACL,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACV,+BACA,YAAY,YAAY;;;;;;;;;;;0BAM9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EACf,yCACA,qCACA,YACI,2CACA;8BAEH,YAAY,sBAAsB;;;;;;;;;;;;;;;;;AAK7C;uCAEe", "debugId": null}}, {"offset": {"line": 4392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/dashboard/DashboardView.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo, useState, useCallback } from 'react';\r\nimport { Responsive, WidthProvider } from 'react-grid-layout';\r\nimport { BarChart3 } from 'lucide-react';\r\nimport { Dashboard, ChartWidget as ChartWidgetType, CHART_CONFIG, UpdateDashboardRequest, DragState } from '@/types';\r\nimport ChartWidget from './ChartWidget';\r\nimport InlineEdit from '@/components/ui/inline-edit';\r\nimport DragToDeleteZone from './DragToDeleteZone';\r\n\r\n// Import CSS for react-grid-layout\r\nimport 'react-grid-layout/css/styles.css';\r\nimport 'react-resizable/css/styles.css';\r\n\r\nconst ResponsiveGridLayout = WidthProvider(Responsive);\r\n\r\ninterface DashboardViewProps {\r\n  dashboard: Dashboard;\r\n  widgets: ChartWidgetType[];\r\n  onCreateChart: () => void;\r\n  onDeleteWidget: (widgetId: string) => void;\r\n  onUpdateWidget: (widgetId: string, updates: Partial<ChartWidgetType>) => void;\r\n  onLayoutChange: (layout: any[]) => void;\r\n  onUpdateDashboard: (dashboardId: string, updates: UpdateDashboardRequest) => Promise<void>;\r\n  onOpenConfigSidebar: (widget: ChartWidgetType) => void;\r\n  className?: string;\r\n}\r\n\r\nconst DashboardView: React.FC<DashboardViewProps> = ({\r\n  dashboard,\r\n  widgets,\r\n  onCreateChart,\r\n  onDeleteWidget,\r\n  onUpdateWidget,\r\n  onLayoutChange,\r\n  onUpdateDashboard,\r\n  onOpenConfigSidebar,\r\n  className = '',\r\n}) => {\r\n  // API calls are now handled by parent Dashboard component\r\n  \r\n  // Drag-to-delete state\r\n  const [dragState, setDragState] = useState<DragState>({\r\n    isDragging: false,\r\n    draggedWidgetId: null,\r\n    isOverDeleteZone: false,\r\n  });\r\n\r\n  // Sidebar state is now managed by parent Dashboard component\r\n\r\n  // Generate layout from widgets\r\n  const layouts = useMemo(() => {\r\n    const layout = widgets.map((widget) => ({\r\n      i: widget.id,\r\n      x: widget.layout.x,\r\n      y: widget.layout.y,\r\n      w: widget.layout.w,\r\n      h: widget.layout.h,\r\n      minW: CHART_CONFIG.MIN_WIDGET_SIZE.w,\r\n      minH: CHART_CONFIG.MIN_WIDGET_SIZE.h,\r\n      maxW: CHART_CONFIG.MAX_WIDGET_SIZE.w,\r\n      maxH: CHART_CONFIG.MAX_WIDGET_SIZE.h,\r\n    }));\r\n\r\n    return {\r\n      lg: layout,\r\n      md: layout,\r\n      sm: layout.map(item => ({ \r\n        ...item, \r\n        w: Math.min(item.w, 6),\r\n        maxW: 6 \r\n      })),\r\n      xs: layout.map(item => ({ \r\n        ...item, \r\n        w: Math.min(item.w, 4),\r\n        maxW: 4 \r\n      })),\r\n      xxs: layout.map(item => ({ \r\n        ...item, \r\n        w: 2,\r\n        maxW: 2 \r\n      })),\r\n    };\r\n  }, [widgets]);\r\n\r\n  // Drag event handlers\r\n  const handleDragStart = useCallback((_layout: any[], _oldItem: any, newItem: any, _placeholder: any, _e: MouseEvent, _element: HTMLElement) => {\r\n    console.log('Drag started for widget:', newItem.i); // Debug log\r\n    setDragState({\r\n      isDragging: true,\r\n      draggedWidgetId: newItem.i,\r\n      isOverDeleteZone: false,\r\n    });\r\n  }, []);\r\n\r\n\r\n\r\n  // Mouse position tracking for delete zone collision detection\r\n  const handleMouseMove = useCallback((e: MouseEvent) => {\r\n    if (!dragState.isDragging) return;\r\n\r\n    // Get delete zone element bounds\r\n    const deleteZone = document.querySelector('.drag-delete-zone');\r\n    if (!deleteZone) return;\r\n\r\n    const rect = deleteZone.getBoundingClientRect();\r\n    const isOverZone = (\r\n      e.clientX >= rect.left &&\r\n      e.clientX <= rect.right &&\r\n      e.clientY >= rect.top &&\r\n      e.clientY <= rect.bottom\r\n    );\r\n\r\n    // Update state only if hover state changed\r\n    if (isOverZone !== dragState.isOverDeleteZone) {\r\n      setDragState(prev => ({ ...prev, isOverDeleteZone: isOverZone }));\r\n      console.log('Delete zone hover:', isOverZone); // Debug log\r\n    }\r\n  }, [dragState.isDragging, dragState.isOverDeleteZone]);\r\n\r\n  // Enhanced drag stop handler with delete zone check\r\n  const handleDragStopWithDelete = useCallback((_layout: any[], _oldItem: any, _newItem: any, _placeholder: any, _e: MouseEvent, _element: HTMLElement) => {\r\n    const wasOverDeleteZone = dragState.isOverDeleteZone;\r\n    const widgetId = dragState.draggedWidgetId;\r\n\r\n    console.log('Drag stopped - Over delete zone:', wasOverDeleteZone, 'Widget ID:', widgetId); // Debug log\r\n\r\n    setDragState({\r\n      isDragging: false,\r\n      draggedWidgetId: null,\r\n      isOverDeleteZone: false,\r\n    });\r\n\r\n    // If dropped over delete zone, delete the widget\r\n    if (wasOverDeleteZone && widgetId) {\r\n      console.log('Deleting widget:', widgetId); // Debug log\r\n      onDeleteWidget(widgetId);\r\n      return; // Don't update layout if deleting\r\n    }\r\n  }, [dragState.isOverDeleteZone, dragState.draggedWidgetId, onDeleteWidget]);\r\n\r\n  // Add/remove mouse move listener when dragging\r\n  React.useEffect(() => {\r\n    if (dragState.isDragging) {\r\n      document.addEventListener('mousemove', handleMouseMove);\r\n      return () => document.removeEventListener('mousemove', handleMouseMove);\r\n    }\r\n  }, [dragState.isDragging, handleMouseMove]);\r\n\r\n  const canCreateChart = widgets.length < CHART_CONFIG.MAX_WIDGETS;\r\n\r\n  // Sidebar handlers are now managed by parent Dashboard component\r\n\r\n  return (\r\n    <div className={`space-y-6 ${className}`}>\r\n      {/* Dashboard Info */}\r\n      <div className=\"space-y-3\">\r\n        <div className=\"flex items-center\">\r\n          <InlineEdit\r\n            value={dashboard.name}\r\n            onSave={(newName) => onUpdateDashboard(dashboard.id, { name: newName })}\r\n            placeholder=\"Dashboard name...\"\r\n            displayClassName=\"text-2xl font-bold text-foreground\"\r\n            editClassName=\"text-3xl font-bold\"\r\n            maxLength={100}\r\n            hideButtons={true}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Chart Widgets Grid */}\r\n      {widgets.length > 0 && (\r\n        <div className=\"relative\">\r\n          <ResponsiveGridLayout\r\n            className=\"layout\"\r\n            layouts={layouts}\r\n            breakpoints={CHART_CONFIG.BREAKPOINTS}\r\n            cols={CHART_CONFIG.GRID_COLS}\r\n            rowHeight={CHART_CONFIG.ROW_HEIGHT}\r\n            onLayoutChange={onLayoutChange}\r\n            onDragStart={handleDragStart}\r\n            onDragStop={handleDragStopWithDelete}\r\n            isDraggable={true}\r\n            isResizable={true}\r\n            margin={[4, 4]}\r\n            containerPadding={[0, 0]}\r\n            // Chart sizing constraints:\r\n            // - Minimum: 3×4 grid units (246×356px at default row height)\r\n            // - Maximum: 12×15 grid units (max width × 3× default height)\r\n            // - Users can drag freely and resize within these bounds\r\n          >\r\n            {widgets.map((widget) => (\r\n              <div key={widget.id} className=\"grid-item\">\r\n                <ChartWidget\r\n                  widget={widget}\r\n                  onDelete={onDeleteWidget}\r\n                  onUpdate={onUpdateWidget}\r\n                  onOpenConfigSidebar={onOpenConfigSidebar}\r\n                  className=\"h-full\"\r\n                />\r\n              </div>\r\n            ))}\r\n          </ResponsiveGridLayout>\r\n        </div>\r\n      )}\r\n\r\n      {/* Drag-to-Delete Zone */}\r\n      <DragToDeleteZone\r\n        isVisible={dragState.isDragging}\r\n        isHovered={dragState.isOverDeleteZone}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardView;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;;AAcA,MAAM,uBAAuB,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD,EAAE,gJAAA,CAAA,aAAU;AAcrD,MAAM,gBAA8C,CAAC,EACnD,SAAS,EACT,OAAO,EACP,aAAa,EACb,cAAc,EACd,cAAc,EACd,cAAc,EACd,iBAAiB,EACjB,mBAAmB,EACnB,YAAY,EAAE,EACf;IACC,0DAA0D;IAE1D,uBAAuB;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,YAAY;QACZ,iBAAiB;QACjB,kBAAkB;IACpB;IAEA,6DAA6D;IAE7D,+BAA+B;IAC/B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACtB,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAC,SAAW,CAAC;gBACtC,GAAG,OAAO,EAAE;gBACZ,GAAG,OAAO,MAAM,CAAC,CAAC;gBAClB,GAAG,OAAO,MAAM,CAAC,CAAC;gBAClB,GAAG,OAAO,MAAM,CAAC,CAAC;gBAClB,GAAG,OAAO,MAAM,CAAC,CAAC;gBAClB,MAAM,qHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,CAAC;gBACpC,MAAM,qHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,CAAC;gBACpC,MAAM,qHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,CAAC;gBACpC,MAAM,qHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,CAAC;YACtC,CAAC;QAED,OAAO;YACL,IAAI;YACJ,IAAI;YACJ,IAAI,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;oBACpB,MAAM;gBACR,CAAC;YACD,IAAI,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;oBACpB,MAAM;gBACR,CAAC;YACD,KAAK,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACvB,GAAG,IAAI;oBACP,GAAG;oBACH,MAAM;gBACR,CAAC;QACH;IACF,GAAG;QAAC;KAAQ;IAEZ,sBAAsB;IACtB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAgB,UAAe,SAAc,cAAmB,IAAgB;QACnH,QAAQ,GAAG,CAAC,4BAA4B,QAAQ,CAAC,GAAG,YAAY;QAChE,aAAa;YACX,YAAY;YACZ,iBAAiB,QAAQ,CAAC;YAC1B,kBAAkB;QACpB;IACF,GAAG,EAAE;IAIL,8DAA8D;IAC9D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,CAAC,UAAU,UAAU,EAAE;QAE3B,iCAAiC;QACjC,MAAM,aAAa,SAAS,aAAa,CAAC;QAC1C,IAAI,CAAC,YAAY;QAEjB,MAAM,OAAO,WAAW,qBAAqB;QAC7C,MAAM,aACJ,EAAE,OAAO,IAAI,KAAK,IAAI,IACtB,EAAE,OAAO,IAAI,KAAK,KAAK,IACvB,EAAE,OAAO,IAAI,KAAK,GAAG,IACrB,EAAE,OAAO,IAAI,KAAK,MAAM;QAG1B,2CAA2C;QAC3C,IAAI,eAAe,UAAU,gBAAgB,EAAE;YAC7C,aAAa,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,kBAAkB;gBAAW,CAAC;YAC/D,QAAQ,GAAG,CAAC,sBAAsB,aAAa,YAAY;QAC7D;IACF,GAAG;QAAC,UAAU,UAAU;QAAE,UAAU,gBAAgB;KAAC;IAErD,oDAAoD;IACpD,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAgB,UAAe,UAAe,cAAmB,IAAgB;QAC7H,MAAM,oBAAoB,UAAU,gBAAgB;QACpD,MAAM,WAAW,UAAU,eAAe;QAE1C,QAAQ,GAAG,CAAC,oCAAoC,mBAAmB,cAAc,WAAW,YAAY;QAExG,aAAa;YACX,YAAY;YACZ,iBAAiB;YACjB,kBAAkB;QACpB;QAEA,iDAAiD;QACjD,IAAI,qBAAqB,UAAU;YACjC,QAAQ,GAAG,CAAC,oBAAoB,WAAW,YAAY;YACvD,eAAe;YACf,QAAQ,kCAAkC;QAC5C;IACF,GAAG;QAAC,UAAU,gBAAgB;QAAE,UAAU,eAAe;QAAE;KAAe;IAE1E,+CAA+C;IAC/C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,UAAU,UAAU,EAAE;YACxB,SAAS,gBAAgB,CAAC,aAAa;YACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;QACzD;IACF,GAAG;QAAC,UAAU,UAAU;QAAE;KAAgB;IAE1C,MAAM,iBAAiB,QAAQ,MAAM,GAAG,qHAAA,CAAA,eAAY,CAAC,WAAW;IAEhE,iEAAiE;IAEjE,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0IAAA,CAAA,UAAU;wBACT,OAAO,UAAU,IAAI;wBACrB,QAAQ,CAAC,UAAY,kBAAkB,UAAU,EAAE,EAAE;gCAAE,MAAM;4BAAQ;wBACrE,aAAY;wBACZ,kBAAiB;wBACjB,eAAc;wBACd,WAAW;wBACX,aAAa;;;;;;;;;;;;;;;;YAMlB,QAAQ,MAAM,GAAG,mBAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;oBACT,aAAa,qHAAA,CAAA,eAAY,CAAC,WAAW;oBACrC,MAAM,qHAAA,CAAA,eAAY,CAAC,SAAS;oBAC5B,WAAW,qHAAA,CAAA,eAAY,CAAC,UAAU;oBAClC,gBAAgB;oBAChB,aAAa;oBACb,YAAY;oBACZ,aAAa;oBACb,aAAa;oBACb,QAAQ;wBAAC;wBAAG;qBAAE;oBACd,kBAAkB;wBAAC;wBAAG;qBAAE;8BAMvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;4BAAoB,WAAU;sCAC7B,cAAA,8OAAC,0JAAA,CAAA,UAAW;gCACV,QAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,qBAAqB;gCACrB,WAAU;;;;;;2BANJ,OAAO,EAAE;;;;;;;;;;;;;;;0BAe3B,8OAAC,+JAAA,CAAA,UAAgB;gBACf,WAAW,UAAU,UAAU;gBAC/B,WAAW,UAAU,gBAAgB;;;;;;;;;;;;AAI7C;uCAEe", "debugId": null}}, {"offset": {"line": 4623, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/dashboard/ChartConfigSidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\n\r\nimport { X, CheckCircle, Database, FileText, Cloud, PlusCircle,\r\n         BarChart3, TrendingUp, Clock, AlignLeft, Filter, Hash,\r\n         Image, FileText as FileTextIcon, Type, PieChart, Activity } from 'lucide-react';\r\nimport { ChartData, ChartWidget } from '@/types';\r\nimport { useDataSources } from '@/providers/DataSourcesContext';\r\n\r\ninterface ChartConfigSidebarProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  chartData?: ChartData | null;\r\n  widget?: ChartWidget | null;\r\n  sqlQuery?: string;\r\n  onConfigureChart?: (chartType: string, dataSourceId: string, prompt?: string) => void;\r\n  className?: string;\r\n}\r\n\r\n// Chart type definitions with icons\r\nconst CHART_TYPES = [\r\n  { id: 'table', name: 'Table', icon: AlignLeft },\r\n  { id: 'line', name: 'Line', icon: TrendingUp },\r\n  { id: 'timebar', name: 'Timebar', icon: Clock },\r\n  { id: 'bar', name: 'Horizontal bar', icon: BarChart3 },\r\n  { id: 'funnel', name: 'Funnel', icon: Filter },\r\n  { id: 'number', name: 'Number', icon: Hash },\r\n  { id: 'image', name: 'Image', icon: Image },\r\n  { id: 'detail', name: 'Detail', icon: FileTextIcon },\r\n  { id: 'text', name: 'Text', icon: Type },\r\n  { id: 'pie', name: 'Pie', icon: PieChart },\r\n  { id: 'activity', name: 'Activity', icon: Activity },\r\n];\r\n\r\n// Icon mapping for data source types - only databases now\r\nconst getDataSourceIcon = (type: string) => {\r\n  return Database; // All data sources are databases\r\n};\r\n\r\nconst ChartConfigSidebar: React.FC<ChartConfigSidebarProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  chartData,\r\n  widget,\r\n  sqlQuery,\r\n  onConfigureChart,\r\n  className = '',\r\n}) => {\r\n  const [selectedChartType, setSelectedChartType] = useState<string>('');\r\n  const [selectedDataSource, setSelectedDataSource] = useState<string>('');\r\n  const { connectedDataSources } = useDataSources();\r\n\r\n  // Reset selections when sidebar opens\r\n  useEffect(() => {\r\n    if (isOpen && !chartData) {\r\n      setSelectedChartType('');\r\n      setSelectedDataSource('');\r\n    }\r\n  }, [isOpen, chartData]);\r\n\r\n  const handleApplyConfiguration = () => {\r\n    if (selectedChartType && selectedDataSource && onConfigureChart) {\r\n      // The prompt will come from the chart input field, not from the sidebar\r\n      onConfigureChart(selectedChartType, selectedDataSource);\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  const canApplyConfiguration = selectedChartType && selectedDataSource;\r\n\r\n  // Handle keyboard shortcuts\r\n  useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (event.key === 'Escape') {\r\n        onClose();\r\n      } else if (event.key === 'Enter' && canApplyConfiguration) {\r\n        event.preventDefault();\r\n        handleApplyConfiguration();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleKeyDown);\r\n    return () => document.removeEventListener('keydown', handleKeyDown);\r\n  }, [onClose, canApplyConfiguration, handleApplyConfiguration]);\r\n\r\n  // Component is now always rendered when parent shows it, no need to check isOpen\r\n\r\n  return (\r\n    <div \r\n      className={`w-full h-full flex flex-col ${className}`}\r\n      style={{\r\n        backgroundColor: 'var(--sidebar-surface-secondary)',\r\n        borderLeft: '1px solid var(--sidebar-border)',\r\n      }}\r\n    >\r\n        {/* Header - Fixed with close button only */}\r\n        <div \r\n          className=\"flex-shrink-0 flex items-center justify-end p-3\"\r\n          style={{ \r\n            backgroundColor: 'var(--sidebar-surface-secondary)',\r\n\r\n          }}\r\n        >\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={onClose}\r\n            className=\"h-7 w-7 p-0 rounded-md\"\r\n            style={{ color: 'var(--sidebar-text-secondary)' }}\r\n            onMouseEnter={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'transparent';\r\n            }}\r\n          >\r\n            <X className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n\r\n        {/* Content - Scrollable */}\r\n        <div className=\"flex-1 overflow-y-auto p-4 space-y-6\">\r\n          {/* Chart Data View (when data exists) */}\r\n          {chartData && (\r\n            <div className=\"space-y-4\">\r\n              <div>\r\n                <h3 \r\n                  className=\"text-sm font-medium mb-2\"\r\n                  style={{ color: 'var(--sidebar-text-primary)' }}\r\n                >\r\n                  Chart Type\r\n                </h3>\r\n                <div \r\n                  className=\"p-3 rounded-lg border\"\r\n                  style={{\r\n                    backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                    borderColor: 'var(--sidebar-border)',\r\n                  }}\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    {(() => {\r\n                      const chartType = CHART_TYPES.find(type => type.id === chartData.chartType);\r\n                      const Icon = chartType?.icon || BarChart3;\r\n                      return (\r\n                        <>\r\n                          <Icon className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                          <span \r\n                            className=\"text-sm\"\r\n                            style={{ color: 'var(--sidebar-text-primary)' }}\r\n                          >\r\n                            {chartType?.name || chartData.chartType}\r\n                          </span>\r\n                        </>\r\n                      );\r\n                    })()}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {sqlQuery && (\r\n                <div>\r\n                  <h3 \r\n                    className=\"text-sm font-medium mb-2\"\r\n                    style={{ color: 'var(--sidebar-text-primary)' }}\r\n                  >\r\n                    SQL Query\r\n                  </h3>\r\n                  <div \r\n                    className=\"p-3 rounded-lg border max-h-48 overflow-y-auto\"\r\n                    style={{\r\n                      backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                      borderColor: 'var(--sidebar-border)',\r\n                    }}\r\n                  >\r\n                    <pre \r\n                      className=\"text-xs font-mono whitespace-pre-wrap\"\r\n                      style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                    >\r\n                      {sqlQuery}\r\n                    </pre>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              <div>\r\n                <h3 \r\n                  className=\"text-sm font-medium mb-2\"\r\n                  style={{ color: 'var(--sidebar-text-primary)' }}\r\n                >\r\n                  Chart Title\r\n                </h3>\r\n                <div \r\n                  className=\"p-3 rounded-lg border\"\r\n                  style={{\r\n                    backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                    borderColor: 'var(--sidebar-border)',\r\n                  }}\r\n                >\r\n                  <span \r\n                    className=\"text-sm\"\r\n                    style={{ color: 'var(--sidebar-text-primary)' }}\r\n                  >\r\n                    {chartData.title}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Chart Configuration (when no data exists) */}\r\n          {!chartData && (\r\n            <div className=\"space-y-6\">\r\n              {/* Data Source Selection - moved above chart types */}\r\n              <div>\r\n                <h3\r\n                  className=\"text-sm font-medium mb-3\"\r\n                  style={{ color: 'var(--sidebar-text-primary)' }}\r\n                >\r\n                  Select a database\r\n                </h3>\r\n                <p\r\n                  className=\"text-xs mb-3\"\r\n                  style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                >\r\n                  Choose which connected database to query for real data. Charts will display actual data from your selected database.\r\n                </p>\r\n                {widget?.pendingPrompt && (\r\n                  <div\r\n                    className=\"text-xs mb-3 p-2 rounded\"\r\n                    style={{\r\n                      backgroundColor: 'var(--sidebar-surface-tertiary)',\r\n                      color: 'var(--sidebar-text-primary)',\r\n                      border: '1px solid var(--sidebar-border)'\r\n                    }}\r\n                  >\r\n                    <strong>Your prompt:</strong> \"{widget.pendingPrompt}\"\r\n                  </div>\r\n                )}\r\n                {connectedDataSources.length > 0 ? (\r\n                  <div className=\"space-y-1.5\">\r\n                    {connectedDataSources.map((dataSource) => {\r\n                      const Icon = getDataSourceIcon(dataSource.type);\r\n                      const isSelected = selectedDataSource === dataSource.id;\r\n                      \r\n                      return (\r\n                        <div\r\n                          key={dataSource.id}\r\n                          className={`flex items-center gap-2.5 p-2.5 cursor-pointer transition-all duration-200 ${\r\n                            isSelected ? 'ring-1 ring-blue-400 rounded-md' : ''\r\n                          }`}\r\n                          style={{\r\n                            backgroundColor: isSelected ? 'var(--sidebar-surface-secondary)' : 'var(--sidebar-surface-secondary)',\r\n                          }}\r\n                          onClick={() => setSelectedDataSource(dataSource.id)}\r\n                        >\r\n                          <Icon className=\"h-3.5 w-3.5\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                          <div className=\"flex-1 min-w-0\">\r\n                            <span \r\n                              className=\"text-xs font-medium block\"\r\n                              style={{ color: 'var(--sidebar-text-primary)' }}\r\n                            >\r\n                              {dataSource.name}\r\n                            </span>\r\n                            <span \r\n                              className=\"text-xs block truncate\"\r\n                              style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                            >\r\n                              {dataSource.description}\r\n                            </span>\r\n                          </div>\r\n                          {isSelected && (\r\n                            <CheckCircle className=\"h-3.5 w-3.5 ml-1\" style={{ color: 'rgba(59, 130, 246, 1)' }} />\r\n                          )}\r\n                        </div>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                ) : (\r\n                  <div \r\n                    className=\"p-3 text-center\"\r\n                    style={{\r\n                      backgroundColor: '#303030',\r\n                    }}\r\n                  >\r\n                    <Database className=\"h-6 w-6 mx-auto mb-1.5\" style={{ color: 'var(--sidebar-text-tertiary)' }} />\r\n                    <p \r\n                      className=\"text-xs font-medium\"\r\n                      style={{ color: 'var(--sidebar-text-primary)' }}\r\n                    >\r\n                      No data sources connected\r\n                    </p>\r\n                    <p \r\n                      className=\"text-xs mt-0.5\"\r\n                      style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                    >\r\n                      Connect a data source to create charts\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* User Prompt Input */}\r\n\r\n\r\n              {/* Chart Type Selection - moved below data sources */}\r\n              <div>\r\n                <h3 \r\n                  className=\"text-sm font-medium mb-3\"\r\n                  style={{ color: 'var(--sidebar-text-primary)' }}\r\n                >\r\n                  Select a chart type\r\n                </h3>\r\n                <div className=\"space-y-1.5\">\r\n                  {CHART_TYPES.map((chartType) => {\r\n                    const Icon = chartType.icon;\r\n                    const isSelected = selectedChartType === chartType.id;\r\n                    \r\n                    return (\r\n                      <div\r\n                        key={chartType.id}\r\n                        className={`flex items-center gap-2.5 p-2.5 cursor-pointer transition-all duration-200 ${\r\n                          isSelected ? 'ring-1 ring-blue-400 rounded-md' : ''\r\n                        }`}\r\n                        style={{\r\n                          backgroundColor: isSelected ? 'var(--sidebar-surface-secondary)' : 'var(--sidebar-surface-secondary)',\r\n                        }}\r\n                        onClick={() => setSelectedChartType(chartType.id)}\r\n                      >\r\n                        <Icon className=\"h-3.5 w-3.5\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                        <span \r\n                          className=\"text-xs font-medium\"\r\n                          style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        >\r\n                          {chartType.name}\r\n                        </span>\r\n                        {isSelected && (\r\n                          <CheckCircle className=\"h-3.5 w-3.5 ml-auto\" style={{ color: 'rgba(59, 130, 246, 1)' }} />\r\n                        )}\r\n                      </div>\r\n                    );\r\n                  })}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Generate Chart Button */}\r\n              <div className=\"pt-4\">\r\n                <Button\r\n                  onClick={handleApplyConfiguration}\r\n                  disabled={!canApplyConfiguration}\r\n                  className=\"w-full\"\r\n                  style={{\r\n                    backgroundColor: canApplyConfiguration ? 'rgba(59, 130, 246, 1)' : 'var(--sidebar-surface-secondary)',\r\n                    color: canApplyConfiguration ? 'white' : 'var(--sidebar-text-secondary)',\r\n                    borderColor: 'var(--sidebar-border)',\r\n                  }}\r\n                >\r\n                  Generate Chart with Real Data\r\n                </Button>\r\n                {selectedDataSource && (\r\n                  <p\r\n                    className=\"text-xs mt-2 text-center\"\r\n                    style={{ color: 'var(--sidebar-text-secondary)' }}\r\n                  >\r\n                    Will query: {connectedDataSources.find(ds => ds.id === selectedDataSource)?.name}\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\nexport default ChartConfigSidebar; "], "names": [], "mappings": ";;;;AAEA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AAVA;;;;;;AAsBA,oCAAoC;AACpC,MAAM,cAAc;IAClB;QAAE,IAAI;QAAS,MAAM;QAAS,MAAM,gNAAA,CAAA,YAAS;IAAC;IAC9C;QAAE,IAAI;QAAQ,MAAM;QAAQ,MAAM,kNAAA,CAAA,aAAU;IAAC;IAC7C;QAAE,IAAI;QAAW,MAAM;QAAW,MAAM,oMAAA,CAAA,QAAK;IAAC;IAC9C;QAAE,IAAI;QAAO,MAAM;QAAkB,MAAM,kNAAA,CAAA,YAAS;IAAC;IACrD;QAAE,IAAI;QAAU,MAAM;QAAU,MAAM,sMAAA,CAAA,SAAM;IAAC;IAC7C;QAAE,IAAI;QAAU,MAAM;QAAU,MAAM,kMAAA,CAAA,OAAI;IAAC;IAC3C;QAAE,IAAI;QAAS,MAAM;QAAS,MAAM,oMAAA,CAAA,QAAK;IAAC;IAC1C;QAAE,IAAI;QAAU,MAAM;QAAU,MAAM,8MAAA,CAAA,WAAY;IAAC;IACnD;QAAE,IAAI;QAAQ,MAAM;QAAQ,MAAM,kMAAA,CAAA,OAAI;IAAC;IACvC;QAAE,IAAI;QAAO,MAAM;QAAO,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACzC;QAAE,IAAI;QAAY,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACpD;AAED,0DAA0D;AAC1D,MAAM,oBAAoB,CAAC;IACzB,OAAO,0MAAA,CAAA,WAAQ,EAAE,iCAAiC;AACpD;AAEA,MAAM,qBAAwD,CAAC,EAC7D,MAAM,EACN,OAAO,EACP,SAAS,EACT,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,iBAAc,AAAD;IAE9C,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,CAAC,WAAW;YACxB,qBAAqB;YACrB,sBAAsB;QACxB;IACF,GAAG;QAAC;QAAQ;KAAU;IAEtB,MAAM,2BAA2B;QAC/B,IAAI,qBAAqB,sBAAsB,kBAAkB;YAC/D,wEAAwE;YACxE,iBAAiB,mBAAmB;YACpC;QACF;IACF;IAEA,MAAM,wBAAwB,qBAAqB;IAEnD,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,MAAM,GAAG,KAAK,UAAU;gBAC1B;YACF,OAAO,IAAI,MAAM,GAAG,KAAK,WAAW,uBAAuB;gBACzD,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAS;QAAuB;KAAyB;IAE7D,iFAAiF;IAEjF,qBACE,8OAAC;QACC,WAAW,CAAC,4BAA4B,EAAE,WAAW;QACrD,OAAO;YACL,iBAAiB;YACjB,YAAY;QACd;;0BAGE,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB;gBAEnB;0BAEA,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;oBACV,OAAO;wBAAE,OAAO;oBAAgC;oBAChD,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oBAC1C;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oBAC1C;8BAEA,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAKjB,8OAAC;gBAAI,WAAU;;oBAEZ,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAA8B;kDAC/C;;;;;;kDAGD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,aAAa;wCACf;kDAEA,cAAA,8OAAC;4CAAI,WAAU;sDACZ,CAAC;gDACA,MAAM,YAAY,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,UAAU,SAAS;gDAC1E,MAAM,OAAO,WAAW,QAAQ,kNAAA,CAAA,YAAS;gDACzC,qBACE;;sEACE,8OAAC;4DAAK,WAAU;4DAAU,OAAO;gEAAE,OAAO;4DAAsB;;;;;;sEAChE,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO;4DAA8B;sEAE7C,WAAW,QAAQ,UAAU,SAAS;;;;;;;;4CAI/C,CAAC;;;;;;;;;;;;;;;;;4BAKN,0BACC,8OAAC;;kDACC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAA8B;kDAC/C;;;;;;kDAGD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,aAAa;wCACf;kDAEA,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO;4CAAgC;sDAE/C;;;;;;;;;;;;;;;;;0CAMT,8OAAC;;kDACC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAA8B;kDAC/C;;;;;;kDAGD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,aAAa;wCACf;kDAEA,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO;4CAA8B;sDAE7C,UAAU,KAAK;;;;;;;;;;;;;;;;;;;;;;;oBAQzB,CAAC,2BACA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAA8B;kDAC/C;;;;;;kDAGD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAgC;kDACjD;;;;;;oCAGA,QAAQ,+BACP,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,OAAO;4CACP,QAAQ;wCACV;;0DAEA,8OAAC;0DAAO;;;;;;4CAAqB;4CAAG,OAAO,aAAa;4CAAC;;;;;;;oCAGxD,qBAAqB,MAAM,GAAG,kBAC7B,8OAAC;wCAAI,WAAU;kDACZ,qBAAqB,GAAG,CAAC,CAAC;4CACzB,MAAM,OAAO,kBAAkB,WAAW,IAAI;4CAC9C,MAAM,aAAa,uBAAuB,WAAW,EAAE;4CAEvD,qBACE,8OAAC;gDAEC,WAAW,CAAC,2EAA2E,EACrF,aAAa,oCAAoC,IACjD;gDACF,OAAO;oDACL,iBAAiB,aAAa,qCAAqC;gDACrE;gDACA,SAAS,IAAM,sBAAsB,WAAW,EAAE;;kEAElD,8OAAC;wDAAK,WAAU;wDAAc,OAAO;4DAAE,OAAO;wDAAsB;;;;;;kEACpE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO;gEAA8B;0EAE7C,WAAW,IAAI;;;;;;0EAElB,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO;gEAAgC;0EAE/C,WAAW,WAAW;;;;;;;;;;;;oDAG1B,4BACC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;wDAAmB,OAAO;4DAAE,OAAO;wDAAwB;;;;;;;+CAzB/E,WAAW,EAAE;;;;;wCA6BxB;;;;;6DAGF,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;wCACnB;;0DAEA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAyB,OAAO;oDAAE,OAAO;gDAA+B;;;;;;0DAC5F,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAA8B;0DAC/C;;;;;;0DAGD,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAgC;0DACjD;;;;;;;;;;;;;;;;;;0CAWP,8OAAC;;kDACC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAA8B;kDAC/C;;;;;;kDAGD,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC;4CAChB,MAAM,OAAO,UAAU,IAAI;4CAC3B,MAAM,aAAa,sBAAsB,UAAU,EAAE;4CAErD,qBACE,8OAAC;gDAEC,WAAW,CAAC,2EAA2E,EACrF,aAAa,oCAAoC,IACjD;gDACF,OAAO;oDACL,iBAAiB,aAAa,qCAAqC;gDACrE;gDACA,SAAS,IAAM,qBAAqB,UAAU,EAAE;;kEAEhD,8OAAC;wDAAK,WAAU;wDAAc,OAAO;4DAAE,OAAO;wDAAsB;;;;;;kEACpE,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO;wDAA8B;kEAE7C,UAAU,IAAI;;;;;;oDAEhB,4BACC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;wDAAsB,OAAO;4DAAE,OAAO;wDAAwB;;;;;;;+CAjBlF,UAAU,EAAE;;;;;wCAqBvB;;;;;;;;;;;;0CAKJ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC;wCACX,WAAU;wCACV,OAAO;4CACL,iBAAiB,wBAAwB,0BAA0B;4CACnE,OAAO,wBAAwB,UAAU;4CACzC,aAAa;wCACf;kDACD;;;;;;oCAGA,oCACC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAgC;;4CACjD;4CACc,qBAAqB,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9F;uCAEa", "debugId": null}}, {"offset": {"line": 5268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/common/SidebarLayout.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface SidebarLayoutProps {\r\n  children: React.ReactNode;\r\n  sidebar?: React.ReactNode;\r\n  showSidebar?: boolean;\r\n  sidebarWidth?: string;\r\n  sidebarPosition?: 'left' | 'right';\r\n  className?: string;\r\n}\r\n\r\nconst SidebarLayout: React.FC<SidebarLayoutProps> = ({\r\n  children,\r\n  sidebar,\r\n  showSidebar = false,\r\n  sidebarWidth = '320px',\r\n  sidebarPosition = 'right',\r\n  className,\r\n}) => {\r\n  const gridTemplateColumns = React.useMemo(() => {\r\n    if (!showSidebar) return '1fr';\r\n    \r\n    return sidebarPosition === 'left' \r\n      ? `${sidebarWidth} 1fr`\r\n      : `1fr ${sidebarWidth}`;\r\n  }, [showSidebar, sidebarWidth, sidebarPosition]);\r\n\r\n  return (\r\n    <div \r\n      className={cn(\"h-full grid transition-all duration-300 ease-in-out\", className)}\r\n      style={{ gridTemplateColumns }}\r\n    >\r\n      {/* Conditionally render sidebar first if left positioned */}\r\n      {showSidebar && sidebarPosition === 'left' && (\r\n        <div className=\"bg-background border-r overflow-auto\">\r\n          {sidebar}\r\n        </div>\r\n      )}\r\n      \r\n      {/* Main Content */}\r\n      <div className=\"overflow-auto min-w-0\">\r\n        {children}\r\n      </div>\r\n      \r\n      {/* Conditionally render sidebar last if right positioned */}\r\n      {showSidebar && sidebarPosition === 'right' && (\r\n        <div className=\"bg-background border-l overflow-auto\">\r\n          {sidebar}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SidebarLayout; "], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAWA,MAAM,gBAA8C,CAAC,EACnD,QAAQ,EACR,OAAO,EACP,cAAc,KAAK,EACnB,eAAe,OAAO,EACtB,kBAAkB,OAAO,EACzB,SAAS,EACV;IACC,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,aAAa,OAAO;QAEzB,OAAO,oBAAoB,SACvB,GAAG,aAAa,IAAI,CAAC,GACrB,CAAC,IAAI,EAAE,cAAc;IAC3B,GAAG;QAAC;QAAa;QAAc;KAAgB;IAE/C,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,yHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACrE,OAAO;YAAE;QAAoB;;YAG5B,eAAe,oBAAoB,wBAClC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAKL,8OAAC;gBAAI,WAAU;0BACZ;;;;;;YAIF,eAAe,oBAAoB,yBAClC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 5331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/dashboard/Dashboard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback, useEffect, useMemo } from 'react';\r\nimport {\r\n  Dashboard as DashboardType,\r\n  ChartWidget as ChartWidgetType,\r\n  CHART_CONFIG,\r\n  DashboardNavigationState,\r\n  CreateDashboardRequest,\r\n  UpdateDashboardRequest\r\n} from '@/types';\r\nimport { useApi } from '@/providers/ApiContext';\r\nimport { usePageTitle } from '@/hooks/usePageTitle';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\nimport { LayoutDashboard } from 'lucide-react';\r\nimport DashboardList from './DashboardList';\r\nimport DashboardView from './DashboardView';\r\nimport ChartConfigSidebar from './ChartConfigSidebar';\r\nimport SidebarLayout from '@/components/common/SidebarLayout';\r\n\r\nconst Dashboard: React.FC = () => {\r\n  // Navigation state\r\n  const [navigationState, setNavigationState] = useState<DashboardNavigationState>({\r\n    currentView: 'list',\r\n    selectedDashboard: null,\r\n    breadcrumbs: [{ label: 'Dashboards' }],\r\n  });\r\n\r\n  // Dashboard data\r\n  const [dashboards, setDashboards] = useState<DashboardType[]>([]);\r\n  const [dashboardStats, setDashboardStats] = useState<Record<string, number>>({});\r\n  const [isLoadingDashboards, setIsLoadingDashboards] = useState(false);\r\n\r\n  // Current dashboard widgets\r\n  const [widgets, setWidgets] = useState<ChartWidgetType[]>([]);\r\n  const [nextWidgetId, setNextWidgetId] = useState(1);\r\n\r\n  // Dashboard creation state\r\n  const [isCreatingDashboard, setIsCreatingDashboard] = useState(false);\r\n\r\n  // Chart configuration sidebar state\r\n  const [selectedWidget, setSelectedWidget] = useState<ChartWidgetType | null>(null);\r\n  const [showConfigSidebar, setShowConfigSidebar] = useState(false);\r\n\r\n  // API and utilities\r\n  const {\r\n    listDashboards,\r\n    createDashboard,\r\n    updateDashboard,\r\n    deleteDashboard,\r\n    queryChart\r\n  } = useApi();\r\n  const { setPageActions } = usePageHeader();\r\n  // const { toast } = useToast();\r\n\r\n  // Load dashboards on component mount\r\n  useEffect(() => {\r\n    loadDashboards();\r\n  }, []);\r\n\r\n  const loadDashboards = useCallback(async () => {\r\n    setIsLoadingDashboards(true);\r\n    try {\r\n      const response = await listDashboards();\r\n      if (response.success) {\r\n        setDashboards(response.data);\r\n        // Mock dashboard stats - in real app, this would come from API\r\n        const stats: Record<string, number> = {};\r\n        response.data.forEach((dashboard: DashboardType) => {\r\n          stats[dashboard.id] = Math.floor(Math.random() * 8); // Random chart count for demo\r\n        });\r\n        setDashboardStats(stats);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load dashboards:', error);\r\n      // toast({\r\n      //   title: \"Error\",\r\n      //   description: \"Failed to load dashboards. Please try again.\",\r\n      //   variant: \"destructive\",\r\n      // });\r\n    } finally {\r\n      setIsLoadingDashboards(false);\r\n    }\r\n  }, [listDashboards]);\r\n\r\n  // Navigation handlers\r\n  const handleSelectDashboard = useCallback((dashboard: DashboardType) => {\r\n    setNavigationState({\r\n      currentView: 'dashboard',\r\n      selectedDashboard: dashboard,\r\n      breadcrumbs: [\r\n        { label: 'Dashboards', onClick: () => handleNavigateBack() },\r\n        { label: dashboard.name },\r\n      ],\r\n    });\r\n    // Load dashboard widgets - for now, start with empty\r\n    setWidgets([]);\r\n    setNextWidgetId(1);\r\n  }, []);\r\n\r\n  const handleNavigateBack = useCallback(() => {\r\n    setNavigationState({\r\n      currentView: 'list',\r\n      selectedDashboard: null,\r\n      breadcrumbs: [{ label: 'Dashboards' }],\r\n    });\r\n    setWidgets([]);\r\n  }, []);\r\n\r\n  // Set page title dynamically based on navigation state - memoized to prevent re-renders\r\n  const pageConfig = useMemo(() => ({\r\n    title: navigationState.currentView === 'dashboard' && navigationState.selectedDashboard \r\n      ? navigationState.selectedDashboard.name\r\n      : 'Dashboard',\r\n    icon: LayoutDashboard,\r\n    breadcrumbs: navigationState.currentView === 'dashboard' && navigationState.selectedDashboard\r\n      ? [\r\n          { label: 'Dashboard', onClick: () => handleNavigateBack() },\r\n          { label: navigationState.selectedDashboard.name },\r\n        ]\r\n      : [{ label: 'Dashboard' }],\r\n  }), [navigationState.currentView, navigationState.selectedDashboard, handleNavigateBack]);\r\n\r\n  usePageTitle(pageConfig);\r\n\r\n  // Dashboard CRUD handlers\r\n  const handleCreateDashboard = useCallback(async () => {\r\n    setIsCreatingDashboard(true);\r\n    try {\r\n      // Create dashboard with default name\r\n      const defaultName = `Dashboard ${dashboards.length + 1}`;\r\n      const response = await createDashboard({\r\n        name: defaultName,\r\n        description: '',\r\n      });\r\n\r\n      if (response.success) {\r\n        const newDashboard = response.data;\r\n        setDashboards(prev => [...prev, newDashboard]);\r\n        setDashboardStats(prev => ({ ...prev, [newDashboard.id]: 0 }));\r\n\r\n        // Immediately navigate to the new dashboard\r\n        handleSelectDashboard(newDashboard);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to create dashboard:', error);\r\n    } finally {\r\n      setIsCreatingDashboard(false);\r\n    }\r\n  }, [dashboards.length, createDashboard, handleSelectDashboard]);\r\n\r\n  const handleUpdateDashboard = useCallback(async (dashboardId: string, updates: UpdateDashboardRequest) => {\r\n    try {\r\n      const response = await updateDashboard(dashboardId, updates);\r\n      if (response.success) {\r\n        setDashboards(prev => prev.map(d =>\r\n          d.id === dashboardId ? response.data : d\r\n        ));\r\n\r\n        // Update the selected dashboard if it's the one being edited\r\n        if (navigationState.selectedDashboard?.id === dashboardId) {\r\n          setNavigationState(prev => ({\r\n            ...prev,\r\n            selectedDashboard: response.data,\r\n            breadcrumbs: [\r\n              { label: 'Dashboards', onClick: () => handleNavigateBack() },\r\n              { label: response.data.name },\r\n            ],\r\n          }));\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to update dashboard:', error);\r\n    }\r\n  }, [updateDashboard, navigationState.selectedDashboard, handleNavigateBack]);\r\n\r\n  const handleDeleteDashboard = useCallback(async (dashboardId: string) => {\r\n    if (!confirm('Are you sure you want to delete this dashboard? This action cannot be undone.')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await deleteDashboard(dashboardId);\r\n      if (response.success) {\r\n        setDashboards(prev => prev.filter(d => d.id !== dashboardId));\r\n        setDashboardStats(prev => {\r\n          const newStats = { ...prev };\r\n          delete newStats[dashboardId];\r\n          return newStats;\r\n        });\r\n\r\n        // If we're currently viewing the deleted dashboard, navigate back\r\n        if (navigationState.selectedDashboard?.id === dashboardId) {\r\n          handleNavigateBack();\r\n        }\r\n\r\n        // toast({\r\n        //   title: \"Success\",\r\n        //   description: \"Dashboard deleted successfully.\",\r\n        // });\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete dashboard:', error);\r\n      // toast({\r\n      //   title: \"Error\",\r\n      //   description: \"Failed to delete dashboard. Please try again.\",\r\n      //   variant: \"destructive\",\r\n      // });\r\n    }\r\n  }, [deleteDashboard, navigationState.selectedDashboard, handleNavigateBack]);\r\n\r\n  // Chart widget management\r\n  const handleCreateChart = useCallback(() => {\r\n    if (widgets.length >= CHART_CONFIG.MAX_WIDGETS) {\r\n      // toast({\r\n      //   title: \"Limit Reached\",\r\n      //   description: `Maximum of ${CHART_CONFIG.MAX_WIDGETS} charts allowed per dashboard.`,\r\n      //   variant: \"destructive\",\r\n      // });\r\n      return;\r\n    }\r\n\r\n    // Find next available position\r\n    let x = 0;\r\n    let y = 0;\r\n\r\n    // Simple positioning logic - place widgets in rows\r\n    const widgetsPerRow = Math.floor(12 / CHART_CONFIG.DEFAULT_WIDGET_SIZE.w);\r\n    const currentRow = Math.floor(widgets.length / widgetsPerRow);\r\n    const currentCol = widgets.length % widgetsPerRow;\r\n\r\n    x = currentCol * CHART_CONFIG.DEFAULT_WIDGET_SIZE.w;\r\n    y = currentRow * CHART_CONFIG.DEFAULT_WIDGET_SIZE.h;\r\n\r\n    const newWidget: ChartWidgetType = {\r\n      id: `widget-${nextWidgetId}`,\r\n      title: '',\r\n      chartData: null,\r\n      isLoading: false,\r\n      error: null,\r\n      dashboard_id: navigationState.selectedDashboard?.id,\r\n      layout: {\r\n        x,\r\n        y,\r\n        w: CHART_CONFIG.DEFAULT_WIDGET_SIZE.w,\r\n        h: CHART_CONFIG.DEFAULT_WIDGET_SIZE.h,\r\n      },\r\n    };\r\n\r\n    setWidgets(prev => [...prev, newWidget]);\r\n    setNextWidgetId(prev => prev + 1);\r\n  }, [widgets, nextWidgetId, navigationState.selectedDashboard]);\r\n\r\n  const handleDeleteWidget = useCallback((widgetId: string) => {\r\n    setWidgets(prev => prev.filter(w => w.id !== widgetId));\r\n  }, []);\r\n\r\n  const handleUpdateWidget = useCallback((widgetId: string, updates: Partial<ChartWidgetType>) => {\r\n    setWidgets(prev => prev.map(w =>\r\n      w.id === widgetId ? { ...w, ...updates } : w\r\n    ));\r\n  }, []);\r\n\r\n  // Handle opening the config sidebar\r\n  const handleOpenConfigSidebar = useCallback((widget: ChartWidgetType) => {\r\n    setSelectedWidget(widget);\r\n    setShowConfigSidebar(true);\r\n  }, []);\r\n\r\n  // Handle closing the config sidebar\r\n  const handleCloseConfigSidebar = useCallback(() => {\r\n    setShowConfigSidebar(false);\r\n    setSelectedWidget(null);\r\n  }, []);\r\n\r\n  // Handle chart configuration\r\n  const handleConfigureChart = useCallback(async (chartType: string, dataSourceId: string, userPrompt?: string) => {\r\n    if (!selectedWidget) return;\r\n\r\n    console.log('Configuring chart:', { chartType, dataSourceId, userPrompt });\r\n\r\n    // Use the pending prompt from the widget, or the provided prompt, or create a default one\r\n    const prompt = selectedWidget.pendingPrompt || userPrompt || `Create a ${chartType} chart showing relevant data from the connected database`;\r\n\r\n    // Update the widget to loading state and clear pending prompt\r\n    handleUpdateWidget(selectedWidget.id, {\r\n      isLoading: true,\r\n      error: null,\r\n      title: 'Generating...',\r\n      pendingPrompt: undefined // Clear the pending prompt\r\n    });\r\n\r\n    try {\r\n      // Pass the database_id to get real data instead of mock data\r\n      const response = await queryChart({\r\n        prompt: prompt,\r\n        database_id: dataSourceId  // This will tell the backend to use real database data\r\n      });\r\n\r\n      if (response.success && response.data) {\r\n        handleUpdateWidget(selectedWidget.id, {\r\n          chartData: response.data,\r\n          isLoading: false,\r\n          error: null,\r\n          title: response.data.title,\r\n        });\r\n      } else {\r\n        throw new Error(response.error || 'Failed to generate chart');\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create chart';\r\n      handleUpdateWidget(selectedWidget.id, {\r\n        isLoading: false,\r\n        error: errorMessage,\r\n        title: 'Generation Failed',\r\n      });\r\n    }\r\n  }, [selectedWidget, queryChart, handleUpdateWidget]);\r\n\r\n  // Chart widget layout management with size constraints\r\n  // Charts can be dragged anywhere on the grid and resized within bounds:\r\n  // - Minimum size: 3x4 grid units (CHART_CONFIG.MIN_WIDGET_SIZE)\r\n  // - Maximum size: 12x15 grid units (CHART_CONFIG.MAX_WIDGET_SIZE) \r\n  // - Maximum height constraint: 3x the default height (15 units = 3 × 5)\r\n  // - Full width allowed up to grid boundary (12 units max)\r\n  const handleLayoutChange = useCallback((layout: any[]) => {\r\n    // Update widget positions based on grid layout changes with size constraints\r\n    setWidgets(prev => prev.map(widget => {\r\n      const layoutItem = layout.find(item => item.i === widget.id);\r\n      if (layoutItem) {\r\n        // Enforce size constraints\r\n        const constrainedWidth = Math.max(\r\n          CHART_CONFIG.MIN_WIDGET_SIZE.w,\r\n          Math.min(layoutItem.w, CHART_CONFIG.MAX_WIDGET_SIZE.w)\r\n        );\r\n        const constrainedHeight = Math.max(\r\n          CHART_CONFIG.MIN_WIDGET_SIZE.h,\r\n          Math.min(layoutItem.h, CHART_CONFIG.MAX_WIDGET_SIZE.h)\r\n        );\r\n\r\n        return {\r\n          ...widget,\r\n          layout: {\r\n            ...widget.layout,\r\n            x: layoutItem.x,\r\n            y: layoutItem.y,\r\n            w: constrainedWidth,\r\n            h: constrainedHeight,\r\n          },\r\n        };\r\n      }\r\n      return widget;\r\n    }));\r\n  }, []);\r\n\r\n  // Update page actions when navigation state or widgets change\r\n  useEffect(() => {\r\n    const isListView = navigationState.currentView === 'list';\r\n    const isDashboardView = navigationState.currentView === 'dashboard' && navigationState.selectedDashboard;\r\n    \r\n    setPageActions({\r\n      // Show New Dashboard button only on list view\r\n      onCreateDashboard: isListView ? handleCreateDashboard : undefined,\r\n      \r\n      // Show Create Chart button only on dashboard view\r\n      onCreateChart: isDashboardView ? handleCreateChart : undefined,\r\n      chartCount: widgets.length,\r\n      maxCharts: CHART_CONFIG.MAX_WIDGETS,\r\n    });\r\n  }, [navigationState.currentView, navigationState.selectedDashboard, widgets.length, setPageActions, handleCreateDashboard, handleCreateChart]);\r\n\r\n  return (\r\n    <SidebarLayout\r\n      showSidebar={showConfigSidebar}\r\n      sidebarPosition=\"right\"\r\n      sidebarWidth=\"320px\"\r\n      sidebar={\r\n        <ChartConfigSidebar\r\n          isOpen={showConfigSidebar}\r\n          onClose={handleCloseConfigSidebar}\r\n          chartData={selectedWidget?.chartData}\r\n          widget={selectedWidget}\r\n          sqlQuery={selectedWidget?.chartData?.metadata?.sqlQuery}\r\n          onConfigureChart={handleConfigureChart}\r\n        />\r\n      }\r\n    >\r\n      <div className=\"container mx-auto p-6 space-y-6\">\r\n        {navigationState.currentView === 'list' ? (\r\n          <DashboardList\r\n            dashboards={dashboards}\r\n            dashboardStats={dashboardStats}\r\n            onSelectDashboard={handleSelectDashboard}\r\n            onDeleteDashboard={handleDeleteDashboard}\r\n          />\r\n        ) : navigationState.selectedDashboard ? (\r\n          <DashboardView\r\n            dashboard={navigationState.selectedDashboard}\r\n            widgets={widgets}\r\n            onCreateChart={handleCreateChart}\r\n            onDeleteWidget={handleDeleteWidget}\r\n            onUpdateWidget={handleUpdateWidget}\r\n            onLayoutChange={handleLayoutChange}\r\n            onUpdateDashboard={handleUpdateDashboard}\r\n            onOpenConfigSidebar={handleOpenConfigSidebar}\r\n          />\r\n        ) : null}\r\n      </div>\r\n    </SidebarLayout>\r\n  );\r\n};\r\n\r\nexport default Dashboard;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;;AAoBA,MAAM,YAAsB;IAC1B,mBAAmB;IACnB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;QAC/E,aAAa;QACb,mBAAmB;QACnB,aAAa;YAAC;gBAAE,OAAO;YAAa;SAAE;IACxC;IAEA,iBAAiB;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,4BAA4B;IAC5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,2BAA2B;IAC3B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,oCAAoC;IACpC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC7E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,oBAAoB;IACpB,MAAM,EACJ,cAAc,EACd,eAAe,EACf,eAAe,EACf,eAAe,EACf,UAAU,EACX,GAAG,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD;IACT,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IACvC,gCAAgC;IAEhC,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,uBAAuB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM;YACvB,IAAI,SAAS,OAAO,EAAE;gBACpB,cAAc,SAAS,IAAI;gBAC3B,+DAA+D;gBAC/D,MAAM,QAAgC,CAAC;gBACvC,SAAS,IAAI,CAAC,OAAO,CAAC,CAAC;oBACrB,KAAK,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,8BAA8B;gBACrF;gBACA,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,UAAU;QACV,oBAAoB;QACpB,iEAAiE;QACjE,4BAA4B;QAC5B,MAAM;QACR,SAAU;YACR,uBAAuB;QACzB;IACF,GAAG;QAAC;KAAe;IAEnB,sBAAsB;IACtB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,mBAAmB;YACjB,aAAa;YACb,mBAAmB;YACnB,aAAa;gBACX;oBAAE,OAAO;oBAAc,SAAS,IAAM;gBAAqB;gBAC3D;oBAAE,OAAO,UAAU,IAAI;gBAAC;aACzB;QACH;QACA,qDAAqD;QACrD,WAAW,EAAE;QACb,gBAAgB;IAClB,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,mBAAmB;YACjB,aAAa;YACb,mBAAmB;YACnB,aAAa;gBAAC;oBAAE,OAAO;gBAAa;aAAE;QACxC;QACA,WAAW,EAAE;IACf,GAAG,EAAE;IAEL,wFAAwF;IACxF,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAChC,OAAO,gBAAgB,WAAW,KAAK,eAAe,gBAAgB,iBAAiB,GACnF,gBAAgB,iBAAiB,CAAC,IAAI,GACtC;YACJ,MAAM,4NAAA,CAAA,kBAAe;YACrB,aAAa,gBAAgB,WAAW,KAAK,eAAe,gBAAgB,iBAAiB,GACzF;gBACE;oBAAE,OAAO;oBAAa,SAAS,IAAM;gBAAqB;gBAC1D;oBAAE,OAAO,gBAAgB,iBAAiB,CAAC,IAAI;gBAAC;aACjD,GACD;gBAAC;oBAAE,OAAO;gBAAY;aAAE;QAC9B,CAAC,GAAG;QAAC,gBAAgB,WAAW;QAAE,gBAAgB,iBAAiB;QAAE;KAAmB;IAExF,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;IAEb,0BAA0B;IAC1B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,uBAAuB;QACvB,IAAI;YACF,qCAAqC;YACrC,MAAM,cAAc,CAAC,UAAU,EAAE,WAAW,MAAM,GAAG,GAAG;YACxD,MAAM,WAAW,MAAM,gBAAgB;gBACrC,MAAM;gBACN,aAAa;YACf;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,eAAe,SAAS,IAAI;gBAClC,cAAc,CAAA,OAAQ;2BAAI;wBAAM;qBAAa;gBAC7C,kBAAkB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,aAAa,EAAE,CAAC,EAAE;oBAAE,CAAC;gBAE5D,4CAA4C;gBAC5C,sBAAsB;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,uBAAuB;QACzB;IACF,GAAG;QAAC,WAAW,MAAM;QAAE;QAAiB;KAAsB;IAE9D,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,aAAqB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,gBAAgB,aAAa;YACpD,IAAI,SAAS,OAAO,EAAE;gBACpB,cAAc,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC7B,EAAE,EAAE,KAAK,cAAc,SAAS,IAAI,GAAG;gBAGzC,6DAA6D;gBAC7D,IAAI,gBAAgB,iBAAiB,EAAE,OAAO,aAAa;oBACzD,mBAAmB,CAAA,OAAQ,CAAC;4BAC1B,GAAG,IAAI;4BACP,mBAAmB,SAAS,IAAI;4BAChC,aAAa;gCACX;oCAAE,OAAO;oCAAc,SAAS,IAAM;gCAAqB;gCAC3D;oCAAE,OAAO,SAAS,IAAI,CAAC,IAAI;gCAAC;6BAC7B;wBACH,CAAC;gBACH;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF,GAAG;QAAC;QAAiB,gBAAgB,iBAAiB;QAAE;KAAmB;IAE3E,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC/C,IAAI,CAAC,QAAQ,kFAAkF;YAC7F;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,gBAAgB;YACvC,IAAI,SAAS,OAAO,EAAE;gBACpB,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAChD,kBAAkB,CAAA;oBAChB,MAAM,WAAW;wBAAE,GAAG,IAAI;oBAAC;oBAC3B,OAAO,QAAQ,CAAC,YAAY;oBAC5B,OAAO;gBACT;gBAEA,kEAAkE;gBAClE,IAAI,gBAAgB,iBAAiB,EAAE,OAAO,aAAa;oBACzD;gBACF;YAEA,UAAU;YACV,sBAAsB;YACtB,oDAAoD;YACpD,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,UAAU;QACV,oBAAoB;QACpB,kEAAkE;QAClE,4BAA4B;QAC5B,MAAM;QACR;IACF,GAAG;QAAC;QAAiB,gBAAgB,iBAAiB;QAAE;KAAmB;IAE3E,0BAA0B;IAC1B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI,QAAQ,MAAM,IAAI,qHAAA,CAAA,eAAY,CAAC,WAAW,EAAE;YAC9C,UAAU;YACV,4BAA4B;YAC5B,yFAAyF;YACzF,4BAA4B;YAC5B,MAAM;YACN;QACF;QAEA,+BAA+B;QAC/B,IAAI,IAAI;QACR,IAAI,IAAI;QAER,mDAAmD;QACnD,MAAM,gBAAgB,KAAK,KAAK,CAAC,KAAK,qHAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,CAAC;QACxE,MAAM,aAAa,KAAK,KAAK,CAAC,QAAQ,MAAM,GAAG;QAC/C,MAAM,aAAa,QAAQ,MAAM,GAAG;QAEpC,IAAI,aAAa,qHAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,CAAC;QACnD,IAAI,aAAa,qHAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,CAAC;QAEnD,MAAM,YAA6B;YACjC,IAAI,CAAC,OAAO,EAAE,cAAc;YAC5B,OAAO;YACP,WAAW;YACX,WAAW;YACX,OAAO;YACP,cAAc,gBAAgB,iBAAiB,EAAE;YACjD,QAAQ;gBACN;gBACA;gBACA,GAAG,qHAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,CAAC;gBACrC,GAAG,qHAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,CAAC;YACvC;QACF;QAEA,WAAW,CAAA,OAAQ;mBAAI;gBAAM;aAAU;QACvC,gBAAgB,CAAA,OAAQ,OAAO;IACjC,GAAG;QAAC;QAAS;QAAc,gBAAgB,iBAAiB;KAAC;IAE7D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,WAAW,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC/C,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAkB;QACxD,WAAW,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC1B,EAAE,EAAE,KAAK,WAAW;oBAAE,GAAG,CAAC;oBAAE,GAAG,OAAO;gBAAC,IAAI;IAE/C,GAAG,EAAE;IAEL,oCAAoC;IACpC,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3C,kBAAkB;QAClB,qBAAqB;IACvB,GAAG,EAAE;IAEL,oCAAoC;IACpC,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,qBAAqB;QACrB,kBAAkB;IACpB,GAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB,cAAsB;QACvF,IAAI,CAAC,gBAAgB;QAErB,QAAQ,GAAG,CAAC,sBAAsB;YAAE;YAAW;YAAc;QAAW;QAExE,0FAA0F;QAC1F,MAAM,SAAS,eAAe,aAAa,IAAI,cAAc,CAAC,SAAS,EAAE,UAAU,wDAAwD,CAAC;QAE5I,8DAA8D;QAC9D,mBAAmB,eAAe,EAAE,EAAE;YACpC,WAAW;YACX,OAAO;YACP,OAAO;YACP,eAAe,UAAU,2BAA2B;QACtD;QAEA,IAAI;YACF,6DAA6D;YAC7D,MAAM,WAAW,MAAM,WAAW;gBAChC,QAAQ;gBACR,aAAa,aAAc,uDAAuD;YACpF;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,mBAAmB,eAAe,EAAE,EAAE;oBACpC,WAAW,SAAS,IAAI;oBACxB,WAAW;oBACX,OAAO;oBACP,OAAO,SAAS,IAAI,CAAC,KAAK;gBAC5B;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,mBAAmB,eAAe,EAAE,EAAE;gBACpC,WAAW;gBACX,OAAO;gBACP,OAAO;YACT;QACF;IACF,GAAG;QAAC;QAAgB;QAAY;KAAmB;IAEnD,uDAAuD;IACvD,wEAAwE;IACxE,gEAAgE;IAChE,mEAAmE;IACnE,wEAAwE;IACxE,0DAA0D;IAC1D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,6EAA6E;QAC7E,WAAW,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBAC1B,MAAM,aAAa,OAAO,IAAI,CAAC,CAAA,OAAQ,KAAK,CAAC,KAAK,OAAO,EAAE;gBAC3D,IAAI,YAAY;oBACd,2BAA2B;oBAC3B,MAAM,mBAAmB,KAAK,GAAG,CAC/B,qHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,CAAC,EAC9B,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,qHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,CAAC;oBAEvD,MAAM,oBAAoB,KAAK,GAAG,CAChC,qHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,CAAC,EAC9B,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,qHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,CAAC;oBAGvD,OAAO;wBACL,GAAG,MAAM;wBACT,QAAQ;4BACN,GAAG,OAAO,MAAM;4BAChB,GAAG,WAAW,CAAC;4BACf,GAAG,WAAW,CAAC;4BACf,GAAG;4BACH,GAAG;wBACL;oBACF;gBACF;gBACA,OAAO;YACT;IACF,GAAG,EAAE;IAEL,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,gBAAgB,WAAW,KAAK;QACnD,MAAM,kBAAkB,gBAAgB,WAAW,KAAK,eAAe,gBAAgB,iBAAiB;QAExG,eAAe;YACb,8CAA8C;YAC9C,mBAAmB,aAAa,wBAAwB;YAExD,kDAAkD;YAClD,eAAe,kBAAkB,oBAAoB;YACrD,YAAY,QAAQ,MAAM;YAC1B,WAAW,qHAAA,CAAA,eAAY,CAAC,WAAW;QACrC;IACF,GAAG;QAAC,gBAAgB,WAAW;QAAE,gBAAgB,iBAAiB;QAAE,QAAQ,MAAM;QAAE;QAAgB;QAAuB;KAAkB;IAE7I,qBACE,8OAAC,6IAAA,CAAA,UAAa;QACZ,aAAa;QACb,iBAAgB;QAChB,cAAa;QACb,uBACE,8OAAC,iKAAA,CAAA,UAAkB;YACjB,QAAQ;YACR,SAAS;YACT,WAAW,gBAAgB;YAC3B,QAAQ;YACR,UAAU,gBAAgB,WAAW,UAAU;YAC/C,kBAAkB;;;;;;kBAItB,cAAA,8OAAC;YAAI,WAAU;sBACZ,gBAAgB,WAAW,KAAK,uBAC/B,8OAAC,4JAAA,CAAA,UAAa;gBACZ,YAAY;gBACZ,gBAAgB;gBAChB,mBAAmB;gBACnB,mBAAmB;;;;;uBAEnB,gBAAgB,iBAAiB,iBACnC,8OAAC,4JAAA,CAAA,UAAa;gBACZ,WAAW,gBAAgB,iBAAiB;gBAC5C,SAAS;gBACT,eAAe;gBACf,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,mBAAmB;gBACnB,qBAAqB;;;;;uBAErB;;;;;;;;;;;AAIZ;uCAEe", "debugId": null}}, {"offset": {"line": 5784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/onboarding.ts"], "sourcesContent": ["// Onboarding utility functions\r\n\r\nimport { ROUTES } from '@/lib/constants';\r\n\r\n/**\r\n * Determines the appropriate redirect path based on user authentication and onboarding status\r\n */\r\nexport function getRedirectPath(\r\n  isAuthenticated: boolean,\r\n  isNewUser: boolean,\r\n  currentPath: string\r\n): string | null {\r\n  // Not authenticated - redirect to login (except for public routes)\r\n  if (!isAuthenticated) {\r\n    const publicRoutes = [\r\n      ROUTES.HOME,\r\n      ROUTES.LOGIN,\r\n      ROUTES.REGISTER,\r\n      ROUTES.AUTH.CALLBACK,\r\n      ROUTES.OAUTH.CALLBACK,\r\n      ROUTES.ONBOARDING,\r\n    ];\r\n\r\n    if (!publicRoutes.includes(currentPath as any)) {\r\n      return ROUTES.LOGIN;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // Authenticated user scenarios\r\n  if (isAuthenticated) {\r\n    // New user not on onboarding page - redirect to onboarding\r\n    if (isNewUser && currentPath !== ROUTES.ONBOARDING) {\r\n      const publicRoutes = [\r\n        ROUTES.HOME,\r\n        ROUTES.LOGIN,\r\n        ROUTES.REGISTER,\r\n        ROUTES.AUTH.CALLBACK,\r\n        ROUTES.OAUTH.CALLBACK,\r\n      ];\r\n\r\n      // Don't redirect if on public routes (except login)\r\n      if (!publicRoutes.includes(currentPath as any) || currentPath === ROUTES.LOGIN) {\r\n        return ROUTES.ONBOARDING;\r\n      }\r\n    }\r\n    \r\n    // Existing user on onboarding page - redirect to dashboard\r\n    if (!isNewUser && currentPath === ROUTES.ONBOARDING) {\r\n      return ROUTES.DASHBOARD;\r\n    }\r\n    \r\n    // Authenticated user on login or register page - redirect based on onboarding status\r\n    if (currentPath === ROUTES.LOGIN || currentPath === ROUTES.REGISTER) {\r\n      return isNewUser ? ROUTES.ONBOARDING : ROUTES.DASHBOARD;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Checks if a route requires authentication\r\n */\r\nexport function isProtectedRoute(path: string): boolean {\r\n  const publicRoutes = [\r\n    ROUTES.HOME,\r\n    ROUTES.LOGIN,\r\n    ROUTES.REGISTER,\r\n    ROUTES.AUTH.CALLBACK,\r\n    ROUTES.OAUTH.CALLBACK,\r\n    ROUTES.ONBOARDING,\r\n  ];\r\n\r\n  return !publicRoutes.includes(path as any);\r\n}\r\n\r\n/**\r\n * Checks if a route is accessible to new users\r\n */\r\nexport function isNewUserAccessibleRoute(path: string): boolean {\r\n  const newUserRoutes = [\r\n    ROUTES.HOME,\r\n    ROUTES.LOGIN,\r\n    ROUTES.REGISTER,\r\n    ROUTES.AUTH.CALLBACK,\r\n    ROUTES.OAUTH.CALLBACK,\r\n    ROUTES.ONBOARDING,\r\n  ];\r\n\r\n  return newUserRoutes.includes(path as any);\r\n}\r\n\r\n/**\r\n * Gets the next step in the onboarding flow after completion\r\n */\r\nexport function getPostOnboardingRedirect(): string {\r\n  return ROUTES.DASHBOARD;\r\n}\r\n\r\n/**\r\n * Validates if onboarding completion is allowed from the current state\r\n */\r\nexport function canCompleteOnboarding(\r\n  isAuthenticated: boolean,\r\n  isNewUser: boolean,\r\n  currentPath: string\r\n): boolean {\r\n  return isAuthenticated && isNewUser && currentPath === ROUTES.ONBOARDING;\r\n}\r\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;AAE/B;AAAA;;AAKO,SAAS,gBACd,eAAwB,EACxB,SAAkB,EAClB,WAAmB;IAEnB,mEAAmE;IACnE,IAAI,CAAC,iBAAiB;QACpB,MAAM,eAAe;YACnB,iIAAA,CAAA,SAAM,CAAC,IAAI;YACX,iIAAA,CAAA,SAAM,CAAC,KAAK;YACZ,iIAAA,CAAA,SAAM,CAAC,QAAQ;YACf,iIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;YACpB,iIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YACrB,iIAAA,CAAA,SAAM,CAAC,UAAU;SAClB;QAED,IAAI,CAAC,aAAa,QAAQ,CAAC,cAAqB;YAC9C,OAAO,iIAAA,CAAA,SAAM,CAAC,KAAK;QACrB;QACA,OAAO;IACT;IAEA,+BAA+B;IAC/B,IAAI,iBAAiB;QACnB,2DAA2D;QAC3D,IAAI,aAAa,gBAAgB,iIAAA,CAAA,SAAM,CAAC,UAAU,EAAE;YAClD,MAAM,eAAe;gBACnB,iIAAA,CAAA,SAAM,CAAC,IAAI;gBACX,iIAAA,CAAA,SAAM,CAAC,KAAK;gBACZ,iIAAA,CAAA,SAAM,CAAC,QAAQ;gBACf,iIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;gBACpB,iIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;aACtB;YAED,oDAAoD;YACpD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAuB,gBAAgB,iIAAA,CAAA,SAAM,CAAC,KAAK,EAAE;gBAC9E,OAAO,iIAAA,CAAA,SAAM,CAAC,UAAU;YAC1B;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAAC,aAAa,gBAAgB,iIAAA,CAAA,SAAM,CAAC,UAAU,EAAE;YACnD,OAAO,iIAAA,CAAA,SAAM,CAAC,SAAS;QACzB;QAEA,qFAAqF;QACrF,IAAI,gBAAgB,iIAAA,CAAA,SAAM,CAAC,KAAK,IAAI,gBAAgB,iIAAA,CAAA,SAAM,CAAC,QAAQ,EAAE;YACnE,OAAO,YAAY,iIAAA,CAAA,SAAM,CAAC,UAAU,GAAG,iIAAA,CAAA,SAAM,CAAC,SAAS;QACzD;IACF;IAEA,OAAO;AACT;AAKO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,eAAe;QACnB,iIAAA,CAAA,SAAM,CAAC,IAAI;QACX,iIAAA,CAAA,SAAM,CAAC,KAAK;QACZ,iIAAA,CAAA,SAAM,CAAC,QAAQ;QACf,iIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;QACpB,iIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QACrB,iIAAA,CAAA,SAAM,CAAC,UAAU;KAClB;IAED,OAAO,CAAC,aAAa,QAAQ,CAAC;AAChC;AAKO,SAAS,yBAAyB,IAAY;IACnD,MAAM,gBAAgB;QACpB,iIAAA,CAAA,SAAM,CAAC,IAAI;QACX,iIAAA,CAAA,SAAM,CAAC,KAAK;QACZ,iIAAA,CAAA,SAAM,CAAC,QAAQ;QACf,iIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;QACpB,iIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QACrB,iIAAA,CAAA,SAAM,CAAC,UAAU;KAClB;IAED,OAAO,cAAc,QAAQ,CAAC;AAChC;AAKO,SAAS;IACd,OAAO,iIAAA,CAAA,SAAM,CAAC,SAAS;AACzB;AAKO,SAAS,sBACd,eAAwB,EACxB,SAAkB,EAClB,WAAmB;IAEnB,OAAO,mBAAmB,aAAa,gBAAgB,iIAAA,CAAA,SAAM,CAAC,UAAU;AAC1E", "debugId": null}}, {"offset": {"line": 5872, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useAuth } from '@/providers/AuthContext';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { isProtectedRoute } from '@/lib/utils/onboarding';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requireAuth?: boolean;\n  redirectTo?: string;\n}\n\nexport default function ProtectedRoute({ \n  children, \n  requireAuth = true, \n  redirectTo = '/login' \n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading, isNewUser, signIn } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isCheckingAuth, setIsCheckingAuth] = useState(true);\n  const [hasTriedAutoAuth, setHasTriedAutoAuth] = useState(false);\n\n  useEffect(() => {\n    const checkAuthentication = async () => {\n      // Skip auth check for public routes\n      if (!requireAuth || !isProtectedRoute(pathname)) {\n        setIsCheckingAuth(false);\n        return;\n      }\n\n      // If already authenticated, handle onboarding redirect\n      if (isAuthenticated) {\n        if (isNewUser && pathname !== '/onboarding') {\n          console.log('New user on protected route, redirecting to onboarding');\n          router.push('/onboarding');\n          return;\n        }\n        if (!isNewUser && pathname === '/onboarding') {\n          console.log('Existing user on onboarding, redirecting to dashboard');\n          router.push('/dashboard');\n          return;\n        }\n        setIsCheckingAuth(false);\n        return;\n      }\n\n      // If not authenticated and haven't tried auto-auth yet, try it once\n      if (!isAuthenticated && !hasTriedAutoAuth && !isLoading) {\n        setHasTriedAutoAuth(true);\n        console.log('Attempting automatic authentication from stored tokens...');\n\n        try {\n          await signIn(undefined, false); // Don't redirect, just authenticate\n          // If successful, the useEffect will re-run due to isAuthenticated change\n        } catch (error) {\n          console.log('Auto-authentication failed, redirecting to login');\n          router.push(redirectTo);\n        }\n        return;\n      }\n\n      // If not authenticated and already tried auto-auth, redirect to login\n      if (!isAuthenticated && hasTriedAutoAuth && !isLoading) {\n        console.log('Not authenticated, redirecting to login');\n        router.push(redirectTo);\n        return;\n      }\n\n      setIsCheckingAuth(false);\n    };\n\n    checkAuthentication();\n  }, [isAuthenticated, isLoading, isNewUser, pathname, requireAuth, redirectTo, router, signIn, hasTriedAutoAuth]);\n\n  // Show loading state while checking authentication\n  if (isCheckingAuth || isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">Checking authentication...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // For protected routes, only render children if authenticated\n  if (requireAuth && !isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold mb-4\">Authentication Required</h2>\n          <p className=\"text-muted-foreground mb-4\">Please sign in to access this page.</p>\n          <button\n            onClick={() => router.push(redirectTo)}\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n          >\n            Sign In\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return <>{children}</>;\n}\n\n// Higher-order component for easy wrapping\nexport function withProtectedRoute<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: Omit<ProtectedRouteProps, 'children'>\n) {\n  return function ProtectedComponent(props: P) {\n    return (\n      <ProtectedRoute {...options}>\n        <Component {...props} />\n      </ProtectedRoute>\n    );\n  };\n}\n\n// Hook for manual authentication checks\nexport function useRequireAuth(redirectTo: string = '/login') {\n  const { isAuthenticated, isLoading, signIn } = useAuth();\n  const router = useRouter();\n  const [isChecking, setIsChecking] = useState(true);\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (isLoading) return;\n\n      if (!isAuthenticated) {\n        try {\n          await signIn(undefined, false); // Don't redirect, just authenticate\n        } catch {\n          router.push(redirectTo);\n        }\n      }\n      setIsChecking(false);\n    };\n\n    checkAuth();\n  }, [isAuthenticated, isLoading, signIn, router, redirectTo]);\n\n  return { isAuthenticated, isLoading: isLoading || isChecking };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAae,SAAS,eAAe,EACrC,QAAQ,EACR,cAAc,IAAI,EAClB,aAAa,QAAQ,EACD;IACpB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD;IAChE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,oCAAoC;YACpC,IAAI,CAAC,eAAe,CAAC,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;gBAC/C,kBAAkB;gBAClB;YACF;YAEA,uDAAuD;YACvD,IAAI,iBAAiB;gBACnB,IAAI,aAAa,aAAa,eAAe;oBAC3C,QAAQ,GAAG,CAAC;oBACZ,OAAO,IAAI,CAAC;oBACZ;gBACF;gBACA,IAAI,CAAC,aAAa,aAAa,eAAe;oBAC5C,QAAQ,GAAG,CAAC;oBACZ,OAAO,IAAI,CAAC;oBACZ;gBACF;gBACA,kBAAkB;gBAClB;YACF;YAEA,oEAAoE;YACpE,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,WAAW;gBACvD,oBAAoB;gBACpB,QAAQ,GAAG,CAAC;gBAEZ,IAAI;oBACF,MAAM,OAAO,WAAW,QAAQ,oCAAoC;gBACpE,yEAAyE;gBAC3E,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CAAC;oBACZ,OAAO,IAAI,CAAC;gBACd;gBACA;YACF;YAEA,sEAAsE;YACtE,IAAI,CAAC,mBAAmB,oBAAoB,CAAC,WAAW;gBACtD,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,kBAAkB;QACpB;QAEA;IACF,GAAG;QAAC;QAAiB;QAAW;QAAW;QAAU;QAAa;QAAY;QAAQ;QAAQ;KAAiB;IAE/G,mDAAmD;IACnD,IAAI,kBAAkB,WAAW;QAC/B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,8DAA8D;IAC9D,IAAI,eAAe,CAAC,iBAAiB;QACnC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBAAO;kBAAG;;AACZ;AAGO,SAAS,mBACd,SAAiC,EACjC,OAA+C;IAE/C,OAAO,SAAS,mBAAmB,KAAQ;QACzC,qBACE,8OAAC;YAAgB,GAAG,OAAO;sBACzB,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,aAAqB,QAAQ;IAC1D,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD;IACrD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI,WAAW;YAEf,IAAI,CAAC,iBAAiB;gBACpB,IAAI;oBACF,MAAM,OAAO,WAAW,QAAQ,oCAAoC;gBACtE,EAAE,OAAM;oBACN,OAAO,IAAI,CAAC;gBACd;YACF;YACA,cAAc;QAChB;QAEA;IACF,GAAG;QAAC;QAAiB;QAAW;QAAQ;QAAQ;KAAW;IAE3D,OAAO;QAAE;QAAiB,WAAW,aAAa;IAAW;AAC/D", "debugId": null}}, {"offset": {"line": 6084, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/app/%28dashboard%29/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport Layout from '@/components/layout/Layout';\r\nimport Dashboard from '@/components/features/dashboard/Dashboard';\r\nimport ProtectedRoute from '@/components/auth/ProtectedRoute';\r\n\r\nexport default function DashboardPage() {\r\n  return (\r\n    <ProtectedRoute>\r\n      <Layout>\r\n        <Dashboard />\r\n      </Layout>\r\n    </ProtectedRoute>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,8OAAC,4IAAA,CAAA,UAAc;kBACb,cAAA,8OAAC,sIAAA,CAAA,UAAM;sBACL,cAAA,8OAAC,wJAAA,CAAA,UAAS;;;;;;;;;;;;;;;AAIlB", "debugId": null}}]}