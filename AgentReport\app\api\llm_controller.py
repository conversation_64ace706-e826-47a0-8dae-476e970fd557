"""
LLM Management API
==================
API endpoints for managing LLM model assignments and configurations.
This serves as the control center for switching models for different purposes.
"""

from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, status, Depends

from app.config.llm_config import (
    ModelPurpose, 
    LLMModelRegistry, 
    get_model_for_purpose,
    switch_model_for_purpose
)
from app.utils.security import get_current_user

router = APIRouter(prefix="/api/llm", tags=["llm-management"])

# ────────────────────────────────────────────────────────────────
# Auth helper (only admin users should manage models)
# ────────────────────────────────────────────────────────────────
async def get_current_user_id(current_user=Depends(get_current_user)) -> str:
    return current_user.id

# ────────────────────────────────────────────────────────────────
# Model Information Endpoints
# ────────────────────────────────────────────────────────────────

@router.get("/models", response_model=Dict[str, Any])
async def list_available_models(user_id: str = Depends(get_current_user_id)):
    """List all available models with their configurations."""
    models = LLMModelRegistry.list_available_models()
    
    return {
        "available_models": {
            key: {
                "model_id": config.model_id,
                "display_name": config.display_name,
                "description": config.description,
                "cost_tier": config.cost_tier,
                "speed_tier": config.speed_tier,
                "quality_tier": config.quality_tier,
                "max_tokens": config.max_tokens
            }
            for key, config in models.items()
        },
        "total_models": len(models)
    }

@router.get("/purposes", response_model=Dict[str, Any])
async def list_model_purposes(user_id: str = Depends(get_current_user_id)):
    """List all available model purposes/use cases."""
    purposes = [
        {
            "purpose": purpose.value,
            "description": {
                ModelPurpose.GENERAL: "General purpose tasks and conversations",
                ModelPurpose.ANALYSIS: "Data analysis and planning tasks",
                ModelPurpose.CODE_GENERATION: "Python code generation and programming",
                ModelPurpose.CODE_EXECUTION: "Code execution and debugging assistance",
                ModelPurpose.DASHBOARD: "Dashboard and visualization creation",
                ModelPurpose.CONVERSATION: "Chat and conversational interactions",
                ModelPurpose.ORCHESTRATION: "Agent orchestration and workflow planning",
                ModelPurpose.FILE_INGESTION: "File processing and metadata extraction"
            }.get(purpose, "Unknown purpose")
        }
        for purpose in ModelPurpose
    ]
    
    return {
        "available_purposes": purposes,
        "total_purposes": len(purposes)
    }

@router.get("/assignments", response_model=Dict[str, Any])
async def get_current_assignments(user_id: str = Depends(get_current_user_id)):
    """Get current model assignments for all purposes."""
    assignments = LLMModelRegistry.get_purpose_assignments()
    
    # Get detailed info for each assignment
    detailed_assignments = {}
    for purpose, model_key in assignments.items():
        model_config = LLMModelRegistry.get_model_by_key(model_key)
        detailed_assignments[purpose.value] = {
            "model_key": model_key,
            "model_id": model_config.model_id,
            "display_name": model_config.display_name,
            "description": model_config.description,
            "cost_tier": model_config.cost_tier,
            "speed_tier": model_config.speed_tier,
            "quality_tier": model_config.quality_tier
        }
    
    return {
        "current_assignments": detailed_assignments,
        "last_updated": "runtime"  # In a real system, you'd track this
    }

# ────────────────────────────────────────────────────────────────
# Model Assignment Management
# ────────────────────────────────────────────────────────────────

@router.post("/assign", response_model=Dict[str, Any])
async def assign_model_to_purpose(
    purpose: str,
    model_key: str,
    user_id: str = Depends(get_current_user_id)
):
    """Assign a specific model to a purpose."""
    try:
        # Validate purpose
        try:
            purpose_enum = ModelPurpose(purpose)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid purpose: {purpose}. Available purposes: {[p.value for p in ModelPurpose]}"
            )
        
        # Validate model exists
        available_models = LLMModelRegistry.list_available_models()
        if model_key not in available_models:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid model_key: {model_key}. Available models: {list(available_models.keys())}"
            )
        
        # Make the assignment
        switch_model_for_purpose(purpose_enum, model_key)
        
        # Get the updated assignment info
        model_config = LLMModelRegistry.get_model_by_key(model_key)
        
        return {
            "success": True,
            "message": f"Successfully assigned {model_config.display_name} to {purpose}",
            "assignment": {
                "purpose": purpose,
                "model_key": model_key,
                "model_display_name": model_config.display_name,
                "previous_assignment": "unknown"  # Could track this in a real system
            }
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to assign model: {str(e)}"
        )

@router.post("/bulk-assign", response_model=Dict[str, Any])
async def bulk_assign_models(
    assignments: Dict[str, str],  # purpose -> model_key mapping
    user_id: str = Depends(get_current_user_id)
):
    """Assign multiple models to multiple purposes in one operation."""
    try:
        results = []
        errors = []
        
        for purpose_str, model_key in assignments.items():
            try:
                # Validate purpose
                purpose_enum = ModelPurpose(purpose_str)
                
                # Validate model
                LLMModelRegistry.get_model_by_key(model_key)  # Will raise if invalid
                
                # Make assignment
                switch_model_for_purpose(purpose_enum, model_key)
                
                results.append({
                    "purpose": purpose_str,
                    "model_key": model_key,
                    "status": "success"
                })
                
            except Exception as e:
                errors.append({
                    "purpose": purpose_str,
                    "model_key": model_key,
                    "error": str(e)
                })
        
        return {
            "success": len(errors) == 0,
            "total_assignments": len(assignments),
            "successful_assignments": len(results),
            "failed_assignments": len(errors),
            "results": results,
            "errors": errors
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Bulk assignment failed: {str(e)}"
        )

# ────────────────────────────────────────────────────────────────
# Model Testing and Information
# ────────────────────────────────────────────────────────────────

@router.get("/model/{model_key}/info", response_model=Dict[str, Any])
async def get_model_info(
    model_key: str,
    user_id: str = Depends(get_current_user_id)
):
    """Get detailed information about a specific model."""
    try:
        model_config = LLMModelRegistry.get_model_by_key(model_key)
        
        # Find which purposes currently use this model
        assignments = LLMModelRegistry.get_purpose_assignments()
        used_by_purposes = [
            purpose.value for purpose, assigned_model in assignments.items() 
            if assigned_model == model_key
        ]
        
        return {
            "model_key": model_key,
            "model_id": model_config.model_id,
            "display_name": model_config.display_name,
            "description": model_config.description,
            "max_tokens": model_config.max_tokens,
            "temperature": model_config.temperature,
            "top_p": model_config.top_p,
            "cost_tier": model_config.cost_tier,
            "speed_tier": model_config.speed_tier,
            "quality_tier": model_config.quality_tier,
            "currently_used_by": used_by_purposes,
            "usage_count": len(used_by_purposes)
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )

@router.get("/dashboard", response_model=Dict[str, Any])
async def get_llm_dashboard(user_id: str = Depends(get_current_user_id)):
    """Get a comprehensive dashboard view of LLM configuration."""
    models = LLMModelRegistry.list_available_models()
    assignments = LLMModelRegistry.get_purpose_assignments()
    
    # Calculate usage statistics
    model_usage = {}
    for model_key in models.keys():
        usage_count = sum(1 for assigned_model in assignments.values() if assigned_model == model_key)
        model_usage[model_key] = usage_count
    
    # Group by cost/quality tiers
    cost_distribution = {"low": 0, "medium": 0, "high": 0}
    quality_distribution = {"basic": 0, "good": 0, "excellent": 0}
    
    for model_config in models.values():
        cost_distribution[model_config.cost_tier] = cost_distribution.get(model_config.cost_tier, 0) + 1
        quality_distribution[model_config.quality_tier] = quality_distribution.get(model_config.quality_tier, 0) + 1
    
    return {
        "summary": {
            "total_models": len(models),
            "total_purposes": len(ModelPurpose),
            "assigned_purposes": len([p for p in assignments.values() if p]),
            "most_used_model": max(model_usage.items(), key=lambda x: x[1]) if model_usage else None
        },
        "model_usage": model_usage,
        "cost_distribution": cost_distribution,
        "quality_distribution": quality_distribution,
        "current_assignments": {
            purpose.value: {
                "model_key": model_key,
                "display_name": models[model_key].display_name
            }
            for purpose, model_key in assignments.items()
        }
    } 
