"use client";

import React, { useEffect } from 'react';
import { useAuth } from '@/providers/AuthContext';
import { useRouter } from 'next/navigation';
import OnboardingFlow from '@/components/features/onboarding/OnboardingFlow';
import OnboardingErrorBoundary from '@/components/features/onboarding/OnboardingErrorBoundary';

export default function OnboardingPage() {
  const { isAuthenticated, isLoading, isNewUser, isCompletingOnboarding } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isCompletingOnboarding) {
      if (!isAuthenticated) {
        // Not authenticated - redirect to login
        console.log('🔄 Onboarding page: Not authenticated, redirecting to login');
        router.push('/login');
      } else if (!isNewUser) {
        // Authenticated but not a new user - redirect to dashboard
        console.log('🔄 Onboarding page: Not a new user, redirecting to dashboard');
        router.push('/dashboard');
      }
    }
  }, [isLoading, isAuthenticated, isNewUser, isCompletingOnboarding, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !isNewUser) {
    return null; // Will redirect via useEffect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <OnboardingErrorBoundary>
        <OnboardingFlow />
      </OnboardingErrorBoundary>
    </div>
  );
}
