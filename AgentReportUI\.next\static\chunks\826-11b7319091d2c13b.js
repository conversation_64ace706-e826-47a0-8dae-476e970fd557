"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[826],{9449:(e,t,n)=>{n.d(t,{UC:()=>e3,q7:()=>e8,ZL:()=>e6,bL:()=>e2,l9:()=>e4});var r=n(12115),o=n(85185),a=n(6101),l=n(46081),i=n(5845),u=n(63655),s=n(37328),d=n(94315),c=n(19178),p=n(92293),f=n(25519),m=n(61285),h=n(35152),v=n(34378),g=n(28905),y=n(39033),w=n(95155),x="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},M="RovingFocusGroup",[C,k,R]=(0,s.N)(M),[D,j]=(0,l.A)(M,[R]),[A,E]=D(M),I=r.forwardRef((e,t)=>(0,w.jsx)(C.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(C.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(N,{...e,ref:t})})}));I.displayName=M;var N=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:l,loop:s=!1,dir:c,currentTabStopId:p,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:m,onEntryFocus:h,preventScrollOnEntryFocus:v=!1,...g}=e,C=r.useRef(null),R=(0,a.s)(t,C),D=(0,d.jH)(c),[j,E]=(0,i.i)({prop:p,defaultProp:null!=f?f:null,onChange:m,caller:M}),[I,N]=r.useState(!1),T=(0,y.c)(h),_=k(n),O=r.useRef(!1),[S,F]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(x,T),()=>e.removeEventListener(x,T)},[T]),(0,w.jsx)(A,{scope:n,orientation:l,dir:D,loop:s,currentTabStopId:j,onItemFocus:r.useCallback(e=>E(e),[E]),onItemShiftTab:r.useCallback(()=>N(!0),[]),onFocusableItemAdd:r.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>F(e=>e-1),[]),children:(0,w.jsx)(u.sG.div,{tabIndex:I||0===S?-1:0,"data-orientation":l,...g,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!I){let t=new CustomEvent(x,b);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);P([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),v)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>N(!1))})})}),T="RovingFocusGroupItem",_=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:l=!1,tabStopId:i,children:s,...d}=e,c=(0,m.B)(),p=i||c,f=E(T,n),h=f.currentTabStopId===p,v=k(n),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:x}=f;return r.useEffect(()=>{if(a)return g(),()=>y()},[a,g,y]),(0,w.jsx)(C.ItemSlot,{scope:n,id:p,focusable:a,active:l,children:(0,w.jsx)(u.sG.span,{tabIndex:h?0:-1,"data-orientation":f.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?f.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>f.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return O[o]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=f.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>P(n))}}),children:"function"==typeof s?s({isCurrentTabStop:h,hasTabStop:null!=x}):s})})});_.displayName=T;var O={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function P(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var S=n(99708),F=n(38168),L=n(93795),K=["Enter"," "],G=["ArrowUp","PageDown","End"],U=["ArrowDown","PageUp","Home",...G],B={ltr:[...K,"ArrowRight"],rtl:[...K,"ArrowLeft"]},q={ltr:["ArrowLeft"],rtl:["ArrowRight"]},V="Menu",[W,H,Z]=(0,s.N)(V),[z,X]=(0,l.A)(V,[Z,h.Bk,j]),J=(0,h.Bk)(),Y=j(),[Q,$]=z(V),[ee,et]=z(V),en=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:a,onOpenChange:l,modal:i=!0}=e,u=J(t),[s,c]=r.useState(null),p=r.useRef(!1),f=(0,y.c)(l),m=(0,d.jH)(a);return r.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,w.jsx)(h.bL,{...u,children:(0,w.jsx)(Q,{scope:t,open:n,onOpenChange:f,content:s,onContentChange:c,children:(0,w.jsx)(ee,{scope:t,onClose:r.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:m,modal:i,children:o})})})};en.displayName=V;var er=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=J(n);return(0,w.jsx)(h.Mz,{...o,...r,ref:t})});er.displayName="MenuAnchor";var eo="MenuPortal",[ea,el]=z(eo,{forceMount:void 0}),ei=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=$(eo,t);return(0,w.jsx)(ea,{scope:t,forceMount:n,children:(0,w.jsx)(g.C,{present:n||a.open,children:(0,w.jsx)(v.Z,{asChild:!0,container:o,children:r})})})};ei.displayName=eo;var eu="MenuContent",[es,ed]=z(eu),ec=r.forwardRef((e,t)=>{let n=el(eu,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=$(eu,e.__scopeMenu),l=et(eu,e.__scopeMenu);return(0,w.jsx)(W.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(g.C,{present:r||a.open,children:(0,w.jsx)(W.Slot,{scope:e.__scopeMenu,children:l.modal?(0,w.jsx)(ep,{...o,ref:t}):(0,w.jsx)(ef,{...o,ref:t})})})})}),ep=r.forwardRef((e,t)=>{let n=$(eu,e.__scopeMenu),l=r.useRef(null),i=(0,a.s)(t,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,F.Eq)(e)},[]),(0,w.jsx)(eh,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),ef=r.forwardRef((e,t)=>{let n=$(eu,e.__scopeMenu);return(0,w.jsx)(eh,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),em=(0,S.TL)("MenuContent.ScrollLock"),eh=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:i,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,disableOutsideScroll:M,...C}=e,k=$(eu,n),R=et(eu,n),D=J(n),j=Y(n),A=H(n),[E,N]=r.useState(null),T=r.useRef(null),_=(0,a.s)(t,T,k.onContentChange),O=r.useRef(0),P=r.useRef(""),S=r.useRef(0),F=r.useRef(null),K=r.useRef("right"),B=r.useRef(0),q=M?L.A:r.Fragment,V=e=>{var t,n;let r=P.current+e,o=A().filter(e=>!e.disabled),a=document.activeElement,l=null==(t=o.find(e=>e.ref.current===a))?void 0:t.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,l=(r=Math.max(a,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let i=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==n?i:void 0}(o.map(e=>e.textValue),r,l),u=null==(n=o.find(e=>e.textValue===i))?void 0:n.ref.current;!function e(t){P.current=t,window.clearTimeout(O.current),""!==t&&(O.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())};r.useEffect(()=>()=>window.clearTimeout(O.current),[]),(0,p.Oh)();let W=r.useCallback(e=>{var t,n;return K.current===(null==(t=F.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let l=t[e],i=t[a],u=l.x,s=l.y,d=i.x,c=i.y;s>r!=c>r&&n<(d-u)*(r-s)/(c-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=F.current)?void 0:n.area)},[]);return(0,w.jsx)(es,{scope:n,searchRef:P,onItemEnter:r.useCallback(e=>{W(e)&&e.preventDefault()},[W]),onItemLeave:r.useCallback(e=>{var t;W(e)||(null==(t=T.current)||t.focus(),N(null))},[W]),onTriggerLeave:r.useCallback(e=>{W(e)&&e.preventDefault()},[W]),pointerGraceTimerRef:S,onPointerGraceIntentChange:r.useCallback(e=>{F.current=e},[]),children:(0,w.jsx)(q,{...M?{as:em,allowPinchZoom:!0}:void 0,children:(0,w.jsx)(f.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.m)(u,e=>{var t;e.preventDefault(),null==(t=T.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,w.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,children:(0,w.jsx)(I,{asChild:!0,...j,dir:R.dir,orientation:"vertical",loop:l,currentTabStopId:E,onCurrentTabStopIdChange:N,onEntryFocus:(0,o.m)(m,e=>{R.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,w.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eU(k.open),"data-radix-menu-content":"",dir:R.dir,...D,...C,ref:_,style:{outline:"none",...C.style},onKeyDown:(0,o.m)(C.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&V(e.key));let o=T.current;if(e.target!==o||!U.includes(e.key))return;e.preventDefault();let a=A().filter(e=>!e.disabled).map(e=>e.ref.current);G.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(O.current),P.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eV(e=>{let t=e.target,n=B.current!==e.clientX;e.currentTarget.contains(t)&&n&&(K.current=e.clientX>B.current?"right":"left",B.current=e.clientX)}))})})})})})})});ec.displayName=eu;var ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"group",...r,ref:t})});ev.displayName="MenuGroup";var eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{...r,ref:t})});eg.displayName="MenuLabel";var ey="MenuItem",ew="menu.itemSelect",ex=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:l,...i}=e,s=r.useRef(null),d=et(ey,e.__scopeMenu),c=ed(ey,e.__scopeMenu),p=(0,a.s)(t,s),f=r.useRef(!1);return(0,w.jsx)(eb,{...i,ref:p,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let t=new CustomEvent(ew,{bubbles:!0,cancelable:!0});e.addEventListener(ew,e=>null==l?void 0:l(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;f.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;n||t&&" "===e.key||K.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ex.displayName=ey;var eb=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:l=!1,textValue:i,...s}=e,d=ed(ey,n),c=Y(n),p=r.useRef(null),f=(0,a.s)(t,p),[m,h]=r.useState(!1),[v,g]=r.useState("");return r.useEffect(()=>{let e=p.current;if(e){var t;g((null!=(t=e.textContent)?t:"").trim())}},[s.children]),(0,w.jsx)(W.ItemSlot,{scope:n,disabled:l,textValue:null!=i?i:v,children:(0,w.jsx)(_,{asChild:!0,...c,focusable:!l,children:(0,w.jsx)(u.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eV(e=>{l?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eV(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),eM=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...a}=e;return(0,w.jsx)(eI,{scope:e.__scopeMenu,checked:n,children:(0,w.jsx)(ex,{role:"menuitemcheckbox","aria-checked":eB(n)?"mixed":n,...a,ref:t,"data-state":eq(n),onSelect:(0,o.m)(a.onSelect,()=>null==r?void 0:r(!!eB(n)||!n),{checkForDefaultPrevented:!1})})})});eM.displayName="MenuCheckboxItem";var eC="MenuRadioGroup",[ek,eR]=z(eC,{value:void 0,onValueChange:()=>{}}),eD=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,a=(0,y.c)(r);return(0,w.jsx)(ek,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,w.jsx)(ev,{...o,ref:t})})});eD.displayName=eC;var ej="MenuRadioItem",eA=r.forwardRef((e,t)=>{let{value:n,...r}=e,a=eR(ej,e.__scopeMenu),l=n===a.value;return(0,w.jsx)(eI,{scope:e.__scopeMenu,checked:l,children:(0,w.jsx)(ex,{role:"menuitemradio","aria-checked":l,...r,ref:t,"data-state":eq(l),onSelect:(0,o.m)(r.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});eA.displayName=ej;var eE="MenuItemIndicator",[eI,eN]=z(eE,{checked:!1}),eT=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,a=eN(eE,n);return(0,w.jsx)(g.C,{present:r||eB(a.checked)||!0===a.checked,children:(0,w.jsx)(u.sG.span,{...o,ref:t,"data-state":eq(a.checked)})})});eT.displayName=eE;var e_=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});e_.displayName="MenuSeparator";var eO=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=J(n);return(0,w.jsx)(h.i3,{...o,...r,ref:t})});eO.displayName="MenuArrow";var[eP,eS]=z("MenuSub"),eF="MenuSubTrigger",eL=r.forwardRef((e,t)=>{let n=$(eF,e.__scopeMenu),l=et(eF,e.__scopeMenu),i=eS(eF,e.__scopeMenu),u=ed(eF,e.__scopeMenu),s=r.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=u,p={__scopeMenu:e.__scopeMenu},f=r.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return r.useEffect(()=>f,[f]),r.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,w.jsx)(er,{asChild:!0,...p,children:(0,w.jsx)(eb,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":i.contentId,"data-state":eU(n.open),...e,ref:(0,a.t)(t,i.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eV(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eV(e=>{var t,r;f();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,a="right"===t,l=o[a?"left":"right"],i=o[a?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:i,y:o.top},{x:i,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;if(!e.disabled&&(!r||" "!==t.key)&&B[l.dir].includes(t.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),t.preventDefault()}})})})});eL.displayName=eF;var eK="MenuSubContent",eG=r.forwardRef((e,t)=>{let n=el(eu,e.__scopeMenu),{forceMount:l=n.forceMount,...i}=e,u=$(eu,e.__scopeMenu),s=et(eu,e.__scopeMenu),d=eS(eK,e.__scopeMenu),c=r.useRef(null),p=(0,a.s)(t,c);return(0,w.jsx)(W.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(g.C,{present:l||u.open,children:(0,w.jsx)(W.Slot,{scope:e.__scopeMenu,children:(0,w.jsx)(eh,{id:d.contentId,"aria-labelledby":d.triggerId,...i,ref:p,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;s.isUsingKeyboardRef.current&&(null==(t=c.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=q[s.dir].includes(e.key);if(t&&n){var r;u.onOpenChange(!1),null==(r=d.trigger)||r.focus(),e.preventDefault()}})})})})})});function eU(e){return e?"open":"closed"}function eB(e){return"indeterminate"===e}function eq(e){return eB(e)?"indeterminate":e?"checked":"unchecked"}function eV(e){return t=>"mouse"===t.pointerType?e(t):void 0}eG.displayName=eK;var eW="DropdownMenu",[eH,eZ]=(0,l.A)(eW,[X]),ez=X(),[eX,eJ]=eH(eW),eY=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:a,defaultOpen:l,onOpenChange:u,modal:s=!0}=e,d=ez(t),c=r.useRef(null),[p,f]=(0,i.i)({prop:a,defaultProp:null!=l&&l,onChange:u,caller:eW});return(0,w.jsx)(eX,{scope:t,triggerId:(0,m.B)(),triggerRef:c,contentId:(0,m.B)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:s,children:(0,w.jsx)(en,{...d,open:p,onOpenChange:f,dir:o,modal:s,children:n})})};eY.displayName=eW;var eQ="DropdownMenuTrigger",e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...l}=e,i=eJ(eQ,n),s=ez(n);return(0,w.jsx)(er,{asChild:!0,...s,children:(0,w.jsx)(u.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...l,ref:(0,a.t)(t,i.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e$.displayName=eQ;var e0=e=>{let{__scopeDropdownMenu:t,...n}=e,r=ez(t);return(0,w.jsx)(ei,{...r,...n})};e0.displayName="DropdownMenuPortal";var e1="DropdownMenuContent",e5=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,l=eJ(e1,n),i=ez(n),u=r.useRef(!1);return(0,w.jsx)(ec,{id:l.contentId,"aria-labelledby":l.triggerId,...i,...a,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;u.current||null==(t=l.triggerRef.current)||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!l.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e5.displayName=e1,r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ez(n);return(0,w.jsx)(ev,{...o,...r,ref:t})}).displayName="DropdownMenuGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ez(n);return(0,w.jsx)(eg,{...o,...r,ref:t})}).displayName="DropdownMenuLabel";var e9=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ez(n);return(0,w.jsx)(ex,{...o,...r,ref:t})});e9.displayName="DropdownMenuItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ez(n);return(0,w.jsx)(eM,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ez(n);return(0,w.jsx)(eD,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ez(n);return(0,w.jsx)(eA,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ez(n);return(0,w.jsx)(eT,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ez(n);return(0,w.jsx)(e_,{...o,...r,ref:t})}).displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ez(n);return(0,w.jsx)(eO,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ez(n);return(0,w.jsx)(eL,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ez(n);return(0,w.jsx)(eG,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e2=eY,e4=e$,e6=e0,e3=e5,e8=e9},13052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15452:(e,t,n)=>{n.d(t,{UC:()=>ee,ZL:()=>Q,bL:()=>J,bm:()=>et,hJ:()=>$,l9:()=>Y});var r=n(12115),o=n(85185),a=n(6101),l=n(46081),i=n(61285),u=n(5845),s=n(19178),d=n(25519),c=n(34378),p=n(28905),f=n(63655),m=n(92293),h=n(93795),v=n(38168),g=n(99708),y=n(95155),w="Dialog",[x,b]=(0,l.A)(w),[M,C]=x(w),k=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:l,modal:s=!0}=e,d=r.useRef(null),c=r.useRef(null),[p,f]=(0,u.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:w});return(0,y.jsx)(M,{scope:t,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:s,children:n})};k.displayName=w;var R="DialogTrigger",D=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=C(R,n),i=(0,a.s)(t,l.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":V(l.open),...r,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});D.displayName=R;var j="DialogPortal",[A,E]=x(j,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,l=C(j,t);return(0,y.jsx)(A,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(p.C,{present:n||l.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};I.displayName=j;var N="DialogOverlay",T=r.forwardRef((e,t)=>{let n=E(N,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=C(N,e.__scopeDialog);return a.modal?(0,y.jsx)(p.C,{present:r||a.open,children:(0,y.jsx)(O,{...o,ref:t})}):null});T.displayName=N;var _=(0,g.TL)("DialogOverlay.RemoveScroll"),O=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(N,n);return(0,y.jsx)(h.A,{as:_,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":V(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",S=r.forwardRef((e,t)=>{let n=E(P,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=C(P,e.__scopeDialog);return(0,y.jsx)(p.C,{present:r||a.open,children:a.modal?(0,y.jsx)(F,{...o,ref:t}):(0,y.jsx)(L,{...o,ref:t})})});S.displayName=P;var F=r.forwardRef((e,t)=>{let n=C(P,e.__scopeDialog),l=r.useRef(null),i=(0,a.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(K,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,t)=>{let n=C(P,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,y.jsx)(K,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),K=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...u}=e,c=C(P,n),p=r.useRef(null),f=(0,a.s)(t,p);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,y.jsx)(s.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":V(c.open),...u,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(z,{titleId:c.titleId}),(0,y.jsx)(X,{contentRef:p,descriptionId:c.descriptionId})]})]})}),G="DialogTitle";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(G,n);return(0,y.jsx)(f.sG.h2,{id:o.titleId,...r,ref:t})}).displayName=G;var U="DialogDescription";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(U,n);return(0,y.jsx)(f.sG.p,{id:o.descriptionId,...r,ref:t})}).displayName=U;var B="DialogClose",q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=C(B,n);return(0,y.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function V(e){return e?"open":"closed"}q.displayName=B;var W="DialogTitleWarning",[H,Z]=(0,l.q)(W,{contentName:P,titleName:G,docsSlug:"dialog"}),z=e=>{let{titleId:t}=e,n=Z(W),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},X=e=>{let{contentRef:t,descriptionId:n}=e,o=Z("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(a))},[a,t,n]),null},J=k,Y=D,Q=I,$=T,ee=S,et=q},28905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(12115),o=n(6101),a=n(52712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),s=r.useRef(e),d=r.useRef("none"),[c,p]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(u.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=d.current,o=i(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):n&&r!==o?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=e}},[e,p]),(0,a.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=i(u.current).includes(e.animationName);if(e.target===o&&r&&(p("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=i(u.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),s=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||l.isPresent?r.cloneElement(u,{ref:s}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},34835:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},38857:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("message-circle-plus",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},44020:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},49376:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},51362:(e,t,n)=>{n.d(t,{D:()=>s,N:()=>d});var r=n(12115),o=(e,t,n,r,o,a,l,i)=>{let u=document.documentElement,s=["light","dark"];function d(t){var n;(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&a?o.map(e=>a[e]||e):o;n?(u.classList.remove(...r),u.classList.add(a&&a[t]?a[t]:t)):u.setAttribute(e,t)}),n=t,i&&s.includes(n)&&(u.style.colorScheme=n)}if(r)d(r);else try{let e=localStorage.getItem(t)||n,r=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(r)}catch(e){}},a=["light","dark"],l="(prefers-color-scheme: dark)",i=r.createContext(void 0),u={setTheme:e=>{},themes:[]},s=()=>{var e;return null!=(e=r.useContext(i))?e:u},d=e=>r.useContext(i)?r.createElement(r.Fragment,null,e.children):r.createElement(p,{...e}),c=["light","dark"],p=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:o=!0,enableColorScheme:u=!0,storageKey:s="theme",themes:d=c,defaultTheme:p=o?"system":"light",attribute:g="data-theme",value:y,children:w,nonce:x,scriptProps:b}=e,[M,C]=r.useState(()=>m(s,p)),[k,R]=r.useState(()=>"system"===M?v():M),D=y?Object.values(y):d,j=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=v());let r=y?y[t]:t,l=n?h(x):null,i=document.documentElement,s=e=>{"class"===e?(i.classList.remove(...D),r&&i.classList.add(r)):e.startsWith("data-")&&(r?i.setAttribute(e,r):i.removeAttribute(e))};if(Array.isArray(g)?g.forEach(s):s(g),u){let e=a.includes(p)?p:null,n=a.includes(t)?t:e;i.style.colorScheme=n}null==l||l()},[x]),A=r.useCallback(e=>{let t="function"==typeof e?e(M):e;C(t);try{localStorage.setItem(s,t)}catch(e){}},[M]),E=r.useCallback(e=>{R(v(e)),"system"===M&&o&&!t&&j("system")},[M,t]);r.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(E),E(e),()=>e.removeListener(E)},[E]),r.useEffect(()=>{let e=e=>{e.key===s&&(e.newValue?C(e.newValue):A(p))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[A]),r.useEffect(()=>{j(null!=t?t:M)},[t,M]);let I=r.useMemo(()=>({theme:M,setTheme:A,forcedTheme:t,resolvedTheme:"system"===M?k:M,themes:o?[...d,"system"]:d,systemTheme:o?k:void 0}),[M,A,t,k,o,d]);return r.createElement(i.Provider,{value:I},r.createElement(f,{forcedTheme:t,storageKey:s,attribute:g,enableSystem:o,enableColorScheme:u,defaultTheme:p,value:y,themes:d,nonce:x,scriptProps:b}),w)},f=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:a,enableSystem:l,enableColorScheme:i,defaultTheme:u,value:s,themes:d,nonce:c,scriptProps:p}=e,f=JSON.stringify([a,n,u,t,d,s,l,i]).slice(1,-1);return r.createElement("script",{...p,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(f,")")}})}),m=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},h=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},v=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")},73783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},74783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},91788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])}}]);