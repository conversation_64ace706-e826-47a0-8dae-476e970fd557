"""
Query API
–––––––––
Two chat entry-points:

1. /question → database / SQL pipeline with optional streaming
2. /report   → report / analysis pipeline (+ optional user file upload)

The controller stays slim—heavy lifting lives in services.
"""

from typing import Optional, List
import json
import asyncio

from fastapi import APIRouter, Depends, UploadFile, File, Form, Request, HTTPException
from fastapi.security import H<PERSON><PERSON><PERSON>earer

from app.models.api_models import QueryRequest, QueryResponse
from app.services.database_manager_service import DatabaseManagerService
from app.services.report_manager_service import ReportManagerService
from app.services.connection_pool_manager import get_connection_pool_manager
from app.utils.security import get_current_user, oauth2_scheme

router = APIRouter(prefix="/api/ask", tags=["query"])
db_manager_service = DatabaseManagerService()
report_manager_service = ReportManagerService()

# ────────────────────────────────────────────────────────────────
# auth helpers
# ────────────────────────────────────────────────────────────────
async def get_current_user_id(current_user=Depends(get_current_user)) -> str:
    return current_user.id

async def get_current_user_and_token(
    current_user=Depends(get_current_user),
    token: str = Depends(oauth2_scheme)
) -> tuple[str, str]:
    """Get both user ID and JWT token for session-aware connection pooling."""
    return current_user.id, token

# ────────────────────────────────────────────────────────────────
# question endpoint with optional streaming
# ────────────────────────────────────────────────────────────────
@router.post("/question")
async def ask_data_question(
    request: Request,
    *,
    query: Optional[str] = Form(None),
    output_format: Optional[str] = Form("json"),
    session_id: Optional[str] = Form(None),
    target_databases: Optional[str] = Form(None),  # comma-separated list
    target_tables: Optional[str] = Form(None),     # JSON string
    target_columns: Optional[str] = Form(None),    # JSON string
    enable_token_streaming: Optional[bool] = Form(False),    # Enable token-level streaming
    user_auth: tuple[str, str] = Depends(get_current_user_and_token),
):
    """
    Question answering endpoint with optional real-time token streaming.
    Supports both JSON and form-data requests.

    When enable_token_streaming=True, returns Server-Sent Events (SSE) with token-level streaming.
    When enable_token_streaming=False, returns standard JSON response.

    Streaming provides real-time feedback for the final answer generation only.
    """
    # Extract user ID and token from authentication
    user_id, token = user_auth

    # Initialize connection pool manager for this user session
    connection_pool_manager = get_connection_pool_manager()

    # Handle JSON requests from frontend
    content_type = request.headers.get("content-type", "")
    if "application/json" in content_type:
        try:
            json_data = await request.json()
            query = json_data.get("query")
            output_format = json_data.get("output_format", "json")
            session_id = json_data.get("session_id")
            target_databases = json_data.get("target_databases")
            target_tables = json_data.get("target_tables")
            target_columns = json_data.get("target_columns")
            enable_token_streaming = json_data.get("enable_token_streaming", False)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON data: {str(e)}")

    # Validate required fields
    if not query:
        raise HTTPException(status_code=422, detail="Field 'query' is required")

    # Parse complex parameters for both JSON and form data
    parsed_target_databases = None
    parsed_target_tables = None
    parsed_target_columns = None

    if target_databases:
        if isinstance(target_databases, str):
            parsed_target_databases = [db.strip() for db in target_databases.split(",")]
        elif isinstance(target_databases, list):
            parsed_target_databases = target_databases

    if target_tables:
        if isinstance(target_tables, str):
            try:
                parsed_target_tables = json.loads(target_tables)
            except json.JSONDecodeError:
                parsed_target_tables = None
        elif isinstance(target_tables, dict):
            parsed_target_tables = target_tables

    if target_columns:
        if isinstance(target_columns, str):
            try:
                parsed_target_columns = json.loads(target_columns)
            except json.JSONDecodeError:
                parsed_target_columns = None
        elif isinstance(target_columns, dict):
            parsed_target_columns = target_columns

    if enable_token_streaming:
        # Return streaming response (already wrapped in StreamingResponse by service)
        return await db_manager_service.query_databases_streaming(
            query=query,
            output_format=output_format,
            user_id=user_id,
            session_id=session_id,
            target_databases=parsed_target_databases,
            target_tables=parsed_target_tables,
            target_columns=parsed_target_columns,
        )
    else:
        return await db_manager_service.query_databases(
            query=query,
            output_format=output_format,
            user_id=user_id,
            session_id=session_id,
            target_databases=parsed_target_databases,
            target_tables=parsed_target_tables,
            target_columns=parsed_target_columns,
        )

# ────────────────────────────────────────────────────────────────
# report generation (/report)
# ────────────────────────────────────────────────────────────────
@router.post("/report")
async def ask_report_question(
    *,
    query: str = Form(""),  # Make query optional since we can auto-generate for file uploads
    output_format: str = Form("csv"),
    session_id: Optional[str] = Form(None),
    target_datasets: Optional[str] = Form(None),     # comma-sep list
    file: Optional[UploadFile] = File(None),         # optional user upload
    user_id: str = Depends(get_current_user_id),
):
    """
    Report generation endpoint for comprehensive data analysis.
    Returns standard JSON response with analysis results.

    Requires either target_datasets or file upload to work with.
    No automatic dataset discovery - only works with explicitly provided datasets.
    """

    return await report_manager_service.handle_report_request(
        query=query,
        output_format=output_format,
        user_id=user_id,
        session_id=session_id,
        target_datasets=target_datasets,
        file=file,
    )
