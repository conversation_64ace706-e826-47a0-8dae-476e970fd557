(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[962],{1922:(e,t,n)=>{"use strict";n.d(t,{dc:()=>a,VG:()=>s});var r=n(17915);let i=[],a=!1;function s(e,t,n,s){let o;"function"==typeof t&&"function"!=typeof n?(s=n,n=t):o=t;let l=(0,r.C)(o),c=s?-1:1;(function e(r,o,u){let h=r&&"object"==typeof r?r:{};if("string"==typeof h.type){let e="string"==typeof h.tagName?h.tagName:"string"==typeof h.name?h.name:void 0;Object.defineProperty(d,"name",{value:"node ("+r.type+(e?"<"+e+">":"")+")"})}return d;function d(){var h;let d,p,f,m=i;if((!t||l(r,o,u[u.length-1]||void 0))&&(m=Array.isArray(h=n(r,u))?h:"number"==typeof h?[!0,h]:null==h?i:[h])[0]===a)return m;if("children"in r&&r.children&&r.children&&"skip"!==m[0])for(p=(s?r.children.length:-1)+c,f=u.concat(r);p>-1&&p<r.children.length;){if((d=e(r.children[p],p,f)())[0]===a)return d;p="number"==typeof d[1]?d[1]:p+c}return m}})(e,void 0,[])()}},4392:(e,t,n)=>{"use strict";n.d(t,{d:()=>i});let r={};function i(e,t){let n=t||r;return a(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function a(e,t,n){var r;if((r=e)&&"object"==typeof r){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return s(e.children,t,n)}return Array.isArray(e)?s(e,t,n):""}function s(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=a(e[i],t,n);return r.join("")}},7887:(e,t,n)=>{"use strict";n.d(t,{I:()=>c});var r=n(25437),i=n(68581),a=n(59739);let s=/[A-Z]/g,o=/-[a-z]/g,l=/^data[-\w.:]+$/i;function c(e,t){let n=(0,a.S)(t),c=t,d=i.R;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&l.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(o,h);c="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!o.test(e)){let n=e.replace(s,u);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}d=r.E}return new d(c,t)}function u(e){return"-"+e.toLowerCase()}function h(e){return e.charAt(1).toUpperCase()}},8918:(e,t,n)=>{"use strict";n.r(t),n.d(t,{boolean:()=>i,booleanish:()=>a,commaOrSpaceSeparated:()=>u,commaSeparated:()=>c,number:()=>o,overloadedBoolean:()=>s,spaceSeparated:()=>l});let r=0,i=h(),a=h(),s=h(),o=h(),l=h(),c=h(),u=h();function h(){return 2**++r}},11603:(e,t,n)=>{"use strict";function r(e,t,n,r){let i,a=e.length,s=0;if(t=t<0?-t>a?0:a+t:t>a?a:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);s<r.length;)(i=r.slice(s,s+1e4)).unshift(t,0),e.splice(...i),s+=1e4,t+=1e4}function i(e,t){return e.length>0?(r(e,e.length,0,t),e):t}n.d(t,{V:()=>i,m:()=>r})},12556:(e,t,n)=>{"use strict";n.d(t,{BM:()=>o,CW:()=>r,Ee:()=>h,HP:()=>u,JQ:()=>s,Ny:()=>f,On:()=>d,cx:()=>a,es:()=>p,lV:()=>i,ok:()=>l,ol:()=>c});let r=m(/[A-Za-z]/),i=m(/[\dA-Za-z]/),a=m(/[#-'*+\--9=?A-Z^-~]/);function s(e){return null!==e&&(e<32||127===e)}let o=m(/\d/),l=m(/[\dA-Fa-f]/),c=m(/[!-/:-@[-`{-~]/);function u(e){return null!==e&&e<-2}function h(e){return null!==e&&(e<0||32===e)}function d(e){return -2===e||-1===e||32===e}let p=m(/\p{P}|\p{S}/u),f=m(/\s/);function m(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}},14947:(e,t,n)=>{"use strict";function r(e){let t=String(e||"").trim();return t?t.split(/[ \t\n\r\f]+/g):[]}function i(e){return e.join(" ").trim()}n.d(t,{A:()=>i,q:()=>r})},17915:(e,t,n)=>{"use strict";n.d(t,{C:()=>r});let r=function(e){var t,n;if(null==e)return a;if("function"==typeof e)return i(e);if("object"==typeof e){return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=r(e[n]);return i(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):(t=e,i(function(e){let n;for(n in t)if(e[n]!==t[n])return!1;return!0}))}if("string"==typeof e){return n=e,i(function(e){return e&&e.type===n})}throw Error("Expected function, string, or object as test")};function i(e){return function(t,n,r){return!!(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function a(){return!0}},25437:(e,t,n)=>{"use strict";n.d(t,{E:()=>s});var r=n(68581),i=n(8918);let a=Object.keys(i);class s extends r.R{constructor(e,t,n,r){let s=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",r),"number"==typeof n)for(;++s<a.length;){let e=a[s];!function(e,t,n){n&&(e[t]=n)}(this,a[s],(n&i[e])===i[e])}}}s.prototype.defined=!0},31300:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,a=/^-(webkit|moz|ms|o|khtml)-/,s=/^-(ms)-/,o=function(e,t){return t.toUpperCase()},l=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var c;return(void 0===t&&(t={}),!(c=e)||i.test(c)||n.test(c))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(s,l):e.replace(a,l)).replace(r,o))}},33386:(e,t,n)=>{"use strict";function r(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}n.d(t,{B:()=>r})},33786:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},34093:(e,t,n)=>{"use strict";function r(){}function i(){}n.d(t,{HB:()=>i,ok:()=>r})},36301:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,a=/^:\s*/,s=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,o=/^[;\s]*/,l=/^\s+|\s+$/g;function c(e){return e?e.replace(l,""):""}e.exports=function(e,l){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];l=l||{};var u=1,h=1;function d(e){var t=e.match(n);t&&(u+=t.length);var r=e.lastIndexOf("\n");h=~r?e.length-r:h+e.length}function p(){var e={line:u,column:h};return function(t){return t.position=new f(e),T(r),t}}function f(e){this.start=e,this.end={line:u,column:h},this.source=l.source}f.prototype.content=e;var m=[];function E(t){var n=Error(l.source+":"+u+":"+h+": "+t);if(n.reason=t,n.filename=l.source,n.line=u,n.column=h,n.source=e,l.silent)m.push(n);else throw n}function T(t){var n=t.exec(e);if(n){var r=n[0];return d(r),e=e.slice(r.length),n}}function g(e){var t;for(e=e||[];t=A();)!1!==t&&e.push(t);return e}function A(){var t=p();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return E("End of comment missing");var r=e.slice(2,n-2);return h+=2,d(r),e=e.slice(n),h+=2,t({type:"comment",comment:r})}}T(r);var _,S=[];for(g(S);_=function(){var e=p(),n=T(i);if(n){if(A(),!T(a))return E("property missing ':'");var r=T(s),l=e({type:"declaration",property:c(n[0].replace(t,"")),value:r?c(r[0].replace(t,"")):""});return T(o),l}}();)!1!==_&&(S.push(_),g(S));return S}},49535:(e,t,n)=>{"use strict";n.d(t,{S:()=>i});var r=n(12556);function i(e){return null===e||(0,r.Ee)(e)||(0,r.Ny)(e)?1:(0,r.es)(e)?2:void 0}},51154:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53360:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},s=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),a=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!a)return!1;for(r in e);return void 0===r||t.call(e,r)},o=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},l=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,c,u,h=arguments[0],d=1,p=arguments.length,f=!1;for("boolean"==typeof h&&(f=h,h=arguments[1]||{},d=2),(null==h||"object"!=typeof h&&"function"!=typeof h)&&(h={});d<p;++d)if(t=arguments[d],null!=t)for(n in t)r=l(h,n),h!==(i=l(t,n))&&(f&&i&&(s(i)||(c=a(i)))?(c?(c=!1,u=r&&a(r)?r:[]):u=r&&s(r)?r:{},o(h,{name:n,newValue:e(f,u,i)})):void 0!==i&&o(h,{name:n,newValue:i}));return h}},53724:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(87924)),i=n(31300);function a(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}a.default=a,e.exports=a},55548:(e,t,n)=>{"use strict";function r(e){let t=[],n=String(e||""),r=n.indexOf(","),i=0,a=!1;for(;!a;){-1===r&&(r=n.length,a=!0);let e=n.slice(i,r).trim();(e||!a)&&t.push(e),i=r+1,r=n.indexOf(",",i)}return t}function i(e,t){let n=t||{};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}n.d(t,{A:()=>i,q:()=>r})},56454:(e,t,n)=>{"use strict";n.d(t,{oz:()=>tB});var r={};n.r(r),n.d(r,{attentionMarkers:()=>eQ,contentInitial:()=>eG,disable:()=>ej,document:()=>eU,flow:()=>ez,flowInitial:()=>eY,insideSpan:()=>eW,string:()=>eV,text:()=>eq});var i=n(34093),a=n(55548);let s=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,o=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,l={};function c(e,t){return((t||l).jsx?o:s).test(e)}let u=/[ \t\n\f\r]/g;function h(e){return""===e.replace(u,"")}var d=n(83846),p=n(7887);let f={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var m=n(14947),E=n(53724),T=n(70832);function g(e){return e&&"object"==typeof e?"position"in e||"type"in e?_(e.position):"start"in e||"end"in e?_(e):"line"in e||"column"in e?A(e):"":""}function A(e){return S(e&&e.line)+":"+S(e&&e.column)}function _(e){return A(e&&e.start)+"-"+A(e&&e.end)}function S(e){return e&&"number"==typeof e?e:1}class I extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},a=!1;if(t&&(i="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(a=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let s=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=s?s.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=s?s.line:void 0,this.name=g(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=a&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual,this.expected,this.note,this.url}}I.prototype.file="",I.prototype.name="",I.prototype.reason="",I.prototype.message="",I.prototype.stack="",I.prototype.column=void 0,I.prototype.line=void 0,I.prototype.ancestors=void 0,I.prototype.cause=void 0,I.prototype.fatal=void 0,I.prototype.place=void 0,I.prototype.ruleId=void 0,I.prototype.source=void 0;let k={}.hasOwnProperty,C=new Map,N=/[A-Z]/g,D=new Set(["table","tbody","thead","tfoot","tr"]),b=new Set(["td","th"]),y="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function O(e,t,n){var r;return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(e.schema=d.JW),e.ancestors.push(t);let s=M(e,t.tagName,!1),o=function(e,t){let n,r,i={};for(r in t.properties)if("children"!==r&&k.call(t.properties,r)){let s=function(e,t,n){let r=(0,p.I)(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?(0,a.A)(n):(0,m.A)(n)),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return E(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new I("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=y+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t,n={};for(t in e)k.call(e,t)&&(n[function(e){let t=e.replace(N,x);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?f[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(s){let[r,a]=s;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof a&&b.has(t.tagName)?n=a:i[r]=a}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),l=P(e,t);return D.has(t.tagName)&&(l=l.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&h(e.value):h(e))})),R(e,o,s,t),L(o,l),e.ancestors.pop(),e.schema=r,e.create(t,s,o,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return(0,i.ok)("ExpressionStatement"===n.type),e.evaluater.evaluateExpression(n.expression)}v(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,a=r;"svg"===t.name&&"html"===r.space&&(e.schema=d.JW),e.ancestors.push(t);let s=null===t.name?e.Fragment:M(e,t.name,!0),o=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];(0,i.ok)("ExpressionStatement"===t.type);let a=t.expression;(0,i.ok)("ObjectExpression"===a.type);let s=a.properties[0];(0,i.ok)("SpreadElement"===s.type),Object.assign(n,e.evaluater.evaluateExpression(s.argument))}else v(e,t.position);else{let a,s=r.name;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];(0,i.ok)("ExpressionStatement"===t.type),a=e.evaluater.evaluateExpression(t.expression)}else v(e,t.position);else a=null===r.value||r.value;n[s]=a}return n}(e,t),l=P(e,t);return R(e,o,s,t),L(o,l),e.ancestors.pop(),e.schema=r,e.create(t,s,o,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);v(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return L(r,P(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?(r=0,t.value):void 0}function R(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function L(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function P(e,t){let n=[],r=-1,i=e.passKeys?new Map:C;for(;++r<t.children.length;){let a,s=t.children[r];if(e.passKeys){let e="element"===s.type?s.tagName:"mdxJsxFlowElement"===s.type||"mdxJsxTextElement"===s.type?s.name:void 0;if(e){let t=i.get(e)||0;a=e+"-"+t,i.set(e,t+1)}}let o=O(e,s,a);void 0!==o&&n.push(o)}return n}function M(e,t,n){let r;if(n)if(t.includes(".")){let e,n=t.split("."),a=-1;for(;++a<n.length;){let t=c(n[a])?{type:"Identifier",name:n[a]}:{type:"Literal",value:n[a]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(a&&"Literal"===t.type),optional:!1}:t}(0,i.ok)(e,"always a result"),r=e}else r=c(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return k.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);v(e)}function v(e,t){let n=new I("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=y+"#cannot-handle-mdx-estrees-without-createevaluater",n}function x(e){return"-"+e.toLowerCase()}let w={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var B=n(95155);n(12115);var F=n(4392),H=n(11603);class U{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&G(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),G(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),G(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length))if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);G(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);G(this.left,t.reverse())}}}function G(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function Y(e){let t,n,r,i,a,s,o,l={},c=-1,u=new U(e);for(;++c<u.length;){for(;c in l;)c=l[c];if(t=u.get(c),c&&"chunkFlow"===t[1].type&&"listItemPrefix"===u.get(c-1)[1].type&&((r=0)<(s=t[1]._tokenizer.events).length&&"lineEndingBlank"===s[r][1].type&&(r+=2),r<s.length&&"content"===s[r][1].type))for(;++r<s.length&&"content"!==s[r][1].type;)"chunkText"===s[r][1].type&&(s[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(l,function(e,t){let n,r,i=e.get(t)[1],a=e.get(t)[2],s=t-1,o=[],l=i._tokenizer;!l&&(l=a.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(l._contentTypeTextTrailing=!0));let c=l.events,u=[],h={},d=-1,p=i,f=0,m=0,E=[0];for(;p;){for(;e.get(++s)[1]!==p;);o.push(s),!p._tokenizer&&(n=a.sliceStream(p),p.next||n.push(null),r&&l.defineSkip(p.start),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(n),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),r=p,p=p.next}for(p=i;++d<c.length;)"exit"===c[d][0]&&"enter"===c[d-1][0]&&c[d][1].type===c[d-1][1].type&&c[d][1].start.line!==c[d][1].end.line&&(m=d+1,E.push(m),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(l.events=[],p?(p._tokenizer=void 0,p.previous=void 0):E.pop(),d=E.length;d--;){let t=c.slice(E[d],E[d+1]),n=o.pop();u.push([n,n+t.length-1]),e.splice(n,2,t)}for(u.reverse(),d=-1;++d<u.length;)h[f+u[d][0]]=f+u[d][1],f+=u[d][1]-u[d][0]-1;return h}(u,c)),c=l[c],o=!0);else if(t[1]._container){for(r=c,n=void 0;r--;)if("lineEnding"===(i=u.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(u.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...u.get(n)[1].start},(a=u.slice(n,c)).unshift(t),u.splice(n,c-n+1,a))}}return(0,H.m)(e,0,Number.POSITIVE_INFINITY,u.slice(0)),!o}var z=n(69381),V=n(94581),q=n(12556);let W={tokenize:function(e){let t,n=e.attempt(this.parser.constructs.contentInitial,function(t){return null===t?void e.consume(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,V.N)(e,n,"linePrefix"))},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return(0,q.HP)(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},Q={tokenize:function(e){let t,n,r,i=this,a=[],s=0;return o;function o(t){if(s<a.length){let n=a[s];return i.containerState=n[1],e.attempt(n[0].continuation,l,c)(t)}return c(t)}function l(e){if(s++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&T();let r=i.events.length,a=r;for(;a--;)if("exit"===i.events[a][0]&&"chunkFlow"===i.events[a][1].type){n=i.events[a][1].end;break}E(s);let o=r;for(;o<i.events.length;)i.events[o][1].end={...n},o++;return(0,H.m)(i.events,a+1,0,i.events.slice(r)),i.events.length=o,c(e)}return o(e)}function c(n){if(s===a.length){if(!t)return d(n);if(t.currentConstruct&&t.currentConstruct.concrete)return f(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(j,u,h)(n)}function u(e){return t&&T(),E(s),d(e)}function h(e){return i.parser.lazy[i.now().line]=s!==a.length,r=i.now().offset,f(e)}function d(t){return i.containerState={},e.attempt(j,p,f)(t)}function p(e){return s++,a.push([i.currentConstruct,i.containerState]),d(e)}function f(r){if(null===r){t&&T(),E(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),E(0),e.consume(n);return}return(0,q.HP)(n)?(e.consume(n),m(e.exit("chunkFlow")),s=0,i.interrupt=void 0,o):(e.consume(n),t)}(r)}function m(e,a){let o=i.sliceStream(e);if(a&&o.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(o),i.parser.lazy[e.start.line]){let e,n,a=t.events.length;for(;a--;)if(t.events[a][1].start.offset<r&&(!t.events[a][1].end||t.events[a][1].end.offset>r))return;let o=i.events.length,l=o;for(;l--;)if("exit"===i.events[l][0]&&"chunkFlow"===i.events[l][1].type){if(e){n=i.events[l][1].end;break}e=!0}for(E(s),a=o;a<i.events.length;)i.events[a][1].end={...n},a++;(0,H.m)(i.events,l+1,0,i.events.slice(o)),i.events.length=a}}function E(t){let n=a.length;for(;n-- >t;){let t=a[n];i.containerState=t[1],t[0].exit.call(i,e)}a.length=t}function T(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},j={tokenize:function(e,t,n){return(0,V.N)(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var K=n(95333);let X={resolve:function(e){return Y(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):(0,q.HP)(t)?e.check(J,a,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function a(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},J={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,V.N)(e,i,"linePrefix")};function i(i){if(null===i||(0,q.HP)(i))return n(i);let a=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&a&&"linePrefix"===a[1].type&&a[2].sliceSerialize(a[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},Z={tokenize:function(e){let t=this,n=e.attempt(K.B,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n)},e.attempt(this.parser.constructs.flowInitial,r,(0,V.N)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(X,r)),"linePrefix")));return n;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n)}}},$={resolveAll:er()},ee=en("string"),et=en("text");function en(e){return{resolveAll:er("text"===e?ei:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,a,s);return a;function a(e){return l(e)?i(e):s(e)}function s(e){return null===e?void t.consume(e):(t.enter("data"),t.consume(e),o)}function o(e){return l(e)?(t.exit("data"),i(e)):(t.consume(e),o)}function l(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function er(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function ei(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r,i=e[n-1][1],a=t.sliceStream(i),s=a.length,o=-1,l=0;for(;s--;){let e=a[s];if("string"==typeof e){for(o=e.length;32===e.charCodeAt(o-1);)l++,o--;if(o)break;o=-1}else if(-2===e)r=!0,l++;else if(-1===e);else{s++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(l=0),l){let a={type:n===e.length||r||l<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:s?o:i.start._bufferIndex+o,_index:i.start._index+s,line:i.end.line,column:i.end.column-l,offset:i.end.offset-l},end:{...i.end}};i.end={...a.start},i.start.offset===i.end.offset?Object.assign(i,a):(e.splice(n,0,["enter",a,t],["exit",a,t]),n+=2)}n++}return e}let ea={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(a){var s;return e.enter("thematicBreak"),r=s=a,function a(s){return s===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),(0,q.On)(n)?(0,V.N)(e,a,"whitespace")(n):a(n))}(s)):i>=3&&(null===s||(0,q.HP)(s))?(e.exit("thematicBreak"),t(s)):n(s)}(s)}}},es={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(K.B,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,V.N)(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!(0,q.On)(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(el,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,V.N)(e,e.attempt(es,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],a=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,s=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:(0,q.BM)(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(ea,n,o)(t):o(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return(0,q.BM)(i)&&++s<10?(e.consume(i),t):(!r.interrupt||s<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),o(i)):n(i)}(t)}return n(t)};function o(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(K.B,r.interrupt?n:l,e.attempt(eo,u,c))}function l(e){return r.containerState.initialBlankLine=!0,a++,u(e)}function c(t){return(0,q.On)(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),u):n(t)}function u(n){return r.containerState.size=a+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},eo={partial:!0,tokenize:function(e,t,n){let r=this;return(0,V.N)(e,function(e){let i=r.events[r.events.length-1];return!(0,q.On)(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},el={partial:!0,tokenize:function(e,t,n){let r=this;return(0,V.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},ec={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return(0,q.On)(t)?(0,V.N)(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(ec,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return(0,q.On)(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function eu(e,t,n,r,i,a,s,o,l){let c=l||Number.POSITIVE_INFINITY,u=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(a),e.consume(t),e.exit(a),h):null===t||32===t||41===t||(0,q.JQ)(t)?n(t):(e.enter(r),e.enter(s),e.enter(o),e.enter("chunkString",{contentType:"string"}),f(t))};function h(n){return 62===n?(e.enter(a),e.consume(n),e.exit(a),e.exit(i),e.exit(r),t):(e.enter(o),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(o),h(t)):null===t||60===t||(0,q.HP)(t)?n(t):(e.consume(t),92===t?p:d)}function p(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function f(i){return!u&&(null===i||41===i||(0,q.Ee)(i))?(e.exit("chunkString"),e.exit(o),e.exit(s),e.exit(r),t(i)):u<c&&40===i?(e.consume(i),u++,f):41===i?(e.consume(i),u--,f):null===i||32===i||40===i||(0,q.JQ)(i)?n(i):(e.consume(i),92===i?m:f)}function m(t){return 40===t||41===t||92===t?(e.consume(t),f):f(t)}}function eh(e,t,n,r,i,a){let s,o=this,l=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(a),c};function c(h){return l>999||null===h||91===h||93===h&&!s||94===h&&!l&&"_hiddenFootnoteSupport"in o.parser.constructs?n(h):93===h?(e.exit(a),e.enter(i),e.consume(h),e.exit(i),e.exit(r),t):(0,q.HP)(h)?(e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),c):(e.enter("chunkString",{contentType:"string"}),u(h))}function u(t){return null===t||91===t||93===t||(0,q.HP)(t)||l++>999?(e.exit("chunkString"),c(t)):(e.consume(t),s||(s=!(0,q.On)(t)),92===t?h:u)}function h(t){return 91===t||92===t||93===t?(e.consume(t),l++,u):u(t)}}function ed(e,t,n,r,i,a){let s;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),s=40===t?41:t,o):n(t)};function o(n){return n===s?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(a),l(n))}function l(t){return t===s?(e.exit(a),o(s)):null===t?n(t):(0,q.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,V.N)(e,l,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),c(t))}function c(t){return t===s||null===t||(0,q.HP)(t)?(e.exit("chunkString"),l(t)):(e.consume(t),92===t?u:c)}function u(t){return t===s||92===t?(e.consume(t),c):c(t)}}function ep(e,t){let n;return function r(i){return(0,q.HP)(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):(0,q.On)(i)?(0,V.N)(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}var ef=n(33386);let em={partial:!0,tokenize:function(e,t,n){return function(t){return(0,q.Ee)(t)?ep(e,r)(t):n(t)};function r(t){return ed(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return(0,q.On)(t)?(0,V.N)(e,a,"whitespace")(t):a(t)}function a(e){return null===e||(0,q.HP)(e)?t(e):n(e)}}},eE={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),(0,V.N)(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?a(n):(0,q.HP)(n)?e.attempt(eT,t,a)(n):(e.enter("codeFlowValue"),function n(r){return null===r||(0,q.HP)(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function a(n){return e.exit("codeIndented"),t(n)}}},eT={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):(0,q.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):(0,V.N)(e,a,"linePrefix",5)(t)}function a(e){let a=r.events[r.events.length-1];return a&&"linePrefix"===a[1].type&&a[2].sliceSerialize(a[1],!0).length>=4?t(e):(0,q.HP)(e)?i(e):n(e)}}},eg={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,a=e.length;for(;a--;)if("enter"===e[a][0]){if("content"===e[a][1].type){n=a;break}"paragraph"===e[a][1].type&&(r=a)}else"content"===e[a][1].type&&e.splice(a,1),i||"definition"!==e[a][1].type||(i=a);let s={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",s,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=s,e.push(["exit",s,t]),e},tokenize:function(e,t,n){let r,i=this;return function(t){var s;let o,l=i.events.length;for(;l--;)if("lineEnding"!==i.events[l][1].type&&"linePrefix"!==i.events[l][1].type&&"content"!==i.events[l][1].type){o="paragraph"===i.events[l][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||o)?(e.enter("setextHeadingLine"),r=t,s=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),(0,q.On)(n)?(0,V.N)(e,a,"lineSuffix")(n):a(n))}(s)):n(t)};function a(r){return null===r||(0,q.HP)(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},eA=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],e_=["pre","script","style","textarea"],eS={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(K.B,t,n)}}},eI={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return(0,q.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},ek={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},eC={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r,i=this,a={partial:!0,tokenize:function(e,t,n){let a=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),s};function s(t){return e.enter("codeFencedFence"),(0,q.On)(t)?(0,V.N)(e,l,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):l(t)}function l(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(a++,e.consume(i),t):a>=o?(e.exit("codeFencedFenceSequence"),(0,q.On)(i)?(0,V.N)(e,c,"whitespace")(i):c(i)):n(i)}(t)):n(t)}function c(r){return null===r||(0,q.HP)(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},s=0,o=0;return function(t){var a=t;let c=i.events[i.events.length-1];return s=c&&"linePrefix"===c[1].type?c[2].sliceSerialize(c[1],!0).length:0,r=a,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(o++,e.consume(i),t):o<3?n(i):(e.exit("codeFencedFenceSequence"),(0,q.On)(i)?(0,V.N)(e,l,"whitespace")(i):l(i))}(a)};function l(a){return null===a||(0,q.HP)(a)?(e.exit("codeFencedFence"),i.interrupt?t(a):e.check(ek,u,f)(a)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,q.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),l(i)):(0,q.On)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,V.N)(e,c,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(a))}function c(t){return null===t||(0,q.HP)(t)?l(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,q.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),l(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function u(t){return e.attempt(a,f,h)(t)}function h(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),d}function d(t){return s>0&&(0,q.On)(t)?(0,V.N)(e,p,"linePrefix",s+1)(t):p(t)}function p(t){return null===t||(0,q.HP)(t)?e.check(ek,u,f)(t):(e.enter("codeFlowValue"),function t(n){return null===n||(0,q.HP)(n)?(e.exit("codeFlowValue"),p(n)):(e.consume(n),t)}(t))}function f(n){return e.exit("codeFenced"),t(n)}}},eN=document.createElement("i");function eD(e){let t="&"+e+";";eN.innerHTML=t;let n=eN.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}let eb={name:"characterReference",tokenize:function(e,t,n){let r,i,a=this,s=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),o};function o(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),l):(e.enter("characterReferenceValue"),r=31,i=q.lV,c(t))}function l(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=q.ok,c):(e.enter("characterReferenceValue"),r=7,i=q.BM,c(t))}function c(o){if(59===o&&s){let r=e.exit("characterReferenceValue");return i!==q.lV||eD(a.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(o),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(o)}return i(o)&&s++<r?(e.consume(o),c):n(o)}}},ey={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return(0,q.ol)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},eO={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,V.N)(e,t,"linePrefix")}}};var eR=n(91877);let eL={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&(0,H.m)(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,a,s=e.length,o=0;for(;s--;)if(n=e[s][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[s][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[s][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=s,"labelLink"!==n.type)){o=2;break}}else"labelEnd"===n.type&&(i=s);let l={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},u={type:"labelText",start:{...e[r+o+2][1].end},end:{...e[i-2][1].start}};return a=[["enter",l,t],["enter",c,t]],a=(0,H.V)(a,e.slice(r+1,r+o+3)),a=(0,H.V)(a,[["enter",u,t]]),a=(0,H.V)(a,(0,eR.W)(t.parser.constructs.insideSpan.null,e.slice(r+o+4,i-3),t)),a=(0,H.V)(a,[["exit",u,t],e[i-2],e[i-1],["exit",c,t]]),a=(0,H.V)(a,e.slice(i+1)),a=(0,H.V)(a,[["exit",l,t]]),(0,H.m)(e,r,e.length,a),e},tokenize:function(e,t,n){let r,i,a=this,s=a.events.length;for(;s--;)if(("labelImage"===a.events[s][1].type||"labelLink"===a.events[s][1].type)&&!a.events[s][1]._balanced){r=a.events[s][1];break}return function(t){return r?r._inactive?u(t):(i=a.parser.defined.includes((0,ef.B)(a.sliceSerialize({start:r.end,end:a.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),o):n(t)};function o(t){return 40===t?e.attempt(eP,c,i?c:u)(t):91===t?e.attempt(eM,c,i?l:u)(t):i?c(t):u(t)}function l(t){return e.attempt(ev,c,u)(t)}function c(e){return t(e)}function u(e){return r._balanced=!0,n(e)}}},eP={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return(0,q.Ee)(t)?ep(e,i)(t):i(t)}function i(t){return 41===t?c(t):eu(e,a,s,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function a(t){return(0,q.Ee)(t)?ep(e,o)(t):c(t)}function s(e){return n(e)}function o(t){return 34===t||39===t||40===t?ed(e,l,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):c(t)}function l(t){return(0,q.Ee)(t)?ep(e,c)(t):c(t)}function c(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},eM={tokenize:function(e,t,n){let r=this;return function(t){return eh.call(r,e,i,a,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes((0,ef.B)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function a(e){return n(e)}}},ev={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},ex={name:"labelStartImage",resolveAll:eL.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),a):n(t)}function a(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};var ew=n(49535);let eB={name:"attention",resolveAll:function(e,t){let n,r,i,a,s,o,l,c,u=-1;for(;++u<e.length;)if("enter"===e[u][0]&&"attentionSequence"===e[u][1].type&&e[u][1]._close){for(n=u;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[u][1]).charCodeAt(0)){if((e[n][1]._close||e[u][1]._open)&&(e[u][1].end.offset-e[u][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[u][1].end.offset-e[u][1].start.offset)%3))continue;o=e[n][1].end.offset-e[n][1].start.offset>1&&e[u][1].end.offset-e[u][1].start.offset>1?2:1;let h={...e[n][1].end},d={...e[u][1].start};eF(h,-o),eF(d,o),a={type:o>1?"strongSequence":"emphasisSequence",start:h,end:{...e[n][1].end}},s={type:o>1?"strongSequence":"emphasisSequence",start:{...e[u][1].start},end:d},i={type:o>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[u][1].start}},r={type:o>1?"strong":"emphasis",start:{...a.start},end:{...s.end}},e[n][1].end={...a.start},e[u][1].start={...s.end},l=[],e[n][1].end.offset-e[n][1].start.offset&&(l=(0,H.V)(l,[["enter",e[n][1],t],["exit",e[n][1],t]])),l=(0,H.V)(l,[["enter",r,t],["enter",a,t],["exit",a,t],["enter",i,t]]),l=(0,H.V)(l,(0,eR.W)(t.parser.constructs.insideSpan.null,e.slice(n+1,u),t)),l=(0,H.V)(l,[["exit",i,t],["enter",s,t],["exit",s,t],["exit",r,t]]),e[u][1].end.offset-e[u][1].start.offset?(c=2,l=(0,H.V)(l,[["enter",e[u][1],t],["exit",e[u][1],t]])):c=0,(0,H.m)(e,n-1,u-n+3,l),u=n+l.length-c-2;break}}for(u=-1;++u<e.length;)"attentionSequence"===e[u][1].type&&(e[u][1].type="data");return e},tokenize:function(e,t){let n,r=this.parser.constructs.attentionMarkers.null,i=this.previous,a=(0,ew.S)(i);return function(s){return n=s,e.enter("attentionSequence"),function s(o){if(o===n)return e.consume(o),s;let l=e.exit("attentionSequence"),c=(0,ew.S)(o),u=!c||2===c&&a||r.includes(o),h=!a||2===a&&c||r.includes(i);return l._open=!!(42===n?u:u&&(a||!h)),l._close=!!(42===n?h:h&&(c||!u)),t(o)}(s)}}};function eF(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let eH={name:"labelStartLink",resolveAll:eL.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},eU={42:es,43:es,45:es,48:es,49:es,50:es,51:es,52:es,53:es,54:es,55:es,56:es,57:es,62:ec},eG={91:{name:"definition",tokenize:function(e,t,n){let r,i=this;return function(t){var r;return e.enter("definition"),r=t,eh.call(i,e,a,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function a(t){return(r=(0,ef.B)(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),s):n(t)}function s(t){return(0,q.Ee)(t)?ep(e,o)(t):o(t)}function o(t){return eu(e,l,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function l(t){return e.attempt(em,c,c)(t)}function c(t){return(0,q.On)(t)?(0,V.N)(e,u,"whitespace")(t):u(t)}function u(a){return null===a||(0,q.HP)(a)?(e.exit("definition"),i.parser.defined.push(r),t(a)):n(a)}}}},eY={[-2]:eE,[-1]:eE,32:eE},ez={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,a=3;return"whitespace"===e[3][1].type&&(a+=2),i-2>a&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(a===i-1||i-4>a&&"whitespace"===e[i-2][1].type)&&(i-=a+1===i?2:4),i>a&&(n={type:"atxHeadingText",start:e[a][1].start,end:e[i][1].end},r={type:"chunkText",start:e[a][1].start,end:e[i][1].end,contentType:"text"},(0,H.m)(e,a,i-a+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){var a;return e.enter("atxHeading"),a=i,e.enter("atxHeadingSequence"),function i(a){return 35===a&&r++<6?(e.consume(a),i):null===a||(0,q.Ee)(a)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||(0,q.HP)(r)?(e.exit("atxHeading"),t(r)):(0,q.On)(r)?(0,V.N)(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||(0,q.Ee)(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(a)):n(a)}(a)}}},42:ea,45:[eg,ea],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,a,s,o,l=this;return function(t){var n;return n=t,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),c};function c(s){return 33===s?(e.consume(s),u):47===s?(e.consume(s),i=!0,p):63===s?(e.consume(s),r=3,l.interrupt?t:P):(0,q.CW)(s)?(e.consume(s),a=String.fromCharCode(s),f):n(s)}function u(i){return 45===i?(e.consume(i),r=2,h):91===i?(e.consume(i),r=5,s=0,d):(0,q.CW)(i)?(e.consume(i),r=4,l.interrupt?t:P):n(i)}function h(r){return 45===r?(e.consume(r),l.interrupt?t:P):n(r)}function d(r){let i="CDATA[";return r===i.charCodeAt(s++)?(e.consume(r),s===i.length)?l.interrupt?t:C:d:n(r)}function p(t){return(0,q.CW)(t)?(e.consume(t),a=String.fromCharCode(t),f):n(t)}function f(s){if(null===s||47===s||62===s||(0,q.Ee)(s)){let o=47===s,c=a.toLowerCase();return!o&&!i&&e_.includes(c)?(r=1,l.interrupt?t(s):C(s)):eA.includes(a.toLowerCase())?(r=6,o)?(e.consume(s),m):l.interrupt?t(s):C(s):(r=7,l.interrupt&&!l.parser.lazy[l.now().line]?n(s):i?function t(n){return(0,q.On)(n)?(e.consume(n),t):I(n)}(s):E(s))}return 45===s||(0,q.lV)(s)?(e.consume(s),a+=String.fromCharCode(s),f):n(s)}function m(r){return 62===r?(e.consume(r),l.interrupt?t:C):n(r)}function E(t){return 47===t?(e.consume(t),I):58===t||95===t||(0,q.CW)(t)?(e.consume(t),T):(0,q.On)(t)?(e.consume(t),E):I(t)}function T(t){return 45===t||46===t||58===t||95===t||(0,q.lV)(t)?(e.consume(t),T):g(t)}function g(t){return 61===t?(e.consume(t),A):(0,q.On)(t)?(e.consume(t),g):E(t)}function A(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),o=t,_):(0,q.On)(t)?(e.consume(t),A):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||(0,q.Ee)(n)?g(n):(e.consume(n),t)}(t)}function _(t){return t===o?(e.consume(t),o=null,S):null===t||(0,q.HP)(t)?n(t):(e.consume(t),_)}function S(e){return 47===e||62===e||(0,q.On)(e)?E(e):n(e)}function I(t){return 62===t?(e.consume(t),k):n(t)}function k(t){return null===t||(0,q.HP)(t)?C(t):(0,q.On)(t)?(e.consume(t),k):n(t)}function C(t){return 45===t&&2===r?(e.consume(t),y):60===t&&1===r?(e.consume(t),O):62===t&&4===r?(e.consume(t),M):63===t&&3===r?(e.consume(t),P):93===t&&5===r?(e.consume(t),L):(0,q.HP)(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(eS,v,N)(t)):null===t||(0,q.HP)(t)?(e.exit("htmlFlowData"),N(t)):(e.consume(t),C)}function N(t){return e.check(eI,D,v)(t)}function D(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),b}function b(t){return null===t||(0,q.HP)(t)?N(t):(e.enter("htmlFlowData"),C(t))}function y(t){return 45===t?(e.consume(t),P):C(t)}function O(t){return 47===t?(e.consume(t),a="",R):C(t)}function R(t){if(62===t){let n=a.toLowerCase();return e_.includes(n)?(e.consume(t),M):C(t)}return(0,q.CW)(t)&&a.length<8?(e.consume(t),a+=String.fromCharCode(t),R):C(t)}function L(t){return 93===t?(e.consume(t),P):C(t)}function P(t){return 62===t?(e.consume(t),M):45===t&&2===r?(e.consume(t),P):C(t)}function M(t){return null===t||(0,q.HP)(t)?(e.exit("htmlFlowData"),v(t)):(e.consume(t),M)}function v(n){return e.exit("htmlFlow"),t(n)}}},61:eg,95:ea,96:eC,126:eC},eV={38:eb,92:ey},eq={[-5]:eO,[-4]:eO,[-3]:eO,33:ex,38:eb,42:eB,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return(0,q.CW)(t)?(e.consume(t),a):64===t?n(t):o(t)}function a(t){return 43===t||45===t||46===t||(0,q.lV)(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,s):(43===n||45===n||46===n||(0,q.lV)(n))&&r++<32?(e.consume(n),t):(r=0,o(n))}(t)):o(t)}function s(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||(0,q.JQ)(r)?n(r):(e.consume(r),s)}function o(t){return 64===t?(e.consume(t),l):(0,q.cx)(t)?(e.consume(t),o):n(t)}function l(i){return(0,q.lV)(i)?function i(a){return 46===a?(e.consume(a),r=0,l):62===a?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(a),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(a){if((45===a||(0,q.lV)(a))&&r++<63){let n=45===a?t:i;return e.consume(a),n}return n(a)}(a)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,a,s=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),o};function o(t){return 33===t?(e.consume(t),l):47===t?(e.consume(t),_):63===t?(e.consume(t),g):(0,q.CW)(t)?(e.consume(t),I):n(t)}function l(t){return 45===t?(e.consume(t),c):91===t?(e.consume(t),i=0,p):(0,q.CW)(t)?(e.consume(t),T):n(t)}function c(t){return 45===t?(e.consume(t),d):n(t)}function u(t){return null===t?n(t):45===t?(e.consume(t),h):(0,q.HP)(t)?(a=u,R(t)):(e.consume(t),u)}function h(t){return 45===t?(e.consume(t),d):u(t)}function d(e){return 62===e?O(e):45===e?h(e):u(e)}function p(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?f:p):n(t)}function f(t){return null===t?n(t):93===t?(e.consume(t),m):(0,q.HP)(t)?(a=f,R(t)):(e.consume(t),f)}function m(t){return 93===t?(e.consume(t),E):f(t)}function E(t){return 62===t?O(t):93===t?(e.consume(t),E):f(t)}function T(t){return null===t||62===t?O(t):(0,q.HP)(t)?(a=T,R(t)):(e.consume(t),T)}function g(t){return null===t?n(t):63===t?(e.consume(t),A):(0,q.HP)(t)?(a=g,R(t)):(e.consume(t),g)}function A(e){return 62===e?O(e):g(e)}function _(t){return(0,q.CW)(t)?(e.consume(t),S):n(t)}function S(t){return 45===t||(0,q.lV)(t)?(e.consume(t),S):function t(n){return(0,q.HP)(n)?(a=t,R(n)):(0,q.On)(n)?(e.consume(n),t):O(n)}(t)}function I(t){return 45===t||(0,q.lV)(t)?(e.consume(t),I):47===t||62===t||(0,q.Ee)(t)?k(t):n(t)}function k(t){return 47===t?(e.consume(t),O):58===t||95===t||(0,q.CW)(t)?(e.consume(t),C):(0,q.HP)(t)?(a=k,R(t)):(0,q.On)(t)?(e.consume(t),k):O(t)}function C(t){return 45===t||46===t||58===t||95===t||(0,q.lV)(t)?(e.consume(t),C):function t(n){return 61===n?(e.consume(n),N):(0,q.HP)(n)?(a=t,R(n)):(0,q.On)(n)?(e.consume(n),t):k(n)}(t)}function N(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,D):(0,q.HP)(t)?(a=N,R(t)):(0,q.On)(t)?(e.consume(t),N):(e.consume(t),b)}function D(t){return t===r?(e.consume(t),r=void 0,y):null===t?n(t):(0,q.HP)(t)?(a=D,R(t)):(e.consume(t),D)}function b(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||(0,q.Ee)(t)?k(t):(e.consume(t),b)}function y(e){return 47===e||62===e||(0,q.Ee)(e)?k(e):n(e)}function O(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function R(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),L}function L(t){return(0,q.On)(t)?(0,V.N)(e,P,"linePrefix",s.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):P(t)}function P(t){return e.enter("htmlTextData"),a(t)}}}],91:eH,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return(0,q.HP)(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},ey],93:eL,95:eB,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,a=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),a++,t):(e.exit("codeTextSequence"),s(n))}(t)};function s(l){return null===l?n(l):32===l?(e.enter("space"),e.consume(l),e.exit("space"),s):96===l?(i=e.enter("codeTextSequence"),r=0,function n(s){return 96===s?(e.consume(s),r++,n):r===a?(e.exit("codeTextSequence"),e.exit("codeText"),t(s)):(i.type="codeTextData",o(s))}(l)):(0,q.HP)(l)?(e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),s):(e.enter("codeTextData"),o(l))}function o(t){return null===t||32===t||96===t||(0,q.HP)(t)?(e.exit("codeTextData"),s(t)):(e.consume(t),o)}}}},eW={null:[eB,$]},eQ={null:[42,95]},ej={null:[]},eK=/[\0\t\n\r]/g;function eX(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let eJ=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function eZ(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return eX(n.slice(t?2:1),t?16:10)}return eD(n)||e}let e$={}.hasOwnProperty;function e0(e){return{line:e.line,column:e.column,offset:e.offset}}function e1(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+g({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+g({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+g({start:t.start,end:t.end})+") is still open")}function e3(e){let t=this;t.parser=function(n){var i,a;let s,o,l,c;return"string"!=typeof(i={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(a=i,i=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(T),autolinkProtocol:c,autolinkEmail:c,atxHeading:r(f),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:c,characterReference:c,codeFenced:r(p),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:r(p,i),codeText:r(function(){return{type:"inlineCode",value:""}},i),codeTextData:c,data:c,codeFlowValue:c,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(E,i),htmlFlowData:c,htmlText:r(E,i),htmlTextData:c,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:i,link:r(T),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(A,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(A),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:r(f),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:s(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:s(),autolinkEmail:function(e){u.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){u.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:s(),characterEscapeValue:u,characterReferenceMarkerHexadecimal:d,characterReferenceMarkerNumeric:d,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=eX(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=eD(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=e0(e.end)},codeFenced:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:u,codeIndented:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:u,data:u,definition:s(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,ef.B)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:s(),hardBreakEscape:s(h),hardBreakTrailing:s(h),htmlFlow:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:u,htmlText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:u,image:s(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(eJ,eZ),n.identifier=(0,ef.B)(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=e0(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(c.call(this,e),u.call(this,e))},link:s(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:s(),listOrdered:s(),listUnordered:s(),paragraph:s(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,ef.B)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:s(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:s(),thematicBreak:s()}};!function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(e$.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},s={stack:[r],tokenStack:[],config:t,enter:a,exit:o,buffer:i,resume:l,data:n},c=[],u=-1;for(;++u<e.length;)("listOrdered"===e[u][1].type||"listUnordered"===e[u][1].type)&&("enter"===e[u][0]?c.push(u):u=function(e,t,n){let r,i,a,s,o=t-1,l=-1,c=!1;for(;++o<=n;){let t=e[o];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?l++:l--,s=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||s||l||a||(a=o),s=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:s=void 0}if(!l&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===l&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let s=o;for(i=void 0;s--;){let t=e[s];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",c=!0),t[1].type="lineEnding",i=s}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}a&&(!i||a<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||o,0,["exit",r,t[2]]),o++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(o,0,["enter",i,t[2]]),o++,n++,a=void 0,s=!0}}}return e[t][1]._spread=c,n}(e,c.pop(),u));for(u=-1;++u<e.length;){let n=t[e[u][0]];e$.call(n,e[u][1].type)&&n[e[u][1].type].call(Object.assign({sliceSerialize:e[u][2].sliceSerialize},s),e[u][1])}if(s.tokenStack.length>0){let e=s.tokenStack[s.tokenStack.length-1];(e[1]||e1).call(s,void 0,e[0])}for(r.position={start:e0(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:e0(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},u=-1;++u<t.transforms.length;)r=t.transforms[u](r)||r;return r};function r(e,t){return function(n){a.call(this,e(n),n),t&&t.call(this,n)}}function i(){this.stack.push({type:"fragment",children:[]})}function a(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:e0(t.start),end:void 0}}function s(e){return function(t){e&&e.call(this,t),o.call(this,t)}}function o(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||e1).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+g({start:e.start,end:e.end})+"): it’s not open");n.position.end=e0(e.end)}function l(){return(0,F.d)(this.stack.pop())}function c(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:e0(e.start),end:void 0},t.push(n)),this.stack.push(n)}function u(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=e0(e.end)}function h(){this.data.atHardBreak=!0}function d(e){this.data.characterReferenceType=e.type}function p(){return{type:"code",lang:null,meta:null,value:""}}function f(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function E(){return{type:"html",value:""}}function T(){return{type:"link",title:null,url:"",children:[]}}function A(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(a)(function(e){for(;!Y(e););return e}((function(e){let t={constructs:(0,z.y)([r,...(e||{}).extensions||[]]),content:n(W),defined:[],document:n(Q),flow:n(Z),lazy:{},string:n(ee),text:n(et)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},a=[],s=[],o=[],l={attempt:f(function(e,t){m(e,t.from)}),check:f(p),consume:function(e){(0,q.HP)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,E()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===s[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=d(),c.events.push(["enter",n,c]),o.push(n),n},exit:function(e){let t=o.pop();return t.end=d(),c.events.push(["exit",t,c]),t},interrupt:f(p,{interrupt:!0})},c={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,E()},events:[],now:d,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let a,s=e[r];if("string"==typeof s)a=s;else switch(s){case -5:a="\r";break;case -4:a="\n";break;case -3:a="\r\n";break;case -2:a=t?" ":"	";break;case -1:if(!t&&n)continue;a=" ";break;default:a=String.fromCharCode(s)}n=-2===s,i.push(a)}return i.join("")}(h(e),t)},sliceStream:h,write:function(e){return(s=(0,H.V)(s,e),function(){let e;for(;r._index<s.length;){let n=s[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),u=u(t)}else u=u(n)}}(),null!==s[s.length-1])?[]:(m(t,0),c.events=(0,eR.W)(a,c.events,c),c.events)}},u=t.tokenize.call(c,l);return t.resolveAll&&a.push(t),c;function h(e){return function(e,t){let n,r=t.start._index,i=t.start._bufferIndex,a=t.end._index,s=t.end._bufferIndex;if(r===a)n=[e[r].slice(i,s)];else{if(n=e.slice(r,a),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}s>0&&n.push(e[a].slice(0,s))}return n}(s,e)}function d(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:a}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:a}}function p(e,t){t.restore()}function f(e,t){return function(n,i,a){var s;let u,h,p,f;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(s=n,function(e){let t=null!==e&&s[e],n=null!==e&&s.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});function m(e){return(u=e,h=0,0===e.length)?a:T(e[h])}function T(e){return function(n){return(f=function(){let e=d(),t=c.previous,n=c.currentConstruct,i=c.events.length,a=Array.from(o);return{from:i,restore:function(){r=e,c.previous=t,c.currentConstruct=n,c.events.length=i,o=a,E()}}}(),p=e,e.partial||(c.currentConstruct=e),e.name&&c.parser.constructs.disable.null.includes(e.name))?A(n):e.tokenize.call(t?Object.assign(Object.create(c),t):c,l,g,A)(n)}}function g(t){return e(p,f),i}function A(e){return(f.restore(),++h<u.length)?T(u[h]):a}}}function m(e,t){e.resolveAll&&!a.includes(e)&&a.push(e),e.resolve&&(0,H.m)(c.events,t,c.events.length-t,e.resolve(c.events.slice(t),c)),e.resolveTo&&(c.events=e.resolveTo(c.events,c))}function E(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(a).document().write((o=1,l="",c=!0,function(e,t,n){let r,i,a,u,h,d=[];for(e=l+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),a=0,l="",c&&(65279===e.charCodeAt(0)&&a++,c=void 0);a<e.length;){if(eK.lastIndex=a,u=(r=eK.exec(e))&&void 0!==r.index?r.index:e.length,h=e.charCodeAt(u),!r){l=e.slice(a);break}if(10===h&&a===u&&s)d.push(-3),s=void 0;else switch(s&&(d.push(-5),s=void 0),a<u&&(d.push(e.slice(a,u)),o+=u-a),h){case 0:d.push(65533),o++;break;case 9:for(i=4*Math.ceil(o/4),d.push(-2);o++<i;)d.push(-1);break;case 10:d.push(-4),o=1;break;default:s=!0,o=1}a=u+1}return n&&(s&&d.push(-5),l&&d.push(l),d.push(null)),d})(n,i,!0))))}}var e2=n(66945);function e5(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let a=e.charCodeAt(n),s="";if(37===a&&(0,q.lV)(e.charCodeAt(n+1))&&(0,q.lV)(e.charCodeAt(n+2)))i=2;else if(a<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(a))||(s=String.fromCharCode(a));else if(a>55295&&a<57344){let t=e.charCodeAt(n+1);a<56320&&t>56319&&t<57344?(s=String.fromCharCode(a,t),i=1):s="�"}else s=String.fromCharCode(a);s&&(t.push(e.slice(r,n),encodeURIComponent(s)),r=n+i+1,s=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function e4(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function e8(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}var e9=n(88428);function e6(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),a=i[0];a&&"text"===a.type?a.value="["+a.value:i.unshift({type:"text",value:"["});let s=i[i.length-1];return s&&"text"===s.type?s.value+=r:i.push({type:"text",value:r}),i}function e7(e){let t=e.spread;return null==t?e.children.length>1:t}function te(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let tt={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),a=e5(i.toLowerCase()),s=e.footnoteOrder.indexOf(i),o=e.footnoteCounts.get(i);void 0===o?(o=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=s+1,o+=1,e.footnoteCounts.set(i,o);let l={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+a,id:r+"fnref-"+a+(o>1?"-"+o:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,l);let c={type:"element",tagName:"sup",properties:{},children:[l]};return e.patch(t,c),e.applyData(t,c)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return e6(e,t);let i={src:e5(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let a={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,a),e.applyData(t,a)},image:function(e,t){let n={src:e5(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return e6(e,t);let i={href:e5(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let a={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,a),e.applyData(t,a)},link:function(e,t){let n={href:e5(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=e7(n[r])}return t}(n):e7(t),a={},s=[];if("boolean"==typeof t.checked){let e,n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),a.className=["task-list-item"]}let o=-1;for(;++o<r.length;){let e=r[o];(i||0!==o||"element"!==e.type||"p"!==e.tagName)&&s.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?s.push(e):s.push(...e.children)}let l=r[r.length-1];l&&(i||"element"!==l.type||"p"!==l.tagName)&&s.push({type:"text",value:"\n"});let c={type:"element",tagName:"li",properties:a,children:s};return e.patch(t,c),e.applyData(t,c)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let a={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,a),e.applyData(t,a)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},a=(0,T.PW)(t.children[1]),s=(0,T.Y)(t.children[t.children.length-1]);a&&s&&(r.position={start:a,end:s}),i.push(r)}let a={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,a),e.applyData(t,a)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",a=n&&"table"===n.type?n.align:void 0,s=a?a.length:t.children.length,o=-1,l=[];for(;++o<s;){let n=t.children[o],r={},s=a?a[o]:void 0;s&&(r.align=s);let c={type:"element",tagName:i,properties:r,children:[]};n&&(c.children=e.all(n),e.patch(n,c),c=e.applyData(n,c)),l.push(c)}let c={type:"element",tagName:"tr",properties:{},children:e.wrap(l,!0)};return e.patch(t,c),e.applyData(t,c)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,a=[];for(;r;)a.push(te(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return a.push(te(t.slice(i),i>0,!1)),a.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:tn,yaml:tn,definition:tn,footnoteDefinition:tn};function tn(){}let tr={}.hasOwnProperty,ti={};function ta(e,t){e.position&&(t.position=(0,T.G1)(e))}function ts(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,(0,e2.Ay)(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function to(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function tl(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function tc(e,t){let n=function(e,t){let n=t||ti,r=new Map,i=new Map,a={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=a.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=tl(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=tl(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:ts,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...tt,...n.handlers},one:function(e,t){let n=e.type,r=a.handlers[n];if(tr.call(a.handlers,n)&&r)return r(a,e,t);if(a.options.passThrough&&a.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=(0,e2.Ay)(n);return r.children=a.all(e),r}return(0,e2.Ay)(e)}return(a.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(tr.call(n,"hProperties")||tr.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(a,e,t)},options:n,patch:ta,wrap:to};return(0,e9.YR)(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),a}(e,t),r=n.one(e,void 0),a=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||e4,r=e.options.footnoteBackLabel||e8,i=e.options.footnoteLabel||"Footnotes",a=e.options.footnoteLabelTagName||"h2",s=e.options.footnoteLabelProperties||{className:["sr-only"]},o=[],l=-1;for(;++l<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[l]);if(!i)continue;let a=e.all(i),s=String(i.identifier).toUpperCase(),c=e5(s.toLowerCase()),u=0,h=[],d=e.footnoteCounts.get(s);for(;void 0!==d&&++u<=d;){h.length>0&&h.push({type:"text",value:" "});let e="string"==typeof n?n:n(l,u);"string"==typeof e&&(e={type:"text",value:e}),h.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+c+(u>1?"-"+u:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(l,u),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let p=a[a.length-1];if(p&&"element"===p.type&&"p"===p.tagName){let e=p.children[p.children.length-1];e&&"text"===e.type?e.value+=" ":p.children.push({type:"text",value:" "}),p.children.push(...h)}else a.push(...h);let f={type:"element",tagName:"li",properties:{id:t+"fn-"+c},children:e.wrap(a,!0)};e.patch(i,f),o.push(f)}if(0!==o.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:a,properties:{...(0,e2.Ay)(s),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(o,!0)},{type:"text",value:"\n"}]}}(n),s=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return a&&((0,i.ok)("children"in s),s.children.push({type:"text",value:"\n"},a)),s}function tu(e,t){return e&&"run"in e?async function(n,r){let i=tc(n,{file:r,...t});await e.run(i,r)}:function(n,r){return tc(n,{file:r,...e||t})}}function th(e){if(e)throw e}var td=n(53360);function tp(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let tf={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');tm(e);let r=0,i=-1,a=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;a--;)if(47===e.codePointAt(a)){if(n){r=a+1;break}}else i<0&&(n=!0,i=a+1);return i<0?"":e.slice(r,i)}if(t===e)return"";let s=-1,o=t.length-1;for(;a--;)if(47===e.codePointAt(a)){if(n){r=a+1;break}}else s<0&&(n=!0,s=a+1),o>-1&&(e.codePointAt(a)===t.codePointAt(o--)?o<0&&(i=a):(o=-1,i=s));return r===i?i=s:i<0&&(i=e.length),e.slice(r,i)},dirname:function(e){let t;if(tm(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){let t;tm(e);let n=e.length,r=-1,i=0,a=-1,s=0;for(;n--;){let o=e.codePointAt(n);if(47===o){if(t){i=n+1;break}continue}r<0&&(t=!0,r=n+1),46===o?a<0?a=n:1!==s&&(s=1):a>-1&&(s=-1)}return a<0||r<0||0===s||1===s&&a===r-1&&a===i+1?"":e.slice(a,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)tm(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){tm(e);let t=47===e.codePointAt(0),n=function(e,t){let n,r,i="",a=0,s=-1,o=0,l=-1;for(;++l<=e.length;){if(l<e.length)n=e.codePointAt(l);else if(47===n)break;else n=47;if(47===n){if(s===l-1||1===o);else if(s!==l-1&&2===o){if(i.length<2||2!==a||46!==i.codePointAt(i.length-1)||46!==i.codePointAt(i.length-2)){if(i.length>2){if((r=i.lastIndexOf("/"))!==i.length-1){r<0?(i="",a=0):a=(i=i.slice(0,r)).length-1-i.lastIndexOf("/"),s=l,o=0;continue}}else if(i.length>0){i="",a=0,s=l,o=0;continue}}t&&(i=i.length>0?i+"/..":"..",a=2)}else i.length>0?i+="/"+e.slice(s+1,l):i=e.slice(s+1,l),a=l-s-1;s=l,o=0}else 46===n&&o>-1?o++:o=-1}return i}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},sep:"/"};function tm(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let tE={cwd:function(){return"/"}};function tT(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let tg=["history","path","basename","stem","extname","dirname"];class tA{constructor(e){let t,n;t=e?tT(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":tE.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<tg.length;){let e=tg[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)tg.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?tf.basename(this.path):void 0}set basename(e){tS(e,"basename"),t_(e,"basename"),this.path=tf.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?tf.dirname(this.path):void 0}set dirname(e){tI(this.basename,"dirname"),this.path=tf.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?tf.extname(this.path):void 0}set extname(e){if(t_(e,"extname"),tI(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=tf.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){tT(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!tT(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){let e=t.codePointAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),tS(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?tf.basename(this.path,this.extname):void 0}set stem(e){tS(e,"stem"),t_(e,"stem"),this.path=tf.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new I(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function t_(e,t){if(e&&e.includes(tf.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+tf.sep+"`")}function tS(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function tI(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let tk=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},tC={}.hasOwnProperty;class tN extends tk{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);!function i(a,...s){let o=e[++n],l=-1;if(a)return void r(a);for(;++l<t.length;)(null===s[l]||void 0===s[l])&&(s[l]=t[l]);t=s,o?(function(e,t){let n;return function(...t){let a,s=e.length>t.length;s&&t.push(r);try{a=e.apply(this,t)}catch(e){if(s&&n)throw e;return r(e)}s||(a&&a.then&&"function"==typeof a.then?a.then(i,r):a instanceof Error?r(a):i(a))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(o,i)(...s):r(null,...s)}(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new tN,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(td(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(tO("data",this.frozen),this.namespace[e]=t,this):tC.call(this.namespace,e)&&this.namespace[e]||void 0:e?(tO("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=tP(e),n=this.parser||this.Parser;return tb("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),tb("process",this.parser||this.Parser),ty("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,a){let s=tP(e),o=n.parse(s);function l(e,n){e||!n?a(e):r?r(n):((0,i.ok)(t,"`done` is defined if `resolve` is not"),t(void 0,n))}n.run(o,s,function(e,t,r){var i,a;if(e||!t||!r)return l(e);let s=n.stringify(t,r);"string"==typeof(i=s)||(a=i)&&"object"==typeof a&&"byteLength"in a&&"byteOffset"in a?r.value=s:r.result=s,l(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),tb("processSync",this.parser||this.Parser),ty("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,th(e),t=r}),tL("processSync","process",n),(0,i.ok)(t,"we either bailed on an error or have a tree"),t}run(e,t,n){tR(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?a(void 0,n):new Promise(a);function a(a,s){(0,i.ok)("function"!=typeof t,"`file` can’t be a `done` anymore, we checked");let o=tP(t);r.run(e,o,function(t,r,o){let l=r||e;t?s(t):a?a(l):((0,i.ok)(n,"`done` is defined if `resolve` is not"),n(void 0,l,o))})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){th(e),n=t,r=!0}),tL("runSync","run",r),(0,i.ok)(n,"we either bailed on an error or have a tree"),n}stringify(e,t){this.freeze();let n=tP(t),r=this.compiler||this.Compiler;return ty("stringify",r),tR(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(tO("use",this.frozen),null==e);else if("function"==typeof e)s(e,t);else if("object"==typeof e)Array.isArray(e)?a(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");a(e.plugins),e.settings&&(r.settings=td(!0,r.settings,e.settings))}function a(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){var n=e[t];if("function"==typeof n)s(n,[]);else if("object"==typeof n)if(Array.isArray(n)){let[e,...t]=n;s(e,t)}else i(n);else throw TypeError("Expected usable value, not `"+n+"`")}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function s(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...a]=t,s=n[i][1];tp(s)&&tp(r)&&(r=td(!0,s,r)),n[i]=[e,r,...a]}}}}let tD=new tN().freeze();function tb(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function ty(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function tO(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function tR(e){if(!tp(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function tL(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function tP(e){var t;return(t=e)&&"object"==typeof t&&"message"in t&&"messages"in t?e:new tA(e)}let tM=[],tv={allowDangerousHtml:!0},tx=/^(https?|ircs?|mailto|xmpp)$/i,tw=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function tB(e){let t=function(e){let t=e.rehypePlugins||tM,n=e.remarkPlugins||tM,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...tv}:tv;return tD().use(e3).use(n).use(tu,r).use(t)}(e),n=function(e){let t=e.children||"",n=new tA;return"string"==typeof t?n.value=t:(0,i.HB)("Unexpected value `"+t+"` for `children` prop, expected `string`"),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,a=t.components,s=t.disallowedElements,o=t.skipHtml,l=t.unwrapDisallowed,c=t.urlTransform||tF;for(let e of tw)Object.hasOwn(t,e.from)&&(0,i.HB)("Unexpected `"+e.from+"` prop, "+(e.to?"use `"+e.to+"` instead":"remove it")+" (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#"+e.id+"> for more info)");return n&&s&&(0,i.HB)("Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other"),t.className&&(e={type:"element",tagName:"div",properties:{className:t.className},children:"root"===e.type?e.children:[e]}),(0,e9.YR)(e,function(e,t,i){if("raw"===e.type&&i&&"number"==typeof t)return o?i.children.splice(t,1):i.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in w)if(Object.hasOwn(w,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=w[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=c(String(n||""),t,e))}}if("element"===e.type){let a=n?!n.includes(e.tagName):!!s&&s.includes(e.tagName);if(!a&&r&&"number"==typeof t&&(a=!r(e,t,i)),a&&i&&"number"==typeof t)return l&&e.children?i.children.splice(t,1,...e.children):i.children.splice(t,1),t}}),function(e,t){var n,r,i,a,s;let o;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let l=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=l,r=t.jsxDEV,o=function(e,t,i,a){let s=Array.isArray(i.children),o=(0,T.PW)(e);return r(t,i,a,s,{columnNumber:o?o.column-1:void 0,fileName:n,lineNumber:o?o.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");i=0,a=t.jsx,s=t.jsxs,o=function(e,t,n,r){let i=Array.isArray(n.children)?s:a;return r?i(t,n,r):i(t,n)}}let c={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:o,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:l,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?d.JW:d.qy,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},u=O(c,e,void 0);return u&&"string"!=typeof u?u:c.create(e,c.Fragment,{children:u||void 0},void 0)}(e,{Fragment:B.Fragment,components:a,ignoreInvalidStyle:!0,jsx:B.jsx,jsxs:B.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function tF(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||tx.test(e.slice(0,t))?e:""}},59739:(e,t,n)=>{"use strict";function r(e){return e.toLowerCase()}n.d(t,{S:()=>r})},60760:(e,t,n)=>{"use strict";n.d(t,{N:()=>g});var r=n(95155),i=n(12115),a=n(90869),s=n(82885),o=n(97494),l=n(80845),c=n(27351),u=n(51508);class h extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,c.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:t,isPresent:n,anchorX:a}=e,s=(0,i.useId)(),o=(0,i.useRef)(null),l=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:c}=(0,i.useContext)(u.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:i,right:u}=l.current;if(n||!o.current||!e||!t)return;o.current.dataset.motionPopId=s;let h=document.createElement("style");return c&&(h.nonce=c),document.head.appendChild(h),h.sheet&&h.sheet.insertRule('\n          [data-motion-pop-id="'.concat(s,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===a?"left: ".concat(i):"right: ".concat(u),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{document.head.contains(h)&&document.head.removeChild(h)}},[n]),(0,r.jsx)(h,{isPresent:n,childRef:o,sizeRef:l,children:i.cloneElement(t,{ref:o})})}let p=e=>{let{children:t,initial:n,isPresent:a,onExitComplete:o,custom:c,presenceAffectsLayout:u,mode:h,anchorX:p}=e,m=(0,s.M)(f),E=(0,i.useId)(),T=!0,g=(0,i.useMemo)(()=>(T=!1,{id:E,initial:n,isPresent:a,custom:c,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;o&&o()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[a,m,o]);return u&&T&&(g={...g}),(0,i.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[a]),i.useEffect(()=>{a||m.size||!o||o()},[a]),"popLayout"===h&&(t=(0,r.jsx)(d,{isPresent:a,anchorX:p,children:t})),(0,r.jsx)(l.t.Provider,{value:g,children:t})};function f(){return new Map}var m=n(32082);let E=e=>e.key||"";function T(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let g=e=>{let{children:t,custom:n,initial:l=!0,onExitComplete:c,presenceAffectsLayout:u=!0,mode:h="sync",propagate:d=!1,anchorX:f="left"}=e,[g,A]=(0,m.xQ)(d),_=(0,i.useMemo)(()=>T(t),[t]),S=d&&!g?[]:_.map(E),I=(0,i.useRef)(!0),k=(0,i.useRef)(_),C=(0,s.M)(()=>new Map),[N,D]=(0,i.useState)(_),[b,y]=(0,i.useState)(_);(0,o.E)(()=>{I.current=!1,k.current=_;for(let e=0;e<b.length;e++){let t=E(b[e]);S.includes(t)?C.delete(t):!0!==C.get(t)&&C.set(t,!1)}},[b,S.length,S.join("-")]);let O=[];if(_!==N){let e=[..._];for(let t=0;t<b.length;t++){let n=b[t],r=E(n);S.includes(r)||(e.splice(t,0,n),O.push(n))}return"wait"===h&&O.length&&(e=O),y(T(e)),D(_),null}let{forceRender:R}=(0,i.useContext)(a.L);return(0,r.jsx)(r.Fragment,{children:b.map(e=>{let t=E(e),i=(!d||!!g)&&(_===b||S.includes(t));return(0,r.jsx)(p,{isPresent:i,initial:(!I.current||!!l)&&void 0,custom:n,presenceAffectsLayout:u,mode:h,onExitComplete:i?void 0:()=>{if(!C.has(t))return;C.set(t,!0);let e=!0;C.forEach(t=>{t||(e=!1)}),e&&(null==R||R(),y(k.current),d&&(null==A||A()),c&&c())},anchorX:f,children:e},t)})})}},65112:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},66945:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>d});let r="object"==typeof self?self:globalThis,i=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),i=a=>{if(e.has(a))return e.get(a);let[s,o]=t[a];switch(s){case 0:case -1:return n(o,a);case 1:{let e=n([],a);for(let t of o)e.push(i(t));return e}case 2:{let e=n({},a);for(let[t,n]of o)e[i(t)]=i(n);return e}case 3:return n(new Date(o),a);case 4:{let{source:e,flags:t}=o;return n(new RegExp(e,t),a)}case 5:{let e=n(new Map,a);for(let[t,n]of o)e.set(i(t),i(n));return e}case 6:{let e=n(new Set,a);for(let t of o)e.add(i(t));return e}case 7:{let{name:e,message:t}=o;return n(new r[e](t),a)}case 8:return n(BigInt(o),a);case"BigInt":return n(Object(BigInt(o)),a);case"ArrayBuffer":return n(new Uint8Array(o).buffer,o);case"DataView":{let{buffer:e}=new Uint8Array(o);return n(new DataView(e),o)}}return n(new r[s](o),a)};return i},a=e=>i(new Map,e)(0),{toString:s}={},{keys:o}=Object,l=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=s.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},c=([e,t])=>0===e&&("function"===t||"symbol"===t),u=(e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},a=r=>{if(n.has(r))return n.get(r);let[s,u]=l(r);switch(s){case 0:{let t=r;switch(u){case"bigint":s=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+u);t=null;break;case"undefined":return i([-1],r)}return i([s,t],r)}case 1:{if(u){let e=r;return"DataView"===u?e=new Uint8Array(r.buffer):"ArrayBuffer"===u&&(e=new Uint8Array(r)),i([u,[...e]],r)}let e=[],t=i([s,e],r);for(let t of r)e.push(a(t));return t}case 2:{if(u)switch(u){case"BigInt":return i([u,r.toString()],r);case"Boolean":case"Number":case"String":return i([u,r.valueOf()],r)}if(t&&"toJSON"in r)return a(r.toJSON());let n=[],h=i([s,n],r);for(let t of o(r))(e||!c(l(r[t])))&&n.push([a(t),a(r[t])]);return h}case 3:return i([s,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([s,{source:e,flags:t}],r)}case 5:{let t=[],n=i([s,t],r);for(let[n,i]of r)(e||!(c(l(n))||c(l(i))))&&t.push([a(n),a(i)]);return n}case 6:{let t=[],n=i([s,t],r);for(let n of r)(e||!c(l(n)))&&t.push(a(n));return n}}let{message:h}=r;return i([s,{name:u,message:h}],r)};return a},h=(e,{json:t,lossy:n}={})=>{let r=[];return u(!(t||n),!!t,new Map,r)(e),r},d="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?a(h(e,t)):structuredClone(e):(e,t)=>a(h(e,t))},68581:(e,t,n)=>{"use strict";n.d(t,{R:()=>r});class r{constructor(e,t){this.attribute=t,this.property=e}}r.prototype.attribute="",r.prototype.booleanish=!1,r.prototype.boolean=!1,r.prototype.commaOrSpaceSeparated=!1,r.prototype.commaSeparated=!1,r.prototype.defined=!1,r.prototype.mustUseProperty=!1,r.prototype.number=!1,r.prototype.overloadedBoolean=!1,r.prototype.property="",r.prototype.spaceSeparated=!1,r.prototype.space=void 0},69381:(e,t,n)=>{"use strict";n.d(t,{y:()=>a});var r=n(11603);let i={}.hasOwnProperty;function a(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let a,s=(i.call(e,n)?e[n]:void 0)||(e[n]={}),o=t[n];if(o)for(a in o){i.call(s,a)||(s[a]=[]);let e=o[a];!function(e,t){let n=-1,i=[];for(;++n<t.length;)("after"===t[n].add?e:i).push(t[n]);(0,r.m)(e,0,0,i)}(s[a],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}},70832:(e,t,n)=>{"use strict";n.d(t,{G1:()=>s,PW:()=>i,Y:()=>r});let r=a("end"),i=a("start");function a(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function s(e){let t=i(e),n=r(e);if(t&&n)return{start:t,end:n}}},71366:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},75006:(e,t,n)=>{"use strict";n.d(t,{A:()=>nE});var r,i,a,s,o,l,c,u,h,d,p,f,m,E,T,g,A={};n.r(A),n.d(A,{boolean:()=>W,booleanish:()=>Q,commaOrSpaceSeparated:()=>Z,commaSeparated:()=>J,number:()=>K,overloadedBoolean:()=>j,spaceSeparated:()=>X});var _=n(66945),S=n(34093),I=n(83846),k=n(55548);let C=/[#.]/g;var N=n(7887),D=n(59739),b=n(14947);function y(e,t,n){let r=n?function(e){let t=new Map;for(let n of e)t.set(n.toLowerCase(),n);return t}(n):void 0;return function(n,i,...a){let s;if(null==n)s={type:"root",children:[]},a.unshift(i);else{let o=(s=function(e,t){let n,r,i=e||"",a={},s=0;for(;s<i.length;){C.lastIndex=s;let e=C.exec(i),t=i.slice(s,e?e.index:i.length);t&&(n?"#"===n?a.id=t:Array.isArray(a.className)?a.className.push(t):a.className=[t]:r=t,s+=t.length),e&&(n=e[0],s++)}return{type:"element",tagName:r||t||"div",properties:a,children:[]}}(n,t)).tagName.toLowerCase(),l=r?r.get(o):void 0;if(s.tagName=l||o,function(e){if(null===e||"object"!=typeof e||Array.isArray(e))return!0;if("string"!=typeof e.type)return!1;for(let t of Object.keys(e)){let n=e[t];if(n&&"object"==typeof n){if(!Array.isArray(n))return!0;for(let e of n)if("number"!=typeof e&&"string"!=typeof e)return!0}}return!!("children"in e&&Array.isArray(e.children))}(i))a.unshift(i);else for(let[t,n]of Object.entries(i))!function(e,t,n,r){let i,a=(0,N.I)(e,n);if(null!=r){if("number"==typeof r){if(Number.isNaN(r))return;i=r}else i="boolean"==typeof r?r:"string"==typeof r?a.spaceSeparated?(0,b.q)(r):a.commaSeparated?(0,k.q)(r):a.commaOrSpaceSeparated?(0,b.q)((0,k.q)(r).join(" ")):O(a,a.property,r):Array.isArray(r)?[...r]:"style"===a.property?function(e){let t=[];for(let[n,r]of Object.entries(e))t.push([n,r].join(": "));return t.join("; ")}(r):String(r);if(Array.isArray(i)){let e=[];for(let t of i)e.push(O(a,a.property,t));i=e}"className"===a.property&&Array.isArray(t.className)&&(i=t.className.concat(i)),t[a.property]=i}}(e,s.properties,t,n)}for(let e of a)!function e(t,n){if(null==n);else if("number"==typeof n||"string"==typeof n)t.push({type:"text",value:String(n)});else if(Array.isArray(n))for(let r of n)e(t,r);else if("object"==typeof n&&"type"in n)"root"===n.type?e(t,n.children):t.push(n);else throw Error("Expected node, nodes, or string, got `"+n+"`")}(s.children,e);return"element"===s.type&&"template"===s.tagName&&(s.content={type:"root",children:s.children},s.children=[]),s}}function O(e,t,n){if("string"==typeof n){if(e.number&&n&&!Number.isNaN(Number(n)))return Number(n);if((e.boolean||e.overloadedBoolean)&&(""===n||(0,D.S)(n)===(0,D.S)(t)))return!0}return n}let R=y(I.qy,"div"),L=y(I.JW,"g",["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","solidColor","textArea","textPath"]);function P(e,t){let n=e.indexOf("\r",t),r=e.indexOf("\n",t);return -1===r?n:-1===n||n+1===r?r:n<r?n:r}let M={html:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"},v={}.hasOwnProperty,x=Object.prototype;function w(e,t){let n;switch(t.nodeName){case"#comment":return n={type:"comment",value:t.data},F(e,t,n),n;case"#document":case"#document-fragment":{let r="mode"in t&&("quirks"===t.mode||"limited-quirks"===t.mode);if(n={type:"root",children:B(e,t.childNodes),data:{quirksMode:r}},e.file&&e.location){let t=String(e.file),r=function(e){let t=String(e),n=[];return{toOffset:function(e){if(e&&"number"==typeof e.line&&"number"==typeof e.column&&!Number.isNaN(e.line)&&!Number.isNaN(e.column)){for(;n.length<e.line;){let e=n[n.length-1],r=P(t,e),i=-1===r?t.length+1:r+1;if(e===i)break;n.push(i)}let r=(e.line>1?n[e.line-2]:0)+e.column-1;if(r<n[e.line-1])return r}},toPoint:function(e){if("number"==typeof e&&e>-1&&e<=t.length){let r=0;for(;;){let i=n[r];if(void 0===i){let e=P(t,n[r-1]);i=-1===e?t.length+1:e+1,n[r]=i}if(i>e)return{line:r+1,column:e-(r>0?n[r-1]:0)+1,offset:e};r++}}}}}(t),i=r.toPoint(0),a=r.toPoint(t.length);(0,S.ok)(i,"expected `start`"),(0,S.ok)(a,"expected `end`"),n.position={start:i,end:a}}return n}case"#documentType":return F(e,t,n={type:"doctype"}),n;case"#text":return n={type:"text",value:t.value},F(e,t,n),n;default:return function(e,t){let n=e.schema;e.schema=t.namespaceURI===M.svg?I.JW:I.qy;let r=-1,i={};for(;++r<t.attrs.length;){let e=t.attrs[r],n=(e.prefix?e.prefix+":":"")+e.name;v.call(x,n)||(i[n]=e.value)}let a=("svg"===e.schema.space?L:R)(t.tagName,i,B(e,t.childNodes));if(F(e,t,a),"template"===a.tagName){let n=t.sourceCodeLocation,r=n&&n.startTag&&H(n.startTag),i=n&&n.endTag&&H(n.endTag),s=w(e,t.content);r&&i&&e.file&&(s.position={start:r.end,end:i.start}),a.content=s}return e.schema=n,a}(e,t)}}function B(e,t){let n=-1,r=[];for(;++n<t.length;){let i=w(e,t[n]);r.push(i)}return r}function F(e,t,n){if("sourceCodeLocation"in t&&t.sourceCodeLocation&&e.file){let r=function(e,t,n){let r=H(n);if("element"===t.type){let i=t.children[t.children.length-1];if(r&&!n.endTag&&i&&i.position&&i.position.end&&(r.end=Object.assign({},i.position.end)),e.verbose){let r,i={};if(n.attrs)for(r in n.attrs)v.call(n.attrs,r)&&(i[(0,N.I)(e.schema,r).property]=H(n.attrs[r]));(0,S.ok)(n.startTag,"a start tag should exist");let a=H(n.startTag),s=n.endTag?H(n.endTag):void 0,o={opening:a};s&&(o.closing=s),o.properties=i,t.data={position:o}}}return r}(e,n,t.sourceCodeLocation);r&&(e.location=!0,n.position=r)}}function H(e){let t=U({line:e.startLine,column:e.startCol,offset:e.startOffset}),n=U({line:e.endLine,column:e.endCol,offset:e.endOffset});return t||n?{start:t,end:n}:void 0}function U(e){return e.line&&e.column?e:void 0}class G{constructor(e,t,n){this.property=e,this.normal=t,n&&(this.space=n)}}function Y(e,t){let n={},r={},i=-1;for(;++i<e.length;)Object.assign(n,e[i].property),Object.assign(r,e[i].normal);return new G(n,r,t)}function z(e){return e.toLowerCase()}G.prototype.property={},G.prototype.normal={},G.prototype.space=null;class V{constructor(e,t){this.property=e,this.attribute=t}}V.prototype.space=null,V.prototype.boolean=!1,V.prototype.booleanish=!1,V.prototype.overloadedBoolean=!1,V.prototype.number=!1,V.prototype.commaSeparated=!1,V.prototype.spaceSeparated=!1,V.prototype.commaOrSpaceSeparated=!1,V.prototype.mustUseProperty=!1,V.prototype.defined=!1;let q=0,W=$(),Q=$(),j=$(),K=$(),X=$(),J=$(),Z=$();function $(){return 2**++q}let ee=Object.keys(A);class et extends V{constructor(e,t,n,r){let i=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",r),"number"==typeof n)for(;++i<ee.length;){let e=ee[i];!function(e,t,n){n&&(e[t]=n)}(this,ee[i],(n&A[e])===A[e])}}}et.prototype.defined=!0;let en={}.hasOwnProperty;function er(e){let t,n={},r={};for(t in e.properties)if(en.call(e.properties,t)){let i=e.properties[t],a=new et(t,e.transform(e.attributes||{},t),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(t)&&(a.mustUseProperty=!0),n[t]=a,r[z(t)]=t,r[z(a.attribute)]=t}return new G(n,r,e.space)}let ei=er({space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase(),properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),ea=er({space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase(),properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function es(e,t){return t in e?e[t]:t}function eo(e,t){return es(e,t.toLowerCase())}let el=er({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:eo,properties:{xmlns:null,xmlnsXLink:null}}),ec=er({transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase(),properties:{ariaActiveDescendant:null,ariaAtomic:Q,ariaAutoComplete:null,ariaBusy:Q,ariaChecked:Q,ariaColCount:K,ariaColIndex:K,ariaColSpan:K,ariaControls:X,ariaCurrent:null,ariaDescribedBy:X,ariaDetails:null,ariaDisabled:Q,ariaDropEffect:X,ariaErrorMessage:null,ariaExpanded:Q,ariaFlowTo:X,ariaGrabbed:Q,ariaHasPopup:null,ariaHidden:Q,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:X,ariaLevel:K,ariaLive:null,ariaModal:Q,ariaMultiLine:Q,ariaMultiSelectable:Q,ariaOrientation:null,ariaOwns:X,ariaPlaceholder:null,ariaPosInSet:K,ariaPressed:Q,ariaReadOnly:Q,ariaRelevant:null,ariaRequired:Q,ariaRoleDescription:X,ariaRowCount:K,ariaRowIndex:K,ariaRowSpan:K,ariaSelected:Q,ariaSetSize:K,ariaSort:null,ariaValueMax:K,ariaValueMin:K,ariaValueNow:K,ariaValueText:null,role:null}}),eu=er({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:eo,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:J,acceptCharset:X,accessKey:X,action:null,allow:null,allowFullScreen:W,allowPaymentRequest:W,allowUserMedia:W,alt:null,as:null,async:W,autoCapitalize:null,autoComplete:X,autoFocus:W,autoPlay:W,blocking:X,capture:null,charSet:null,checked:W,cite:null,className:X,cols:K,colSpan:null,content:null,contentEditable:Q,controls:W,controlsList:X,coords:K|J,crossOrigin:null,data:null,dateTime:null,decoding:null,default:W,defer:W,dir:null,dirName:null,disabled:W,download:j,draggable:Q,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:W,formTarget:null,headers:X,height:K,hidden:W,high:K,href:null,hrefLang:null,htmlFor:X,httpEquiv:X,id:null,imageSizes:null,imageSrcSet:null,inert:W,inputMode:null,integrity:null,is:null,isMap:W,itemId:null,itemProp:X,itemRef:X,itemScope:W,itemType:X,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:W,low:K,manifest:null,max:null,maxLength:K,media:null,method:null,min:null,minLength:K,multiple:W,muted:W,name:null,nonce:null,noModule:W,noValidate:W,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:W,optimum:K,pattern:null,ping:X,placeholder:null,playsInline:W,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:W,referrerPolicy:null,rel:X,required:W,reversed:W,rows:K,rowSpan:K,sandbox:X,scope:null,scoped:W,seamless:W,selected:W,shadowRootClonable:W,shadowRootDelegatesFocus:W,shadowRootMode:null,shape:null,size:K,sizes:null,slot:null,span:K,spellCheck:Q,src:null,srcDoc:null,srcLang:null,srcSet:null,start:K,step:null,style:null,tabIndex:K,target:null,title:null,translate:null,type:null,typeMustMatch:W,useMap:null,value:Q,width:K,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:X,axis:null,background:null,bgColor:null,border:K,borderColor:null,bottomMargin:K,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:W,declare:W,event:null,face:null,frame:null,frameBorder:null,hSpace:K,leftMargin:K,link:null,longDesc:null,lowSrc:null,marginHeight:K,marginWidth:K,noResize:W,noHref:W,noShade:W,noWrap:W,object:null,profile:null,prompt:null,rev:null,rightMargin:K,rules:null,scheme:null,scrolling:Q,standby:null,summary:null,text:null,topMargin:K,valueType:null,version:null,vAlign:null,vLink:null,vSpace:K,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:W,disableRemotePlayback:W,prefix:null,property:null,results:K,security:null,unselectable:null}}),eh=er({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:es,properties:{about:Z,accentHeight:K,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:K,amplitude:K,arabicForm:null,ascent:K,attributeName:null,attributeType:null,azimuth:K,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:K,by:null,calcMode:null,capHeight:K,className:X,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:K,diffuseConstant:K,direction:null,display:null,dur:null,divisor:K,dominantBaseline:null,download:W,dx:null,dy:null,edgeMode:null,editable:null,elevation:K,enableBackground:null,end:null,event:null,exponent:K,externalResourcesRequired:null,fill:null,fillOpacity:K,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:J,g2:J,glyphName:J,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:K,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:K,horizOriginX:K,horizOriginY:K,id:null,ideographic:K,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:K,k:K,k1:K,k2:K,k3:K,k4:K,kernelMatrix:Z,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:K,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:K,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:K,overlineThickness:K,paintOrder:null,panose1:null,path:null,pathLength:K,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:X,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:K,pointsAtY:K,pointsAtZ:K,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Z,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Z,rev:Z,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Z,requiredFeatures:Z,requiredFonts:Z,requiredFormats:Z,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:K,specularExponent:K,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:K,strikethroughThickness:K,string:null,stroke:null,strokeDashArray:Z,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:K,strokeOpacity:K,strokeWidth:null,style:null,surfaceScale:K,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Z,tabIndex:K,tableValues:null,target:null,targetX:K,targetY:K,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Z,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:K,underlineThickness:K,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:K,values:null,vAlphabetic:K,vMathematical:K,vectorEffect:null,vHanging:K,vIdeographic:K,version:null,vertAdvY:K,vertOriginX:K,vertOriginY:K,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:K,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),ed=Y([ea,ei,el,ec,eu],"html"),ep=Y([ea,ei,el,ec,eh],"svg"),ef=/^data[-\w.:]+$/i,em=/-[a-z]/g,eE=/[A-Z]/g;function eT(e){return"-"+e.toLowerCase()}function eg(e){return e.charAt(1).toUpperCase()}let eA={}.hasOwnProperty;function e_(e,t){let n=t||{};function r(t,...n){let i=r.invalid,a=r.handlers;if(t&&eA.call(t,e)){let n=String(t[e]);i=eA.call(a,n)?a[n]:r.unknown}if(i)return i.call(this,t,...n)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}let eS={}.hasOwnProperty,eI=e_("type",{handlers:{root:function(e,t){let n={nodeName:"#document",mode:(e.data||{}).quirksMode?"quirks":"no-quirks",childNodes:[]};return n.childNodes=ek(e.children,n,t),eC(e,n),n},element:function(e,t){let n,r=t;"element"===e.type&&"svg"===e.tagName.toLowerCase()&&"html"===t.space&&(r=ep);let i=[];if(e.properties){for(n in e.properties)if("children"!==n&&eS.call(e.properties,n)){let t=function(e,t,n){let r=function(e,t){let n=z(t),r=t,i=V;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&ef.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(em,eg);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!em.test(e)){let n=e.replace(eE,eT);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}i=et}return new i(r,t)}(e,t);if(!1===n||null==n||"number"==typeof n&&Number.isNaN(n)||!n&&r.boolean)return;Array.isArray(n)&&(n=r.commaSeparated?(0,k.A)(n):(0,b.A)(n));let i={name:r.attribute,value:!0===n?"":String(n)};if(r.space&&"html"!==r.space&&"svg"!==r.space){let e=i.name.indexOf(":");e<0?i.prefix="":(i.name=i.name.slice(e+1),i.prefix=r.attribute.slice(0,e)),i.namespace=M[r.space]}return i}(r,n,e.properties[n]);t&&i.push(t)}}let a=r.space;(0,S.ok)(a);let s={nodeName:e.tagName,tagName:e.tagName,attrs:i,namespaceURI:M[a],childNodes:[],parentNode:null};return s.childNodes=ek(e.children,s,r),eC(e,s),"template"===e.tagName&&e.content&&(s.content=function(e,t){let n={nodeName:"#document-fragment",childNodes:[]};return n.childNodes=ek(e.children,n,t),eC(e,n),n}(e.content,r)),s},text:function(e){let t={nodeName:"#text",value:e.value,parentNode:null};return eC(e,t),t},comment:function(e){let t={nodeName:"#comment",data:e.value,parentNode:null};return eC(e,t),t},doctype:function(e){let t={nodeName:"#documentType",name:"html",publicId:"",systemId:"",parentNode:null};return eC(e,t),t}}});function ek(e,t,n){let r=-1,i=[];if(e)for(;++r<e.length;){let a=eI(e[r],n);a.parentNode=t,i.push(a)}return i}function eC(e,t){let n=e.position;n&&n.start&&n.end&&((0,S.ok)("number"==typeof n.start.offset),(0,S.ok)("number"==typeof n.end.offset),t.sourceCodeLocation={startLine:n.start.line,startCol:n.start.column,startOffset:n.start.offset,endLine:n.end.line,endCol:n.end.column,endOffset:n.end.offset})}let eN=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"],eD=new Set([65534,65535,131070,131071,196606,196607,262142,262143,327678,327679,393214,393215,458750,458751,524286,524287,589822,589823,655358,655359,720894,720895,786430,786431,851966,851967,917502,917503,983038,983039,1048574,1048575,1114110,1114111]);!function(e){e[e.EOF=-1]="EOF",e[e.NULL=0]="NULL",e[e.TABULATION=9]="TABULATION",e[e.CARRIAGE_RETURN=13]="CARRIAGE_RETURN",e[e.LINE_FEED=10]="LINE_FEED",e[e.FORM_FEED=12]="FORM_FEED",e[e.SPACE=32]="SPACE",e[e.EXCLAMATION_MARK=33]="EXCLAMATION_MARK",e[e.QUOTATION_MARK=34]="QUOTATION_MARK",e[e.AMPERSAND=38]="AMPERSAND",e[e.APOSTROPHE=39]="APOSTROPHE",e[e.HYPHEN_MINUS=45]="HYPHEN_MINUS",e[e.SOLIDUS=47]="SOLIDUS",e[e.DIGIT_0=48]="DIGIT_0",e[e.DIGIT_9=57]="DIGIT_9",e[e.SEMICOLON=59]="SEMICOLON",e[e.LESS_THAN_SIGN=60]="LESS_THAN_SIGN",e[e.EQUALS_SIGN=61]="EQUALS_SIGN",e[e.GREATER_THAN_SIGN=62]="GREATER_THAN_SIGN",e[e.QUESTION_MARK=63]="QUESTION_MARK",e[e.LATIN_CAPITAL_A=65]="LATIN_CAPITAL_A",e[e.LATIN_CAPITAL_Z=90]="LATIN_CAPITAL_Z",e[e.RIGHT_SQUARE_BRACKET=93]="RIGHT_SQUARE_BRACKET",e[e.GRAVE_ACCENT=96]="GRAVE_ACCENT",e[e.LATIN_SMALL_A=97]="LATIN_SMALL_A",e[e.LATIN_SMALL_Z=122]="LATIN_SMALL_Z"}(r||(r={}));let eb={DASH_DASH:"--",CDATA_START:"[CDATA[",DOCTYPE:"doctype",SCRIPT:"script",PUBLIC:"public",SYSTEM:"system"};function ey(e){return e>=55296&&e<=57343}function eO(e){return 32!==e&&10!==e&&13!==e&&9!==e&&12!==e&&e>=1&&e<=31||e>=127&&e<=159}function eR(e){return e>=64976&&e<=65007||eD.has(e)}!function(e){e.controlCharacterInInputStream="control-character-in-input-stream",e.noncharacterInInputStream="noncharacter-in-input-stream",e.surrogateInInputStream="surrogate-in-input-stream",e.nonVoidHtmlElementStartTagWithTrailingSolidus="non-void-html-element-start-tag-with-trailing-solidus",e.endTagWithAttributes="end-tag-with-attributes",e.endTagWithTrailingSolidus="end-tag-with-trailing-solidus",e.unexpectedSolidusInTag="unexpected-solidus-in-tag",e.unexpectedNullCharacter="unexpected-null-character",e.unexpectedQuestionMarkInsteadOfTagName="unexpected-question-mark-instead-of-tag-name",e.invalidFirstCharacterOfTagName="invalid-first-character-of-tag-name",e.unexpectedEqualsSignBeforeAttributeName="unexpected-equals-sign-before-attribute-name",e.missingEndTagName="missing-end-tag-name",e.unexpectedCharacterInAttributeName="unexpected-character-in-attribute-name",e.unknownNamedCharacterReference="unknown-named-character-reference",e.missingSemicolonAfterCharacterReference="missing-semicolon-after-character-reference",e.unexpectedCharacterAfterDoctypeSystemIdentifier="unexpected-character-after-doctype-system-identifier",e.unexpectedCharacterInUnquotedAttributeValue="unexpected-character-in-unquoted-attribute-value",e.eofBeforeTagName="eof-before-tag-name",e.eofInTag="eof-in-tag",e.missingAttributeValue="missing-attribute-value",e.missingWhitespaceBetweenAttributes="missing-whitespace-between-attributes",e.missingWhitespaceAfterDoctypePublicKeyword="missing-whitespace-after-doctype-public-keyword",e.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers="missing-whitespace-between-doctype-public-and-system-identifiers",e.missingWhitespaceAfterDoctypeSystemKeyword="missing-whitespace-after-doctype-system-keyword",e.missingQuoteBeforeDoctypePublicIdentifier="missing-quote-before-doctype-public-identifier",e.missingQuoteBeforeDoctypeSystemIdentifier="missing-quote-before-doctype-system-identifier",e.missingDoctypePublicIdentifier="missing-doctype-public-identifier",e.missingDoctypeSystemIdentifier="missing-doctype-system-identifier",e.abruptDoctypePublicIdentifier="abrupt-doctype-public-identifier",e.abruptDoctypeSystemIdentifier="abrupt-doctype-system-identifier",e.cdataInHtmlContent="cdata-in-html-content",e.incorrectlyOpenedComment="incorrectly-opened-comment",e.eofInScriptHtmlCommentLikeText="eof-in-script-html-comment-like-text",e.eofInDoctype="eof-in-doctype",e.nestedComment="nested-comment",e.abruptClosingOfEmptyComment="abrupt-closing-of-empty-comment",e.eofInComment="eof-in-comment",e.incorrectlyClosedComment="incorrectly-closed-comment",e.eofInCdata="eof-in-cdata",e.absenceOfDigitsInNumericCharacterReference="absence-of-digits-in-numeric-character-reference",e.nullCharacterReference="null-character-reference",e.surrogateCharacterReference="surrogate-character-reference",e.characterReferenceOutsideUnicodeRange="character-reference-outside-unicode-range",e.controlCharacterReference="control-character-reference",e.noncharacterCharacterReference="noncharacter-character-reference",e.missingWhitespaceBeforeDoctypeName="missing-whitespace-before-doctype-name",e.missingDoctypeName="missing-doctype-name",e.invalidCharacterSequenceAfterDoctypeName="invalid-character-sequence-after-doctype-name",e.duplicateAttribute="duplicate-attribute",e.nonConformingDoctype="non-conforming-doctype",e.missingDoctype="missing-doctype",e.misplacedDoctype="misplaced-doctype",e.endTagWithoutMatchingOpenElement="end-tag-without-matching-open-element",e.closingOfElementWithOpenChildElements="closing-of-element-with-open-child-elements",e.disallowedContentInNoscriptInHead="disallowed-content-in-noscript-in-head",e.openElementsLeftAfterEof="open-elements-left-after-eof",e.abandonedHeadElementChild="abandoned-head-element-child",e.misplacedStartTagForHeadElement="misplaced-start-tag-for-head-element",e.nestedNoscriptInHead="nested-noscript-in-head",e.eofInElementThatCanContainOnlyText="eof-in-element-that-can-contain-only-text"}(i||(i={}));class eL{constructor(e){this.handler=e,this.html="",this.pos=-1,this.lastGapPos=-2,this.gapStack=[],this.skipNextNewLine=!1,this.lastChunkWritten=!1,this.endOfChunkHit=!1,this.bufferWaterline=65536,this.isEol=!1,this.lineStartPos=0,this.droppedBufferSize=0,this.line=1,this.lastErrOffset=-1}get col(){return this.pos-this.lineStartPos+Number(this.lastGapPos!==this.pos)}get offset(){return this.droppedBufferSize+this.pos}getError(e,t){let{line:n,col:r,offset:i}=this,a=r+t,s=i+t;return{code:e,startLine:n,endLine:n,startCol:a,endCol:a,startOffset:s,endOffset:s}}_err(e){this.handler.onParseError&&this.lastErrOffset!==this.offset&&(this.lastErrOffset=this.offset,this.handler.onParseError(this.getError(e,0)))}_addGap(){this.gapStack.push(this.lastGapPos),this.lastGapPos=this.pos}_processSurrogate(e){if(this.pos!==this.html.length-1){let t=this.html.charCodeAt(this.pos+1);if(t>=56320&&t<=57343)return this.pos++,this._addGap(),(e-55296)*1024+9216+t}else if(!this.lastChunkWritten)return this.endOfChunkHit=!0,r.EOF;return this._err(i.surrogateInInputStream),e}willDropParsedChunk(){return this.pos>this.bufferWaterline}dropParsedChunk(){this.willDropParsedChunk()&&(this.html=this.html.substring(this.pos),this.lineStartPos-=this.pos,this.droppedBufferSize+=this.pos,this.pos=0,this.lastGapPos=-2,this.gapStack.length=0)}write(e,t){this.html.length>0?this.html+=e:this.html=e,this.endOfChunkHit=!1,this.lastChunkWritten=t}insertHtmlAtCurrentPos(e){this.html=this.html.substring(0,this.pos+1)+e+this.html.substring(this.pos+1),this.endOfChunkHit=!1}startsWith(e,t){if(this.pos+e.length>this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,!1;if(t)return this.html.startsWith(e,this.pos);for(let t=0;t<e.length;t++)if((32|this.html.charCodeAt(this.pos+t))!==e.charCodeAt(t))return!1;return!0}peek(e){let t=this.pos+e;if(t>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,r.EOF;let n=this.html.charCodeAt(t);return n===r.CARRIAGE_RETURN?r.LINE_FEED:n}advance(){if(this.pos++,this.isEol&&(this.isEol=!1,this.line++,this.lineStartPos=this.pos),this.pos>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,r.EOF;let e=this.html.charCodeAt(this.pos);return e===r.CARRIAGE_RETURN?(this.isEol=!0,this.skipNextNewLine=!0,r.LINE_FEED):e===r.LINE_FEED&&(this.isEol=!0,this.skipNextNewLine)?(this.line--,this.skipNextNewLine=!1,this._addGap(),this.advance()):(this.skipNextNewLine=!1,ey(e)&&(e=this._processSurrogate(e)),null===this.handler.onParseError||e>31&&e<127||e===r.LINE_FEED||e===r.CARRIAGE_RETURN||e>159&&e<64976||this._checkForProblematicCharacters(e),e)}_checkForProblematicCharacters(e){eO(e)?this._err(i.controlCharacterInInputStream):eR(e)&&this._err(i.noncharacterInInputStream)}retreat(e){for(this.pos-=e;this.pos<this.lastGapPos;)this.lastGapPos=this.gapStack.pop(),this.pos--;this.isEol=!1}}function eP(e,t){for(let n=e.attrs.length-1;n>=0;n--)if(e.attrs[n].name===t)return e.attrs[n].value;return null}!function(e){e[e.CHARACTER=0]="CHARACTER",e[e.NULL_CHARACTER=1]="NULL_CHARACTER",e[e.WHITESPACE_CHARACTER=2]="WHITESPACE_CHARACTER",e[e.START_TAG=3]="START_TAG",e[e.END_TAG=4]="END_TAG",e[e.COMMENT=5]="COMMENT",e[e.DOCTYPE=6]="DOCTYPE",e[e.EOF=7]="EOF",e[e.HIBERNATION=8]="HIBERNATION"}(a||(a={}));let eM=new Uint16Array('ᵁ<\xd5ıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms\x7f\x84\x8b\x90\x95\x98\xa6\xb3\xb9\xc8\xcflig耻\xc6䃆P耻&䀦cute耻\xc1䃁reve;䄂Āiyx}rc耻\xc2䃂;䐐r;쀀\ud835\udd04rave耻\xc0䃀pha;䎑acr;䄀d;橓Āgp\x9d\xa1on;䄄f;쀀\ud835\udd38plyFunction;恡ing耻\xc5䃅Ācs\xbe\xc3r;쀀\ud835\udc9cign;扔ilde耻\xc3䃃ml耻\xc4䃄Ѐaceforsu\xe5\xfb\xfeėĜĢħĪĀcr\xea\xf2kslash;或Ŷ\xf6\xf8;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀\ud835\udd05pf;쀀\ud835\udd39eve;䋘c\xf2ēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻\xa9䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻\xc7䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷\xf2ſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀\ud835\udc9epĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀\ud835\udd07Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀\ud835\udd3bƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegra\xecȹoɴ͹\0\0ͻ\xbb͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔e\xe5ˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀\ud835\udc9frok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻\xd0䃐cute耻\xc9䃉ƀaiyӒӗӜron;䄚rc耻\xca䃊;䐭ot;䄖r;쀀\ud835\udd08rave耻\xc8䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀\ud835\udd3csilon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻\xcb䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀\ud835\udd09lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀\ud835\udd3dAll;戀riertrf;愱c\xf2׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀\ud835\udd0a;拙pf;쀀\ud835\udd3eeater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀\ud835\udca2;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅ\xf2کrok;䄦mpńېۘownHum\xf0įqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻\xcd䃍Āiyܓܘrc耻\xce䃎;䐘ot;䄰r;愑rave耻\xcc䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lie\xf3ϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀\ud835\udd40a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻\xcf䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀\ud835\udd0dpf;쀀\ud835\udd41ǣ߇\0ߌr;쀀\ud835\udca5rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀\ud835\udd0epf;쀀\ud835\udd42cr;쀀\ud835\udca6րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ight\xe1Μs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀\ud835\udd0fĀ;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊight\xe1οight\xe1ϊf;쀀\ud835\udd43erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂ\xf2ࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀\ud835\udd10nusPlus;戓pf;쀀\ud835\udd44c\xf2੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘\xeb૙eryThi\xee૙tedĀGL૸ଆreaterGreate\xf2ٳessLes\xf3ੈLine;䀊r;쀀\ud835\udd11ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀\ud835\udca9ilde耻\xd1䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻\xd3䃓Āiy෎ීrc耻\xd4䃔;䐞blac;䅐r;쀀\ud835\udd12rave耻\xd2䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀\ud835\udd46enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀\ud835\udcaaash耻\xd8䃘iŬื฼de耻\xd5䃕es;樷ml耻\xd6䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀\ud835\udd13i;䎦;䎠usMinus;䂱Āipຢອncareplan\xe5ڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀\ud835\udcab;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀\ud835\udd14pf;愚cr;쀀\ud835\udcac؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻\xae䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r\xbbཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀\ud835\udd16ortȀDLRUᄪᄴᄾᅉownArrow\xbbОeftArrow\xbb࢚ightArrow\xbb࿝pArrow;憑gma;䎣allCircle;战pf;쀀\ud835\udd4aɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀\ud835\udcaear;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Th\xe1ྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et\xbbሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻\xde䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀\ud835\udd17Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀\ud835\udd4bipleDot;惛Āctዖዛr;쀀\ud835\udcafrok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻\xda䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻\xdb䃛;䐣blac;䅰r;쀀\ud835\udd18rave耻\xd9䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀\ud835\udd4cЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥own\xe1ϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀\ud835\udcb0ilde;䅨ml耻\xdc䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀\ud835\udd19pf;쀀\ud835\udd4dcr;쀀\ud835\udcb1dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀\ud835\udd1apf;쀀\ud835\udd4ecr;쀀\ud835\udcb2Ȁfiosᓋᓐᓒᓘr;쀀\ud835\udd1b;䎞pf;쀀\ud835\udd4fcr;쀀\ud835\udcb3ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻\xdd䃝Āiyᔉᔍrc;䅶;䐫r;쀀\ud835\udd1cpf;쀀\ud835\udd50cr;쀀\ud835\udcb4ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidt\xe8૙a;䎖r;愨pf;愤cr;쀀\ud835\udcb5௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻\xe1䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻\xe2䃢te肻\xb4̆;䐰lig耻\xe6䃦Ā;r\xb2ᖺ;쀀\ud835\udd1erave耻\xe0䃠ĀepᗊᗖĀfpᗏᗔsym;愵\xe8ᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e\xbbᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢\xbb\xb9arr;捼Āgpᙣᙧon;䄅f;쀀\ud835\udd52΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒ\xf1ᚃing耻\xe5䃥ƀctyᚡᚦᚨr;쀀\ud835\udcb6;䀪mpĀ;e዁ᚯ\xf1ʈilde耻\xe3䃣ml耻\xe4䃤Āciᛂᛈonin\xf4ɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e\xbbᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰s\xe9ᜌno\xf5ēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀\ud835\udd1fg΀costuvwឍឝឳេ៕៛៞ƀaiuបពរ\xf0ݠrc;旯p\xbb፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄e\xe5ᑄ\xe5ᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀\ud835\udd53Ā;tᏋᡣom\xbbᏌtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻\xa6䂦Ȁceioᥑᥖᥚᥠr;쀀\ud835\udcb7mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t\xbb᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁\xeeړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻\xe7䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻\xb8ƭptyv;榲t脀\xa2;eᨭᨮ䂢r\xe4Ʋr;쀀\ud835\udd20ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark\xbbᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟\xbbཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it\xbb᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;q\xc7\xc6ɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁\xeeᅠeĀmx᫱᫶ent\xbb᫩e\xf3ɍǧ᫾\0ᬇĀ;dኻᬂot;橭n\xf4Ɇƀfryᬐᬔᬗ;쀀\ud835\udd54o\xe4ɔ脀\xa9;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀\ud835\udcb8Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒre\xe3᭳u\xe3᭵ee;拎edge;拏en耻\xa4䂤earrowĀlrᯮ᯳eft\xbbᮀight\xbbᮽe\xe4ᯝĀciᰁᰇonin\xf4Ƿnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍r\xf2΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸\xf2ᄳhĀ;vᱚᱛ怐\xbbऊūᱡᱧarow;椏a\xe3̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻\xb0䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀\ud835\udd21arĀlrᲳᲵ\xbbࣜ\xbbသʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀\xf7;o᳧ᳰntimes;拇n\xf8᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀\ud835\udd55ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedg\xe5\xfanƀadhᄮᵝᵧownarrow\xf3ᲃarpoonĀlrᵲᵶef\xf4Ჴigh\xf4ᲶŢᵿᶅkaro\xf7གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀\ud835\udcb9;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃r\xf2Щa\xf2ྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴo\xf4ᲉĀcsḎḔute耻\xe9䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻\xea䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀\ud835\udd22ƀ;rsṐṑṗ檚ave耻\xe8䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et\xbbẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀\ud835\udd56ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on\xbbớ;䏵ȀcsuvỪỳἋἣĀioữḱrc\xbbḮɩỹ\0\0ỻ\xedՈantĀglἂἆtr\xbbṝess\xbbṺƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯o\xf4͒ĀahὉὋ;䎷耻\xf0䃰Āmrὓὗl耻\xeb䃫o;悬ƀcipὡὤὧl;䀡s\xf4ծĀeoὬὴctatio\xeeՙnential\xe5չৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotse\xf1Ṅy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀\ud835\udd23lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀\ud835\udd57ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻\xbd䂽;慓耻\xbc䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻\xbe䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀\ud835\udcbbࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lan\xf4٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀\ud835\udd24Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox\xbbℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀\ud835\udd58Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎pro\xf8₞r;楸qĀlqؿ↖les\xf3₈i\xed٫Āen↣↭rtneqq;쀀≩︀\xc5↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽r\xf2ΠȀilmr⇐⇔⇗⇛rs\xf0ᒄf\xbb․il\xf4کĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it\xbb∊lip;怦con;抹r;쀀\ud835\udd25sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀\ud835\udd59bar;怕ƀclt≯≴≸r;쀀\ud835\udcbdas\xe8⇴rok;䄧Ābp⊂⊇ull;恃hen\xbbᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻\xed䃭ƀ;iyݱ⊰⊵rc耻\xee䃮;䐸Ācx⊼⊿y;䐵cl耻\xa1䂡ĀfrΟ⋉;쀀\ud835\udd26rave耻\xec䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓in\xe5ގar\xf4ܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝do\xf4⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙er\xf3ᕣ\xe3⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀\ud835\udd5aa;䎹uest耻\xbf䂿Āci⎊⎏r;쀀\ud835\udcbenʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻\xef䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀\ud835\udd27ath;䈷pf;쀀\ud835\udd5bǣ⏬\0⏱r;쀀\ud835\udcbfrcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀\ud835\udd28reen;䄸cy;䑅cy;䑜pf;쀀\ud835\udd5ccr;쀀\ud835\udcc0஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼r\xf2৆\xf2Εail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴ra\xeeࡌbda;䎻gƀ;dlࢎⓁⓃ;榑\xe5ࢎ;檅uo耻\xab䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝\xeb≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼\xecࢰ\xe2┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□a\xe9⓶arpoonĀdu▯▴own\xbbњp\xbb०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoon\xf3྘quigarro\xf7⇰hreetimes;拋ƀ;qs▋ও◺lan\xf4বʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋ppro\xf8Ⓠot;拖qĀgq♃♅\xf4উgt\xf2⒌\xf4ছi\xedলƀilr♕࣡♚sht;楼;쀀\ud835\udd29Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖r\xf2◁orne\xf2ᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che\xbb⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox\xbb⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽r\xebࣁgƀlmr⛿✍✔eftĀar০✇ight\xe1৲apsto;柼ight\xe1৽parrowĀlr✥✩ef\xf4⓭ight;憬ƀafl✶✹✽r;榅;쀀\ud835\udd5dus;樭imes;樴š❋❏st;戗\xe1ፎƀ;ef❗❘᠀旊nge\xbb❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇r\xf2ࢨorne\xf2ᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀\ud835\udcc1mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹re\xe5◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀\xc5⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻\xaf䂯Āet⡗⡙;時Ā;e⡞⡟朠se\xbb⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻ow\xeeҌef\xf4ए\xf0Ꮡker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle\xbbᘦr;쀀\ud835\udd2ao;愧ƀcdn⢯⢴⣉ro耻\xb5䂵Ȁ;acdᑤ⢽⣀⣄s\xf4ᚧir;櫰ot肻\xb7Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛\xf2−\xf0ઁĀdp⣩⣮els;抧f;쀀\ud835\udd5eĀct⣸⣽r;쀀\ud835\udcc2pos\xbbᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la\xbb˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉ro\xf8඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻\xa0ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸ui\xf6ୣĀei⩊⩎ar;椨\xed஘istĀ;s஠டr;쀀\ud835\udd2bȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lan\xf4௢i\xed௪Ā;rஶ⪁\xbbஷƀAap⪊⪍⪑r\xf2⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹r\xf2⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro\xf7⫁ightarro\xf7⪐ƀ;qs఻⪺⫪lan\xf4ౕĀ;sౕ⫴\xbbశi\xedౝĀ;rవ⫾iĀ;eచథi\xe4ඐĀpt⬌⬑f;쀀\ud835\udd5f膀\xac;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lle\xec୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳u\xe5ಥĀ;cಘ⭸Ā;eಒ⭽\xf1ಘȀAait⮈⮋⮝⮧r\xf2⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow\xbb⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉u\xe5൅;쀀\ud835\udcc3ortɭ⬅\0\0⯖ar\xe1⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭\xe5೸\xe5ഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗ\xf1സȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇ\xecௗlde耻\xf1䃱\xe7ృiangleĀlrⱒⱜeftĀ;eచⱚ\xf1దightĀ;eೋⱥ\xf1೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻\xf3䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻\xf4䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀\ud835\udd2cͯ⵹\0\0⵼\0ⶂn;䋛ave耻\xf2䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨr\xf2᪀Āir⶝ⶠr;榾oss;榻n\xe5๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀\ud835\udd60ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨r\xf2᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f\xbbⷿ耻\xaa䂪耻\xba䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧\xf2⸁ash耻\xf8䃸l;折iŬⸯ⸴de耻\xf5䃵esĀ;aǛ⸺s;樶ml耻\xf6䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀\xb6;l⹭⹮䂶le\xecЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀\ud835\udd2dƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕ma\xf4੶ne;明ƀ;tv⺿⻀⻈䏀chfork\xbb´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎\xf6⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻\xb1ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀\ud835\udd61nd耻\xa3䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷u\xe5໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾ppro\xf8⽃urlye\xf1໙\xf1໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨i\xedໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺\xf0⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴\xef໻rel;抰Āci⿀⿅r;쀀\ud835\udcc5;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀\ud835\udd2epf;쀀\ud835\udd62rime;恗cr;쀀\ud835\udcc6ƀaeo⿸〉〓tĀei⿾々rnion\xf3ڰnt;樖stĀ;e【】䀿\xf1Ἑ\xf4༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがr\xf2Ⴓ\xf2ϝail;検ar\xf2ᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕i\xe3ᅮmptyv;榳gȀ;del࿑らるろ;榒;榥\xe5࿑uo耻\xbb䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞\xeb≝\xf0✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶al\xf3༞ƀabrョリヮr\xf2៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗\xec࿲\xe2ヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜn\xe5Ⴛar\xf4ྩt;断ƀilrㅩဣㅮsht;楽;쀀\ud835\udd2fĀaoㅷㆆrĀduㅽㅿ\xbbѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭa\xe9トarpoonĀduㆻㆿow\xeeㅾp\xbb႒eftĀah㇊㇐rrow\xf3࿪arpoon\xf3Ցightarrows;應quigarro\xf7ニhreetimes;拌g;䋚ingdotse\xf1ἲƀahm㈍㈐㈓r\xf2࿪a\xf2Ց;怏oustĀ;a㈞㈟掱che\xbb㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾r\xebဃƀafl㉇㉊㉎r;榆;쀀\ud835\udd63us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒ar\xf2㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀\ud835\udcc7Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠re\xe5ㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛qu\xef➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡u\xe5ᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓i\xedሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒\xeb∨Ā;oਸ਼਴t耻\xa7䂧i;䀻war;椩mĀin㍩\xf0nu\xf3\xf1t;朶rĀ;o㍶⁕쀀\ud835\udd30Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜i\xe4ᑤara\xec⹯耻\xad䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲ar\xf2ᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetm\xe9㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀\ud835\udd64aĀdr㑍ЂesĀ;u㑔㑕晠it\xbb㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍\xf1ᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝\xf1ᆮƀ;afᅻ㒦ְrť㒫ֱ\xbbᅼar\xf2ᅈȀcemt㒹㒾㓂㓅r;쀀\ud835\udcc8tm\xee\xf1i\xec㐕ar\xe6ᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psilo\xeeỠh\xe9⺯s\xbb⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦ppro\xf8㋺urlye\xf1ᇾ\xf1ᇳƀaes㖂㖈㌛ppro\xf8㌚q\xf1㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻\xb9䂹耻\xb2䂲耻\xb3䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨\xeb∮Ā;oਫ਩war;椪lig耻\xdf䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄r\xeb๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀\ud835\udd31Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮ppro\xf8዁im\xbbኬs\xf0ኞĀas㚺㚮\xf0዁rn耻\xfe䃾Ǭ̟㛆⋧es膀\xd7;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀\xe1⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀\ud835\udd65rk;櫚\xe1㍢rime;怴ƀaip㜏㜒㝤d\xe5ቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own\xbbᶻeftĀ;e⠀㜾\xf1म;扜ightĀ;e㊪㝋\xf1ၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀\ud835\udcc9;䑆cy;䑛rok;䅧Āio㞋㞎x\xf4᝷headĀlr㞗㞠eftarro\xf7ࡏightarrow\xbbཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶r\xf2ϭar;楣Ācr㟜㟢ute耻\xfa䃺\xf2ᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻\xfb䃻;䑃ƀabh㠃㠆㠋r\xf2Ꭽlac;䅱a\xf2ᏃĀir㠓㠘sht;楾;쀀\ud835\udd32rave耻\xf9䃹š㠧㠱rĀlr㠬㠮\xbbॗ\xbbႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r\xbb㡆op;挏ri;旸Āal㡖㡚cr;䅫肻\xa8͉Āgp㡢㡦on;䅳f;쀀\ud835\udd66̀adhlsuᅋ㡸㡽፲㢑㢠own\xe1ᎳarpoonĀlr㢈㢌ef\xf4㠭igh\xf4㠯iƀ;hl㢙㢚㢜䏅\xbbᏺon\xbb㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r\xbb㢽op;挎ng;䅯ri;旹cr;쀀\ud835\udccaƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨\xbb᠓Āam㣯㣲r\xf2㢨l耻\xfc䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠r\xf2ϷarĀ;v㤦㤧櫨;櫩as\xe8ϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖app\xe1␕othin\xe7ẖƀhir㓫⻈㥙op\xf4⾵Ā;hᎷ㥢\xefㆍĀiu㥩㥭gm\xe1㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟et\xe1㚜iangleĀlr㦪㦯eft\xbbथight\xbbၑy;䐲ash\xbbံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨa\xf2ᑩr;쀀\ud835\udd33tr\xe9㦮suĀbp㧯㧱\xbbജ\xbb൙pf;쀀\ud835\udd67ro\xf0໻tr\xe9㦴Ācu㨆㨋r;쀀\ud835\udccbĀbp㨐㨘nĀEe㦀㨖\xbb㥾nĀEe㦒㨞\xbb㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀\ud835\udd34pf;쀀\ud835\udd68Ā;eᑹ㩦at\xe8ᑹcr;쀀\ud835\udcccૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tr\xe9៑r;쀀\ud835\udd35ĀAa㪔㪗r\xf2σr\xf2৶;䎾ĀAa㪡㪤r\xf2θr\xf2৫a\xf0✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀\ud835\udd69im\xe5ឲĀAa㫇㫊r\xf2ώr\xf2ਁĀcq㫒ីr;쀀\ud835\udccdĀpt៖㫜r\xe9។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻\xfd䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻\xa5䂥r;쀀\ud835\udd36cy;䑗pf;쀀\ud835\udd6acr;쀀\ud835\udcceĀcm㬦㬩y;䑎l耻\xff䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡tr\xe6ᕟa;䎶r;쀀\ud835\udd37cy;䐶grarr;懝pf;쀀\ud835\udd6bcr;쀀\ud835\udccfĀjn㮅㮇;怍j;怌'.split("").map(e=>e.charCodeAt(0))),ev=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]);s=String.fromCodePoint;function ex(e){return e>=o.ZERO&&e<=o.NINE}!function(e){e[e.NUM=35]="NUM",e[e.SEMI=59]="SEMI",e[e.EQUALS=61]="EQUALS",e[e.ZERO=48]="ZERO",e[e.NINE=57]="NINE",e[e.LOWER_A=97]="LOWER_A",e[e.LOWER_F=102]="LOWER_F",e[e.LOWER_X=120]="LOWER_X",e[e.LOWER_Z=122]="LOWER_Z",e[e.UPPER_A=65]="UPPER_A",e[e.UPPER_F=70]="UPPER_F",e[e.UPPER_Z=90]="UPPER_Z"}(o||(o={})),!function(e){e[e.VALUE_LENGTH=49152]="VALUE_LENGTH",e[e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",e[e.JUMP_TABLE=127]="JUMP_TABLE"}(l||(l={})),!function(e){e[e.EntityStart=0]="EntityStart",e[e.NumericStart=1]="NumericStart",e[e.NumericDecimal=2]="NumericDecimal",e[e.NumericHex=3]="NumericHex",e[e.NamedEntity=4]="NamedEntity"}(c||(c={})),function(e){e[e.Legacy=0]="Legacy",e[e.Strict=1]="Strict",e[e.Attribute=2]="Attribute"}(u||(u={}));class ew{constructor(e,t,n){this.decodeTree=e,this.emitCodePoint=t,this.errors=n,this.state=c.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=u.Strict}startEntity(e){this.decodeMode=e,this.state=c.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case c.EntityStart:if(e.charCodeAt(t)===o.NUM)return this.state=c.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1);return this.state=c.NamedEntity,this.stateNamedEntity(e,t);case c.NumericStart:return this.stateNumericStart(e,t);case c.NumericDecimal:return this.stateNumericDecimal(e,t);case c.NumericHex:return this.stateNumericHex(e,t);case c.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===o.LOWER_X?(this.state=c.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=c.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,n,r){if(t!==n){let i=n-t;this.result=this.result*Math.pow(r,i)+Number.parseInt(e.substr(t,i),r),this.consumed+=i}}stateNumericHex(e,t){let n=t;for(;t<e.length;){var r;let i=e.charCodeAt(t);if(!ex(i)&&(!((r=i)>=o.UPPER_A)||!(r<=o.UPPER_F))&&(!(r>=o.LOWER_A)||!(r<=o.LOWER_F)))return this.addToNumericResult(e,n,t,16),this.emitNumericEntity(i,3);t+=1}return this.addToNumericResult(e,n,t,16),-1}stateNumericDecimal(e,t){let n=t;for(;t<e.length;){let r=e.charCodeAt(t);if(!ex(r))return this.addToNumericResult(e,n,t,10),this.emitNumericEntity(r,2);t+=1}return this.addToNumericResult(e,n,t,10),-1}emitNumericEntity(e,t){var n,r,i;if(this.consumed<=t)return null==(n=this.errors)||n.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===o.SEMI)this.consumed+=1;else if(this.decodeMode===u.Strict)return 0;return this.emitCodePoint((r=this.result)>=55296&&r<=57343||r>1114111?65533:null!=(i=ev.get(r))?i:r,this.consumed),this.errors&&(e!==o.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){let{decodeTree:n}=this,r=n[this.treeIndex],i=(r&l.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){let a=e.charCodeAt(t);if(this.treeIndex=function(e,t,n,r){let i=(t&l.BRANCH_LENGTH)>>7,a=t&l.JUMP_TABLE;if(0===i)return 0!==a&&r===a?n:-1;if(a){let t=r-a;return t<0||t>=i?-1:e[n+t]-1}let s=n,o=s+i-1;for(;s<=o;){let t=s+o>>>1,n=e[t];if(n<r)s=t+1;else{if(!(n>r))return e[t+i];o=t-1}}return -1}(n,r,this.treeIndex+Math.max(1,i),a),this.treeIndex<0)return 0===this.result||this.decodeMode===u.Attribute&&(0===i||function(e){var t;return e===o.EQUALS||(t=e)>=o.UPPER_A&&t<=o.UPPER_Z||t>=o.LOWER_A&&t<=o.LOWER_Z||ex(t)}(a))?0:this.emitNotTerminatedNamedEntity();if(0!=(i=((r=n[this.treeIndex])&l.VALUE_LENGTH)>>14)){if(a===o.SEMI)return this.emitNamedEntityData(this.treeIndex,i,this.consumed+this.excess);this.decodeMode!==u.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return -1}emitNotTerminatedNamedEntity(){var e;let{result:t,decodeTree:n}=this,r=(n[t]&l.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,r,this.consumed),null==(e=this.errors)||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,n){let{decodeTree:r}=this;return this.emitCodePoint(1===t?r[e]&~l.VALUE_LENGTH:r[e+1],n),3===t&&this.emitCodePoint(r[e+2],n),n}end(){var e;switch(this.state){case c.NamedEntity:return 0!==this.result&&(this.decodeMode!==u.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case c.NumericDecimal:return this.emitNumericEntity(0,2);case c.NumericHex:return this.emitNumericEntity(0,3);case c.NumericStart:return null==(e=this.errors)||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case c.EntityStart:return 0}}}!function(e){e.HTML="http://www.w3.org/1999/xhtml",e.MATHML="http://www.w3.org/1998/Math/MathML",e.SVG="http://www.w3.org/2000/svg",e.XLINK="http://www.w3.org/1999/xlink",e.XML="http://www.w3.org/XML/1998/namespace",e.XMLNS="http://www.w3.org/2000/xmlns/"}(h||(h={})),function(e){e.TYPE="type",e.ACTION="action",e.ENCODING="encoding",e.PROMPT="prompt",e.NAME="name",e.COLOR="color",e.FACE="face",e.SIZE="size"}(d||(d={})),function(e){e.NO_QUIRKS="no-quirks",e.QUIRKS="quirks",e.LIMITED_QUIRKS="limited-quirks"}(p||(p={})),function(e){e.A="a",e.ADDRESS="address",e.ANNOTATION_XML="annotation-xml",e.APPLET="applet",e.AREA="area",e.ARTICLE="article",e.ASIDE="aside",e.B="b",e.BASE="base",e.BASEFONT="basefont",e.BGSOUND="bgsound",e.BIG="big",e.BLOCKQUOTE="blockquote",e.BODY="body",e.BR="br",e.BUTTON="button",e.CAPTION="caption",e.CENTER="center",e.CODE="code",e.COL="col",e.COLGROUP="colgroup",e.DD="dd",e.DESC="desc",e.DETAILS="details",e.DIALOG="dialog",e.DIR="dir",e.DIV="div",e.DL="dl",e.DT="dt",e.EM="em",e.EMBED="embed",e.FIELDSET="fieldset",e.FIGCAPTION="figcaption",e.FIGURE="figure",e.FONT="font",e.FOOTER="footer",e.FOREIGN_OBJECT="foreignObject",e.FORM="form",e.FRAME="frame",e.FRAMESET="frameset",e.H1="h1",e.H2="h2",e.H3="h3",e.H4="h4",e.H5="h5",e.H6="h6",e.HEAD="head",e.HEADER="header",e.HGROUP="hgroup",e.HR="hr",e.HTML="html",e.I="i",e.IMG="img",e.IMAGE="image",e.INPUT="input",e.IFRAME="iframe",e.KEYGEN="keygen",e.LABEL="label",e.LI="li",e.LINK="link",e.LISTING="listing",e.MAIN="main",e.MALIGNMARK="malignmark",e.MARQUEE="marquee",e.MATH="math",e.MENU="menu",e.META="meta",e.MGLYPH="mglyph",e.MI="mi",e.MO="mo",e.MN="mn",e.MS="ms",e.MTEXT="mtext",e.NAV="nav",e.NOBR="nobr",e.NOFRAMES="noframes",e.NOEMBED="noembed",e.NOSCRIPT="noscript",e.OBJECT="object",e.OL="ol",e.OPTGROUP="optgroup",e.OPTION="option",e.P="p",e.PARAM="param",e.PLAINTEXT="plaintext",e.PRE="pre",e.RB="rb",e.RP="rp",e.RT="rt",e.RTC="rtc",e.RUBY="ruby",e.S="s",e.SCRIPT="script",e.SEARCH="search",e.SECTION="section",e.SELECT="select",e.SOURCE="source",e.SMALL="small",e.SPAN="span",e.STRIKE="strike",e.STRONG="strong",e.STYLE="style",e.SUB="sub",e.SUMMARY="summary",e.SUP="sup",e.TABLE="table",e.TBODY="tbody",e.TEMPLATE="template",e.TEXTAREA="textarea",e.TFOOT="tfoot",e.TD="td",e.TH="th",e.THEAD="thead",e.TITLE="title",e.TR="tr",e.TRACK="track",e.TT="tt",e.U="u",e.UL="ul",e.SVG="svg",e.VAR="var",e.WBR="wbr",e.XMP="xmp"}(f||(f={})),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.A=1]="A",e[e.ADDRESS=2]="ADDRESS",e[e.ANNOTATION_XML=3]="ANNOTATION_XML",e[e.APPLET=4]="APPLET",e[e.AREA=5]="AREA",e[e.ARTICLE=6]="ARTICLE",e[e.ASIDE=7]="ASIDE",e[e.B=8]="B",e[e.BASE=9]="BASE",e[e.BASEFONT=10]="BASEFONT",e[e.BGSOUND=11]="BGSOUND",e[e.BIG=12]="BIG",e[e.BLOCKQUOTE=13]="BLOCKQUOTE",e[e.BODY=14]="BODY",e[e.BR=15]="BR",e[e.BUTTON=16]="BUTTON",e[e.CAPTION=17]="CAPTION",e[e.CENTER=18]="CENTER",e[e.CODE=19]="CODE",e[e.COL=20]="COL",e[e.COLGROUP=21]="COLGROUP",e[e.DD=22]="DD",e[e.DESC=23]="DESC",e[e.DETAILS=24]="DETAILS",e[e.DIALOG=25]="DIALOG",e[e.DIR=26]="DIR",e[e.DIV=27]="DIV",e[e.DL=28]="DL",e[e.DT=29]="DT",e[e.EM=30]="EM",e[e.EMBED=31]="EMBED",e[e.FIELDSET=32]="FIELDSET",e[e.FIGCAPTION=33]="FIGCAPTION",e[e.FIGURE=34]="FIGURE",e[e.FONT=35]="FONT",e[e.FOOTER=36]="FOOTER",e[e.FOREIGN_OBJECT=37]="FOREIGN_OBJECT",e[e.FORM=38]="FORM",e[e.FRAME=39]="FRAME",e[e.FRAMESET=40]="FRAMESET",e[e.H1=41]="H1",e[e.H2=42]="H2",e[e.H3=43]="H3",e[e.H4=44]="H4",e[e.H5=45]="H5",e[e.H6=46]="H6",e[e.HEAD=47]="HEAD",e[e.HEADER=48]="HEADER",e[e.HGROUP=49]="HGROUP",e[e.HR=50]="HR",e[e.HTML=51]="HTML",e[e.I=52]="I",e[e.IMG=53]="IMG",e[e.IMAGE=54]="IMAGE",e[e.INPUT=55]="INPUT",e[e.IFRAME=56]="IFRAME",e[e.KEYGEN=57]="KEYGEN",e[e.LABEL=58]="LABEL",e[e.LI=59]="LI",e[e.LINK=60]="LINK",e[e.LISTING=61]="LISTING",e[e.MAIN=62]="MAIN",e[e.MALIGNMARK=63]="MALIGNMARK",e[e.MARQUEE=64]="MARQUEE",e[e.MATH=65]="MATH",e[e.MENU=66]="MENU",e[e.META=67]="META",e[e.MGLYPH=68]="MGLYPH",e[e.MI=69]="MI",e[e.MO=70]="MO",e[e.MN=71]="MN",e[e.MS=72]="MS",e[e.MTEXT=73]="MTEXT",e[e.NAV=74]="NAV",e[e.NOBR=75]="NOBR",e[e.NOFRAMES=76]="NOFRAMES",e[e.NOEMBED=77]="NOEMBED",e[e.NOSCRIPT=78]="NOSCRIPT",e[e.OBJECT=79]="OBJECT",e[e.OL=80]="OL",e[e.OPTGROUP=81]="OPTGROUP",e[e.OPTION=82]="OPTION",e[e.P=83]="P",e[e.PARAM=84]="PARAM",e[e.PLAINTEXT=85]="PLAINTEXT",e[e.PRE=86]="PRE",e[e.RB=87]="RB",e[e.RP=88]="RP",e[e.RT=89]="RT",e[e.RTC=90]="RTC",e[e.RUBY=91]="RUBY",e[e.S=92]="S",e[e.SCRIPT=93]="SCRIPT",e[e.SEARCH=94]="SEARCH",e[e.SECTION=95]="SECTION",e[e.SELECT=96]="SELECT",e[e.SOURCE=97]="SOURCE",e[e.SMALL=98]="SMALL",e[e.SPAN=99]="SPAN",e[e.STRIKE=100]="STRIKE",e[e.STRONG=101]="STRONG",e[e.STYLE=102]="STYLE",e[e.SUB=103]="SUB",e[e.SUMMARY=104]="SUMMARY",e[e.SUP=105]="SUP",e[e.TABLE=106]="TABLE",e[e.TBODY=107]="TBODY",e[e.TEMPLATE=108]="TEMPLATE",e[e.TEXTAREA=109]="TEXTAREA",e[e.TFOOT=110]="TFOOT",e[e.TD=111]="TD",e[e.TH=112]="TH",e[e.THEAD=113]="THEAD",e[e.TITLE=114]="TITLE",e[e.TR=115]="TR",e[e.TRACK=116]="TRACK",e[e.TT=117]="TT",e[e.U=118]="U",e[e.UL=119]="UL",e[e.SVG=120]="SVG",e[e.VAR=121]="VAR",e[e.WBR=122]="WBR",e[e.XMP=123]="XMP"}(m||(m={}));let eB=new Map([[f.A,m.A],[f.ADDRESS,m.ADDRESS],[f.ANNOTATION_XML,m.ANNOTATION_XML],[f.APPLET,m.APPLET],[f.AREA,m.AREA],[f.ARTICLE,m.ARTICLE],[f.ASIDE,m.ASIDE],[f.B,m.B],[f.BASE,m.BASE],[f.BASEFONT,m.BASEFONT],[f.BGSOUND,m.BGSOUND],[f.BIG,m.BIG],[f.BLOCKQUOTE,m.BLOCKQUOTE],[f.BODY,m.BODY],[f.BR,m.BR],[f.BUTTON,m.BUTTON],[f.CAPTION,m.CAPTION],[f.CENTER,m.CENTER],[f.CODE,m.CODE],[f.COL,m.COL],[f.COLGROUP,m.COLGROUP],[f.DD,m.DD],[f.DESC,m.DESC],[f.DETAILS,m.DETAILS],[f.DIALOG,m.DIALOG],[f.DIR,m.DIR],[f.DIV,m.DIV],[f.DL,m.DL],[f.DT,m.DT],[f.EM,m.EM],[f.EMBED,m.EMBED],[f.FIELDSET,m.FIELDSET],[f.FIGCAPTION,m.FIGCAPTION],[f.FIGURE,m.FIGURE],[f.FONT,m.FONT],[f.FOOTER,m.FOOTER],[f.FOREIGN_OBJECT,m.FOREIGN_OBJECT],[f.FORM,m.FORM],[f.FRAME,m.FRAME],[f.FRAMESET,m.FRAMESET],[f.H1,m.H1],[f.H2,m.H2],[f.H3,m.H3],[f.H4,m.H4],[f.H5,m.H5],[f.H6,m.H6],[f.HEAD,m.HEAD],[f.HEADER,m.HEADER],[f.HGROUP,m.HGROUP],[f.HR,m.HR],[f.HTML,m.HTML],[f.I,m.I],[f.IMG,m.IMG],[f.IMAGE,m.IMAGE],[f.INPUT,m.INPUT],[f.IFRAME,m.IFRAME],[f.KEYGEN,m.KEYGEN],[f.LABEL,m.LABEL],[f.LI,m.LI],[f.LINK,m.LINK],[f.LISTING,m.LISTING],[f.MAIN,m.MAIN],[f.MALIGNMARK,m.MALIGNMARK],[f.MARQUEE,m.MARQUEE],[f.MATH,m.MATH],[f.MENU,m.MENU],[f.META,m.META],[f.MGLYPH,m.MGLYPH],[f.MI,m.MI],[f.MO,m.MO],[f.MN,m.MN],[f.MS,m.MS],[f.MTEXT,m.MTEXT],[f.NAV,m.NAV],[f.NOBR,m.NOBR],[f.NOFRAMES,m.NOFRAMES],[f.NOEMBED,m.NOEMBED],[f.NOSCRIPT,m.NOSCRIPT],[f.OBJECT,m.OBJECT],[f.OL,m.OL],[f.OPTGROUP,m.OPTGROUP],[f.OPTION,m.OPTION],[f.P,m.P],[f.PARAM,m.PARAM],[f.PLAINTEXT,m.PLAINTEXT],[f.PRE,m.PRE],[f.RB,m.RB],[f.RP,m.RP],[f.RT,m.RT],[f.RTC,m.RTC],[f.RUBY,m.RUBY],[f.S,m.S],[f.SCRIPT,m.SCRIPT],[f.SEARCH,m.SEARCH],[f.SECTION,m.SECTION],[f.SELECT,m.SELECT],[f.SOURCE,m.SOURCE],[f.SMALL,m.SMALL],[f.SPAN,m.SPAN],[f.STRIKE,m.STRIKE],[f.STRONG,m.STRONG],[f.STYLE,m.STYLE],[f.SUB,m.SUB],[f.SUMMARY,m.SUMMARY],[f.SUP,m.SUP],[f.TABLE,m.TABLE],[f.TBODY,m.TBODY],[f.TEMPLATE,m.TEMPLATE],[f.TEXTAREA,m.TEXTAREA],[f.TFOOT,m.TFOOT],[f.TD,m.TD],[f.TH,m.TH],[f.THEAD,m.THEAD],[f.TITLE,m.TITLE],[f.TR,m.TR],[f.TRACK,m.TRACK],[f.TT,m.TT],[f.U,m.U],[f.UL,m.UL],[f.SVG,m.SVG],[f.VAR,m.VAR],[f.WBR,m.WBR],[f.XMP,m.XMP]]);function eF(e){var t;return null!=(t=eB.get(e))?t:m.UNKNOWN}let eH=m,eU={[h.HTML]:new Set([eH.ADDRESS,eH.APPLET,eH.AREA,eH.ARTICLE,eH.ASIDE,eH.BASE,eH.BASEFONT,eH.BGSOUND,eH.BLOCKQUOTE,eH.BODY,eH.BR,eH.BUTTON,eH.CAPTION,eH.CENTER,eH.COL,eH.COLGROUP,eH.DD,eH.DETAILS,eH.DIR,eH.DIV,eH.DL,eH.DT,eH.EMBED,eH.FIELDSET,eH.FIGCAPTION,eH.FIGURE,eH.FOOTER,eH.FORM,eH.FRAME,eH.FRAMESET,eH.H1,eH.H2,eH.H3,eH.H4,eH.H5,eH.H6,eH.HEAD,eH.HEADER,eH.HGROUP,eH.HR,eH.HTML,eH.IFRAME,eH.IMG,eH.INPUT,eH.LI,eH.LINK,eH.LISTING,eH.MAIN,eH.MARQUEE,eH.MENU,eH.META,eH.NAV,eH.NOEMBED,eH.NOFRAMES,eH.NOSCRIPT,eH.OBJECT,eH.OL,eH.P,eH.PARAM,eH.PLAINTEXT,eH.PRE,eH.SCRIPT,eH.SECTION,eH.SELECT,eH.SOURCE,eH.STYLE,eH.SUMMARY,eH.TABLE,eH.TBODY,eH.TD,eH.TEMPLATE,eH.TEXTAREA,eH.TFOOT,eH.TH,eH.THEAD,eH.TITLE,eH.TR,eH.TRACK,eH.UL,eH.WBR,eH.XMP]),[h.MATHML]:new Set([eH.MI,eH.MO,eH.MN,eH.MS,eH.MTEXT,eH.ANNOTATION_XML]),[h.SVG]:new Set([eH.TITLE,eH.FOREIGN_OBJECT,eH.DESC]),[h.XLINK]:new Set,[h.XML]:new Set,[h.XMLNS]:new Set},eG=new Set([eH.H1,eH.H2,eH.H3,eH.H4,eH.H5,eH.H6]);f.STYLE,f.SCRIPT,f.XMP,f.IFRAME,f.NOEMBED,f.NOFRAMES,f.PLAINTEXT,!function(e){e[e.DATA=0]="DATA",e[e.RCDATA=1]="RCDATA",e[e.RAWTEXT=2]="RAWTEXT",e[e.SCRIPT_DATA=3]="SCRIPT_DATA",e[e.PLAINTEXT=4]="PLAINTEXT",e[e.TAG_OPEN=5]="TAG_OPEN",e[e.END_TAG_OPEN=6]="END_TAG_OPEN",e[e.TAG_NAME=7]="TAG_NAME",e[e.RCDATA_LESS_THAN_SIGN=8]="RCDATA_LESS_THAN_SIGN",e[e.RCDATA_END_TAG_OPEN=9]="RCDATA_END_TAG_OPEN",e[e.RCDATA_END_TAG_NAME=10]="RCDATA_END_TAG_NAME",e[e.RAWTEXT_LESS_THAN_SIGN=11]="RAWTEXT_LESS_THAN_SIGN",e[e.RAWTEXT_END_TAG_OPEN=12]="RAWTEXT_END_TAG_OPEN",e[e.RAWTEXT_END_TAG_NAME=13]="RAWTEXT_END_TAG_NAME",e[e.SCRIPT_DATA_LESS_THAN_SIGN=14]="SCRIPT_DATA_LESS_THAN_SIGN",e[e.SCRIPT_DATA_END_TAG_OPEN=15]="SCRIPT_DATA_END_TAG_OPEN",e[e.SCRIPT_DATA_END_TAG_NAME=16]="SCRIPT_DATA_END_TAG_NAME",e[e.SCRIPT_DATA_ESCAPE_START=17]="SCRIPT_DATA_ESCAPE_START",e[e.SCRIPT_DATA_ESCAPE_START_DASH=18]="SCRIPT_DATA_ESCAPE_START_DASH",e[e.SCRIPT_DATA_ESCAPED=19]="SCRIPT_DATA_ESCAPED",e[e.SCRIPT_DATA_ESCAPED_DASH=20]="SCRIPT_DATA_ESCAPED_DASH",e[e.SCRIPT_DATA_ESCAPED_DASH_DASH=21]="SCRIPT_DATA_ESCAPED_DASH_DASH",e[e.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN=22]="SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN",e[e.SCRIPT_DATA_ESCAPED_END_TAG_OPEN=23]="SCRIPT_DATA_ESCAPED_END_TAG_OPEN",e[e.SCRIPT_DATA_ESCAPED_END_TAG_NAME=24]="SCRIPT_DATA_ESCAPED_END_TAG_NAME",e[e.SCRIPT_DATA_DOUBLE_ESCAPE_START=25]="SCRIPT_DATA_DOUBLE_ESCAPE_START",e[e.SCRIPT_DATA_DOUBLE_ESCAPED=26]="SCRIPT_DATA_DOUBLE_ESCAPED",e[e.SCRIPT_DATA_DOUBLE_ESCAPED_DASH=27]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH",e[e.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH=28]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH",e[e.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN=29]="SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN",e[e.SCRIPT_DATA_DOUBLE_ESCAPE_END=30]="SCRIPT_DATA_DOUBLE_ESCAPE_END",e[e.BEFORE_ATTRIBUTE_NAME=31]="BEFORE_ATTRIBUTE_NAME",e[e.ATTRIBUTE_NAME=32]="ATTRIBUTE_NAME",e[e.AFTER_ATTRIBUTE_NAME=33]="AFTER_ATTRIBUTE_NAME",e[e.BEFORE_ATTRIBUTE_VALUE=34]="BEFORE_ATTRIBUTE_VALUE",e[e.ATTRIBUTE_VALUE_DOUBLE_QUOTED=35]="ATTRIBUTE_VALUE_DOUBLE_QUOTED",e[e.ATTRIBUTE_VALUE_SINGLE_QUOTED=36]="ATTRIBUTE_VALUE_SINGLE_QUOTED",e[e.ATTRIBUTE_VALUE_UNQUOTED=37]="ATTRIBUTE_VALUE_UNQUOTED",e[e.AFTER_ATTRIBUTE_VALUE_QUOTED=38]="AFTER_ATTRIBUTE_VALUE_QUOTED",e[e.SELF_CLOSING_START_TAG=39]="SELF_CLOSING_START_TAG",e[e.BOGUS_COMMENT=40]="BOGUS_COMMENT",e[e.MARKUP_DECLARATION_OPEN=41]="MARKUP_DECLARATION_OPEN",e[e.COMMENT_START=42]="COMMENT_START",e[e.COMMENT_START_DASH=43]="COMMENT_START_DASH",e[e.COMMENT=44]="COMMENT",e[e.COMMENT_LESS_THAN_SIGN=45]="COMMENT_LESS_THAN_SIGN",e[e.COMMENT_LESS_THAN_SIGN_BANG=46]="COMMENT_LESS_THAN_SIGN_BANG",e[e.COMMENT_LESS_THAN_SIGN_BANG_DASH=47]="COMMENT_LESS_THAN_SIGN_BANG_DASH",e[e.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH=48]="COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH",e[e.COMMENT_END_DASH=49]="COMMENT_END_DASH",e[e.COMMENT_END=50]="COMMENT_END",e[e.COMMENT_END_BANG=51]="COMMENT_END_BANG",e[e.DOCTYPE=52]="DOCTYPE",e[e.BEFORE_DOCTYPE_NAME=53]="BEFORE_DOCTYPE_NAME",e[e.DOCTYPE_NAME=54]="DOCTYPE_NAME",e[e.AFTER_DOCTYPE_NAME=55]="AFTER_DOCTYPE_NAME",e[e.AFTER_DOCTYPE_PUBLIC_KEYWORD=56]="AFTER_DOCTYPE_PUBLIC_KEYWORD",e[e.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER=57]="BEFORE_DOCTYPE_PUBLIC_IDENTIFIER",e[e.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED=58]="DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED",e[e.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED=59]="DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED",e[e.AFTER_DOCTYPE_PUBLIC_IDENTIFIER=60]="AFTER_DOCTYPE_PUBLIC_IDENTIFIER",e[e.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS=61]="BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS",e[e.AFTER_DOCTYPE_SYSTEM_KEYWORD=62]="AFTER_DOCTYPE_SYSTEM_KEYWORD",e[e.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER=63]="BEFORE_DOCTYPE_SYSTEM_IDENTIFIER",e[e.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED=64]="DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED",e[e.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED=65]="DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED",e[e.AFTER_DOCTYPE_SYSTEM_IDENTIFIER=66]="AFTER_DOCTYPE_SYSTEM_IDENTIFIER",e[e.BOGUS_DOCTYPE=67]="BOGUS_DOCTYPE",e[e.CDATA_SECTION=68]="CDATA_SECTION",e[e.CDATA_SECTION_BRACKET=69]="CDATA_SECTION_BRACKET",e[e.CDATA_SECTION_END=70]="CDATA_SECTION_END",e[e.CHARACTER_REFERENCE=71]="CHARACTER_REFERENCE",e[e.AMBIGUOUS_AMPERSAND=72]="AMBIGUOUS_AMPERSAND"}(E||(E={}));let eY={DATA:E.DATA,RCDATA:E.RCDATA,RAWTEXT:E.RAWTEXT,SCRIPT_DATA:E.SCRIPT_DATA,PLAINTEXT:E.PLAINTEXT,CDATA_SECTION:E.CDATA_SECTION};function ez(e){return e>=r.LATIN_CAPITAL_A&&e<=r.LATIN_CAPITAL_Z}function eV(e){return e>=r.LATIN_SMALL_A&&e<=r.LATIN_SMALL_Z||ez(e)}function eq(e){return eV(e)||e>=r.DIGIT_0&&e<=r.DIGIT_9}function eW(e){return e===r.SPACE||e===r.LINE_FEED||e===r.TABULATION||e===r.FORM_FEED}function eQ(e){return eW(e)||e===r.SOLIDUS||e===r.GREATER_THAN_SIGN}class ej{constructor(e,t){this.options=e,this.handler=t,this.paused=!1,this.inLoop=!1,this.inForeignNode=!1,this.lastStartTagName="",this.active=!1,this.state=E.DATA,this.returnState=E.DATA,this.entityStartPos=0,this.consumedAfterSnapshot=-1,this.currentCharacterToken=null,this.currentToken=null,this.currentAttr={name:"",value:""},this.preprocessor=new eL(t),this.currentLocation=this.getCurrentLocation(-1),this.entityDecoder=new ew(eM,(e,t)=>{this.preprocessor.pos=this.entityStartPos+t-1,this._flushCodePointConsumedAsCharacterReference(e)},t.onParseError?{missingSemicolonAfterCharacterReference:()=>{this._err(i.missingSemicolonAfterCharacterReference,1)},absenceOfDigitsInNumericCharacterReference:e=>{this._err(i.absenceOfDigitsInNumericCharacterReference,this.entityStartPos-this.preprocessor.pos+e)},validateNumericCharacterReference:e=>{let t=function(e){if(e===r.NULL)return i.nullCharacterReference;if(e>1114111)return i.characterReferenceOutsideUnicodeRange;if(ey(e))return i.surrogateCharacterReference;if(eR(e))return i.noncharacterCharacterReference;if(eO(e)||e===r.CARRIAGE_RETURN)return i.controlCharacterReference;return null}(e);t&&this._err(t,1)}}:void 0)}_err(e,t=0){var n,r;null==(r=(n=this.handler).onParseError)||r.call(n,this.preprocessor.getError(e,t))}getCurrentLocation(e){return this.options.sourceCodeLocationInfo?{startLine:this.preprocessor.line,startCol:this.preprocessor.col-e,startOffset:this.preprocessor.offset-e,endLine:-1,endCol:-1,endOffset:-1}:null}_runParsingLoop(){if(!this.inLoop){for(this.inLoop=!0;this.active&&!this.paused;){this.consumedAfterSnapshot=0;let e=this._consume();this._ensureHibernation()||this._callState(e)}this.inLoop=!1}}pause(){this.paused=!0}resume(e){if(!this.paused)throw Error("Parser was already resumed");this.paused=!1,!this.inLoop&&(this._runParsingLoop(),this.paused||null==e||e())}write(e,t,n){this.active=!0,this.preprocessor.write(e,t),this._runParsingLoop(),this.paused||null==n||n()}insertHtmlAtCurrentPos(e){this.active=!0,this.preprocessor.insertHtmlAtCurrentPos(e),this._runParsingLoop()}_ensureHibernation(){return!!this.preprocessor.endOfChunkHit&&(this.preprocessor.retreat(this.consumedAfterSnapshot),this.consumedAfterSnapshot=0,this.active=!1,!0)}_consume(){return this.consumedAfterSnapshot++,this.preprocessor.advance()}_advanceBy(e){this.consumedAfterSnapshot+=e;for(let t=0;t<e;t++)this.preprocessor.advance()}_consumeSequenceIfMatch(e,t){return!!this.preprocessor.startsWith(e,t)&&(this._advanceBy(e.length-1),!0)}_createStartTagToken(){this.currentToken={type:a.START_TAG,tagName:"",tagID:m.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(1)}}_createEndTagToken(){this.currentToken={type:a.END_TAG,tagName:"",tagID:m.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(2)}}_createCommentToken(e){this.currentToken={type:a.COMMENT,data:"",location:this.getCurrentLocation(e)}}_createDoctypeToken(e){this.currentToken={type:a.DOCTYPE,name:e,forceQuirks:!1,publicId:null,systemId:null,location:this.currentLocation}}_createCharacterToken(e,t){this.currentCharacterToken={type:e,chars:t,location:this.currentLocation}}_createAttr(e){this.currentAttr={name:e,value:""},this.currentLocation=this.getCurrentLocation(0)}_leaveAttrName(){var e,t;let n=this.currentToken;null===eP(n,this.currentAttr.name)?(n.attrs.push(this.currentAttr),n.location&&this.currentLocation&&((null!=(e=(t=n.location).attrs)?e:t.attrs=Object.create(null))[this.currentAttr.name]=this.currentLocation,this._leaveAttrValue())):this._err(i.duplicateAttribute)}_leaveAttrValue(){this.currentLocation&&(this.currentLocation.endLine=this.preprocessor.line,this.currentLocation.endCol=this.preprocessor.col,this.currentLocation.endOffset=this.preprocessor.offset)}prepareToken(e){this._emitCurrentCharacterToken(e.location),this.currentToken=null,e.location&&(e.location.endLine=this.preprocessor.line,e.location.endCol=this.preprocessor.col+1,e.location.endOffset=this.preprocessor.offset+1),this.currentLocation=this.getCurrentLocation(-1)}emitCurrentTagToken(){let e=this.currentToken;this.prepareToken(e),e.tagID=eF(e.tagName),e.type===a.START_TAG?(this.lastStartTagName=e.tagName,this.handler.onStartTag(e)):(e.attrs.length>0&&this._err(i.endTagWithAttributes),e.selfClosing&&this._err(i.endTagWithTrailingSolidus),this.handler.onEndTag(e)),this.preprocessor.dropParsedChunk()}emitCurrentComment(e){this.prepareToken(e),this.handler.onComment(e),this.preprocessor.dropParsedChunk()}emitCurrentDoctype(e){this.prepareToken(e),this.handler.onDoctype(e),this.preprocessor.dropParsedChunk()}_emitCurrentCharacterToken(e){if(this.currentCharacterToken){switch(e&&this.currentCharacterToken.location&&(this.currentCharacterToken.location.endLine=e.startLine,this.currentCharacterToken.location.endCol=e.startCol,this.currentCharacterToken.location.endOffset=e.startOffset),this.currentCharacterToken.type){case a.CHARACTER:this.handler.onCharacter(this.currentCharacterToken);break;case a.NULL_CHARACTER:this.handler.onNullCharacter(this.currentCharacterToken);break;case a.WHITESPACE_CHARACTER:this.handler.onWhitespaceCharacter(this.currentCharacterToken)}this.currentCharacterToken=null}}_emitEOFToken(){let e=this.getCurrentLocation(0);e&&(e.endLine=e.startLine,e.endCol=e.startCol,e.endOffset=e.startOffset),this._emitCurrentCharacterToken(e),this.handler.onEof({type:a.EOF,location:e}),this.active=!1}_appendCharToCurrentCharacterToken(e,t){if(this.currentCharacterToken)if(this.currentCharacterToken.type===e){this.currentCharacterToken.chars+=t;return}else this.currentLocation=this.getCurrentLocation(0),this._emitCurrentCharacterToken(this.currentLocation),this.preprocessor.dropParsedChunk();this._createCharacterToken(e,t)}_emitCodePoint(e){let t=eW(e)?a.WHITESPACE_CHARACTER:e===r.NULL?a.NULL_CHARACTER:a.CHARACTER;this._appendCharToCurrentCharacterToken(t,String.fromCodePoint(e))}_emitChars(e){this._appendCharToCurrentCharacterToken(a.CHARACTER,e)}_startCharacterReference(){this.returnState=this.state,this.state=E.CHARACTER_REFERENCE,this.entityStartPos=this.preprocessor.pos,this.entityDecoder.startEntity(this._isCharacterReferenceInAttribute()?u.Attribute:u.Legacy)}_isCharacterReferenceInAttribute(){return this.returnState===E.ATTRIBUTE_VALUE_DOUBLE_QUOTED||this.returnState===E.ATTRIBUTE_VALUE_SINGLE_QUOTED||this.returnState===E.ATTRIBUTE_VALUE_UNQUOTED}_flushCodePointConsumedAsCharacterReference(e){this._isCharacterReferenceInAttribute()?this.currentAttr.value+=String.fromCodePoint(e):this._emitCodePoint(e)}_callState(e){switch(this.state){case E.DATA:this._stateData(e);break;case E.RCDATA:this._stateRcdata(e);break;case E.RAWTEXT:this._stateRawtext(e);break;case E.SCRIPT_DATA:this._stateScriptData(e);break;case E.PLAINTEXT:this._statePlaintext(e);break;case E.TAG_OPEN:this._stateTagOpen(e);break;case E.END_TAG_OPEN:this._stateEndTagOpen(e);break;case E.TAG_NAME:this._stateTagName(e);break;case E.RCDATA_LESS_THAN_SIGN:this._stateRcdataLessThanSign(e);break;case E.RCDATA_END_TAG_OPEN:this._stateRcdataEndTagOpen(e);break;case E.RCDATA_END_TAG_NAME:this._stateRcdataEndTagName(e);break;case E.RAWTEXT_LESS_THAN_SIGN:this._stateRawtextLessThanSign(e);break;case E.RAWTEXT_END_TAG_OPEN:this._stateRawtextEndTagOpen(e);break;case E.RAWTEXT_END_TAG_NAME:this._stateRawtextEndTagName(e);break;case E.SCRIPT_DATA_LESS_THAN_SIGN:this._stateScriptDataLessThanSign(e);break;case E.SCRIPT_DATA_END_TAG_OPEN:this._stateScriptDataEndTagOpen(e);break;case E.SCRIPT_DATA_END_TAG_NAME:this._stateScriptDataEndTagName(e);break;case E.SCRIPT_DATA_ESCAPE_START:this._stateScriptDataEscapeStart(e);break;case E.SCRIPT_DATA_ESCAPE_START_DASH:this._stateScriptDataEscapeStartDash(e);break;case E.SCRIPT_DATA_ESCAPED:this._stateScriptDataEscaped(e);break;case E.SCRIPT_DATA_ESCAPED_DASH:this._stateScriptDataEscapedDash(e);break;case E.SCRIPT_DATA_ESCAPED_DASH_DASH:this._stateScriptDataEscapedDashDash(e);break;case E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataEscapedLessThanSign(e);break;case E.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:this._stateScriptDataEscapedEndTagOpen(e);break;case E.SCRIPT_DATA_ESCAPED_END_TAG_NAME:this._stateScriptDataEscapedEndTagName(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPE_START:this._stateScriptDataDoubleEscapeStart(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPED:this._stateScriptDataDoubleEscaped(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH:this._stateScriptDataDoubleEscapedDash(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH:this._stateScriptDataDoubleEscapedDashDash(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataDoubleEscapedLessThanSign(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPE_END:this._stateScriptDataDoubleEscapeEnd(e);break;case E.BEFORE_ATTRIBUTE_NAME:this._stateBeforeAttributeName(e);break;case E.ATTRIBUTE_NAME:this._stateAttributeName(e);break;case E.AFTER_ATTRIBUTE_NAME:this._stateAfterAttributeName(e);break;case E.BEFORE_ATTRIBUTE_VALUE:this._stateBeforeAttributeValue(e);break;case E.ATTRIBUTE_VALUE_DOUBLE_QUOTED:this._stateAttributeValueDoubleQuoted(e);break;case E.ATTRIBUTE_VALUE_SINGLE_QUOTED:this._stateAttributeValueSingleQuoted(e);break;case E.ATTRIBUTE_VALUE_UNQUOTED:this._stateAttributeValueUnquoted(e);break;case E.AFTER_ATTRIBUTE_VALUE_QUOTED:this._stateAfterAttributeValueQuoted(e);break;case E.SELF_CLOSING_START_TAG:this._stateSelfClosingStartTag(e);break;case E.BOGUS_COMMENT:this._stateBogusComment(e);break;case E.MARKUP_DECLARATION_OPEN:this._stateMarkupDeclarationOpen(e);break;case E.COMMENT_START:this._stateCommentStart(e);break;case E.COMMENT_START_DASH:this._stateCommentStartDash(e);break;case E.COMMENT:this._stateComment(e);break;case E.COMMENT_LESS_THAN_SIGN:this._stateCommentLessThanSign(e);break;case E.COMMENT_LESS_THAN_SIGN_BANG:this._stateCommentLessThanSignBang(e);break;case E.COMMENT_LESS_THAN_SIGN_BANG_DASH:this._stateCommentLessThanSignBangDash(e);break;case E.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:this._stateCommentLessThanSignBangDashDash(e);break;case E.COMMENT_END_DASH:this._stateCommentEndDash(e);break;case E.COMMENT_END:this._stateCommentEnd(e);break;case E.COMMENT_END_BANG:this._stateCommentEndBang(e);break;case E.DOCTYPE:this._stateDoctype(e);break;case E.BEFORE_DOCTYPE_NAME:this._stateBeforeDoctypeName(e);break;case E.DOCTYPE_NAME:this._stateDoctypeName(e);break;case E.AFTER_DOCTYPE_NAME:this._stateAfterDoctypeName(e);break;case E.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._stateAfterDoctypePublicKeyword(e);break;case E.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER:this._stateBeforeDoctypePublicIdentifier(e);break;case E.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypePublicIdentifierDoubleQuoted(e);break;case E.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypePublicIdentifierSingleQuoted(e);break;case E.AFTER_DOCTYPE_PUBLIC_IDENTIFIER:this._stateAfterDoctypePublicIdentifier(e);break;case E.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS:this._stateBetweenDoctypePublicAndSystemIdentifiers(e);break;case E.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._stateAfterDoctypeSystemKeyword(e);break;case E.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER:this._stateBeforeDoctypeSystemIdentifier(e);break;case E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypeSystemIdentifierDoubleQuoted(e);break;case E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypeSystemIdentifierSingleQuoted(e);break;case E.AFTER_DOCTYPE_SYSTEM_IDENTIFIER:this._stateAfterDoctypeSystemIdentifier(e);break;case E.BOGUS_DOCTYPE:this._stateBogusDoctype(e);break;case E.CDATA_SECTION:this._stateCdataSection(e);break;case E.CDATA_SECTION_BRACKET:this._stateCdataSectionBracket(e);break;case E.CDATA_SECTION_END:this._stateCdataSectionEnd(e);break;case E.CHARACTER_REFERENCE:this._stateCharacterReference();break;case E.AMBIGUOUS_AMPERSAND:this._stateAmbiguousAmpersand(e);break;default:throw Error("Unknown state")}}_stateData(e){switch(e){case r.LESS_THAN_SIGN:this.state=E.TAG_OPEN;break;case r.AMPERSAND:this._startCharacterReference();break;case r.NULL:this._err(i.unexpectedNullCharacter),this._emitCodePoint(e);break;case r.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRcdata(e){switch(e){case r.AMPERSAND:this._startCharacterReference();break;case r.LESS_THAN_SIGN:this.state=E.RCDATA_LESS_THAN_SIGN;break;case r.NULL:this._err(i.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRawtext(e){switch(e){case r.LESS_THAN_SIGN:this.state=E.RAWTEXT_LESS_THAN_SIGN;break;case r.NULL:this._err(i.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptData(e){switch(e){case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_LESS_THAN_SIGN;break;case r.NULL:this._err(i.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_statePlaintext(e){switch(e){case r.NULL:this._err(i.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateTagOpen(e){if(eV(e))this._createStartTagToken(),this.state=E.TAG_NAME,this._stateTagName(e);else switch(e){case r.EXCLAMATION_MARK:this.state=E.MARKUP_DECLARATION_OPEN;break;case r.SOLIDUS:this.state=E.END_TAG_OPEN;break;case r.QUESTION_MARK:this._err(i.unexpectedQuestionMarkInsteadOfTagName),this._createCommentToken(1),this.state=E.BOGUS_COMMENT,this._stateBogusComment(e);break;case r.EOF:this._err(i.eofBeforeTagName),this._emitChars("<"),this._emitEOFToken();break;default:this._err(i.invalidFirstCharacterOfTagName),this._emitChars("<"),this.state=E.DATA,this._stateData(e)}}_stateEndTagOpen(e){if(eV(e))this._createEndTagToken(),this.state=E.TAG_NAME,this._stateTagName(e);else switch(e){case r.GREATER_THAN_SIGN:this._err(i.missingEndTagName),this.state=E.DATA;break;case r.EOF:this._err(i.eofBeforeTagName),this._emitChars("</"),this._emitEOFToken();break;default:this._err(i.invalidFirstCharacterOfTagName),this._createCommentToken(2),this.state=E.BOGUS_COMMENT,this._stateBogusComment(e)}}_stateTagName(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.BEFORE_ATTRIBUTE_NAME;break;case r.SOLIDUS:this.state=E.SELF_CLOSING_START_TAG;break;case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentTagToken();break;case r.NULL:this._err(i.unexpectedNullCharacter),t.tagName+="�";break;case r.EOF:this._err(i.eofInTag),this._emitEOFToken();break;default:t.tagName+=String.fromCodePoint(ez(e)?e+32:e)}}_stateRcdataLessThanSign(e){e===r.SOLIDUS?this.state=E.RCDATA_END_TAG_OPEN:(this._emitChars("<"),this.state=E.RCDATA,this._stateRcdata(e))}_stateRcdataEndTagOpen(e){eV(e)?(this.state=E.RCDATA_END_TAG_NAME,this._stateRcdataEndTagName(e)):(this._emitChars("</"),this.state=E.RCDATA,this._stateRcdata(e))}handleSpecialEndTag(e){if(!this.preprocessor.startsWith(this.lastStartTagName,!1))return!this._ensureHibernation();switch(this._createEndTagToken(),this.currentToken.tagName=this.lastStartTagName,this.preprocessor.peek(this.lastStartTagName.length)){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:return this._advanceBy(this.lastStartTagName.length),this.state=E.BEFORE_ATTRIBUTE_NAME,!1;case r.SOLIDUS:return this._advanceBy(this.lastStartTagName.length),this.state=E.SELF_CLOSING_START_TAG,!1;case r.GREATER_THAN_SIGN:return this._advanceBy(this.lastStartTagName.length),this.emitCurrentTagToken(),this.state=E.DATA,!1;default:return!this._ensureHibernation()}}_stateRcdataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=E.RCDATA,this._stateRcdata(e))}_stateRawtextLessThanSign(e){e===r.SOLIDUS?this.state=E.RAWTEXT_END_TAG_OPEN:(this._emitChars("<"),this.state=E.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagOpen(e){eV(e)?(this.state=E.RAWTEXT_END_TAG_NAME,this._stateRawtextEndTagName(e)):(this._emitChars("</"),this.state=E.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=E.RAWTEXT,this._stateRawtext(e))}_stateScriptDataLessThanSign(e){switch(e){case r.SOLIDUS:this.state=E.SCRIPT_DATA_END_TAG_OPEN;break;case r.EXCLAMATION_MARK:this.state=E.SCRIPT_DATA_ESCAPE_START,this._emitChars("<!");break;default:this._emitChars("<"),this.state=E.SCRIPT_DATA,this._stateScriptData(e)}}_stateScriptDataEndTagOpen(e){eV(e)?(this.state=E.SCRIPT_DATA_END_TAG_NAME,this._stateScriptDataEndTagName(e)):(this._emitChars("</"),this.state=E.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=E.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStart(e){e===r.HYPHEN_MINUS?(this.state=E.SCRIPT_DATA_ESCAPE_START_DASH,this._emitChars("-")):(this.state=E.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStartDash(e){e===r.HYPHEN_MINUS?(this.state=E.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-")):(this.state=E.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscaped(e){switch(e){case r.HYPHEN_MINUS:this.state=E.SCRIPT_DATA_ESCAPED_DASH,this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case r.NULL:this._err(i.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._err(i.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataEscapedDash(e){switch(e){case r.HYPHEN_MINUS:this.state=E.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case r.NULL:this._err(i.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_ESCAPED,this._emitChars("�");break;case r.EOF:this._err(i.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=E.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedDashDash(e){switch(e){case r.HYPHEN_MINUS:this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case r.GREATER_THAN_SIGN:this.state=E.SCRIPT_DATA,this._emitChars(">");break;case r.NULL:this._err(i.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_ESCAPED,this._emitChars("�");break;case r.EOF:this._err(i.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=E.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedLessThanSign(e){e===r.SOLIDUS?this.state=E.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:eV(e)?(this._emitChars("<"),this.state=E.SCRIPT_DATA_DOUBLE_ESCAPE_START,this._stateScriptDataDoubleEscapeStart(e)):(this._emitChars("<"),this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagOpen(e){eV(e)?(this.state=E.SCRIPT_DATA_ESCAPED_END_TAG_NAME,this._stateScriptDataEscapedEndTagName(e)):(this._emitChars("</"),this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscapeStart(e){if(this.preprocessor.startsWith(eb.SCRIPT,!1)&&eQ(this.preprocessor.peek(eb.SCRIPT.length))){this._emitCodePoint(e);for(let e=0;e<eb.SCRIPT.length;e++)this._emitCodePoint(this._consume());this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED}else this._ensureHibernation()||(this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscaped(e){switch(e){case r.HYPHEN_MINUS:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH,this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case r.NULL:this._err(i.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._err(i.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDash(e){switch(e){case r.HYPHEN_MINUS:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH,this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case r.NULL:this._err(i.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars("�");break;case r.EOF:this._err(i.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDashDash(e){switch(e){case r.HYPHEN_MINUS:this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case r.GREATER_THAN_SIGN:this.state=E.SCRIPT_DATA,this._emitChars(">");break;case r.NULL:this._err(i.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars("�");break;case r.EOF:this._err(i.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedLessThanSign(e){e===r.SOLIDUS?(this.state=E.SCRIPT_DATA_DOUBLE_ESCAPE_END,this._emitChars("/")):(this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateScriptDataDoubleEscapeEnd(e){if(this.preprocessor.startsWith(eb.SCRIPT,!1)&&eQ(this.preprocessor.peek(eb.SCRIPT.length))){this._emitCodePoint(e);for(let e=0;e<eb.SCRIPT.length;e++)this._emitCodePoint(this._consume());this.state=E.SCRIPT_DATA_ESCAPED}else this._ensureHibernation()||(this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateBeforeAttributeName(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.SOLIDUS:case r.GREATER_THAN_SIGN:case r.EOF:this.state=E.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case r.EQUALS_SIGN:this._err(i.unexpectedEqualsSignBeforeAttributeName),this._createAttr("="),this.state=E.ATTRIBUTE_NAME;break;default:this._createAttr(""),this.state=E.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateAttributeName(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:case r.SOLIDUS:case r.GREATER_THAN_SIGN:case r.EOF:this._leaveAttrName(),this.state=E.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case r.EQUALS_SIGN:this._leaveAttrName(),this.state=E.BEFORE_ATTRIBUTE_VALUE;break;case r.QUOTATION_MARK:case r.APOSTROPHE:case r.LESS_THAN_SIGN:this._err(i.unexpectedCharacterInAttributeName),this.currentAttr.name+=String.fromCodePoint(e);break;case r.NULL:this._err(i.unexpectedNullCharacter),this.currentAttr.name+="�";break;default:this.currentAttr.name+=String.fromCodePoint(ez(e)?e+32:e)}}_stateAfterAttributeName(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.SOLIDUS:this.state=E.SELF_CLOSING_START_TAG;break;case r.EQUALS_SIGN:this.state=E.BEFORE_ATTRIBUTE_VALUE;break;case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentTagToken();break;case r.EOF:this._err(i.eofInTag),this._emitEOFToken();break;default:this._createAttr(""),this.state=E.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateBeforeAttributeValue(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.QUOTATION_MARK:this.state=E.ATTRIBUTE_VALUE_DOUBLE_QUOTED;break;case r.APOSTROPHE:this.state=E.ATTRIBUTE_VALUE_SINGLE_QUOTED;break;case r.GREATER_THAN_SIGN:this._err(i.missingAttributeValue),this.state=E.DATA,this.emitCurrentTagToken();break;default:this.state=E.ATTRIBUTE_VALUE_UNQUOTED,this._stateAttributeValueUnquoted(e)}}_stateAttributeValueDoubleQuoted(e){switch(e){case r.QUOTATION_MARK:this.state=E.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case r.AMPERSAND:this._startCharacterReference();break;case r.NULL:this._err(i.unexpectedNullCharacter),this.currentAttr.value+="�";break;case r.EOF:this._err(i.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueSingleQuoted(e){switch(e){case r.APOSTROPHE:this.state=E.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case r.AMPERSAND:this._startCharacterReference();break;case r.NULL:this._err(i.unexpectedNullCharacter),this.currentAttr.value+="�";break;case r.EOF:this._err(i.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueUnquoted(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this._leaveAttrValue(),this.state=E.BEFORE_ATTRIBUTE_NAME;break;case r.AMPERSAND:this._startCharacterReference();break;case r.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=E.DATA,this.emitCurrentTagToken();break;case r.NULL:this._err(i.unexpectedNullCharacter),this.currentAttr.value+="�";break;case r.QUOTATION_MARK:case r.APOSTROPHE:case r.LESS_THAN_SIGN:case r.EQUALS_SIGN:case r.GRAVE_ACCENT:this._err(i.unexpectedCharacterInUnquotedAttributeValue),this.currentAttr.value+=String.fromCodePoint(e);break;case r.EOF:this._err(i.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAfterAttributeValueQuoted(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this._leaveAttrValue(),this.state=E.BEFORE_ATTRIBUTE_NAME;break;case r.SOLIDUS:this._leaveAttrValue(),this.state=E.SELF_CLOSING_START_TAG;break;case r.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=E.DATA,this.emitCurrentTagToken();break;case r.EOF:this._err(i.eofInTag),this._emitEOFToken();break;default:this._err(i.missingWhitespaceBetweenAttributes),this.state=E.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateSelfClosingStartTag(e){switch(e){case r.GREATER_THAN_SIGN:this.currentToken.selfClosing=!0,this.state=E.DATA,this.emitCurrentTagToken();break;case r.EOF:this._err(i.eofInTag),this._emitEOFToken();break;default:this._err(i.unexpectedSolidusInTag),this.state=E.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateBogusComment(e){let t=this.currentToken;switch(e){case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentComment(t);break;case r.EOF:this.emitCurrentComment(t),this._emitEOFToken();break;case r.NULL:this._err(i.unexpectedNullCharacter),t.data+="�";break;default:t.data+=String.fromCodePoint(e)}}_stateMarkupDeclarationOpen(e){this._consumeSequenceIfMatch(eb.DASH_DASH,!0)?(this._createCommentToken(eb.DASH_DASH.length+1),this.state=E.COMMENT_START):this._consumeSequenceIfMatch(eb.DOCTYPE,!1)?(this.currentLocation=this.getCurrentLocation(eb.DOCTYPE.length+1),this.state=E.DOCTYPE):this._consumeSequenceIfMatch(eb.CDATA_START,!0)?this.inForeignNode?this.state=E.CDATA_SECTION:(this._err(i.cdataInHtmlContent),this._createCommentToken(eb.CDATA_START.length+1),this.currentToken.data="[CDATA[",this.state=E.BOGUS_COMMENT):this._ensureHibernation()||(this._err(i.incorrectlyOpenedComment),this._createCommentToken(2),this.state=E.BOGUS_COMMENT,this._stateBogusComment(e))}_stateCommentStart(e){switch(e){case r.HYPHEN_MINUS:this.state=E.COMMENT_START_DASH;break;case r.GREATER_THAN_SIGN:{this._err(i.abruptClosingOfEmptyComment),this.state=E.DATA;let e=this.currentToken;this.emitCurrentComment(e);break}default:this.state=E.COMMENT,this._stateComment(e)}}_stateCommentStartDash(e){let t=this.currentToken;switch(e){case r.HYPHEN_MINUS:this.state=E.COMMENT_END;break;case r.GREATER_THAN_SIGN:this._err(i.abruptClosingOfEmptyComment),this.state=E.DATA,this.emitCurrentComment(t);break;case r.EOF:this._err(i.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=E.COMMENT,this._stateComment(e)}}_stateComment(e){let t=this.currentToken;switch(e){case r.HYPHEN_MINUS:this.state=E.COMMENT_END_DASH;break;case r.LESS_THAN_SIGN:t.data+="<",this.state=E.COMMENT_LESS_THAN_SIGN;break;case r.NULL:this._err(i.unexpectedNullCharacter),t.data+="�";break;case r.EOF:this._err(i.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+=String.fromCodePoint(e)}}_stateCommentLessThanSign(e){let t=this.currentToken;switch(e){case r.EXCLAMATION_MARK:t.data+="!",this.state=E.COMMENT_LESS_THAN_SIGN_BANG;break;case r.LESS_THAN_SIGN:t.data+="<";break;default:this.state=E.COMMENT,this._stateComment(e)}}_stateCommentLessThanSignBang(e){e===r.HYPHEN_MINUS?this.state=E.COMMENT_LESS_THAN_SIGN_BANG_DASH:(this.state=E.COMMENT,this._stateComment(e))}_stateCommentLessThanSignBangDash(e){e===r.HYPHEN_MINUS?this.state=E.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:(this.state=E.COMMENT_END_DASH,this._stateCommentEndDash(e))}_stateCommentLessThanSignBangDashDash(e){e!==r.GREATER_THAN_SIGN&&e!==r.EOF&&this._err(i.nestedComment),this.state=E.COMMENT_END,this._stateCommentEnd(e)}_stateCommentEndDash(e){let t=this.currentToken;switch(e){case r.HYPHEN_MINUS:this.state=E.COMMENT_END;break;case r.EOF:this._err(i.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=E.COMMENT,this._stateComment(e)}}_stateCommentEnd(e){let t=this.currentToken;switch(e){case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentComment(t);break;case r.EXCLAMATION_MARK:this.state=E.COMMENT_END_BANG;break;case r.HYPHEN_MINUS:t.data+="-";break;case r.EOF:this._err(i.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--",this.state=E.COMMENT,this._stateComment(e)}}_stateCommentEndBang(e){let t=this.currentToken;switch(e){case r.HYPHEN_MINUS:t.data+="--!",this.state=E.COMMENT_END_DASH;break;case r.GREATER_THAN_SIGN:this._err(i.incorrectlyClosedComment),this.state=E.DATA,this.emitCurrentComment(t);break;case r.EOF:this._err(i.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--!",this.state=E.COMMENT,this._stateComment(e)}}_stateDoctype(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.BEFORE_DOCTYPE_NAME;break;case r.GREATER_THAN_SIGN:this.state=E.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e);break;case r.EOF:{this._err(i.eofInDoctype),this._createDoctypeToken(null);let e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._err(i.missingWhitespaceBeforeDoctypeName),this.state=E.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e)}}_stateBeforeDoctypeName(e){if(ez(e))this._createDoctypeToken(String.fromCharCode(e+32)),this.state=E.DOCTYPE_NAME;else switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.NULL:this._err(i.unexpectedNullCharacter),this._createDoctypeToken("�"),this.state=E.DOCTYPE_NAME;break;case r.GREATER_THAN_SIGN:{this._err(i.missingDoctypeName),this._createDoctypeToken(null);let e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this.state=E.DATA;break}case r.EOF:{this._err(i.eofInDoctype),this._createDoctypeToken(null);let e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._createDoctypeToken(String.fromCodePoint(e)),this.state=E.DOCTYPE_NAME}}_stateDoctypeName(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.AFTER_DOCTYPE_NAME;break;case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.NULL:this._err(i.unexpectedNullCharacter),t.name+="�";break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.name+=String.fromCodePoint(ez(e)?e+32:e)}}_stateAfterDoctypeName(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._consumeSequenceIfMatch(eb.PUBLIC,!1)?this.state=E.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._consumeSequenceIfMatch(eb.SYSTEM,!1)?this.state=E.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._ensureHibernation()||(this._err(i.invalidCharacterSequenceAfterDoctypeName),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e))}}_stateAfterDoctypePublicKeyword(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER;break;case r.QUOTATION_MARK:this._err(i.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:this._err(i.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case r.GREATER_THAN_SIGN:this._err(i.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(i.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypePublicIdentifier(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.QUOTATION_MARK:t.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:t.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case r.GREATER_THAN_SIGN:this._err(i.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(i.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypePublicIdentifierDoubleQuoted(e){let t=this.currentToken;switch(e){case r.QUOTATION_MARK:this.state=E.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case r.NULL:this._err(i.unexpectedNullCharacter),t.publicId+="�";break;case r.GREATER_THAN_SIGN:this._err(i.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateDoctypePublicIdentifierSingleQuoted(e){let t=this.currentToken;switch(e){case r.APOSTROPHE:this.state=E.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case r.NULL:this._err(i.unexpectedNullCharacter),t.publicId+="�";break;case r.GREATER_THAN_SIGN:this._err(i.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateAfterDoctypePublicIdentifier(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS;break;case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.QUOTATION_MARK:this._err(i.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:this._err(i.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(i.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBetweenDoctypePublicAndSystemIdentifiers(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.QUOTATION_MARK:t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(i.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateAfterDoctypeSystemKeyword(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER;break;case r.QUOTATION_MARK:this._err(i.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:this._err(i.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case r.GREATER_THAN_SIGN:this._err(i.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(i.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypeSystemIdentifier(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.QUOTATION_MARK:t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case r.GREATER_THAN_SIGN:this._err(i.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(i.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypeSystemIdentifierDoubleQuoted(e){let t=this.currentToken;switch(e){case r.QUOTATION_MARK:this.state=E.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case r.NULL:this._err(i.unexpectedNullCharacter),t.systemId+="�";break;case r.GREATER_THAN_SIGN:this._err(i.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateDoctypeSystemIdentifierSingleQuoted(e){let t=this.currentToken;switch(e){case r.APOSTROPHE:this.state=E.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case r.NULL:this._err(i.unexpectedNullCharacter),t.systemId+="�";break;case r.GREATER_THAN_SIGN:this._err(i.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateAfterDoctypeSystemIdentifier(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.EOF:this._err(i.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(i.unexpectedCharacterAfterDoctypeSystemIdentifier),this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBogusDoctype(e){let t=this.currentToken;switch(e){case r.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.NULL:this._err(i.unexpectedNullCharacter);break;case r.EOF:this.emitCurrentDoctype(t),this._emitEOFToken()}}_stateCdataSection(e){switch(e){case r.RIGHT_SQUARE_BRACKET:this.state=E.CDATA_SECTION_BRACKET;break;case r.EOF:this._err(i.eofInCdata),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateCdataSectionBracket(e){e===r.RIGHT_SQUARE_BRACKET?this.state=E.CDATA_SECTION_END:(this._emitChars("]"),this.state=E.CDATA_SECTION,this._stateCdataSection(e))}_stateCdataSectionEnd(e){switch(e){case r.GREATER_THAN_SIGN:this.state=E.DATA;break;case r.RIGHT_SQUARE_BRACKET:this._emitChars("]");break;default:this._emitChars("]]"),this.state=E.CDATA_SECTION,this._stateCdataSection(e)}}_stateCharacterReference(){let e=this.entityDecoder.write(this.preprocessor.html,this.preprocessor.pos);if(e<0)if(this.preprocessor.lastChunkWritten)e=this.entityDecoder.end();else{this.active=!1,this.preprocessor.pos=this.preprocessor.html.length-1,this.consumedAfterSnapshot=0,this.preprocessor.endOfChunkHit=!0;return}0===e?(this.preprocessor.pos=this.entityStartPos,this._flushCodePointConsumedAsCharacterReference(r.AMPERSAND),this.state=!this._isCharacterReferenceInAttribute()&&eq(this.preprocessor.peek(1))?E.AMBIGUOUS_AMPERSAND:this.returnState):this.state=this.returnState}_stateAmbiguousAmpersand(e){eq(e)?this._flushCodePointConsumedAsCharacterReference(e):(e===r.SEMICOLON&&this._err(i.unknownNamedCharacterReference),this.state=this.returnState,this._callState(e))}}let eK=new Set([m.DD,m.DT,m.LI,m.OPTGROUP,m.OPTION,m.P,m.RB,m.RP,m.RT,m.RTC]),eX=new Set([...eK,m.CAPTION,m.COLGROUP,m.TBODY,m.TD,m.TFOOT,m.TH,m.THEAD,m.TR]),eJ=new Set([m.APPLET,m.CAPTION,m.HTML,m.MARQUEE,m.OBJECT,m.TABLE,m.TD,m.TEMPLATE,m.TH]),eZ=new Set([...eJ,m.OL,m.UL]),e$=new Set([...eJ,m.BUTTON]),e0=new Set([m.ANNOTATION_XML,m.MI,m.MN,m.MO,m.MS,m.MTEXT]),e1=new Set([m.DESC,m.FOREIGN_OBJECT,m.TITLE]),e3=new Set([m.TR,m.TEMPLATE,m.HTML]),e2=new Set([m.TBODY,m.TFOOT,m.THEAD,m.TEMPLATE,m.HTML]),e5=new Set([m.TABLE,m.TEMPLATE,m.HTML]),e4=new Set([m.TD,m.TH]);class e8{get currentTmplContentOrNode(){return this._isInTemplate()?this.treeAdapter.getTemplateContent(this.current):this.current}constructor(e,t,n){this.treeAdapter=t,this.handler=n,this.items=[],this.tagIDs=[],this.stackTop=-1,this.tmplCount=0,this.currentTagId=m.UNKNOWN,this.current=e}_indexOf(e){return this.items.lastIndexOf(e,this.stackTop)}_isInTemplate(){return this.currentTagId===m.TEMPLATE&&this.treeAdapter.getNamespaceURI(this.current)===h.HTML}_updateCurrentElement(){this.current=this.items[this.stackTop],this.currentTagId=this.tagIDs[this.stackTop]}push(e,t){this.stackTop++,this.items[this.stackTop]=e,this.current=e,this.tagIDs[this.stackTop]=t,this.currentTagId=t,this._isInTemplate()&&this.tmplCount++,this.handler.onItemPush(e,t,!0)}pop(){let e=this.current;this.tmplCount>0&&this._isInTemplate()&&this.tmplCount--,this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!0)}replace(e,t){let n=this._indexOf(e);this.items[n]=t,n===this.stackTop&&(this.current=t)}insertAfter(e,t,n){let r=this._indexOf(e)+1;this.items.splice(r,0,t),this.tagIDs.splice(r,0,n),this.stackTop++,r===this.stackTop&&this._updateCurrentElement(),this.current&&void 0!==this.currentTagId&&this.handler.onItemPush(this.current,this.currentTagId,r===this.stackTop)}popUntilTagNamePopped(e){let t=this.stackTop+1;do t=this.tagIDs.lastIndexOf(e,t-1);while(t>0&&this.treeAdapter.getNamespaceURI(this.items[t])!==h.HTML);this.shortenToLength(Math.max(t,0))}shortenToLength(e){for(;this.stackTop>=e;){let t=this.current;this.tmplCount>0&&this._isInTemplate()&&(this.tmplCount-=1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(t,this.stackTop<e)}}popUntilElementPopped(e){let t=this._indexOf(e);this.shortenToLength(Math.max(t,0))}popUntilPopped(e,t){let n=this._indexOfTagNames(e,t);this.shortenToLength(Math.max(n,0))}popUntilNumberedHeaderPopped(){this.popUntilPopped(eG,h.HTML)}popUntilTableCellPopped(){this.popUntilPopped(e4,h.HTML)}popAllUpToHtmlElement(){this.tmplCount=0,this.shortenToLength(1)}_indexOfTagNames(e,t){for(let n=this.stackTop;n>=0;n--)if(e.has(this.tagIDs[n])&&this.treeAdapter.getNamespaceURI(this.items[n])===t)return n;return -1}clearBackTo(e,t){let n=this._indexOfTagNames(e,t);this.shortenToLength(n+1)}clearBackToTableContext(){this.clearBackTo(e5,h.HTML)}clearBackToTableBodyContext(){this.clearBackTo(e2,h.HTML)}clearBackToTableRowContext(){this.clearBackTo(e3,h.HTML)}remove(e){let t=this._indexOf(e);t>=0&&(t===this.stackTop?this.pop():(this.items.splice(t,1),this.tagIDs.splice(t,1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!1)))}tryPeekProperlyNestedBodyElement(){return this.stackTop>=1&&this.tagIDs[1]===m.BODY?this.items[1]:null}contains(e){return this._indexOf(e)>-1}getCommonAncestor(e){let t=this._indexOf(e)-1;return t>=0?this.items[t]:null}isRootHtmlElementCurrent(){return 0===this.stackTop&&this.tagIDs[0]===m.HTML}hasInDynamicScope(e,t){for(let n=this.stackTop;n>=0;n--){let r=this.tagIDs[n];switch(this.treeAdapter.getNamespaceURI(this.items[n])){case h.HTML:if(r===e)return!0;if(t.has(r))return!1;break;case h.SVG:if(e1.has(r))return!1;break;case h.MATHML:if(e0.has(r))return!1}}return!0}hasInScope(e){return this.hasInDynamicScope(e,eJ)}hasInListItemScope(e){return this.hasInDynamicScope(e,eZ)}hasInButtonScope(e){return this.hasInDynamicScope(e,e$)}hasNumberedHeaderInScope(){for(let e=this.stackTop;e>=0;e--){let t=this.tagIDs[e];switch(this.treeAdapter.getNamespaceURI(this.items[e])){case h.HTML:if(eG.has(t))return!0;if(eJ.has(t))return!1;break;case h.SVG:if(e1.has(t))return!1;break;case h.MATHML:if(e0.has(t))return!1}}return!0}hasInTableScope(e){for(let t=this.stackTop;t>=0;t--)if(this.treeAdapter.getNamespaceURI(this.items[t])===h.HTML)switch(this.tagIDs[t]){case e:return!0;case m.TABLE:case m.HTML:return!1}return!0}hasTableBodyContextInTableScope(){for(let e=this.stackTop;e>=0;e--)if(this.treeAdapter.getNamespaceURI(this.items[e])===h.HTML)switch(this.tagIDs[e]){case m.TBODY:case m.THEAD:case m.TFOOT:return!0;case m.TABLE:case m.HTML:return!1}return!0}hasInSelectScope(e){for(let t=this.stackTop;t>=0;t--)if(this.treeAdapter.getNamespaceURI(this.items[t])===h.HTML)switch(this.tagIDs[t]){case e:return!0;case m.OPTION:case m.OPTGROUP:break;default:return!1}return!0}generateImpliedEndTags(){for(;void 0!==this.currentTagId&&eK.has(this.currentTagId);)this.pop()}generateImpliedEndTagsThoroughly(){for(;void 0!==this.currentTagId&&eX.has(this.currentTagId);)this.pop()}generateImpliedEndTagsWithExclusion(e){for(;void 0!==this.currentTagId&&this.currentTagId!==e&&eX.has(this.currentTagId);)this.pop()}}!function(e){e[e.Marker=0]="Marker",e[e.Element=1]="Element"}(T||(T={}));let e9={type:T.Marker};class e6{constructor(e){this.treeAdapter=e,this.entries=[],this.bookmark=null}_getNoahArkConditionCandidates(e,t){let n=[],r=t.length,i=this.treeAdapter.getTagName(e),a=this.treeAdapter.getNamespaceURI(e);for(let e=0;e<this.entries.length;e++){let t=this.entries[e];if(t.type===T.Marker)break;let{element:s}=t;if(this.treeAdapter.getTagName(s)===i&&this.treeAdapter.getNamespaceURI(s)===a){let t=this.treeAdapter.getAttrList(s);t.length===r&&n.push({idx:e,attrs:t})}}return n}_ensureNoahArkCondition(e){if(this.entries.length<3)return;let t=this.treeAdapter.getAttrList(e),n=this._getNoahArkConditionCandidates(e,t);if(n.length<3)return;let r=new Map(t.map(e=>[e.name,e.value])),i=0;for(let e=0;e<n.length;e++){let t=n[e];t.attrs.every(e=>r.get(e.name)===e.value)&&(i+=1)>=3&&this.entries.splice(t.idx,1)}}insertMarker(){this.entries.unshift(e9)}pushElement(e,t){this._ensureNoahArkCondition(e),this.entries.unshift({type:T.Element,element:e,token:t})}insertElementAfterBookmark(e,t){let n=this.entries.indexOf(this.bookmark);this.entries.splice(n,0,{type:T.Element,element:e,token:t})}removeEntry(e){let t=this.entries.indexOf(e);-1!==t&&this.entries.splice(t,1)}clearToLastMarker(){let e=this.entries.indexOf(e9);-1===e?this.entries.length=0:this.entries.splice(0,e+1)}getElementEntryInScopeWithTagName(e){let t=this.entries.find(t=>t.type===T.Marker||this.treeAdapter.getTagName(t.element)===e);return t&&t.type===T.Element?t:null}getElementEntry(e){return this.entries.find(t=>t.type===T.Element&&t.element===e)}}let e7={createDocument:()=>({nodeName:"#document",mode:p.NO_QUIRKS,childNodes:[]}),createDocumentFragment:()=>({nodeName:"#document-fragment",childNodes:[]}),createElement:(e,t,n)=>({nodeName:e,tagName:e,attrs:n,namespaceURI:t,childNodes:[],parentNode:null}),createCommentNode:e=>({nodeName:"#comment",data:e,parentNode:null}),createTextNode:e=>({nodeName:"#text",value:e,parentNode:null}),appendChild(e,t){e.childNodes.push(t),t.parentNode=e},insertBefore(e,t,n){let r=e.childNodes.indexOf(n);e.childNodes.splice(r,0,t),t.parentNode=e},setTemplateContent(e,t){e.content=t},getTemplateContent:e=>e.content,setDocumentType(e,t,n,r){let i=e.childNodes.find(e=>"#documentType"===e.nodeName);i?(i.name=t,i.publicId=n,i.systemId=r):e7.appendChild(e,{nodeName:"#documentType",name:t,publicId:n,systemId:r,parentNode:null})},setDocumentMode(e,t){e.mode=t},getDocumentMode:e=>e.mode,detachNode(e){if(e.parentNode){let t=e.parentNode.childNodes.indexOf(e);e.parentNode.childNodes.splice(t,1),e.parentNode=null}},insertText(e,t){if(e.childNodes.length>0){let n=e.childNodes[e.childNodes.length-1];if(e7.isTextNode(n)){n.value+=t;return}}e7.appendChild(e,e7.createTextNode(t))},insertTextBefore(e,t,n){let r=e.childNodes[e.childNodes.indexOf(n)-1];r&&e7.isTextNode(r)?r.value+=t:e7.insertBefore(e,e7.createTextNode(t),n)},adoptAttributes(e,t){let n=new Set(e.attrs.map(e=>e.name));for(let r=0;r<t.length;r++)n.has(t[r].name)||e.attrs.push(t[r])},getFirstChild:e=>e.childNodes[0],getChildNodes:e=>e.childNodes,getParentNode:e=>e.parentNode,getAttrList:e=>e.attrs,getTagName:e=>e.tagName,getNamespaceURI:e=>e.namespaceURI,getTextNodeContent:e=>e.value,getCommentNodeContent:e=>e.data,getDocumentTypeNodeName:e=>e.name,getDocumentTypeNodePublicId:e=>e.publicId,getDocumentTypeNodeSystemId:e=>e.systemId,isTextNode:e=>"#text"===e.nodeName,isCommentNode:e=>"#comment"===e.nodeName,isDocumentTypeNode:e=>"#documentType"===e.nodeName,isElementNode:e=>Object.prototype.hasOwnProperty.call(e,"tagName"),setNodeSourceCodeLocation(e,t){e.sourceCodeLocation=t},getNodeSourceCodeLocation:e=>e.sourceCodeLocation,updateNodeSourceCodeLocation(e,t){e.sourceCodeLocation={...e.sourceCodeLocation,...t}}},te="html",tt=["+//silmaril//dtd html pro v0r11 19970101//","-//as//dtd html 3.0 aswedit + extensions//","-//advasoft ltd//dtd html 3.0 aswedit + extensions//","-//ietf//dtd html 2.0 level 1//","-//ietf//dtd html 2.0 level 2//","-//ietf//dtd html 2.0 strict level 1//","-//ietf//dtd html 2.0 strict level 2//","-//ietf//dtd html 2.0 strict//","-//ietf//dtd html 2.0//","-//ietf//dtd html 2.1e//","-//ietf//dtd html 3.0//","-//ietf//dtd html 3.2 final//","-//ietf//dtd html 3.2//","-//ietf//dtd html 3//","-//ietf//dtd html level 0//","-//ietf//dtd html level 1//","-//ietf//dtd html level 2//","-//ietf//dtd html level 3//","-//ietf//dtd html strict level 0//","-//ietf//dtd html strict level 1//","-//ietf//dtd html strict level 2//","-//ietf//dtd html strict level 3//","-//ietf//dtd html strict//","-//ietf//dtd html//","-//metrius//dtd metrius presentational//","-//microsoft//dtd internet explorer 2.0 html strict//","-//microsoft//dtd internet explorer 2.0 html//","-//microsoft//dtd internet explorer 2.0 tables//","-//microsoft//dtd internet explorer 3.0 html strict//","-//microsoft//dtd internet explorer 3.0 html//","-//microsoft//dtd internet explorer 3.0 tables//","-//netscape comm. corp.//dtd html//","-//netscape comm. corp.//dtd strict html//","-//o'reilly and associates//dtd html 2.0//","-//o'reilly and associates//dtd html extended 1.0//","-//o'reilly and associates//dtd html extended relaxed 1.0//","-//sq//dtd html 2.0 hotmetal + extensions//","-//softquad software//dtd hotmetal pro 6.0::19990601::extensions to html 4.0//","-//softquad//dtd hotmetal pro 4.0::19971010::extensions to html 4.0//","-//spyglass//dtd html 2.0 extended//","-//sun microsystems corp.//dtd hotjava html//","-//sun microsystems corp.//dtd hotjava strict html//","-//w3c//dtd html 3 1995-03-24//","-//w3c//dtd html 3.2 draft//","-//w3c//dtd html 3.2 final//","-//w3c//dtd html 3.2//","-//w3c//dtd html 3.2s draft//","-//w3c//dtd html 4.0 frameset//","-//w3c//dtd html 4.0 transitional//","-//w3c//dtd html experimental 19960712//","-//w3c//dtd html experimental 970421//","-//w3c//dtd w3 html//","-//w3o//dtd w3 html 3.0//","-//webtechs//dtd mozilla html 2.0//","-//webtechs//dtd mozilla html//"],tn=[...tt,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"],tr=new Set(["-//w3o//dtd w3 html strict 3.0//en//","-/w3c/dtd html 4.0 transitional/en","html"]),ti=["-//w3c//dtd xhtml 1.0 frameset//","-//w3c//dtd xhtml 1.0 transitional//"],ta=[...ti,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"];function ts(e,t){return t.some(t=>e.startsWith(t))}let to={TEXT_HTML:"text/html",APPLICATION_XML:"application/xhtml+xml"},tl=new Map(["attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map(e=>[e.toLowerCase(),e])),tc=new Map([["xlink:actuate",{prefix:"xlink",name:"actuate",namespace:h.XLINK}],["xlink:arcrole",{prefix:"xlink",name:"arcrole",namespace:h.XLINK}],["xlink:href",{prefix:"xlink",name:"href",namespace:h.XLINK}],["xlink:role",{prefix:"xlink",name:"role",namespace:h.XLINK}],["xlink:show",{prefix:"xlink",name:"show",namespace:h.XLINK}],["xlink:title",{prefix:"xlink",name:"title",namespace:h.XLINK}],["xlink:type",{prefix:"xlink",name:"type",namespace:h.XLINK}],["xml:lang",{prefix:"xml",name:"lang",namespace:h.XML}],["xml:space",{prefix:"xml",name:"space",namespace:h.XML}],["xmlns",{prefix:"",name:"xmlns",namespace:h.XMLNS}],["xmlns:xlink",{prefix:"xmlns",name:"xlink",namespace:h.XMLNS}]]),tu=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map(e=>[e.toLowerCase(),e])),th=new Set([m.B,m.BIG,m.BLOCKQUOTE,m.BODY,m.BR,m.CENTER,m.CODE,m.DD,m.DIV,m.DL,m.DT,m.EM,m.EMBED,m.H1,m.H2,m.H3,m.H4,m.H5,m.H6,m.HEAD,m.HR,m.I,m.IMG,m.LI,m.LISTING,m.MENU,m.META,m.NOBR,m.OL,m.P,m.PRE,m.RUBY,m.S,m.SMALL,m.SPAN,m.STRONG,m.STRIKE,m.SUB,m.SUP,m.TABLE,m.TT,m.U,m.UL,m.VAR]);function td(e){for(let t=0;t<e.attrs.length;t++)if("definitionurl"===e.attrs[t].name){e.attrs[t].name="definitionURL";break}}function tp(e){for(let t=0;t<e.attrs.length;t++){let n=tl.get(e.attrs[t].name);null!=n&&(e.attrs[t].name=n)}}function tf(e){for(let t=0;t<e.attrs.length;t++){let n=tc.get(e.attrs[t].name);n&&(e.attrs[t].prefix=n.prefix,e.attrs[t].name=n.name,e.attrs[t].namespace=n.namespace)}}!function(e){e[e.INITIAL=0]="INITIAL",e[e.BEFORE_HTML=1]="BEFORE_HTML",e[e.BEFORE_HEAD=2]="BEFORE_HEAD",e[e.IN_HEAD=3]="IN_HEAD",e[e.IN_HEAD_NO_SCRIPT=4]="IN_HEAD_NO_SCRIPT",e[e.AFTER_HEAD=5]="AFTER_HEAD",e[e.IN_BODY=6]="IN_BODY",e[e.TEXT=7]="TEXT",e[e.IN_TABLE=8]="IN_TABLE",e[e.IN_TABLE_TEXT=9]="IN_TABLE_TEXT",e[e.IN_CAPTION=10]="IN_CAPTION",e[e.IN_COLUMN_GROUP=11]="IN_COLUMN_GROUP",e[e.IN_TABLE_BODY=12]="IN_TABLE_BODY",e[e.IN_ROW=13]="IN_ROW",e[e.IN_CELL=14]="IN_CELL",e[e.IN_SELECT=15]="IN_SELECT",e[e.IN_SELECT_IN_TABLE=16]="IN_SELECT_IN_TABLE",e[e.IN_TEMPLATE=17]="IN_TEMPLATE",e[e.AFTER_BODY=18]="AFTER_BODY",e[e.IN_FRAMESET=19]="IN_FRAMESET",e[e.AFTER_FRAMESET=20]="AFTER_FRAMESET",e[e.AFTER_AFTER_BODY=21]="AFTER_AFTER_BODY",e[e.AFTER_AFTER_FRAMESET=22]="AFTER_AFTER_FRAMESET"}(g||(g={}));let tm={startLine:-1,startCol:-1,startOffset:-1,endLine:-1,endCol:-1,endOffset:-1},tE=new Set([m.TABLE,m.TBODY,m.TFOOT,m.THEAD,m.TR]),tT={scriptingEnabled:!0,sourceCodeLocationInfo:!1,treeAdapter:e7,onParseError:null};class tg{constructor(e,t,n=null,r=null){this.fragmentContext=n,this.scriptHandler=r,this.currentToken=null,this.stopped=!1,this.insertionMode=g.INITIAL,this.originalInsertionMode=g.INITIAL,this.headElement=null,this.formElement=null,this.currentNotInHTML=!1,this.tmplInsertionModeStack=[],this.pendingCharacterTokens=[],this.hasNonWhitespacePendingCharacterToken=!1,this.framesetOk=!0,this.skipNextNewLine=!1,this.fosterParentingEnabled=!1,this.options={...tT,...e},this.treeAdapter=this.options.treeAdapter,this.onParseError=this.options.onParseError,this.onParseError&&(this.options.sourceCodeLocationInfo=!0),this.document=null!=t?t:this.treeAdapter.createDocument(),this.tokenizer=new ej(this.options,this),this.activeFormattingElements=new e6(this.treeAdapter),this.fragmentContextID=n?eF(this.treeAdapter.getTagName(n)):m.UNKNOWN,this._setContextModes(null!=n?n:this.document,this.fragmentContextID),this.openElements=new e8(this.document,this.treeAdapter,this)}static parse(e,t){let n=new this(t);return n.tokenizer.write(e,!0),n.document}static getFragmentParser(e,t){let n={...tT,...t};null!=e||(e=n.treeAdapter.createElement(f.TEMPLATE,h.HTML,[]));let r=n.treeAdapter.createElement("documentmock",h.HTML,[]),i=new this(n,r,e);return i.fragmentContextID===m.TEMPLATE&&i.tmplInsertionModeStack.unshift(g.IN_TEMPLATE),i._initTokenizerForFragmentParsing(),i._insertFakeRootElement(),i._resetInsertionMode(),i._findFormInFragmentContext(),i}getFragment(){let e=this.treeAdapter.getFirstChild(this.document),t=this.treeAdapter.createDocumentFragment();return this._adoptNodes(e,t),t}_err(e,t,n){var r;if(!this.onParseError)return;let i=null!=(r=e.location)?r:tm,a={code:t,startLine:i.startLine,startCol:i.startCol,startOffset:i.startOffset,endLine:n?i.startLine:i.endLine,endCol:n?i.startCol:i.endCol,endOffset:n?i.startOffset:i.endOffset};this.onParseError(a)}onItemPush(e,t,n){var r,i;null==(i=(r=this.treeAdapter).onItemPush)||i.call(r,e),n&&this.openElements.stackTop>0&&this._setContextModes(e,t)}onItemPop(e,t){var n,r;if(this.options.sourceCodeLocationInfo&&this._setEndLocation(e,this.currentToken),null==(r=(n=this.treeAdapter).onItemPop)||r.call(n,e,this.openElements.current),t){let e,t;0===this.openElements.stackTop&&this.fragmentContext?(e=this.fragmentContext,t=this.fragmentContextID):{current:e,currentTagId:t}=this.openElements,this._setContextModes(e,t)}}_setContextModes(e,t){let n=e===this.document||e&&this.treeAdapter.getNamespaceURI(e)===h.HTML;this.currentNotInHTML=!n,this.tokenizer.inForeignNode=!n&&void 0!==e&&void 0!==t&&!this._isIntegrationPoint(t,e)}_switchToTextParsing(e,t){this._insertElement(e,h.HTML),this.tokenizer.state=t,this.originalInsertionMode=this.insertionMode,this.insertionMode=g.TEXT}switchToPlaintextParsing(){this.insertionMode=g.TEXT,this.originalInsertionMode=g.IN_BODY,this.tokenizer.state=eY.PLAINTEXT}_getAdjustedCurrentElement(){return 0===this.openElements.stackTop&&this.fragmentContext?this.fragmentContext:this.openElements.current}_findFormInFragmentContext(){let e=this.fragmentContext;for(;e;){if(this.treeAdapter.getTagName(e)===f.FORM){this.formElement=e;break}e=this.treeAdapter.getParentNode(e)}}_initTokenizerForFragmentParsing(){if(this.fragmentContext&&this.treeAdapter.getNamespaceURI(this.fragmentContext)===h.HTML)switch(this.fragmentContextID){case m.TITLE:case m.TEXTAREA:this.tokenizer.state=eY.RCDATA;break;case m.STYLE:case m.XMP:case m.IFRAME:case m.NOEMBED:case m.NOFRAMES:case m.NOSCRIPT:this.tokenizer.state=eY.RAWTEXT;break;case m.SCRIPT:this.tokenizer.state=eY.SCRIPT_DATA;break;case m.PLAINTEXT:this.tokenizer.state=eY.PLAINTEXT}}_setDocumentType(e){let t=e.name||"",n=e.publicId||"",r=e.systemId||"";if(this.treeAdapter.setDocumentType(this.document,t,n,r),e.location){let t=this.treeAdapter.getChildNodes(this.document).find(e=>this.treeAdapter.isDocumentTypeNode(e));t&&this.treeAdapter.setNodeSourceCodeLocation(t,e.location)}}_attachElementToTree(e,t){if(this.options.sourceCodeLocationInfo){let n=t&&{...t,startTag:t};this.treeAdapter.setNodeSourceCodeLocation(e,n)}if(this._shouldFosterParentOnInsertion())this._fosterParentElement(e);else{let t=this.openElements.currentTmplContentOrNode;this.treeAdapter.appendChild(null!=t?t:this.document,e)}}_appendElement(e,t){let n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location)}_insertElement(e,t){let n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location),this.openElements.push(n,e.tagID)}_insertFakeElement(e,t){let n=this.treeAdapter.createElement(e,h.HTML,[]);this._attachElementToTree(n,null),this.openElements.push(n,t)}_insertTemplate(e){let t=this.treeAdapter.createElement(e.tagName,h.HTML,e.attrs),n=this.treeAdapter.createDocumentFragment();this.treeAdapter.setTemplateContent(t,n),this._attachElementToTree(t,e.location),this.openElements.push(t,e.tagID),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,null)}_insertFakeRootElement(){let e=this.treeAdapter.createElement(f.HTML,h.HTML,[]);this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(e,null),this.treeAdapter.appendChild(this.openElements.current,e),this.openElements.push(e,m.HTML)}_appendCommentNode(e,t){let n=this.treeAdapter.createCommentNode(e.data);this.treeAdapter.appendChild(t,n),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,e.location)}_insertCharacters(e){let t,n;if(this._shouldFosterParentOnInsertion()?({parent:t,beforeElement:n}=this._findFosterParentingLocation(),n?this.treeAdapter.insertTextBefore(t,e.chars,n):this.treeAdapter.insertText(t,e.chars)):(t=this.openElements.currentTmplContentOrNode,this.treeAdapter.insertText(t,e.chars)),!e.location)return;let r=this.treeAdapter.getChildNodes(t),i=n?r.lastIndexOf(n):r.length,a=r[i-1];if(this.treeAdapter.getNodeSourceCodeLocation(a)){let{endLine:t,endCol:n,endOffset:r}=e.location;this.treeAdapter.updateNodeSourceCodeLocation(a,{endLine:t,endCol:n,endOffset:r})}else this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(a,e.location)}_adoptNodes(e,t){for(let n=this.treeAdapter.getFirstChild(e);n;n=this.treeAdapter.getFirstChild(e))this.treeAdapter.detachNode(n),this.treeAdapter.appendChild(t,n)}_setEndLocation(e,t){if(this.treeAdapter.getNodeSourceCodeLocation(e)&&t.location){let n=t.location,r=this.treeAdapter.getTagName(e),i=t.type===a.END_TAG&&r===t.tagName?{endTag:{...n},endLine:n.endLine,endCol:n.endCol,endOffset:n.endOffset}:{endLine:n.startLine,endCol:n.startCol,endOffset:n.startOffset};this.treeAdapter.updateNodeSourceCodeLocation(e,i)}}shouldProcessStartTagTokenInForeignContent(e){let t,n;return!!this.currentNotInHTML&&(0===this.openElements.stackTop&&this.fragmentContext?(t=this.fragmentContext,n=this.fragmentContextID):{current:t,currentTagId:n}=this.openElements,(e.tagID!==m.SVG||this.treeAdapter.getTagName(t)!==f.ANNOTATION_XML||this.treeAdapter.getNamespaceURI(t)!==h.MATHML)&&(this.tokenizer.inForeignNode||(e.tagID===m.MGLYPH||e.tagID===m.MALIGNMARK)&&void 0!==n&&!this._isIntegrationPoint(n,t,h.HTML)))}_processToken(e){switch(e.type){case a.CHARACTER:this.onCharacter(e);break;case a.NULL_CHARACTER:this.onNullCharacter(e);break;case a.COMMENT:this.onComment(e);break;case a.DOCTYPE:this.onDoctype(e);break;case a.START_TAG:this._processStartTag(e);break;case a.END_TAG:this.onEndTag(e);break;case a.EOF:this.onEof(e);break;case a.WHITESPACE_CHARACTER:this.onWhitespaceCharacter(e)}}_isIntegrationPoint(e,t,n){let r=this.treeAdapter.getNamespaceURI(t),i=this.treeAdapter.getAttrList(t);return(!n||n===h.HTML)&&function(e,t,n){if(t===h.MATHML&&e===m.ANNOTATION_XML){for(let e=0;e<n.length;e++)if(n[e].name===d.ENCODING){let t=n[e].value.toLowerCase();return t===to.TEXT_HTML||t===to.APPLICATION_XML}}return t===h.SVG&&(e===m.FOREIGN_OBJECT||e===m.DESC||e===m.TITLE)}(e,r,i)||(!n||n===h.MATHML)&&r===h.MATHML&&(e===m.MI||e===m.MO||e===m.MN||e===m.MS||e===m.MTEXT)}_reconstructActiveFormattingElements(){let e=this.activeFormattingElements.entries.length;if(e){let t=this.activeFormattingElements.entries.findIndex(e=>e.type===T.Marker||this.openElements.contains(e.element)),n=-1===t?e-1:t-1;for(let e=n;e>=0;e--){let t=this.activeFormattingElements.entries[e];this._insertElement(t.token,this.treeAdapter.getNamespaceURI(t.element)),t.element=this.openElements.current}}}_closeTableCell(){this.openElements.generateImpliedEndTags(),this.openElements.popUntilTableCellPopped(),this.activeFormattingElements.clearToLastMarker(),this.insertionMode=g.IN_ROW}_closePElement(){this.openElements.generateImpliedEndTagsWithExclusion(m.P),this.openElements.popUntilTagNamePopped(m.P)}_resetInsertionMode(){for(let e=this.openElements.stackTop;e>=0;e--)switch(0===e&&this.fragmentContext?this.fragmentContextID:this.openElements.tagIDs[e]){case m.TR:this.insertionMode=g.IN_ROW;return;case m.TBODY:case m.THEAD:case m.TFOOT:this.insertionMode=g.IN_TABLE_BODY;return;case m.CAPTION:this.insertionMode=g.IN_CAPTION;return;case m.COLGROUP:this.insertionMode=g.IN_COLUMN_GROUP;return;case m.TABLE:this.insertionMode=g.IN_TABLE;return;case m.BODY:this.insertionMode=g.IN_BODY;return;case m.FRAMESET:this.insertionMode=g.IN_FRAMESET;return;case m.SELECT:return void this._resetInsertionModeForSelect(e);case m.TEMPLATE:this.insertionMode=this.tmplInsertionModeStack[0];return;case m.HTML:this.insertionMode=this.headElement?g.AFTER_HEAD:g.BEFORE_HEAD;return;case m.TD:case m.TH:if(e>0){this.insertionMode=g.IN_CELL;return}break;case m.HEAD:if(e>0){this.insertionMode=g.IN_HEAD;return}}this.insertionMode=g.IN_BODY}_resetInsertionModeForSelect(e){if(e>0)for(let t=e-1;t>0;t--){let e=this.openElements.tagIDs[t];if(e===m.TEMPLATE)break;if(e===m.TABLE){this.insertionMode=g.IN_SELECT_IN_TABLE;return}}this.insertionMode=g.IN_SELECT}_isElementCausesFosterParenting(e){return tE.has(e)}_shouldFosterParentOnInsertion(){return this.fosterParentingEnabled&&void 0!==this.openElements.currentTagId&&this._isElementCausesFosterParenting(this.openElements.currentTagId)}_findFosterParentingLocation(){for(let e=this.openElements.stackTop;e>=0;e--){let t=this.openElements.items[e];switch(this.openElements.tagIDs[e]){case m.TEMPLATE:if(this.treeAdapter.getNamespaceURI(t)===h.HTML)return{parent:this.treeAdapter.getTemplateContent(t),beforeElement:null};break;case m.TABLE:{let n=this.treeAdapter.getParentNode(t);if(n)return{parent:n,beforeElement:t};return{parent:this.openElements.items[e-1],beforeElement:null}}}}return{parent:this.openElements.items[0],beforeElement:null}}_fosterParentElement(e){let t=this._findFosterParentingLocation();t.beforeElement?this.treeAdapter.insertBefore(t.parent,e,t.beforeElement):this.treeAdapter.appendChild(t.parent,e)}_isSpecialElement(e,t){return eU[this.treeAdapter.getNamespaceURI(e)].has(t)}onCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode){var t,n;return void(t=this,n=e,t._insertCharacters(n),t.framesetOk=!1)}switch(this.insertionMode){case g.INITIAL:tI(this,e);break;case g.BEFORE_HTML:tk(this,e);break;case g.BEFORE_HEAD:tC(this,e);break;case g.IN_HEAD:tb(this,e);break;case g.IN_HEAD_NO_SCRIPT:ty(this,e);break;case g.AFTER_HEAD:tO(this,e);break;case g.IN_BODY:case g.IN_CAPTION:case g.IN_CELL:case g.IN_TEMPLATE:tP(this,e);break;case g.TEXT:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:this._insertCharacters(e);break;case g.IN_TABLE:case g.IN_TABLE_BODY:case g.IN_ROW:tG(this,e);break;case g.IN_TABLE_TEXT:tW(this,e);break;case g.IN_COLUMN_GROUP:tX(this,e);break;case g.AFTER_BODY:t4(this,e);break;case g.AFTER_AFTER_BODY:t8(this,e)}}onNullCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode){var t,n;return void(t=this,(n=e).chars="�",t._insertCharacters(n))}switch(this.insertionMode){case g.INITIAL:tI(this,e);break;case g.BEFORE_HTML:tk(this,e);break;case g.BEFORE_HEAD:tC(this,e);break;case g.IN_HEAD:tb(this,e);break;case g.IN_HEAD_NO_SCRIPT:ty(this,e);break;case g.AFTER_HEAD:tO(this,e);break;case g.TEXT:this._insertCharacters(e);break;case g.IN_TABLE:case g.IN_TABLE_BODY:case g.IN_ROW:tG(this,e);break;case g.IN_COLUMN_GROUP:tX(this,e);break;case g.AFTER_BODY:t4(this,e);break;case g.AFTER_AFTER_BODY:t8(this,e)}}onComment(e){var t,n,r,i;if(this.skipNextNewLine=!1,this.currentNotInHTML)return void t_(this,e);switch(this.insertionMode){case g.INITIAL:case g.BEFORE_HTML:case g.BEFORE_HEAD:case g.IN_HEAD:case g.IN_HEAD_NO_SCRIPT:case g.AFTER_HEAD:case g.IN_BODY:case g.IN_TABLE:case g.IN_CAPTION:case g.IN_COLUMN_GROUP:case g.IN_TABLE_BODY:case g.IN_ROW:case g.IN_CELL:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:case g.IN_TEMPLATE:case g.IN_FRAMESET:case g.AFTER_FRAMESET:t_(this,e);break;case g.IN_TABLE_TEXT:tQ(this,e);break;case g.AFTER_BODY:t=this,n=e,t._appendCommentNode(n,t.openElements.items[0]);break;case g.AFTER_AFTER_BODY:case g.AFTER_AFTER_FRAMESET:r=this,i=e,r._appendCommentNode(i,r.document)}}onDoctype(e){switch(this.skipNextNewLine=!1,this.insertionMode){case g.INITIAL:!function(e,t){e._setDocumentType(t);let n=t.forceQuirks?p.QUIRKS:function(e){if(e.name!==te)return p.QUIRKS;let{systemId:t}=e;if(t&&"http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd"===t.toLowerCase())return p.QUIRKS;let{publicId:n}=e;if(null!==n){if(n=n.toLowerCase(),tr.has(n))return p.QUIRKS;let e=null===t?tn:tt;if(ts(n,e))return p.QUIRKS;if(ts(n,e=null===t?ti:ta))return p.LIMITED_QUIRKS}return p.NO_QUIRKS}(t);(t.name!==te||null!==t.publicId||null!==t.systemId&&"about:legacy-compat"!==t.systemId)&&e._err(t,i.nonConformingDoctype),e.treeAdapter.setDocumentMode(e.document,n),e.insertionMode=g.BEFORE_HTML}(this,e);break;case g.BEFORE_HEAD:case g.IN_HEAD:case g.IN_HEAD_NO_SCRIPT:case g.AFTER_HEAD:this._err(e,i.misplacedDoctype);break;case g.IN_TABLE_TEXT:tQ(this,e)}}onStartTag(e){this.skipNextNewLine=!1,this.currentToken=e,this._processStartTag(e),e.selfClosing&&!e.ackSelfClosing&&this._err(e,i.nonVoidHtmlElementStartTagWithTrailingSolidus)}_processStartTag(e){this.shouldProcessStartTagTokenInForeignContent(e)?function(e,t){if(function(e){let t=e.tagID;return t===m.FONT&&e.attrs.some(({name:e})=>e===d.COLOR||e===d.SIZE||e===d.FACE)||th.has(t)}(t))t9(e),e._startTagOutsideForeignContent(t);else{let n=e._getAdjustedCurrentElement(),r=e.treeAdapter.getNamespaceURI(n);if(r===h.MATHML)td(t);else if(r===h.SVG){let e=tu.get(t.tagName);null!=e&&(t.tagName=e,t.tagID=eF(t.tagName)),tp(t)}tf(t),t.selfClosing?e._appendElement(t,r):e._insertElement(t,r),t.ackSelfClosing=!0}}(this,e):this._startTagOutsideForeignContent(e)}_startTagOutsideForeignContent(e){switch(this.insertionMode){case g.INITIAL:tI(this,e);break;case g.BEFORE_HTML:t=this,(n=e).tagID===m.HTML?(t._insertElement(n,h.HTML),t.insertionMode=g.BEFORE_HEAD):tk(t,n);break;case g.BEFORE_HEAD:var t,n,r,a,s,o,l=this,c=e;switch(c.tagID){case m.HTML:tB(l,c);break;case m.HEAD:l._insertElement(c,h.HTML),l.headElement=l.openElements.current,l.insertionMode=g.IN_HEAD;break;default:tC(l,c)}break;case g.IN_HEAD:tN(this,e);break;case g.IN_HEAD_NO_SCRIPT:var u=this,d=e;switch(d.tagID){case m.HTML:tB(u,d);break;case m.BASEFONT:case m.BGSOUND:case m.HEAD:case m.LINK:case m.META:case m.NOFRAMES:case m.STYLE:tN(u,d);break;case m.NOSCRIPT:u._err(d,i.nestedNoscriptInHead);break;default:ty(u,d)}break;case g.AFTER_HEAD:var p=this,f=e;switch(f.tagID){case m.HTML:tB(p,f);break;case m.BODY:p._insertElement(f,h.HTML),p.framesetOk=!1,p.insertionMode=g.IN_BODY;break;case m.FRAMESET:p._insertElement(f,h.HTML),p.insertionMode=g.IN_FRAMESET;break;case m.BASE:case m.BASEFONT:case m.BGSOUND:case m.LINK:case m.META:case m.NOFRAMES:case m.SCRIPT:case m.STYLE:case m.TEMPLATE:case m.TITLE:p._err(f,i.abandonedHeadElementChild),p.openElements.push(p.headElement,m.HEAD),tN(p,f),p.openElements.remove(p.headElement);break;case m.HEAD:p._err(f,i.misplacedStartTagForHeadElement);break;default:tO(p,f)}break;case g.IN_BODY:tB(this,e);break;case g.IN_TABLE:tY(this,e);break;case g.IN_TABLE_TEXT:tQ(this,e);break;case g.IN_CAPTION:var E=this,T=e;let A=T.tagID;tj.has(A)?E.openElements.hasInTableScope(m.CAPTION)&&(E.openElements.generateImpliedEndTags(),E.openElements.popUntilTagNamePopped(m.CAPTION),E.activeFormattingElements.clearToLastMarker(),E.insertionMode=g.IN_TABLE,tY(E,T)):tB(E,T);break;case g.IN_COLUMN_GROUP:tK(this,e);break;case g.IN_TABLE_BODY:tJ(this,e);break;case g.IN_ROW:t$(this,e);break;case g.IN_CELL:var _=this,S=e;let I=S.tagID;tj.has(I)?(_.openElements.hasInTableScope(m.TD)||_.openElements.hasInTableScope(m.TH))&&(_._closeTableCell(),t$(_,S)):tB(_,S);break;case g.IN_SELECT:t1(this,e);break;case g.IN_SELECT_IN_TABLE:var k=this,C=e;let N=C.tagID;N===m.CAPTION||N===m.TABLE||N===m.TBODY||N===m.TFOOT||N===m.THEAD||N===m.TR||N===m.TD||N===m.TH?(k.openElements.popUntilTagNamePopped(m.SELECT),k._resetInsertionMode(),k._processStartTag(C)):t1(k,C);break;case g.IN_TEMPLATE:var D=this,b=e;switch(b.tagID){case m.BASE:case m.BASEFONT:case m.BGSOUND:case m.LINK:case m.META:case m.NOFRAMES:case m.SCRIPT:case m.STYLE:case m.TEMPLATE:case m.TITLE:tN(D,b);break;case m.CAPTION:case m.COLGROUP:case m.TBODY:case m.TFOOT:case m.THEAD:D.tmplInsertionModeStack[0]=g.IN_TABLE,D.insertionMode=g.IN_TABLE,tY(D,b);break;case m.COL:D.tmplInsertionModeStack[0]=g.IN_COLUMN_GROUP,D.insertionMode=g.IN_COLUMN_GROUP,tK(D,b);break;case m.TR:D.tmplInsertionModeStack[0]=g.IN_TABLE_BODY,D.insertionMode=g.IN_TABLE_BODY,tJ(D,b);break;case m.TD:case m.TH:D.tmplInsertionModeStack[0]=g.IN_ROW,D.insertionMode=g.IN_ROW,t$(D,b);break;default:D.tmplInsertionModeStack[0]=g.IN_BODY,D.insertionMode=g.IN_BODY,tB(D,b)}break;case g.AFTER_BODY:r=this,(a=e).tagID===m.HTML?tB(r,a):t4(r,a);break;case g.IN_FRAMESET:var y=this,O=e;switch(O.tagID){case m.HTML:tB(y,O);break;case m.FRAMESET:y._insertElement(O,h.HTML);break;case m.FRAME:y._appendElement(O,h.HTML),O.ackSelfClosing=!0;break;case m.NOFRAMES:tN(y,O)}break;case g.AFTER_FRAMESET:var R=this,L=e;switch(L.tagID){case m.HTML:tB(R,L);break;case m.NOFRAMES:tN(R,L)}break;case g.AFTER_AFTER_BODY:s=this,(o=e).tagID===m.HTML?tB(s,o):t8(s,o);break;case g.AFTER_AFTER_FRAMESET:var P=this,M=e;switch(M.tagID){case m.HTML:tB(P,M);break;case m.NOFRAMES:tN(P,M)}}}onEndTag(e){this.skipNextNewLine=!1,this.currentToken=e,this.currentNotInHTML?function(e,t){if(t.tagID===m.P||t.tagID===m.BR){t9(e),e._endTagOutsideForeignContent(t);return}for(let n=e.openElements.stackTop;n>0;n--){let r=e.openElements.items[n];if(e.treeAdapter.getNamespaceURI(r)===h.HTML){e._endTagOutsideForeignContent(t);break}let i=e.treeAdapter.getTagName(r);if(i.toLowerCase()===t.tagName){t.tagName=i,e.openElements.shortenToLength(n);break}}}(this,e):this._endTagOutsideForeignContent(e)}_endTagOutsideForeignContent(e){switch(this.insertionMode){case g.INITIAL:tI(this,e);break;case g.BEFORE_HTML:var t,n,r,a,s,o,l=this,c=e;let u=c.tagID;(u===m.HTML||u===m.HEAD||u===m.BODY||u===m.BR)&&tk(l,c);break;case g.BEFORE_HEAD:var h=this,d=e;let p=d.tagID;p===m.HEAD||p===m.BODY||p===m.HTML||p===m.BR?tC(h,d):h._err(d,i.endTagWithoutMatchingOpenElement);break;case g.IN_HEAD:var f=this,E=e;switch(E.tagID){case m.HEAD:f.openElements.pop(),f.insertionMode=g.AFTER_HEAD;break;case m.BODY:case m.BR:case m.HTML:tb(f,E);break;case m.TEMPLATE:tD(f,E);break;default:f._err(E,i.endTagWithoutMatchingOpenElement)}break;case g.IN_HEAD_NO_SCRIPT:var T=this,A=e;switch(A.tagID){case m.NOSCRIPT:T.openElements.pop(),T.insertionMode=g.IN_HEAD;break;case m.BR:ty(T,A);break;default:T._err(A,i.endTagWithoutMatchingOpenElement)}break;case g.AFTER_HEAD:var _=this,S=e;switch(S.tagID){case m.BODY:case m.HTML:case m.BR:tO(_,S);break;case m.TEMPLATE:tD(_,S);break;default:_._err(S,i.endTagWithoutMatchingOpenElement)}break;case g.IN_BODY:tH(this,e);break;case g.TEXT:t=this,e.tagID===m.SCRIPT&&(null==(n=t.scriptHandler)||n.call(t,t.openElements.current)),t.openElements.pop(),t.insertionMode=t.originalInsertionMode;break;case g.IN_TABLE:tz(this,e);break;case g.IN_TABLE_TEXT:tQ(this,e);break;case g.IN_CAPTION:var I=this,k=e;let C=k.tagID;switch(C){case m.CAPTION:case m.TABLE:I.openElements.hasInTableScope(m.CAPTION)&&(I.openElements.generateImpliedEndTags(),I.openElements.popUntilTagNamePopped(m.CAPTION),I.activeFormattingElements.clearToLastMarker(),I.insertionMode=g.IN_TABLE,C===m.TABLE&&tz(I,k));break;case m.BODY:case m.COL:case m.COLGROUP:case m.HTML:case m.TBODY:case m.TD:case m.TFOOT:case m.TH:case m.THEAD:case m.TR:break;default:tH(I,k)}break;case g.IN_COLUMN_GROUP:var N=this,D=e;switch(D.tagID){case m.COLGROUP:N.openElements.currentTagId===m.COLGROUP&&(N.openElements.pop(),N.insertionMode=g.IN_TABLE);break;case m.TEMPLATE:tD(N,D);break;case m.COL:break;default:tX(N,D)}break;case g.IN_TABLE_BODY:tZ(this,e);break;case g.IN_ROW:t0(this,e);break;case g.IN_CELL:var b=this,y=e;let O=y.tagID;switch(O){case m.TD:case m.TH:b.openElements.hasInTableScope(O)&&(b.openElements.generateImpliedEndTags(),b.openElements.popUntilTagNamePopped(O),b.activeFormattingElements.clearToLastMarker(),b.insertionMode=g.IN_ROW);break;case m.TABLE:case m.TBODY:case m.TFOOT:case m.THEAD:case m.TR:b.openElements.hasInTableScope(O)&&(b._closeTableCell(),t0(b,y));break;case m.BODY:case m.CAPTION:case m.COL:case m.COLGROUP:case m.HTML:break;default:tH(b,y)}break;case g.IN_SELECT:t3(this,e);break;case g.IN_SELECT_IN_TABLE:var R=this,L=e;let P=L.tagID;P===m.CAPTION||P===m.TABLE||P===m.TBODY||P===m.TFOOT||P===m.THEAD||P===m.TR||P===m.TD||P===m.TH?R.openElements.hasInTableScope(P)&&(R.openElements.popUntilTagNamePopped(m.SELECT),R._resetInsertionMode(),R.onEndTag(L)):t3(R,L);break;case g.IN_TEMPLATE:r=this,(a=e).tagID===m.TEMPLATE&&tD(r,a);break;case g.AFTER_BODY:t5(this,e);break;case g.IN_FRAMESET:s=this,e.tagID===m.FRAMESET&&!s.openElements.isRootHtmlElementCurrent()&&(s.openElements.pop(),s.fragmentContext||s.openElements.currentTagId===m.FRAMESET||(s.insertionMode=g.AFTER_FRAMESET));break;case g.AFTER_FRAMESET:o=this,e.tagID===m.HTML&&(o.insertionMode=g.AFTER_AFTER_FRAMESET);break;case g.AFTER_AFTER_BODY:t8(this,e)}}onEof(e){switch(this.insertionMode){case g.INITIAL:tI(this,e);break;case g.BEFORE_HTML:tk(this,e);break;case g.BEFORE_HEAD:tC(this,e);break;case g.IN_HEAD:tb(this,e);break;case g.IN_HEAD_NO_SCRIPT:ty(this,e);break;case g.AFTER_HEAD:tO(this,e);break;case g.IN_BODY:case g.IN_TABLE:case g.IN_CAPTION:case g.IN_COLUMN_GROUP:case g.IN_TABLE_BODY:case g.IN_ROW:case g.IN_CELL:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:tU(this,e);break;case g.TEXT:var t,n;t=this,n=e,t._err(n,i.eofInElementThatCanContainOnlyText),t.openElements.pop(),t.insertionMode=t.originalInsertionMode,t.onEof(n);break;case g.IN_TABLE_TEXT:tQ(this,e);break;case g.IN_TEMPLATE:t2(this,e);break;case g.AFTER_BODY:case g.IN_FRAMESET:case g.AFTER_FRAMESET:case g.AFTER_AFTER_BODY:case g.AFTER_AFTER_FRAMESET:tS(this,e)}}onWhitespaceCharacter(e){if(this.skipNextNewLine&&(this.skipNextNewLine=!1,e.chars.charCodeAt(0)===r.LINE_FEED)){if(1===e.chars.length)return;e.chars=e.chars.substr(1)}if(this.tokenizer.inForeignNode)return void this._insertCharacters(e);switch(this.insertionMode){case g.IN_HEAD:case g.IN_HEAD_NO_SCRIPT:case g.AFTER_HEAD:case g.TEXT:case g.IN_COLUMN_GROUP:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:case g.IN_FRAMESET:case g.AFTER_FRAMESET:this._insertCharacters(e);break;case g.IN_BODY:case g.IN_CAPTION:case g.IN_CELL:case g.IN_TEMPLATE:case g.AFTER_BODY:case g.AFTER_AFTER_BODY:case g.AFTER_AFTER_FRAMESET:tL(this,e);break;case g.IN_TABLE:case g.IN_TABLE_BODY:case g.IN_ROW:tG(this,e);break;case g.IN_TABLE_TEXT:tq(this,e)}}}function tA(e,t){for(let n=0;n<8;n++){let n=function(e,t){let n=e.activeFormattingElements.getElementEntryInScopeWithTagName(t.tagName);return n?e.openElements.contains(n.element)?e.openElements.hasInScope(t.tagID)||(n=null):(e.activeFormattingElements.removeEntry(n),n=null):tF(e,t),n}(e,t);if(!n)break;let r=function(e,t){let n=null,r=e.openElements.stackTop;for(;r>=0;r--){let i=e.openElements.items[r];if(i===t.element)break;e._isSpecialElement(i,e.openElements.tagIDs[r])&&(n=i)}return n||(e.openElements.shortenToLength(Math.max(r,0)),e.activeFormattingElements.removeEntry(t)),n}(e,n);if(!r)break;e.activeFormattingElements.bookmark=n;let i=function(e,t,n){let r=t,i=e.openElements.getCommonAncestor(t);for(let a=0,s=i;s!==n;a++,s=i){i=e.openElements.getCommonAncestor(s);let n=e.activeFormattingElements.getElementEntry(s),o=n&&a>=3;!n||o?(o&&e.activeFormattingElements.removeEntry(n),e.openElements.remove(s)):(s=function(e,t){let n=e.treeAdapter.getNamespaceURI(t.element),r=e.treeAdapter.createElement(t.token.tagName,n,t.token.attrs);return e.openElements.replace(t.element,r),t.element=r,r}(e,n),r===t&&(e.activeFormattingElements.bookmark=n),e.treeAdapter.detachNode(r),e.treeAdapter.appendChild(s,r),r=s)}return r}(e,r,n.element),a=e.openElements.getCommonAncestor(n.element);e.treeAdapter.detachNode(i),a&&function(e,t,n){let r=eF(e.treeAdapter.getTagName(t));if(e._isElementCausesFosterParenting(r))e._fosterParentElement(n);else{let i=e.treeAdapter.getNamespaceURI(t);r===m.TEMPLATE&&i===h.HTML&&(t=e.treeAdapter.getTemplateContent(t)),e.treeAdapter.appendChild(t,n)}}(e,a,i);let s=e.treeAdapter.getNamespaceURI(n.element),{token:o}=n,l=e.treeAdapter.createElement(o.tagName,s,o.attrs);e._adoptNodes(r,l),e.treeAdapter.appendChild(r,l),e.activeFormattingElements.insertElementAfterBookmark(l,o),e.activeFormattingElements.removeEntry(n),e.openElements.remove(n.element),e.openElements.insertAfter(r,l,o.tagID)}}function t_(e,t){e._appendCommentNode(t,e.openElements.currentTmplContentOrNode)}function tS(e,t){if(e.stopped=!0,t.location){let n=2*!e.fragmentContext;for(let r=e.openElements.stackTop;r>=n;r--)e._setEndLocation(e.openElements.items[r],t);if(!e.fragmentContext&&e.openElements.stackTop>=0){let n=e.openElements.items[0],r=e.treeAdapter.getNodeSourceCodeLocation(n);if(r&&!r.endTag&&(e._setEndLocation(n,t),e.openElements.stackTop>=1)){let n=e.openElements.items[1],r=e.treeAdapter.getNodeSourceCodeLocation(n);r&&!r.endTag&&e._setEndLocation(n,t)}}}}function tI(e,t){e._err(t,i.missingDoctype,!0),e.treeAdapter.setDocumentMode(e.document,p.QUIRKS),e.insertionMode=g.BEFORE_HTML,e._processToken(t)}function tk(e,t){e._insertFakeRootElement(),e.insertionMode=g.BEFORE_HEAD,e._processToken(t)}function tC(e,t){e._insertFakeElement(f.HEAD,m.HEAD),e.headElement=e.openElements.current,e.insertionMode=g.IN_HEAD,e._processToken(t)}function tN(e,t){switch(t.tagID){case m.HTML:tB(e,t);break;case m.BASE:case m.BASEFONT:case m.BGSOUND:case m.LINK:case m.META:e._appendElement(t,h.HTML),t.ackSelfClosing=!0;break;case m.TITLE:e._switchToTextParsing(t,eY.RCDATA);break;case m.NOSCRIPT:e.options.scriptingEnabled?e._switchToTextParsing(t,eY.RAWTEXT):(e._insertElement(t,h.HTML),e.insertionMode=g.IN_HEAD_NO_SCRIPT);break;case m.NOFRAMES:case m.STYLE:e._switchToTextParsing(t,eY.RAWTEXT);break;case m.SCRIPT:e._switchToTextParsing(t,eY.SCRIPT_DATA);break;case m.TEMPLATE:e._insertTemplate(t),e.activeFormattingElements.insertMarker(),e.framesetOk=!1,e.insertionMode=g.IN_TEMPLATE,e.tmplInsertionModeStack.unshift(g.IN_TEMPLATE);break;case m.HEAD:e._err(t,i.misplacedStartTagForHeadElement);break;default:tb(e,t)}}function tD(e,t){e.openElements.tmplCount>0?(e.openElements.generateImpliedEndTagsThoroughly(),e.openElements.currentTagId!==m.TEMPLATE&&e._err(t,i.closingOfElementWithOpenChildElements),e.openElements.popUntilTagNamePopped(m.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode()):e._err(t,i.endTagWithoutMatchingOpenElement)}function tb(e,t){e.openElements.pop(),e.insertionMode=g.AFTER_HEAD,e._processToken(t)}function ty(e,t){let n=t.type===a.EOF?i.openElementsLeftAfterEof:i.disallowedContentInNoscriptInHead;e._err(t,n),e.openElements.pop(),e.insertionMode=g.IN_HEAD,e._processToken(t)}function tO(e,t){e._insertFakeElement(f.BODY,m.BODY),e.insertionMode=g.IN_BODY,tR(e,t)}function tR(e,t){switch(t.type){case a.CHARACTER:tP(e,t);break;case a.WHITESPACE_CHARACTER:tL(e,t);break;case a.COMMENT:t_(e,t);break;case a.START_TAG:tB(e,t);break;case a.END_TAG:tH(e,t);break;case a.EOF:tU(e,t)}}function tL(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t)}function tP(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t),e.framesetOk=!1}function tM(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,h.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}function tv(e){let t=eP(e,d.TYPE);return null!=t&&"hidden"===t.toLowerCase()}function tx(e,t){e._switchToTextParsing(t,eY.RAWTEXT)}function tw(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML)}function tB(e,t){switch(t.tagID){case m.I:case m.S:case m.B:case m.U:case m.EM:case m.TT:case m.BIG:case m.CODE:case m.FONT:case m.SMALL:case m.STRIKE:case m.STRONG:e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t);break;case m.A:let n=e.activeFormattingElements.getElementEntryInScopeWithTagName(f.A);n&&(tA(e,t),e.openElements.remove(n.element),e.activeFormattingElements.removeEntry(n)),e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t);break;case m.H1:case m.H2:case m.H3:case m.H4:case m.H5:case m.H6:e.openElements.hasInButtonScope(m.P)&&e._closePElement(),void 0!==e.openElements.currentTagId&&eG.has(e.openElements.currentTagId)&&e.openElements.pop(),e._insertElement(t,h.HTML);break;case m.P:case m.DL:case m.OL:case m.UL:case m.DIV:case m.DIR:case m.NAV:case m.MAIN:case m.MENU:case m.ASIDE:case m.CENTER:case m.FIGURE:case m.FOOTER:case m.HEADER:case m.HGROUP:case m.DIALOG:case m.DETAILS:case m.ADDRESS:case m.ARTICLE:case m.SEARCH:case m.SECTION:case m.SUMMARY:case m.FIELDSET:case m.BLOCKQUOTE:case m.FIGCAPTION:e.openElements.hasInButtonScope(m.P)&&e._closePElement(),e._insertElement(t,h.HTML);break;case m.LI:case m.DD:case m.DT:e.framesetOk=!1;let r=t.tagID;for(let t=e.openElements.stackTop;t>=0;t--){let n=e.openElements.tagIDs[t];if(r===m.LI&&n===m.LI||(r===m.DD||r===m.DT)&&(n===m.DD||n===m.DT)){e.openElements.generateImpliedEndTagsWithExclusion(n),e.openElements.popUntilTagNamePopped(n);break}if(n!==m.ADDRESS&&n!==m.DIV&&n!==m.P&&e._isSpecialElement(e.openElements.items[t],n))break}e.openElements.hasInButtonScope(m.P)&&e._closePElement(),e._insertElement(t,h.HTML);break;case m.BR:case m.IMG:case m.WBR:case m.AREA:case m.EMBED:case m.KEYGEN:tM(e,t);break;case m.HR:e.openElements.hasInButtonScope(m.P)&&e._closePElement(),e._appendElement(t,h.HTML),e.framesetOk=!1,t.ackSelfClosing=!0;break;case m.RB:case m.RTC:e.openElements.hasInScope(m.RUBY)&&e.openElements.generateImpliedEndTags(),e._insertElement(t,h.HTML);break;case m.RT:case m.RP:e.openElements.hasInScope(m.RUBY)&&e.openElements.generateImpliedEndTagsWithExclusion(m.RTC),e._insertElement(t,h.HTML);break;case m.PRE:case m.LISTING:e.openElements.hasInButtonScope(m.P)&&e._closePElement(),e._insertElement(t,h.HTML),e.skipNextNewLine=!0,e.framesetOk=!1;break;case m.XMP:e.openElements.hasInButtonScope(m.P)&&e._closePElement(),e._reconstructActiveFormattingElements(),e.framesetOk=!1,e._switchToTextParsing(t,eY.RAWTEXT);break;case m.SVG:e._reconstructActiveFormattingElements(),tp(t),tf(t),t.selfClosing?e._appendElement(t,h.SVG):e._insertElement(t,h.SVG),t.ackSelfClosing=!0;break;case m.HTML:0===e.openElements.tmplCount&&e.treeAdapter.adoptAttributes(e.openElements.items[0],t.attrs);break;case m.BASE:case m.LINK:case m.META:case m.STYLE:case m.TITLE:case m.SCRIPT:case m.BGSOUND:case m.BASEFONT:case m.TEMPLATE:tN(e,t);break;case m.BODY:let i=e.openElements.tryPeekProperlyNestedBodyElement();i&&0===e.openElements.tmplCount&&(e.framesetOk=!1,e.treeAdapter.adoptAttributes(i,t.attrs));break;case m.FORM:let a=e.openElements.tmplCount>0;(!e.formElement||a)&&(e.openElements.hasInButtonScope(m.P)&&e._closePElement(),e._insertElement(t,h.HTML),a||(e.formElement=e.openElements.current));break;case m.NOBR:e._reconstructActiveFormattingElements(),e.openElements.hasInScope(m.NOBR)&&(tA(e,t),e._reconstructActiveFormattingElements()),e._insertElement(t,h.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t);break;case m.MATH:e._reconstructActiveFormattingElements(),td(t),tf(t),t.selfClosing?e._appendElement(t,h.MATHML):e._insertElement(t,h.MATHML),t.ackSelfClosing=!0;break;case m.TABLE:e.treeAdapter.getDocumentMode(e.document)!==p.QUIRKS&&e.openElements.hasInButtonScope(m.P)&&e._closePElement(),e._insertElement(t,h.HTML),e.framesetOk=!1,e.insertionMode=g.IN_TABLE;break;case m.INPUT:e._reconstructActiveFormattingElements(),e._appendElement(t,h.HTML),tv(t)||(e.framesetOk=!1),t.ackSelfClosing=!0;break;case m.PARAM:case m.TRACK:case m.SOURCE:e._appendElement(t,h.HTML),t.ackSelfClosing=!0;break;case m.IMAGE:t.tagName=f.IMG,t.tagID=m.IMG,tM(e,t);break;case m.BUTTON:e.openElements.hasInScope(m.BUTTON)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(m.BUTTON)),e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML),e.framesetOk=!1;break;case m.APPLET:case m.OBJECT:case m.MARQUEE:e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML),e.activeFormattingElements.insertMarker(),e.framesetOk=!1;break;case m.IFRAME:e.framesetOk=!1,e._switchToTextParsing(t,eY.RAWTEXT);break;case m.SELECT:e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML),e.framesetOk=!1,e.insertionMode=e.insertionMode===g.IN_TABLE||e.insertionMode===g.IN_CAPTION||e.insertionMode===g.IN_TABLE_BODY||e.insertionMode===g.IN_ROW||e.insertionMode===g.IN_CELL?g.IN_SELECT_IN_TABLE:g.IN_SELECT;break;case m.OPTION:case m.OPTGROUP:e.openElements.currentTagId===m.OPTION&&e.openElements.pop(),e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML);break;case m.NOEMBED:case m.NOFRAMES:tx(e,t);break;case m.FRAMESET:let s=e.openElements.tryPeekProperlyNestedBodyElement();e.framesetOk&&s&&(e.treeAdapter.detachNode(s),e.openElements.popAllUpToHtmlElement(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_FRAMESET);break;case m.TEXTAREA:e._insertElement(t,h.HTML),e.skipNextNewLine=!0,e.tokenizer.state=eY.RCDATA,e.originalInsertionMode=e.insertionMode,e.framesetOk=!1,e.insertionMode=g.TEXT;break;case m.NOSCRIPT:e.options.scriptingEnabled?tx(e,t):tw(e,t);break;case m.PLAINTEXT:e.openElements.hasInButtonScope(m.P)&&e._closePElement(),e._insertElement(t,h.HTML),e.tokenizer.state=eY.PLAINTEXT;break;case m.COL:case m.TH:case m.TD:case m.TR:case m.HEAD:case m.FRAME:case m.TBODY:case m.TFOOT:case m.THEAD:case m.CAPTION:case m.COLGROUP:break;default:tw(e,t)}}function tF(e,t){let n=t.tagName,r=t.tagID;for(let t=e.openElements.stackTop;t>0;t--){let i=e.openElements.items[t],a=e.openElements.tagIDs[t];if(r===a&&(r!==m.UNKNOWN||e.treeAdapter.getTagName(i)===n)){e.openElements.generateImpliedEndTagsWithExclusion(r),e.openElements.stackTop>=t&&e.openElements.shortenToLength(t);break}if(e._isSpecialElement(i,a))break}}function tH(e,t){switch(t.tagID){case m.A:case m.B:case m.I:case m.S:case m.U:case m.EM:case m.TT:case m.BIG:case m.CODE:case m.FONT:case m.NOBR:case m.SMALL:case m.STRIKE:case m.STRONG:tA(e,t);break;case m.P:e.openElements.hasInButtonScope(m.P)||e._insertFakeElement(f.P,m.P),e._closePElement();break;case m.DL:case m.UL:case m.OL:case m.DIR:case m.DIV:case m.NAV:case m.PRE:case m.MAIN:case m.MENU:case m.ASIDE:case m.BUTTON:case m.CENTER:case m.FIGURE:case m.FOOTER:case m.HEADER:case m.HGROUP:case m.DIALOG:case m.ADDRESS:case m.ARTICLE:case m.DETAILS:case m.SEARCH:case m.SECTION:case m.SUMMARY:case m.LISTING:case m.FIELDSET:case m.BLOCKQUOTE:case m.FIGCAPTION:let n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n));break;case m.LI:e.openElements.hasInListItemScope(m.LI)&&(e.openElements.generateImpliedEndTagsWithExclusion(m.LI),e.openElements.popUntilTagNamePopped(m.LI));break;case m.DD:case m.DT:let r=t.tagID;e.openElements.hasInScope(r)&&(e.openElements.generateImpliedEndTagsWithExclusion(r),e.openElements.popUntilTagNamePopped(r));break;case m.H1:case m.H2:case m.H3:case m.H4:case m.H5:case m.H6:e.openElements.hasNumberedHeaderInScope()&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilNumberedHeaderPopped());break;case m.BR:e._reconstructActiveFormattingElements(),e._insertFakeElement(f.BR,m.BR),e.openElements.pop(),e.framesetOk=!1;break;case m.BODY:if(e.openElements.hasInScope(m.BODY)&&(e.insertionMode=g.AFTER_BODY,e.options.sourceCodeLocationInfo)){let n=e.openElements.tryPeekProperlyNestedBodyElement();n&&e._setEndLocation(n,t)}break;case m.HTML:e.openElements.hasInScope(m.BODY)&&(e.insertionMode=g.AFTER_BODY,t5(e,t));break;case m.FORM:let i=e.openElements.tmplCount>0,{formElement:a}=e;i||(e.formElement=null),(a||i)&&e.openElements.hasInScope(m.FORM)&&(e.openElements.generateImpliedEndTags(),i?e.openElements.popUntilTagNamePopped(m.FORM):a&&e.openElements.remove(a));break;case m.APPLET:case m.OBJECT:case m.MARQUEE:let s=t.tagID;e.openElements.hasInScope(s)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(s),e.activeFormattingElements.clearToLastMarker());break;case m.TEMPLATE:tD(e,t);break;default:tF(e,t)}}function tU(e,t){e.tmplInsertionModeStack.length>0?t2(e,t):tS(e,t)}function tG(e,t){if(void 0!==e.openElements.currentTagId&&tE.has(e.openElements.currentTagId))switch(e.pendingCharacterTokens.length=0,e.hasNonWhitespacePendingCharacterToken=!1,e.originalInsertionMode=e.insertionMode,e.insertionMode=g.IN_TABLE_TEXT,t.type){case a.CHARACTER:tW(e,t);break;case a.WHITESPACE_CHARACTER:tq(e,t)}else tV(e,t)}function tY(e,t){switch(t.tagID){case m.TD:case m.TH:case m.TR:e.openElements.clearBackToTableContext(),e._insertFakeElement(f.TBODY,m.TBODY),e.insertionMode=g.IN_TABLE_BODY,tJ(e,t);break;case m.STYLE:case m.SCRIPT:case m.TEMPLATE:tN(e,t);break;case m.COL:e.openElements.clearBackToTableContext(),e._insertFakeElement(f.COLGROUP,m.COLGROUP),e.insertionMode=g.IN_COLUMN_GROUP,tK(e,t);break;case m.FORM:e.formElement||0!==e.openElements.tmplCount||(e._insertElement(t,h.HTML),e.formElement=e.openElements.current,e.openElements.pop());break;case m.TABLE:e.openElements.hasInTableScope(m.TABLE)&&(e.openElements.popUntilTagNamePopped(m.TABLE),e._resetInsertionMode(),e._processStartTag(t));break;case m.TBODY:case m.TFOOT:case m.THEAD:e.openElements.clearBackToTableContext(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_TABLE_BODY;break;case m.INPUT:tv(t)?e._appendElement(t,h.HTML):tV(e,t),t.ackSelfClosing=!0;break;case m.CAPTION:e.openElements.clearBackToTableContext(),e.activeFormattingElements.insertMarker(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_CAPTION;break;case m.COLGROUP:e.openElements.clearBackToTableContext(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_COLUMN_GROUP;break;default:tV(e,t)}}function tz(e,t){switch(t.tagID){case m.TABLE:e.openElements.hasInTableScope(m.TABLE)&&(e.openElements.popUntilTagNamePopped(m.TABLE),e._resetInsertionMode());break;case m.TEMPLATE:tD(e,t);break;case m.BODY:case m.CAPTION:case m.COL:case m.COLGROUP:case m.HTML:case m.TBODY:case m.TD:case m.TFOOT:case m.TH:case m.THEAD:case m.TR:break;default:tV(e,t)}}function tV(e,t){let n=e.fosterParentingEnabled;e.fosterParentingEnabled=!0,tR(e,t),e.fosterParentingEnabled=n}function tq(e,t){e.pendingCharacterTokens.push(t)}function tW(e,t){e.pendingCharacterTokens.push(t),e.hasNonWhitespacePendingCharacterToken=!0}function tQ(e,t){let n=0;if(e.hasNonWhitespacePendingCharacterToken)for(;n<e.pendingCharacterTokens.length;n++)tV(e,e.pendingCharacterTokens[n]);else for(;n<e.pendingCharacterTokens.length;n++)e._insertCharacters(e.pendingCharacterTokens[n]);e.insertionMode=e.originalInsertionMode,e._processToken(t)}let tj=new Set([m.CAPTION,m.COL,m.COLGROUP,m.TBODY,m.TD,m.TFOOT,m.TH,m.THEAD,m.TR]);function tK(e,t){switch(t.tagID){case m.HTML:tB(e,t);break;case m.COL:e._appendElement(t,h.HTML),t.ackSelfClosing=!0;break;case m.TEMPLATE:tN(e,t);break;default:tX(e,t)}}function tX(e,t){e.openElements.currentTagId===m.COLGROUP&&(e.openElements.pop(),e.insertionMode=g.IN_TABLE,e._processToken(t))}function tJ(e,t){switch(t.tagID){case m.TR:e.openElements.clearBackToTableBodyContext(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_ROW;break;case m.TH:case m.TD:e.openElements.clearBackToTableBodyContext(),e._insertFakeElement(f.TR,m.TR),e.insertionMode=g.IN_ROW,t$(e,t);break;case m.CAPTION:case m.COL:case m.COLGROUP:case m.TBODY:case m.TFOOT:case m.THEAD:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE,tY(e,t));break;default:tY(e,t)}}function tZ(e,t){let n=t.tagID;switch(t.tagID){case m.TBODY:case m.TFOOT:case m.THEAD:e.openElements.hasInTableScope(n)&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE);break;case m.TABLE:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE,tz(e,t));break;case m.BODY:case m.CAPTION:case m.COL:case m.COLGROUP:case m.HTML:case m.TD:case m.TH:case m.TR:break;default:tz(e,t)}}function t$(e,t){switch(t.tagID){case m.TH:case m.TD:e.openElements.clearBackToTableRowContext(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_CELL,e.activeFormattingElements.insertMarker();break;case m.CAPTION:case m.COL:case m.COLGROUP:case m.TBODY:case m.TFOOT:case m.THEAD:case m.TR:e.openElements.hasInTableScope(m.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY,tJ(e,t));break;default:tY(e,t)}}function t0(e,t){switch(t.tagID){case m.TR:e.openElements.hasInTableScope(m.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY);break;case m.TABLE:e.openElements.hasInTableScope(m.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY,tZ(e,t));break;case m.TBODY:case m.TFOOT:case m.THEAD:(e.openElements.hasInTableScope(t.tagID)||e.openElements.hasInTableScope(m.TR))&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY,tZ(e,t));break;case m.BODY:case m.CAPTION:case m.COL:case m.COLGROUP:case m.HTML:case m.TD:case m.TH:break;default:tz(e,t)}}function t1(e,t){switch(t.tagID){case m.HTML:tB(e,t);break;case m.OPTION:e.openElements.currentTagId===m.OPTION&&e.openElements.pop(),e._insertElement(t,h.HTML);break;case m.OPTGROUP:e.openElements.currentTagId===m.OPTION&&e.openElements.pop(),e.openElements.currentTagId===m.OPTGROUP&&e.openElements.pop(),e._insertElement(t,h.HTML);break;case m.HR:e.openElements.currentTagId===m.OPTION&&e.openElements.pop(),e.openElements.currentTagId===m.OPTGROUP&&e.openElements.pop(),e._appendElement(t,h.HTML),t.ackSelfClosing=!0;break;case m.INPUT:case m.KEYGEN:case m.TEXTAREA:case m.SELECT:e.openElements.hasInSelectScope(m.SELECT)&&(e.openElements.popUntilTagNamePopped(m.SELECT),e._resetInsertionMode(),t.tagID!==m.SELECT&&e._processStartTag(t));break;case m.SCRIPT:case m.TEMPLATE:tN(e,t)}}function t3(e,t){switch(t.tagID){case m.OPTGROUP:e.openElements.stackTop>0&&e.openElements.currentTagId===m.OPTION&&e.openElements.tagIDs[e.openElements.stackTop-1]===m.OPTGROUP&&e.openElements.pop(),e.openElements.currentTagId===m.OPTGROUP&&e.openElements.pop();break;case m.OPTION:e.openElements.currentTagId===m.OPTION&&e.openElements.pop();break;case m.SELECT:e.openElements.hasInSelectScope(m.SELECT)&&(e.openElements.popUntilTagNamePopped(m.SELECT),e._resetInsertionMode());break;case m.TEMPLATE:tD(e,t)}}function t2(e,t){e.openElements.tmplCount>0?(e.openElements.popUntilTagNamePopped(m.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode(),e.onEof(t)):tS(e,t)}function t5(e,t){var n;if(t.tagID===m.HTML){if(e.fragmentContext||(e.insertionMode=g.AFTER_AFTER_BODY),e.options.sourceCodeLocationInfo&&e.openElements.tagIDs[0]===m.HTML){e._setEndLocation(e.openElements.items[0],t);let r=e.openElements.items[1];!r||(null==(n=e.treeAdapter.getNodeSourceCodeLocation(r))?void 0:n.endTag)||e._setEndLocation(r,t)}}else t4(e,t)}function t4(e,t){e.insertionMode=g.IN_BODY,tR(e,t)}function t8(e,t){e.insertionMode=g.IN_BODY,tR(e,t)}function t9(e){for(;e.treeAdapter.getNamespaceURI(e.openElements.current)!==h.HTML&&void 0!==e.openElements.currentTagId&&!e._isIntegrationPoint(e.openElements.currentTagId,e.openElements.current);)e.openElements.pop()}null==String.prototype.codePointAt||((e,t)=>e.codePointAt(t));let t6=new Set([f.AREA,f.BASE,f.BASEFONT,f.BGSOUND,f.BR,f.COL,f.EMBED,f.FRAME,f.HR,f.IMG,f.INPUT,f.KEYGEN,f.LINK,f.META,f.PARAM,f.SOURCE,f.TRACK,f.WBR]);var t7=n(70832),ne=n(88428);let nt=/<(\/?)(iframe|noembed|noframes|plaintext|script|style|textarea|title|xmp)(?=[\t\n\f\r />])/gi,nn=new Set(["mdxFlowExpression","mdxJsxFlowElement","mdxJsxTextElement","mdxTextExpression","mdxjsEsm"]),nr={sourceCodeLocationInfo:!0,scriptingEnabled:!1};function ni(e,t){let n=function(e){let t="root"===e.type?e.children[0]:e;return!!(t&&("doctype"===t.type||"element"===t.type&&"html"===t.tagName.toLowerCase()))}(e),r=e_("type",{handlers:{root:ns,element:no,text:nl,comment:nu,doctype:nc,raw:nh},unknown:nd}),i={parser:n?new tg(nr):tg.getFragmentParser(void 0,nr),handle(e){r(e,i)},stitches:!1,options:t||{}};r(e,i),np(i,(0,t7.PW)());let a=function(e,t){let n=t||{};return w({file:n.file||void 0,location:!1,schema:"svg"===n.space?I.JW:I.qy,verbose:n.verbose||!1},e)}(n?i.parser.document:i.parser.getFragment(),{file:i.options.file});return(i.stitches&&(0,ne.YR)(a,"comment",function(e,t,n){if(e.value.stitch&&n&&void 0!==t)return n.children[t]=e.value.stitch,t}),"root"===a.type&&1===a.children.length&&a.children[0].type===e.type)?a.children[0]:a}function na(e,t){let n=-1;if(e)for(;++n<e.length;)t.handle(e[n])}function ns(e,t){na(e.children,t)}function no(e,t){(function(e,t){let n=e.tagName.toLowerCase();if(t.parser.tokenizer.state===eY.PLAINTEXT)return;np(t,(0,t7.PW)(e));let r=t.parser.openElements.current,i="namespaceURI"in r?r.namespaceURI:M.html;i===M.html&&"svg"===n&&(i=M.svg);let s=eI({...e,children:[]},"svg"===({space:i===M.svg?"svg":"html"}).space?ep:ed),o={type:a.START_TAG,tagName:n,tagID:eF(n),selfClosing:!1,ackSelfClosing:!1,attrs:"attrs"in s?s.attrs:[],location:nm(e)};t.parser.currentToken=o,t.parser._processToken(t.parser.currentToken),t.parser.tokenizer.lastStartTagName=n})(e,t),na(e.children,t),function(e,t){let n=e.tagName.toLowerCase();if(!t.parser.tokenizer.inForeignNode&&eN.includes(n)||t.parser.tokenizer.state===eY.PLAINTEXT)return;np(t,(0,t7.Y)(e));let r={type:a.END_TAG,tagName:n,tagID:eF(n),selfClosing:!1,ackSelfClosing:!1,attrs:[],location:nm(e)};t.parser.currentToken=r,t.parser._processToken(t.parser.currentToken),n===t.parser.tokenizer.lastStartTagName&&(t.parser.tokenizer.state===eY.RCDATA||t.parser.tokenizer.state===eY.RAWTEXT||t.parser.tokenizer.state===eY.SCRIPT_DATA)&&(t.parser.tokenizer.state=eY.DATA)}(e,t)}function nl(e,t){t.parser.tokenizer.state>4&&(t.parser.tokenizer.state=0);let n={type:a.CHARACTER,chars:e.value,location:nm(e)};np(t,(0,t7.PW)(e)),t.parser.currentToken=n,t.parser._processToken(t.parser.currentToken)}function nc(e,t){let n={type:a.DOCTYPE,name:"html",forceQuirks:!1,publicId:"",systemId:"",location:nm(e)};np(t,(0,t7.PW)(e)),t.parser.currentToken=n,t.parser._processToken(t.parser.currentToken)}function nu(e,t){let n=e.value,r={type:a.COMMENT,data:n,location:nm(e)};np(t,(0,t7.PW)(e)),t.parser.currentToken=r,t.parser._processToken(t.parser.currentToken)}function nh(e,t){if(t.parser.tokenizer.preprocessor.html="",t.parser.tokenizer.preprocessor.pos=-1,t.parser.tokenizer.preprocessor.lastGapPos=-2,t.parser.tokenizer.preprocessor.gapStack=[],t.parser.tokenizer.preprocessor.skipNextNewLine=!1,t.parser.tokenizer.preprocessor.lastChunkWritten=!1,t.parser.tokenizer.preprocessor.endOfChunkHit=!1,t.parser.tokenizer.preprocessor.isEol=!1,nf(t,(0,t7.PW)(e)),t.parser.tokenizer.write(t.options.tagfilter?e.value.replace(nt,"&lt;$1$2"):e.value,!1),t.parser.tokenizer._runParsingLoop(),72===t.parser.tokenizer.state||78===t.parser.tokenizer.state){t.parser.tokenizer.preprocessor.lastChunkWritten=!0;let e=t.parser.tokenizer._consume();t.parser.tokenizer._callState(e)}}function nd(e,t){if(t.options.passThrough&&t.options.passThrough.includes(e.type)){var n;t.stitches=!0;let r="children"in(n=e)?(0,_.Ay)({...n,children:[]}):(0,_.Ay)(n);"children"in e&&"children"in r&&(r.children=ni({type:"root",children:e.children},t.options).children),nu({type:"comment",value:{stitch:r}},t)}else{let t="";throw nn.has(e.type)&&(t=". It looks like you are using MDX nodes with `hast-util-raw` (or `rehype-raw`). If you use this because you are using remark or rehype plugins that inject `'html'` nodes, then please raise an issue with that plugin, as its a bad and slow idea. If you use this because you are using markdown syntax, then you have to configure this utility (or plugin) to pass through these nodes (see `passThrough` in docs), but you can also migrate to use the MDX syntax"),Error("Cannot compile `"+e.type+"` node"+t)}}function np(e,t){nf(e,t);let n=e.parser.tokenizer.currentCharacterToken;n&&n.location&&(n.location.endLine=e.parser.tokenizer.preprocessor.line,n.location.endCol=e.parser.tokenizer.preprocessor.col+1,n.location.endOffset=e.parser.tokenizer.preprocessor.offset+1,e.parser.currentToken=n,e.parser._processToken(e.parser.currentToken)),e.parser.tokenizer.paused=!1,e.parser.tokenizer.inLoop=!1,e.parser.tokenizer.active=!1,e.parser.tokenizer.returnState=eY.DATA,e.parser.tokenizer.charRefCode=-1,e.parser.tokenizer.consumedAfterSnapshot=-1,e.parser.tokenizer.currentLocation=null,e.parser.tokenizer.currentCharacterToken=null,e.parser.tokenizer.currentToken=null,e.parser.tokenizer.currentAttr={name:"",value:""}}function nf(e,t){if(t&&void 0!==t.offset){let n={startLine:t.line,startCol:t.column,startOffset:t.offset,endLine:-1,endCol:-1,endOffset:-1};e.parser.tokenizer.preprocessor.lineStartPos=-t.column+1,e.parser.tokenizer.preprocessor.droppedBufferSize=t.offset,e.parser.tokenizer.preprocessor.line=t.line,e.parser.tokenizer.currentLocation=n}}function nm(e){let t=(0,t7.PW)(e)||{line:void 0,column:void 0,offset:void 0},n=(0,t7.Y)(e)||{line:void 0,column:void 0,offset:void 0};return{startLine:t.line,startCol:t.column,startOffset:t.offset,endLine:n.line,endCol:n.column,endOffset:n.offset}}function nE(e){return function(t,n){return ni(t,{...e,file:n})}}},83846:(e,t,n)=>{"use strict";n.d(t,{qy:()=>T,JW:()=>g});class r{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function i(e,t){let n={},i={};for(let t of e)Object.assign(n,t.property),Object.assign(i,t.normal);return new r(n,i,t)}r.prototype.normal={},r.prototype.property={},r.prototype.space=void 0;var a=n(59739),s=n(25437);function o(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let o=new s.E(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(o.mustUseProperty=!0),t[r]=o,n[(0,a.S)(r)]=r,n[(0,a.S)(o.attribute)]=r}return new r(t,n,e.space)}var l=n(8918);let c=o({properties:{ariaActiveDescendant:null,ariaAtomic:l.booleanish,ariaAutoComplete:null,ariaBusy:l.booleanish,ariaChecked:l.booleanish,ariaColCount:l.number,ariaColIndex:l.number,ariaColSpan:l.number,ariaControls:l.spaceSeparated,ariaCurrent:null,ariaDescribedBy:l.spaceSeparated,ariaDetails:null,ariaDisabled:l.booleanish,ariaDropEffect:l.spaceSeparated,ariaErrorMessage:null,ariaExpanded:l.booleanish,ariaFlowTo:l.spaceSeparated,ariaGrabbed:l.booleanish,ariaHasPopup:null,ariaHidden:l.booleanish,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:l.spaceSeparated,ariaLevel:l.number,ariaLive:null,ariaModal:l.booleanish,ariaMultiLine:l.booleanish,ariaMultiSelectable:l.booleanish,ariaOrientation:null,ariaOwns:l.spaceSeparated,ariaPlaceholder:null,ariaPosInSet:l.number,ariaPressed:l.booleanish,ariaReadOnly:l.booleanish,ariaRelevant:null,ariaRequired:l.booleanish,ariaRoleDescription:l.spaceSeparated,ariaRowCount:l.number,ariaRowIndex:l.number,ariaRowSpan:l.number,ariaSelected:l.booleanish,ariaSetSize:l.number,ariaSort:null,ariaValueMax:l.number,ariaValueMin:l.number,ariaValueNow:l.number,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function u(e,t){return t in e?e[t]:t}function h(e,t){return u(e,t.toLowerCase())}let d=o({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:l.commaSeparated,acceptCharset:l.spaceSeparated,accessKey:l.spaceSeparated,action:null,allow:null,allowFullScreen:l.boolean,allowPaymentRequest:l.boolean,allowUserMedia:l.boolean,alt:null,as:null,async:l.boolean,autoCapitalize:null,autoComplete:l.spaceSeparated,autoFocus:l.boolean,autoPlay:l.boolean,blocking:l.spaceSeparated,capture:null,charSet:null,checked:l.boolean,cite:null,className:l.spaceSeparated,cols:l.number,colSpan:null,content:null,contentEditable:l.booleanish,controls:l.boolean,controlsList:l.spaceSeparated,coords:l.number|l.commaSeparated,crossOrigin:null,data:null,dateTime:null,decoding:null,default:l.boolean,defer:l.boolean,dir:null,dirName:null,disabled:l.boolean,download:l.overloadedBoolean,draggable:l.booleanish,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:l.boolean,formTarget:null,headers:l.spaceSeparated,height:l.number,hidden:l.overloadedBoolean,high:l.number,href:null,hrefLang:null,htmlFor:l.spaceSeparated,httpEquiv:l.spaceSeparated,id:null,imageSizes:null,imageSrcSet:null,inert:l.boolean,inputMode:null,integrity:null,is:null,isMap:l.boolean,itemId:null,itemProp:l.spaceSeparated,itemRef:l.spaceSeparated,itemScope:l.boolean,itemType:l.spaceSeparated,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:l.boolean,low:l.number,manifest:null,max:null,maxLength:l.number,media:null,method:null,min:null,minLength:l.number,multiple:l.boolean,muted:l.boolean,name:null,nonce:null,noModule:l.boolean,noValidate:l.boolean,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:l.boolean,optimum:l.number,pattern:null,ping:l.spaceSeparated,placeholder:null,playsInline:l.boolean,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:l.boolean,referrerPolicy:null,rel:l.spaceSeparated,required:l.boolean,reversed:l.boolean,rows:l.number,rowSpan:l.number,sandbox:l.spaceSeparated,scope:null,scoped:l.boolean,seamless:l.boolean,selected:l.boolean,shadowRootClonable:l.boolean,shadowRootDelegatesFocus:l.boolean,shadowRootMode:null,shape:null,size:l.number,sizes:null,slot:null,span:l.number,spellCheck:l.booleanish,src:null,srcDoc:null,srcLang:null,srcSet:null,start:l.number,step:null,style:null,tabIndex:l.number,target:null,title:null,translate:null,type:null,typeMustMatch:l.boolean,useMap:null,value:l.booleanish,width:l.number,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:l.spaceSeparated,axis:null,background:null,bgColor:null,border:l.number,borderColor:null,bottomMargin:l.number,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:l.boolean,declare:l.boolean,event:null,face:null,frame:null,frameBorder:null,hSpace:l.number,leftMargin:l.number,link:null,longDesc:null,lowSrc:null,marginHeight:l.number,marginWidth:l.number,noResize:l.boolean,noHref:l.boolean,noShade:l.boolean,noWrap:l.boolean,object:null,profile:null,prompt:null,rev:null,rightMargin:l.number,rules:null,scheme:null,scrolling:l.booleanish,standby:null,summary:null,text:null,topMargin:l.number,valueType:null,version:null,vAlign:null,vLink:null,vSpace:l.number,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:l.boolean,disableRemotePlayback:l.boolean,prefix:null,property:null,results:l.number,security:null,unselectable:null},space:"html",transform:h}),p=o({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:l.commaOrSpaceSeparated,accentHeight:l.number,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:l.number,amplitude:l.number,arabicForm:null,ascent:l.number,attributeName:null,attributeType:null,azimuth:l.number,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:l.number,by:null,calcMode:null,capHeight:l.number,className:l.spaceSeparated,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:l.number,diffuseConstant:l.number,direction:null,display:null,dur:null,divisor:l.number,dominantBaseline:null,download:l.boolean,dx:null,dy:null,edgeMode:null,editable:null,elevation:l.number,enableBackground:null,end:null,event:null,exponent:l.number,externalResourcesRequired:null,fill:null,fillOpacity:l.number,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:l.commaSeparated,g2:l.commaSeparated,glyphName:l.commaSeparated,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:l.number,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:l.number,horizOriginX:l.number,horizOriginY:l.number,id:null,ideographic:l.number,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:l.number,k:l.number,k1:l.number,k2:l.number,k3:l.number,k4:l.number,kernelMatrix:l.commaOrSpaceSeparated,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:l.number,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:l.number,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:l.number,overlineThickness:l.number,paintOrder:null,panose1:null,path:null,pathLength:l.number,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:l.spaceSeparated,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:l.number,pointsAtY:l.number,pointsAtZ:l.number,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:l.commaOrSpaceSeparated,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:l.commaOrSpaceSeparated,rev:l.commaOrSpaceSeparated,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:l.commaOrSpaceSeparated,requiredFeatures:l.commaOrSpaceSeparated,requiredFonts:l.commaOrSpaceSeparated,requiredFormats:l.commaOrSpaceSeparated,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:l.number,specularExponent:l.number,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:l.number,strikethroughThickness:l.number,string:null,stroke:null,strokeDashArray:l.commaOrSpaceSeparated,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:l.number,strokeOpacity:l.number,strokeWidth:null,style:null,surfaceScale:l.number,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:l.commaOrSpaceSeparated,tabIndex:l.number,tableValues:null,target:null,targetX:l.number,targetY:l.number,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:l.commaOrSpaceSeparated,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:l.number,underlineThickness:l.number,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:l.number,values:null,vAlphabetic:l.number,vMathematical:l.number,vectorEffect:null,vHanging:l.number,vIdeographic:l.number,version:null,vertAdvY:l.number,vertOriginX:l.number,vertOriginY:l.number,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:l.number,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:u}),f=o({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),m=o({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:h}),E=o({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),T=i([c,d,f,m,E],"html"),g=i([c,p,f,m,E],"svg")},84823:(e,t,n)=>{"use strict";function r(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}n.d(t,{A:()=>eH});var i=n(34093),a=n(12556),s=n(1922),o=n(17915);let l="phrasing",c=["autolink","link","image","label"];function u(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function h(e){this.config.enter.autolinkProtocol.call(this,e)}function d(e){this.config.exit.autolinkProtocol.call(this,e)}function p(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];(0,i.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function f(e){this.config.exit.autolinkEmail.call(this,e)}function m(e){this.exit(e)}function E(e){!function(e,t,n){let r=(0,o.C)((n||{}).ignore||[]),i=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],r=-1;for(;++r<n.length;){var i;let e=n[r];t.push(["string"==typeof(i=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(i),"g"):i,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),a=-1;for(;++a<i.length;)(0,s.VG)(e,"text",l);function l(e,t){let n,s=-1;for(;++s<t.length;){let e=t[s],i=n?n.children:void 0;if(r(e,i?i.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],r=i[a][0],s=i[a][1],o=0,l=n.children.indexOf(e),c=!1,u=[];r.lastIndex=0;let h=r.exec(e.value);for(;h;){let n=h.index,i={index:h.index,input:h.input,stack:[...t,e]},a=s(...h,i);if("string"==typeof a&&(a=a.length>0?{type:"text",value:a}:void 0),!1===a?r.lastIndex=n+1:(o!==n&&u.push({type:"text",value:e.value.slice(o,n)}),Array.isArray(a)?u.push(...a):a&&u.push(a),o=n+h[0].length,c=!0),!r.global)break;h=r.exec(e.value)}return c?(o<e.value.length&&u.push({type:"text",value:e.value.slice(o)}),n.children.splice(l,1,...u)):u=[e],l+u.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,T],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,g]],{ignore:["link","linkReference"]})}function T(e,t,n,i,a){let s="";if(!A(a)||(/^w/i.test(t)&&(n=t+n,t="",s="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let o=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],i=n.indexOf(")"),a=r(e,"("),s=r(e,")");for(;-1!==i&&a>s;)e+=n.slice(0,i+1),i=(n=n.slice(i+1)).indexOf(")"),s++;return[e,n]}(n+i);if(!o[0])return!1;let l={type:"link",title:null,url:s+t+o[0],children:[{type:"text",value:t+o[0]}]};return o[1]?[l,{type:"text",value:o[1]}]:l}function g(e,t,n,r){return!(!A(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function A(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,a.Ny)(n)||(0,a.es)(n))&&(!t||47!==n)}var _=n(33386);function S(){this.buffer()}function I(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function k(){this.buffer()}function C(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function N(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteReference"===n.type),n.identifier=(0,_.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function D(e){this.exit(e)}function b(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteDefinition"===n.type),n.identifier=(0,_.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function y(e){this.exit(e)}function O(e,t,n,r){let i=n.createTracker(r),a=i.move("[^"),s=n.enter("footnoteReference"),o=n.enter("reference");return a+=i.move(n.safe(n.associationId(e),{after:"]",before:a})),o(),s(),a+=i.move("]")}function R(e,t,n){return 0===t?e:L(e,t,n)}function L(e,t,n){return(n?"":"    ")+e}O.peek=function(){return"["};let P=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function M(e){this.enter({type:"delete",children:[]},e)}function v(e){this.exit(e)}function x(e,t,n,r){let i=n.createTracker(r),a=n.enter("strikethrough"),s=i.move("~~");return s+=n.containerPhrasing(e,{...i.current(),before:s,after:"~"}),s+=i.move("~~"),a(),s}function w(e){return e.length}function B(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:114*(82===t||114===t)}x.peek=function(){return"~"};function F(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function H(e){let t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function U(e){return"&#x"+e.toString(16).toUpperCase()+";"}var G=n(49535);function Y(e,t,n){let r=(0,G.S)(e),i=(0,G.S)(t);return void 0===r?void 0===i?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!1}:{inside:!1,outside:!1}}n(88428);var z=n(4392);function V(e,t,n){let r=e.value||"",i="`",a=-1;for(;RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++a<n.unsafe.length;){let e,t=n.unsafe[a],i=n.compilePattern(t);if(t.atBreak)for(;e=i.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return i+r+i}function q(e,t){let n=(0,z.d)(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}V.peek=function(){return"`"};(0,o.C)(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);let W={inlineCode:V,listItem:function(e,t,n,r){let i=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),a=n.bulletCurrent||function(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}(n);t&&"list"===t.type&&t.ordered&&(a=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+a);let s=a.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(s=4*Math.ceil(s/4));let o=n.createTracker(r);o.move(a+" ".repeat(s-a.length)),o.shift(s);let l=n.enter("listItem"),c=n.indentLines(n.containerFlow(e,o.current()),function(e,t,n){return t?(n?"":" ".repeat(s))+e:(n?a:a+" ".repeat(s-a.length))+e});return l(),c}};function Q(e){let t=e._align;(0,i.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function j(e){this.exit(e),this.data.inTable=void 0}function K(e){this.enter({type:"tableRow",children:[]},e)}function X(e){this.exit(e)}function J(e){this.enter({type:"tableCell",children:[]},e)}function Z(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,$));let n=this.stack[this.stack.length-1];(0,i.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function $(e,t){return"|"===t?t:e}function ee(e){let t=this.stack[this.stack.length-2];(0,i.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function et(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];(0,i.ok)("paragraph"===e.type);let n=e.children[0];if(n&&"text"===n.type){let r,i=t.children,a=-1;for(;++a<i.length;){let e=i[a];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function en(e,t,n,r){let i=e.children[0],a="boolean"==typeof e.checked&&i&&"paragraph"===i.type,s="["+(e.checked?"x":" ")+"] ",o=n.createTracker(r);a&&o.move(s);let l=W.listItem(e,t,n,{...r,...o.current()});return a&&(l=l.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+s})),l}var er=n(69381);let ei={tokenize:function(e,t,n){let r=0;return function t(a){return(87===a||119===a)&&r<3?(r++,e.consume(a),t):46===a&&3===r?(e.consume(a),i):n(a)};function i(e){return null===e?n(e):t(e)}},partial:!0},ea={tokenize:function(e,t,n){let r,i,s;return o;function o(t){return 46===t||95===t?e.check(eo,c,l)(t):null===t||(0,a.Ee)(t)||(0,a.Ny)(t)||45!==t&&(0,a.es)(t)?c(t):(s=!0,e.consume(t),o)}function l(t){return 95===t?r=!0:(i=r,r=void 0),e.consume(t),o}function c(e){return i||r||!s?n(e):t(e)}},partial:!0},es={tokenize:function(e,t){let n=0,r=0;return i;function i(o){return 40===o?(n++,e.consume(o),i):41===o&&r<n?s(o):33===o||34===o||38===o||39===o||41===o||42===o||44===o||46===o||58===o||59===o||60===o||63===o||93===o||95===o||126===o?e.check(eo,t,s)(o):null===o||(0,a.Ee)(o)||(0,a.Ny)(o)?t(o):(e.consume(o),i)}function s(t){return 41===t&&r++,e.consume(t),i}},partial:!0},eo={tokenize:function(e,t,n){return r;function r(o){return 33===o||34===o||39===o||41===o||42===o||44===o||46===o||58===o||59===o||63===o||95===o||126===o?(e.consume(o),r):38===o?(e.consume(o),s):93===o?(e.consume(o),i):60===o||null===o||(0,a.Ee)(o)||(0,a.Ny)(o)?t(o):n(o)}function i(e){return null===e||40===e||91===e||(0,a.Ee)(e)||(0,a.Ny)(e)?t(e):r(e)}function s(t){return(0,a.CW)(t)?function t(i){return 59===i?(e.consume(i),r):(0,a.CW)(i)?(e.consume(i),t):n(i)}(t):n(t)}},partial:!0},el={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return(0,a.lV)(e)?n(e):t(e)}},partial:!0},ec={name:"wwwAutolink",tokenize:function(e,t,n){let r=this;return function(t){return 87!==t&&119!==t||!ef.call(r,r.previous)||eg(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(ei,e.attempt(ea,e.attempt(es,i),n),n)(t))};function i(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:ef},eu={name:"protocolAutolink",tokenize:function(e,t,n){let r=this,i="",s=!1;return function(t){return(72===t||104===t)&&em.call(r,r.previous)&&!eg(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(t),e.consume(t),o):n(t)};function o(t){if((0,a.CW)(t)&&i.length<5)return i+=String.fromCodePoint(t),e.consume(t),o;if(58===t){let n=i.toLowerCase();if("http"===n||"https"===n)return e.consume(t),l}return n(t)}function l(t){return 47===t?(e.consume(t),s)?c:(s=!0,l):n(t)}function c(t){return null===t||(0,a.JQ)(t)||(0,a.Ee)(t)||(0,a.Ny)(t)||(0,a.es)(t)?n(t):e.attempt(ea,e.attempt(es,u),n)(t)}function u(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:em},eh={name:"emailAutolink",tokenize:function(e,t,n){let r,i,s=this;return function(t){return!eT(t)||!eE.call(s,s.previous)||eg(s.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function t(r){return eT(r)?(e.consume(r),t):64===r?(e.consume(r),o):n(r)}(t))};function o(t){return 46===t?e.check(el,c,l)(t):45===t||95===t||(0,a.lV)(t)?(i=!0,e.consume(t),o):c(t)}function l(t){return e.consume(t),r=!0,o}function c(o){return i&&r&&(0,a.CW)(s.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(o)):n(o)}},previous:eE},ed={},ep=48;for(;ep<123;)ed[ep]=eh,58==++ep?ep=65:91===ep&&(ep=97);function ef(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,a.Ee)(e)}function em(e){return!(0,a.CW)(e)}function eE(e){return!(47===e||eT(e))}function eT(e){return 43===e||45===e||46===e||95===e||(0,a.lV)(e)}function eg(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}ed[43]=eh,ed[45]=eh,ed[46]=eh,ed[95]=eh,ed[72]=[eh,eu],ed[104]=[eh,eu],ed[87]=[eh,ec],ed[119]=[eh,ec];var eA=n(95333),e_=n(94581);let eS={tokenize:function(e,t,n){let r=this;return(0,e_.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function eI(e,t,n){let r,i=this,a=i.events.length,s=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);for(;a--;){let e=i.events[a][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(a){if(!r||!r._balanced)return n(a);let o=(0,_.B)(i.sliceSerialize({start:r.end,end:i.now()}));return 94===o.codePointAt(0)&&s.includes(o.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(a),e.exit("gfmFootnoteCallLabelMarker"),t(a)):n(a)}}function ek(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;let a={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},s={type:"chunkString",contentType:"string",start:Object.assign({},a.start),end:Object.assign({},a.end)},o=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",a,t],["enter",s,t],["exit",s,t],["exit",a,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...o),e}function eC(e,t,n){let r,i=this,s=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]),o=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),l};function l(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",c)}function c(l){if(o>999||93===l&&!r||null===l||91===l||(0,a.Ee)(l))return n(l);if(93===l){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return s.includes((0,_.B)(i.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(l)}return(0,a.Ee)(l)||(r=!0),o++,e.consume(l),92===l?u:c}function u(t){return 91===t||92===t||93===t?(e.consume(t),o++,c):c(t)}}function eN(e,t,n){let r,i,s=this,o=s.parser.gfmFootnotes||(s.parser.gfmFootnotes=[]),l=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),c};function c(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",u):n(t)}function u(t){if(l>999||93===t&&!i||null===t||91===t||(0,a.Ee)(t))return n(t);if(93===t){e.exit("chunkString");let n=e.exit("gfmFootnoteDefinitionLabelString");return r=(0,_.B)(s.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),d}return(0,a.Ee)(t)||(i=!0),l++,e.consume(t),92===t?h:u}function h(t){return 91===t||92===t||93===t?(e.consume(t),l++,u):u(t)}function d(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o.includes(r)||o.push(r),(0,e_.N)(e,p,"gfmFootnoteDefinitionWhitespace")):n(t)}function p(e){return t(e)}}function eD(e,t,n){return e.check(eA.B,t,e.attempt(eS,t,n))}function eb(e){e.exit("gfmFootnoteDefinition")}var ey=n(11603),eO=n(91877);class eR{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0!==n||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length,n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(let t of r)e.push(t);r=n.pop()}this.map.length=0}}function eL(e,t,n){let r,i=this,s=0,o=0;return function(e){let t=i.events.length-1;for(;t>-1;){let e=i.events[t][1].type;if("lineEnding"===e||"linePrefix"===e)t--;else break}let r=t>-1?i.events[t][1].type:null,a="tableHead"===r||"tableRow"===r?A:l;return a===A&&i.parser.lazy[i.now().line]?n(e):a(e)};function l(t){var n;return e.enter("tableHead"),e.enter("tableRow"),124===(n=t)||(r=!0,o+=1),c(n)}function c(t){return null===t?n(t):(0,a.HP)(t)?o>1?(o=0,i.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),d):n(t):(0,a.On)(t)?(0,e_.N)(e,c,"whitespace")(t):(o+=1,r&&(r=!1,s+=1),124===t)?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,c):(e.enter("data"),u(t))}function u(t){return null===t||124===t||(0,a.Ee)(t)?(e.exit("data"),c(t)):(e.consume(t),92===t?h:u)}function h(t){return 92===t||124===t?(e.consume(t),u):u(t)}function d(t){return(i.interrupt=!1,i.parser.lazy[i.now().line])?n(t):(e.enter("tableDelimiterRow"),r=!1,(0,a.On)(t))?(0,e_.N)(e,p,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):p(t)}function p(t){return 45===t||58===t?m(t):124===t?(r=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),f):n(t)}function f(t){return(0,a.On)(t)?(0,e_.N)(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(o+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),E):45===t?(o+=1,E(t)):null===t||(0,a.HP)(t)?g(t):n(t)}function E(t){return 45===t?(e.enter("tableDelimiterFiller"),function t(n){return 45===n?(e.consume(n),t):58===n?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(n),e.exit("tableDelimiterMarker"),T):(e.exit("tableDelimiterFiller"),T(n))}(t)):n(t)}function T(t){return(0,a.On)(t)?(0,e_.N)(e,g,"whitespace")(t):g(t)}function g(i){if(124===i)return p(i);if(null===i||(0,a.HP)(i))return r&&s===o?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(i)):n(i);return n(i)}function A(t){return e.enter("tableRow"),_(t)}function _(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),_):null===n||(0,a.HP)(n)?(e.exit("tableRow"),t(n)):(0,a.On)(n)?(0,e_.N)(e,_,"whitespace")(n):(e.enter("data"),S(n))}function S(t){return null===t||124===t||(0,a.Ee)(t)?(e.exit("data"),_(t)):(e.consume(t),92===t?I:S)}function I(t){return 92===t||124===t?(e.consume(t),S):S(t)}}function eP(e,t){let n,r,i,a=-1,s=!0,o=0,l=[0,0,0,0],c=[0,0,0,0],u=!1,h=0,d=new eR;for(;++a<e.length;){let p=e[a],f=p[1];"enter"===p[0]?"tableHead"===f.type?(u=!1,0!==h&&(ev(d,t,h,n,r),r=void 0,h=0),n={type:"table",start:Object.assign({},f.start),end:Object.assign({},f.end)},d.add(a,0,[["enter",n,t]])):"tableRow"===f.type||"tableDelimiterRow"===f.type?(s=!0,i=void 0,l=[0,0,0,0],c=[0,a+1,0,0],u&&(u=!1,r={type:"tableBody",start:Object.assign({},f.start),end:Object.assign({},f.end)},d.add(a,0,[["enter",r,t]])),o="tableDelimiterRow"===f.type?2:r?3:1):o&&("data"===f.type||"tableDelimiterMarker"===f.type||"tableDelimiterFiller"===f.type)?(s=!1,0===c[2]&&(0!==l[1]&&(c[0]=c[1],i=eM(d,t,l,o,void 0,i),l=[0,0,0,0]),c[2]=a)):"tableCellDivider"===f.type&&(s?s=!1:(0!==l[1]&&(c[0]=c[1],i=eM(d,t,l,o,void 0,i)),c=[(l=c)[1],a,0,0])):"tableHead"===f.type?(u=!0,h=a):"tableRow"===f.type||"tableDelimiterRow"===f.type?(h=a,0!==l[1]?(c[0]=c[1],i=eM(d,t,l,o,a,i)):0!==c[1]&&(i=eM(d,t,c,o,a,i)),o=0):o&&("data"===f.type||"tableDelimiterMarker"===f.type||"tableDelimiterFiller"===f.type)&&(c[3]=a)}for(0!==h&&ev(d,t,h,n,r),d.consume(t.events),a=-1;++a<t.events.length;){let e=t.events[a];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,t){let n=!1,r=[];for(;t<e.length;){let i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}(t.events,a))}return e}function eM(e,t,n,r,i,a){0!==n[0]&&(a.end=Object.assign({},ex(t.events,n[0])),e.add(n[0],0,[["exit",a,t]]));let s=ex(t.events,n[1]);if(a={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},s),end:Object.assign({},s)},e.add(n[1],0,[["enter",a,t]]),0!==n[2]){let i=ex(t.events,n[2]),a=ex(t.events,n[3]),s={type:"tableContent",start:Object.assign({},i),end:Object.assign({},a)};if(e.add(n[2],0,[["enter",s,t]]),2!==r){let r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){let t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",s,t]])}return void 0!==i&&(a.end=Object.assign({},ex(t.events,i)),e.add(i,0,[["exit",a,t]]),a=void 0),a}function ev(e,t,n,r,i){let a=[],s=ex(t.events,n);i&&(i.end=Object.assign({},s),a.push(["exit",i,t])),r.end=Object.assign({},s),a.push(["exit",r,t]),e.add(n+1,0,a)}function ex(e,t){let n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}let ew={name:"tasklistCheck",tokenize:function(e,t,n){let r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),i):n(t)};function i(t){return(0,a.Ee)(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),s):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),s):n(t)}function s(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),o):n(t)}function o(r){return(0,a.HP)(r)?t(r):(0,a.On)(r)?e.check({tokenize:eB},t,n)(r):n(r)}}};function eB(e,t,n){return(0,e_.N)(e,function(e){return null===e?n(e):t(e)},"whitespace")}let eF={};function eH(e){let t,n=e||eF,r=this.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),a=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),s=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push((0,er.y)([{text:ed},{document:{91:{name:"gfmFootnoteDefinition",tokenize:eN,continuation:{tokenize:eD},exit:eb}},text:{91:{name:"gfmFootnoteCall",tokenize:eC},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:eI,resolveTo:ek}}},function(e){let t=(e||{}).singleTilde,n={name:"strikethrough",tokenize:function(e,n,r){let i=this.previous,a=this.events,s=0;return function(o){return 126===i&&"characterEscape"!==a[a.length-1][1].type?r(o):(e.enter("strikethroughSequenceTemporary"),function a(o){let l=(0,G.S)(i);if(126===o)return s>1?r(o):(e.consume(o),s++,a);if(s<2&&!t)return r(o);let c=e.exit("strikethroughSequenceTemporary"),u=(0,G.S)(o);return c._open=!u||2===u&&!!l,c._close=!l||2===l&&!!u,n(o)}(o))}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";let i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},a={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},s=[["enter",i,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",a,t]],o=t.parser.constructs.insideSpan.null;o&&(0,ey.m)(s,s.length,0,(0,eO.W)(o,e.slice(r+1,n),t)),(0,ey.m)(s,s.length,0,[["exit",a,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),(0,ey.m)(e,r-1,n-r+3,s),n=r+s.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}(n),{flow:{null:{name:"table",tokenize:eL,resolveAll:eP}}},{text:{91:ew}}])),a.push([{transforms:[E],enter:{literalAutolink:u,literalAutolinkEmail:h,literalAutolinkHttp:h,literalAutolinkWww:h},exit:{literalAutolink:m,literalAutolinkEmail:f,literalAutolinkHttp:d,literalAutolinkWww:p}},{enter:{gfmFootnoteCallString:S,gfmFootnoteCall:I,gfmFootnoteDefinitionLabelString:k,gfmFootnoteDefinition:C},exit:{gfmFootnoteCallString:N,gfmFootnoteCall:D,gfmFootnoteDefinitionLabelString:b,gfmFootnoteDefinition:y}},{canContainEols:["delete"],enter:{strikethrough:M},exit:{strikethrough:v}},{enter:{table:Q,tableData:J,tableHeader:J,tableRow:K},exit:{codeText:Z,table:j,tableData:X,tableHeader:X,tableRow:X}},{exit:{taskListCheckValueChecked:ee,taskListCheckValueUnchecked:ee,paragraph:et}}]),s.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:l,notInConstruct:c},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:l,notInConstruct:c},{character:":",before:"[ps]",after:"\\/",inConstruct:l,notInConstruct:c}]},(t=!1,n&&n.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,i){let a=r.createTracker(i),s=a.move("[^"),o=r.enter("footnoteDefinition"),l=r.enter("label");return s+=a.move(r.safe(r.associationId(e),{before:s,after:"]"})),l(),s+=a.move("]:"),e.children&&e.children.length>0&&(a.shift(4),s+=a.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,a.current()),t?L:R))),o(),s},footnoteReference:O},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:P}],handlers:{delete:x}},function(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,a=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=W.inlineCode(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return o(function(e,t,n){let r=e.children,i=-1,a=[],s=t.enter("table");for(;++i<r.length;)a[i]=l(r[i],t,n);return s(),a}(e,n,r),e.align)},tableCell:s,tableRow:function(e,t,n,r){let i=o([l(e,n,r)]);return i.slice(0,i.indexOf("\n"))}}};function s(e,t,n,r){let i=n.enter("tableCell"),s=n.enter("phrasing"),o=n.containerPhrasing(e,{...r,before:a,after:a});return s(),i(),o}function o(e,t){return function(e,t){let n=t||{},r=(n.align||[]).concat(),i=n.stringLength||w,a=[],s=[],o=[],l=[],c=0,u=-1;for(;++u<e.length;){let t=[],r=[],a=-1;for(e[u].length>c&&(c=e[u].length);++a<e[u].length;){var h;let s=null==(h=e[u][a])?"":String(h);if(!1!==n.alignDelimiters){let e=i(s);r[a]=e,(void 0===l[a]||e>l[a])&&(l[a]=e)}t.push(s)}s[u]=t,o[u]=r}let d=-1;if("object"==typeof r&&"length"in r)for(;++d<c;)a[d]=B(r[d]);else{let e=B(r);for(;++d<c;)a[d]=e}d=-1;let p=[],f=[];for(;++d<c;){let e=a[d],t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,l[d]-t.length-r.length),s=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&((i=t.length+i+r.length)>l[d]&&(l[d]=i),f[d]=i),p[d]=s}s.splice(1,0,p),o.splice(1,0,f),u=-1;let m=[];for(;++u<s.length;){let e=s[u],t=o[u];d=-1;let r=[];for(;++d<c;){let i=e[d]||"",s="",o="";if(!1!==n.alignDelimiters){let e=l[d]-(t[d]||0),n=a[d];114===n?s=" ".repeat(e):99===n?e%2?(s=" ".repeat(e/2+.5),o=" ".repeat(e/2-.5)):o=s=" ".repeat(e/2):o=" ".repeat(e)}!1===n.delimiterStart||d||r.push("|"),!1!==n.padding&&(!1!==n.alignDelimiters||""!==i)&&(!1!==n.delimiterStart||d)&&r.push(" "),!1!==n.alignDelimiters&&r.push(s),r.push(i),!1!==n.alignDelimiters&&r.push(o),!1!==n.padding&&r.push(" "),(!1!==n.delimiterEnd||d!==c-1)&&r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:i})}function l(e,t,n){let r=e.children,i=-1,a=[],o=t.enter("tableRow");for(;++i<r.length;)a[i]=s(r[i],e,t,n);return o(),a}}(n),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:en}}]})}},85339:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},87924:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),a="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;a?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(36301))},88428:(e,t,n)=>{"use strict";n.d(t,{YR:()=>i});var r=n(1922);function i(e,t,n,i){let a,s,o;"function"==typeof t&&"function"!=typeof n?(s=void 0,o=t,a=n):(s=t,o=n,a=i),(0,r.VG)(e,s,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return o(e,r,n)},a)}},91877:(e,t,n)=>{"use strict";function r(e,t,n){let r=[],i=-1;for(;++i<e.length;){let a=e[i].resolveAll;a&&!r.includes(a)&&(t=a(t,n),r.push(a))}return t}n.d(t,{W:()=>r})},94581:(e,t,n)=>{"use strict";n.d(t,{N:()=>i});var r=n(12556);function i(e,t,n,i){let a=i?i-1:Number.POSITIVE_INFINITY,s=0;return function(i){return(0,r.On)(i)?(e.enter(n),function i(o){return(0,r.On)(o)&&s++<a?(e.consume(o),i):(e.exit(n),t(o))}(i)):t(i)}}},95333:(e,t,n)=>{"use strict";n.d(t,{B:()=>a});var r=n(94581),i=n(12556);let a={partial:!0,tokenize:function(e,t,n){return function(t){return(0,i.On)(t)?(0,r.N)(e,a,"linePrefix")(t):a(t)};function a(e){return null===e||(0,i.HP)(e)?t(e):n(e)}}}}}]);