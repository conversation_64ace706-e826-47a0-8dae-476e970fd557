"use client";

import React, { useState } from 'react';
import { OnboardingStepProps } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, ArrowLeft, Loader2, Database, MessageSquare, BarChart3, Sparkles } from 'lucide-react';
import OnboardingLoadingOverlay from '../OnboardingLoadingOverlay';

const nextSteps = [
  {
    icon: Database,
    title: 'Connect Your First Database',
    description: 'Start by connecting to your data sources',
    action: 'Go to Data Sources',
  },
  {
    icon: MessageSquare,
    title: 'Ask Your First Question',
    description: 'Try asking questions about your data in natural language',
    action: 'Start Chatting',
  },
  {
    icon: BarChart3,
    title: 'Explore Reports',
    description: 'View and create reports from your data',
    action: 'View Reports',
  },
];

export default function CompletionStep({ onPrevious, onComplete, isLoading }: OnboardingStepProps) {
  const [isCompleting, setIsCompleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleComplete = async () => {
    setIsCompleting(true);
    setError(null);

    try {
      await onComplete();
      // Success - the onComplete function will handle the redirect
    } catch (err: any) {
      console.error('Failed to complete onboarding:', err);

      // Provide more specific error messages
      let errorMessage = 'Failed to complete setup. Please try again.';

      if (err.message?.includes('required steps')) {
        errorMessage = err.message;
      } else if (err.message?.includes('network') || err.message?.includes('fetch')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (err.message?.includes('server') || err.message?.includes('500')) {
        errorMessage = 'Server error. Please try again in a moment.';
      } else if (err.message?.includes('unauthorized') || err.message?.includes('401')) {
        errorMessage = 'Session expired. Please log in again.';
      }

      setError(errorMessage);
      setIsCompleting(false);
    }
  };

  return (
    <>
      <OnboardingLoadingOverlay
        isVisible={isCompleting}
        message="Completing your setup..."
        submessage="Finalizing your Agent Platform account"
        type="completing"
      />

      <Card className="border-0 shadow-lg">
      <CardHeader className="text-center pb-6">
        <div className="mx-auto mb-4 p-3 bg-green-100 dark:bg-green-900 rounded-full w-fit">
          <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>
        <CardTitle className="text-2xl font-bold text-slate-900 dark:text-slate-100">
          You're All Set!
        </CardTitle>
        <CardDescription className="text-slate-600 dark:text-slate-400 mt-2">
          Welcome to Agent Platform! Your account is ready and you can start exploring your data.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Success message */}
        <div className="text-center py-4">
          <div className="mx-auto mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg w-fit">
            <Sparkles className="h-12 w-12 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
            Setup Complete!
          </h3>
          <p className="text-slate-600 dark:text-slate-400">
            Your Agent Platform account is now configured and ready to use.
          </p>
        </div>

        {/* Next steps */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-slate-900 dark:text-slate-100 text-center">
            What's Next?
          </h4>
          
          <div className="grid gap-4">
            {nextSteps.map((step, index) => (
              <div
                key={index}
                className="flex items-center space-x-4 p-4 rounded-lg bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700"
              >
                <div className="p-2 bg-white dark:bg-slate-700 rounded-lg">
                  <step.icon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="flex-1">
                  <h5 className="font-semibold text-slate-900 dark:text-slate-100">
                    {step.title}
                  </h5>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    {step.description}
                  </p>
                </div>
                <div className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                  {step.action}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-start space-x-3">
              <div className="flex-1">
                <p className="text-sm text-red-600 dark:text-red-400 font-medium mb-1">
                  Setup Error
                </p>
                <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              </div>
            </div>
            <div className="mt-3">
              <Button
                onClick={handleComplete}
                variant="outline"
                size="sm"
                className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-700 dark:hover:bg-red-900/20"
              >
                Try Again
              </Button>
            </div>
          </div>
        )}

        {/* Navigation buttons */}
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={onPrevious}
            disabled={isLoading || isCompleting}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Previous</span>
          </Button>

          <Button
            onClick={handleComplete}
            disabled={isLoading || isCompleting}
            size="lg"
            className="px-8 flex items-center space-x-2"
          >
            {isCompleting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Completing Setup...</span>
              </>
            ) : (
              <>
                <span>Complete Setup</span>
                <CheckCircle className="h-4 w-4" />
              </>
            )}
          </Button>
        </div>

        <div className="text-center text-sm text-slate-500 dark:text-slate-400">
          <p>
            You can always access these features from your dashboard or the navigation menu.
          </p>
        </div>
      </CardContent>
    </Card>
    </>
  );
}
