module.exports = {

"[project]/node_modules/react-grid-layout/build/fastRGLPropsEqual.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// this file was prevaled
module.exports = function fastRGLPropsEqual(a, b, isEqualImpl) {
    if (a === b) return true;
    return a.className === b.className && isEqualImpl(a.style, b.style) && a.width === b.width && a.autoSize === b.autoSize && a.cols === b.cols && a.draggableCancel === b.draggableCancel && a.draggableHandle === b.draggableHandle && isEqualImpl(a.verticalCompact, b.verticalCompact) && isEqualImpl(a.compactType, b.compactType) && isEqualImpl(a.layout, b.layout) && isEqualImpl(a.margin, b.margin) && isEqualImpl(a.containerPadding, b.containerPadding) && a.rowHeight === b.rowHeight && a.maxRows === b.maxRows && a.isBounded === b.isBounded && a.isDraggable === b.isDraggable && a.isResizable === b.isResizable && a.allowOverlap === b.allowOverlap && a.preventCollision === b.preventCollision && a.useCSSTransforms === b.useCSSTransforms && a.transformScale === b.transformScale && a.isDroppable === b.isDroppable && isEqualImpl(a.resizeHandles, b.resizeHandles) && isEqualImpl(a.resizeHandle, b.resizeHandle) && a.onLayoutChange === b.onLayoutChange && a.onDragStart === b.onDragStart && a.onDrag === b.onDrag && a.onDragStop === b.onDragStop && a.onResizeStart === b.onResizeStart && a.onResize === b.onResize && a.onResizeStop === b.onResizeStop && a.onDrop === b.onDrop && isEqualImpl(a.droppingItem, b.droppingItem) && isEqualImpl(a.innerRef, b.innerRef);
};
}}),
"[project]/node_modules/react-grid-layout/build/utils.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.bottom = bottom;
exports.childrenEqual = childrenEqual;
exports.cloneLayout = cloneLayout;
exports.cloneLayoutItem = cloneLayoutItem;
exports.collides = collides;
exports.compact = compact;
exports.compactItem = compactItem;
exports.compactType = compactType;
exports.correctBounds = correctBounds;
exports.fastPositionEqual = fastPositionEqual;
exports.fastRGLPropsEqual = void 0;
exports.getAllCollisions = getAllCollisions;
exports.getFirstCollision = getFirstCollision;
exports.getLayoutItem = getLayoutItem;
exports.getStatics = getStatics;
exports.modifyLayout = modifyLayout;
exports.moveElement = moveElement;
exports.moveElementAwayFromCollision = moveElementAwayFromCollision;
exports.noop = void 0;
exports.perc = perc;
exports.resizeItemInDirection = resizeItemInDirection;
exports.setTopLeft = setTopLeft;
exports.setTransform = setTransform;
exports.sortLayoutItems = sortLayoutItems;
exports.sortLayoutItemsByColRow = sortLayoutItemsByColRow;
exports.sortLayoutItemsByRowCol = sortLayoutItemsByRowCol;
exports.synchronizeLayoutWithChildren = synchronizeLayoutWithChildren;
exports.validateLayout = validateLayout;
exports.withLayoutItem = withLayoutItem;
var _fastEquals = __turbopack_context__.r("[project]/node_modules/fast-equals/dist/fast-equals.esm.js [app-ssr] (ecmascript)");
var _react = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
/*:: import type {
  ChildrenArray as ReactChildrenArray,
  Element as ReactElement
} from "react";*/ /*:: export type ResizeHandleAxis =
  | "s"
  | "w"
  | "e"
  | "n"
  | "sw"
  | "nw"
  | "se"
  | "ne";*/ /*:: export type LayoutItem = {
  w: number,
  h: number,
  x: number,
  y: number,
  i: string,
  minW?: number,
  minH?: number,
  maxW?: number,
  maxH?: number,
  moved?: boolean,
  static?: boolean,
  isDraggable?: ?boolean,
  isResizable?: ?boolean,
  resizeHandles?: Array<ResizeHandleAxis>,
  isBounded?: ?boolean
};*/ /*:: export type Layout = $ReadOnlyArray<LayoutItem>;*/ /*:: export type Position = {
  left: number,
  top: number,
  width: number,
  height: number
};*/ /*:: export type ReactDraggableCallbackData = {
  node: HTMLElement,
  x?: number,
  y?: number,
  deltaX: number,
  deltaY: number,
  lastX?: number,
  lastY?: number
};*/ /*:: export type PartialPosition = { left: number, top: number };*/ /*:: export type DroppingPosition = { left: number, top: number, e: Event };*/ /*:: export type Size = { width: number, height: number };*/ /*:: export type GridDragEvent = {
  e: Event,
  node: HTMLElement,
  newPosition: PartialPosition
};*/ /*:: export type GridResizeEvent = {
  e: Event,
  node: HTMLElement,
  size: Size,
  handle: string
};*/ /*:: export type DragOverEvent = MouseEvent & {
  nativeEvent: {
    layerX: number,
    layerY: number,
    ...Event
  }
};*/ /*:: export type Pick<FromType, Properties: { [string]: 0 }> = $Exact<
  $ObjMapi<Properties, <K, V>(k: K, v: V) => $ElementType<FromType, K>>
>;*/ // Helpful port from TS
/*:: type REl = ReactElement<any>;*/ /*:: export type ReactChildren = ReactChildrenArray<REl>;*/ /*:: export type EventCallback = (
  Layout,
  oldItem: ?LayoutItem,
  newItem: ?LayoutItem,
  placeholder: ?LayoutItem,
  Event,
  ?HTMLElement
) => void;*/ // All callbacks are of the signature (layout, oldItem, newItem, placeholder, e).
/*:: export type CompactType = ?("horizontal" | "vertical");*/ const isProduction = ("TURBOPACK compile-time value", "development") === "production";
const DEBUG = false;
/**
 * Return the bottom coordinate of the layout.
 *
 * @param  {Array} layout Layout array.
 * @return {Number}       Bottom coordinate.
 */ function bottom(layout /*: Layout*/ ) /*: number*/ {
    let max = 0, bottomY;
    for(let i = 0, len = layout.length; i < len; i++){
        bottomY = layout[i].y + layout[i].h;
        if (bottomY > max) max = bottomY;
    }
    return max;
}
function cloneLayout(layout /*: Layout*/ ) /*: Layout*/ {
    const newLayout = Array(layout.length);
    for(let i = 0, len = layout.length; i < len; i++){
        newLayout[i] = cloneLayoutItem(layout[i]);
    }
    return newLayout;
}
// Modify a layoutItem inside a layout. Returns a new Layout,
// does not mutate. Carries over all other LayoutItems unmodified.
function modifyLayout(layout /*: Layout*/ , layoutItem /*: LayoutItem*/ ) /*: Layout*/ {
    const newLayout = Array(layout.length);
    for(let i = 0, len = layout.length; i < len; i++){
        if (layoutItem.i === layout[i].i) {
            newLayout[i] = layoutItem;
        } else {
            newLayout[i] = layout[i];
        }
    }
    return newLayout;
}
// Function to be called to modify a layout item.
// Does defensive clones to ensure the layout is not modified.
function withLayoutItem(layout /*: Layout*/ , itemKey /*: string*/ , cb /*: LayoutItem => LayoutItem*/ ) /*: [Layout, ?LayoutItem]*/ {
    let item = getLayoutItem(layout, itemKey);
    if (!item) return [
        layout,
        null
    ];
    item = cb(cloneLayoutItem(item)); // defensive clone then modify
    // FIXME could do this faster if we already knew the index
    layout = modifyLayout(layout, item);
    return [
        layout,
        item
    ];
}
// Fast path to cloning, since this is monomorphic
function cloneLayoutItem(layoutItem /*: LayoutItem*/ ) /*: LayoutItem*/ {
    return {
        w: layoutItem.w,
        h: layoutItem.h,
        x: layoutItem.x,
        y: layoutItem.y,
        i: layoutItem.i,
        minW: layoutItem.minW,
        maxW: layoutItem.maxW,
        minH: layoutItem.minH,
        maxH: layoutItem.maxH,
        moved: Boolean(layoutItem.moved),
        static: Boolean(layoutItem.static),
        // These can be null/undefined
        isDraggable: layoutItem.isDraggable,
        isResizable: layoutItem.isResizable,
        resizeHandles: layoutItem.resizeHandles,
        isBounded: layoutItem.isBounded
    };
}
/**
 * Comparing React `children` is a bit difficult. This is a good way to compare them.
 * This will catch differences in keys, order, and length.
 */ function childrenEqual(a /*: ReactChildren*/ , b /*: ReactChildren*/ ) /*: boolean*/ {
    return (0, _fastEquals.deepEqual)(_react.default.Children.map(a, (c)=>c?.key), _react.default.Children.map(b, (c)=>c?.key)) && (0, _fastEquals.deepEqual)(_react.default.Children.map(a, (c)=>c?.props["data-grid"]), _react.default.Children.map(b, (c)=>c?.props["data-grid"]));
}
/**
 * See `fastRGLPropsEqual.js`.
 * We want this to run as fast as possible - it is called often - and to be
 * resilient to new props that we add. So rather than call lodash.isEqual,
 * which isn't suited to comparing props very well, we use this specialized
 * function in conjunction with preval to generate the fastest possible comparison
 * function, tuned for exactly our props.
 */ /*:: type FastRGLPropsEqual = (Object, Object, Function) => boolean;*/ const fastRGLPropsEqual /*: FastRGLPropsEqual*/  = exports.fastRGLPropsEqual = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/fastRGLPropsEqual.js [app-ssr] (ecmascript)");
// Like the above, but a lot simpler.
function fastPositionEqual(a /*: Position*/ , b /*: Position*/ ) /*: boolean*/ {
    return a.left === b.left && a.top === b.top && a.width === b.width && a.height === b.height;
}
/**
 * Given two layoutitems, check if they collide.
 */ function collides(l1 /*: LayoutItem*/ , l2 /*: LayoutItem*/ ) /*: boolean*/ {
    if (l1.i === l2.i) return false; // same element
    if (l1.x + l1.w <= l2.x) return false; // l1 is left of l2
    if (l1.x >= l2.x + l2.w) return false; // l1 is right of l2
    if (l1.y + l1.h <= l2.y) return false; // l1 is above l2
    if (l1.y >= l2.y + l2.h) return false; // l1 is below l2
    return true; // boxes overlap
}
/**
 * Given a layout, compact it. This involves going down each y coordinate and removing gaps
 * between items.
 *
 * Does not modify layout items (clones). Creates a new layout array.
 *
 * @param  {Array} layout Layout.
 * @param  {Boolean} verticalCompact Whether or not to compact the layout
 *   vertically.
 * @param  {Boolean} allowOverlap When `true`, allows overlapping grid items.
 * @return {Array}       Compacted Layout.
 */ function compact(layout /*: Layout*/ , compactType /*: CompactType*/ , cols /*: number*/ , allowOverlap /*: ?boolean*/ ) /*: Layout*/ {
    // Statics go in the compareWith array right away so items flow around them.
    const compareWith = getStatics(layout);
    // We go through the items by row and column.
    const sorted = sortLayoutItems(layout, compactType);
    // Holding for new items.
    const out = Array(layout.length);
    for(let i = 0, len = sorted.length; i < len; i++){
        let l = cloneLayoutItem(sorted[i]);
        // Don't move static elements
        if (!l.static) {
            l = compactItem(compareWith, l, compactType, cols, sorted, allowOverlap);
            // Add to comparison array. We only collide with items before this one.
            // Statics are already in this array.
            compareWith.push(l);
        }
        // Add to output array to make sure they still come out in the right order.
        out[layout.indexOf(sorted[i])] = l;
        // Clear moved flag, if it exists.
        l.moved = false;
    }
    return out;
}
const heightWidth = {
    x: "w",
    y: "h"
};
/**
 * Before moving item down, it will check if the movement will cause collisions and move those items down before.
 */ function resolveCompactionCollision(layout /*: Layout*/ , item /*: LayoutItem*/ , moveToCoord /*: number*/ , axis /*: "x" | "y"*/ ) {
    const sizeProp = heightWidth[axis];
    item[axis] += 1;
    const itemIndex = layout.map((layoutItem)=>{
        return layoutItem.i;
    }).indexOf(item.i);
    // Go through each item we collide with.
    for(let i = itemIndex + 1; i < layout.length; i++){
        const otherItem = layout[i];
        // Ignore static items
        if (otherItem.static) continue;
        // Optimization: we can break early if we know we're past this el
        // We can do this b/c it's a sorted layout
        if (otherItem.y > item.y + item.h) break;
        if (collides(item, otherItem)) {
            resolveCompactionCollision(layout, otherItem, moveToCoord + item[sizeProp], axis);
        }
    }
    item[axis] = moveToCoord;
}
/**
 * Compact an item in the layout.
 *
 * Modifies item.
 *
 */ function compactItem(compareWith /*: Layout*/ , l /*: LayoutItem*/ , compactType /*: CompactType*/ , cols /*: number*/ , fullLayout /*: Layout*/ , allowOverlap /*: ?boolean*/ ) /*: LayoutItem*/ {
    const compactV = compactType === "vertical";
    const compactH = compactType === "horizontal";
    if (compactV) {
        // Bottom 'y' possible is the bottom of the layout.
        // This allows you to do nice stuff like specify {y: Infinity}
        // This is here because the layout must be sorted in order to get the correct bottom `y`.
        l.y = Math.min(bottom(compareWith), l.y);
        // Move the element up as far as it can go without colliding.
        while(l.y > 0 && !getFirstCollision(compareWith, l)){
            l.y--;
        }
    } else if (compactH) {
        // Move the element left as far as it can go without colliding.
        while(l.x > 0 && !getFirstCollision(compareWith, l)){
            l.x--;
        }
    }
    // Move it down, and keep moving it down if it's colliding.
    let collides;
    // Checking the compactType null value to avoid breaking the layout when overlapping is allowed.
    while((collides = getFirstCollision(compareWith, l)) && !(compactType === null && allowOverlap)){
        if (compactH) {
            resolveCompactionCollision(fullLayout, l, collides.x + collides.w, "x");
        } else {
            resolveCompactionCollision(fullLayout, l, collides.y + collides.h, "y");
        }
        // Since we can't grow without bounds horizontally, if we've overflown, let's move it down and try again.
        if (compactH && l.x + l.w > cols) {
            l.x = cols - l.w;
            l.y++;
            // ALso move element as left as we can
            while(l.x > 0 && !getFirstCollision(compareWith, l)){
                l.x--;
            }
        }
    }
    // Ensure that there are no negative positions
    l.y = Math.max(l.y, 0);
    l.x = Math.max(l.x, 0);
    return l;
}
/**
 * Given a layout, make sure all elements fit within its bounds.
 *
 * Modifies layout items.
 *
 * @param  {Array} layout Layout array.
 * @param  {Number} bounds Number of columns.
 */ function correctBounds(layout /*: Layout*/ , bounds /*: { cols: number }*/ ) /*: Layout*/ {
    const collidesWith = getStatics(layout);
    for(let i = 0, len = layout.length; i < len; i++){
        const l = layout[i];
        // Overflows right
        if (l.x + l.w > bounds.cols) l.x = bounds.cols - l.w;
        // Overflows left
        if (l.x < 0) {
            l.x = 0;
            l.w = bounds.cols;
        }
        if (!l.static) collidesWith.push(l);
        else {
            // If this is static and collides with other statics, we must move it down.
            // We have to do something nicer than just letting them overlap.
            while(getFirstCollision(collidesWith, l)){
                l.y++;
            }
        }
    }
    return layout;
}
/**
 * Get a layout item by ID. Used so we can override later on if necessary.
 *
 * @param  {Array}  layout Layout array.
 * @param  {String} id     ID
 * @return {LayoutItem}    Item at ID.
 */ function getLayoutItem(layout /*: Layout*/ , id /*: string*/ ) /*: ?LayoutItem*/ {
    for(let i = 0, len = layout.length; i < len; i++){
        if (layout[i].i === id) return layout[i];
    }
}
/**
 * Returns the first item this layout collides with.
 * It doesn't appear to matter which order we approach this from, although
 * perhaps that is the wrong thing to do.
 *
 * @param  {Object} layoutItem Layout item.
 * @return {Object|undefined}  A colliding layout item, or undefined.
 */ function getFirstCollision(layout /*: Layout*/ , layoutItem /*: LayoutItem*/ ) /*: ?LayoutItem*/ {
    for(let i = 0, len = layout.length; i < len; i++){
        if (collides(layout[i], layoutItem)) return layout[i];
    }
}
function getAllCollisions(layout /*: Layout*/ , layoutItem /*: LayoutItem*/ ) /*: Array<LayoutItem>*/ {
    return layout.filter((l)=>collides(l, layoutItem));
}
/**
 * Get all static elements.
 * @param  {Array} layout Array of layout objects.
 * @return {Array}        Array of static layout items..
 */ function getStatics(layout /*: Layout*/ ) /*: Array<LayoutItem>*/ {
    return layout.filter((l)=>l.static);
}
/**
 * Move an element. Responsible for doing cascading movements of other elements.
 *
 * Modifies layout items.
 *
 * @param  {Array}      layout            Full layout to modify.
 * @param  {LayoutItem} l                 element to move.
 * @param  {Number}     [x]               X position in grid units.
 * @param  {Number}     [y]               Y position in grid units.
 */ function moveElement(layout /*: Layout*/ , l /*: LayoutItem*/ , x /*: ?number*/ , y /*: ?number*/ , isUserAction /*: ?boolean*/ , preventCollision /*: ?boolean*/ , compactType /*: CompactType*/ , cols /*: number*/ , allowOverlap /*: ?boolean*/ ) /*: Layout*/ {
    // If this is static and not explicitly enabled as draggable,
    // no move is possible, so we can short-circuit this immediately.
    if (l.static && l.isDraggable !== true) return layout;
    // Short-circuit if nothing to do.
    if (l.y === y && l.x === x) return layout;
    log(`Moving element ${l.i} to [${String(x)},${String(y)}] from [${l.x},${l.y}]`);
    const oldX = l.x;
    const oldY = l.y;
    // This is quite a bit faster than extending the object
    if (typeof x === "number") l.x = x;
    if (typeof y === "number") l.y = y;
    l.moved = true;
    // If this collides with anything, move it.
    // When doing this comparison, we have to sort the items we compare with
    // to ensure, in the case of multiple collisions, that we're getting the
    // nearest collision.
    let sorted = sortLayoutItems(layout, compactType);
    const movingUp = compactType === "vertical" && typeof y === "number" ? oldY >= y : compactType === "horizontal" && typeof x === "number" ? oldX >= x : false;
    // $FlowIgnore acceptable modification of read-only array as it was recently cloned
    if (movingUp) sorted = sorted.reverse();
    const collisions = getAllCollisions(sorted, l);
    const hasCollisions = collisions.length > 0;
    // We may have collisions. We can short-circuit if we've turned off collisions or
    // allowed overlap.
    if (hasCollisions && allowOverlap) {
        // Easy, we don't need to resolve collisions. But we *did* change the layout,
        // so clone it on the way out.
        return cloneLayout(layout);
    } else if (hasCollisions && preventCollision) {
        // If we are preventing collision but not allowing overlap, we need to
        // revert the position of this element so it goes to where it came from, rather
        // than the user's desired location.
        log(`Collision prevented on ${l.i}, reverting.`);
        l.x = oldX;
        l.y = oldY;
        l.moved = false;
        return layout; // did not change so don't clone
    }
    // Move each item that collides away from this element.
    for(let i = 0, len = collisions.length; i < len; i++){
        const collision = collisions[i];
        log(`Resolving collision between ${l.i} at [${l.x},${l.y}] and ${collision.i} at [${collision.x},${collision.y}]`);
        // Short circuit so we can't infinite loop
        if (collision.moved) continue;
        // Don't move static items - we have to move *this* element away
        if (collision.static) {
            layout = moveElementAwayFromCollision(layout, collision, l, isUserAction, compactType, cols);
        } else {
            layout = moveElementAwayFromCollision(layout, l, collision, isUserAction, compactType, cols);
        }
    }
    return layout;
}
/**
 * This is where the magic needs to happen - given a collision, move an element away from the collision.
 * We attempt to move it up if there's room, otherwise it goes below.
 *
 * @param  {Array} layout            Full layout to modify.
 * @param  {LayoutItem} collidesWith Layout item we're colliding with.
 * @param  {LayoutItem} itemToMove   Layout item we're moving.
 */ function moveElementAwayFromCollision(layout /*: Layout*/ , collidesWith /*: LayoutItem*/ , itemToMove /*: LayoutItem*/ , isUserAction /*: ?boolean*/ , compactType /*: CompactType*/ , cols /*: number*/ ) /*: Layout*/ {
    const compactH = compactType === "horizontal";
    // Compact vertically if not set to horizontal
    const compactV = compactType === "vertical";
    const preventCollision = collidesWith.static; // we're already colliding (not for static items)
    // If there is enough space above the collision to put this element, move it there.
    // We only do this on the main collision as this can get funky in cascades and cause
    // unwanted swapping behavior.
    if (isUserAction) {
        // Reset isUserAction flag because we're not in the main collision anymore.
        isUserAction = false;
        // Make a mock item so we don't modify the item here, only modify in moveElement.
        const fakeItem /*: LayoutItem*/  = {
            x: compactH ? Math.max(collidesWith.x - itemToMove.w, 0) : itemToMove.x,
            y: compactV ? Math.max(collidesWith.y - itemToMove.h, 0) : itemToMove.y,
            w: itemToMove.w,
            h: itemToMove.h,
            i: "-1"
        };
        const firstCollision = getFirstCollision(layout, fakeItem);
        const collisionNorth = firstCollision && firstCollision.y + firstCollision.h > collidesWith.y;
        const collisionWest = firstCollision && collidesWith.x + collidesWith.w > firstCollision.x;
        // No collision? If so, we can go up there; otherwise, we'll end up moving down as normal
        if (!firstCollision) {
            log(`Doing reverse collision on ${itemToMove.i} up to [${fakeItem.x},${fakeItem.y}].`);
            return moveElement(layout, itemToMove, compactH ? fakeItem.x : undefined, compactV ? fakeItem.y : undefined, isUserAction, preventCollision, compactType, cols);
        } else if (collisionNorth && compactV) {
            return moveElement(layout, itemToMove, undefined, collidesWith.y + 1, isUserAction, preventCollision, compactType, cols);
        } else if (collisionNorth && compactType == null) {
            collidesWith.y = itemToMove.y;
            itemToMove.y = itemToMove.y + itemToMove.h;
            return layout;
        } else if (collisionWest && compactH) {
            return moveElement(layout, collidesWith, itemToMove.x, undefined, isUserAction, preventCollision, compactType, cols);
        }
    }
    const newX = compactH ? itemToMove.x + 1 : undefined;
    const newY = compactV ? itemToMove.y + 1 : undefined;
    if (newX == null && newY == null) {
        return layout;
    }
    return moveElement(layout, itemToMove, compactH ? itemToMove.x + 1 : undefined, compactV ? itemToMove.y + 1 : undefined, isUserAction, preventCollision, compactType, cols);
}
/**
 * Helper to convert a number to a percentage string.
 *
 * @param  {Number} num Any number
 * @return {String}     That number as a percentage.
 */ function perc(num /*: number*/ ) /*: string*/ {
    return num * 100 + "%";
}
/**
 * Helper functions to constrain dimensions of a GridItem
 */ const constrainWidth = (left /*: number*/ , currentWidth /*: number*/ , newWidth /*: number*/ , containerWidth /*: number*/ )=>{
    return left + newWidth > containerWidth ? currentWidth : newWidth;
};
const constrainHeight = (top /*: number*/ , currentHeight /*: number*/ , newHeight /*: number*/ )=>{
    return top < 0 ? currentHeight : newHeight;
};
const constrainLeft = (left /*: number*/ )=>Math.max(0, left);
const constrainTop = (top /*: number*/ )=>Math.max(0, top);
const resizeNorth = (currentSize, _ref, _containerWidth)=>{
    let { left, height, width } = _ref;
    const top = currentSize.top - (height - currentSize.height);
    return {
        left,
        width,
        height: constrainHeight(top, currentSize.height, height),
        top: constrainTop(top)
    };
};
const resizeEast = (currentSize, _ref2, containerWidth)=>{
    let { top, left, height, width } = _ref2;
    return {
        top,
        height,
        width: constrainWidth(currentSize.left, currentSize.width, width, containerWidth),
        left: constrainLeft(left)
    };
};
const resizeWest = (currentSize, _ref3, containerWidth)=>{
    let { top, height, width } = _ref3;
    const left = currentSize.left - (width - currentSize.width);
    return {
        height,
        width: left < 0 ? currentSize.width : constrainWidth(currentSize.left, currentSize.width, width, containerWidth),
        top: constrainTop(top),
        left: constrainLeft(left)
    };
};
const resizeSouth = (currentSize, _ref4, containerWidth)=>{
    let { top, left, height, width } = _ref4;
    return {
        width,
        left,
        height: constrainHeight(top, currentSize.height, height),
        top: constrainTop(top)
    };
};
const resizeNorthEast = function() {
    return resizeNorth(arguments.length <= 0 ? undefined : arguments[0], resizeEast(...arguments), arguments.length <= 2 ? undefined : arguments[2]);
};
const resizeNorthWest = function() {
    return resizeNorth(arguments.length <= 0 ? undefined : arguments[0], resizeWest(...arguments), arguments.length <= 2 ? undefined : arguments[2]);
};
const resizeSouthEast = function() {
    return resizeSouth(arguments.length <= 0 ? undefined : arguments[0], resizeEast(...arguments), arguments.length <= 2 ? undefined : arguments[2]);
};
const resizeSouthWest = function() {
    return resizeSouth(arguments.length <= 0 ? undefined : arguments[0], resizeWest(...arguments), arguments.length <= 2 ? undefined : arguments[2]);
};
const ordinalResizeHandlerMap = {
    n: resizeNorth,
    ne: resizeNorthEast,
    e: resizeEast,
    se: resizeSouthEast,
    s: resizeSouth,
    sw: resizeSouthWest,
    w: resizeWest,
    nw: resizeNorthWest
};
/**
 * Helper for clamping width and position when resizing an item.
 */ function resizeItemInDirection(direction /*: ResizeHandleAxis*/ , currentSize /*: Position*/ , newSize /*: Position*/ , containerWidth /*: number*/ ) /*: Position*/ {
    const ordinalHandler = ordinalResizeHandlerMap[direction];
    // Shouldn't be possible given types; that said, don't fail hard
    if (!ordinalHandler) return newSize;
    return ordinalHandler(currentSize, {
        ...currentSize,
        ...newSize
    }, containerWidth);
}
function setTransform(_ref5 /*:: */ ) /*: Object*/ {
    let { top, left, width, height } /*: Position*/  = _ref5 /*: Position*/ ;
    // Replace unitless items with px
    const translate = `translate(${left}px,${top}px)`;
    return {
        transform: translate,
        WebkitTransform: translate,
        MozTransform: translate,
        msTransform: translate,
        OTransform: translate,
        width: `${width}px`,
        height: `${height}px`,
        position: "absolute"
    };
}
function setTopLeft(_ref6 /*:: */ ) /*: Object*/ {
    let { top, left, width, height } /*: Position*/  = _ref6 /*: Position*/ ;
    return {
        top: `${top}px`,
        left: `${left}px`,
        width: `${width}px`,
        height: `${height}px`,
        position: "absolute"
    };
}
/**
 * Get layout items sorted from top left to right and down.
 *
 * @return {Array} Array of layout objects.
 * @return {Array}        Layout, sorted static items first.
 */ function sortLayoutItems(layout /*: Layout*/ , compactType /*: CompactType*/ ) /*: Layout*/ {
    if (compactType === "horizontal") return sortLayoutItemsByColRow(layout);
    if (compactType === "vertical") return sortLayoutItemsByRowCol(layout);
    else return layout;
}
/**
 * Sort layout items by row ascending and column ascending.
 *
 * Does not modify Layout.
 */ function sortLayoutItemsByRowCol(layout /*: Layout*/ ) /*: Layout*/ {
    // Slice to clone array as sort modifies
    return layout.slice(0).sort(function(a, b) {
        if (a.y > b.y || a.y === b.y && a.x > b.x) {
            return 1;
        } else if (a.y === b.y && a.x === b.x) {
            // Without this, we can get different sort results in IE vs. Chrome/FF
            return 0;
        }
        return -1;
    });
}
/**
 * Sort layout items by column ascending then row ascending.
 *
 * Does not modify Layout.
 */ function sortLayoutItemsByColRow(layout /*: Layout*/ ) /*: Layout*/ {
    return layout.slice(0).sort(function(a, b) {
        if (a.x > b.x || a.x === b.x && a.y > b.y) {
            return 1;
        }
        return -1;
    });
}
/**
 * Generate a layout using the initialLayout and children as a template.
 * Missing entries will be added, extraneous ones will be truncated.
 *
 * Does not modify initialLayout.
 *
 * @param  {Array}  initialLayout Layout passed in through props.
 * @param  {String} breakpoint    Current responsive breakpoint.
 * @param  {?String} compact      Compaction option.
 * @return {Array}                Working layout.
 */ function synchronizeLayoutWithChildren(initialLayout /*: Layout*/ , children /*: ReactChildren*/ , cols /*: number*/ , compactType /*: CompactType*/ , allowOverlap /*: ?boolean*/ ) /*: Layout*/ {
    initialLayout = initialLayout || [];
    // Generate one layout item per child.
    const layout /*: LayoutItem[]*/  = [];
    _react.default.Children.forEach(children, (child /*: ReactElement<any>*/ )=>{
        // Child may not exist
        if (child?.key == null) return;
        const exists = getLayoutItem(initialLayout, String(child.key));
        const g = child.props["data-grid"];
        // Don't overwrite the layout item if it's already in the initial layout.
        // If it has a `data-grid` property, prefer that over what's in the layout.
        if (exists && g == null) {
            layout.push(cloneLayoutItem(exists));
        } else {
            // Hey, this item has a data-grid property, use it.
            if (g) {
                if ("TURBOPACK compile-time truthy", 1) {
                    validateLayout([
                        g
                    ], "ReactGridLayout.children");
                }
                // FIXME clone not really necessary here
                layout.push(cloneLayoutItem({
                    ...g,
                    i: child.key
                }));
            } else {
                // Nothing provided: ensure this is added to the bottom
                // FIXME clone not really necessary here
                layout.push(cloneLayoutItem({
                    w: 1,
                    h: 1,
                    x: 0,
                    y: bottom(layout),
                    i: String(child.key)
                }));
            }
        }
    });
    // Correct the layout.
    const correctedLayout = correctBounds(layout, {
        cols: cols
    });
    return allowOverlap ? correctedLayout : compact(correctedLayout, compactType, cols);
}
/**
 * Validate a layout. Throws errors.
 *
 * @param  {Array}  layout        Array of layout items.
 * @param  {String} [contextName] Context name for errors.
 * @throw  {Error}                Validation error.
 */ function validateLayout(layout /*: Layout*/ ) /*: void*/ {
    let contextName /*: string*/  = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : "Layout";
    const subProps = [
        "x",
        "y",
        "w",
        "h"
    ];
    if (!Array.isArray(layout)) throw new Error(contextName + " must be an array!");
    for(let i = 0, len = layout.length; i < len; i++){
        const item = layout[i];
        for(let j = 0; j < subProps.length; j++){
            const key = subProps[j];
            const value = item[key];
            if (typeof value !== "number" || Number.isNaN(value)) {
                throw new Error(`ReactGridLayout: ${contextName}[${i}].${key} must be a number! Received: ${value} (${typeof value})`);
            }
        }
        if (typeof item.i !== "undefined" && typeof item.i !== "string") {
            throw new Error(`ReactGridLayout: ${contextName}[${i}].i must be a string! Received: ${item.i} (${typeof item.i})`);
        }
    }
}
// Legacy support for verticalCompact: false
function compactType(props /*: ?{ verticalCompact: boolean, compactType: CompactType }*/ ) /*: CompactType*/ {
    const { verticalCompact, compactType } = props || {};
    return verticalCompact === false ? null : compactType;
}
function log() {
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
}
const noop = ()=>{};
exports.noop = noop;
}}),
"[project]/node_modules/react-grid-layout/build/calculateUtils.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.calcGridColWidth = calcGridColWidth;
exports.calcGridItemPosition = calcGridItemPosition;
exports.calcGridItemWHPx = calcGridItemWHPx;
exports.calcWH = calcWH;
exports.calcXY = calcXY;
exports.clamp = clamp;
/*:: import type { Position } from "./utils";*/ /*:: export type PositionParams = {
  margin: [number, number],
  containerPadding: [number, number],
  containerWidth: number,
  cols: number,
  rowHeight: number,
  maxRows: number
};*/ // Helper for generating column width
function calcGridColWidth(positionParams /*: PositionParams*/ ) /*: number*/ {
    const { margin, containerPadding, containerWidth, cols } = positionParams;
    return (containerWidth - margin[0] * (cols - 1) - containerPadding[0] * 2) / cols;
}
// This can either be called:
// calcGridItemWHPx(w, colWidth, margin[0])
// or
// calcGridItemWHPx(h, rowHeight, margin[1])
function calcGridItemWHPx(gridUnits /*: number*/ , colOrRowSize /*: number*/ , marginPx /*: number*/ ) /*: number*/ {
    // 0 * Infinity === NaN, which causes problems with resize contraints
    if (!Number.isFinite(gridUnits)) return gridUnits;
    return Math.round(colOrRowSize * gridUnits + Math.max(0, gridUnits - 1) * marginPx);
}
/**
 * Return position on the page given an x, y, w, h.
 * left, top, width, height are all in pixels.
 * @param  {PositionParams} positionParams  Parameters of grid needed for coordinates calculations.
 * @param  {Number}  x                      X coordinate in grid units.
 * @param  {Number}  y                      Y coordinate in grid units.
 * @param  {Number}  w                      W coordinate in grid units.
 * @param  {Number}  h                      H coordinate in grid units.
 * @return {Position}                       Object containing coords.
 */ function calcGridItemPosition(positionParams /*: PositionParams*/ , x /*: number*/ , y /*: number*/ , w /*: number*/ , h /*: number*/ , state /*: ?Object*/ ) /*: Position*/ {
    const { margin, containerPadding, rowHeight } = positionParams;
    const colWidth = calcGridColWidth(positionParams);
    const out = {};
    // If resizing, use the exact width and height as returned from resizing callbacks.
    if (state && state.resizing) {
        out.width = Math.round(state.resizing.width);
        out.height = Math.round(state.resizing.height);
    } else {
        out.width = calcGridItemWHPx(w, colWidth, margin[0]);
        out.height = calcGridItemWHPx(h, rowHeight, margin[1]);
    }
    // If dragging, use the exact width and height as returned from dragging callbacks.
    if (state && state.dragging) {
        out.top = Math.round(state.dragging.top);
        out.left = Math.round(state.dragging.left);
    } else if (state && state.resizing && typeof state.resizing.top === "number" && typeof state.resizing.left === "number") {
        out.top = Math.round(state.resizing.top);
        out.left = Math.round(state.resizing.left);
    } else {
        out.top = Math.round((rowHeight + margin[1]) * y + containerPadding[1]);
        out.left = Math.round((colWidth + margin[0]) * x + containerPadding[0]);
    }
    return out;
}
/**
 * Translate x and y coordinates from pixels to grid units.
 * @param  {PositionParams} positionParams  Parameters of grid needed for coordinates calculations.
 * @param  {Number} top                     Top position (relative to parent) in pixels.
 * @param  {Number} left                    Left position (relative to parent) in pixels.
 * @param  {Number} w                       W coordinate in grid units.
 * @param  {Number} h                       H coordinate in grid units.
 * @return {Object}                         x and y in grid units.
 */ function calcXY(positionParams /*: PositionParams*/ , top /*: number*/ , left /*: number*/ , w /*: number*/ , h /*: number*/ ) /*: { x: number, y: number }*/ {
    const { margin, containerPadding, cols, rowHeight, maxRows } = positionParams;
    const colWidth = calcGridColWidth(positionParams);
    // left = containerPaddingX + x * (colWidth + marginX)
    // x * (colWidth + marginX) = left - containerPaddingX
    // x = (left - containerPaddingX) / (colWidth + marginX)
    let x = Math.round((left - containerPadding[0]) / (colWidth + margin[0]));
    let y = Math.round((top - containerPadding[1]) / (rowHeight + margin[1]));
    // Capping
    x = clamp(x, 0, cols - w);
    y = clamp(y, 0, maxRows - h);
    return {
        x,
        y
    };
}
/**
 * Given a height and width in pixel values, calculate grid units.
 * @param  {PositionParams} positionParams  Parameters of grid needed for coordinates calcluations.
 * @param  {Number} height                  Height in pixels.
 * @param  {Number} width                   Width in pixels.
 * @param  {Number} x                       X coordinate in grid units.
 * @param  {Number} y                       Y coordinate in grid units.
 * @param {String} handle Resize Handle.
 * @return {Object}                         w, h as grid units.
 */ function calcWH(positionParams /*: PositionParams*/ , width /*: number*/ , height /*: number*/ , x /*: number*/ , y /*: number*/ , handle /*: string*/ ) /*: { w: number, h: number }*/ {
    const { margin, maxRows, cols, rowHeight } = positionParams;
    const colWidth = calcGridColWidth(positionParams);
    // width = colWidth * w - (margin * (w - 1))
    // ...
    // w = (width + margin) / (colWidth + margin)
    let w = Math.round((width + margin[0]) / (colWidth + margin[0]));
    let h = Math.round((height + margin[1]) / (rowHeight + margin[1]));
    // Capping
    let _w = clamp(w, 0, cols - x);
    let _h = clamp(h, 0, maxRows - y);
    if ([
        "sw",
        "w",
        "nw"
    ].indexOf(handle) !== -1) {
        _w = clamp(w, 0, cols);
    }
    if ([
        "nw",
        "n",
        "ne"
    ].indexOf(handle) !== -1) {
        _h = clamp(h, 0, maxRows);
    }
    return {
        w: _w,
        h: _h
    };
}
// Similar to _.clamp
function clamp(num /*: number*/ , lowerBound /*: number*/ , upperBound /*: number*/ ) /*: number*/ {
    return Math.max(Math.min(num, upperBound), lowerBound);
}
}}),
"[project]/node_modules/react-grid-layout/build/ReactGridLayoutPropTypes.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.resizeHandleType = exports.resizeHandleAxesType = exports.default = void 0;
var _propTypes = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/prop-types/index.js [app-ssr] (ecmascript)"));
var _react = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
/*:: import type {
  Ref,
  ChildrenArray as ReactChildrenArray,
  Element as ReactElement
} from "react";*/ /*:: import type {
  DragOverEvent,
  EventCallback,
  CompactType,
  Layout,
  LayoutItem,
  ResizeHandleAxis
} from "./utils";*/ /*:: export type ReactRef<T: HTMLElement> = {|
  +current: T | null
|};*/ // util
/*:: export type ResizeHandle =
  | ReactElement<any>
  | ((
      resizeHandleAxis: ResizeHandleAxis,
      ref: ReactRef<HTMLElement>
    ) => ReactElement<any>);*/ // Defines which resize handles should be rendered (default: 'se')
// Allows for any combination of:
// 's' - South handle (bottom-center)
// 'w' - West handle (left-center)
// 'e' - East handle (right-center)
// 'n' - North handle (top-center)
// 'sw' - Southwest handle (bottom-left)
// 'nw' - Northwest handle (top-left)
// 'se' - Southeast handle (bottom-right)
// 'ne' - Northeast handle (top-right)
const resizeHandleAxesType /*: ReactPropsChainableTypeChecker*/  = exports.resizeHandleAxesType = _propTypes.default.arrayOf(_propTypes.default.oneOf([
    "s",
    "w",
    "e",
    "n",
    "sw",
    "nw",
    "se",
    "ne"
]));
// Custom component for resize handles
const resizeHandleType /*: ReactPropsChainableTypeChecker*/  = exports.resizeHandleType = _propTypes.default.oneOfType([
    _propTypes.default.node,
    _propTypes.default.func
]);
/*:: export type Props = {|
  className: string,
  style: Object,
  width: number,
  autoSize: boolean,
  cols: number,
  draggableCancel: string,
  draggableHandle: string,
  verticalCompact: boolean,
  compactType: CompactType,
  layout: Layout,
  margin: [number, number],
  containerPadding: ?[number, number],
  rowHeight: number,
  maxRows: number,
  isBounded: boolean,
  isDraggable: boolean,
  isResizable: boolean,
  isDroppable: boolean,
  preventCollision: boolean,
  useCSSTransforms: boolean,
  transformScale: number,
  droppingItem: $Shape<LayoutItem>,
  resizeHandles: ResizeHandleAxis[],
  resizeHandle?: ResizeHandle,
  allowOverlap: boolean,

  // Callbacks
  onLayoutChange: Layout => void,
  onDrag: EventCallback,
  onDragStart: EventCallback,
  onDragStop: EventCallback,
  onResize: EventCallback,
  onResizeStart: EventCallback,
  onResizeStop: EventCallback,
  onDropDragOver: (e: DragOverEvent) => ?({| w?: number, h?: number |} | false),
  onDrop: (layout: Layout, item: ?LayoutItem, e: Event) => void,
  children: ReactChildrenArray<ReactElement<any>>,
  innerRef?: Ref<"div">
|};*/ /*:: export type DefaultProps = $Diff<
  Props,
  {
    children: ReactChildrenArray<ReactElement<any>>,
    width: number
  }
>;*/ var _default = exports.default = {
    //
    // Basic props
    //
    className: _propTypes.default.string,
    style: _propTypes.default.object,
    // This can be set explicitly. If it is not set, it will automatically
    // be set to the container width. Note that resizes will *not* cause this to adjust.
    // If you need that behavior, use WidthProvider.
    width: _propTypes.default.number,
    // If true, the container height swells and contracts to fit contents
    autoSize: _propTypes.default.bool,
    // # of cols.
    cols: _propTypes.default.number,
    // A selector that will not be draggable.
    draggableCancel: _propTypes.default.string,
    // A selector for the draggable handler
    draggableHandle: _propTypes.default.string,
    // Deprecated
    verticalCompact: function(props /*: Props*/ ) {
        if (props.verticalCompact === false && ("TURBOPACK compile-time value", "development") !== "production") {
            console.warn(// eslint-disable-line no-console
            "`verticalCompact` on <ReactGridLayout> is deprecated and will be removed soon. " + 'Use `compactType`: "horizontal" | "vertical" | null.');
        }
    },
    // Choose vertical or hotizontal compaction
    compactType: _propTypes.default.oneOf([
        "vertical",
        "horizontal"
    ]),
    // layout is an array of object with the format:
    // {x: Number, y: Number, w: Number, h: Number, i: String}
    layout: function(props /*: Props*/ ) {
        var layout = props.layout;
        // I hope you're setting the data-grid property on the grid items
        if (layout === undefined) return;
        __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/utils.js [app-ssr] (ecmascript)").validateLayout(layout, "layout");
    },
    //
    // Grid Dimensions
    //
    // Margin between items [x, y] in px
    margin: _propTypes.default.arrayOf(_propTypes.default.number),
    // Padding inside the container [x, y] in px
    containerPadding: _propTypes.default.arrayOf(_propTypes.default.number),
    // Rows have a static height, but you can change this based on breakpoints if you like
    rowHeight: _propTypes.default.number,
    // Default Infinity, but you can specify a max here if you like.
    // Note that this isn't fully fleshed out and won't error if you specify a layout that
    // extends beyond the row capacity. It will, however, not allow users to drag/resize
    // an item past the barrier. They can push items beyond the barrier, though.
    // Intentionally not documented for this reason.
    maxRows: _propTypes.default.number,
    //
    // Flags
    //
    isBounded: _propTypes.default.bool,
    isDraggable: _propTypes.default.bool,
    isResizable: _propTypes.default.bool,
    // If true, grid can be placed one over the other.
    allowOverlap: _propTypes.default.bool,
    // If true, grid items won't change position when being dragged over.
    preventCollision: _propTypes.default.bool,
    // Use CSS transforms instead of top/left
    useCSSTransforms: _propTypes.default.bool,
    // parent layout transform scale
    transformScale: _propTypes.default.number,
    // If true, an external element can trigger onDrop callback with a specific grid position as a parameter
    isDroppable: _propTypes.default.bool,
    // Resize handle options
    resizeHandles: resizeHandleAxesType,
    resizeHandle: resizeHandleType,
    //
    // Callbacks
    //
    // Callback so you can save the layout. Calls after each drag & resize stops.
    onLayoutChange: _propTypes.default.func,
    // Calls when drag starts. Callback is of the signature (layout, oldItem, newItem, placeholder, e, ?node).
    // All callbacks below have the same signature. 'start' and 'stop' callbacks omit the 'placeholder'.
    onDragStart: _propTypes.default.func,
    // Calls on each drag movement.
    onDrag: _propTypes.default.func,
    // Calls when drag is complete.
    onDragStop: _propTypes.default.func,
    //Calls when resize starts.
    onResizeStart: _propTypes.default.func,
    // Calls when resize movement happens.
    onResize: _propTypes.default.func,
    // Calls when resize is complete.
    onResizeStop: _propTypes.default.func,
    // Calls when some element is dropped.
    onDrop: _propTypes.default.func,
    //
    // Other validations
    //
    droppingItem: _propTypes.default.shape({
        i: _propTypes.default.string.isRequired,
        w: _propTypes.default.number.isRequired,
        h: _propTypes.default.number.isRequired
    }),
    // Children must not have duplicate keys.
    children: function(props /*: Props*/ , propName /*: string*/ ) {
        const children = props[propName];
        // Check children keys for duplicates. Throw if found.
        const keys = {};
        _react.default.Children.forEach(children, function(child) {
            if (child?.key == null) return;
            if (keys[child.key]) {
                throw new Error('Duplicate child key "' + child.key + '" found! This will cause problems in ReactGridLayout.');
            }
            keys[child.key] = true;
        });
    },
    // Optional ref for getting a reference for the wrapping div.
    innerRef: _propTypes.default.any
};
}}),
"[project]/node_modules/react-grid-layout/build/GridItem.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = void 0;
var _react = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var _reactDom = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)");
var _propTypes = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/prop-types/index.js [app-ssr] (ecmascript)"));
var _reactDraggable = __turbopack_context__.r("[project]/node_modules/react-draggable/build/cjs/cjs.js [app-ssr] (ecmascript)");
var _reactResizable = __turbopack_context__.r("[project]/node_modules/react-resizable/index.js [app-ssr] (ecmascript)");
var _utils = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/utils.js [app-ssr] (ecmascript)");
var _calculateUtils = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/calculateUtils.js [app-ssr] (ecmascript)");
var _ReactGridLayoutPropTypes = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/ReactGridLayoutPropTypes.js [app-ssr] (ecmascript)");
var _clsx = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/clsx/dist/clsx.js [app-ssr] (ecmascript)"));
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _defineProperty(obj, key, value) {
    key = _toPropertyKey(key);
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _toPropertyKey(arg) {
    var key = _toPrimitive(arg, "string");
    return typeof key === "symbol" ? key : String(key);
}
function _toPrimitive(input, hint) {
    if (typeof input !== "object" || input === null) return input;
    var prim = input[Symbol.toPrimitive];
    if (prim !== undefined) {
        var res = prim.call(input, hint || "default");
        if (typeof res !== "object") return res;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (hint === "string" ? String : Number)(input);
}
/*:: import type { Element as ReactElement, Node as ReactNode } from "react";*/ /*:: import type {
  ReactDraggableCallbackData,
  GridDragEvent,
  GridResizeEvent,
  DroppingPosition,
  Position,
  ResizeHandleAxis
} from "./utils";*/ /*:: import type { PositionParams } from "./calculateUtils";*/ /*:: import type { ResizeHandle, ReactRef } from "./ReactGridLayoutPropTypes";*/ /*:: type PartialPosition = { top: number, left: number };*/ /*:: type GridItemCallback<Data: GridDragEvent | GridResizeEvent> = (
  i: string,
  w: number,
  h: number,
  Data
) => void;*/ /*:: type ResizeCallbackData = {
  node: HTMLElement,
  size: Position,
  handle: ResizeHandleAxis
};*/ /*:: type GridItemResizeCallback = (
  e: Event,
  data: ResizeCallbackData,
  position: Position
) => void;*/ /*:: type State = {
  resizing: ?{ top: number, left: number, width: number, height: number },
  dragging: ?{ top: number, left: number },
  className: string
};*/ /*:: type Props = {
  children: ReactElement<any>,
  cols: number,
  containerWidth: number,
  margin: [number, number],
  containerPadding: [number, number],
  rowHeight: number,
  maxRows: number,
  isDraggable: boolean,
  isResizable: boolean,
  isBounded: boolean,
  static?: boolean,
  useCSSTransforms?: boolean,
  usePercentages?: boolean,
  transformScale: number,
  droppingPosition?: DroppingPosition,

  className: string,
  style?: Object,
  // Draggability
  cancel: string,
  handle: string,

  x: number,
  y: number,
  w: number,
  h: number,

  minW: number,
  maxW: number,
  minH: number,
  maxH: number,
  i: string,

  resizeHandles?: ResizeHandleAxis[],
  resizeHandle?: ResizeHandle,

  onDrag?: GridItemCallback<GridDragEvent>,
  onDragStart?: GridItemCallback<GridDragEvent>,
  onDragStop?: GridItemCallback<GridDragEvent>,
  onResize?: GridItemCallback<GridResizeEvent>,
  onResizeStart?: GridItemCallback<GridResizeEvent>,
  onResizeStop?: GridItemCallback<GridResizeEvent>
};*/ /*:: type DefaultProps = {
  className: string,
  cancel: string,
  handle: string,
  minH: number,
  minW: number,
  maxH: number,
  maxW: number,
  transformScale: number
};*/ /**
 * An individual item within a ReactGridLayout.
 */ class GridItem extends _react.default.Component /*:: <Props, State>*/  {
    constructor(){
        super(...arguments);
        _defineProperty(this, "state", {
            resizing: null,
            dragging: null,
            className: ""
        });
        _defineProperty(this, "elementRef", /*#__PURE__*/ _react.default.createRef());
        /**
     * onDragStart event handler
     * @param  {Event}  e             event data
     * @param  {Object} callbackData  an object with node, delta and position information
     */ _defineProperty(this, "onDragStart", (e, _ref)=>{
            let { node } = _ref;
            const { onDragStart, transformScale } = this.props;
            if (!onDragStart) return;
            const newPosition /*: PartialPosition*/  = {
                top: 0,
                left: 0
            };
            // TODO: this wont work on nested parents
            const { offsetParent } = node;
            if (!offsetParent) return;
            const parentRect = offsetParent.getBoundingClientRect();
            const clientRect = node.getBoundingClientRect();
            const cLeft = clientRect.left / transformScale;
            const pLeft = parentRect.left / transformScale;
            const cTop = clientRect.top / transformScale;
            const pTop = parentRect.top / transformScale;
            newPosition.left = cLeft - pLeft + offsetParent.scrollLeft;
            newPosition.top = cTop - pTop + offsetParent.scrollTop;
            this.setState({
                dragging: newPosition
            });
            // Call callback with this data
            const { x, y } = (0, _calculateUtils.calcXY)(this.getPositionParams(), newPosition.top, newPosition.left, this.props.w, this.props.h);
            return onDragStart.call(this, this.props.i, x, y, {
                e,
                node,
                newPosition
            });
        });
        /**
     * onDrag event handler
     * @param  {Event}  e             event data
     * @param  {Object} callbackData  an object with node, delta and position information
     * @param  {boolean} dontFlush    if true, will not call flushSync
     */ _defineProperty(this, "onDrag", (e, _ref2, dontFlush)=>{
            let { node, deltaX, deltaY } = _ref2;
            const { onDrag } = this.props;
            if (!onDrag) return;
            if (!this.state.dragging) {
                throw new Error("onDrag called before onDragStart.");
            }
            let top = this.state.dragging.top + deltaY;
            let left = this.state.dragging.left + deltaX;
            const { isBounded, i, w, h, containerWidth } = this.props;
            const positionParams = this.getPositionParams();
            // Boundary calculations; keeps items within the grid
            if (isBounded) {
                const { offsetParent } = node;
                if (offsetParent) {
                    const { margin, rowHeight, containerPadding } = this.props;
                    const bottomBoundary = offsetParent.clientHeight - (0, _calculateUtils.calcGridItemWHPx)(h, rowHeight, margin[1]);
                    top = (0, _calculateUtils.clamp)(top - containerPadding[1], 0, bottomBoundary);
                    const colWidth = (0, _calculateUtils.calcGridColWidth)(positionParams);
                    const rightBoundary = containerWidth - (0, _calculateUtils.calcGridItemWHPx)(w, colWidth, margin[0]);
                    left = (0, _calculateUtils.clamp)(left - containerPadding[0], 0, rightBoundary);
                }
            }
            const newPosition /*: PartialPosition*/  = {
                top,
                left
            };
            // dontFlush is set if we're calling from inside
            if (dontFlush) {
                this.setState({
                    dragging: newPosition
                });
            } else {
                (0, _reactDom.flushSync)(()=>{
                    this.setState({
                        dragging: newPosition
                    });
                });
            }
            // Call callback with this data
            const { x, y } = (0, _calculateUtils.calcXY)(positionParams, top, left, w, h);
            return onDrag.call(this, i, x, y, {
                e,
                node,
                newPosition
            });
        });
        /**
     * onDragStop event handler
     * @param  {Event}  e             event data
     * @param  {Object} callbackData  an object with node, delta and position information
     */ _defineProperty(this, "onDragStop", (e, _ref3)=>{
            let { node } = _ref3;
            const { onDragStop } = this.props;
            if (!onDragStop) return;
            if (!this.state.dragging) {
                throw new Error("onDragEnd called before onDragStart.");
            }
            const { w, h, i } = this.props;
            const { left, top } = this.state.dragging;
            const newPosition /*: PartialPosition*/  = {
                top,
                left
            };
            this.setState({
                dragging: null
            });
            const { x, y } = (0, _calculateUtils.calcXY)(this.getPositionParams(), top, left, w, h);
            return onDragStop.call(this, i, x, y, {
                e,
                node,
                newPosition
            });
        });
        /**
     * onResizeStop event handler
     * @param  {Event}  e             event data
     * @param  {Object} callbackData  an object with node and size information
     */ _defineProperty(this, "onResizeStop", (e, callbackData, position)=>this.onResizeHandler(e, callbackData, position, "onResizeStop"));
        // onResizeStart event handler
        _defineProperty(this, "onResizeStart", (e, callbackData, position)=>this.onResizeHandler(e, callbackData, position, "onResizeStart"));
        // onResize event handler
        _defineProperty(this, "onResize", (e, callbackData, position)=>this.onResizeHandler(e, callbackData, position, "onResize"));
    }
    shouldComponentUpdate(nextProps /*: Props*/ , nextState /*: State*/ ) /*: boolean*/ {
        // We can't deeply compare children. If the developer memoizes them, we can
        // use this optimization.
        if (this.props.children !== nextProps.children) return true;
        if (this.props.droppingPosition !== nextProps.droppingPosition) return true;
        // TODO memoize these calculations so they don't take so long?
        const oldPosition = (0, _calculateUtils.calcGridItemPosition)(this.getPositionParams(this.props), this.props.x, this.props.y, this.props.w, this.props.h, this.state);
        const newPosition = (0, _calculateUtils.calcGridItemPosition)(this.getPositionParams(nextProps), nextProps.x, nextProps.y, nextProps.w, nextProps.h, nextState);
        return !(0, _utils.fastPositionEqual)(oldPosition, newPosition) || this.props.useCSSTransforms !== nextProps.useCSSTransforms;
    }
    componentDidMount() {
        this.moveDroppingItem({});
    }
    componentDidUpdate(prevProps /*: Props*/ ) {
        this.moveDroppingItem(prevProps);
    }
    // When a droppingPosition is present, this means we should fire a move event, as if we had moved
    // this element by `x, y` pixels.
    moveDroppingItem(prevProps /*: Props*/ ) {
        const { droppingPosition } = this.props;
        if (!droppingPosition) return;
        const node = this.elementRef.current;
        // Can't find DOM node (are we unmounted?)
        if (!node) return;
        const prevDroppingPosition = prevProps.droppingPosition || {
            left: 0,
            top: 0
        };
        const { dragging } = this.state;
        const shouldDrag = dragging && droppingPosition.left !== prevDroppingPosition.left || droppingPosition.top !== prevDroppingPosition.top;
        if (!dragging) {
            this.onDragStart(droppingPosition.e, {
                node,
                deltaX: droppingPosition.left,
                deltaY: droppingPosition.top
            });
        } else if (shouldDrag) {
            const deltaX = droppingPosition.left - dragging.left;
            const deltaY = droppingPosition.top - dragging.top;
            this.onDrag(droppingPosition.e, {
                node,
                deltaX,
                deltaY
            }, true // dontFLush: avoid flushSync to temper warnings
            );
        }
    }
    getPositionParams() /*: PositionParams*/ {
        let props /*: Props*/  = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.props;
        return {
            cols: props.cols,
            containerPadding: props.containerPadding,
            containerWidth: props.containerWidth,
            margin: props.margin,
            maxRows: props.maxRows,
            rowHeight: props.rowHeight
        };
    }
    /**
   * This is where we set the grid item's absolute placement. It gets a little tricky because we want to do it
   * well when server rendering, and the only way to do that properly is to use percentage width/left because
   * we don't know exactly what the browser viewport is.
   * Unfortunately, CSS Transforms, which are great for performance, break in this instance because a percentage
   * left is relative to the item itself, not its container! So we cannot use them on the server rendering pass.
   *
   * @param  {Object} pos Position object with width, height, left, top.
   * @return {Object}     Style object.
   */ createStyle(pos /*: Position*/ ) /*: { [key: string]: ?string }*/ {
        const { usePercentages, containerWidth, useCSSTransforms } = this.props;
        let style;
        // CSS Transforms support (default)
        if (useCSSTransforms) {
            style = (0, _utils.setTransform)(pos);
        } else {
            // top,left (slow)
            style = (0, _utils.setTopLeft)(pos);
            // This is used for server rendering.
            if (usePercentages) {
                style.left = (0, _utils.perc)(pos.left / containerWidth);
                style.width = (0, _utils.perc)(pos.width / containerWidth);
            }
        }
        return style;
    }
    /**
   * Mix a Draggable instance into a child.
   * @param  {Element} child    Child element.
   * @return {Element}          Child wrapped in Draggable.
   */ mixinDraggable(child /*: ReactElement<any>*/ , isDraggable /*: boolean*/ ) /*: ReactElement<any>*/ {
        return /*#__PURE__*/ _react.default.createElement(_reactDraggable.DraggableCore, {
            disabled: !isDraggable,
            onStart: this.onDragStart,
            onDrag: this.onDrag,
            onStop: this.onDragStop,
            handle: this.props.handle,
            cancel: ".react-resizable-handle" + (this.props.cancel ? "," + this.props.cancel : ""),
            scale: this.props.transformScale,
            nodeRef: this.elementRef
        }, child);
    }
    /**
   * Utility function to setup callback handler definitions for
   * similarily structured resize events.
   */ curryResizeHandler(position /*: Position*/ , handler /*: Function*/ ) /*: Function*/ {
        return (e /*: Event*/ , data /*: ResizeCallbackData*/ )=>/*: Function*/ handler(e, data, position);
    }
    /**
   * Mix a Resizable instance into a child.
   * @param  {Element} child    Child element.
   * @param  {Object} position  Position object (pixel values)
   * @return {Element}          Child wrapped in Resizable.
   */ mixinResizable(child /*: ReactElement<any>*/ , position /*: Position*/ , isResizable /*: boolean*/ ) /*: ReactElement<any>*/ {
        const { cols, minW, minH, maxW, maxH, transformScale, resizeHandles, resizeHandle } = this.props;
        const positionParams = this.getPositionParams();
        // This is the max possible width - doesn't go to infinity because of the width of the window
        const maxWidth = (0, _calculateUtils.calcGridItemPosition)(positionParams, 0, 0, cols, 0).width;
        // Calculate min/max constraints using our min & maxes
        const mins = (0, _calculateUtils.calcGridItemPosition)(positionParams, 0, 0, minW, minH);
        const maxes = (0, _calculateUtils.calcGridItemPosition)(positionParams, 0, 0, maxW, maxH);
        const minConstraints = [
            mins.width,
            mins.height
        ];
        const maxConstraints = [
            Math.min(maxes.width, maxWidth),
            Math.min(maxes.height, Infinity)
        ];
        return /*#__PURE__*/ _react.default.createElement(_reactResizable.Resizable, {
            draggableOpts: {
                disabled: !isResizable
            },
            className: isResizable ? undefined : "react-resizable-hide",
            width: position.width,
            height: position.height,
            minConstraints: minConstraints,
            maxConstraints: maxConstraints,
            onResizeStop: this.curryResizeHandler(position, this.onResizeStop),
            onResizeStart: this.curryResizeHandler(position, this.onResizeStart),
            onResize: this.curryResizeHandler(position, this.onResize),
            transformScale: transformScale,
            resizeHandles: resizeHandles,
            handle: resizeHandle
        }, child);
    }
    /**
   * Wrapper around resize events to provide more useful data.
   */ onResizeHandler(e /*: Event*/ , _ref4 /*:: */ , // 'size' is updated position
    position /*: Position*/ , // existing position
    handlerName /*: string*/ ) /*: void*/ {
        let { node, size, handle } /*: ResizeCallbackData*/  = _ref4 /*: ResizeCallbackData*/ ;
        const handler = this.props[handlerName];
        if (!handler) return;
        const { x, y, i, maxH, minH, containerWidth } = this.props;
        const { minW, maxW } = this.props;
        // Clamping of dimensions based on resize direction
        let updatedSize = size;
        if (node) {
            updatedSize = (0, _utils.resizeItemInDirection)(handle, position, size, containerWidth);
            (0, _reactDom.flushSync)(()=>{
                this.setState({
                    resizing: handlerName === "onResizeStop" ? null : updatedSize
                });
            });
        }
        // Get new XY based on pixel size
        let { w, h } = (0, _calculateUtils.calcWH)(this.getPositionParams(), updatedSize.width, updatedSize.height, x, y, handle);
        // Min/max capping.
        // minW should be at least 1 (TODO propTypes validation?)
        w = (0, _calculateUtils.clamp)(w, Math.max(minW, 1), maxW);
        h = (0, _calculateUtils.clamp)(h, minH, maxH);
        handler.call(this, i, w, h, {
            e,
            node,
            size: updatedSize,
            handle
        });
    }
    render() /*: ReactNode*/ {
        const { x, y, w, h, isDraggable, isResizable, droppingPosition, useCSSTransforms } = this.props;
        const pos = (0, _calculateUtils.calcGridItemPosition)(this.getPositionParams(), x, y, w, h, this.state);
        const child = _react.default.Children.only(this.props.children);
        // Create the child element. We clone the existing element but modify its className and style.
        let newChild = /*#__PURE__*/ _react.default.cloneElement(child, {
            ref: this.elementRef,
            className: (0, _clsx.default)("react-grid-item", child.props.className, this.props.className, {
                static: this.props.static,
                resizing: Boolean(this.state.resizing),
                "react-draggable": isDraggable,
                "react-draggable-dragging": Boolean(this.state.dragging),
                dropping: Boolean(droppingPosition),
                cssTransforms: useCSSTransforms
            }),
            // We can set the width and height on the child, but unfortunately we can't set the position.
            style: {
                ...this.props.style,
                ...child.props.style,
                ...this.createStyle(pos)
            }
        });
        // Resizable support. This is usually on but the user can toggle it off.
        newChild = this.mixinResizable(newChild, pos, isResizable);
        // Draggable support. This is always on, except for with placeholders.
        newChild = this.mixinDraggable(newChild, isDraggable);
        return newChild;
    }
}
exports.default = GridItem;
_defineProperty(GridItem, "propTypes", {
    // Children must be only a single element
    children: _propTypes.default.element,
    // General grid attributes
    cols: _propTypes.default.number.isRequired,
    containerWidth: _propTypes.default.number.isRequired,
    rowHeight: _propTypes.default.number.isRequired,
    margin: _propTypes.default.array.isRequired,
    maxRows: _propTypes.default.number.isRequired,
    containerPadding: _propTypes.default.array.isRequired,
    // These are all in grid units
    x: _propTypes.default.number.isRequired,
    y: _propTypes.default.number.isRequired,
    w: _propTypes.default.number.isRequired,
    h: _propTypes.default.number.isRequired,
    // All optional
    minW: function(props /*: Props*/ , propName /*: string*/ ) {
        const value = props[propName];
        if (typeof value !== "number") return new Error("minWidth not Number");
        if (value > props.w || value > props.maxW) return new Error("minWidth larger than item width/maxWidth");
    },
    maxW: function(props /*: Props*/ , propName /*: string*/ ) {
        const value = props[propName];
        if (typeof value !== "number") return new Error("maxWidth not Number");
        if (value < props.w || value < props.minW) return new Error("maxWidth smaller than item width/minWidth");
    },
    minH: function(props /*: Props*/ , propName /*: string*/ ) {
        const value = props[propName];
        if (typeof value !== "number") return new Error("minHeight not Number");
        if (value > props.h || value > props.maxH) return new Error("minHeight larger than item height/maxHeight");
    },
    maxH: function(props /*: Props*/ , propName /*: string*/ ) {
        const value = props[propName];
        if (typeof value !== "number") return new Error("maxHeight not Number");
        if (value < props.h || value < props.minH) return new Error("maxHeight smaller than item height/minHeight");
    },
    // ID is nice to have for callbacks
    i: _propTypes.default.string.isRequired,
    // Resize handle options
    resizeHandles: _ReactGridLayoutPropTypes.resizeHandleAxesType,
    resizeHandle: _ReactGridLayoutPropTypes.resizeHandleType,
    // Functions
    onDragStop: _propTypes.default.func,
    onDragStart: _propTypes.default.func,
    onDrag: _propTypes.default.func,
    onResizeStop: _propTypes.default.func,
    onResizeStart: _propTypes.default.func,
    onResize: _propTypes.default.func,
    // Flags
    isDraggable: _propTypes.default.bool.isRequired,
    isResizable: _propTypes.default.bool.isRequired,
    isBounded: _propTypes.default.bool.isRequired,
    static: _propTypes.default.bool,
    // Use CSS transforms instead of top/left
    useCSSTransforms: _propTypes.default.bool.isRequired,
    transformScale: _propTypes.default.number,
    // Others
    className: _propTypes.default.string,
    // Selector for draggable handle
    handle: _propTypes.default.string,
    // Selector for draggable cancel (see react-draggable)
    cancel: _propTypes.default.string,
    // Current position of a dropping element
    droppingPosition: _propTypes.default.shape({
        e: _propTypes.default.object.isRequired,
        left: _propTypes.default.number.isRequired,
        top: _propTypes.default.number.isRequired
    })
});
_defineProperty(GridItem, "defaultProps", {
    className: "",
    cancel: "",
    handle: "",
    minH: 1,
    minW: 1,
    maxH: Infinity,
    maxW: Infinity,
    transformScale: 1
});
}}),
"[project]/node_modules/react-grid-layout/build/ReactGridLayout.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var _fastEquals = __turbopack_context__.r("[project]/node_modules/fast-equals/dist/fast-equals.esm.js [app-ssr] (ecmascript)");
var _clsx = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/clsx/dist/clsx.js [app-ssr] (ecmascript)"));
var _utils = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/utils.js [app-ssr] (ecmascript)");
var _calculateUtils = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/calculateUtils.js [app-ssr] (ecmascript)");
var _GridItem = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/react-grid-layout/build/GridItem.js [app-ssr] (ecmascript)"));
var _ReactGridLayoutPropTypes = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/react-grid-layout/build/ReactGridLayoutPropTypes.js [app-ssr] (ecmascript)"));
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function _defineProperty(obj, key, value) {
    key = _toPropertyKey(key);
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _toPropertyKey(arg) {
    var key = _toPrimitive(arg, "string");
    return typeof key === "symbol" ? key : String(key);
}
function _toPrimitive(input, hint) {
    if (typeof input !== "object" || input === null) return input;
    var prim = input[Symbol.toPrimitive];
    if (prim !== undefined) {
        var res = prim.call(input, hint || "default");
        if (typeof res !== "object") return res;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (hint === "string" ? String : Number)(input);
}
/*:: import type {
  ChildrenArray as ReactChildrenArray,
  Element as ReactElement
} from "react";*/ /*:: import type {
  CompactType,
  GridResizeEvent,
  GridDragEvent,
  DragOverEvent,
  Layout,
  DroppingPosition,
  LayoutItem
} from "./utils";*/ // Types
/*:: import type { PositionParams } from "./calculateUtils";*/ /*:: type State = {
  activeDrag: ?LayoutItem,
  layout: Layout,
  mounted: boolean,
  oldDragItem: ?LayoutItem,
  oldLayout: ?Layout,
  oldResizeItem: ?LayoutItem,
  resizing: boolean,
  droppingDOMNode: ?ReactElement<any>,
  droppingPosition?: DroppingPosition,
  // Mirrored props
  children: ReactChildrenArray<ReactElement<any>>,
  compactType?: CompactType,
  propsLayout?: Layout
};*/ /*:: import type { Props, DefaultProps } from "./ReactGridLayoutPropTypes";*/ // End Types
const layoutClassName = "react-grid-layout";
let isFirefox = false;
// Try...catch will protect from navigator not existing (e.g. node) or a bad implementation of navigator
try {
    isFirefox = /firefox/i.test(navigator.userAgent);
} catch (e) {
/* Ignore */ }
/**
 * A reactive, fluid grid layout with draggable, resizable components.
 */ class ReactGridLayout extends React.Component /*:: <Props, State>*/  {
    constructor(){
        super(...arguments);
        _defineProperty(this, "state", {
            activeDrag: null,
            layout: (0, _utils.synchronizeLayoutWithChildren)(this.props.layout, this.props.children, this.props.cols, // Legacy support for verticalCompact: false
            (0, _utils.compactType)(this.props), this.props.allowOverlap),
            mounted: false,
            oldDragItem: null,
            oldLayout: null,
            oldResizeItem: null,
            resizing: false,
            droppingDOMNode: null,
            children: []
        });
        _defineProperty(this, "dragEnterCounter", 0);
        /**
     * When dragging starts
     * @param {String} i Id of the child
     * @param {Number} x X position of the move
     * @param {Number} y Y position of the move
     * @param {Event} e The mousedown event
     * @param {Element} node The current dragging DOM element
     */ _defineProperty(this, "onDragStart", (i /*: string*/ , x /*: number*/ , y /*: number*/ , _ref /*:: */ )=>{
            let { e, node } /*: GridDragEvent*/  = _ref /*: GridDragEvent*/ ;
            const { layout } = this.state;
            const l = (0, _utils.getLayoutItem)(layout, i);
            if (!l) return;
            // Create placeholder (display only)
            const placeholder = {
                w: l.w,
                h: l.h,
                x: l.x,
                y: l.y,
                placeholder: true,
                i: i
            };
            this.setState({
                oldDragItem: (0, _utils.cloneLayoutItem)(l),
                oldLayout: layout,
                activeDrag: placeholder
            });
            return this.props.onDragStart(layout, l, l, null, e, node);
        });
        /**
     * Each drag movement create a new dragelement and move the element to the dragged location
     * @param {String} i Id of the child
     * @param {Number} x X position of the move
     * @param {Number} y Y position of the move
     * @param {Event} e The mousedown event
     * @param {Element} node The current dragging DOM element
     */ _defineProperty(this, "onDrag", (i, x, y, _ref2)=>{
            let { e, node } = _ref2;
            const { oldDragItem } = this.state;
            let { layout } = this.state;
            const { cols, allowOverlap, preventCollision } = this.props;
            const l = (0, _utils.getLayoutItem)(layout, i);
            if (!l) return;
            // Create placeholder (display only)
            const placeholder = {
                w: l.w,
                h: l.h,
                x: l.x,
                y: l.y,
                placeholder: true,
                i: i
            };
            // Move the element to the dragged location.
            const isUserAction = true;
            layout = (0, _utils.moveElement)(layout, l, x, y, isUserAction, preventCollision, (0, _utils.compactType)(this.props), cols, allowOverlap);
            this.props.onDrag(layout, oldDragItem, l, placeholder, e, node);
            this.setState({
                layout: allowOverlap ? layout : (0, _utils.compact)(layout, (0, _utils.compactType)(this.props), cols),
                activeDrag: placeholder
            });
        });
        /**
     * When dragging stops, figure out which position the element is closest to and update its x and y.
     * @param  {String} i Index of the child.
     * @param {Number} x X position of the move
     * @param {Number} y Y position of the move
     * @param {Event} e The mousedown event
     * @param {Element} node The current dragging DOM element
     */ _defineProperty(this, "onDragStop", (i, x, y, _ref3)=>{
            let { e, node } = _ref3;
            if (!this.state.activeDrag) return;
            const { oldDragItem } = this.state;
            let { layout } = this.state;
            const { cols, preventCollision, allowOverlap } = this.props;
            const l = (0, _utils.getLayoutItem)(layout, i);
            if (!l) return;
            // Move the element here
            const isUserAction = true;
            layout = (0, _utils.moveElement)(layout, l, x, y, isUserAction, preventCollision, (0, _utils.compactType)(this.props), cols, allowOverlap);
            // Set state
            const newLayout = allowOverlap ? layout : (0, _utils.compact)(layout, (0, _utils.compactType)(this.props), cols);
            this.props.onDragStop(newLayout, oldDragItem, l, null, e, node);
            const { oldLayout } = this.state;
            this.setState({
                activeDrag: null,
                layout: newLayout,
                oldDragItem: null,
                oldLayout: null
            });
            this.onLayoutMaybeChanged(newLayout, oldLayout);
        });
        _defineProperty(this, "onResizeStart", (i, w, h, _ref4)=>{
            let { e, node } = _ref4;
            const { layout } = this.state;
            const l = (0, _utils.getLayoutItem)(layout, i);
            if (!l) return;
            this.setState({
                oldResizeItem: (0, _utils.cloneLayoutItem)(l),
                oldLayout: this.state.layout,
                resizing: true
            });
            this.props.onResizeStart(layout, l, l, null, e, node);
        });
        _defineProperty(this, "onResize", (i, w, h, _ref5)=>{
            let { e, node, size, handle } = _ref5;
            const { oldResizeItem } = this.state;
            const { layout } = this.state;
            const { cols, preventCollision, allowOverlap } = this.props;
            let shouldMoveItem = false;
            let finalLayout;
            let x;
            let y;
            const [newLayout, l] = (0, _utils.withLayoutItem)(layout, i, (l)=>{
                let hasCollisions;
                x = l.x;
                y = l.y;
                if ([
                    "sw",
                    "w",
                    "nw",
                    "n",
                    "ne"
                ].indexOf(handle) !== -1) {
                    if ([
                        "sw",
                        "nw",
                        "w"
                    ].indexOf(handle) !== -1) {
                        x = l.x + (l.w - w);
                        w = l.x !== x && x < 0 ? l.w : w;
                        x = x < 0 ? 0 : x;
                    }
                    if ([
                        "ne",
                        "n",
                        "nw"
                    ].indexOf(handle) !== -1) {
                        y = l.y + (l.h - h);
                        h = l.y !== y && y < 0 ? l.h : h;
                        y = y < 0 ? 0 : y;
                    }
                    shouldMoveItem = true;
                }
                // Something like quad tree should be used
                // to find collisions faster
                if (preventCollision && !allowOverlap) {
                    const collisions = (0, _utils.getAllCollisions)(layout, {
                        ...l,
                        w,
                        h,
                        x,
                        y
                    }).filter((layoutItem)=>layoutItem.i !== l.i);
                    hasCollisions = collisions.length > 0;
                    // If we're colliding, we need adjust the placeholder.
                    if (hasCollisions) {
                        // Reset layoutItem dimensions if there were collisions
                        y = l.y;
                        h = l.h;
                        x = l.x;
                        w = l.w;
                        shouldMoveItem = false;
                    }
                }
                l.w = w;
                l.h = h;
                return l;
            });
            // Shouldn't ever happen, but typechecking makes it necessary
            if (!l) return;
            finalLayout = newLayout;
            if (shouldMoveItem) {
                // Move the element to the new position.
                const isUserAction = true;
                finalLayout = (0, _utils.moveElement)(newLayout, l, x, y, isUserAction, this.props.preventCollision, (0, _utils.compactType)(this.props), cols, allowOverlap);
            }
            // Create placeholder element (display only)
            const placeholder = {
                w: l.w,
                h: l.h,
                x: l.x,
                y: l.y,
                static: true,
                i: i
            };
            this.props.onResize(finalLayout, oldResizeItem, l, placeholder, e, node);
            // Re-compact the newLayout and set the drag placeholder.
            this.setState({
                layout: allowOverlap ? finalLayout : (0, _utils.compact)(finalLayout, (0, _utils.compactType)(this.props), cols),
                activeDrag: placeholder
            });
        });
        _defineProperty(this, "onResizeStop", (i, w, h, _ref6)=>{
            let { e, node } = _ref6;
            const { layout, oldResizeItem } = this.state;
            const { cols, allowOverlap } = this.props;
            const l = (0, _utils.getLayoutItem)(layout, i);
            // Set state
            const newLayout = allowOverlap ? layout : (0, _utils.compact)(layout, (0, _utils.compactType)(this.props), cols);
            this.props.onResizeStop(newLayout, oldResizeItem, l, null, e, node);
            const { oldLayout } = this.state;
            this.setState({
                activeDrag: null,
                layout: newLayout,
                oldResizeItem: null,
                oldLayout: null,
                resizing: false
            });
            this.onLayoutMaybeChanged(newLayout, oldLayout);
        });
        // Called while dragging an element. Part of browser native drag/drop API.
        // Native event target might be the layout itself, or an element within the layout.
        _defineProperty(this, "onDragOver", (e)=>{
            e.preventDefault(); // Prevent any browser native action
            e.stopPropagation();
            // we should ignore events from layout's children in Firefox
            // to avoid unpredictable jumping of a dropping placeholder
            // FIXME remove this hack
            if (isFirefox && // $FlowIgnore can't figure this out
            !e.nativeEvent.target?.classList.contains(layoutClassName)) {
                return false;
            }
            const { droppingItem, onDropDragOver, margin, cols, rowHeight, maxRows, width, containerPadding, transformScale } = this.props;
            // Allow user to customize the dropping item or short-circuit the drop based on the results
            // of the `onDragOver(e: Event)` callback.
            const onDragOverResult = onDropDragOver?.(e);
            if (onDragOverResult === false) {
                if (this.state.droppingDOMNode) {
                    this.removeDroppingPlaceholder();
                }
                return false;
            }
            const finalDroppingItem = {
                ...droppingItem,
                ...onDragOverResult
            };
            const { layout } = this.state;
            // $FlowIgnore missing def
            const gridRect = e.currentTarget.getBoundingClientRect(); // The grid's position in the viewport
            // Calculate the mouse position relative to the grid
            const layerX = e.clientX - gridRect.left;
            const layerY = e.clientY - gridRect.top;
            const droppingPosition = {
                left: layerX / transformScale,
                top: layerY / transformScale,
                e
            };
            if (!this.state.droppingDOMNode) {
                const positionParams /*: PositionParams*/  = {
                    cols,
                    margin,
                    maxRows,
                    rowHeight,
                    containerWidth: width,
                    containerPadding: containerPadding || margin
                };
                const calculatedPosition = (0, _calculateUtils.calcXY)(positionParams, layerY, layerX, finalDroppingItem.w, finalDroppingItem.h);
                this.setState({
                    droppingDOMNode: /*#__PURE__*/ React.createElement("div", {
                        key: finalDroppingItem.i
                    }),
                    droppingPosition,
                    layout: [
                        ...layout,
                        {
                            ...finalDroppingItem,
                            x: calculatedPosition.x,
                            y: calculatedPosition.y,
                            static: false,
                            isDraggable: true
                        }
                    ]
                });
            } else if (this.state.droppingPosition) {
                const { left, top } = this.state.droppingPosition;
                const shouldUpdatePosition = left != layerX || top != layerY;
                if (shouldUpdatePosition) {
                    this.setState({
                        droppingPosition
                    });
                }
            }
        });
        _defineProperty(this, "removeDroppingPlaceholder", ()=>{
            const { droppingItem, cols } = this.props;
            const { layout } = this.state;
            const newLayout = (0, _utils.compact)(layout.filter((l)=>l.i !== droppingItem.i), (0, _utils.compactType)(this.props), cols, this.props.allowOverlap);
            this.setState({
                layout: newLayout,
                droppingDOMNode: null,
                activeDrag: null,
                droppingPosition: undefined
            });
        });
        _defineProperty(this, "onDragLeave", (e)=>{
            e.preventDefault(); // Prevent any browser native action
            e.stopPropagation();
            this.dragEnterCounter--;
            // onDragLeave can be triggered on each layout's child.
            // But we know that count of dragEnter and dragLeave events
            // will be balanced after leaving the layout's container
            // so we can increase and decrease count of dragEnter and
            // when it'll be equal to 0 we'll remove the placeholder
            if (this.dragEnterCounter === 0) {
                this.removeDroppingPlaceholder();
            }
        });
        _defineProperty(this, "onDragEnter", (e)=>{
            e.preventDefault(); // Prevent any browser native action
            e.stopPropagation();
            this.dragEnterCounter++;
        });
        _defineProperty(this, "onDrop", (e /*: Event*/ )=>{
            e.preventDefault(); // Prevent any browser native action
            e.stopPropagation();
            const { droppingItem } = this.props;
            const { layout } = this.state;
            const item = layout.find((l)=>l.i === droppingItem.i);
            // reset dragEnter counter on drop
            this.dragEnterCounter = 0;
            this.removeDroppingPlaceholder();
            this.props.onDrop(layout, item, e);
        });
    }
    componentDidMount() {
        this.setState({
            mounted: true
        });
        // Possibly call back with layout on mount. This should be done after correcting the layout width
        // to ensure we don't rerender with the wrong width.
        this.onLayoutMaybeChanged(this.state.layout, this.props.layout);
    }
    static getDerivedStateFromProps(nextProps /*: Props*/ , prevState /*: State*/ ) /*: $Shape<State> | null*/ {
        let newLayoutBase;
        if (prevState.activeDrag) {
            return null;
        }
        // Legacy support for compactType
        // Allow parent to set layout directly.
        if (!(0, _fastEquals.deepEqual)(nextProps.layout, prevState.propsLayout) || nextProps.compactType !== prevState.compactType) {
            newLayoutBase = nextProps.layout;
        } else if (!(0, _utils.childrenEqual)(nextProps.children, prevState.children)) {
            // If children change, also regenerate the layout. Use our state
            // as the base in case because it may be more up to date than
            // what is in props.
            newLayoutBase = prevState.layout;
        }
        // We need to regenerate the layout.
        if (newLayoutBase) {
            const newLayout = (0, _utils.synchronizeLayoutWithChildren)(newLayoutBase, nextProps.children, nextProps.cols, (0, _utils.compactType)(nextProps), nextProps.allowOverlap);
            return {
                layout: newLayout,
                // We need to save these props to state for using
                // getDerivedStateFromProps instead of componentDidMount (in which we would get extra rerender)
                compactType: nextProps.compactType,
                children: nextProps.children,
                propsLayout: nextProps.layout
            };
        }
        return null;
    }
    shouldComponentUpdate(nextProps /*: Props*/ , nextState /*: State*/ ) /*: boolean*/ {
        return(// NOTE: this is almost always unequal. Therefore the only way to get better performance
        // from SCU is if the user intentionally memoizes children. If they do, and they can
        // handle changes properly, performance will increase.
        this.props.children !== nextProps.children || !(0, _utils.fastRGLPropsEqual)(this.props, nextProps, _fastEquals.deepEqual) || this.state.activeDrag !== nextState.activeDrag || this.state.mounted !== nextState.mounted || this.state.droppingPosition !== nextState.droppingPosition);
    }
    componentDidUpdate(prevProps /*: Props*/ , prevState /*: State*/ ) {
        if (!this.state.activeDrag) {
            const newLayout = this.state.layout;
            const oldLayout = prevState.layout;
            this.onLayoutMaybeChanged(newLayout, oldLayout);
        }
    }
    /**
   * Calculates a pixel value for the container.
   * @return {String} Container height in pixels.
   */ containerHeight() /*: ?string*/ {
        if (!this.props.autoSize) return;
        const nbRow = (0, _utils.bottom)(this.state.layout);
        const containerPaddingY = this.props.containerPadding ? this.props.containerPadding[1] : this.props.margin[1];
        return nbRow * this.props.rowHeight + (nbRow - 1) * this.props.margin[1] + containerPaddingY * 2 + "px";
    }
    onLayoutMaybeChanged(newLayout /*: Layout*/ , oldLayout /*: ?Layout*/ ) {
        if (!oldLayout) oldLayout = this.state.layout;
        if (!(0, _fastEquals.deepEqual)(oldLayout, newLayout)) {
            this.props.onLayoutChange(newLayout);
        }
    }
    /**
   * Create a placeholder object.
   * @return {Element} Placeholder div.
   */ placeholder() /*: ?ReactElement<any>*/ {
        const { activeDrag } = this.state;
        if (!activeDrag) return null;
        const { width, cols, margin, containerPadding, rowHeight, maxRows, useCSSTransforms, transformScale } = this.props;
        // {...this.state.activeDrag} is pretty slow, actually
        return /*#__PURE__*/ React.createElement(_GridItem.default, {
            w: activeDrag.w,
            h: activeDrag.h,
            x: activeDrag.x,
            y: activeDrag.y,
            i: activeDrag.i,
            className: `react-grid-placeholder ${this.state.resizing ? "placeholder-resizing" : ""}`,
            containerWidth: width,
            cols: cols,
            margin: margin,
            containerPadding: containerPadding || margin,
            maxRows: maxRows,
            rowHeight: rowHeight,
            isDraggable: false,
            isResizable: false,
            isBounded: false,
            useCSSTransforms: useCSSTransforms,
            transformScale: transformScale
        }, /*#__PURE__*/ React.createElement("div", null));
    }
    /**
   * Given a grid item, set its style attributes & surround in a <Draggable>.
   * @param  {Element} child React element.
   * @return {Element}       Element wrapped in draggable and properly placed.
   */ processGridItem(child /*: ReactElement<any>*/ , isDroppingItem /*: boolean*/ ) /*: ?ReactElement<any>*/ {
        if (!child || !child.key) return;
        const l = (0, _utils.getLayoutItem)(this.state.layout, String(child.key));
        if (!l) return null;
        const { width, cols, margin, containerPadding, rowHeight, maxRows, isDraggable, isResizable, isBounded, useCSSTransforms, transformScale, draggableCancel, draggableHandle, resizeHandles, resizeHandle } = this.props;
        const { mounted, droppingPosition } = this.state;
        // Determine user manipulations possible.
        // If an item is static, it can't be manipulated by default.
        // Any properties defined directly on the grid item will take precedence.
        const draggable = typeof l.isDraggable === "boolean" ? l.isDraggable : !l.static && isDraggable;
        const resizable = typeof l.isResizable === "boolean" ? l.isResizable : !l.static && isResizable;
        const resizeHandlesOptions = l.resizeHandles || resizeHandles;
        // isBounded set on child if set on parent, and child is not explicitly false
        const bounded = draggable && isBounded && l.isBounded !== false;
        return /*#__PURE__*/ React.createElement(_GridItem.default, {
            containerWidth: width,
            cols: cols,
            margin: margin,
            containerPadding: containerPadding || margin,
            maxRows: maxRows,
            rowHeight: rowHeight,
            cancel: draggableCancel,
            handle: draggableHandle,
            onDragStop: this.onDragStop,
            onDragStart: this.onDragStart,
            onDrag: this.onDrag,
            onResizeStart: this.onResizeStart,
            onResize: this.onResize,
            onResizeStop: this.onResizeStop,
            isDraggable: draggable,
            isResizable: resizable,
            isBounded: bounded,
            useCSSTransforms: useCSSTransforms && mounted,
            usePercentages: !mounted,
            transformScale: transformScale,
            w: l.w,
            h: l.h,
            x: l.x,
            y: l.y,
            i: l.i,
            minH: l.minH,
            minW: l.minW,
            maxH: l.maxH,
            maxW: l.maxW,
            static: l.static,
            droppingPosition: isDroppingItem ? droppingPosition : undefined,
            resizeHandles: resizeHandlesOptions,
            resizeHandle: resizeHandle
        }, child);
    }
    render() /*: React.Element<"div">*/ {
        const { className, style, isDroppable, innerRef } = this.props;
        const mergedClassName = (0, _clsx.default)(layoutClassName, className);
        const mergedStyle = {
            height: this.containerHeight(),
            ...style
        };
        return /*#__PURE__*/ React.createElement("div", {
            ref: innerRef,
            className: mergedClassName,
            style: mergedStyle,
            onDrop: isDroppable ? this.onDrop : _utils.noop,
            onDragLeave: isDroppable ? this.onDragLeave : _utils.noop,
            onDragEnter: isDroppable ? this.onDragEnter : _utils.noop,
            onDragOver: isDroppable ? this.onDragOver : _utils.noop
        }, React.Children.map(this.props.children, (child)=>this.processGridItem(child)), isDroppable && this.state.droppingDOMNode && this.processGridItem(this.state.droppingDOMNode, true), this.placeholder());
    }
}
exports.default = ReactGridLayout;
// TODO publish internal ReactClass displayName transform
_defineProperty(ReactGridLayout, "displayName", "ReactGridLayout");
// Refactored to another module to make way for preval
_defineProperty(ReactGridLayout, "propTypes", _ReactGridLayoutPropTypes.default);
_defineProperty(ReactGridLayout, "defaultProps", {
    autoSize: true,
    cols: 12,
    className: "",
    style: {},
    draggableHandle: "",
    draggableCancel: "",
    containerPadding: null,
    rowHeight: 150,
    maxRows: Infinity,
    // infinite vertical growth
    layout: [],
    margin: [
        10,
        10
    ],
    isBounded: false,
    isDraggable: true,
    isResizable: true,
    allowOverlap: false,
    isDroppable: false,
    useCSSTransforms: true,
    transformScale: 1,
    verticalCompact: true,
    compactType: "vertical",
    preventCollision: false,
    droppingItem: {
        i: "__dropping-elem__",
        h: 1,
        w: 1
    },
    resizeHandles: [
        "se"
    ],
    onLayoutChange: _utils.noop,
    onDragStart: _utils.noop,
    onDrag: _utils.noop,
    onDragStop: _utils.noop,
    onResizeStart: _utils.noop,
    onResize: _utils.noop,
    onResizeStop: _utils.noop,
    onDrop: _utils.noop,
    onDropDragOver: _utils.noop
});
}}),
"[project]/node_modules/react-grid-layout/build/responsiveUtils.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.findOrGenerateResponsiveLayout = findOrGenerateResponsiveLayout;
exports.getBreakpointFromWidth = getBreakpointFromWidth;
exports.getColsFromBreakpoint = getColsFromBreakpoint;
exports.sortBreakpoints = sortBreakpoints;
var _utils = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/utils.js [app-ssr] (ecmascript)");
/*:: import type { CompactType, Layout } from "./utils";*/ /*:: export type Breakpoint = string;*/ /*:: export type DefaultBreakpoints = "lg" | "md" | "sm" | "xs" | "xxs";*/ /*:: export type ResponsiveLayout<T: Breakpoint> = {
  +[breakpoint: T]: Layout
};*/ // + indicates read-only
/*:: export type Breakpoints<T: Breakpoint> = {
  +[breakpoint: T]: number
};*/ /*:: export type OnLayoutChangeCallback = (
  Layout,
  { [key: Breakpoint]: Layout }
) => void;*/ /**
 * Given a width, find the highest breakpoint that matches is valid for it (width > breakpoint).
 *
 * @param  {Object} breakpoints Breakpoints object (e.g. {lg: 1200, md: 960, ...})
 * @param  {Number} width Screen width.
 * @return {String}       Highest breakpoint that is less than width.
 */ function getBreakpointFromWidth(breakpoints /*: Breakpoints<Breakpoint>*/ , width /*: number*/ ) /*: Breakpoint*/ {
    const sorted = sortBreakpoints(breakpoints);
    let matching = sorted[0];
    for(let i = 1, len = sorted.length; i < len; i++){
        const breakpointName = sorted[i];
        if (width > breakpoints[breakpointName]) matching = breakpointName;
    }
    return matching;
}
/**
 * Given a breakpoint, get the # of cols set for it.
 * @param  {String} breakpoint Breakpoint name.
 * @param  {Object} cols       Map of breakpoints to cols.
 * @return {Number}            Number of cols.
 */ function getColsFromBreakpoint(breakpoint /*: Breakpoint*/ , cols /*: Breakpoints<Breakpoint>*/ ) /*: number*/ {
    if (!cols[breakpoint]) {
        throw new Error("ResponsiveReactGridLayout: `cols` entry for breakpoint " + breakpoint + " is missing!");
    }
    return cols[breakpoint];
}
/**
 * Given existing layouts and a new breakpoint, find or generate a new layout.
 *
 * This finds the layout above the new one and generates from it, if it exists.
 *
 * @param  {Object} layouts     Existing layouts.
 * @param  {Array} breakpoints All breakpoints.
 * @param  {String} breakpoint New breakpoint.
 * @param  {String} breakpoint Last breakpoint (for fallback).
 * @param  {Number} cols       Column count at new breakpoint.
 * @param  {Boolean} verticalCompact Whether or not to compact the layout
 *   vertically.
 * @return {Array}             New layout.
 */ function findOrGenerateResponsiveLayout(layouts /*: ResponsiveLayout<Breakpoint>*/ , breakpoints /*: Breakpoints<Breakpoint>*/ , breakpoint /*: Breakpoint*/ , lastBreakpoint /*: Breakpoint*/ , cols /*: number*/ , compactType /*: CompactType*/ ) /*: Layout*/ {
    // If it already exists, just return it.
    if (layouts[breakpoint]) return (0, _utils.cloneLayout)(layouts[breakpoint]);
    // Find or generate the next layout
    let layout = layouts[lastBreakpoint];
    const breakpointsSorted = sortBreakpoints(breakpoints);
    const breakpointsAbove = breakpointsSorted.slice(breakpointsSorted.indexOf(breakpoint));
    for(let i = 0, len = breakpointsAbove.length; i < len; i++){
        const b = breakpointsAbove[i];
        if (layouts[b]) {
            layout = layouts[b];
            break;
        }
    }
    layout = (0, _utils.cloneLayout)(layout || []); // clone layout so we don't modify existing items
    return (0, _utils.compact)((0, _utils.correctBounds)(layout, {
        cols: cols
    }), compactType, cols);
}
/**
 * Given breakpoints, return an array of breakpoints sorted by width. This is usually
 * e.g. ['xxs', 'xs', 'sm', ...]
 *
 * @param  {Object} breakpoints Key/value pair of breakpoint names to widths.
 * @return {Array}              Sorted breakpoints.
 */ function sortBreakpoints(breakpoints /*: Breakpoints<Breakpoint>*/ ) /*: Array<Breakpoint>*/ {
    const keys /*: Array<string>*/  = Object.keys(breakpoints);
    return keys.sort(function(a, b) {
        return breakpoints[a] - breakpoints[b];
    });
}
}}),
"[project]/node_modules/react-grid-layout/build/ResponsiveReactGridLayout.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var _propTypes = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/prop-types/index.js [app-ssr] (ecmascript)"));
var _fastEquals = __turbopack_context__.r("[project]/node_modules/fast-equals/dist/fast-equals.esm.js [app-ssr] (ecmascript)");
var _utils = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/utils.js [app-ssr] (ecmascript)");
var _responsiveUtils = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/responsiveUtils.js [app-ssr] (ecmascript)");
var _ReactGridLayout = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/react-grid-layout/build/ReactGridLayout.js [app-ssr] (ecmascript)"));
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function _extends() {
    _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined);
    return _extends.apply(this, arguments);
}
function _defineProperty(obj, key, value) {
    key = _toPropertyKey(key);
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _toPropertyKey(arg) {
    var key = _toPrimitive(arg, "string");
    return typeof key === "symbol" ? key : String(key);
}
function _toPrimitive(input, hint) {
    if (typeof input !== "object" || input === null) return input;
    var prim = input[Symbol.toPrimitive];
    if (prim !== undefined) {
        var res = prim.call(input, hint || "default");
        if (typeof res !== "object") return res;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (hint === "string" ? String : Number)(input);
} /*:: import { type Layout, type Pick } from "./utils";*/  /*:: import { type ResponsiveLayout, type OnLayoutChangeCallback, type Breakpoints } from "./responsiveUtils";*/ 
// $FlowFixMe[method-unbinding]
const type = (obj)=>Object.prototype.toString.call(obj);
/**
 * Get a value of margin or containerPadding.
 *
 * @param  {Array | Object} param Margin | containerPadding, e.g. [10, 10] | {lg: [10, 10], ...}.
 * @param  {String} breakpoint   Breakpoint: lg, md, sm, xs and etc.
 * @return {Array}
 */ function getIndentationValue(param /*: { [key: string]: T } | T*/ , breakpoint /*: string*/ ) /*: T*/ {
    // $FlowIgnore TODO fix this typedef
    if (param == null) return null;
    // $FlowIgnore TODO fix this typedef
    return Array.isArray(param) ? param : param[breakpoint];
}
/*:: type State = {
  layout: Layout,
  breakpoint: string,
  cols: number,
  layouts?: ResponsiveLayout<string>
};*/ /*:: type Props<Breakpoint: string = string> = {|
  ...React.ElementConfig<typeof ReactGridLayout>,

  // Responsive config
  breakpoint?: ?Breakpoint,
  breakpoints: Breakpoints<Breakpoint>,
  cols: { [key: Breakpoint]: number },
  layouts: ResponsiveLayout<Breakpoint>,
  width: number,
  margin: { [key: Breakpoint]: [number, number] } | [number, number],
  /* prettier-ignore *-/
  containerPadding: { [key: Breakpoint]: ?[number, number] } | ?[number, number],

  // Callbacks
  onBreakpointChange: (Breakpoint, cols: number) => void,
  onLayoutChange: OnLayoutChangeCallback,
  onWidthChange: (
    containerWidth: number,
    margin: [number, number],
    cols: number,
    containerPadding: ?[number, number]
  ) => void
|};*/ /*:: type DefaultProps = Pick<
  Props<>,
  {|
    allowOverlap: 0,
    breakpoints: 0,
    cols: 0,
    containerPadding: 0,
    layouts: 0,
    margin: 0,
    onBreakpointChange: 0,
    onLayoutChange: 0,
    onWidthChange: 0
  |}
>;*/ class ResponsiveReactGridLayout extends React.Component {
    constructor(){
        super(...arguments);
        _defineProperty(this, "state", this.generateInitialState());
        // wrap layouts so we do not need to pass layouts to child
        _defineProperty(this, "onLayoutChange", (layout /*: Layout*/ )=>{
            this.props.onLayoutChange(layout, {
                ...this.props.layouts,
                [this.state.breakpoint]: layout
            });
        });
    }
    generateInitialState() /*: State*/ {
        const { width, breakpoints, layouts, cols } = this.props;
        const breakpoint = (0, _responsiveUtils.getBreakpointFromWidth)(breakpoints, width);
        const colNo = (0, _responsiveUtils.getColsFromBreakpoint)(breakpoint, cols);
        // verticalCompact compatibility, now deprecated
        const compactType = this.props.verticalCompact === false ? null : this.props.compactType;
        // Get the initial layout. This can tricky; we try to generate one however possible if one doesn't exist
        // for this layout.
        const initialLayout = (0, _responsiveUtils.findOrGenerateResponsiveLayout)(layouts, breakpoints, breakpoint, breakpoint, colNo, compactType);
        return {
            layout: initialLayout,
            breakpoint: breakpoint,
            cols: colNo
        };
    }
    static getDerivedStateFromProps(nextProps /*: Props<*>*/ , prevState /*: State*/ ) /*: ?$Shape<State>*/ {
        if (!(0, _fastEquals.deepEqual)(nextProps.layouts, prevState.layouts)) {
            // Allow parent to set layouts directly.
            const { breakpoint, cols } = prevState;
            // Since we're setting an entirely new layout object, we must generate a new responsive layout
            // if one does not exist.
            const newLayout = (0, _responsiveUtils.findOrGenerateResponsiveLayout)(nextProps.layouts, nextProps.breakpoints, breakpoint, breakpoint, cols, nextProps.compactType);
            return {
                layout: newLayout,
                layouts: nextProps.layouts
            };
        }
        return null;
    }
    componentDidUpdate(prevProps /*: Props<*>*/ ) {
        // Allow parent to set width or breakpoint directly.
        if (this.props.width != prevProps.width || this.props.breakpoint !== prevProps.breakpoint || !(0, _fastEquals.deepEqual)(this.props.breakpoints, prevProps.breakpoints) || !(0, _fastEquals.deepEqual)(this.props.cols, prevProps.cols)) {
            this.onWidthChange(prevProps);
        }
    }
    /**
   * When the width changes work through breakpoints and reset state with the new width & breakpoint.
   * Width changes are necessary to figure out the widget widths.
   */ onWidthChange(prevProps /*: Props<*>*/ ) {
        const { breakpoints, cols, layouts, compactType } = this.props;
        const newBreakpoint = this.props.breakpoint || (0, _responsiveUtils.getBreakpointFromWidth)(this.props.breakpoints, this.props.width);
        const lastBreakpoint = this.state.breakpoint;
        const newCols /*: number*/  = (0, _responsiveUtils.getColsFromBreakpoint)(newBreakpoint, cols);
        const newLayouts = {
            ...layouts
        };
        // Breakpoint change
        if (lastBreakpoint !== newBreakpoint || prevProps.breakpoints !== breakpoints || prevProps.cols !== cols) {
            // Preserve the current layout if the current breakpoint is not present in the next layouts.
            if (!(lastBreakpoint in newLayouts)) newLayouts[lastBreakpoint] = (0, _utils.cloneLayout)(this.state.layout);
            // Find or generate a new layout.
            let layout = (0, _responsiveUtils.findOrGenerateResponsiveLayout)(newLayouts, breakpoints, newBreakpoint, lastBreakpoint, newCols, compactType);
            // This adds missing items.
            layout = (0, _utils.synchronizeLayoutWithChildren)(layout, this.props.children, newCols, compactType, this.props.allowOverlap);
            // Store the new layout.
            newLayouts[newBreakpoint] = layout;
            // callbacks
            this.props.onBreakpointChange(newBreakpoint, newCols);
            this.props.onLayoutChange(layout, newLayouts);
            this.setState({
                breakpoint: newBreakpoint,
                layout: layout,
                cols: newCols
            });
        }
        const margin = getIndentationValue(this.props.margin, newBreakpoint);
        const containerPadding = getIndentationValue(this.props.containerPadding, newBreakpoint);
        //call onWidthChange on every change of width, not only on breakpoint changes
        this.props.onWidthChange(this.props.width, margin, newCols, containerPadding);
    }
    render() /*: React.Element<typeof ReactGridLayout>*/ {
        /* eslint-disable no-unused-vars */ const { breakpoint, breakpoints, cols, layouts, margin, containerPadding, onBreakpointChange, onLayoutChange, onWidthChange, ...other } = this.props;
        /* eslint-enable no-unused-vars */ return /*#__PURE__*/ React.createElement(_ReactGridLayout.default, _extends({}, other, {
            // $FlowIgnore should allow nullable here due to DefaultProps
            margin: getIndentationValue(margin, this.state.breakpoint),
            containerPadding: getIndentationValue(containerPadding, this.state.breakpoint),
            onLayoutChange: this.onLayoutChange,
            layout: this.state.layout,
            cols: this.state.cols
        }));
    }
}
exports.default = ResponsiveReactGridLayout;
// This should only include propTypes needed in this code; RGL itself
// will do validation of the rest props passed to it.
_defineProperty(ResponsiveReactGridLayout, "propTypes", {
    //
    // Basic props
    //
    // Optional, but if you are managing width yourself you may want to set the breakpoint
    // yourself as well.
    breakpoint: _propTypes.default.string,
    // {name: pxVal}, e.g. {lg: 1200, md: 996, sm: 768, xs: 480}
    breakpoints: _propTypes.default.object,
    allowOverlap: _propTypes.default.bool,
    // # of cols. This is a breakpoint -> cols map
    cols: _propTypes.default.object,
    // # of margin. This is a breakpoint -> margin map
    // e.g. { lg: [5, 5], md: [10, 10], sm: [15, 15] }
    // Margin between items [x, y] in px
    // e.g. [10, 10]
    margin: _propTypes.default.oneOfType([
        _propTypes.default.array,
        _propTypes.default.object
    ]),
    // # of containerPadding. This is a breakpoint -> containerPadding map
    // e.g. { lg: [5, 5], md: [10, 10], sm: [15, 15] }
    // Padding inside the container [x, y] in px
    // e.g. [10, 10]
    containerPadding: _propTypes.default.oneOfType([
        _propTypes.default.array,
        _propTypes.default.object
    ]),
    // layouts is an object mapping breakpoints to layouts.
    // e.g. {lg: Layout, md: Layout, ...}
    layouts (props /*: Props<>*/ , propName /*: string*/ ) {
        if (type(props[propName]) !== "[object Object]") {
            throw new Error("Layout property must be an object. Received: " + type(props[propName]));
        }
        Object.keys(props[propName]).forEach((key)=>{
            if (!(key in props.breakpoints)) {
                throw new Error("Each key in layouts must align with a key in breakpoints.");
            }
            (0, _utils.validateLayout)(props.layouts[key], "layouts." + key);
        });
    },
    // The width of this component.
    // Required in this propTypes stanza because generateInitialState() will fail without it.
    width: _propTypes.default.number.isRequired,
    //
    // Callbacks
    //
    // Calls back with breakpoint and new # cols
    onBreakpointChange: _propTypes.default.func,
    // Callback so you can save the layout.
    // Calls back with (currentLayout, allLayouts). allLayouts are keyed by breakpoint.
    onLayoutChange: _propTypes.default.func,
    // Calls back with (containerWidth, margin, cols, containerPadding)
    onWidthChange: _propTypes.default.func
});
_defineProperty(ResponsiveReactGridLayout, "defaultProps", {
    breakpoints: {
        lg: 1200,
        md: 996,
        sm: 768,
        xs: 480,
        xxs: 0
    },
    cols: {
        lg: 12,
        md: 10,
        sm: 6,
        xs: 4,
        xxs: 2
    },
    containerPadding: {
        lg: null,
        md: null,
        sm: null,
        xs: null,
        xxs: null
    },
    layouts: {},
    margin: [
        10,
        10
    ],
    allowOverlap: false,
    onBreakpointChange: _utils.noop,
    onLayoutChange: _utils.noop,
    onWidthChange: _utils.noop
});
}}),
"[project]/node_modules/react-grid-layout/build/components/WidthProvider.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = WidthProvideRGL;
var React = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var _propTypes = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/prop-types/index.js [app-ssr] (ecmascript)"));
var _resizeObserverPolyfill = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js [app-ssr] (ecmascript)"));
var _clsx = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/clsx/dist/clsx.js [app-ssr] (ecmascript)"));
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function _extends() {
    _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined);
    return _extends.apply(this, arguments);
}
function _defineProperty(obj, key, value) {
    key = _toPropertyKey(key);
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _toPropertyKey(arg) {
    var key = _toPrimitive(arg, "string");
    return typeof key === "symbol" ? key : String(key);
}
function _toPrimitive(input, hint) {
    if (typeof input !== "object" || input === null) return input;
    var prim = input[Symbol.toPrimitive];
    if (prim !== undefined) {
        var res = prim.call(input, hint || "default");
        if (typeof res !== "object") return res;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (hint === "string" ? String : Number)(input);
}
/*:: import type { ReactRef } from "../ReactGridLayoutPropTypes";*/ /*:: type WPDefaultProps = {|
  measureBeforeMount: boolean
|};*/ /*:: type WPProps = {|
  className?: string,
  style?: Object,
  ...WPDefaultProps
|};*/ // eslint-disable-next-line no-unused-vars
/*:: type WPState = {|
  width: number
|};*/ /*:: type ComposedProps<Config> = {|
  ...Config,
  measureBeforeMount?: boolean,
  className?: string,
  style?: Object,
  width?: number
|};*/ const layoutClassName = "react-grid-layout";
/*
 * A simple HOC that provides facility for listening to container resizes.
 *
 * The Flow type is pretty janky here. I can't just spread `WPProps` into this returned object - I wish I could - but it triggers
 * a flow bug of some sort that causes it to stop typechecking.
 */ function WidthProvideRGL(ComposedComponent /*: React.AbstractComponent<Config>*/ ) /*: React.AbstractComponent<ComposedProps<Config>>*/ {
    var _class;
    return _class = class WidthProvider extends React.Component {
        constructor(){
            super(...arguments);
            _defineProperty(this, "state", {
                width: 1280
            });
            _defineProperty(this, "elementRef", /*#__PURE__*/ React.createRef());
            _defineProperty(this, "mounted", false);
            _defineProperty(this, "resizeObserver", void 0);
        }
        componentDidMount() {
            this.mounted = true;
            this.resizeObserver = new _resizeObserverPolyfill.default((entries)=>{
                const node = this.elementRef.current;
                if (node instanceof HTMLElement) {
                    const width = entries[0].contentRect.width;
                    this.setState({
                        width
                    });
                }
            });
            const node = this.elementRef.current;
            if (node instanceof HTMLElement) {
                this.resizeObserver.observe(node);
            }
        }
        componentWillUnmount() {
            this.mounted = false;
            const node = this.elementRef.current;
            if (node instanceof HTMLElement) {
                this.resizeObserver.unobserve(node);
            }
            this.resizeObserver.disconnect();
        }
        render() {
            const { measureBeforeMount, ...rest } = this.props;
            if (measureBeforeMount && !this.mounted) {
                return /*#__PURE__*/ React.createElement("div", {
                    className: (0, _clsx.default)(this.props.className, layoutClassName),
                    style: this.props.style,
                    ref: this.elementRef
                });
            }
            return /*#__PURE__*/ React.createElement(ComposedComponent, _extends({
                innerRef: this.elementRef
            }, rest, this.state));
        }
    }, _defineProperty(_class, "defaultProps", {
        measureBeforeMount: false
    }), _defineProperty(_class, "propTypes", {
        // If true, will not render children until mounted. Useful for getting the exact width before
        // rendering, to prevent any unsightly resizing.
        measureBeforeMount: _propTypes.default.bool
    }), _class;
}
}}),
"[project]/node_modules/react-grid-layout/index.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/ReactGridLayout.js [app-ssr] (ecmascript)").default;
module.exports.utils = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/utils.js [app-ssr] (ecmascript)");
module.exports.calculateUtils = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/calculateUtils.js [app-ssr] (ecmascript)");
module.exports.Responsive = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/ResponsiveReactGridLayout.js [app-ssr] (ecmascript)").default;
module.exports.Responsive.utils = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/responsiveUtils.js [app-ssr] (ecmascript)");
module.exports.WidthProvider = __turbopack_context__.r("[project]/node_modules/react-grid-layout/build/components/WidthProvider.js [app-ssr] (ecmascript)").default;
}}),

};

//# sourceMappingURL=node_modules_react-grid-layout_f495c81a._.js.map