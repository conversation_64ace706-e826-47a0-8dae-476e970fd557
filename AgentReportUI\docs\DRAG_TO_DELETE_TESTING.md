# Drag-to-Delete Feature Testing Guide

## Overview

The drag-to-delete feature allows users to delete chart widgets by dragging them to a trash can zone that appears at the bottom of the screen during drag operations.

## How It Works

1. **Drag Detection**: When a user starts dragging a chart widget, the system detects this via react-grid-layout's `onDragStart` callback
2. **Trash Can Appearance**: A trash can icon appears at the bottom center of the viewport with a fixed position
3. **Collision Detection**: Mouse position is tracked during drag to detect when the cursor is over the delete zone
4. **Visual Feedback**: The trash can changes appearance (color, size, animation) when hovered
5. **Deletion**: When the drag ends while over the delete zone, the widget is deleted

## Testing Steps

### Basic Functionality Test

1. Navigate to the Dashboard page
2. Create at least one chart widget
3. Start dragging the chart widget by clicking and holding on it
4. **Expected**: A trash can icon should appear at the bottom center of the screen
5. Drag the widget towards the trash can
6. **Expected**: The trash can should change color and scale up when the mouse cursor is over it
7. Release the mouse button while over the trash can
8. **Expected**: The chart widget should be deleted from the dashboard

### Visual Feedback Test

1. Start dragging a chart widget
2. **Expected**: The widget should rotate slightly and become semi-transparent
3. **Expected**: The widget should have an enhanced shadow and border
4. Move the cursor over the trash can
5. **Expected**: The trash can should turn red and scale up
6. **Expected**: The text should change from "Drop here to delete" to "Release to delete"
7. Move the cursor away from the trash can
8. **Expected**: The trash can should return to its normal appearance

### Debug Console Test

1. Open browser developer tools (F12)
2. Go to the Console tab
3. Start dragging a chart widget
4. **Expected**: Console should log "Drag started for widget: [widget-id]"
5. Move cursor over the trash can
6. **Expected**: Console should log "Delete zone hover: true"
7. Move cursor away from trash can
8. **Expected**: Console should log "Delete zone hover: false"
9. Drop widget over trash can
10. **Expected**: Console should log "Drag stopped - Over delete zone: true Widget ID: [widget-id]"
11. **Expected**: Console should log "Deleting widget: [widget-id]"

## Known Issues and Troubleshooting

### Issue: Trash can doesn't appear
- **Cause**: Drag event not being detected
- **Solution**: Check that the chart widget is properly draggable and react-grid-layout is working

### Issue: Collision detection not working
- **Cause**: CSS class `.drag-delete-zone` not found or mouse tracking not working
- **Solution**: Check browser console for errors, verify CSS classes are applied

### Issue: Widget not deleted when dropped
- **Cause**: `onDeleteWidget` callback not being called or not working properly
- **Solution**: Check console logs, verify the delete handler in Dashboard.tsx is working

### Issue: Visual feedback not showing
- **Cause**: CSS animations or hover states not applying
- **Solution**: Check that CSS classes are properly applied and Tailwind styles are loading

## Technical Implementation Details

### Key Components
- `DashboardView.tsx`: Main component with drag logic
- `DragToDeleteZone.tsx`: Trash can component
- `globals.css`: Drag-related styling

### Key Functions
- `handleDragStart`: Detects when dragging begins
- `handleMouseMove`: Tracks mouse position for collision detection
- `handleDragStopWithDelete`: Handles drag end and deletion logic

### CSS Classes
- `.drag-delete-zone`: Main trash can element
- `.react-grid-item.react-draggable-dragging`: Styling for dragged widgets
- `.hovered`: Applied when cursor is over delete zone

## Browser Compatibility

Tested on:
- Chrome 120+
- Firefox 120+
- Safari 17+
- Edge 120+

## Performance Notes

- Mouse move events are throttled to prevent excessive re-renders
- Collision detection uses efficient bounding box calculations
- CSS animations use hardware acceleration for smooth performance
