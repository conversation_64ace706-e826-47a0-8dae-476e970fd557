"""Custom Exception Classes

This module defines custom exception classes for authentication and security operations.
"""

from fastapi import HTTPException, status


class AuthenticationError(HTTPException):
    """Base class for authentication-related errors."""
    
    def __init__(self, detail: str = "Authentication failed", status_code: int = status.HTTP_401_UNAUTHORIZED):
        super().__init__(
            status_code=status_code,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )


class TokenExpiredError(AuthenticationError):
    """Raised when a token has expired."""
    
    def __init__(self, token_type: str = "token"):
        super().__init__(
            detail=f"The {token_type} has expired",
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class TokenRevokedError(AuthenticationError):
    """Raised when a token has been revoked."""
    
    def __init__(self, token_type: str = "token"):
        super().__init__(
            detail=f"The {token_type} has been revoked",
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class TokenBlacklistedError(AuthenticationError):
    """Raised when a token is blacklisted."""
    
    def __init__(self, token_type: str = "token"):
        super().__init__(
            detail=f"The {token_type} is blacklisted",
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class InvalidTokenError(AuthenticationError):
    """Raised when a token is invalid or malformed."""
    
    def __init__(self, token_type: str = "token"):
        super().__init__(
            detail=f"Invalid {token_type}",
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class TokenNotFoundError(AuthenticationError):
    """Raised when a token is not found in the database."""
    
    def __init__(self, token_type: str = "token"):
        super().__init__(
            detail=f"The {token_type} was not found",
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class UserNotFoundError(AuthenticationError):
    """Raised when a user is not found."""
    
    def __init__(self):
        super().__init__(
            detail="User not found",
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class UserInactiveError(AuthenticationError):
    """Raised when a user account is inactive."""
    
    def __init__(self):
        super().__init__(
            detail="User account is inactive",
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class TokenRotationError(HTTPException):
    """Raised when token rotation fails."""
    
    def __init__(self, detail: str = "Token rotation failed"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )


class ConcurrentTokenUseError(AuthenticationError):
    """Raised when concurrent token usage is detected."""

    def __init__(self):
        super().__init__(
            detail="Concurrent token usage detected. Please re-authenticate.",
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class EmailAlreadyExistsError(HTTPException):
    """Raised when attempting to register with an email that already exists."""

    def __init__(self, email: str = None):
        detail = "An account with this email address already exists"
        if email:
            # Don't include the actual email in the error message for security
            detail = "An account with this email address already exists"
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail
        )
