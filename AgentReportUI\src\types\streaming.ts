// Streaming-related type definitions

export interface StreamingState {
  isStreaming: boolean;
  streamingContent: string;
  error: string | null;
  isComplete: boolean;
}

export interface StreamingMessage {
  id: string;
  role: 'user' | 'agent';
  content: string;
  isStreaming?: boolean;
  streamingContent?: string;
  timestamp?: Date;
}

export interface UseTokenStreamingOptions {
  sessionId: string;
  query: string;
  outputFormat?: string;
  onTokenReceived?: (token: string) => void;
  onComplete?: (finalContent: string) => void;
  onError?: (error: string) => void;
}

export interface UseTokenStreamingReturn {
  startStreaming: () => void;
  stopStreaming: () => void;
  streamingState: StreamingState;
  isConnected: boolean;
}

// SSE Event types from backend
export interface SSETokenEvent {
  type: 'token_stream';
  agent: string;
  data: {
    token: string;
  };
  timestamp: string;
}

export interface SSECompleteEvent {
  type: 'conversation_complete';
  agent: string;
  data: {
    message: string;
  };
  timestamp: string;
}

export interface SSEErrorEvent {
  type: 'error';
  agent: string;
  data: {
    error: string;
  };
  timestamp: string;
}

export type SSEEvent = SSETokenEvent | SSECompleteEvent | SSEErrorEvent;

// Typewriter effect configuration
export interface TypewriterConfig {
  speed: number; // milliseconds per character
  enableCursor: boolean;
  cursorChar: string;
}
