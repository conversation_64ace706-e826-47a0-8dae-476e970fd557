// Main type exports - barrel file for easy imports

// Re-export all types from individual modules
export * from './api';
export * from './auth';
export * from './profile';
export * from './streaming';
export * from './chart';
export * from './dashboard';
export * from './chat';

// Common utility types
export interface BaseEntity {
  id: string;
  created_at: Date;
  updated_at: Date;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginationResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

export interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  resolvedTheme?: Theme;
  mounted: boolean;
}

// Analysis project types
export interface AnalysisProject {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'running' | 'completed' | 'error';
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  dataSource?: string;
  stepCount: number;
  progress?: number;
  userId: string;
}

export interface PipelineStep {
  id: string;
  projectId: string;
  title: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  type: 'data_loading' | 'processing' | 'analysis' | 'visualization' | 'export';
  order: number;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  outputs?: PipelineStepOutput;
}

export interface PipelineStepOutput {
  data?: DataOutput;
  code?: string;
  visualization?: VisualizationOutput;
  summary?: string;
}

export interface DataOutput {
  rows: Record<string, any>[];
  columns: DataColumn[];
  totalRows: number;
  schema?: Record<string, string>;
}

export interface DataColumn {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date';
  nullable: boolean;
}

export interface VisualizationOutput {
  type: 'chart' | 'graph' | 'table' | 'heatmap';
  config: Record<string, any>;
  data: Record<string, any>;
}

// API Request/Response types
export interface CreateAnalysisProjectRequest {
  name: string;
  description?: string;
  dataSource?: string;
}

export interface UpdateAnalysisProjectRequest {
  name?: string;
  description?: string;
  status?: AnalysisProject['status'];
}

export interface ListAnalysisProjectsResponse {
  projects: AnalysisProject[];
  totalCount: number;
}

export interface GetProjectDataRequest {
  projectId: string;
  stepId?: string;
  page?: number;
  pageSize?: number;
}

export interface GetProjectDataResponse {
  project: AnalysisProject;
  steps: PipelineStep[];
  data?: DataOutput;
}

// File upload types
export interface FileUploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadedFile {
  id: string;
  filename: string;
  size: number;
  type: string;
  uploadedAt: string;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  errorMessage?: string;
}

export interface FileUploadRequest {
  file: File;
  projectId: string;
  onProgress?: (progress: FileUploadProgress) => void;
}

export interface FileUploadResponse {
  success: boolean;
  data?: {
    fileId: string;
    filename: string;
    rowCount: number;
    columnCount: number;
    previewData: DataOutput;
  };
  error?: string;
}
