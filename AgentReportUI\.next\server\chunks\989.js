"use strict";exports.id=989,exports.ids=[989],exports.modules={9989:(e,t,r)=>{r.d(t,{Kq:()=>U,UC:()=>Z,ZL:()=>Y,bL:()=>W,i3:()=>K,l9:()=>X});var n=r(43210),o=r(70569),i=r(98599),l=r(11273),a=r(31355),s=r(96963),u=r(55509),c=r(25028),d=r(46059),p=r(14163),f=r(8730),x=r(65551),h=r(69024),g=r(60687),[v,y]=(0,l.A)("Tooltip",[u.Bk]),m=(0,u.Bk)(),b="TooltipProvider",w="tooltip.open",[C,T]=v(b),E=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,g.jsx)(C,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:l})};E.displayName=b;var L="Tooltip",[j,k]=v(L),R=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,d=T(L,e.__scopeTooltip),p=m(t),[f,h]=n.useState(null),v=(0,s.B)(),y=n.useRef(0),b=a??d.disableHoverableContent,C=c??d.delayDuration,E=n.useRef(!1),[k,R]=(0,x.i)({prop:o,defaultProp:i??!1,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(w))):d.onClose(),l?.(e)},caller:L}),P=n.useMemo(()=>k?E.current?"delayed-open":"instant-open":"closed",[k]),M=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,E.current=!1,R(!0)},[R]),_=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,R(!1)},[R]),D=n.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{E.current=!0,R(!0),y.current=0},C)},[C,R]);return n.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,g.jsx)(u.bL,{...p,children:(0,g.jsx)(j,{scope:t,contentId:v,open:k,stateAttribute:P,trigger:f,onTriggerChange:h,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?D():M()},[d.isOpenDelayedRef,D,M]),onTriggerLeave:n.useCallback(()=>{b?_():(window.clearTimeout(y.current),y.current=0)},[_,b]),onOpen:M,onClose:_,disableHoverableContent:b,children:r})})};R.displayName=L;var P="TooltipTrigger",M=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...l}=e,a=k(P,r),s=T(P,r),c=m(r),d=n.useRef(null),f=(0,i.s)(t,d,a.onTriggerChange),x=n.useRef(!1),h=n.useRef(!1),v=n.useCallback(()=>x.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",v),[v]),(0,g.jsx)(u.Mz,{asChild:!0,...c,children:(0,g.jsx)(p.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(h.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),h.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),h.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),x.current=!0,document.addEventListener("pointerup",v,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{x.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});M.displayName=P;var _="TooltipPortal",[D,O]=v(_,{forceMount:void 0}),B=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,i=k(_,t);return(0,g.jsx)(D,{scope:t,forceMount:r,children:(0,g.jsx)(d.C,{present:r||i.open,children:(0,g.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};B.displayName=_;var I="TooltipContent",N=n.forwardRef((e,t)=>{let r=O(I,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,l=k(I,e.__scopeTooltip);return(0,g.jsx)(d.C,{present:n||l.open,children:l.disableHoverableContent?(0,g.jsx)(q,{side:o,...i,ref:t}):(0,g.jsx)(A,{side:o,...i,ref:t})})}),A=n.forwardRef((e,t)=>{let r=k(I,e.__scopeTooltip),o=T(I,e.__scopeTooltip),l=n.useRef(null),a=(0,i.s)(t,l),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,p=l.current,{onPointerInTransitChange:f}=o,x=n.useCallback(()=>{u(null),f(!1)},[f]),h=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>x(),[x]),n.useEffect(()=>{if(c&&p){let e=e=>h(e,p),t=e=>h(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,h,x]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=c?.contains(t)||p?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],s=l.x,u=l.y,c=a.x,d=a.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?x():o&&(x(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,x]),(0,g.jsx)(q,{...e,ref:a})}),[H,F]=v(L,{isInside:!1}),S=(0,f.Dc)("TooltipContent"),q=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...c}=e,d=k(I,r),p=m(r),{onClose:f}=d;return n.useEffect(()=>(document.addEventListener(w,f),()=>document.removeEventListener(w,f)),[f]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;t?.contains(d.trigger)&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,g.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,g.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,g.jsx)(S,{children:o}),(0,g.jsx)(H,{scope:r,isInside:!0,children:(0,g.jsx)(h.bL,{id:d.contentId,role:"tooltip",children:i||o})})]})})});N.displayName=I;var z="TooltipArrow",G=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=m(r);return F(z,r).isInside?null:(0,g.jsx)(u.i3,{...o,...n,ref:t})});G.displayName=z;var U=E,W=R,X=M,Y=B,Z=N,K=G},69024:(e,t,r)=>{r.d(t,{Qg:()=>l,bL:()=>s});var n=r(43210),o=r(14163),i=r(60687),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=n.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var s=a}};