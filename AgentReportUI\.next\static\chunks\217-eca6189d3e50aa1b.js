(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[217],{30095:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i});var a=r(95155),n=r(12115),s=r(74045),l=r(35695),o=r(45786);function i(e){let{children:t,requireAuth:r=!0,redirectTo:i="/login"}=e,{isAuthenticated:c,isLoading:d,isNewUser:u,signIn:m}=(0,s.A)(),g=(0,l.useRouter)(),h=(0,l.usePathname)(),[x,p]=(0,n.useState)(!0),[f,b]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{(async()=>{if(!r||[o.bw.HOME,o.bw.LOGIN,o.bw.REGISTER,o.bw.AUTH.CALLBACK,o.bw.OAUTH.CALLBACK,o.bw.ONBOARDING].includes(h))return p(!1);if(c){if(u&&"/onboarding"!==h){console.log("New user on protected route, redirecting to onboarding"),g.push("/onboarding");return}if(!u&&"/onboarding"===h){console.log("Existing user on onboarding, redirecting to dashboard"),g.push("/dashboard");return}p(!1);return}if(!c&&!f&&!d){b(!0),console.log("Attempting automatic authentication from stored tokens...");try{await m(void 0,!1)}catch(e){console.log("Auto-authentication failed, redirecting to login"),g.push(i)}return}if(!c&&f&&!d){console.log("Not authenticated, redirecting to login"),g.push(i);return}p(!1)})()},[c,d,u,h,r,i,g,m,f]),x||d)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Checking authentication..."})]})}):r&&!c?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Authentication Required"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please sign in to access this page."}),(0,a.jsx)("button",{onClick:()=>g.push(i),className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Sign In"})]})}):(0,a.jsx)(a.Fragment,{children:t})}},32383:()=>{},41916:(e,t,r)=>{"use strict";r.d(t,{A:()=>ee});var a=r(95155),n=r(12115),s=r(10871),l=r(35695),o=r(87914),i=r(71366),c=r(84616),d=r(89613),u=r(15452);function m(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(" ")}let g=d.Kq,h=d.bL,x=d.l9,p=n.forwardRef((e,t)=>{let{className:r,sideOffset:n=4,showArrow:s=!1,...l}=e;return(0,a.jsx)(d.ZL,{children:(0,a.jsxs)(d.UC,{ref:t,sideOffset:n,className:m("relative z-50 max-w-[280px] rounded-md bg-popover text-popover-foreground px-1.5 py-1 text-xs animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...l,children:[l.children,s&&(0,a.jsx)(d.i3,{className:"-my-px fill-popover"})]})})});p.displayName=d.UC.displayName;let f=u.bL,b=u.ZL;u.l9;let y=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(u.hJ,{ref:t,className:m("fixed inset-0 z-50 bg-black/60 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...n})});y.displayName=u.hJ.displayName;let v=n.forwardRef((e,t)=>{let{className:r,children:n,...s}=e;return(0,a.jsxs)(b,{children:[(0,a.jsx)(y,{}),(0,a.jsx)(u.UC,{ref:t,className:m("fixed left-[50%] top-[50%] z-50 grid w-full max-w-[90vw] md:max-w-[800px] translate-x-[-50%] translate-y-[-50%] gap-4 border-none bg-transparent p-0 shadow-none duration-300 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",r),...s,children:(0,a.jsxs)("div",{className:"relative bg-card dark:bg-[#303030] rounded-[28px] overflow-hidden shadow-2xl p-1",children:[n,(0,a.jsxs)(u.bm,{className:"absolute right-3 top-3 z-10 rounded-full bg-background/50 dark:bg-[#303030] p-1 hover:bg-accent dark:hover:bg-[#515151] transition-all",children:[(0,a.jsx)(k,{className:"h-5 w-5 text-muted-foreground dark:text-gray-200 hover:text-foreground dark:hover:text-white"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]})});v.displayName=u.UC.displayName;let w=e=>(0,a.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,a.jsx)("path",{d:"M12 5V19",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M5 12H19",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),j=e=>(0,a.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,a.jsx)("path",{d:"M12 5.25L12 18.75",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M18.75 12L12 5.25L5.25 12",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),k=e=>(0,a.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[(0,a.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,a.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),N=n.forwardRef((e,t)=>{let{className:r,onSubmit:s,isLoading:l=!1,...o}=e,i=n.useRef(null),c=n.useRef(null),[d,u]=n.useState(""),[b,y]=n.useState(null),[N,S]=n.useState(null),[C,E]=n.useState(!1);n.useImperativeHandle(t,()=>i.current,[]),n.useLayoutEffect(()=>{let e=i.current;if(e){e.style.height="auto";let t=Math.min(e.scrollHeight,200);e.style.height="".concat(t,"px")}},[d]);let _=(d.trim().length>0||b)&&!l,D=n.useCallback(e=>{u(e.target.value),o.onChange&&o.onChange(e)},[o.onChange]),R=n.useCallback(e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),A()),o.onKeyDown&&o.onKeyDown(e)},[o.onKeyDown]),A=n.useCallback(()=>{if(!_)return;let e=d.trim();console.log("MessageInput: Submitting message",{message:e,file:b}),null==s||s(e,b||void 0),u(""),y(null),S(null),c.current&&(c.current.value="")},[d,b,_,s]),L=n.useCallback(()=>{var e;null==(e=c.current)||e.click()},[]),M=n.useCallback(e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r&&r.type.startsWith("image/")){y(r);let e=new FileReader;e.onloadend=()=>{S(e.result)},e.readAsDataURL(r)}e.target.value=""},[]),F=n.useCallback(e=>{e.stopPropagation(),y(null),S(null),c.current&&(c.current.value="")},[]);return(0,a.jsxs)("div",{className:m("flex flex-col rounded-[28px] p-2 shadow-sm transition-colors bg-white border dark:bg-[#303030] dark:border-transparent cursor-text",r),children:[(0,a.jsx)("input",{type:"file",ref:c,onChange:M,className:"hidden",accept:"image/*","aria-label":"Upload image"}),N&&(0,a.jsxs)(f,{open:C,onOpenChange:E,children:[(0,a.jsxs)("div",{className:"relative mb-1 w-fit rounded-[1rem] px-1 pt-1",children:[(0,a.jsx)("button",{type:"button",className:"transition-transform hover:scale-105",onClick:()=>E(!0),"aria-label":"View full size image",children:(0,a.jsx)("img",{src:N,alt:"Attached image preview",className:"h-14 w-14 rounded-[1rem] object-cover"})}),(0,a.jsx)("button",{onClick:F,className:"absolute right-2 top-2 z-10 flex h-4 w-4 items-center justify-center rounded-full bg-white/50 dark:bg-[#303030] text-black dark:text-white transition-colors hover:bg-accent dark:hover:bg-[#515151]","aria-label":"Remove attached image",children:(0,a.jsx)(k,{className:"h-3 w-3"})})]}),(0,a.jsx)(v,{children:(0,a.jsx)("img",{src:N,alt:"Full size attached image",className:"w-full max-h-[95vh] object-contain rounded-[24px]"})})]}),(0,a.jsx)("textarea",{ref:i,rows:1,value:d,onChange:D,onKeyDown:R,placeholder:"Message...",disabled:l,className:"w-full resize-none border-0 bg-transparent p-3 text-foreground dark:text-white placeholder:text-muted-foreground dark:placeholder:text-gray-300 focus:ring-0 focus-visible:outline-none min-h-12 disabled:opacity-50","aria-label":"Type your message",...o}),(0,a.jsx)("div",{className:"mt-0.5 p-1 pt-0",children:(0,a.jsx)(g,{delayDuration:100,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(h,{children:[(0,a.jsx)(x,{asChild:!0,children:(0,a.jsx)("button",{type:"button",onClick:L,disabled:l,className:"flex h-8 w-8 items-center justify-center rounded-full text-foreground dark:text-white transition-colors hover:bg-accent dark:hover:bg-[#515151] focus-visible:outline-none disabled:opacity-50","aria-label":"Attach image",children:(0,a.jsx)(w,{className:"h-5 w-5"})})}),(0,a.jsx)(p,{side:"top",showArrow:!0,children:(0,a.jsx)("p",{children:"Attach image"})})]}),(0,a.jsxs)(h,{children:[(0,a.jsx)(x,{asChild:!0,children:(0,a.jsx)("button",{type:"button",onClick:A,disabled:!_,className:"flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none bg-black text-white hover:bg-black/80 dark:bg-white dark:text-black dark:hover:bg-white/80 disabled:bg-black/40 dark:disabled:bg-[#515151]","aria-label":"Send message",children:(0,a.jsx)(j,{className:"h-5 w-5"})})}),(0,a.jsx)(p,{side:"top",showArrow:!0,children:(0,a.jsx)("p",{children:"Send message"})})]})]})})})]})});N.displayName="MessageInput";var S=r(76408),C=r(60760),E=r(46486);function _(e){let{placeholders:t,onChange:r,onSubmit:s,disabled:l=!1}=e,[o,i]=(0,n.useState)(0),c=(0,n.useRef)(null),d=()=>{c.current=setInterval(()=>{i(e=>(e+1)%t.length)},3e3)},u=()=>{"visible"!==document.visibilityState&&c.current?(clearInterval(c.current),c.current=null):"visible"===document.visibilityState&&d()};(0,n.useEffect)(()=>(d(),document.addEventListener("visibilitychange",u),()=>{c.current&&clearInterval(c.current),document.removeEventListener("visibilitychange",u)}),[t]);let m=(0,n.useRef)(null),g=(0,n.useRef)([]),h=(0,n.useRef)(null),[x,p]=(0,n.useState)(""),[f,b]=(0,n.useState)(!1),y=(0,n.useCallback)(()=>{if(!h.current)return;let e=m.current;if(!e)return;let t=e.getContext("2d");if(!t)return;e.width=800,e.height=800,t.clearRect(0,0,800,800);let r=getComputedStyle(h.current),a=parseFloat(r.getPropertyValue("font-size"));t.font="".concat(2*a,"px ").concat(r.fontFamily),t.fillStyle="#FFF",t.fillText(x,16,40);let n=t.getImageData(0,0,800,800).data,s=[];for(let e=0;e<800;e++){let t=4*e*800;for(let r=0;r<800;r++){let a=t+4*r;0!==n[a]&&0!==n[a+1]&&0!==n[a+2]&&s.push({x:r,y:e,color:[n[a],n[a+1],n[a+2],n[a+3]]})}}g.current=s.map(e=>{let{x:t,y:r,color:a}=e;return{x:t,y:r,r:1,color:"rgba(".concat(a[0],", ").concat(a[1],", ").concat(a[2],", ").concat(a[3],")")}})},[x]);(0,n.useEffect)(()=>{y()},[x,y]);let v=e=>{let t=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;requestAnimationFrame(()=>{var r;let a=[];for(let t=0;t<g.current.length;t++){let r=g.current[t];if(r.x<e)a.push(r);else{if(r.r<=0){r.r=0;continue}r.x+=Math.random()>.5?1:-1,r.y+=Math.random()>.5?1:-1,r.r-=.05*Math.random(),a.push(r)}}g.current=a;let n=null==(r=m.current)?void 0:r.getContext("2d");n&&(n.clearRect(e,0,800,800),g.current.forEach(t=>{let{x:r,y:a,r:s,color:l}=t;r>e&&(n.beginPath(),n.rect(r,a,s,s),n.fillStyle=l,n.strokeStyle=l,n.stroke())})),g.current.length>0?t(e-8):(p(""),b(!1))})};t(e)},w=()=>{var e;!l&&(b(!0),y(),((null==(e=h.current)?void 0:e.value)||0)&&h.current&&v(g.current.reduce((e,t)=>t.x>e?t.x:e,0)))};return(0,a.jsxs)("form",{className:(0,E.cn)("w-full relative max-w-xl mx-auto h-12 rounded-[28px] overflow-hidden transition duration-200","bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700","shadow-sm dark:shadow-gray-900/10",x&&"",l&&"opacity-50 cursor-not-allowed"),onSubmit:e=>{e.preventDefault(),!l&&(w(),s&&s(e))},children:[(0,a.jsx)("canvas",{className:(0,E.cn)("absolute pointer-events-none text-base transform scale-50 top-[20%] left-2 sm:left-8 origin-top-left pr-20",f?"opacity-100":"opacity-0"),ref:m}),(0,a.jsx)("input",{onChange:e=>{f||l||(p(e.target.value),r&&r(e))},onKeyDown:e=>{"Enter"!==e.key||f||l||w()},ref:h,value:x,type:"text",disabled:l,className:(0,E.cn)("w-full relative text-sm sm:text-base z-50 border-none bg-transparent h-full rounded-[28px] focus:outline-none focus:ring-0 pl-4 sm:pl-10 pr-20","text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400",f&&"text-transparent",l&&"cursor-not-allowed")}),(0,a.jsx)("button",{disabled:!x||l,type:"submit",className:(0,E.cn)("absolute right-2 top-1/2 z-50 -translate-y-1/2 h-8 w-8 rounded-full transition duration-200 flex items-center justify-center disabled:opacity-30",x?"bg-white dark:bg-gray-200 text-gray-800 dark:text-gray-900":"bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500"),children:(0,a.jsxs)(S.P.svg,{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:(0,E.cn)("h-4 w-4",x?"text-gray-800 dark:text-gray-900":"text-gray-400 dark:text-gray-500"),children:[(0,a.jsx)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,a.jsx)(S.P.path,{d:"M5 12l14 0",initial:{strokeDasharray:"50%",strokeDashoffset:"50%"},animate:{strokeDashoffset:x?0:"50%"},transition:{duration:.3,ease:"linear"}}),(0,a.jsx)("path",{d:"M13 18l6 -6"}),(0,a.jsx)("path",{d:"M13 6l6 6"})]})}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center rounded-full pointer-events-none",children:(0,a.jsx)(C.N,{mode:"wait",children:!x&&(0,a.jsx)(S.P.p,{initial:{y:5,opacity:0},animate:{y:0,opacity:1},exit:{y:-15,opacity:0},transition:{duration:.3,ease:"linear"},className:"text-sm sm:text-base font-normal pl-4 sm:pl-12 text-left w-[calc(100%-2rem)] truncate text-gray-500 dark:text-gray-400",children:t[o]},"current-placeholder-".concat(o))})})]})}function D(e){let{onSendMessage:t}=e,[r,s]=(0,n.useState)(!1),l=(0,n.useRef)(0),i=(0,n.useMemo)(()=>({title:"New Chat",icon:c.A}),[]);return(0,o.H)(i),(0,a.jsx)("div",{className:"fixed inset-0 flex flex-col overflow-hidden overflow-x-hidden z-50 bg-sidebar-bg",style:{left:"var(--sidebar-width)",top:"var(--header-height)"},children:(0,a.jsx)("div",{className:"flex flex-1 items-center justify-center min-h-0 @lg/thread:items-end",children:(0,a.jsxs)("div",{className:"max-w-2xl w-full mx-auto px-6",children:[(0,a.jsx)("div",{className:"text-center mb-8",children:(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-sidebar-text-primary text-[28px] leading-[34px] font-semibold tracking-[0.38px] mb-3 motion-safe:transition-all duration-200 inline-flex min-h-10.5 items-baseline whitespace-pre-wrap opacity-100",children:"Ready when you are."}),r&&(0,a.jsx)("p",{className:"text-sidebar-text-secondary text-[15px] leading-[18px] tracking-[-0.23px] motion-safe:transition-opacity duration-200",children:"Sending your message..."})]})}),(0,a.jsx)("div",{className:"mb-6 motion-safe:transition-all duration-200",children:(0,a.jsx)(_,{placeholders:["What would you like to know about your data?","Ask me to analyze your database records","Need help with data insights? Ask me anything","What trends should we explore in your data?","Looking for specific metrics or KPIs? Just ask"],onChange:e=>{},onSubmit:e=>{e.preventDefault();let a=Date.now();if(a-l.current<1e3)return void console.log("Rate limit exceeded, ignoring submission");if(r)return void console.log("Message already being sent, ignoring submission");let n=e.currentTarget.querySelector("input");if(n&&n.value.trim()){s(!0),l.current=a;try{t(n.value.trim()),n.value=""}catch(e){console.error("Error sending message:",e),s(!1)}setTimeout(()=>{s(!1)},2e3)}},disabled:r})})]})})})}var R=r(45786);class A{async startStreaming(e){let{sessionId:t,query:r,outputFormat:a="excel",onTokenReceived:n,onComplete:s,onError:l}=e;this.accumulatedContent="",this.isCompleted=!1,this.cleanup();let o=localStorage.getItem(R.d5.ACCESS_TOKEN);if(!o){null==l||l("No authentication token found");return}try{let e=(0,R.hY)(),i=new FormData;i.append("query",r),i.append("output_format",a),i.append("session_id",t),i.append("enable_token_streaming","true"),console.log("\uD83D\uDD17 Making POST request to:","".concat(e,"/ask/question")),console.log("\uD83D\uDCE4 Request data (FormData):"),console.log("  - query:",r),console.log("  - output_format:",a),console.log("  - session_id:",t),console.log("  - enable_token_streaming: true");let c=await fetch("".concat(e,"/ask/question"),{method:"POST",headers:{Authorization:"Bearer ".concat(o)},body:i});if(console.log("\uD83D\uDCE5 Response status:",c.status),console.log("\uD83D\uDCE5 Response headers:",Object.fromEntries(c.headers.entries())),!c.ok){let e="";try{let t=await c.text();console.error("❌ Error response body:",t),e=" - ".concat(t)}catch(e){console.error("❌ Could not read error response body")}throw Error("Failed to initiate streaming: ".concat(c.status," ").concat(c.statusText).concat(e))}let d=c.headers.get("content-type");if(console.log("\uD83D\uDCCB Response content-type:",d),null==d?void 0:d.includes("text/event-stream"))console.log("\uD83D\uDD04 Processing SSE stream from POST response..."),this.handleSSEResponse(c,n,s,l);else{let e=await c.json();if(console.log("\uD83D\uDCC4 JSON response received:",e),e.streaming_url)this.connectToSSE(e.streaming_url,n,s,l);else{let t=e.summary||e.message||e.content||"No response available";null==s||s(t,e)}}}catch(t){console.error("Error starting streaming connection:",t);let e=t instanceof Error?t.message:"Failed to start streaming connection";null==l||l(e)}}async handleSSEResponse(e,t,r,a){if(!e.body){null==a||a("No response body available for streaming");return}let n=e.body.getReader(),s=new TextDecoder;try{for(this.isConnected=!0,console.log("✅ SSE stream connected");;){let{done:e,value:l}=await n.read();if(e){console.log("\uD83D\uDCE1 SSE stream ended");break}let o=s.decode(l,{stream:!0});this.processSSEChunk(o,t,r,a)}}catch(e){console.error("❌ Error reading SSE stream:",e),null==a||a("Error reading streaming response")}finally{n.releaseLock(),this.isConnected=!1}}connectToSSE(e,t,r,a){console.log("\uD83D\uDD17 Connecting to SSE URL:",e),this.eventSource=new EventSource(e),this.eventSource.onopen=()=>{console.log("✅ SSE connection opened"),this.isConnected=!0},this.eventSource.onmessage=e=>{this.processSSEEvent(e.data,t,r,a)},this.eventSource.onerror=e=>{var t;console.error("❌ SSE connection error:",e),this.isConnected=!1,(null==(t=this.eventSource)?void 0:t.readyState)===EventSource.CLOSED&&(null==a||a("Connection to server lost"),this.cleanup())}}processSSEChunk(e,t,r,a){for(let n of e.split("\n"))if(n.startsWith("data: ")){let e=n.substring(6);this.processSSEEvent(e,t,r,a)}}processSSEEvent(e,t,r,a){try{var n,s,l,o;let i=JSON.parse(e);if(console.log("\uD83D\uDCE8 SSE event received:",i),this.isCompleted&&["token_complete","conversation_complete"].includes(i.type))return void console.log("⏭️ Skipping duplicate completion event:",i.type);switch(i.type){case"token_stream":let c=i.token;console.log("\uD83D\uDD0D Raw token received:",c),c&&"string"==typeof c&&(console.log('⏰ Processing token: "'.concat(c,'"')),this.accumulatedContent+=c,null==t||t(c));break;case"token_complete":let d=(null==(n=i.data)?void 0:n.complete_response)||(null==(s=i.data)?void 0:s.message),u=i.data;console.log("\uD83C\uDFAF Processing token_complete event"),console.log("Complete response:",d),console.log("Full response data:",u),!this.isCompleted&&d&&(console.log("✅ Using complete response from token_complete"),this.isCompleted=!0,null==r||r(d,u),this.cleanup());break;case"conversation_complete":let m=null==(l=i.data)?void 0:l.message;console.log("\uD83C\uDFAF Processing conversation_complete event"),console.log("Final message:",m),this.isCompleted||(m?(console.log("✅ Using final message from conversation_complete"),this.isCompleted=!0,null==r||r(m)):this.accumulatedContent&&(console.log("✅ Using accumulated content from tokens"),this.isCompleted=!0,null==r||r(this.accumulatedContent)),this.cleanup());break;case"error":let g=(null==(o=i.data)?void 0:o.error)||i.error||"Unknown streaming error";null==a||a(g),this.cleanup();break;default:["agent_status","agent_result"].includes(i.type)||console.warn("Unknown SSE event type:",i.type)}}catch(t){console.error("Error parsing SSE event:",t),console.error("Raw event data:",e)}}stopStreaming(){this.cleanup()}extractCleanToken(e){if(console.log('\uD83D\uDD0D Extracting clean token from: "'.concat(e,'"')),['{\n  "','\n  "message','message": "','",\n}','."\n}','{\n  "message": "','"',"\n","  ","{","}",'": "'].some(t=>e===t))return console.log('⏭️ Skipping JSON structure token: "'.concat(e,'"')),null;if(e.includes('": "')){let t=e.match(/": "(.+)/),r=t?t[1]:null;return console.log('\uD83D\uDCDD Extracted from JSON key-value: "'.concat(r,'"')),r}if(e.endsWith('",')||e.endsWith('."\n}')||e.endsWith('"')){let t=e.replace(/[",\n}]+$/,"");return console.log('\uD83E\uDDF9 Cleaned trailing JSON: "'.concat(t,'"')),t}if(e.startsWith('{\n  "')||e.startsWith('"')){let t=e.replace(/^[{\n\s"]+/,"");return console.log('\uD83E\uDDF9 Cleaned leading JSON: "'.concat(t,'"')),t||null}return console.log('✅ Clean content token: "'.concat(e,'"')),e}cleanup(){this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.isConnected=!1,this.isCompleted=!1}getConnectionStatus(){return this.isConnected}getAccumulatedContent(){return this.accumulatedContent}constructor(){this.eventSource=null,this.accumulatedContent="",this.isConnected=!1,this.isCompleted=!1,this.cleanup=this.cleanup.bind(this)}}let L=new A;var M=r(56454),F=r(84823),T=r(75006);let I=e=>{if(!e)return"";let t=e;return(t=(t=(t=(t=(t=(t=(t=(t=t.split("###").map(e=>e.trim()).filter(Boolean).map((e,t)=>{let r=e.trim(),a=r.match(/^([🏆📊📋💡🔍📈📉💰🎯⚡🚀💎🔥⭐🎉🎊🏅🎖️💹💲💸💵💴💶💷🔢📝📄📃📑🔎📖📚📓📕📗📘📙📒📰🗞️])\s*([\s\S]+)$/);if(a){let[,e,t]=a,n=t.split("\n"),s=n[0].trim(),l=n.slice(1).join("\n").trim();r=l?"## ".concat(e," ").concat(s,"\n\n").concat(l):"## ".concat(e," ").concat(s)}return r}).join("\n\n")).replace(/•\s*/g,"- ")).replace(/(\d+\.\s*\*\*[^*]+\*\*)/g,"\n$1")).replace(/(:)\s*(-\s+)/g,"$1\n\n$2")).replace(/^([^|\n#]+?)\s+\|\s+(.+)$/gm,"### $1\n\n| $2")).replace(/\s-\s\*\*/g,"\n- **")).replace(/\n{3,}/g,"\n\n")).replace(/^\n+/,"")).trim()},O=(()=>{let e="",t="";return function(r){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(r===e)return t;if(!a||r.length<100){let a=I(r);return e=r,t=a,a}if(50>Math.abs(r.length-e.length)&&a)return t+r.slice(e.length);let n=I(r);return e=r,t=n,n}})(),q=n.memo(e=>{let{content:t,className:r="",isStreaming:s=!1}=e,l=(0,n.useRef)(""),o=(0,n.useRef)("");(0,n.useRef)("");let i=(0,n.useRef)(0),c=(0,n.useMemo)(()=>{if(i.current++,s&&i.current>10&&t===l.current||t===l.current)return o.current;let e=O(t,s);return(!s||t.length-l.current.length>100)&&(l.current=t,o.current=e),e},[t,s]);(0,n.useMemo)(()=>{},[t,c,s]);let d=(0,n.useRef)(0);(0,n.useMemo)(()=>{Math.abs(t.length-d.current)>200&&(i.current=0,d.current=t.length)},[t.length]);let u=(0,n.useMemo)(()=>(0,a.jsx)(M.oz,{remarkPlugins:[F.A],rehypePlugins:[T.A],components:{h1:e=>{let{children:t}=e;return(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2 mb-4 mt-6",children:t})},h2:e=>{let{children:t}=e;return(0,a.jsx)("h2",{className:"text-xl font-semibold text-blue-600 dark:text-blue-400 border-b border-gray-200 dark:border-gray-700 pb-2 mb-4 mt-6",children:t})},h3:e=>{let{children:t}=e;return(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-700 dark:text-gray-300 mb-3 mt-5 border-l-4 border-blue-300 dark:border-blue-600 pl-3",children:t})},h4:e=>{let{children:t}=e;return(0,a.jsx)("h4",{className:"text-base font-medium text-gray-700 dark:text-gray-300 mb-2 mt-2",children:t})},h5:e=>{let{children:t}=e;return(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 mt-2",children:t})},h6:e=>{let{children:t}=e;return(0,a.jsx)("h6",{className:"text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 mt-2",children:t})},strong:e=>{let{children:t}=e;return(0,a.jsx)("strong",{className:"font-semibold text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 px-1 py-0.5 rounded",children:t})},em:e=>{let{children:t}=e;return(0,a.jsx)("em",{className:"italic text-gray-800 dark:text-gray-200",children:t})},ul:e=>{let{children:t}=e;return(0,a.jsx)("ul",{className:"list-disc pl-6 my-4 space-y-2 text-gray-800 dark:text-gray-200",children:t})},ol:e=>{let{children:t}=e;return(0,a.jsx)("ol",{className:"list-decimal pl-6 my-4 space-y-2 text-gray-800 dark:text-gray-200",children:t})},li:e=>{let{children:t}=e;return(0,a.jsx)("li",{className:"leading-relaxed py-0.5 text-sm",children:t})},p:e=>{let{children:t}=e;return(0,a.jsx)("p",{className:"mb-4 leading-relaxed text-gray-800 dark:text-gray-200 text-sm",children:t})},code:e=>{let{children:t,inline:r}=e;return r?(0,a.jsx)("code",{className:"bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono text-gray-900 dark:text-gray-100",children:t}):(0,a.jsx)("pre",{className:"bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-3 my-3 overflow-x-auto",children:(0,a.jsx)("code",{className:"text-sm font-mono text-gray-900 dark:text-gray-100",children:t})})},table:e=>{let{children:t}=e;return(0,a.jsx)("div",{className:"overflow-x-auto my-3",children:(0,a.jsx)("table",{className:"w-full border-collapse border border-gray-300 dark:border-gray-600",children:t})})},th:e=>{let{children:t}=e;return(0,a.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 px-3 py-2 text-left font-semibold text-gray-900 dark:text-gray-100",children:t})},td:e=>{let{children:t}=e;return(0,a.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-800 dark:text-gray-200",children:t})},blockquote:e=>{let{children:t}=e;return(0,a.jsx)("blockquote",{className:"border-l-4 border-blue-500 dark:border-blue-400 pl-4 my-3 italic text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 py-2 rounded-r",children:t})},a:e=>{let{children:t,href:r}=e;return(0,a.jsx)("a",{href:r,className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline",target:"_blank",rel:"noopener noreferrer",children:t})},hr:()=>(0,a.jsx)("hr",{className:"my-4 border-gray-300 dark:border-gray-600"})},children:c||"No content available"}),[c]);return(0,a.jsx)("div",{className:(0,E.cn)("markdown-content",r),children:u})});q.displayName="MarkdownRenderer";let P=e=>({hasExecutiveSummary:/## Executive Summary/i.test(e),hasKeyInsights:/### Key Insights/i.test(e),hasBusinessImplications:/### Business Implications/i.test(e),hasRecommendations:/### Recommendation/i.test(e),hasStatisticalOverview:/### Statistical Overview/i.test(e),hasNumberedResults:/^\d+\.\s+\*\*/.test(e.replace(/\n/g," ")),hasBulletPoints:/^\*\s+\*\*/.test(e.replace(/\n/g," "))}),z=e=>{if(!e)return"";let t=e,r=P(e);return r.hasExecutiveSummary&&(t=t.replace(/## Executive Summary/gi,"## \uD83D\uDCCA Executive Summary")),r.hasKeyInsights&&(t=t.replace(/### Key Insights:/gi,"### \uD83D\uDCA1 Key Insights:")),r.hasBusinessImplications&&(t=t.replace(/### Business Implications:/gi,"### \uD83C\uDFAF Business Implications:")),r.hasRecommendations&&(t=t.replace(/### Recommendation:/gi,"### \uD83D\uDE80 Recommendation:")),r.hasStatisticalOverview&&(t=t.replace(/### Statistical Overview/gi,"### \uD83D\uDCC8 Statistical Overview")),t},B=n.memo(e=>{let{content:t,className:r="",isStreaming:s=!1}=e,l=(0,n.useMemo)(()=>P(t),[t]),o=(0,n.useMemo)(()=>z(t),[t]),i=(0,n.useMemo)(()=>Object.values(l).some(Boolean),[l]);return(0,a.jsx)("div",{className:(0,E.cn)("analytical-content",r),children:(0,a.jsx)(q,{content:o,isStreaming:s,className:(0,E.cn)(i&&"analytical-markdown",r)})})});B.displayName="AnalyticalContentRenderer";let W={speed:0,enableCursor:!0,cursorChar:"|"},K=n.memo(e=>{let{content:t,isStreaming:r,className:s="",typewriterConfig:l={}}=e,[o,i]=(0,n.useState)(""),[c,d]=(0,n.useState)(!1),u=(0,n.useRef)(null),[m,g]=(0,n.useState)(0),h=(0,n.useMemo)(()=>({...W,...l}),[l]);(0,n.useEffect)(()=>{if(0===h.speed)i(t),g(t.length);else{if(!t){i(""),g(0);return}if(t.length<o.length){i(t),g(t.length);return}if(o===t&&!r)return;let e=o.length,a=setInterval(()=>{e<t.length?(e++,i(t.slice(0,e)),g(e)):clearInterval(a)},h.speed);return()=>clearInterval(a)}},[t,h.speed,r,o.length]);let x=(0,n.useMemo)(()=>(0,a.jsx)(B,{content:o,isStreaming:r,className:"inline"}),[o,r]);return(0,n.useEffect)(()=>{if(!h.enableCursor||!r){d(!1),u.current&&(clearInterval(u.current),u.current=null);return}return d(!0),u.current=setInterval(()=>{d(e=>!e)},600),()=>{u.current&&(clearInterval(u.current),u.current=null)}},[r,h.enableCursor]),(0,n.useEffect)(()=>()=>{u.current&&clearInterval(u.current)},[]),(0,a.jsx)("div",{className:"streaming-message ".concat(s),children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"text-sm",children:[x,r&&h.enableCursor&&(0,a.jsx)("span",{className:"inline-block ml-0.5 text-blue-500 font-bold transition-opacity duration-150 ".concat(c?"opacity-100":"opacity-0"),style:{width:"2px"},children:h.cursorChar})]}),r&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-50/20 to-transparent rounded pointer-events-none animate-pulse"})]})})});K.displayName="StreamingMessage";var U=r(30285),H=r(65112),$=r(91788),J=r(33786),Y=r(51154),Q=r(85339),V=r(85127),X=r(3925);let G=e=>{let{outputFiles:t,className:r=""}=e,[s,l]=(0,n.useState)(null),[o,i]=(0,n.useState)(!1),[c,d]=(0,n.useState)(null),[u,m]=(0,n.useState)(!0),g=e=>{let t=e.trim().split("\n");if(0===t.length)return{headers:[],rows:[]};let r=e=>{let t=[],r="",a=!1;for(let n=0;n<e.length;n++){let s=e[n];'"'===s?a&&'"'===e[n+1]?(r+='"',n++):a=!a:","!==s||a?r+=s:(t.push(r.trim()),r="")}return t.push(r.trim()),t};return{headers:r(t[0]),rows:t.slice(1).map(e=>r(e))}},h=e=>{try{let t=X.LF(e,{type:"array"}),r=t.SheetNames[0],a=t.Sheets[r],n=X.Wp.sheet_to_json(a,{header:1});if(0===n.length)return{headers:[],rows:[]};let s=n[0]||[],l=n.slice(1);return{headers:s,rows:l}}catch(e){throw console.error("Error parsing Excel file:",e),Error("Failed to parse Excel file")}},x=async(e,t,r)=>{i(!0),d(null);try{let a,n=await fetch(e);if(!n.ok)throw Error("Failed to fetch data: ".concat(n.status," ").concat(n.statusText));if("csv"===t.toLowerCase()){let e=await n.text();a=g(e)}else if("xlsx"===t.toLowerCase()||"xls"===t.toLowerCase()){let e=await n.arrayBuffer();a=h(e)}else throw Error("Unsupported file format: ".concat(t));l({headers:a.headers,rows:a.rows,fileName:r,format:t})}catch(e){console.error("Failed to fetch file data:",e),d(e instanceof Error?e.message:"Failed to load data")}finally{i(!1)}};(0,n.useEffect)(()=>{if(t&&t.length>0){let e=t[0],r=e.file_path.split("/").pop()||"data.".concat(e.format);x(e.file_path,e.format,r)}},[t]);let p=(e,t)=>{let r=document.createElement("a");r.href=e,r.download=t,r.target="_blank",document.body.appendChild(r),r.click(),document.body.removeChild(r)};if(!t||0===t.length)return null;let f=t[0],b=f.file_path.split("/").pop()||"data.".concat(f.format);return(0,a.jsxs)("div",{className:"border border-blue-200 dark:border-blue-800 rounded-lg bg-blue-50/30 dark:bg-blue-900/10 p-4 mt-4 ".concat(r),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,a.jsx)(H.A,{className:"w-4 h-4 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-sm text-blue-900 dark:text-blue-100",children:"Query Results Data"}),(0,a.jsxs)("p",{className:"text-xs text-blue-700 dark:text-blue-300",children:[f.database_name," • ",f.format.toUpperCase()," • ",(null==s?void 0:s.rows.length)||0," rows"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(U.$,{onClick:()=>p(f.file_path,b),size:"sm",variant:"outline",className:"text-xs",children:[(0,a.jsx)($.A,{className:"w-3 h-3 mr-1"}),"Download"]}),(0,a.jsxs)(U.$,{onClick:()=>window.open(f.file_path,"_blank"),size:"sm",variant:"outline",className:"text-xs",children:[(0,a.jsx)(J.A,{className:"w-3 h-3 mr-1"}),"Open"]})]})]}),o&&(0,a.jsxs)("div",{className:"flex items-center justify-center py-8 text-gray-600 dark:text-gray-400",children:[(0,a.jsx)(Y.A,{className:"w-4 h-4 animate-spin mr-2"}),(0,a.jsx)("span",{className:"text-sm",children:"Loading table data..."})]}),c&&!o&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-600 dark:text-red-400 text-sm mb-3 p-3 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800",children:[(0,a.jsx)(Q.A,{className:"w-4 h-4 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Failed to load table data"}),(0,a.jsx)("p",{className:"text-xs mt-1",children:c})]})]}),u&&s&&!c&&!o&&(0,a.jsxs)("div",{className:"max-h-96 overflow-auto border border-blue-200 dark:border-blue-700 rounded-lg bg-white dark:bg-gray-900",children:[(0,a.jsxs)(V.XI,{children:[(0,a.jsxs)(V.r6,{className:"text-xs text-blue-600 dark:text-blue-400 font-medium py-2",children:["Showing ",Math.min(s.rows.length,100)," of ",s.rows.length," rows • ",s.fileName]}),(0,a.jsx)(V.A0,{children:(0,a.jsx)(V.Hj,{children:s.headers.map((e,t)=>(0,a.jsx)(V.nd,{className:"text-xs font-medium",children:e||"Column ".concat(t+1)},t))})}),(0,a.jsx)(V.BF,{children:s.rows.slice(0,100).map((e,t)=>(0,a.jsx)(V.Hj,{children:s.headers.map((t,r)=>(0,a.jsx)(V.nA,{className:"text-xs",children:e[r]||""},r))},t))})]}),s.rows.length>100&&(0,a.jsxs)("div",{className:"p-2 text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 border-t",children:["Showing first 100 rows of ",s.rows.length," total rows. Download the full file to see all data."]})]}),s&&!o&&(0,a.jsx)("div",{className:"mt-2 flex justify-center",children:(0,a.jsx)(U.$,{onClick:()=>m(!u),size:"sm",variant:"ghost",className:"text-xs",children:u?"Collapse Table":"Expand Table"})}),!u&&s&&!o&&(0,a.jsxs)("div",{className:"text-center py-4 text-gray-600 dark:text-gray-400",children:[(0,a.jsx)(H.A,{className:"w-6 h-6 mx-auto mb-2 opacity-50"}),(0,a.jsxs)("p",{className:"text-sm",children:["Table with ",s.rows.length," rows and ",s.headers.length," columns"]}),(0,a.jsx)("p",{className:"text-xs mt-1",children:'Click "Expand Table" to view data'})]})]})},Z=new Set,ee=e=>{let{chatId:t}=e,[r,d]=(0,n.useState)(!1),[u,m]=(0,n.useState)([]),[g,h]=(0,n.useState)(null),[x,p]=(0,n.useState)(null),[f,b]=(0,n.useState)(!1),[y,v]=(0,n.useState)(!1),w=(0,n.useRef)(null),j=(0,n.useRef)([]),k=(0,n.useRef)(null),S=(0,n.useRef)(0),C=(0,n.useRef)(!1),E=(0,n.useRef)(!1),_=(0,n.useRef)(null),{chatHistory:R,activeChat:A,chatMessages:M,isLoadingHistory:F,isLoadingChats:T,pendingFirstMessage:I,setPendingFirstMessage:O,setActiveChat:q,addMessageToChat:P,addChat:z,refreshChatList:W}=(0,s.m)(),U=(0,l.useRouter)(),H=A&&M[A.session_id]||[],$=(0,n.useMemo)(()=>({title:A?"Data Chat ".concat(A.id.startsWith("chat_sess_")?A.id.slice(9,17):A.id.slice(0,8)):"New Chat",icon:A?i.A:c.A}),[A]);(0,o.H)($),(0,n.useEffect)(()=>{if(console.log("Chat component useEffect triggered with chatId:",t),console.log("Current activeChat:",null==A?void 0:A.id),console.log("Chat history loaded:",R.length>0),console.log("Is loading chats:",T),!t){A&&(console.log("Clearing active chat for new chat scenario"),q(null));return}if(A&&A.id===t)return void console.log("Chat is already active, no action needed");b(!1);let e=R.find(e=>e.id===t);if(e){console.log("Found chat in local history, setting as active:",e.id),q(e);return}return T?void console.log("Still loading chats, waiting..."):0===R.length?void console.log("Chat history not loaded yet, waiting for backend data..."):void(console.log("Chat not found in loaded history, marking as not found:",t),b(!0))},[t,null==A?void 0:A.id,T,R.length]),(0,n.useEffect)(()=>{if(!E.current&&(console.log("\uD83D\uDD0D pendingFirstMessage useEffect triggered:",{pendingFirstMessage:!!I,activeChat:!!A,activeChatId:null==A?void 0:A.id,chatId:t,isLoading:r,chatIdMatches:(null==A?void 0:A.id)===t}),I&&A&&t&&A.id===t&&!Z.has(t))){E.current=!0,Z.add(t),console.log("✅ Processing pending first message after navigation:",I),d(!0),console.log("\uD83D\uDD04 pendingFirstMessage: Set isLoading to true"),h(null),p("welcome_stream_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11))),_.current=null,O(null);let e=!1;(async()=>{try{await L.startStreaming({sessionId:A.session_id,query:I,outputFormat:"excel",onTokenReceived:e=>{console.log("\uD83D\uDD24 Welcome message token received:",e),d(!1),e&&"string"==typeof e&&X(e)},onComplete:async(t,r)=>{d(!1);let a="".concat(t,"_").concat(Date.now());if(e||_.current===a)return void console.log("⏭️ Skipping duplicate onComplete call");e=!0,_.current=a,console.log("✅ Welcome message streaming complete:",t),console.log("\uD83D\uDCCA Complete response data:",r),k.current&&(clearTimeout(k.current),k.current=null),j.current=[];let n={role:"agent",content:t,timestamp:new Date,isStreaming:!1,outputFiles:(null==r?void 0:r.output_files)||[],sqlQueries:(null==r?void 0:r.sql_queries)||{}};P(A.session_id,n),h(null),p(null),setTimeout(async()=>{await W()},500)},onError:e=>{if(console.error("Streaming failed:",e),d(!1),k.current&&(clearTimeout(k.current),k.current=null),j.current=[],A){let e={role:"agent",content:"Sorry, there was an error processing your request. Please try again.",timestamp:new Date};P(A.session_id,e)}h(null),p(null)}})}catch(e){if(console.error("Failed to start streaming for first message:",e),d(!1),k.current&&(clearTimeout(k.current),k.current=null),j.current=[],A){let e={role:"agent",content:"Sorry, there was an error processing your request. Please try again.",timestamp:new Date};P(A.session_id,e)}h(null),p(null)}})()}},[I,A,t]),(0,n.useEffect)(()=>{E.current=!1},[t]),(0,n.useEffect)(()=>()=>{L.stopStreaming(),k.current&&clearTimeout(k.current)},[]);let J=()=>{var e;null==(e=w.current)||e.scrollIntoView({behavior:"smooth"})},Y={TOKEN_DELAY:400,MIN_DELAY:200,MAX_DELAY:600},Q=e=>{let t=Y.TOKEN_DELAY,r=e.trim().length;return r<=2?Y.MIN_DELAY:r>8?Y.MAX_DELAY:t},V=()=>{if(0===j.current.length){k.current&&(clearTimeout(k.current),k.current=null);return}let e=j.current.shift();e&&e.trim()?(h(t=>{if(!t)return{role:"agent",content:e,timestamp:new Date,isStreaming:!0};let r=t.content+e;return r===t.content?t:{...t,content:r}}),j.current.length>0?k.current=setTimeout(V,Q(e)):k.current&&(clearTimeout(k.current),k.current=null)):j.current.length>0&&(k.current=setTimeout(V,50))},X=e=>{if(!e||!e.trim())return;let t=Date.now();!(t-(j.current.length>0&&j.current.lastAddTime||0)<10)&&(j.current.push(e),j.current.lastAddTime=t,k.current||V())};(0,n.useEffect)(()=>{J()},[H,g]);let ee=(0,n.useCallback)(async(e,t)=>{if(console.log("Chat: Message submitted",{message:e,file:t}),!e.trim()&&!t)return void console.log("Chat: No message or file provided, skipping submit");if(!A)return void console.error("Chat: Cannot send message - no active chat");if(C.current||y)return void console.log("Chat: Message already being sent, ignoring duplicate request");let r=Date.now();if(r-S.current<1e3)return void console.log("Chat: Rate limit exceeded, ignoring message send");C.current=!0,v(!0),S.current=r;try{t&&(console.log("Chat: Processing attached file",t.name),m(e=>[...e,{name:t.name,type:t.type}]));let r={role:"user",content:e.trim(),timestamp:new Date};if(P(A.session_id,r),d(!0),g&&g.content.trim()){console.log("Chat: Saving previous streaming message to history");let e={role:"agent",content:g.content,timestamp:new Date,isStreaming:!1,outputFiles:g.outputFiles||[],sqlQueries:g.sqlQueries||{}};P(A.session_id,e)}h(null);let a="stream_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11));p(a),_.current=null;let n=!1;console.log("Chat: Starting streaming for session:",A.session_id),await L.startStreaming({sessionId:A.session_id,query:e.trim(),outputFormat:"excel",onTokenReceived:e=>{e&&"string"==typeof e&&(d(!1),X(e))},onComplete:(e,t)=>{console.log("Chat: Streaming complete",{finalContent:e,outputFiles:null==t?void 0:t.output_files}),d(!1);let r="".concat(e,"_").concat(Date.now());if(n||_.current===r)return void console.log("Chat: Skipping duplicate onComplete call");n=!0,_.current=r,k.current&&(clearTimeout(k.current),k.current=null),j.current=[];let a={role:"agent",content:e,timestamp:new Date,isStreaming:!1,outputFiles:(null==t?void 0:t.output_files)||[],sqlQueries:(null==t?void 0:t.sql_queries)||{}};P(A.session_id,a),h(null),p(null)},onError:e=>{console.error("Chat: Streaming failed:",e),d(!1),k.current&&(clearTimeout(k.current),k.current=null),j.current=[];let t={role:"agent",content:"Sorry, there was an error processing your request. Please try again.",timestamp:new Date};P(A.session_id,t),h(null),p(null)}})}catch(e){if(console.error("Chat: Error in message submission:",e),d(!1),A){let e={role:"agent",content:"Sorry, there was an error processing your request. Please try again.",timestamp:new Date};P(A.session_id,e)}}finally{setTimeout(()=>{v(!1),C.current=!1},1500)}},[A,y,g,P,X]);return(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full w-full md:p-6 bg-sidebar-bg overflow-y-auto",onDragOver:e=>{e.preventDefault(),e.stopPropagation()},onDrop:e=>{var t;e.preventDefault(),e.stopPropagation();let r=null==(t=e.dataTransfer.files)?void 0:t[0];r&&m(e=>[...e,{name:r.name,type:r.type}])},children:[!A&&!t&&(0,a.jsx)(D,{onSendMessage:async e=>{console.log("Sending first message to create new chat:",e);let t=z();await q(t);let r={role:"user",content:e,timestamp:new Date};P(t.session_id,r),d(!0),console.log("\uD83D\uDD04 WelcomeMessage: Set isLoading to true"),h(null),p("welcome_stream_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11))),U.replace("/chat/".concat(t.id)),console.log("\uD83D\uDD04 WelcomeMessage: Navigated to chat, setting pending message"),O(e)}}),(0,a.jsxs)("div",{className:"w-full max-w-3xl flex flex-col flex-1 h-full",children:[(0,a.jsxs)("div",{className:"flex-grow space-y-8 pr-2 relative",children:[(A&&F||t&&!A&&(T||!f))&&(0,a.jsx)("div",{className:"flex flex-1 items-center justify-center min-h-[400px] h-full w-full",children:(0,a.jsx)("div",{className:"max-w-md w-full text-center mx-auto",children:(0,a.jsxs)("div",{className:"mb-6 p-6 rounded-lg bg-blue-50 dark:bg-blue-900 text-blue-900 dark:text-blue-100 border border-blue-200 dark:border-blue-700 shadow flex flex-col items-center justify-center text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-3"}),(0,a.jsx)("h3",{className:"text-lg font-bold mb-2",children:A&&F?"Loading conversation...":"Loading chat..."}),(0,a.jsx)("p",{className:"text-sm",children:A&&F?"Fetching your chat history from the server.":"Finding your chat and loading the conversation."})]})})}),t&&f&&!T&&(0,a.jsx)("div",{className:"flex flex-1 items-center justify-center min-h-[400px] h-full w-full",children:(0,a.jsx)("div",{className:"max-w-md w-full text-center mx-auto",children:(0,a.jsxs)("div",{className:"mb-6 p-6 rounded-lg bg-red-50 dark:bg-red-900 text-red-900 dark:text-red-100 border border-red-200 dark:border-red-700 shadow flex flex-col items-center justify-center text-center",children:[(0,a.jsx)("div",{className:"text-red-500 mb-3",children:(0,a.jsx)("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,a.jsx)("h3",{className:"text-lg font-bold mb-2",children:"Chat Not Found"}),(0,a.jsx)("p",{className:"text-sm mb-4",children:"The chat you're looking for doesn't exist or may have been deleted."}),(0,a.jsx)("button",{onClick:()=>U.push("/chat"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Start New Chat"})]})})}),A&&!F&&H.map((e,t)=>(0,a.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,a.jsxs)("div",{className:"max-w-[85%] px-5 py-2.5 ".concat("user"===e.role?"bg-gray-200/50 dark:bg-gray-700/50 rounded-3xl text-gray-900 dark:text-gray-100":"text-gray-900 dark:text-gray-100"),children:["user"===e.role?(0,a.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:e.content}):(0,a.jsx)(B,{content:e.content,isStreaming:!1,className:"text-sm"}),"agent"===e.role&&e.outputFiles&&e.outputFiles.length>0&&(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsx)(G,{outputFiles:e.outputFiles})})]})},t)),g&&A&&(0,a.jsx)("div",{className:"flex justify-start",children:(0,a.jsxs)("div",{className:"max-w-[85%] px-5 py-2.5 text-gray-900 dark:text-gray-100",children:[(0,a.jsx)(K,{content:g.content,isStreaming:g.isStreaming||!1,className:"text-gray-900 dark:text-gray-100"}),g.outputFiles&&g.outputFiles.length>0&&(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsx)(G,{outputFiles:g.outputFiles})})]})}),r&&A&&!g&&(0,a.jsx)("div",{className:"flex justify-start mb-4 animate-in fade-in duration-300",children:(0,a.jsx)("div",{className:"max-w-[70%] px-4 py-3 rounded-2xl bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 shadow-sm border border-gray-100 dark:border-gray-600",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0ms",animationDuration:"1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"150ms",animationDuration:"1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"300ms",animationDuration:"1s"}})]}),(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-300 font-medium",children:"AI is thinking..."})]})})}),(0,a.jsx)("div",{ref:w})]}),(A||t&&!f)&&(0,a.jsx)("div",{className:"mt-6 border-t border-gray-200 dark:border-gray-700 pt-6",children:(0,a.jsx)(N,{onSubmit:ee,placeholder:A?"Message...":"Loading chat...",disabled:!A||r||y,isLoading:r||y,className:"w-full max-w-full"})})]})]})}},83686:()=>{},85127:(e,t,r)=>{"use strict";r.d(t,{A0:()=>o,BF:()=>i,Hj:()=>c,XI:()=>l,nA:()=>u,nd:()=>d,r6:()=>m});var a=r(95155),n=r(12115),s=r(46486);let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,s.cn)("w-full caption-bottom text-sm",r),...n})})});l.displayName="Table";let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("thead",{ref:t,className:(0,s.cn)("[&_tr]:border-b",r),...n})});o.displayName="TableHeader";let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,s.cn)("[&_tr:last-child]:border-0",r),...n})});i.displayName="TableBody",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,s.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...n})}).displayName="TableFooter";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("tr",{ref:t,className:(0,s.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...n})});c.displayName="TableRow";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("th",{ref:t,className:(0,s.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...n})});d.displayName="TableHead";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("td",{ref:t,className:(0,s.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...n})});u.displayName="TableCell";let m=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("caption",{ref:t,className:(0,s.cn)("mt-4 text-sm text-muted-foreground",r),...n})});m.displayName="TableCaption"}}]);