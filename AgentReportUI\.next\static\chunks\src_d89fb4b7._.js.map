{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/cn.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/date.ts"], "sourcesContent": ["// Date utility functions\n\nexport const formatDate = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n};\n\nexport const formatDateTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n};\n\nexport const formatRelativeTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n\n  return formatDate(d);\n};\n\nexport const isTokenExpired = (expiresAt: string): boolean => {\n  const expirationTime = new Date(expiresAt).getTime();\n  const currentTime = new Date().getTime();\n  const bufferTime = 5 * 60 * 1000; // 5 minutes buffer\n  \n  return currentTime >= (expirationTime - bufferTime);\n};\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;AAElB,MAAM,aAAa,CAAC;IACzB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,cAAc,CAAC,SAAS;QAC/B,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;IACrE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;IAC5D;IAEA,OAAO,WAAW;AACpB;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,iBAAiB,IAAI,KAAK,WAAW,OAAO;IAClD,MAAM,cAAc,IAAI,OAAO,OAAO;IACtC,MAAM,aAAa,IAAI,KAAK,MAAM,mBAAmB;IAErD,OAAO,eAAgB,iBAAiB;AAC1C", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/format.ts"], "sourcesContent": ["// Formatting utility functions\n\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\nexport const formatNumber = (num: number): string => {\n  return new Intl.NumberFormat('en-US').format(num);\n};\n\nexport const formatCurrency = (amount: number, currency = 'USD'): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n};\n\nexport const truncateText = (text: string, maxLength: number): string => {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n};\n\nexport const capitalizeFirst = (str: string): string => {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n};\n\nexport const camelToTitle = (str: string): string => {\n  return str\n    .replace(/([A-Z])/g, ' $1')\n    .replace(/^./, (str) => str.toUpperCase())\n    .trim();\n};\n\nexport const generateSessionId = (): string => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;AAExB,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,MAAM,iBAAiB,CAAC,QAAgB,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,eAAe,CAAC,MAAc;IACzC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,YAAY,OACpB,OAAO,CAAC,MAAM,CAAC,MAAQ,IAAI,WAAW,IACtC,IAAI;AACT;AAEO,MAAM,oBAAoB;IAC/B,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/validation.ts"], "sourcesContent": ["// Validation utility functions\n\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const isValidPassword = (password: string): boolean => {\n  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number\n  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;\n  return passwordRegex.test(password);\n};\n\nexport const isValidUrl = (url: string): boolean => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const isValidPort = (port: number): boolean => {\n  return port >= 1 && port <= 65535;\n};\n\nexport const isValidDatabaseName = (name: string): boolean => {\n  // Basic validation for database names\n  const dbNameRegex = /^[a-zA-Z][a-zA-Z0-9_-]*$/;\n  return dbNameRegex.test(name) && name.length >= 2 && name.length <= 50;\n};\n\nexport const sanitizeInput = (input: string): string => {\n  return input.trim().replace(/[<>]/g, '');\n};\n\nexport const validateRequired = (value: any): boolean => {\n  if (typeof value === 'string') {\n    return value.trim().length > 0;\n  }\n  return value !== null && value !== undefined;\n};\n\nexport const validateMinLength = (value: string, minLength: number): boolean => {\n  return value.length >= minLength;\n};\n\nexport const validateMaxLength = (value: string, maxLength: number): boolean => {\n  return value.length <= maxLength;\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;AAExB,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,kBAAkB,CAAC;IAC9B,4DAA4D;IAC5D,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,QAAQ,KAAK,QAAQ;AAC9B;AAEO,MAAM,sBAAsB,CAAC;IAClC,sCAAsC;IACtC,MAAM,cAAc;IACpB,OAAO,YAAY,IAAI,CAAC,SAAS,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI;AACtE;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,MAAM,IAAI,GAAG,MAAM,GAAG;IAC/B;IACA,OAAO,UAAU,QAAQ,UAAU;AACrC;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/index.ts"], "sourcesContent": ["// Main utility exports\n\nexport * from './cn';\nexport * from './date';\nexport * from './format';\nexport * from './validation';\n\n// Re-export the original cn function for backward compatibility\nexport { cn } from './cn';\n"], "names": [], "mappings": "AAAA,uBAAuB;;AAEvB;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gbACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAAA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AAAA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/auth/LoginForm.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, FormEvent, useEffect } from 'react';\r\nimport { useSearchParams } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { Label } from '@/components/ui/label';\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { getApiBaseUrl } from '@/lib/constants';\r\nimport { Eye, EyeOff, Mail, Lock } from 'lucide-react';\r\n\r\nconst LoginForm: React.FC = () => {\r\n  const [username, setUsername] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [rememberMe, setRememberMe] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const { login } = useAuth();\r\n  const searchParams = useSearchParams();\r\n\r\n  // Check for OAuth errors and registration success in URL parameters\r\n  useEffect(() => {\r\n    const oauthError = searchParams.get('oauth_error');\r\n    const errorType = searchParams.get('error');\r\n    const registrationSuccess = searchParams.get('registration');\r\n\r\n    if (registrationSuccess === 'success') {\r\n      setSuccessMessage('Account created successfully! Please sign in with your credentials.');\r\n      setError(null);\r\n    } else if (oauthError || errorType) {\r\n      let errorMessage = 'Authentication failed. Please try again.';\r\n\r\n      if (errorType === 'session_expired') {\r\n        errorMessage = 'Your session has expired. Please sign in again.';\r\n      } else if (errorType === 'oauth_failed') {\r\n        errorMessage = 'OAuth authentication failed. Please try again.';\r\n      } else if (errorType === 'missing_tokens') {\r\n        errorMessage = 'Authentication tokens are missing. Please try again.';\r\n      } else if (oauthError) {\r\n        errorMessage = `OAuth Error: ${oauthError}`;\r\n      }\r\n\r\n      setError(errorMessage);\r\n      setSuccessMessage(null);\r\n    }\r\n  }, [searchParams]);\r\n\r\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\r\n    event.preventDefault();\r\n    setError(null);\r\n    setSuccessMessage(null);\r\n    setIsLoading(true);\r\n    try {\r\n      await login({ username, password });\r\n    } catch (err: any) {\r\n      console.error('Login failed', err);\r\n      if (err.response && err.response.data && err.response.data.detail) {\r\n        setError(err.response.data.detail);\r\n      } else if (err.message) {\r\n        setError(err.message);\r\n      } else {\r\n        setError('Login failed. Please check your credentials and try again.');\r\n      }\r\n    }\r\n    setIsLoading(false);\r\n  };\r\n\r\n  const handleGoogleLogin = async () => {\r\n    setError(null);\r\n    setSuccessMessage(null);\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Get Google OAuth URL from your backend using POST method\r\n      const response = await fetch(`${getApiBaseUrl()}/auth/google`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Accept': 'application/json',\r\n          'X-Requested-With': 'XMLHttpRequest',\r\n        },\r\n        body: JSON.stringify({}),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorText = await response.text();\r\n        throw new Error(`Failed to get Google OAuth URL: ${response.status} - ${errorText}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.auth_url) {\r\n        // Redirect directly to Google OAuth URL\r\n        // Google will redirect back to your backend, which will then redirect to /oauth/callback\r\n        window.location.href = data.auth_url;\r\n      } else {\r\n        throw new Error('No OAuth URL received from backend');\r\n      }\r\n\r\n    } catch (err: any) {\r\n      console.error('Google OAuth failed', err);\r\n      setError('Failed to initiate Google login. Please try again.');\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n      <div className=\"space-y-5\">\r\n        {/* Username Field */}\r\n        <div className=\"relative group\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10\">\r\n            <Mail className=\"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200\" />\r\n          </div>\r\n          <Input\r\n            id=\"username\"\r\n            type=\"text\"\r\n            value={username}\r\n            onChange={(e) => setUsername(e.target.value)}\r\n            placeholder=\"Email or username\"\r\n            className=\"w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500\"\r\n            required\r\n            disabled={isLoading}\r\n          />\r\n        </div>\r\n\r\n        {/* Password Field */}\r\n        <div className=\"relative group\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10\">\r\n            <Lock className=\"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200\" />\r\n          </div>\r\n          <Input\r\n            id=\"password\"\r\n            type={showPassword ? \"text\" : \"password\"}\r\n            value={password}\r\n            onChange={(e) => setPassword(e.target.value)}\r\n            placeholder=\"Password\"\r\n            className=\"w-full pl-12 pr-14 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500\"\r\n            required\r\n            disabled={isLoading}\r\n          />\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => setShowPassword(!showPassword)}\r\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100 transition-colors duration-200 z-10 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n            disabled={isLoading}\r\n          >\r\n            {showPassword ? (\r\n              <EyeOff className=\"h-5 w-5\" />\r\n            ) : (\r\n              <Eye className=\"h-5 w-5\" />\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Success Message */}\r\n      {successMessage && (\r\n        <div className=\"bg-green-50 dark:bg-green-900/30 border-2 border-green-200 dark:border-green-500/50 rounded-2xl p-4 animate-in slide-in-from-top-2 duration-300 shadow-sm\">\r\n          <p className=\"text-sm text-green-700 dark:text-green-300 text-center font-medium\">{successMessage}</p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Error Message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900/30 border-2 border-red-200 dark:border-red-500/50 rounded-2xl p-4 animate-in slide-in-from-top-2 duration-300 shadow-sm\">\r\n          <p className=\"text-sm text-red-700 dark:text-red-300 text-center font-medium\">{error}</p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Remember Me & Forgot Password */}\r\n      <div className=\"flex items-center justify-between pt-2\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          <Checkbox \r\n            id=\"rememberMe\" \r\n            checked={rememberMe} \r\n            onCheckedChange={(checked) => setRememberMe(checked as boolean)} \r\n            className=\"rounded-lg border-2 border-gray-300 dark:border-gray-500 text-blue-500 dark:text-blue-400 data-[state=checked]:bg-blue-500 dark:data-[state=checked]:bg-blue-400 data-[state=checked]:border-blue-500 dark:data-[state=checked]:border-blue-400\"\r\n            disabled={isLoading}\r\n          />\r\n          <Label htmlFor=\"rememberMe\" className=\"text-sm text-gray-700 dark:text-gray-300 font-medium cursor-pointer\">\r\n            Remember me\r\n          </Label>\r\n        </div>\r\n        <a \r\n          href=\"#\" \r\n          className=\"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 font-medium hover:underline\"\r\n        >\r\n          Forgot password?\r\n        </a>\r\n      </div>\r\n\r\n      {/* Submit Button */}\r\n      <Button \r\n        type=\"submit\" \r\n        className=\"w-full bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-500 dark:to-blue-600 hover:from-blue-700 hover:to-blue-800 dark:hover:from-blue-600 dark:hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg shadow-blue-500/30 dark:shadow-blue-400/20 hover:shadow-blue-500/50 dark:hover:shadow-blue-400/30 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:shadow-blue-500/25 text-base border-0\"\r\n        disabled={isLoading}\r\n      >\r\n        {isLoading ? (\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-3\"></div>\r\n            Signing in...\r\n          </div>\r\n        ) : (\r\n          'Sign in'\r\n        )}\r\n      </Button>\r\n\r\n      {/* OAuth Divider */}\r\n      <div className=\"relative\">\r\n        <div className=\"absolute inset-0 flex items-center\">\r\n          <span className=\"w-full border-t-2 border-gray-200 dark:border-gray-600\" />\r\n        </div>\r\n        <div className=\"relative flex justify-center text-sm\">\r\n          <span className=\"px-4 bg-white dark:bg-gray-900 text-gray-600 dark:text-gray-400 font-medium\">Or continue with</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Google OAuth Button */}\r\n      <Button\r\n        type=\"button\"\r\n        onClick={handleGoogleLogin}\r\n        className=\"w-full bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 font-semibold py-4 px-4 rounded-2xl border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 transition-all duration-300 focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:ring-offset-2 dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-sm dark:shadow-gray-900/20 hover:shadow-md dark:hover:shadow-gray-900/30\"\r\n        disabled={isLoading}\r\n      >\r\n        <svg className=\"w-5 h-5 mr-3\" viewBox=\"0 0 24 24\">\r\n          <path fill=\"#4285F4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\r\n          <path fill=\"#34A853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\r\n          <path fill=\"#FBBC05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\r\n          <path fill=\"#EA4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\r\n        </svg>\r\n        Continue with Google\r\n      </Button>\r\n\r\n      {/* Sign Up Link */}\r\n      <div className=\"text-center pt-4\">\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n          Don't have an account?{' '}\r\n          <Link href=\"/register\" className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 font-semibold hover:underline\">\r\n            Create one\r\n          </Link>\r\n        </p>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default LoginForm;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;;;AAXA;;;;;;;;;;;AAaA,MAAM,YAAsB;;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAEnC,oEAAoE;IACpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,aAAa,aAAa,GAAG,CAAC;YACpC,MAAM,YAAY,aAAa,GAAG,CAAC;YACnC,MAAM,sBAAsB,aAAa,GAAG,CAAC;YAE7C,IAAI,wBAAwB,WAAW;gBACrC,kBAAkB;gBAClB,SAAS;YACX,OAAO,IAAI,cAAc,WAAW;gBAClC,IAAI,eAAe;gBAEnB,IAAI,cAAc,mBAAmB;oBACnC,eAAe;gBACjB,OAAO,IAAI,cAAc,gBAAgB;oBACvC,eAAe;gBACjB,OAAO,IAAI,cAAc,kBAAkB;oBACzC,eAAe;gBACjB,OAAO,IAAI,YAAY;oBACrB,eAAe,CAAC,aAAa,EAAE,YAAY;gBAC7C;gBAEA,SAAS;gBACT,kBAAkB;YACpB;QACF;8BAAG;QAAC;KAAa;IAEjB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,SAAS;QACT,kBAAkB;QAClB,aAAa;QACb,IAAI;YACF,MAAM,MAAM;gBAAE;gBAAU;YAAS;QACnC,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE;gBACjE,SAAS,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM;YACnC,OAAO,IAAI,IAAI,OAAO,EAAE;gBACtB,SAAS,IAAI,OAAO;YACtB,OAAO;gBACL,SAAS;YACX;QACF;QACA,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,SAAS;QACT,kBAAkB;QAClB,aAAa;QAEb,IAAI;YACF,2DAA2D;YAC3D,MAAM,WAAW,MAAM,MAAM,GAAG,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,IAAI,YAAY,CAAC,EAAE;gBAC7D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,UAAU;oBACV,oBAAoB;gBACtB;gBACA,MAAM,KAAK,SAAS,CAAC,CAAC;YACxB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;YACrF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,QAAQ,EAAE;gBACjB,wCAAwC;gBACxC,yFAAyF;gBACzF,OAAO,QAAQ,CAAC,IAAI,GAAG,KAAK,QAAQ;YACtC,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QAEF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS;YACT,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,aAAY;gCACZ,WAAU;gCACV,QAAQ;gCACR,UAAU;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAM,eAAe,SAAS;gCAC9B,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,aAAY;gCACZ,WAAU;gCACV,QAAQ;gCACR,UAAU;;;;;;0CAEZ,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB,CAAC;gCAChC,WAAU;gCACV,UAAU;0CAET,6BACC,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;yDAElB,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAOtB,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAsE;;;;;;;;;;;YAKtF,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAkE;;;;;;;;;;;0BAKnF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uIAAA,CAAA,WAAQ;gCACP,IAAG;gCACH,SAAS;gCACT,iBAAiB,CAAC,UAAY,cAAc;gCAC5C,WAAU;gCACV,UAAU;;;;;;0CAEZ,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAa,WAAU;0CAAsE;;;;;;;;;;;;kCAI9G,6LAAC;wBACC,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,WAAU;gBACV,UAAU;0BAET,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;wBAAuF;;;;;;2BAIxG;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;;;;;;;;;;kCAElB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAA8E;;;;;;;;;;;;;;;;;0BAKlG,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,UAAU;;kCAEV,6LAAC;wBAAI,WAAU;wBAAe,SAAQ;;0CACpC,6LAAC;gCAAK,MAAK;gCAAU,GAAE;;;;;;0CACvB,6LAAC;gCAAK,MAAK;gCAAU,GAAE;;;;;;0CACvB,6LAAC;gCAAK,MAAK;gCAAU,GAAE;;;;;;0CACvB,6LAAC;gCAAK,MAAK;gCAAU,GAAE;;;;;;;;;;;;oBACnB;;;;;;;0BAKR,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAA2C;wBAC/B;sCACvB,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAU;sCAA6I;;;;;;;;;;;;;;;;;;;;;;;AAOxL;GA5OM;;QAQc,mIAAA,CAAA,UAAO;QACJ,qIAAA,CAAA,kBAAe;;;KAThC;uCA8OS", "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/app/%28auth%29/login/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport LoginForm from '@/components/features/auth/LoginForm';\r\nimport Link from 'next/link';\r\nimport React, { Suspense } from 'react';\r\n\r\nconst LoginPage: React.FC = () => {\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\r\n      {/* Header with logo */}\r\n      <div className=\"absolute top-0 left-0 p-6 z-20\">\r\n        <Link href=\"/\" className=\"flex items-center space-x-2 group\">\r\n          <div className=\"flex items-center justify-center w-10 h-10 rounded-xl bg-blue-500 group-hover:bg-blue-600 transition-colors duration-200 shadow-sm\">\r\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" className=\"w-5 h-5 text-white\">\r\n              <path\r\n                d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"2\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n              />\r\n            </svg>\r\n          </div>\r\n          <span className=\"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-500 transition-colors duration-200\">\r\n            Agent\r\n          </span>\r\n        </Link>\r\n      </div>\r\n      \r\n      {/* Main content */}\r\n      <div className=\"min-h-screen flex items-center justify-center p-6\">\r\n        <div className=\"w-full max-w-md\">\r\n          {/* Glassmorphism card */}\r\n          <div className=\"backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 rounded-3xl border border-gray-200/50 dark:border-gray-700/50 shadow-xl\">\r\n            {/* Header section */}\r\n            <div className=\"p-8 pb-0 text-center\">\r\n              <h1 className=\"text-3xl font-light text-gray-900 dark:text-white mb-2 tracking-tight\">\r\n                Welcome back\r\n              </h1>\r\n              <p className=\"text-gray-500 dark:text-gray-400 font-light\">\r\n                Sign in to continue to your workspace\r\n              </p>\r\n            </div>\r\n\r\n            {/* Form section */}\r\n            <div className=\"p-8\">\r\n              <Suspense fallback={<div className=\"flex justify-center items-center h-32\">Loading...</div>}>\r\n              <LoginForm />\r\n              </Suspense>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Support link */}\r\n          <div className=\"text-center mt-8\">\r\n            <p className=\"text-sm text-gray-400 dark:text-gray-500\">\r\n              Need assistance?{' '}\r\n              <a href=\"#\" className=\"text-blue-500 hover:text-blue-600 transition-colors duration-200 font-medium\">\r\n                Contact support\r\n              </a>\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoginPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,YAAsB;IAC1B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;;sCACvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,SAAQ;gCAAY,MAAK;gCAAO,WAAU;0CAC7C,cAAA,6LAAC;oCACC,GAAE;oCACF,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;;;;;;;;;;;;;;;;sCAIrB,6LAAC;4BAAK,WAAU;sCAA+G;;;;;;;;;;;;;;;;;0BAOnI,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwE;;;;;;sDAGtF,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;;8CAM7D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6JAAA,CAAA,WAAQ;wCAAC,wBAAU,6LAAC;4CAAI,WAAU;sDAAwC;;;;;;kDAC3E,cAAA,6LAAC,sJAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAA2C;oCACrC;kDACjB,6LAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA+E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnH;KA3DM;uCA6DS", "debugId": null}}]}