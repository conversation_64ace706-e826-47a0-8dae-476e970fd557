(()=>{var e={};e.id=300,e.ids=[300],e.modules={1777:(e,t,s)=>{Promise.resolve().then(s.bind(s,3186))},3186:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\NXTHUMANS\\\\internal-solutions-nxtwebsite\\\\AgentReport 2\\\\AgentReportUI\\\\src\\\\app\\\\(dashboard)\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(dashboard)\\profile\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32118:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=s(65239),r=s(48088),l=s(88170),i=s.n(l),d=s(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);s.d(t,n);let o={children:["",{children:["(dashboard)",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3186)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(dashboard)\\profile\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(dashboard)\\profile\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var a=s(60687),r=s(43210),l=s(7766);let i=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));i.displayName="Textarea"},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41550:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},43649:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>n,Wu:()=>o,ZB:()=>d,Zp:()=>l,aR:()=>i});var a=s(60687);s(43210);var r=s(7766);function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold text-white",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(60687),r=s(43210),l=s(14163),i=r.forwardRef((e,t)=>(0,a.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var d=s(7766);function n({className:e,...t}){return(0,a.jsx)(i,{"data-slot":"label",className:(0,d.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76242:(e,t,s)=>{"use strict";s.d(t,{Bc:()=>d,ZI:()=>c,k$:()=>o,m_:()=>n});var a=s(60687),r=s(43210),l=s(9989),i=s(7766);let d=l.Kq,n=l.bL,o=l.l9,c=r.forwardRef(({className:e,sideOffset:t=4,...s},r)=>(0,a.jsx)(l.UC,{ref:r,sideOffset:t,className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s}));c.displayName=l.UC.displayName},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>l});var a=s(60687);s(43210);var r=s(7766);function l({className:e,type:t,...s}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},89837:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>R});var a=s(60687),r=s(43210),l=s(71837),i=s(51851),d=s(91010),n=s(30070),o=s(29523),c=s(44493),m=s(89667),u=s(54300),x=s(34729),h=s(96834),p=s(76242),b=s(58869),g=s(43649),f=s(78122),v=s(5336),j=s(62688);let k=(0,j.A)("pen-line",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]),N=(0,j.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var y=s(41550),w=s(11860),A=s(8819),_=s(99891),C=s(40228),P=s(48730),M=s(19959);let q=()=>{let{getUserProfile:e}=(0,i.g)(),{user:t}=(0,d.A)(),s=(0,r.useMemo)(()=>({title:"Profile",icon:b.A}),[]);(0,n.H)(s);let[l,j]=(0,r.useState)(null),[q,U]=(0,r.useState)(!0),[R,S]=(0,r.useState)(!1),[E,z]=(0,r.useState)(!1),[T,I]=(0,r.useState)(null),[F,L]=(0,r.useState)(null),[D,Z]=(0,r.useState)({full_name:"",bio:"",email:""}),[H,B]=(0,r.useState)({}),$=(0,r.useCallback)(async()=>{U(!0),I(null);try{let t=await e();j(t),Z({full_name:t.full_name||"",bio:t.bio||"",email:t.email||""})}catch(e){console.error("Failed to fetch user profile:",e),I("Failed to load profile information. Please try again.")}finally{U(!1)}},[e]);(0,r.useEffect)(()=>{$()},[$]);let G=(0,r.useCallback)(()=>{let e={};return D.full_name.trim()?D.full_name.trim().length<2&&(e.full_name="Full name must be at least 2 characters"):e.full_name="Full name is required",D.email&&!D.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)&&(e.email="Please enter a valid email address"),D.bio.length>500&&(e.bio="Bio must be less than 500 characters"),B(e),0===Object.keys(e).length},[D]),J=(0,r.useCallback)((e,t)=>{Z(s=>({...s,[e]:t})),H[e]&&B(t=>({...t,[e]:""}))},[H]),X=(0,r.useCallback)(()=>{S(!0),I(null),L(null)},[]),O=(0,r.useCallback)(()=>{S(!1),B({}),l&&Z({full_name:l.full_name||"",bio:l.bio||"",email:l.email||""})},[l]),V=(0,r.useCallback)(async()=>{if(G()){z(!0),I(null);try{await new Promise(e=>setTimeout(e,1e3)),l&&j({...l,...D}),S(!1),L("Profile updated successfully!"),setTimeout(()=>L(null),3e3)}catch(e){console.error("Failed to update profile:",e),I("Failed to update profile. Please try again.")}finally{z(!1)}}},[D,G,l]),W=(0,r.useCallback)(e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),[]),K=(0,r.useCallback)(e=>{switch(e){case"google_oauth":return{label:"Google OAuth",color:"bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300"};case"github_oauth":return{label:"GitHub OAuth",color:"bg-gray-100 dark:bg-gray-900 text-gray-700 dark:text-gray-300"};case"email":return{label:"Email & Password",color:"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300"};default:return{label:"Unknown",color:"bg-gray-100 dark:bg-gray-900 text-gray-700 dark:text-gray-300"}}},[]);return q?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,a.jsx)("div",{className:"container mx-auto p-4 sm:p-6 lg:p-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,a.jsx)("div",{className:"text-center space-y-4",children:(0,a.jsxs)("div",{className:"inline-flex items-center gap-3 p-4 bg-white dark:bg-slate-900 rounded-2xl shadow-sm border",children:[(0,a.jsx)("div",{className:"p-2 rounded-xl bg-slate-200 dark:bg-slate-700 w-12 h-12 animate-pulse"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-200 dark:bg-slate-700 rounded w-32 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-5 bg-slate-200 dark:bg-slate-700 rounded w-48 animate-pulse"})]})]})}),(0,a.jsxs)(c.Zp,{className:"animate-pulse",children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)("div",{className:"h-6 bg-slate-200 dark:bg-slate-700 rounded w-1/4 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2 animate-pulse"})]}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"h-4 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-slate-200 dark:bg-slate-700 rounded w-3/4 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2 animate-pulse"})]})]})]})})}):T&&!l?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,a.jsx)("div",{className:"container mx-auto p-4 sm:p-6 lg:p-8",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsx)(c.Zp,{className:"border-red-200 dark:border-red-800",children:(0,a.jsxs)(c.Wu,{className:"p-6 text-center",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-red-900 dark:text-red-100 mb-2",children:"Failed to Load Profile"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-300 mb-4",children:T}),(0,a.jsxs)(o.$,{onClick:$,variant:"outline",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Try Again"]})]})})})})}):(0,a.jsx)(p.Bc,{children:(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,a.jsx)("div",{className:"container mx-auto p-4 sm:p-6 lg:p-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[F&&(0,a.jsx)("div",{className:"p-4 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 text-green-600 dark:text-green-400 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-green-800 dark:text-green-200",children:[(0,a.jsx)("div",{className:"font-medium",children:"Success"}),(0,a.jsx)("div",{className:"text-sm",children:F})]})]})}),T&&(0,a.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-red-800 dark:text-red-200",children:[(0,a.jsx)("div",{className:"font-medium",children:"Error"}),(0,a.jsx)("div",{className:"text-sm",children:T})]})]})}),(0,a.jsxs)(c.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,a.jsxs)(c.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100",children:"Profile Information"}),(0,a.jsx)(c.BT,{className:"text-slate-600 dark:text-slate-400",children:"Your personal details and account information"})]}),!R&&(0,a.jsxs)(p.m_,{children:[(0,a.jsx)(p.k$,{asChild:!0,children:(0,a.jsxs)(o.$,{onClick:X,variant:"outline",size:"sm",className:"flex items-center gap-2",children:[(0,a.jsx)(k,{className:"h-4 w-4"}),"Edit"]})}),(0,a.jsx)(p.ZI,{children:(0,a.jsx)("p",{children:"Edit your profile information"})})]})]}),(0,a.jsxs)(c.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-20 h-20 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white text-2xl font-bold shadow-lg",children:l?.profile_picture_url?(0,a.jsx)("img",{src:l.profile_picture_url,alt:"Profile",className:"w-full h-full rounded-full object-cover"}):(l?.full_name||l?.username||"U").charAt(0).toUpperCase()}),R&&(0,a.jsxs)(p.m_,{children:[(0,a.jsx)(p.k$,{asChild:!0,children:(0,a.jsx)(o.$,{size:"sm",variant:"outline",className:"absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0",children:(0,a.jsx)(N,{className:"h-4 w-4"})})}),(0,a.jsx)(p.ZI,{children:(0,a.jsx)("p",{children:"Change profile picture"})})]})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100",children:l?.full_name||l?.username||"Unknown User"}),(0,a.jsxs)("p",{className:"text-slate-600 dark:text-slate-400",children:["@",l?.username]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,a.jsxs)(h.E,{variant:"outline",className:"text-xs",children:["User ID: ",l?.id.slice(0,8),"..."]}),l?.is_active&&(0,a.jsxs)(h.E,{className:"bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800 text-xs",children:[(0,a.jsx)(v.A,{className:"w-3 h-3 mr-1"}),"Active"]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"full_name",className:"text-sm font-medium",children:"Full Name"}),R?(0,a.jsxs)("div",{children:[(0,a.jsx)(m.p,{id:"full_name",value:D.full_name,onChange:e=>J("full_name",e.target.value),placeholder:"Enter your full name",className:H.full_name?"border-red-300 dark:border-red-700":""}),H.full_name&&(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400 mt-1",children:H.full_name})]}):(0,a.jsx)("p",{className:"text-slate-900 dark:text-slate-100 py-2",children:l?.full_name||"Not set"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"email",className:"text-sm font-medium",children:"Email Address"}),R?(0,a.jsxs)("div",{children:[(0,a.jsx)(m.p,{id:"email",type:"email",value:D.email,onChange:e=>J("email",e.target.value),placeholder:"Enter your email",className:H.email?"border-red-300 dark:border-red-700":""}),H.email&&(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400 mt-1",children:H.email})]}):(0,a.jsxs)("div",{className:"flex items-center gap-2 py-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 text-slate-500"}),(0,a.jsx)("span",{className:"text-slate-900 dark:text-slate-100",children:l?.email||"Not set"}),l?.email_verified&&(0,a.jsxs)(p.m_,{children:[(0,a.jsx)(p.k$,{asChild:!0,children:(0,a.jsx)(v.A,{className:"h-4 w-4 text-green-500"})}),(0,a.jsx)(p.ZI,{children:(0,a.jsx)("p",{children:"Email verified"})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,a.jsx)(u.J,{htmlFor:"bio",className:"text-sm font-medium",children:"Bio"}),R?(0,a.jsxs)("div",{children:[(0,a.jsx)(x.T,{id:"bio",value:D.bio,onChange:e=>J("bio",e.target.value),placeholder:"Tell us about yourself...",rows:3,className:H.bio?"border-red-300 dark:border-red-700":""}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-1",children:[H.bio?(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:H.bio}):(0,a.jsx)("div",{}),(0,a.jsxs)("p",{className:"text-xs text-slate-500 dark:text-slate-400",children:[D.bio.length,"/500"]})]})]}):(0,a.jsx)("p",{className:"text-slate-900 dark:text-slate-100 py-2",children:l?.bio||"No bio available"})]})]}),R&&(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-slate-200 dark:border-slate-700",children:[(0,a.jsxs)(o.$,{onClick:O,variant:"outline",disabled:E,children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Cancel"]}),(0,a.jsxs)(o.$,{onClick:V,disabled:E,className:"bg-blue-600 hover:bg-blue-700",children:[E?(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2"}),E?"Saving...":"Save Changes"]})]})]})]}),(0,a.jsxs)(c.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsxs)(c.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Account Details"]}),(0,a.jsx)(c.BT,{className:"text-slate-600 dark:text-slate-400",children:"Information about your account and authentication"})]}),(0,a.jsx)(c.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Account Created"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-slate-900 dark:text-slate-100",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 text-slate-500"}),(0,a.jsx)("span",{children:l?.created_at?W(l.created_at):"Unknown"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Last Login"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-slate-900 dark:text-slate-100",children:[(0,a.jsx)(P.A,{className:"h-4 w-4 text-slate-500"}),(0,a.jsx)("span",{children:l?.last_login?W(l.last_login):"Unknown"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Authentication Method"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"h-4 w-4 text-slate-500"}),(0,a.jsx)(h.E,{variant:"outline",className:K(l?.auth_method).color,children:K(l?.auth_method).label})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Account Status"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 text-slate-500"}),(0,a.jsx)(h.E,{variant:"outline",className:l?.is_active?"bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800":"bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800",children:l?.is_active?"Active":"Inactive"})]})]})]})})]})]})})})})};var U=s(99761);function R(){return(0,a.jsx)(U.Ay,{children:(0,a.jsx)(l.A,{children:(0,a.jsx)(q,{})})})}},92049:(e,t,s)=>{Promise.resolve().then(s.bind(s,89837))},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var a=s(60687);s(43210);var r=s(24224),l=s(7766);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,...s}){return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:t}),e),...s})}},99761:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>d});var a=s(60687),r=s(43210),l=s(91010),i=s(16189);function d({children:e,requireAuth:t=!0,redirectTo:s="/login"}){let{isAuthenticated:d,isLoading:n,isNewUser:o,signIn:c}=(0,l.A)(),m=(0,i.useRouter)();(0,i.usePathname)();let[u,x]=(0,r.useState)(!0),[h,p]=(0,r.useState)(!1);return u||n?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Checking authentication..."})]})}):t&&!d?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Authentication Required"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please sign in to access this page."}),(0,a.jsx)("button",{onClick:()=>m.push(s),className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Sign In"})]})}):(0,a.jsx)(a.Fragment,{children:e})}s(97616)},99891:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,255,658,365,814,292,592,989,695,233],()=>s(32118));module.exports=a})();