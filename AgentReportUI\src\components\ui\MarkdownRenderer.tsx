import React, { useMemo, useRef, useCallback } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { cn } from '@/lib/utils';

interface MarkdownRendererProps {
  content: string;
  className?: string;
  isStreaming?: boolean; // Add this prop to optimize for streaming
}

// Function to preprocess content and convert custom formatting to proper markdown
const preprocessContent = (content: string): string => {
  if (!content) return '';

  let processed = content;

  // The backend sends content with ### as section separators, not proper markdown headers
  // We need to convert this format to proper markdown

  // Step 1: Split content by ### to get sections
  const sections = processed.split('###').map(section => section.trim()).filter(Boolean);

  const processedSections = sections.map((section, index) => {
    let cleanSection = section.trim();

    // Check if section starts with an emoji - make it a header
    const emojiMatch = cleanSection.match(/^([🏆📊📋💡🔍📈📉💰🎯⚡🚀💎🔥⭐🎉🎊🏅🎖️💹💲💸💵💴💶💷🔢📝📄📃📑🔎📖📚📓📕📗📘📙📒📰🗞️])\s*([\s\S]+)$/);

    if (emojiMatch) {
      const [, emoji, rest] = emojiMatch;
      // Split the rest into title and content
      const lines = rest.split('\n');
      const title = lines[0].trim();
      const content = lines.slice(1).join('\n').trim();

      if (content) {
        cleanSection = `## ${emoji} ${title}\n\n${content}`;
      } else {
        cleanSection = `## ${emoji} ${title}`;
      }
    }

    return cleanSection;
  });

  // Join sections with proper spacing
  processed = processedSections.join('\n\n');

  // Step 2: Convert bullet points (•) to proper markdown lists
  processed = processed.replace(/•\s*/g, '- ');

  // Step 3: Fix numbered lists - ensure they're on new lines
  processed = processed.replace(/(\d+\.\s*\*\*[^*]+\*\*)/g, '\n$1');

  // NEW STEP: Ensure lists start on a new line when they immediately follow a header ending with a colon
  // e.g., "## 📊 Key Observations: -" => "## 📊 Key Observations:\n\n-"
  processed = processed.replace(/(:)\s*(-\s+)/g, '$1\n\n$2');

  // NEW STEP: Convert heading followed directly by markdown table into separate header + table
  // e.g., "Top 10 Movies | Rank | Title" => "### Top 10 Movies\n\n| Rank | Title"
  processed = processed.replace(/^([^|\n#]+?)\s+\|\s+(.+)$/gm, '### $1\n\n| $2');

  // NEW STEP: Ensure bullets that appear inline (" - **Bold" pattern) start on new lines for proper list rendering
  processed = processed.replace(/\s-\s\*\*/g, '\n- **');

  // Step 4: Clean up spacing
  processed = processed.replace(/\n{3,}/g, '\n\n');
  processed = processed.replace(/^\n+/, ''); // Remove leading newlines

  return processed.trim();
};

// Create a debounced preprocessing function to prevent excessive re-processing during streaming
const createDebouncedPreprocessor = () => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastContent = '';
  let lastResult = '';
  
  return (content: string, isStreaming: boolean = false): string => {
    // If content hasn't changed, return cached result immediately
    if (content === lastContent) {
      return lastResult;
    }
    
    // For non-streaming or short content, process immediately
    if (!isStreaming || content.length < 100) {
      const result = preprocessContent(content);
      lastContent = content;
      lastResult = result;
      return result;
    }
    
    // For streaming content, use the last processed result for small changes
    // Only reprocess if the content has grown significantly or streaming has stopped
    const contentDiff = Math.abs(content.length - lastContent.length);
    if (contentDiff < 50 && isStreaming) {
      // Return a simple concatenation for small streaming updates
      return lastResult + content.slice(lastContent.length);
    }
    
    // Reprocess for significant changes
    const result = preprocessContent(content);
    lastContent = content;
    lastResult = result;
    return result;
  };
};

// Create a single instance to maintain state across re-renders
const debouncedPreprocessor = createDebouncedPreprocessor();

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = React.memo(({
  content,
  className = '',
  isStreaming = false,
}) => {
  const lastProcessedContentRef = useRef<string>('');
  const lastProcessedResultRef = useRef<string>('');
  const lastLoggedContentRef = useRef<string>('');
  const renderCountRef = useRef(0);
  
  // Optimized preprocessing with streaming awareness
  const processedContent = useMemo(() => {
    renderCountRef.current++;
    
    // Prevent excessive re-renders during streaming
    if (isStreaming && renderCountRef.current > 10 && content === lastProcessedContentRef.current) {
      return lastProcessedResultRef.current;
    }
    
    // If content hasn't changed significantly, return cached result
    if (content === lastProcessedContentRef.current) {
      return lastProcessedResultRef.current;
    }
    
    // Use debounced preprocessor for streaming content
    const result = debouncedPreprocessor(content, isStreaming);
    
    // Cache only if content is complete or has grown significantly
    if (!isStreaming || content.length - lastProcessedContentRef.current.length > 100) {
      lastProcessedContentRef.current = content;
      lastProcessedResultRef.current = result;
    }
    
    return result;
  }, [content, isStreaming]);

  // Throttled logging to prevent console spam during streaming
  useMemo(() => {
    if (process.env.NODE_ENV === 'development' && content && content.includes('###')) {
      // Only log if content actually changed significantly and not during active streaming
      if (!isStreaming && content !== lastLoggedContentRef.current && Math.abs(content.length - lastLoggedContentRef.current.length) > 50) {
        console.log('🔍 MarkdownRenderer Debug (non-streaming):');
        console.log('Original content length:', content.length);
        console.log('Processed content length:', processedContent.length);
        lastLoggedContentRef.current = content;
      }
    }
  }, [content, processedContent, isStreaming]);

  // Reset render count when content changes significantly
  const prevContentLengthRef = useRef(0);
  useMemo(() => {
    if (Math.abs(content.length - prevContentLengthRef.current) > 200) {
      renderCountRef.current = 0;
      prevContentLengthRef.current = content.length;
    }
  }, [content.length]);

  // Memoize the ReactMarkdown component to prevent unnecessary re-renders
  const markdownComponent = useMemo(() => (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      rehypePlugins={[rehypeRaw]}
      components={{
        // Headers
        h1: ({ children }) => (
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2 mb-4 mt-6">
            {children}
          </h1>
        ),
        h2: ({ children }) => (
          <h2 className="text-xl font-semibold text-blue-600 dark:text-blue-400 border-b border-gray-200 dark:border-gray-700 pb-2 mb-4 mt-6">
            {children}
          </h2>
        ),
        h3: ({ children }) => (
          <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3 mt-5 border-l-4 border-blue-300 dark:border-blue-600 pl-3">
            {children}
          </h3>
        ),
        h4: ({ children }) => (
          <h4 className="text-base font-medium text-gray-700 dark:text-gray-300 mb-2 mt-2">
            {children}
          </h4>
        ),
        h5: ({ children }) => (
          <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 mt-2">
            {children}
          </h5>
        ),
        h6: ({ children }) => (
          <h6 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 mt-2">
            {children}
          </h6>
        ),
        
        // Text formatting - Enhanced for analytical content
        strong: ({ children }) => (
          <strong className="font-semibold text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 px-1 py-0.5 rounded">
            {children}
          </strong>
        ),
        em: ({ children }) => (
          <em className="italic text-gray-800 dark:text-gray-200">
            {children}
          </em>
        ),
        
        // Lists - Enhanced for analytical content
        ul: ({ children }) => (
          <ul className="list-disc pl-6 my-4 space-y-2 text-gray-800 dark:text-gray-200">
            {children}
          </ul>
        ),
        ol: ({ children }) => (
          <ol className="list-decimal pl-6 my-4 space-y-2 text-gray-800 dark:text-gray-200">
            {children}
          </ol>
        ),
        li: ({ children }) => (
          <li className="leading-relaxed py-0.5 text-sm">
            {children}
          </li>
        ),
        
        // Paragraphs - Enhanced spacing for analytical content
        p: ({ children }) => (
          <p className="mb-4 leading-relaxed text-gray-800 dark:text-gray-200 text-sm">
            {children}
          </p>
        ),
        
        // Code
        // @ts-ignore - react-markdown passes an "inline" prop that is not in the intrinsic HTML <code> element props
        code: ({ children, inline }) => 
          inline ? (
            <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono text-gray-900 dark:text-gray-100">
              {children}
            </code>
          ) : (
            <pre className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-3 my-3 overflow-x-auto">
              <code className="text-sm font-mono text-gray-900 dark:text-gray-100">
                {children}
              </code>
            </pre>
          ),
        
        // Tables
        table: ({ children }) => (
          <div className="overflow-x-auto my-3">
            <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
              {children}
            </table>
          </div>
        ),
        th: ({ children }) => (
          <th className="border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 px-3 py-2 text-left font-semibold text-gray-900 dark:text-gray-100">
            {children}
          </th>
        ),
        td: ({ children }) => (
          <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-800 dark:text-gray-200">
            {children}
          </td>
        ),
        
        // Blockquotes
        blockquote: ({ children }) => (
          <blockquote className="border-l-4 border-blue-500 dark:border-blue-400 pl-4 my-3 italic text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 py-2 rounded-r">
            {children}
          </blockquote>
        ),
        
        // Links
        a: ({ children, href }) => (
          <a 
            href={href} 
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            {children}
          </a>
        ),
        
        // Horizontal rule
        hr: () => (
          <hr className="my-4 border-gray-300 dark:border-gray-600" />
        ),
      }}
    >
      {processedContent || 'No content available'}
    </ReactMarkdown>
  ), [processedContent]);

  return (
    <div className={cn("markdown-content", className)}>
      {markdownComponent}
    </div>
  );
});

MarkdownRenderer.displayName = 'MarkdownRenderer';

export default MarkdownRenderer;
