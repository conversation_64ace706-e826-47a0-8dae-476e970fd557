"""Database Service

This module provides services for connecting to and querying databases.
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple, Union
import logging
import time
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from dataclasses import dataclass
import threading
import weakref
from collections import defaultdict

import sqlalchemy
from sqlalchemy import inspect, create_engine, text, pool
from sqlalchemy.ext.asyncio import create_async_engine, AsyncConnection
from sqlalchemy.pool import QueuePool, StaticPool
import pandas as pd

from app.models.database import Database, DatabaseType, DatabaseTable, DatabaseColumn, ForeignKeyRelationship

logger = logging.getLogger(__name__)


@dataclass
class ConnectionPoolInfo:
    """Information about a database connection pool."""
    pool: Any
    user_id: str
    database_id: str
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0

    def update_access(self):
        """Update last access time and increment counter."""
        self.last_accessed = datetime.utcnow()
        self.access_count += 1

class DatabaseService:
    """Service for connecting to and querying databases with user-session-aware connection pooling."""

    def __init__(self):
        """Initialize the database service with user-session-aware connection pooling."""
        # Legacy connections for backward compatibility
        self.connections: Dict[str, Any] = {}

        # User-session-aware connection pools
        self.user_connection_pools: Dict[str, Dict[str, ConnectionPoolInfo]] = {}  # user_id -> {db_id -> pool_info}
        self.pool_cleanup_lock = threading.Lock()

        # Schema caching with intelligent cache service integration
        self.schema_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl: Dict[str, float] = {}
        self.CACHE_DURATION = 300  # 5 minutes cache TTL

        # Enhanced connection pool configuration for performance optimization
        self.POOL_IDLE_TIMEOUT = timedelta(minutes=30)  # 30 minutes idle timeout
        self.CLEANUP_INTERVAL = timedelta(minutes=5)    # Check every 5 minutes
        self.last_cleanup = datetime.utcnow()

        # Connection performance monitoring
        self.connection_metrics: Dict[str, List[float]] = defaultdict(list)
        self.query_metrics: Dict[str, List[float]] = defaultdict(list)

        # Connection pool optimization settings
        self.OPTIMAL_POOL_SIZES = {
            "postgresql": {"pool_size": 8, "max_overflow": 15},
            "supabase": {"pool_size": 6, "max_overflow": 12},
            "mysql": {"pool_size": 10, "max_overflow": 20},
            "mongodb": {"maxPoolSize": 8, "minPoolSize": 2}
        }

        # Query timeout configurations by database type
        self.QUERY_TIMEOUTS = {
            "postgresql": 120,  # 2 minutes
            "supabase": 90,     # 1.5 minutes
            "mysql": 120,       # 2 minutes
            "mongodb": 60       # 1 minute
        }

        # Start background cleanup task
        self._start_cleanup_task()

    def _start_cleanup_task(self):
        """Start background cleanup task for idle connection pools."""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(300)  # Check every 5 minutes
                    self._cleanup_idle_pools()
                except Exception as e:
                    logger.error(f"Error in connection pool cleanup: {e}")

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logger.info("Started connection pool cleanup background task")

    async def get_user_connection_pool(
        self,
        user_id: str,
        database: Database,
        force_recreate: bool = False
    ) -> Tuple[bool, Optional[str], Optional[Any]]:
        """Get or create a connection pool for a specific user and database.

        Args:
            user_id: The authenticated user ID
            database: The database to connect to
            force_recreate: Whether to force recreation of the pool

        Returns:
            Tuple of (success, error_message, connection_pool)
        """
        with self.pool_cleanup_lock:
            # Initialize user pools if not exists
            if user_id not in self.user_connection_pools:
                self.user_connection_pools[user_id] = {}

            user_pools = self.user_connection_pools[user_id]

            # Check if we have an existing pool
            if database.id in user_pools and not force_recreate:
                pool_info = user_pools[database.id]
                pool_info.update_access()
                logger.debug(f"Reusing connection pool for user {user_id}, database {database.id}")
                return True, None, pool_info.pool

            # Create new connection pool
            try:
                success, error, pool = await self._create_user_connection_pool(user_id, database)
                if success and pool:
                    pool_info = ConnectionPoolInfo(
                        pool=pool,
                        user_id=user_id,
                        database_id=database.id,
                        created_at=datetime.utcnow(),
                        last_accessed=datetime.utcnow()
                    )
                    user_pools[database.id] = pool_info
                    logger.info(f"Created connection pool for user {user_id}, database {database.id}")
                    return True, None, pool
                else:
                    return False, error, None

            except Exception as e:
                logger.error(f"Error creating connection pool for user {user_id}, database {database.id}: {e}")
                return False, str(e), None

    async def _create_user_connection_pool(
        self,
        user_id: str,
        database: Database
    ) -> Tuple[bool, Optional[str], Optional[Any]]:
        """Create a new connection pool for a user and database.

        Args:
            user_id: The authenticated user ID
            database: The database to connect to

        Returns:
            Tuple of (success, error_message, connection_pool)
        """
        try:
            # Validate Supabase connection details if applicable
            if database.db_type == DatabaseType.SUPABASE:
                self._validate_supabase_connection(database)

            connection_string = self._build_connection_string(database)

            if database.db_type == DatabaseType.MONGODB:
                # MongoDB uses a different connection approach
                from pymongo import MongoClient
                client = MongoClient(
                    connection_string,
                    maxPoolSize=5,  # Smaller pool size per user
                    minPoolSize=1
                )
                # Test the connection
                client.admin.command('ping')
                return True, None, client
            else:
                # SQL databases with connection pooling and timeout configurations
                connect_args = {}

                # Add PostgreSQL-specific timeout configurations
                # pg8000 only supports basic connection parameters - PostgreSQL settings must be set via SQL
                if database.db_type in (DatabaseType.POSTGRESQL, DatabaseType.SUPABASE):
                    connect_args.update({
                        'timeout': 30,  # pg8000 connection timeout parameter
                        'application_name': 'AgentReport_Query_Pipeline'  # pg8000 application name parameter
                    })

                engine = create_engine(
                    connection_string,
                    poolclass=QueuePool,
                    pool_size=5,        # Smaller pool size per user
                    max_overflow=10,    # Reduced overflow
                    pool_pre_ping=True,
                    pool_recycle=1800,  # Recycle connections after 30 minutes (reduced from 1 hour)
                    pool_timeout=30,    # 30 second timeout to get connection from pool
                    echo=False,
                    connect_args=connect_args
                )

                # Test the connection
                with engine.connect() as conn:
                    conn.execute(text("SELECT 1"))

                return True, None, engine

        except Exception as e:
            logger.error(f"Error creating connection pool for user {user_id}, database {database.id}: {str(e)}")
            return False, str(e), None

    def _cleanup_idle_pools(self):
        """Clean up idle connection pools that haven't been accessed recently."""
        current_time = datetime.utcnow()

        with self.pool_cleanup_lock:
            users_to_remove = []

            for user_id, user_pools in self.user_connection_pools.items():
                pools_to_remove = []

                for db_id, pool_info in user_pools.items():
                    # Check if pool has been idle for too long
                    idle_time = current_time - pool_info.last_accessed

                    if idle_time > self.POOL_IDLE_TIMEOUT:
                        logger.info(
                            f"Cleaning up idle connection pool for user {user_id}, "
                            f"database {db_id} (idle for {idle_time})"
                        )

                        # Dispose the connection pool
                        try:
                            if hasattr(pool_info.pool, 'dispose'):
                                pool_info.pool.dispose()
                            elif hasattr(pool_info.pool, 'close'):
                                pool_info.pool.close()
                        except Exception as e:
                            logger.error(f"Error disposing connection pool: {e}")

                        pools_to_remove.append(db_id)

                # Remove idle pools
                for db_id in pools_to_remove:
                    del user_pools[db_id]

                # If user has no pools left, mark for removal
                if not user_pools:
                    users_to_remove.append(user_id)

            # Remove users with no pools
            for user_id in users_to_remove:
                del self.user_connection_pools[user_id]
                logger.debug(f"Removed empty pool collection for user {user_id}")

        self.last_cleanup = current_time

    def cleanup_user_pools(self, user_id: str, reason: str = "user_logout"):
        """Clean up all connection pools for a specific user.

        Args:
            user_id: The user ID whose pools should be cleaned up
            reason: Reason for cleanup (for logging)
        """
        with self.pool_cleanup_lock:
            if user_id in self.user_connection_pools:
                user_pools = self.user_connection_pools[user_id]

                logger.info(f"Cleaning up {len(user_pools)} connection pools for user {user_id} ({reason})")

                for db_id, pool_info in user_pools.items():
                    try:
                        if hasattr(pool_info.pool, 'dispose'):
                            pool_info.pool.dispose()
                        elif hasattr(pool_info.pool, 'close'):
                            pool_info.pool.close()
                        logger.debug(f"Disposed connection pool for user {user_id}, database {db_id}")
                    except Exception as e:
                        logger.error(f"Error disposing connection pool for user {user_id}, database {db_id}: {e}")

                del self.user_connection_pools[user_id]
                logger.info(f"Completed cleanup for user {user_id}")

    def cleanup_expired_user_pools(self, expired_user_ids: List[str]):
        """Clean up connection pools for users with expired tokens.

        Args:
            expired_user_ids: List of user IDs with expired tokens
        """
        for user_id in expired_user_ids:
            self.cleanup_user_pools(user_id, "token_expired")

    def get_user_pool_stats(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Get statistics about connection pools.

        Args:
            user_id: Optional user ID to get stats for specific user

        Returns:
            Dictionary with pool statistics
        """
        with self.pool_cleanup_lock:
            if user_id:
                if user_id in self.user_connection_pools:
                    user_pools = self.user_connection_pools[user_id]
                    return {
                        "user_id": user_id,
                        "pool_count": len(user_pools),
                        "databases": list(user_pools.keys()),
                        "pools": {
                            db_id: {
                                "created_at": pool_info.created_at.isoformat(),
                                "last_accessed": pool_info.last_accessed.isoformat(),
                                "access_count": pool_info.access_count
                            }
                            for db_id, pool_info in user_pools.items()
                        }
                    }
                else:
                    return {"user_id": user_id, "pool_count": 0, "databases": [], "pools": {}}
            else:
                # Global stats
                total_pools = sum(len(pools) for pools in self.user_connection_pools.values())
                return {
                    "total_users": len(self.user_connection_pools),
                    "total_pools": total_pools,
                    "users": {
                        user_id: len(pools)
                        for user_id, pools in self.user_connection_pools.items()
                    }
                }
        
    async def connect_database(
        self,
        database: Database,
        user_id: Optional[str] = None
    ) -> Tuple[bool, Optional[str]]:
        """Connect to a database with user-session-aware connection pooling.

        Args:
            database: The database to connect to
            user_id: Optional user ID for session-aware pooling

        Returns:
            A tuple of (success, error_message)
        """
        try:
            # If user_id is provided, use user-session-aware pooling
            if user_id:
                success, error, pool = await self.get_user_connection_pool(user_id, database)
                if success and pool:
                    # Store in legacy connections dict for backward compatibility
                    self.connections[database.id] = pool
                    return True, None
                else:
                    return False, error

            # Legacy connection method for backward compatibility
            # Check if we already have a connection pool for this database
            if database.id in self.connections:
                logger.info(f"Reusing existing legacy connection for database {database.id}")
                return True, None

            # Validate Supabase connection details if applicable
            if database.db_type == DatabaseType.SUPABASE:
                self._validate_supabase_connection(database)

            connection_string = self._build_connection_string(database)

            if database.db_type == DatabaseType.MONGODB:
                # MongoDB uses a different connection approach
                from pymongo import MongoClient
                client = MongoClient(connection_string, maxPoolSize=10, minPoolSize=2)
                self.connections[database.id] = client
            else:
                # SQL databases with connection pooling and timeout configurations
                connect_args = {}

                # Add PostgreSQL-specific timeout configurations for legacy connections
                # pg8000 only supports basic connection parameters - PostgreSQL settings must be set via SQL
                if database.db_type in (DatabaseType.POSTGRESQL, DatabaseType.SUPABASE):
                    connect_args.update({
                        'timeout': 30,  # pg8000 connection timeout parameter
                        'application_name': 'AgentReport_Legacy_Connection'  # pg8000 application name parameter
                    })

                engine = create_engine(
                    connection_string,
                    poolclass=QueuePool,
                    pool_size=10,
                    max_overflow=20,
                    pool_pre_ping=True,
                    pool_recycle=1800,  # Recycle connections after 30 minutes (reduced from 1 hour)
                    pool_timeout=30,    # 30 second timeout to get connection from pool
                    echo=False,
                    connect_args=connect_args
                )

                # Test the connection
                connection = engine.connect()
                connection.close()  # Return to pool immediately

                self.connections[database.id] = engine

            logger.info(f"Created legacy connection pool for database {database.id}")
            return True, None

        except Exception as e:
            logger.error(f"Error connecting to database {database.id}: {str(e)}")
            return False, str(e)
    
    def _validate_supabase_connection(self, database: Database) -> None:
        """Validate Supabase connection details.
        
        Args:
            database: The database to validate
            
        Raises:
            ValueError: If the connection details are invalid
        """
        creds = database.credentials
        
        # Check if host is in the expected format for Supabase
        if not creds.host.endswith('supabase.co'):
            logger.warning(f"Supabase host does not end with 'supabase.co': {creds.host}")
            # Don't raise an error, just log a warning - user might be using a custom domain
            
        # Typically Supabase uses port 5432 (PostgreSQL default)
        if creds.port != 5432:
            logger.warning(f"Unusual port for Supabase: {creds.port}. Default is 5432.")
            
        # Default database name for Supabase is usually 'postgres'
        if creds.database != 'postgres':
            logger.info(f"Using non-default database name for Supabase: {creds.database}")
            
        # Default username for Supabase is 'postgres'
        if creds.username != 'postgres':
            logger.info(f"Using non-default username for Supabase: {creds.username}")
    
    def _build_connection_string(self, database: Database) -> str:
        """Build a connection string for a database.
        
        Args:
            database: The database to build a connection string for
            
        Returns:
            The connection string
        """
        creds = database.credentials
        
        if database.connection_string:
            return database.connection_string
            
        if database.db_type in (DatabaseType.POSTGRESQL, DatabaseType.SUPABASE):
            # Both PostgreSQL and Supabase use the same connection string format with pg8000 driver
            return f"postgresql+pg8000://{creds.username}:{creds.password}@{creds.host}:{creds.port}/{creds.database}"
            
        elif database.db_type == DatabaseType.MYSQL:
            return f"mysql+pymysql://{creds.username}:{creds.password}@{creds.host}:{creds.port}/{creds.database}"
            
        elif database.db_type == DatabaseType.SQLSERVER:
            return f"mssql+pyodbc://{creds.username}:{creds.password}@{creds.host}:{creds.port}/{creds.database}?driver=ODBC+Driver+17+for+SQL+Server"
            
        elif database.db_type == DatabaseType.ORACLE:
            return f"oracle+oracledb://{creds.username}:{creds.password}@{creds.host}:{creds.port}/{creds.database}"
            
        elif database.db_type == DatabaseType.MONGODB:
            return f"mongodb://{creds.username}:{creds.password}@{creds.host}:{creds.port}/{creds.database}"
            
        else:
            raise ValueError(f"Unsupported database type: {database.db_type}")
    
    async def list_tables(self, database_id: str, schema: Optional[str] = None) -> List[str]:
        """List tables in a database with caching.

        Args:
            database_id: The ID of the database
            schema: The schema to list tables from

        Returns:
            A list of table names
        """
        # Check cache first
        cache_key = f"{database_id}:tables:{schema or 'default'}"
        if self._is_cache_valid(cache_key):
            logger.debug(f"Using cached table list for {database_id}")
            return self.schema_cache[cache_key]["data"]

        connection = self.connections.get(database_id)
        if not connection:
            raise ValueError(f"Database {database_id} is not connected")

        try:
            if isinstance(connection, sqlalchemy.engine.Engine):
                # Use connection pool
                with connection.connect() as conn:
                    inspector = inspect(conn)
                    tables = inspector.get_table_names(schema=schema)
            elif isinstance(connection, sqlalchemy.engine.base.Connection):
                inspector = inspect(connection.engine)
                tables = inspector.get_table_names(schema=schema)
            else:
                # MongoDB
                tables = connection[database_id].list_collection_names()

            # Cache the result
            self._cache_data(cache_key, tables)
            return tables

        except Exception as e:
            logger.error(f"Error listing tables for database {database_id}: {str(e)}")
            raise
    
    async def get_table_metadata(self, database_id: str, table_name: str, schema: Optional[str] = None) -> DatabaseTable:
        """Get metadata for a table.
        
        Args:
            database_id: The ID of the database
            table_name: The name of the table
            schema: The schema the table belongs to
            
        Returns:
            A DatabaseTable object
        """
        connection = self.connections.get(database_id)
        if not connection:
            raise ValueError(f"Database {database_id} is not connected")
            
        try:
            table = DatabaseTable(name=table_name, schema=schema)

            # Handle both Engine and Connection objects for SQL databases
            if isinstance(connection, (sqlalchemy.engine.base.Connection, sqlalchemy.engine.Engine)):
                # Get inspector from either connection or engine
                if isinstance(connection, sqlalchemy.engine.Engine):
                    inspector = inspect(connection)
                else:
                    inspector = inspect(connection.engine)
                
                # Get primary key information safely
                try:
                    pk_constraint = inspector.get_pk_constraint(table_name, schema=schema)
                    pk_columns = []
                    if pk_constraint and isinstance(pk_constraint, dict):
                        pk_columns = pk_constraint.get('constrained_columns', [])
                except Exception as pk_error:
                    logger.warning(f"Error getting primary key info for {table_name}: {str(pk_error)}")
                    pk_columns = []
                
                # Get columns
                columns = []
                for column in inspector.get_columns(table_name, schema=schema):
                    # Safely extract column info
                    col_name = column.get('name', '')
                    col_type = str(column.get('type', 'unknown'))
                    is_nullable = column.get('nullable', True)
                    default_val = column.get('default')
                    
                    # Check if this column is a primary key
                    is_pk = col_name in pk_columns
                    
                    db_column = DatabaseColumn(
                        name=col_name,
                        data_type=col_type,
                        is_nullable=is_nullable,
                        is_primary_key=is_pk,
                        default_value=str(default_val) if default_val else None
                    )
                    columns.append(db_column)
                
                table.columns = columns
                
                # Set primary keys
                table.primary_keys = pk_columns
                
                # Get foreign keys safely
                fks = []
                try:
                    foreign_keys = inspector.get_foreign_keys(table_name, schema=schema)
                    for fk in foreign_keys:
                        if isinstance(fk, dict):
                            constrained_cols = fk.get('constrained_columns', [])
                            referred_cols = fk.get('referred_columns', [])
                            
                            for i, col in enumerate(constrained_cols):
                                if i < len(referred_cols):
                                    ref_col = referred_cols[i]
                                    fks.append(ForeignKeyRelationship(
                                        column_name=col,
                                        referenced_table_name=fk.get('referred_table', ''),
                                        referenced_column_name=ref_col,
                                        referenced_schema=fk.get('referred_schema')
                                    ))
                                    
                                    # Mark column as FK
                                    for column in table.columns:
                                        if column.name == col:
                                            column.is_foreign_key = True
                except Exception as fk_error:
                    logger.warning(f"Error getting foreign key info for {table_name}: {str(fk_error)}")
                
                table.foreign_keys = fks
                
                # Try to get row count
                try:
                    if isinstance(connection, sqlalchemy.engine.Engine):
                        # Use connection pool
                        with connection.connect() as conn:
                            result = conn.execute(text(f"SELECT COUNT(*) FROM {schema+'.' if schema else ''}{table_name}"))
                            table.row_count = result.scalar()
                    else:
                        # Direct connection
                        result = connection.execute(text(f"SELECT COUNT(*) FROM {schema+'.' if schema else ''}{table_name}"))
                        table.row_count = result.scalar()
                except Exception as count_error:
                    logger.warning(f"Error getting row count for {table_name}: {str(count_error)}")
                    # Row count isn't essential, so don't fail if we can't get it
                    pass
                    
            else:
                # MongoDB
                try:
                    db = connection[database_id]
                    collection = db[table_name]
                    
                    # Get a sample document to infer schema
                    sample = collection.find_one()
                    columns = []
                    
                    if sample and isinstance(sample, dict):
                        for key, value in sample.items():
                            columns.append(DatabaseColumn(
                                name=key,
                                data_type=type(value).__name__,
                                is_primary_key=key == '_id'
                            ))
                    
                    table.columns = columns
                    table.primary_keys = ['_id']
                    
                    # Try to get document count
                    try:
                        table.row_count = collection.count_documents({})
                    except Exception as count_error:
                        logger.warning(f"Error getting document count for {table_name}: {str(count_error)}")
                except Exception as mongo_error:
                    logger.warning(f"Error processing MongoDB collection {table_name}: {str(mongo_error)}")
                    # Create empty column list to avoid further errors
                    table.columns = []
            
            return table
                
        except Exception as e:
            logger.error(f"Error getting metadata for table {table_name} in database {database_id}: {str(e)}")
            raise
    
    async def execute_query(
        self,
        database_id: str,
        query: str,
        params: Optional[Dict[str, Any]] = None
    ) -> Union[pd.DataFrame, List[Dict[str, Any]]]:
        """Execute a query on a database with proper transaction management.

        Args:
            database_id: The ID of the database
            query: The query to execute
            params: Parameters for the query

        Returns:
            The query results as a pandas DataFrame or list of dicts
        """
        connection = self.connections.get(database_id)
        if not connection:
            raise ValueError(f"Database {database_id} is not connected")

        try:
            if isinstance(connection, (sqlalchemy.engine.base.Connection, sqlalchemy.engine.Engine)):
                # SQL query with transaction management
                return await self._execute_sql_query(connection, database_id, query, params)
            else:
                # MongoDB query - expect a JSON string
                return await self._execute_mongodb_query(connection, database_id, query)

        except Exception as e:
            logger.error(f"Error executing query on database {database_id}: {str(e)}")
            # For SQL connections, attempt to recover from invalid transaction state
            if isinstance(connection, (sqlalchemy.engine.base.Connection, sqlalchemy.engine.Engine)):
                await self._recover_sql_connection(database_id, connection)
            raise

    async def _execute_sql_query(
        self,
        connection: Union[sqlalchemy.engine.base.Connection, sqlalchemy.engine.Engine],
        database_id: str,
        query: str,
        params: Optional[Dict[str, Any]] = None
    ) -> pd.DataFrame:
        """Execute SQL query with connection pooling, timeout handling, and proper transaction management."""
        import asyncio
        from sqlalchemy.exc import OperationalError, StatementError

        try:
            # Use connection pool if available
            if isinstance(connection, sqlalchemy.engine.Engine):
                with connection.connect() as conn:
                    # Set query timeout at connection level for PostgreSQL
                    if 'postgresql' in str(connection.url).lower():
                        try:
                            conn.execute(text("SET statement_timeout = '120s'"))
                        except Exception as timeout_error:
                            logger.warning(f"Could not set statement timeout: {timeout_error}")

                    # Execute query with asyncio timeout as additional safety
                    async def execute_query():
                        if params:
                            result = conn.execute(text(query), params)
                        else:
                            result = conn.execute(text(query))

                        # Fetch results and convert to DataFrame
                        rows = result.fetchall()
                        columns = result.keys()
                        return pd.DataFrame(rows, columns=columns)

                    # Add asyncio timeout as additional protection (150 seconds total)
                    return await asyncio.wait_for(execute_query(), timeout=150.0)

            else:
                # Legacy connection handling with timeout
                if 'postgresql' in str(getattr(connection, 'engine', {}).url or '').lower():
                    try:
                        connection.execute(text("SET statement_timeout = '120s'"))
                    except Exception as timeout_error:
                        logger.warning(f"Could not set statement timeout: {timeout_error}")

                async def execute_legacy_query():
                    if params:
                        result = connection.execute(text(query), params)
                    else:
                        result = connection.execute(text(query))

                    # Fetch results and convert to DataFrame
                    rows = result.fetchall()
                    columns = result.keys()
                    return pd.DataFrame(rows, columns=columns)

                # Add asyncio timeout as additional protection (150 seconds total)
                return await asyncio.wait_for(execute_legacy_query(), timeout=150.0)

        except asyncio.TimeoutError:
            logger.error(f"Query execution timed out after 150 seconds for database {database_id}")
            raise Exception(f"Query execution timed out. The query took longer than 150 seconds to complete.")
        except OperationalError as e:
            error_msg = str(e).lower()
            if '57014' in error_msg or 'statement timeout' in error_msg or 'canceling statement' in error_msg:
                logger.error(f"PostgreSQL statement timeout (57014) for database {database_id}: {e}")
                raise Exception(f"Query timed out due to PostgreSQL statement timeout. Please simplify your query or add more specific filters.")
            elif 'connection' in error_msg and ('reset' in error_msg or 'closed' in error_msg):
                logger.error(f"Connection error for database {database_id}: {e}")
                raise Exception(f"Database connection was reset. Please retry your query.")
            else:
                logger.error(f"Operational error for database {database_id}: {e}")
                raise
        except StatementError as e:
            logger.error(f"Statement error for database {database_id}: {e}")
            raise Exception(f"SQL statement error: {str(e)}")
        except Exception as e:
            # If there's an active transaction and it failed, try to rollback
            if hasattr(connection, '_transaction') and connection._transaction:
                try:
                    connection.rollback()
                    logger.info(f"Transaction rolled back for database {database_id}")
                except Exception as rollback_error:
                    logger.error(f"Error during rollback for database {database_id}: {rollback_error}")

            # Re-raise with more context
            error_msg = str(e).lower()
            if 'timeout' in error_msg:
                raise Exception(f"Query execution timed out: {str(e)}")
            else:
                raise e

    async def _execute_mongodb_query(
        self,
        connection,
        database_id: str,
        query: str
    ) -> List[Dict[str, Any]]:
        """Execute MongoDB query."""
        import json
        query_dict = json.loads(query)
        db = connection[database_id]

        if 'collection' not in query_dict:
            raise ValueError("MongoDB query must specify a 'collection'")

        collection = db[query_dict['collection']]

        if 'find' in query_dict:
            # Find query
            cursor = collection.find(query_dict['find'])
            return list(cursor)
        elif 'aggregate' in query_dict:
            # Aggregation pipeline
            cursor = collection.aggregate(query_dict['aggregate'])
            return list(cursor)
        else:
            raise ValueError("MongoDB query must contain either 'find' or 'aggregate'")

    async def _recover_sql_connection(
        self,
        database_id: str,
        connection: Union[sqlalchemy.engine.base.Connection, sqlalchemy.engine.Engine]
    ) -> None:
        """Attempt to recover from invalid transaction state."""
        try:
            # Handle Engine objects (connection pools)
            if isinstance(connection, sqlalchemy.engine.Engine):
                # For Engine objects, test the connection pool
                try:
                    with connection.connect() as conn:
                        conn.execute(text("SELECT 1"))
                    logger.info(f"Connection pool test successful for database {database_id}")
                    return
                except Exception as test_error:
                    logger.warning(f"Connection pool test failed for database {database_id}: {test_error}")
                    await self._recreate_connection(database_id)
                    return

            # Handle direct Connection objects
            # Check if connection is in an invalid transaction state
            if hasattr(connection, '_transaction') and connection._transaction:
                logger.warning(f"Attempting to recover invalid transaction for database {database_id}")
                # Force rollback any pending transaction
                try:
                    connection.rollback()
                    logger.info(f"Successfully rolled back invalid transaction for database {database_id}")
                    return  # Recovery successful
                except Exception as rollback_error:
                    logger.error(f"Failed to rollback invalid transaction for database {database_id}: {rollback_error}")
                    # If rollback fails, we need to recreate the connection
                    await self._recreate_connection(database_id)
                    return

            # If no active transaction, check if connection is still usable
            try:
                # Test the connection with a simple query
                connection.execute(text("SELECT 1"))
                logger.info(f"Connection test successful for database {database_id}")
            except Exception as test_error:
                logger.warning(f"Connection test failed for database {database_id}: {test_error}")
                await self._recreate_connection(database_id)

        except Exception as recovery_error:
            logger.error(f"Error during connection recovery for database {database_id}: {recovery_error}")
            # As a last resort, recreate the connection
            await self._recreate_connection(database_id)

    async def _recreate_connection(self, database_id: str) -> None:
        """Recreate a database connection when recovery fails."""
        try:
            logger.warning(f"Recreating connection for database {database_id}")

            # Close and remove the invalid connection
            old_connection = self.connections.get(database_id)
            if old_connection:
                try:
                    if isinstance(old_connection, sqlalchemy.engine.Engine):
                        old_connection.dispose()
                    else:
                        old_connection.close()
                except Exception:
                    pass  # Ignore errors when closing invalid connection

                # Remove from connections dict and connection pools
                del self.connections[database_id]
                self.connection_pools.pop(database_id, None)
                logger.info(f"Removed invalid connection for database {database_id}")

        except Exception as e:
            logger.error(f"Error recreating connection for database {database_id}: {e}")

    def is_connection_valid(self, database_id: str) -> bool:
        """Check if a database connection is valid and ready for queries."""
        connection = self.connections.get(database_id)
        if not connection:
            return False

        try:
            if isinstance(connection, sqlalchemy.engine.Engine):
                # For Engine objects (connection pools), test by getting a connection
                try:
                    with connection.connect() as conn:
                        conn.execute(text("SELECT 1"))
                    return True
                except Exception as query_error:
                    logger.warning(f"Connection pool test failed for database {database_id}: {query_error}")
                    return False
            elif isinstance(connection, sqlalchemy.engine.base.Connection):
                # For SQL connections, check if connection is closed or in invalid state
                if connection.closed:
                    return False

                # Check for invalid transaction state
                if hasattr(connection, '_transaction') and connection._transaction:
                    # If there's an active transaction, check if it's in a valid state
                    try:
                        # Try to check transaction state without executing a query
                        if hasattr(connection._transaction, '_is_active'):
                            if not connection._transaction._is_active:
                                logger.warning(f"Inactive transaction detected for database {database_id}")
                                return False
                    except Exception:
                        # If we can't check transaction state, assume it's invalid
                        logger.warning(f"Could not check transaction state for database {database_id}")
                        return False

                # Try a simple query to test the connection (without starting a new transaction)
                try:
                    # Use autocommit mode for the test query
                    with connection.begin() as trans:
                        connection.execute(text("SELECT 1"))
                        trans.commit()
                    return True
                except Exception as query_error:
                    logger.warning(f"Test query failed for database {database_id}: {query_error}")
                    return False
            else:
                # For MongoDB, check if client is still connected
                # MongoDB connections are more resilient, so we assume they're valid
                return True
        except Exception as e:
            logger.warning(f"Connection validation failed for database {database_id}: {e}")
            return False

    async def ensure_connection(self, database: Database) -> bool:
        """Ensure a database connection is valid, reconnecting if necessary."""
        if self.is_connection_valid(database.id):
            return True

        logger.info(f"Reconnecting to database {database.id}")
        # Remove invalid connection
        if database.id in self.connections:
            await self._recreate_connection(database.id)

        # Reconnect
        success, error = await self.connect_database(database)
        if not success:
            logger.error(f"Failed to reconnect to database {database.id}: {error}")
            return False

        return True
            
    def disconnect_database(self, database_id: str) -> None:
        """Disconnect from a database.
        
        Args:
            database_id: The ID of the database
        """
        connection = self.connections.get(database_id)
        if connection:
            try:
                if isinstance(connection, sqlalchemy.engine.Engine):
                    # For Engine objects, dispose the connection pool
                    connection.dispose()
                elif isinstance(connection, sqlalchemy.engine.base.Connection):
                    connection.close()
                else:
                    # MongoDB
                    connection.close()

                del self.connections[database_id]
                # Also remove from connection pools if it exists
                self.connection_pools.pop(database_id, None)
            except Exception as e:
                logger.error(f"Error disconnecting from database {database_id}: {str(e)}")
                
    def disconnect_all(self) -> None:
        """Disconnect from all databases."""
        database_ids = list(self.connections.keys())
        for database_id in database_ids:
            self.disconnect_database(database_id)

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self.schema_cache:
            return False

        import time
        cache_time = self.cache_ttl.get(cache_key, 0)
        return time.time() - cache_time < self.CACHE_DURATION

    def _cache_data(self, cache_key: str, data: Any) -> None:
        """Cache data with timestamp."""
        import time
        self.schema_cache[cache_key] = {"data": data}
        self.cache_ttl[cache_key] = time.time()

    def clear_cache(self, database_id: Optional[str] = None) -> None:
        """Clear schema cache for a specific database or all databases."""
        if database_id:
            keys_to_remove = [k for k in self.schema_cache.keys() if k.startswith(f"{database_id}:")]
            for key in keys_to_remove:
                self.schema_cache.pop(key, None)
                self.cache_ttl.pop(key, None)
        else:
            self.schema_cache.clear()
            self.cache_ttl.clear()
