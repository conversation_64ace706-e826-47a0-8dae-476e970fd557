exports.id=695,exports.ids=[695],exports.modules={8717:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},17091:(e,t,r)=>{"use strict";r.d(t,{ChatHistoryProvider:()=>o});var a=r(12907);let o=(0,a.registerClientReference)(function(){throw Error("Attempted to call ChatHistoryProvider() from the server but ChatHistoryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\providers\\ChatHistoryContext.tsx","ChatHistoryProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call useChatHistory() from the server but useChatHistory is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\providers\\ChatHistoryContext.tsx","useChatHistory")},17223:(e,t,r)=>{"use strict";r.d(t,{_:()=>l,s:()=>c});var a=r(60687),o=r(43210);let s={title:"Agent Platform",subtitle:"AI-Powered Data Analytics"},n={onCreateChart:void 0,chartCount:0,maxCharts:12},i=(0,o.createContext)(void 0),l=({children:e})=>{let[t,r]=(0,o.useState)(s),[l,c]=(0,o.useState)(n),d=(0,o.useCallback)(e=>{r(e)},[]),u=(0,o.useCallback)(e=>{c(e)},[]),h=(0,o.useCallback)(()=>{r(s),c(n)},[]);return(0,a.jsx)(i.Provider,{value:{pageInfo:t,actions:l,setPageHeader:d,setPageActions:u,resetPageHeader:h},children:e})},c=()=>{let e=(0,o.useContext)(i);if(void 0===e)throw Error("usePageHeader must be used within a PageHeaderProvider");return e}},17849:(e,t,r)=>{"use strict";r.d(t,{G:()=>i,M:()=>l});var a=r(60687),o=r(43210),s=r(51851);let n=(0,o.createContext)(void 0),i=()=>{let e=(0,o.useContext)(n);if(!e)throw Error("useDataSources must be used within a DataSourcesProvider");return e},l=({children:e})=>{let{listDatabases:t,disconnectExistingDatabase:r}=(0,s.g)(),[i,l]=(0,o.useState)([]),[c,d]=(0,o.useState)(!1),[u,h]=(0,o.useState)(null),[g,p]=(0,o.useState)(!1),m=(0,o.useCallback)(e=>({id:e.id,name:e.name,type:"database",description:e.description||`${e.type} database`,isConnected:!0}),[]),E=(0,o.useCallback)(async()=>{d(!0),h(null);try{console.log("Fetching connected databases...");let e=(await t()).map(m);l(e),p(!0)}catch(e){console.error("Failed to fetch connected databases:",e),h(e instanceof Error?e.message:"Failed to fetch connected databases"),l([]),p(!0)}finally{d(!1)}},[t,m]);(0,o.useEffect)(()=>{g||E()},[E,g]);let f=i.filter(e=>e.isConnected),w=(0,o.useCallback)(async e=>{console.log("Connect data source not implemented - use the Data Sources page"),h("Please use the Data Sources page to connect new databases")},[]),S=(0,o.useCallback)(async e=>{d(!0),h(null);try{await r(e),await E()}catch(e){h(e instanceof Error?e.message:"Failed to disconnect data source"),d(!1)}},[r,E]),y=(0,o.useCallback)(e=>i.find(t=>t.id===e),[i]),C=(0,o.useCallback)(async()=>{await E()},[E]);return(0,a.jsx)(n.Provider,{value:{dataSources:i,connectedDataSources:f,isLoading:c,error:u,connectDataSource:w,disconnectDataSource:S,getDataSource:y,refreshDataSources:C},children:e})}},20625:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\NXTHUMANS\\\\internal-solutions-nxtwebsite\\\\AgentReport 2\\\\AgentReportUI\\\\src\\\\components\\\\common\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\components\\common\\ClientLayout.tsx","default")},44297:(e,t,r)=>{"use strict";r.d(t,{ChatHistoryProvider:()=>c,m:()=>d});var a=r(60687),o=r(43210),s=r(51060),n=r(91010),i=r(51851);let l=(0,o.createContext)(void 0),c=({children:e})=>{let{user:t,isAuthenticated:r,logout:c,isLoading:d}=(0,n.A)(),{listUserChats:u,getChatHistory:h,deleteChat:g}=(0,i.g)(),[p,m]=(0,o.useState)([]),[E,f]=(0,o.useState)(null),[w,S]=(0,o.useState)({}),[y,C]=(0,o.useState)(!1),[A,_]=(0,o.useState)(!1),[b,T]=(0,o.useState)(null),[k,I]=(0,o.useState)(!1),v=(0,o.useRef)(new Set),R=(0,o.useRef)(!1),N=(0,o.useRef)(!1),D=(0,o.useCallback)(()=>{let e="sess_";for(let t=0;t<8;t++)e+="0123456789abcdef"[Math.floor(16*Math.random())];return e},[]),O=(0,o.useCallback)(e=>{let t=new Date(e.last_seen);return{id:`chat_${e.session_id}`,session_id:e.session_id,title:e.title||"Untitled Chat",created_at:t,last_updated:t,message_count:e.message_count||0}},[]),P=(0,o.useCallback)(e=>({role:"assistant"===e.role?"agent":e.role,content:e.content,timestamp:new Date}),[]),x=(0,o.useCallback)(async e=>{if(r&&t){if(v.current.has(e))return void console.log(`Load for session ${e} already in progress, skipping.`);_(!0),v.current.add(e);try{let t=(await h(e)).map(P);S(r=>({...r,[e]:t})),m(r=>r.map(r=>r.session_id===e?{...r,message_count:t.length,last_updated:new Date}:r))}catch(t){if(console.error("Error loading chat history from backend:",t),s.A.isAxiosError(t)&&t.response?.status===401){console.log("Authentication token invalid while loading chat history, logging out..."),c();return}s.A.isAxiosError(t)&&t.response?.status===404?(console.log("Chat history not found (404) - this is normal for new chats"),S(t=>({...t,[e]:[]})),m(t=>t.map(t=>t.session_id===e?{...t,message_count:0,last_updated:new Date}:t))):(console.error("Unexpected error loading chat history:",t),S(t=>({...t,[e]:[]})),m(t=>t.map(t=>t.session_id===e?{...t,message_count:0,last_updated:new Date}:t)))}finally{_(!1),v.current.delete(e)}}},[r,t,c,h,P]),$=(0,o.useCallback)(async e=>{if(console.log("setActiveChat called with:",e),N.current)return void console.log("setActiveChat already in progress, ignoring call");if(null===e)return void f(null);if(E&&E.id===e.id)return void console.log("Chat is already active, skipping to prevent duplicate calls");N.current=!0;try{f(e);let t=(w[e.session_id]||[]).length>0;A||t||!e.session_id?console.log("Skipping history load - either already loaded or new chat (no session_id yet)"):(console.log("Loading chat history for session:",e.session_id),await x(e.session_id))}finally{N.current=!1}},[x,w,E,A]),K=(0,o.useCallback)(e=>{if(k){console.log("Chat creation already in progress, returning existing or throwing error");let e=p[0];if(e)return e;throw Error("Chat creation already in progress")}I(!0);try{let t=D(),r=`chat_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,a=new Date,o={id:r,session_id:t,title:e||"New Chat",created_at:a,last_updated:a,message_count:0};return m(e=>[o,...e]),o}finally{setTimeout(()=>{I(!1)},500)}},[D,k,p]),U=(0,o.useCallback)((e,t)=>{let r,a=null;return m(o=>o.map(o=>o.id===e?(r=o.session_id,a={...o,session_id:t}):o)),r&&r!==t&&S(e=>{let a=e[r]||[],o={...e};return delete o[r],o[t]=a,o}),a},[]),H=(0,o.useCallback)(e=>{m(t=>{let r=t.find(t=>t.id===e);return r?(S(e=>{let t={...e};return delete t[r.session_id],t}),f(t=>t?.id===e?null:t),t.filter(t=>t.id!==e)):t})},[]),L=(0,o.useCallback)(async e=>{if(!r||!t)return;let a=p.find(t=>t.id===e);if(!a)return void console.warn("Chat not found for deletion:",e);try{await g(a.session_id),console.log("Chat deleted successfully from backend:",a.session_id),H(e)}catch(t){if(console.error("Error deleting chat from backend:",t),s.A.isAxiosError(t)&&t.response?.status===401){console.log("Authentication token invalid while deleting chat, logging out..."),c();return}if(s.A.isAxiosError(t)&&t.response?.status===404){console.log("Chat not found on backend (404) - removing locally"),H(e);return}throw t}},[r,t,p,g,H,c]),j=(0,o.useCallback)((e,t)=>{m(r=>r.map(r=>r.id===e?{...r,title:t,last_updated:new Date}:r))},[]),M=(0,o.useCallback)(e=>{let t=e.trim().substring(0,50);return t=(t=t.replace(/^(what|how|when|where|why|who|can|could|would|should|is|are|do|does|did)\s+/i,"")).charAt(0).toUpperCase()+t.slice(1),e.length>50&&(t+="..."),t||"New Chat"},[]),B=(0,o.useCallback)((e,t)=>{let r={...t,timestamp:t.timestamp||new Date};S(t=>({...t,[e]:[...t[e]||[],r]})),m(t=>t.map(t=>t.session_id===e?{...t,last_updated:new Date,message_count:t.message_count+1}:t)),"user"===t.role&&m(r=>r.map(r=>{if(r.session_id===e&&("New Chat"===r.title||"Untitled Chat"===r.title)){let e=M(t.content);return{...r,title:e}}return r}))},[M]);(0,o.useEffect)(()=>{if(console.log("ChatHistoryContext useEffect triggered:",{authLoading:d,isAuthenticated:r,hasAccessToken:!!t?.access_token,userId:t?.user_id,hasLoadedChats:R.current}),d||!r||!t?.access_token){console.log("Skipping chat load - auth loading or not authenticated"),d||r||(m([]),S({}),f(null),R.current=!1);return}if(R.current)return void console.log("Chats already loaded for this session, skipping...");console.log("Loading chats for authenticated user..."),R.current=!0,C(!0),u().then(e=>{console.log("Successfully loaded chats from backend:",e.length);let t=e.map(O);t.sort((e,t)=>t.last_updated.getTime()-e.last_updated.getTime()),m(t)}).catch(e=>{if(console.error("Error loading chats from backend:",e),R.current=!1,s.A.isAxiosError(e)&&e.response?.status===401){console.log("401 error while loading chats, logging out..."),c();return}m([])}).finally(()=>{C(!1)})},[d,r,t?.access_token,t?.user_id]);let Y=(0,o.useCallback)(async()=>{if(r&&t?.access_token)try{console.log("Refreshing chat list from backend...");let e=(await u()).map(O);e.sort((e,t)=>t.last_updated.getTime()-e.last_updated.getTime()),m(e),console.log("Chat list refreshed successfully")}catch(e){console.error("Error refreshing chat list:",e),s.A.isAxiosError(e)&&e.response?.status===401&&c()}},[r,t?.access_token]),z=(0,o.useCallback)(async e=>{console.log("Loading chat by ID:",e);let t=p.find(t=>t.id===e);return t?(console.log("Chat found in existing history:",t),await $(t),t):(y?console.log("Chats still loading, waiting..."):console.log("Chat not found in loaded chat history - chat may not exist:",e),null)},[p,y,$]);return(0,a.jsx)(l.Provider,{value:{chatHistory:p,activeChat:E,chatMessages:w,isLoadingChats:y,isLoadingHistory:A,pendingFirstMessage:b,setPendingFirstMessage:T,addChat:K,updateChatSessionId:U,deleteChat:L,renameChat:j,setActiveChat:$,loadChatById:z,addMessageToChat:B,loadChatHistory:x,refreshChatList:Y,generateSessionId:D},children:e})},d=()=>{let e=(0,o.useContext)(l);if(!e)throw Error("useChatHistory must be used within a ChatHistoryProvider");return e}},50169:(e,t,r)=>{Promise.resolve().then(r.bind(r,20625)),Promise.resolve().then(r.bind(r,17091)),Promise.resolve().then(r.bind(r,62395))},51851:(e,t,r)=>{"use strict";r.d(t,{K:()=>l,g:()=>c});var a=r(60687),o=r(43210),s=r(51060),n=r(97616);let i=(0,o.createContext)(void 0),l=({children:e})=>{let t=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");let r=new FormData;r.append("query",e.query),r.append("output_format",e.output_format||"excel"),e.session_id&&r.append("session_id",e.session_id),e.target_databases&&r.append("target_databases",JSON.stringify(e.target_databases)),e.target_tables&&r.append("target_tables",JSON.stringify(e.target_tables)),e.target_columns&&r.append("target_columns",JSON.stringify(e.target_columns)),e.enable_token_streaming&&r.append("enable_token_streaming","true");let a={Authorization:`Bearer ${t}`};return(await s.A.post(`${(0,n.hY)()}/ask/question`,r,{headers:a})).data}catch(e){throw console.error("API error (queryDatabases):",e),e}},[]),r=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN),r={"Content-Type":"application/json"};return t&&(r.Authorization=`Bearer ${t}`),(await s.A.post(`${(0,n.hY)()}/databases/schema`,{db_id:e},{headers:r})).data}catch(e){throw console.error("Error fetching database schema:",e),e}},[]),l=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!e)return console.log("No access token found, skipping database list request"),[];let t={"Content-Type":"application/json",Authorization:`Bearer ${e}`},r=await s.A.post(`${(0,n.hY)()}/databases/listdatabases`,{},{headers:t});return r.data.databases||r.data||[]}catch(e){throw console.error("Error listing databases:",e),e}},[]),c=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");let r={"Content-Type":"application/json",Authorization:`Bearer ${t}`};return(await s.A.post(`${(0,n.hY)()}/databases/connectdatabase`,e,{headers:r})).data}catch(e){throw console.error("Error connecting new database:",e),e}},[]),d=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN),r={"Content-Type":"application/json"};return t&&(r.Authorization=`Bearer ${t}`),(await s.A.post(`${(0,n.hY)()}/databases/disconnectdatabase`,{db_id:e},{headers:r})).data}catch(e){throw console.error("Error disconnecting database:",e),e}},[]),u=(0,o.useCallback)(async(e,t,r,a,o,i)=>{try{let r=localStorage.getItem(n.d5.ACCESS_TOKEN),l={"Content-Type":"application/json"};return r&&(l.Authorization=`Bearer ${r}`),(await s.A.post(`${(0,n.hY)()}/query/ask`,{query:e,output_format:t,target_databases:a,target_tables:o,target_columns:i},{headers:l})).data}catch(e){throw console.error("Error asking query:",e),e}},[]),h=(0,o.useCallback)(async e=>{try{let t=new URLSearchParams;t.append("username",e.username),t.append("password",e.password);let r=await s.A.post(`${(0,n.hY)()}/auth/login`,t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}});return r.data.access_token&&(localStorage.setItem(n.d5.ACCESS_TOKEN,r.data.access_token),console.log("Access token set in localStorage:",r.data.access_token),localStorage.setItem(n.d5.TOKEN_TYPE,r.data.token_type),localStorage.setItem(n.d5.REFRESH_TOKEN,r.data.refresh_token),localStorage.setItem(n.d5.USER_ID,r.data.user_id),localStorage.setItem(n.d5.EXPIRES_AT,r.data.expires_at)),r.data}catch(e){throw console.error("Error during login:",e),e}},[]),g=(0,o.useCallback)(async e=>{try{return(await s.A.post(`${(0,n.hY)()}${n.Sn.AUTH.REGISTER}`,{email:e.email,password:e.password,full_name:e.full_name},{headers:{"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error registering user:",e),e}},[]),p=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(n.d5.REFRESH_TOKEN);if(!e)throw Error("No refresh token found");let t=await s.A.post(`${(0,n.hY)()}/auth/refresh`,{refresh_token:e},{headers:{"Content-Type":"application/json"}});return t.data.access_token&&(localStorage.setItem(n.d5.ACCESS_TOKEN,t.data.access_token),localStorage.setItem(n.d5.TOKEN_TYPE,t.data.token_type),localStorage.setItem(n.d5.REFRESH_TOKEN,t.data.refresh_token),localStorage.setItem(n.d5.USER_ID,t.data.user_id),localStorage.setItem(n.d5.EXPIRES_AT,t.data.expires_at),console.log("Token refreshed successfully")),t.data}catch(e){throw console.error("Error refreshing token:",e),localStorage.removeItem(n.d5.ACCESS_TOKEN),localStorage.removeItem(n.d5.REFRESH_TOKEN),localStorage.removeItem(n.d5.USER_ID),localStorage.removeItem(n.d5.EXPIRES_AT),localStorage.removeItem(n.d5.TOKEN_TYPE),e}},[]),m=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(n.d5.ACCESS_TOKEN);e&&await s.A.post(`${(0,n.hY)()}/auth/logout`,{},{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}})}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem(n.d5.ACCESS_TOKEN),localStorage.removeItem(n.d5.REFRESH_TOKEN),localStorage.removeItem(n.d5.USER_ID),localStorage.removeItem(n.d5.EXPIRES_AT),localStorage.removeItem(n.d5.TOKEN_TYPE),console.log("User logged out and tokens cleared")}},[]),E=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!e)throw Error("No authentication token found");return(await s.A.post(`${(0,n.hY)()}/auth/me`,{},{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error fetching user profile:",e),e}},[]),f=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!e)return console.log("No access token found, skipping chat list request"),[];let t={"Content-Type":"application/json",Authorization:`Bearer ${e}`},r=`${(0,n.hY)()}/chats/listchats`;console.log("\uD83D\uDD17 Making request to:",r),console.log("\uD83D\uDD11 Using Bearer token:",e.substring(0,20)+"...");let a=await s.A.post(r,{},{headers:t});return console.log("✅ List chats response received:",a.status),a.data||[]}catch(e){throw console.error("❌ Error listing user chats:",e),s.A.isAxiosError(e)&&(console.error("\uD83D\uDCCD Request URL:",e.config?.url),console.error("\uD83D\uDCCB Request headers:",e.config?.headers),e.response?(console.error("\uD83D\uDEA8 Response status:",e.response.status),console.error("\uD83D\uDEA8 Response data:",e.response.data),console.error("\uD83D\uDEA8 Response headers:",e.response.headers)):e.request&&console.error("\uD83C\uDF10 Network error - no response received:",e.request)),e}},[]),w=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!t)return console.log("No access token found, skipping chat history request"),[];let r={"Content-Type":"application/json",Authorization:`Bearer ${t}`};return(await s.A.post(`${(0,n.hY)()}/chats/getchathistory`,{session_id:e},{headers:r})).data||[]}catch(e){throw console.error("Error getting chat history:",e),s.A.isAxiosError(e)&&e.response&&(console.error("Chat history error response:",e.response.data),console.error("Chat history error status:",e.response.status)),e}},[]),S=(0,o.useCallback)(async(e={})=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!t)return console.log("No access token found, skipping reports list request"),{reports:[],total_count:0};let r={"Content-Type":"application/json",Authorization:`Bearer ${t}`};return(await s.A.post(`${(0,n.hY)()}/reports/listreports`,e,{headers:r})).data}catch(e){throw console.error("Error listing user reports:",e),s.A.isAxiosError(e)&&e.response&&(console.error("List reports error response:",e.response.data),console.error("List reports error status:",e.response.status)),e}},[]),y=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");let r={"Content-Type":"application/json",Authorization:`Bearer ${t}`};return(await s.A.post(`${(0,n.hY)()}/chats/deletechat`,{session_id:e},{headers:r})).data}catch(e){throw console.error("Error deleting chat:",e),e}},[]),C=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await s.A.put(`${(0,n.hY)()}/auth/profile`,e,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error updating user profile:",e),e}},[]),A=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await s.A.post(`${(0,n.hY)()}/auth/change-password`,e,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error changing password:",e),e}},[]),_=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await s.A.put(`${(0,n.hY)()}/auth/email-preferences`,e,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error saving email preferences:",e),e}},[]),b=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");return(await s.A.put(`${(0,n.hY)()}/auth/privacy-settings`,e,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error saving privacy settings:",e),e}},[]),T=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!e)throw Error("No authentication token found");return(await s.A.post(`${(0,n.hY)()}/auth/export-data`,{},{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error exporting user data:",e),e}},[]),k=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!e)throw Error("No authentication token found");return(await s.A.delete(`${(0,n.hY)()}/auth/account`,{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error deleting account:",e),e}},[]),I=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!e)throw Error("No authentication token found");try{return(await s.A.post(`${(0,n.hY)()}${n.Sn.AUTH.COMPLETE_ONBOARDING}`,{},{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}})).data}catch(e){throw e.response?.status,e}}catch(e){throw console.error("Error completing onboarding:",e),e}},[]),v=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");let r={"Content-Type":"application/json",Authorization:`Bearer ${t}`};return(await s.A.post(`${(0,n.hY)()}${n.Sn.CHART.QUERY}`,e,{headers:r})).data}catch(e){throw console.error("Error querying chart:",e),e}},[]),R=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!e)throw Error("No authentication token found");let t={"Content-Type":"application/json",Authorization:`Bearer ${e}`};return(await s.A.get(`${(0,n.hY)()}${n.Sn.CHART.TYPES}`,{headers:t})).data}catch(e){throw console.error("Error getting chart types:",e),e}},[]),N=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!t)throw Error("No authentication token found");let r={"Content-Type":"application/json",Authorization:`Bearer ${t}`};return(await s.A.post(`${(0,n.hY)()}${n.Sn.CHART.VALIDATE}`,e,{headers:r})).data}catch(e){throw console.error("Error validating chart query:",e),e}},[]),D=(0,o.useCallback)(async()=>{try{let e=localStorage.getItem(n.d5.ACCESS_TOKEN);if(!e)throw Error("No authentication token found");let t={"Content-Type":"application/json",Authorization:`Bearer ${e}`};return(await s.A.get(`${(0,n.hY)()}${n.Sn.CHART.HEALTH}`,{headers:t})).data}catch(e){throw console.error("Error getting chart health:",e),e}},[]),O=(0,o.useCallback)(async()=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:[]}}catch(e){throw console.error("Error listing dashboards:",e),e}},[]),P=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");let t={id:`dashboard-${Date.now()}`,name:e.name,description:e.description,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),user_id:"user-1"};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:t}}catch(e){throw console.error("Error creating dashboard:",e),e}},[]),x=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");let t={id:e,name:"Sample Dashboard",description:"A sample dashboard with charts",created_at:new Date(Date.now()-6048e5).toISOString(),updated_at:new Date(Date.now()-864e5).toISOString(),user_id:"user-1",widgets:[]};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:t}}catch(e){throw console.error("Error getting dashboard:",e),e}},[]),$=(0,o.useCallback)(async(e,t)=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");let r={id:e,name:t.name||"Updated Dashboard",description:t.description||"Updated description",created_at:new Date(Date.now()-6048e5).toISOString(),updated_at:new Date().toISOString(),user_id:"user-1"};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:r}}catch(e){throw console.error("Error updating dashboard:",e),e}},[]),K=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,message:"Dashboard deleted successfully"}}catch(e){throw console.error("Error deleting dashboard:",e),e}},[]),U=(0,o.useCallback)(async()=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");let e=[{id:"analysis-1",name:"Employee Performance Analysis",description:"Analyzing employee performance metrics, salary distributions, and department insights",status:"completed",createdAt:new Date(Date.now()-1728e5).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),dataSource:"HR Database",stepCount:3,userId:"user-1"},{id:"analysis-2",name:"Customer Behavior Study",description:"Understanding customer purchasing patterns and churn prediction",status:"running",createdAt:new Date(Date.now()-432e6).toISOString(),updatedAt:new Date(Date.now()-72e5).toISOString(),dataSource:"Sales Database",stepCount:4,progress:67,userId:"user-1"},{id:"analysis-3",name:"Market Trend Forecasting",description:"Forecasting market trends and demand patterns for Q4",status:"draft",createdAt:new Date(Date.now()-864e5).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),dataSource:"Market Data API",stepCount:0,userId:"user-1"}];return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:{projects:e,totalCount:e.length}}}catch(e){throw console.error("Error listing analysis projects:",e),e}},[]),H=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");let t={id:`analysis-${Date.now()}`,name:e.name,description:e.description||"",status:"draft",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),dataSource:e.dataSource,stepCount:0,userId:"user-1"};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:t}}catch(e){throw console.error("Error creating analysis project:",e),e}},[]),L=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");let t={id:e,name:"Employee Performance Analysis",description:"Analyzing employee performance metrics, salary distributions, and department insights",status:"completed",createdAt:new Date(Date.now()-1728e5).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),dataSource:"HR Database",stepCount:3,userId:"user-1"};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:t}}catch(e){throw console.error("Error getting analysis project:",e),e}},[]),j=(0,o.useCallback)(async(e,t)=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");let r={id:e,name:t.name||"Updated Analysis Project",description:t.description||"Updated description",status:t.status||"draft",createdAt:new Date(Date.now()-1728e5).toISOString(),updatedAt:new Date().toISOString(),dataSource:"HR Database",stepCount:3,userId:"user-1"};return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:r}}catch(e){throw console.error("Error updating analysis project:",e),e}},[]),M=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,message:"Analysis project deleted successfully"}}catch(e){throw console.error("Error deleting analysis project:",e),e}},[]),B=(0,o.useCallback)(async(e,t={})=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,300*Math.random()+100)),{success:!0,data:{rows:[],columns:[],totalRows:0,schema:{}}}}catch(e){throw console.error("Error getting project data:",e),e}},[]),Y=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");let t=[{id:"step-1",projectId:e,title:"Data Loading",status:"completed",type:"data_loading",order:1,createdAt:new Date(Date.now()-72e5).toISOString(),updatedAt:new Date(Date.now()-36e5).toISOString(),outputs:{summary:"Successfully loaded 5 employee records from the connected database.",code:`import pandas as pd
import numpy as np

# Load employee data from connected database
df = pd.read_sql_query("""
    SELECT id, name, age, department, salary, performance_rating as performance
    FROM employees 
    WHERE active = true
""", connection)

print(f"Loaded {len(df)} employee records")
df.head()`}},{id:"step-2",projectId:e,title:"Data Analysis",status:"completed",type:"analysis",order:2,createdAt:new Date(Date.now()-36e5).toISOString(),updatedAt:new Date(Date.now()-18e5).toISOString(),outputs:{summary:"Analyzed salary distributions and identified 2 high-performing employees.",code:`# Analyze salary distribution by department
dept_stats = df.groupby('department').agg({
    'salary': ['mean', 'median', 'std'],
    'performance': 'mean',
    'age': 'mean'
}).round(2)

print("Department Statistics:")
print(dept_stats)

# Identify high performers
high_performers = df[df['performance'] >= 4.5]
print(f"\\nHigh performers ({len(high_performers)} employees):")
print(high_performers[['name', 'department', 'performance']])`}},{id:"step-3",projectId:e,title:"Visualization",status:"completed",type:"visualization",order:3,createdAt:new Date(Date.now()-18e5).toISOString(),updatedAt:new Date(Date.now()-6e5).toISOString(),outputs:{summary:"Created scatter plot showing relationship between salary and performance across departments.",code:`import matplotlib.pyplot as plt
import seaborn as sns

# Create scatter plot of salary vs performance
plt.figure(figsize=(10, 6))
sns.scatterplot(data=df, x='performance', y='salary', hue='department', s=100)
plt.title('Salary vs Performance by Department')
plt.xlabel('Performance Rating')
plt.ylabel('Salary ($)')
plt.legend(title='Department')
plt.tight_layout()
plt.show()`,visualization:{type:"chart",config:{title:"Salary vs Performance by Department",xAxis:"performance",yAxis:"salary",groupBy:"department"},data:[{x:4.2,y:85e3,group:"Engineering",name:"John Doe"},{x:4.8,y:65e3,group:"Marketing",name:"Jane Smith"},{x:4.1,y:92e3,group:"Engineering",name:"Mike Johnson"},{x:4.6,y:7e4,group:"Design",name:"Sarah Wilson"},{x:3.9,y:98e3,group:"Engineering",name:"David Brown"}]}}}];return await new Promise(e=>setTimeout(e,500*Math.random()+200)),{success:!0,data:t}}catch(e){throw console.error("Error getting project steps:",e),e}},[]),z=(0,o.useCallback)(async(e,t)=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,2e3*Math.random()+1e3)),{success:!0,message:"Step execution completed successfully"}}catch(e){throw console.error("Error executing step:",e),e}},[]),F=(0,o.useCallback)(async e=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");let{file:t,projectId:r,onProgress:a}=e,o=new FormData;if(o.append("file",t),o.append("projectId",r),a)for(let e=0;e<=100;e+=10)await new Promise(e=>setTimeout(e,100)),a({loaded:t.size*e/100,total:t.size,percentage:e});let s={rows:[],columns:[],totalRows:0,schema:{}};return(t.name.toLowerCase().includes("csv")||t.name.toLowerCase().includes("excel"))&&(s={rows:[{id:1,name:"Alice Johnson",age:28,salary:75e3,department:"Engineering"},{id:2,name:"Bob Smith",age:34,salary:82e3,department:"Marketing"},{id:3,name:"Carol White",age:29,salary:78e3,department:"Design"},{id:4,name:"David Brown",age:31,salary:85e3,department:"Engineering"},{id:5,name:"Emma Davis",age:26,salary:72e3,department:"Sales"}],columns:[{name:"id",type:"number",nullable:!1},{name:"name",type:"string",nullable:!1},{name:"age",type:"number",nullable:!1},{name:"salary",type:"number",nullable:!1},{name:"department",type:"string",nullable:!1}],totalRows:5,schema:{id:"INTEGER",name:"VARCHAR(255)",age:"INTEGER",salary:"INTEGER",department:"VARCHAR(100)"}}),await new Promise(e=>setTimeout(e,500)),{success:!0,data:{fileId:`file-${Date.now()}`,filename:t.name,rowCount:s.totalRows,columnCount:s.columns.length,previewData:s}}}catch(e){throw console.error("Error uploading file:",e),e}},[]),q=(0,o.useCallback)(async(e,t)=>{try{if(!localStorage.getItem(n.d5.ACCESS_TOKEN))throw Error("No authentication token found");return await new Promise(e=>setTimeout(e,300)),{success:!0,message:"File deleted successfully"}}catch(e){throw console.error("Error deleting file:",e),e}},[]);return(0,a.jsx)(i.Provider,{value:{queryDatabases:t,getDatabaseSchema:r,listDatabases:l,connectNewDatabase:c,disconnectExistingDatabase:d,askQuery:u,loginUser:h,registerUser:g,refreshToken:p,logoutUser:m,getUserProfile:E,updateUserProfile:C,changePassword:A,saveEmailPreferences:_,savePrivacySettings:b,exportUserData:T,deleteAccount:k,listUserChats:f,getChatHistory:w,listUserReports:S,deleteChat:y,completeOnboarding:I,queryChart:v,getChartTypes:R,validateChartQuery:N,getChartHealth:D,listDashboards:O,createDashboard:P,getDashboard:x,updateDashboard:$,deleteDashboard:K,listAnalysisProjects:U,createAnalysisProject:H,getAnalysisProject:L,updateAnalysisProject:j,deleteAnalysisProject:M,getProjectData:B,getProjectSteps:Y,executeStep:z,uploadProjectFile:F,deleteProjectFile:q},children:e})},c=()=>{let e=(0,o.useContext)(i);if(void 0===e)throw Error("useApi must be used within an ApiProvider");return e}},61135:()=>{},61453:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},62395:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var a=r(12907);let o=(0,a.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\providers\\theme-provider.tsx","ThemeProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\providers\\theme-provider.tsx","useTheme")},63321:(e,t,r)=>{Promise.resolve().then(r.bind(r,98583)),Promise.resolve().then(r.bind(r,44297)),Promise.resolve().then(r.bind(r,95397))},91010:(e,t,r)=>{"use strict";r.d(t,{O:()=>m,A:()=>E});var a=r(60687),o=r(43210),s=r(16189),n=r(51851),i=r(97616);let l={maxRetries:3,baseDelay:1e3,maxDelay:1e4,backoffFactor:2};async function c(e,t=l,r="operation"){let a;for(let o=0;o<=t.maxRetries;o++)try{console.log(`Attempting ${r} (attempt ${o+1}/${t.maxRetries+1})`);let a=await e();return o>0&&console.log(`${r} succeeded after ${o+1} attempts`),a}catch(s){if(a=s,console.warn(`${r} failed on attempt ${o+1}:`,s),o===t.maxRetries)break;let e=Math.min(t.baseDelay*Math.pow(t.backoffFactor,o),t.maxDelay);console.log(`Retrying ${r} in ${e}ms...`),await new Promise(t=>setTimeout(t,e))}throw console.error(`${r} failed after ${t.maxRetries+1} attempts`),a}async function d(e,t,r){try{return{data:await c(e,{...l,...r},t)}}catch(r){let e=function(e){let t=new Date().toISOString();if("NETWORK_ERROR"===e.code||e.message?.includes("network")||e.message?.includes("fetch"))return{code:"NETWORK_ERROR",message:e.message||"Network error occurred",retryable:!0,userMessage:"Network connection issue. Please check your internet connection and try again.",timestamp:t};if(e.response?.status===401||e.message?.includes("token")||e.message?.includes("unauthorized"))return{code:"TOKEN_ERROR",message:e.message||"Authentication token invalid",retryable:!1,userMessage:"Your session has expired. Please log in again.",timestamp:t};if(e.response?.status>=500)return{code:"SERVER_ERROR",message:e.message||"Server error occurred",retryable:!0,userMessage:"Server is temporarily unavailable. Please try again in a moment.",timestamp:t};if(e.response?.status===409){let r=e.response?.data?.error;return r?.code==="EMAIL_ALREADY_EXISTS"?{code:"EMAIL_ALREADY_EXISTS",message:r.message||"Email already exists",retryable:!1,userMessage:r.message||"An account with this email address already exists. Please use a different email or try logging in.",timestamp:t}:{code:"CONFLICT_ERROR",message:e.response?.data?.error?.message||e.message||"Conflict error occurred",retryable:!1,userMessage:e.response?.data?.error?.message||"There was a conflict with your request. Please try again.",timestamp:t}}if(e.response?.status>=400&&e.response?.status<500){let r=e.response?.data?.error;return r?.message?{code:r.code||"CLIENT_ERROR",message:r.message,retryable:!1,userMessage:r.message,timestamp:t}:{code:"CLIENT_ERROR",message:e.message||"Client error occurred",retryable:!1,userMessage:"There was an issue with your request. Please try again or contact support.",timestamp:t}}return e.message?.includes("onboarding")||e.message?.includes("is_new_user")?{code:"ONBOARDING_ERROR",message:e.message||"Onboarding error occurred",retryable:!0,userMessage:"There was an issue completing your setup. Please try again.",timestamp:t}:{code:"UNKNOWN_ERROR",message:e.message||"An unknown error occurred",retryable:!0,userMessage:"An unexpected error occurred. Please try again.",timestamp:t}}(r);return console.error(`Safe API call failed for ${t}:`,e),{error:e}}}function u(e){if(!function(e){if(!e||"object"!=typeof e)return!1;for(let t of["access_token","user_id"])if(!e[t]||"string"!=typeof e[t])return!1;return void 0===e.is_new_user||"boolean"==typeof e.is_new_user}(e))throw Error("Invalid authentication response structure");return{...e,is_new_user:e.is_new_user??!1,token_type:e.token_type||"bearer"}}function h(e,t){console.error("Auth Error:",{context:t,code:e.code,message:e.message,timestamp:e.timestamp,retryable:e.retryable,userAgent:"server",url:"unknown"})}var g=r(51060);let p=(0,o.createContext)(void 0),m=({children:e})=>{let[t,r]=(0,o.useState)(null),[l,c]=(0,o.useState)(!1),[m,E]=(0,o.useState)(!1),[f,w]=(0,o.useState)(Date.now()),[S,y]=(0,o.useState)(!1),[C,A]=(0,o.useState)(!1),{loginUser:_,registerUser:b,refreshToken:T,logoutUser:k,getUserProfile:I,completeOnboarding:v}=(0,n.g)(),R=(0,s.useRouter)(),N=(0,o.useCallback)(async()=>{if(!t)return;let{data:e,error:r}=await d(()=>I(),"refresh user state",{maxRetries:2});e?(E(e.is_new_user||!1),w(Date.now()),console.log("User state refreshed, is_new_user:",e.is_new_user)):r&&h(r,"refreshUserState")},[t,I,E,w]),D=(0,o.useCallback)(()=>Date.now()-f>3e5,[f]);(0,o.useEffect)(()=>{let e=async()=>{!document.hidden&&t&&D()&&(console.log("Tab became visible, refreshing user state..."),await N())};return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)},[t,D,N]),(0,o.useEffect)(()=>{if(!t)return;let e=setInterval(async()=>{D()&&(console.log("Periodic state refresh triggered..."),await N())},6e5);return()=>clearInterval(e)},[t,D,N]);let O=(0,o.useCallback)(async()=>{console.log("Manually initializing authentication from storage..."),c(!0);let e=localStorage.getItem(i.d5.ACCESS_TOKEN),t=localStorage.getItem(i.d5.USER_ID),a=localStorage.getItem(i.d5.REFRESH_TOKEN),o=localStorage.getItem(i.d5.EXPIRES_AT);if(!e||!t||!a||!o)return console.log("No complete auth data found in localStorage"),c(!1),!1;try{r({access_token:e,user_id:t,refresh_token:a,expires_at:o,token_type:"bearer"});let s=await I();E(s.is_new_user||!1),w(Date.now()),console.log("User profile fetched, is_new_user:",s.is_new_user);try{let e=new Date(o).getTime();if(Date.now()>e-3e5){console.log("Access token nearing expiry. Refreshing…");let e=await T();r(e)}}catch(e){console.warn("Proactive refresh check failed:",e)}return c(!1),!0}catch(e){if(g.A.isAxiosError(e)&&e.response?.status===401){console.warn("Access token invalid (401). Attempting refresh…");try{let e=await T();console.log("Token refresh successful after 401"),r(e);let t=await I();return E(t.is_new_user||!1),w(Date.now()),c(!1),!0}catch(e){console.error("Token refresh failed after 401:",e)}}else console.error("Failed to fetch user profile during initialization:",e);return localStorage.removeItem(i.d5.ACCESS_TOKEN),localStorage.removeItem(i.d5.REFRESH_TOKEN),localStorage.removeItem(i.d5.USER_ID),localStorage.removeItem(i.d5.EXPIRES_AT),r(null),c(!1),!1}},[T,I]);(0,o.useEffect)(()=>{!C&&(A(!0),localStorage.getItem(i.d5.ACCESS_TOKEN)&&localStorage.getItem(i.d5.REFRESH_TOKEN)&&console.log("Found stored auth data, but not auto-authenticating. User must manually sign in."))},[C]);let P=async e=>{let{data:t,error:a}=await d(()=>_(e),"user login");if(a)throw h(a,"login"),Error(a.userMessage);if(t){let e=u(t);r(e),E(e.is_new_user),w(Date.now()),e.is_new_user?R.push("/onboarding"):R.push("/dashboard")}},x=async(e,t=!0)=>{if(await O()){console.log("Authenticated from stored tokens"),t&&(m?R.push("/onboarding"):R.push("/dashboard"));return}if(e)await P(e);else if(t)R.push("/login");else throw Error("No valid authentication found")},$=async e=>{let{data:t,error:a}=await d(()=>b(e),"user registration");if(a)throw h(a,"register"),Error(a.userMessage);if(t){console.log("Registration successful, automatically logging in user...");try{let t={username:e.email,password:e.password},{data:a,error:o}=await d(()=>_(t),"auto-login after registration");if(o){h(o,"auto-login after registration"),console.warn("Auto-login failed after registration, redirecting to login page"),R.push("/login?registration=success");return}if(a){let e=u(a);r(e),E(!0),w(Date.now()),console.log("Auto-login successful, redirecting to onboarding..."),R.push("/onboarding")}}catch(e){console.error("Auto-login failed after registration:",e),R.push("/login?registration=success")}}},K=async()=>{try{await k()}catch(e){console.error("Logout API call failed:",e)}localStorage.removeItem(i.d5.ACCESS_TOKEN),localStorage.removeItem(i.d5.REFRESH_TOKEN),localStorage.removeItem(i.d5.USER_ID),localStorage.removeItem(i.d5.EXPIRES_AT),localStorage.removeItem(i.d5.TOKEN_TYPE),r(null),E(!1),A(!1),R.push("/")},U=async()=>{try{let e=await T();r(e),void 0!==e.is_new_user?(E(e.is_new_user),w(Date.now())):D()&&await N()}catch(e){throw console.error("Token refresh failed:",e),r(null),E(!1),w(Date.now()),R.push("/login"),e}},H=async()=>{try{y(!0),console.log("\uD83D\uDE80 Starting onboarding completion..."),await v(),console.log("✅ Backend onboarding completion successful");let e=await I();console.log("✅ Updated profile fetched:",{is_new_user:e.is_new_user}),E(e.is_new_user||!1),w(Date.now()),console.log("✅ User state updated, redirecting to dashboard..."),setTimeout(()=>{y(!1),R.push("/dashboard")},100)}catch(e){throw console.error("❌ Failed to complete onboarding:",e),y(!1),e}},L=(0,o.useCallback)(async e=>{try{let t=new Date(Date.now()+1e3*e.expires_in).toISOString();localStorage.setItem(i.d5.ACCESS_TOKEN,e.access_token),localStorage.setItem(i.d5.REFRESH_TOKEN,e.refresh_token),localStorage.setItem(i.d5.USER_ID,e.user_id),localStorage.setItem(i.d5.EXPIRES_AT,t),localStorage.setItem(i.d5.TOKEN_TYPE,e.token_type||"bearer");let a={access_token:e.access_token,token_type:e.token_type||"bearer",user_id:e.user_id,expires_at:t,refresh_token:e.refresh_token};r(a);try{let e=await I();E(e.is_new_user||!1),w(Date.now()),console.log("OAuth user profile fetched, is_new_user:",e.is_new_user)}catch(e){console.error("Failed to fetch user profile during OAuth callback:",e),E(!1)}}catch(e){throw console.error("OAuth callback handling failed:",e),e}},[r]);return(0,a.jsx)(p.Provider,{value:{isAuthenticated:!!t,user:t,isLoading:l,isNewUser:m,isCompletingOnboarding:S,login:P,signIn:x,initializeAuthFromStorage:O,register:$,logout:K,setUser:r,refreshUserToken:U,handleOAuthCallback:L,completeOnboarding:H},children:e})},E=()=>{let e=(0,o.useContext)(p);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>u});var a=r(37413),o=r(35230),s=r.n(o),n=r(38198),i=r.n(n);r(61135);var l=r(20625),c=r(17091),d=r(62395);let u={title:"Agent Platform",description:"Agent Platform for database queries"};function h({children:e}){return(0,a.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,a.jsx)("head",{children:(0,a.jsx)("meta",{name:"color-scheme",content:"light dark"})}),(0,a.jsx)("body",{className:`${s().variable} ${i().variable} bg-white dark:bg-gray-800`,children:(0,a.jsx)(d.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,a.jsx)(l.default,{children:(0,a.jsx)(c.ChatHistoryProvider,{children:e})})})})]})}},95397:(e,t,r)=>{"use strict";r.d(t,{D:()=>i,ThemeProvider:()=>n});var a=r(60687),o=r(43210),s=r(10218);function n({children:e,...t}){return(0,a.jsx)(s.N,{...t,children:e})}let i=()=>{let[e,t]=o.useState(!1),{theme:r,setTheme:a,resolvedTheme:n}=(0,s.D)();return o.useEffect(()=>t(!0),[]),{theme:e?r:void 0,setTheme:a,resolvedTheme:e?n:void 0,mounted:e}}},97616:(e,t,r)=>{"use strict";r.d(t,{Sn:()=>a,bw:()=>c,d5:()=>l,hY:()=>i});let a={AUTH:{LOGIN:"/auth/login",REGISTER:"/auth/register",LOGOUT:"/auth/logout",REFRESH:"/auth/refresh",ME:"/auth/me",GOOGLE:"/auth/google",COMPLETE_ONBOARDING:"/auth/onboarding/complete"},DATABASES:{LIST:"/databases/listdatabases",CONNECT:"/databases/connectdatabase",DISCONNECT:"/databases/disconnectdatabase",SCHEMA:"/databases/schema"},QUERY:{ASK:"/query/ask"},CHAT:{LIST:"/chat/list",HISTORY:"/chat/history",DELETE:"/chat/delete"},REPORTS:{LIST:"/reports/list"},ASK:{QUESTION:"/ask/question"},CHART:{QUERY:"/chart/query",TYPES:"/chart/types",VALIDATE:"/chart/validate",HEALTH:"/chart/health"},DASHBOARD:{LIST:"/dashboard/list",CREATE:"/dashboard/create",GET:"/dashboard/get",UPDATE:"/dashboard/update",DELETE:"/dashboard/delete"},ANALYSIS:{LIST:"/analysis/list",CREATE:"/analysis/create",GET:"/analysis/get",UPDATE:"/analysis/update",DELETE:"/analysis/delete",DATA:"/analysis/data",STEPS:"/analysis/steps",EXECUTE_STEP:"/analysis/execute-step",UPLOAD_FILE:"/analysis/upload-file",DELETE_FILE:"/analysis/delete-file"}},o={development:{defaultApiBase:"http://localhost:8000",name:"Development",enableDebugLogs:!0},production:{defaultApiBase:"https://agentreportbackend.vercel.app",name:"Production",enableDebugLogs:!1},staging:{defaultApiBase:"https://staging-agentreportbackend.vercel.app",name:"Staging",enableDebugLogs:!0}},s=()=>{let e="production",t="http://localhost:8000";if(e&&!Object.keys(o).includes(e)&&console.warn(`⚠️  Unknown NODE_ENV: ${e}. Expected: development, production, or staging`),t)try{new URL(t)}catch(e){console.error(`❌ Invalid NEXT_PUBLIC_API_BASE URL: ${t}`)}else console.warn("⚠️  NEXT_PUBLIC_API_BASE is not set. Using environment defaults.")},n=()=>o.production||o.development,i=()=>{s();let e="http://localhost:8000",t=n();!e&&(e=t.defaultApiBase,t.enableDebugLogs&&console.log(`🔧 Using default ${t.name} API URL: ${e}`));let r=`${e.replace(/\/+$/,"")}/api`;return t.enableDebugLogs,r},l={ACCESS_TOKEN:"accessToken",REFRESH_TOKEN:"refreshToken",TOKEN_TYPE:"tokenType",USER_ID:"userId",EXPIRES_AT:"expiresAt"},c={HOME:"/",LOGIN:"/login",REGISTER:"/register",AUTH:{CALLBACK:"/auth/callback"},OAUTH:{CALLBACK:"/oauth/callback"},ONBOARDING:"/onboarding",DASHBOARD:"/dashboard",CHAT:"/chat",DATASOURCES:"/datasources",REPORTS:"/reports",CHAT_WITH_ID:e=>`/chat/${e}`};c.HOME,c.LOGIN,c.REGISTER,c.AUTH.CALLBACK,c.OAUTH.CALLBACK,c.ONBOARDING,c.DASHBOARD,c.CHAT,c.DATASOURCES,c.REPORTS,c.DASHBOARD},98583:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var a=r(60687);r(43210);var o=r(92314),s=r(8693),n=r(51851),i=r(91010),l=r(17223),c=r(17849);let d=new o.E,u=({children:e})=>(0,a.jsx)(s.Ht,{client:d,children:(0,a.jsx)(n.K,{children:(0,a.jsx)(i.O,{children:(0,a.jsx)(c.M,{children:(0,a.jsx)(l._,{children:e})})})})})}};