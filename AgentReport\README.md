# Database Query Agent Platform

A platform that enables users to connect multiple databases and query them using natural language. The system uses a multi-agent architecture powered by AWS Bedrock to understand user queries, identify relevant data across databases, generate SQL, and return formatted results.

## Features

- Connect to multiple databases simultaneously (PostgreSQL, MySQL, SQL Server, Oracle, MongoDB, Supabase)
- Query databases using natural language
- Target specific databases, tables, and columns for more efficient querying
- Automatic identification of relevant tables and columns
- Support for follow-up questions and conversational context
- Output results in CSV or Excel format

## Architecture

The system uses a multi-agent architecture:

1. **Orchestrator Agent**: Coordinates the entire system
2. **Database Manager Agents**: Manage connections to individual databases
   - For databases with 800+ tables, creates sub-agents to handle subsets of tables
3. **SQL Agent**: Generates SQL queries based on the relevant schema
4. **Output Agent**: Formats and presents results

## Setup

### Prerequisites

- Python 3.9+
- AWS account with Bedrock access

### Installation

1. Clone the repository:
```
git clone https://github.com/yourusername/database-query-agent.git
cd database-query-agent
```

2. Create a virtual environment:
```
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```
pip install -r requirements.txt
```

4. Create a `.env` file with your AWS credentials:
```
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-********-v1:0
```

## Running the Application

Start the API server:

```
python app/main.py
```

Or alternatively:

```
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

The API will be available at `http://localhost:8000`.

## API Endpoints

- `POST /api/databases`: Connect to a database
- `GET /api/databases`: List connected databases
- `DELETE /api/databases/{db_id}`: Disconnect from a database
- `POST /api/query`: Query databases using natural language

## Example Usage

### Connecting a Database

```json
POST /api/databases
{
  "name": "My PostgreSQL DB",
  "type": "postgresql",
  "host": "localhost",
  "port": 5432,
  "username": "postgres",
  "password": "password",
  "database": "mydatabase"
}
```

### Querying Databases

```json
POST /api/query
{
  "query": "Show me all customers who made purchases last month",
  "output_format": "excel"
}
```

### Targeting Specific Databases and Tables

You can optionally target specific databases, tables, and columns for more efficient queries:

```json
POST /api/query
{
  "query": "Show me all customers who made purchases last month",
  "output_format": "excel",
  "target_databases": ["db_12345678"],
  "target_tables": {
    "db_12345678": ["customers", "orders"]
  },
  "target_columns": {
    "db_12345678": {
      "customers": ["customer_id", "name", "email"],
      "orders": ["order_id", "customer_id", "order_date", "total_amount"]
    }
  }
}
```

If no results are found in the targeted databases or tables, the system will automatically fall back to searching all connected databases.

## License

MIT

