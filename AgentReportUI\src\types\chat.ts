// Chat-related type definitions

export interface ChatHistoryItem {
  id: string;
  session_id: string; // Backend session ID
  title: string;
  created_at: Date;
  last_updated: Date;
  message_count: number;
}

export interface ChatMessage {
  role: 'user' | 'agent';
  content: string;
  timestamp?: Date;
  outputFiles?: Array<{
    database_name: string;
    file_path: string;
    format: string;
  }>;
  sqlQueries?: Record<string, string>;
}

export interface ChatHistoryContextType {
  chatHistory: ChatHistoryItem[];
  activeChat: ChatHistoryItem | null;
  chatMessages: { [sessionId: string]: ChatMessage[] };
  isLoadingChats: boolean;
  isLoadingHistory: boolean;
  pendingFirstMessage: string | null;
  setPendingFirstMessage: (message: string | null) => void;
  addChat: (title?: string) => ChatHistoryItem;
  updateChatSessionId: (chatId: string, newSessionId: string) => ChatHistoryItem | null;
  deleteChat: (id: string) => Promise<void>;
  renameChat: (id: string, newTitle: string) => void;
  setActiveChat: (chat: ChatHistoryItem | null) => Promise<void>;
  loadChatById: (chatId: string) => Promise<ChatHistoryItem | null>;
  addMessageToChat: (sessionId: string, message: ChatMessage) => void;
  loadChatHistory: (sessionId: string) => Promise<void>;
  refreshChatList: () => Promise<void>;
  generateSessionId: () => string;
}

export interface ChatProps {
  chatId?: string;
}
