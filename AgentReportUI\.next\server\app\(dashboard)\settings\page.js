(()=>{var e={};e.id=289,e.ids=[289],e.modules={17:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>U});var r=s(60687),a=s(43210),d=s(71837),l=s(91010),i=s(95397),n=s(30070),c=s(29523),o=s(44493),x=s(89667),m=s(54300),u=s(76242),h=s(82978),p=s(84027),b=s(5336),f=s(11860),g=s(43649),k=s(99891),j=s(19959),v=s(12597),y=s(13861),w=s(78122),N=s(64021),A=s(62688);let P=(0,A.A)("link-2",[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]]),C=(0,A.A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);var S=s(21134),M=s(363),E=s(41550),D=s(8819),T=s(61611),q=s(31158),R=s(88233);let z=()=>{let{user:e,logout:t}=(0,l.A)(),{theme:s,setTheme:d}=(0,i.D)(),A=(0,a.useMemo)(()=>({title:"Settings",icon:p.A}),[]);(0,n.H)(A);let[z,F]=(0,a.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[U,_]=(0,a.useState)({marketingEmails:!0,securityAlerts:!0,productUpdates:!1,weeklyDigest:!0}),[O,$]=(0,a.useState)({profileVisibility:"private",dataCollection:!0,analyticsOptOut:!1,thirdPartySharing:!1}),[L,Z]=(0,a.useState)(!1),[G,H]=(0,a.useState)(!1),[B,I]=(0,a.useState)(!1),[J,X]=(0,a.useState)(!1),[V,W]=(0,a.useState)(!1),[K,Q]=(0,a.useState)(!1),[Y,ee]=(0,a.useState)(!1),[et,es]=(0,a.useState)(""),[er,ea]=(0,a.useState)(!1),[ed,el]=(0,a.useState)(null),[ei,en]=(0,a.useState)(null),[ec,eo]=(0,a.useState)({}),ex=(0,a.useCallback)(()=>{let e={};return z.currentPassword||(e.currentPassword="Current password is required"),z.newPassword?z.newPassword.length<8?e.newPassword="Password must be at least 8 characters":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(z.newPassword)||(e.newPassword="Password must contain uppercase, lowercase, and number"):e.newPassword="New password is required",z.confirmPassword?z.newPassword!==z.confirmPassword&&(e.confirmPassword="Passwords do not match"):e.confirmPassword="Please confirm your new password",eo(e),0===Object.keys(e).length},[z]),em=(0,a.useCallback)((e,t)=>{F(s=>({...s,[e]:t})),ec[e]&&eo(t=>({...t,[e]:""}))},[ec]),eu=(0,a.useCallback)(async()=>{if(ex()){X(!0),el(null);try{await new Promise(e=>setTimeout(e,1500)),F({currentPassword:"",newPassword:"",confirmPassword:""}),en("Password changed successfully!"),setTimeout(()=>en(null),3e3)}catch(e){console.error("Failed to change password:",e),el("Failed to change password. Please check your current password and try again.")}finally{X(!1)}}},[z,ex]),eh=(0,a.useCallback)(async()=>{W(!0),el(null);try{await new Promise(e=>setTimeout(e,1e3)),en("Email preferences saved successfully!"),setTimeout(()=>en(null),3e3)}catch(e){console.error("Failed to save email preferences:",e),el("Failed to save email preferences. Please try again.")}finally{W(!1)}},[U]),ep=(0,a.useCallback)(async()=>{W(!0),el(null);try{await new Promise(e=>setTimeout(e,1e3)),en("Privacy settings saved successfully!"),setTimeout(()=>en(null),3e3)}catch(e){console.error("Failed to save privacy settings:",e),el("Failed to save privacy settings. Please try again.")}finally{W(!1)}},[O]),eb=(0,a.useCallback)(async()=>{Q(!0),el(null);try{await new Promise(e=>setTimeout(e,2e3));let e=document.createElement("a");e.href="data:text/json;charset=utf-8,"+encodeURIComponent(JSON.stringify({message:"This would be your actual data export",timestamp:new Date().toISOString()})),e.download=`user-data-export-${Date.now()}.json`,e.click(),en("Data export completed successfully!"),setTimeout(()=>en(null),3e3)}catch(e){console.error("Failed to export data:",e),el("Failed to export data. Please try again.")}finally{Q(!1)}},[]),ef=(0,a.useCallback)(async()=>{if("DELETE"!==et)return void el('Please type "DELETE" to confirm account deletion');ea(!0),el(null);try{await new Promise(e=>setTimeout(e,2e3)),await t()}catch(e){console.error("Failed to delete account:",e),el("Failed to delete account. Please try again.")}finally{ea(!1)}},[et,t]),eg=(0,a.useCallback)(()=>{el(null),en(null)},[]);return(0,r.jsx)(u.Bc,{children:(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:(0,r.jsx)("div",{className:"container mx-auto p-4 sm:p-6 lg:p-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[ei&&(0,r.jsx)("div",{className:"p-4 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 text-green-600 dark:text-green-400 flex-shrink-0"}),(0,r.jsxs)("div",{className:"text-green-800 dark:text-green-200",children:[(0,r.jsx)("div",{className:"font-medium",children:"Success"}),(0,r.jsx)("div",{className:"text-sm",children:ei})]})]}),(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:eg,children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})]})}),ed&&(0,r.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0"}),(0,r.jsxs)("div",{className:"text-red-800 dark:text-red-200",children:[(0,r.jsx)("div",{className:"font-medium",children:"Error"}),(0,r.jsx)("div",{className:"text-sm",children:ed})]})]}),(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:eg,children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})]})}),(0,r.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,r.jsx)(k.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Account Security"]}),(0,r.jsx)(o.BT,{className:"text-slate-600 dark:text-slate-400",children:"Manage your password and security settings"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),"Change Password"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.p,{id:"currentPassword",type:L?"text":"password",value:z.currentPassword,onChange:e=>em("currentPassword",e.target.value),placeholder:"Enter current password",className:ec.currentPassword?"border-red-300 dark:border-red-700":""}),(0,r.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>Z(!L),children:L?(0,r.jsx)(v.A,{className:"h-4 w-4"}):(0,r.jsx)(y.A,{className:"h-4 w-4"})})]}),ec.currentPassword&&(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:ec.currentPassword})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"newPassword",children:"New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.p,{id:"newPassword",type:G?"text":"password",value:z.newPassword,onChange:e=>em("newPassword",e.target.value),placeholder:"Enter new password",className:ec.newPassword?"border-red-300 dark:border-red-700":""}),(0,r.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>H(!G),children:G?(0,r.jsx)(v.A,{className:"h-4 w-4"}):(0,r.jsx)(y.A,{className:"h-4 w-4"})})]}),ec.newPassword&&(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:ec.newPassword})]}),(0,r.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,r.jsx)(m.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.p,{id:"confirmPassword",type:B?"text":"password",value:z.confirmPassword,onChange:e=>em("confirmPassword",e.target.value),placeholder:"Confirm new password",className:ec.confirmPassword?"border-red-300 dark:border-red-700":""}),(0,r.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>I(!B),children:B?(0,r.jsx)(v.A,{className:"h-4 w-4"}):(0,r.jsx)(y.A,{className:"h-4 w-4"})})]}),ec.confirmPassword&&(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:ec.confirmPassword})]})]}),(0,r.jsxs)(c.$,{onClick:eu,disabled:J,className:"bg-blue-600 hover:bg-blue-700",children:[J?(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,r.jsx)(N.A,{className:"h-4 w-4 mr-2"}),J?"Changing Password...":"Change Password"]})]}),(0,r.jsxs)("div",{className:"border-t border-slate-200 dark:border-slate-700 pt-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-slate-900 dark:text-slate-100 flex items-center gap-2 mb-4",children:[(0,r.jsx)(P,{className:"h-4 w-4"}),"Connected Accounts"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-red-600 dark:text-red-400 text-sm font-bold",children:"G"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"Google"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Connect with Google OAuth"})]})]}),(0,r.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(P,{className:"h-4 w-4 mr-2"}),"Connect"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-100 dark:bg-gray-900 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-600 dark:text-gray-400 text-sm font-bold",children:"GH"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"GitHub"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Connect with GitHub OAuth"})]})]}),(0,r.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(P,{className:"h-4 w-4 mr-2"}),"Connect"]})]})]})]})]})]}),(0,r.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,r.jsx)(C,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Theme Preferences"]}),(0,r.jsx)(o.BT,{className:"text-slate-600 dark:text-slate-400",children:"Choose your preferred theme and appearance"})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,r.jsx)("div",{className:`p-4 border-2 rounded-lg cursor-pointer transition-all ${"light"===s?"border-blue-500 bg-blue-50 dark:bg-blue-950":"border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600"}`,onClick:()=>d("light"),children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(S.A,{className:"h-5 w-5 text-yellow-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"Light"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Light theme"})]})]})}),(0,r.jsx)("div",{className:`p-4 border-2 rounded-lg cursor-pointer transition-all ${"dark"===s?"border-blue-500 bg-blue-50 dark:bg-blue-950":"border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600"}`,onClick:()=>d("dark"),children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(M.A,{className:"h-5 w-5 text-slate-600 dark:text-slate-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"Dark"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Dark theme"})]})]})}),(0,r.jsx)("div",{className:`p-4 border-2 rounded-lg cursor-pointer transition-all ${"system"===s?"border-blue-500 bg-blue-50 dark:bg-blue-950":"border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600"}`,onClick:()=>d("system"),children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(C,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"System"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Follow system"})]})]})})]})})]}),(0,r.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,r.jsx)(E.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Email Preferences"]}),(0,r.jsx)(o.BT,{className:"text-slate-600 dark:text-slate-400",children:"Control what emails you receive from us"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.S,{id:"marketingEmails",checked:U.marketingEmails,onCheckedChange:e=>_(t=>({...t,marketingEmails:!!e}))}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(m.J,{htmlFor:"marketingEmails",className:"text-sm font-medium cursor-pointer",children:"Marketing emails"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Receive updates about new features and promotions"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.S,{id:"securityAlerts",checked:U.securityAlerts,onCheckedChange:e=>_(t=>({...t,securityAlerts:!!e}))}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(m.J,{htmlFor:"securityAlerts",className:"text-sm font-medium cursor-pointer",children:"Security alerts"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Important notifications about your account security"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.S,{id:"productUpdates",checked:U.productUpdates,onCheckedChange:e=>_(t=>({...t,productUpdates:!!e}))}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(m.J,{htmlFor:"productUpdates",className:"text-sm font-medium cursor-pointer",children:"Product updates"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"News about product improvements and new releases"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.S,{id:"weeklyDigest",checked:U.weeklyDigest,onCheckedChange:e=>_(t=>({...t,weeklyDigest:!!e}))}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(m.J,{htmlFor:"weeklyDigest",className:"text-sm font-medium cursor-pointer",children:"Weekly digest"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Summary of your activity and insights"})]})]})]}),(0,r.jsxs)(c.$,{onClick:eh,disabled:V,className:"bg-blue-600 hover:bg-blue-700",children:[V?(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,r.jsx)(D.A,{className:"h-4 w-4 mr-2"}),V?"Saving...":"Save Preferences"]})]})]}),(0,r.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2",children:[(0,r.jsx)(T.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Privacy & Data"]}),(0,r.jsx)(o.BT,{className:"text-slate-600 dark:text-slate-400",children:"Manage your data and privacy settings"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-slate-900 dark:text-slate-100",children:"Privacy Settings"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.S,{id:"dataCollection",checked:O.dataCollection,onCheckedChange:e=>$(t=>({...t,dataCollection:!!e}))}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(m.J,{htmlFor:"dataCollection",className:"text-sm font-medium cursor-pointer",children:"Allow data collection for service improvement"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Help us improve our services by collecting anonymous usage data"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.S,{id:"analyticsOptOut",checked:O.analyticsOptOut,onCheckedChange:e=>$(t=>({...t,analyticsOptOut:!!e}))}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(m.J,{htmlFor:"analyticsOptOut",className:"text-sm font-medium cursor-pointer",children:"Opt out of analytics tracking"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Disable analytics and tracking cookies"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.S,{id:"thirdPartySharing",checked:O.thirdPartySharing,onCheckedChange:e=>$(t=>({...t,thirdPartySharing:!!e}))}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(m.J,{htmlFor:"thirdPartySharing",className:"text-sm font-medium cursor-pointer",children:"Allow third-party data sharing"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Share anonymized data with trusted partners for research"})]})]})]}),(0,r.jsxs)(c.$,{onClick:ep,disabled:V,className:"bg-blue-600 hover:bg-blue-700",children:[V?(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,r.jsx)(D.A,{className:"h-4 w-4 mr-2"}),V?"Saving...":"Save Privacy Settings"]})]}),(0,r.jsxs)("div",{className:"border-t border-slate-200 dark:border-slate-700 pt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-slate-900 dark:text-slate-100 mb-4",children:"Data Management"}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:"Export your data"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Download a copy of all your account data"})]}),(0,r.jsxs)(c.$,{onClick:eb,disabled:K,variant:"outline",children:[K?(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,r.jsx)(q.A,{className:"h-4 w-4 mr-2"}),K?"Exporting...":"Export Data"]})]})})]})]})]}),(0,r.jsxs)(o.Zp,{className:"bg-white dark:bg-slate-900 border-red-200 dark:border-red-800",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"text-xl font-semibold text-red-900 dark:text-red-100 flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 text-red-600 dark:text-red-400"}),"Danger Zone"]}),(0,r.jsx)(o.BT,{className:"text-red-700 dark:text-red-300",children:"Irreversible actions that will permanently affect your account"})]}),(0,r.jsx)(o.Wu,{children:Y?(0,r.jsxs)("div",{className:"p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950 space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-red-900 dark:text-red-100 mb-2",children:"Are you absolutely sure?"}),(0,r.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300 mb-4",children:"This action cannot be undone. This will permanently delete your account and remove all of your data from our servers."}),(0,r.jsxs)(m.J,{htmlFor:"deleteConfirmation",className:"text-sm font-medium text-red-900 dark:text-red-100",children:["Type ",(0,r.jsx)("strong",{children:"DELETE"})," to confirm:"]}),(0,r.jsx)(x.p,{id:"deleteConfirmation",value:et,onChange:e=>es(e.target.value),placeholder:"DELETE",className:"mt-2 border-red-300 dark:border-red-700"})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(c.$,{onClick:()=>{ee(!1),es("")},variant:"outline",disabled:er,children:"Cancel"}),(0,r.jsxs)(c.$,{onClick:ef,disabled:er||"DELETE"!==et,variant:"destructive",className:"bg-red-600 hover:bg-red-700",children:[er?(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,r.jsx)(R.A,{className:"h-4 w-4 mr-2"}),er?"Deleting Account...":"Delete Account"]})]})]}):(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-red-900 dark:text-red-100",children:"Delete Account"}),(0,r.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:"Permanently delete your account and all associated data"})]}),(0,r.jsxs)(c.$,{onClick:()=>ee(!0),variant:"destructive",className:"bg-red-600 hover:bg-red-700",children:[(0,r.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"Delete Account"]})]})})]})]})})})})};var F=s(16189);function U(){let{isAuthenticated:e,isLoading:t}=(0,l.A)();return((0,F.useRouter)(),t||!e)?(0,r.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-slate-600 dark:text-slate-400",children:"Loading..."})]})}):(0,r.jsx)(d.A,{children:(0,r.jsx)(z,{})})}},363:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13964:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},21134:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33191:(e,t,s)=>{Promise.resolve().then(s.bind(s,17))},33873:e=>{"use strict";e.exports=require("path")},41550:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},43649:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>n,Wu:()=>c,ZB:()=>i,Zp:()=>d,aR:()=>l});var r=s(60687);s(43210);var a=s(7766);function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold text-white",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},50063:(e,t,s)=>{Promise.resolve().then(s.bind(s,64954))},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var r=s(60687),a=s(43210),d=s(14163),l=a.forwardRef((e,t)=>(0,r.jsx)(d.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=s(7766);function n({className:e,...t}){return(0,r.jsx)(l,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},64954:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\NXTHUMANS\\\\internal-solutions-nxtwebsite\\\\AgentReport 2\\\\AgentReportUI\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(dashboard)\\settings\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},75732:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),d=s(88170),l=s.n(d),i=s(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(t,n);let c={children:["",{children:["(dashboard)",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,64954)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(dashboard)\\settings\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(dashboard)\\settings\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},76242:(e,t,s)=>{"use strict";s.d(t,{Bc:()=>i,ZI:()=>o,k$:()=>c,m_:()=>n});var r=s(60687),a=s(43210),d=s(9989),l=s(7766);let i=d.Kq,n=d.bL,c=d.l9,o=a.forwardRef(({className:e,sideOffset:t=4,...s},a)=>(0,r.jsx)(d.UC,{ref:a,sideOffset:t,className:(0,l.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s}));o.displayName=d.UC.displayName},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82978:(e,t,s)=>{"use strict";s.d(t,{S:()=>E});var r=s(60687),a=s(43210),d=s(98599),l=s(11273),i=s(70569),n=s(65551),c=s(83721),o=s(18853),x=s(46059),m=s(14163),u="Checkbox",[h,p]=(0,l.A)(u),[b,f]=h(u);function g(e){let{__scopeCheckbox:t,checked:s,children:d,defaultChecked:l,disabled:i,form:c,name:o,onCheckedChange:x,required:m,value:h="on",internal_do_not_use_render:p}=e,[f,g]=(0,n.i)({prop:s,defaultProp:l??!1,onChange:x,caller:u}),[k,j]=a.useState(null),[v,y]=a.useState(null),w=a.useRef(!1),N=!k||!!c||!!k.closest("form"),A={checked:f,disabled:i,setChecked:g,control:k,setControl:j,name:o,form:c,value:h,hasConsumerStoppedPropagationRef:w,required:m,defaultChecked:!P(l)&&l,isFormControl:N,bubbleInput:v,setBubbleInput:y};return(0,r.jsx)(b,{scope:t,...A,children:"function"==typeof p?p(A):d})}var k="CheckboxTrigger",j=a.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:s,...l},n)=>{let{control:c,value:o,disabled:x,checked:u,required:h,setControl:p,setChecked:b,hasConsumerStoppedPropagationRef:g,isFormControl:j,bubbleInput:v}=f(k,e),y=(0,d.s)(n,p),w=a.useRef(u);return a.useEffect(()=>{let e=c?.form;if(e){let t=()=>b(w.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,b]),(0,r.jsx)(m.sG.button,{type:"button",role:"checkbox","aria-checked":P(u)?"mixed":u,"aria-required":h,"data-state":C(u),"data-disabled":x?"":void 0,disabled:x,value:o,...l,ref:y,onKeyDown:(0,i.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(s,e=>{b(e=>!!P(e)||!e),v&&j&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})})});j.displayName=k;var v=a.forwardRef((e,t)=>{let{__scopeCheckbox:s,name:a,checked:d,defaultChecked:l,required:i,disabled:n,value:c,onCheckedChange:o,form:x,...m}=e;return(0,r.jsx)(g,{__scopeCheckbox:s,checked:d,defaultChecked:l,disabled:n,required:i,onCheckedChange:o,name:a,form:x,value:c,internal_do_not_use_render:({isFormControl:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j,{...m,ref:t,__scopeCheckbox:s}),e&&(0,r.jsx)(A,{__scopeCheckbox:s})]})})});v.displayName=u;var y="CheckboxIndicator",w=a.forwardRef((e,t)=>{let{__scopeCheckbox:s,forceMount:a,...d}=e,l=f(y,s);return(0,r.jsx)(x.C,{present:a||P(l.checked)||!0===l.checked,children:(0,r.jsx)(m.sG.span,{"data-state":C(l.checked),"data-disabled":l.disabled?"":void 0,...d,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=y;var N="CheckboxBubbleInput",A=a.forwardRef(({__scopeCheckbox:e,...t},s)=>{let{control:l,hasConsumerStoppedPropagationRef:i,checked:n,defaultChecked:x,required:u,disabled:h,name:p,value:b,form:g,bubbleInput:k,setBubbleInput:j}=f(N,e),v=(0,d.s)(s,j),y=(0,c.Z)(n),w=(0,o.X)(l);a.useEffect(()=>{if(!k)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(y!==n&&e){let s=new Event("click",{bubbles:t});k.indeterminate=P(n),e.call(k,!P(n)&&n),k.dispatchEvent(s)}},[k,y,n,i]);let A=a.useRef(!P(n)&&n);return(0,r.jsx)(m.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:x??A.current,required:u,disabled:h,name:p,value:b,form:g,...t,tabIndex:-1,ref:v,style:{...t.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function P(e){return"indeterminate"===e}function C(e){return P(e)?"indeterminate":e?"checked":"unchecked"}A.displayName=N;var S=s(13964),M=s(7766);function E({className:e,...t}){return(0,r.jsx)(v,{"data-slot":"checkbox",className:(0,M.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,r.jsx)(w,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(S.A,{className:"size-3.5"})})})}},83721:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});var r=s(43210);function a(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>d});var r=s(60687);s(43210);var a=s(7766);function d({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},94735:e=>{"use strict";e.exports=require("events")},99891:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,255,658,365,814,292,592,989,695,233],()=>s(75732));module.exports=r})();