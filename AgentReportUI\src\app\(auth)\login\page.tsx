'use client';

import LoginForm from '@/components/features/auth/LoginForm';
import Link from 'next/link';
import React, { Suspense } from 'react';

const LoginPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header with logo */}
      <div className="absolute top-0 left-0 p-6 z-20">
        <Link href="/" className="flex items-center space-x-2 group">
          <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-blue-500 group-hover:bg-blue-600 transition-colors duration-200 shadow-sm">
            <svg viewBox="0 0 24 24" fill="none" className="w-5 h-5 text-white">
              <path
                d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <span className="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-500 transition-colors duration-200">
            Agent
          </span>
        </Link>
      </div>
      
      {/* Main content */}
      <div className="min-h-screen flex items-center justify-center p-6">
        <div className="w-full max-w-md">
          {/* Glassmorphism card */}
          <div className="backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 rounded-3xl border border-gray-200/50 dark:border-gray-700/50 shadow-xl">
            {/* Header section */}
            <div className="p-8 pb-0 text-center">
              <h1 className="text-3xl font-light text-gray-900 dark:text-white mb-2 tracking-tight">
                Welcome back
              </h1>
              <p className="text-gray-500 dark:text-gray-400 font-light">
                Sign in to continue to your workspace
              </p>
            </div>

            {/* Form section */}
            <div className="p-8">
              <Suspense fallback={<div className="flex justify-center items-center h-32">Loading...</div>}>
              <LoginForm />
              </Suspense>
            </div>
          </div>

          {/* Support link */}
          <div className="text-center mt-8">
            <p className="text-sm text-gray-400 dark:text-gray-500">
              Need assistance?{' '}
              <a href="#" className="text-blue-500 hover:text-blue-600 transition-colors duration-200 font-medium">
                Contact support
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;