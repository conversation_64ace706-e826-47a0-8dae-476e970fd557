// Type guards and runtime validation utilities

import { TokenResponse } from '@/types/api';
import { OAuthTokens } from '@/types/auth';
import { OnboardingProgress, OnboardingStep, OnboardingState } from '@/types/auth';

/**
 * Type guard for TokenResponse
 */
export function isTokenResponse(data: any): data is TokenResponse {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.access_token === 'string' &&
    typeof data.user_id === 'string' &&
    typeof data.refresh_token === 'string' &&
    typeof data.expires_at === 'string' &&
    (data.is_new_user === undefined || typeof data.is_new_user === 'boolean') &&
    (data.token_type === undefined || typeof data.token_type === 'string')
  );
}

/**
 * Type guard for OAuthTokens
 */
export function isOAuthTokens(data: any): data is OAuthTokens {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.access_token === 'string' &&
    typeof data.refresh_token === 'string' &&
    typeof data.expires_in === 'number' &&
    typeof data.user_id === 'string' &&
    (data.token_type === undefined || typeof data.token_type === 'string')
  );
}

/**
 * Type guard for OnboardingProgress
 */
export function isOnboardingProgress(data: any): data is OnboardingProgress {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.currentStep === 'string' &&
    Array.isArray(data.completedSteps) &&
    data.completedSteps.every((step: any) => typeof step === 'string') &&
    typeof data.lastUpdated === 'string' &&
    typeof data.canResume === 'boolean' &&
    typeof data.totalSteps === 'number'
  );
}

/**
 * Type guard for OnboardingStep
 */
export function isOnboardingStep(data: any): data is OnboardingStep {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.id === 'string' &&
    typeof data.title === 'string' &&
    typeof data.description === 'string' &&
    typeof data.component === 'function' &&
    typeof data.isCompleted === 'boolean' &&
    (data.isOptional === undefined || typeof data.isOptional === 'boolean')
  );
}

/**
 * Type guard for OnboardingState
 */
export function isOnboardingState(data: any): data is OnboardingState {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.isNewUser === 'boolean' &&
    (data.progress === undefined || data.progress === null || isOnboardingProgress(data.progress)) &&
    typeof data.isCompleting === 'boolean' &&
    typeof data.hasError === 'boolean' &&
    (data.errorMessage === undefined || typeof data.errorMessage === 'string') &&
    typeof data.canRetry === 'boolean'
  );
}

/**
 * Validate array of OnboardingSteps
 */
export function isOnboardingStepsArray(data: any): data is OnboardingStep[] {
  return Array.isArray(data) && data.every(isOnboardingStep);
}

/**
 * Type guard for API error response
 */
export function isApiErrorResponse(data: any): data is { message: string; code?: string; details?: any } {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.message === 'string'
  );
}

/**
 * Type guard for network error
 */
export function isNetworkError(error: any): boolean {
  return (
    error &&
    (error.code === 'NETWORK_ERROR' ||
     error.name === 'NetworkError' ||
     error.message?.includes('network') ||
     error.message?.includes('fetch') ||
     !error.response) // Axios sets response to undefined for network errors
  );
}

/**
 * Type guard for authentication error
 */
export function isAuthError(error: any): boolean {
  return (
    error &&
    (error.response?.status === 401 ||
     error.response?.status === 403 ||
     error.message?.includes('unauthorized') ||
     error.message?.includes('forbidden') ||
     error.message?.includes('token'))
  );
}

/**
 * Type guard for server error
 */
export function isServerError(error: any): boolean {
  return (
    error &&
    error.response?.status >= 500 &&
    error.response?.status < 600
  );
}

/**
 * Validate URL search parameters for OAuth callback
 */
export function validateOAuthParams(searchParams: URLSearchParams): {
  isValid: boolean;
  errors: string[];
  tokens?: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    userId: string;
    tokenType: string;
  };
} {
  const errors: string[] = [];
  
  const accessToken = searchParams.get('access_token');
  const refreshToken = searchParams.get('refresh_token');
  const expiresIn = searchParams.get('expires_in');
  const userId = searchParams.get('user_id');
  const tokenType = searchParams.get('token_type') || 'bearer';
  const error = searchParams.get('error');
  
  // Check for OAuth error
  if (error) {
    errors.push(`OAuth error: ${error}`);
    const errorDescription = searchParams.get('error_description');
    if (errorDescription) {
      errors.push(`Error description: ${errorDescription}`);
    }
  }
  
  // Validate required parameters
  if (!accessToken) {
    errors.push('Missing access_token parameter');
  }
  
  if (!refreshToken) {
    errors.push('Missing refresh_token parameter');
  }
  
  if (!userId) {
    errors.push('Missing user_id parameter');
  }
  
  if (!expiresIn) {
    errors.push('Missing expires_in parameter');
  } else {
    const expiresInNum = parseInt(expiresIn, 10);
    if (isNaN(expiresInNum) || expiresInNum <= 0) {
      errors.push('Invalid expires_in parameter');
    }
  }
  
  const isValid = errors.length === 0;
  
  return {
    isValid,
    errors,
    tokens: isValid ? {
      accessToken: accessToken!,
      refreshToken: refreshToken!,
      expiresIn: parseInt(expiresIn!, 10),
      userId: userId!,
      tokenType,
    } : undefined,
  };
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate password strength
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  score: number; // 0-4
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;
  
  if (password.length >= 8) {
    score++;
  } else {
    feedback.push('Password should be at least 8 characters long');
  }
  
  if (/[a-z]/.test(password)) {
    score++;
  } else {
    feedback.push('Password should contain lowercase letters');
  }
  
  if (/[A-Z]/.test(password)) {
    score++;
  } else {
    feedback.push('Password should contain uppercase letters');
  }
  
  if (/\d/.test(password)) {
    score++;
  } else {
    feedback.push('Password should contain numbers');
  }
  
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score++;
  } else {
    feedback.push('Password should contain special characters');
  }
  
  return {
    isValid: score >= 3,
    score,
    feedback,
  };
}

/**
 * Sanitize user input to prevent XSS
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Validate and sanitize onboarding form data
 */
export function validateOnboardingFormData(data: any): {
  isValid: boolean;
  errors: string[];
  sanitizedData?: any;
} {
  const errors: string[] = [];
  const sanitizedData: any = {};
  
  // Validate and sanitize common fields
  if (data.fullName !== undefined) {
    if (typeof data.fullName !== 'string') {
      errors.push('Full name must be a string');
    } else if (data.fullName.trim().length === 0) {
      errors.push('Full name is required');
    } else if (data.fullName.length > 100) {
      errors.push('Full name is too long');
    } else {
      sanitizedData.fullName = sanitizeInput(data.fullName);
    }
  }
  
  if (data.email !== undefined) {
    if (typeof data.email !== 'string') {
      errors.push('Email must be a string');
    } else if (!isValidEmail(data.email)) {
      errors.push('Invalid email format');
    } else {
      sanitizedData.email = data.email.toLowerCase().trim();
    }
  }
  
  if (data.jobTitle !== undefined) {
    if (typeof data.jobTitle !== 'string') {
      errors.push('Job title must be a string');
    } else if (data.jobTitle.length > 100) {
      errors.push('Job title is too long');
    } else {
      sanitizedData.jobTitle = sanitizeInput(data.jobTitle);
    }
  }
  
  if (data.company !== undefined) {
    if (typeof data.company !== 'string') {
      errors.push('Company must be a string');
    } else if (data.company.length > 100) {
      errors.push('Company name is too long');
    } else {
      sanitizedData.company = sanitizeInput(data.company);
    }
  }
  
  if (data.bio !== undefined) {
    if (typeof data.bio !== 'string') {
      errors.push('Bio must be a string');
    } else if (data.bio.length > 500) {
      errors.push('Bio is too long');
    } else {
      sanitizedData.bio = sanitizeInput(data.bio);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitizedData: errors.length === 0 ? sanitizedData : undefined,
  };
}
