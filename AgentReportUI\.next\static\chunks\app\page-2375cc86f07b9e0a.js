(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{13896:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var a=t(95155),s=t(12115),n=t(76408),o=t(19946);let l=(0,o.A)("move-right",[["path",{d:"M18 8L22 12L18 16",key:"1r0oui"}],["path",{d:"M2 12H22",key:"1m8cig"}]]),i=(0,o.A)("phone-call",[["path",{d:"M13 2a9 9 0 0 1 9 9",key:"1itnx2"}],["path",{d:"M13 6a5 5 0 0 1 5 5",key:"11nki7"}],["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var c=t(30285);function d(){let[e,r]=(0,s.useState)(0),t=(0,s.useMemo)(()=>["amazing","new","wonderful","beautiful","smart"],[]);return(0,s.useEffect)(()=>{let a=setTimeout(()=>{e===t.length-1?r(0):r(e+1)},2e3);return()=>clearTimeout(a)},[e,t]),(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)("div",{className:"container mx-auto",children:(0,a.jsxs)("div",{className:"flex gap-8 py-20 lg:py-40 items-center justify-center flex-col",children:[(0,a.jsx)("div",{children:(0,a.jsxs)(c.$,{variant:"secondary",size:"sm",className:"gap-4 border-0",style:{backgroundColor:"var(--sidebar-surface-secondary)",color:"var(--sidebar-text-secondary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="var(--sidebar-surface-secondary)"},children:["Read our launch article ",(0,a.jsx)(l,{className:"w-4 h-4"})]})}),(0,a.jsxs)("div",{className:"flex gap-4 flex-col",children:[(0,a.jsxs)("h1",{className:"text-5xl md:text-7xl max-w-2xl tracking-tighter text-center font-regular",children:[(0,a.jsx)("span",{className:"text-center text-5xl md:text-7xl max-w-2xl font-regular",style:{color:"var(--sidebar-text-primary)"},children:"This is something"}),(0,a.jsxs)("span",{className:"relative flex w-full justify-center overflow-hidden text-center md:pb-4 md:pt-1",children:["\xa0",t.map((r,t)=>(0,a.jsx)(n.P.span,{className:"absolute font-semibold",style:{color:"var(--sidebar-text-primary)"},initial:{opacity:0,y:-100},transition:{type:"spring",stiffness:50},animate:e===t?{y:0,opacity:1}:{y:e>t?-150:150,opacity:0},children:r},t))]})]}),(0,a.jsx)("p",{className:"text-lg md:text-xl leading-relaxed tracking-tight max-w-2xl text-center",style:{color:"var(--sidebar-text-secondary)"},children:"Managing a small business today is already tough. Avoid further complications by ditching outdated, tedious trade methods. Our goal is to streamline SMB trade, making it easier and faster than ever."})]}),(0,a.jsxs)("div",{className:"flex flex-row gap-3",children:[(0,a.jsxs)(c.$,{size:"lg",className:"gap-4 border-0",variant:"outline",style:{backgroundColor:"transparent",color:"var(--sidebar-text-secondary)",border:"1px solid var(--sidebar-border)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:["Jump on a call ",(0,a.jsx)(i,{className:"w-4 h-4"})]}),(0,a.jsxs)(c.$,{size:"lg",className:"gap-4 border-0",style:{backgroundColor:"var(--surface-selected)",color:"var(--sidebar-text-primary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="var(--surface-selected)"},children:["Sign up here ",(0,a.jsx)(l,{className:"w-4 h-4"})]})]})]})})})}var u=t(74045),m=t(35695),h=t(6874),g=t.n(h),x=t(93509),b=t(62098),v=t(51362),y=t(46486);function f(e){let{className:r}=e,[t,n]=(0,s.useState)(!1),{resolvedTheme:o,setTheme:l}=(0,v.D)(),i="dark"===o;return((0,s.useEffect)(()=>{n(!0)},[]),t)?(0,a.jsx)("div",{className:(0,y.cn)("flex w-16 h-8 p-1 rounded-full cursor-pointer transition-all duration-300",i?"bg-zinc-950 border border-zinc-800":"bg-white border border-zinc-200",r),onClick:()=>l(i?"light":"dark"),role:"button",tabIndex:0,children:(0,a.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,a.jsx)("div",{className:(0,y.cn)("flex justify-center items-center w-6 h-6 rounded-full transition-transform duration-300",i?"transform translate-x-0 bg-zinc-800":"transform translate-x-8 bg-gray-200"),children:i?(0,a.jsx)(x.A,{className:"w-4 h-4 text-white",strokeWidth:1.5}):(0,a.jsx)(b.A,{className:"w-4 h-4 text-gray-700",strokeWidth:1.5})}),(0,a.jsx)("div",{className:(0,y.cn)("flex justify-center items-center w-6 h-6 rounded-full transition-transform duration-300",i?"bg-transparent":"transform -translate-x-8"),children:i?(0,a.jsx)(b.A,{className:"w-4 h-4 text-gray-500",strokeWidth:1.5}):(0,a.jsx)(x.A,{className:"w-4 h-4 text-black",strokeWidth:1.5})})]})}):(0,a.jsx)("div",{className:(0,y.cn)("flex w-16 h-8 p-1 rounded-full cursor-pointer transition-all duration-300 bg-gray-200 border border-gray-300",r),role:"button",tabIndex:0,children:(0,a.jsx)("div",{className:"flex justify-center items-center w-6 h-6 rounded-full bg-gray-300",children:(0,a.jsx)("div",{className:"w-4 h-4 bg-gray-400 rounded-full"})})})}function p(){let{isAuthenticated:e,signIn:r}=(0,u.A)(),t=(0,m.useRouter)(),s=async()=>{e?t.push("/dashboard"):await r(void 0,!0)};return(0,a.jsxs)("div",{className:"min-h-screen",style:{backgroundColor:"var(--sidebar-bg)"},children:[(0,a.jsx)("header",{className:"backdrop-blur supports-[backdrop-filter]:bg-background/60",style:{borderBottom:"1px solid var(--sidebar-border)",backgroundColor:"var(--sidebar-bg)"},children:(0,a.jsxs)("div",{className:"container mx-auto px-4 h-16 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-lg flex items-center justify-center",style:{backgroundColor:"var(--surface-selected)"},children:(0,a.jsxs)("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"w-5 h-5",style:{color:"var(--sidebar-icon)"},children:[(0,a.jsx)("path",{d:"M12 2L2 7L12 12L22 7L12 2Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M2 17L12 22L22 17",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M2 12L12 17L22 12",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}),(0,a.jsx)("h1",{className:"text-xl font-semibold",style:{color:"var(--sidebar-text-primary)"},children:"Agent Platform"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(f,{}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:e?(0,a.jsx)(c.$,{onClick:()=>t.push("/dashboard"),className:"border-0",style:{backgroundColor:"var(--surface-selected)",color:"var(--sidebar-text-primary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="var(--surface-selected)"},children:"Go to Dashboard"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.$,{variant:"ghost",onClick:s,className:"border-0",style:{color:"var(--sidebar-text-secondary)",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:"Sign In"}),(0,a.jsx)(c.$,{asChild:!0,className:"border-0",style:{backgroundColor:"var(--surface-selected)",color:"var(--sidebar-text-primary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="var(--surface-selected)"},children:(0,a.jsx)(g(),{href:"/register",children:"Get Started"})})]})})]})]})}),(0,a.jsx)("main",{children:(0,a.jsx)(d,{})}),(0,a.jsx)("footer",{style:{borderTop:"1px solid var(--sidebar-border)",backgroundColor:"var(--sidebar-bg)"},children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"text-center text-sm",style:{color:"var(--sidebar-text-tertiary)"},children:(0,a.jsx)("p",{children:"\xa9 2024 Agent Platform. All rights reserved."})})})})]})}},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>c});var a=t(95155),s=t(12115),n=t(99708),o=t(74466),l=t(46486);let i=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,r)=>{let{className:t,variant:s,size:o,asChild:c=!1,...d}=e,u=c?n.DX:"button";return(0,a.jsx)(u,{className:(0,l.cn)(i({variant:s,size:o,className:t})),ref:r,...d})});c.displayName="Button"},46486:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var a=t(52596),s=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},51362:(e,r,t)=>{"use strict";t.d(r,{D:()=>c,N:()=>d});var a=t(12115),s=(e,r,t,a,s,n,o,l)=>{let i=document.documentElement,c=["light","dark"];function d(r){var t;(Array.isArray(e)?e:[e]).forEach(e=>{let t="class"===e,a=t&&n?s.map(e=>n[e]||e):s;t?(i.classList.remove(...a),i.classList.add(n&&n[r]?n[r]:r)):i.setAttribute(e,r)}),t=r,l&&c.includes(t)&&(i.style.colorScheme=t)}if(a)d(a);else try{let e=localStorage.getItem(r)||t,a=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(a)}catch(e){}},n=["light","dark"],o="(prefers-color-scheme: dark)",l=a.createContext(void 0),i={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=a.useContext(l))?e:i},d=e=>a.useContext(l)?a.createElement(a.Fragment,null,e.children):a.createElement(m,{...e}),u=["light","dark"],m=e=>{let{forcedTheme:r,disableTransitionOnChange:t=!1,enableSystem:s=!0,enableColorScheme:i=!0,storageKey:c="theme",themes:d=u,defaultTheme:m=s?"system":"light",attribute:v="data-theme",value:y,children:f,nonce:p,scriptProps:k}=e,[j,w]=a.useState(()=>g(c,m)),[N,C]=a.useState(()=>"system"===j?b():j),M=y?Object.values(y):d,L=a.useCallback(e=>{let r=e;if(!r)return;"system"===e&&s&&(r=b());let a=y?y[r]:r,o=t?x(p):null,l=document.documentElement,c=e=>{"class"===e?(l.classList.remove(...M),a&&l.classList.add(a)):e.startsWith("data-")&&(a?l.setAttribute(e,a):l.removeAttribute(e))};if(Array.isArray(v)?v.forEach(c):c(v),i){let e=n.includes(m)?m:null,t=n.includes(r)?r:e;l.style.colorScheme=t}null==o||o()},[p]),T=a.useCallback(e=>{let r="function"==typeof e?e(j):e;w(r);try{localStorage.setItem(c,r)}catch(e){}},[j]),A=a.useCallback(e=>{C(b(e)),"system"===j&&s&&!r&&L("system")},[j,r]);a.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(A),A(e),()=>e.removeListener(A)},[A]),a.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?w(e.newValue):T(m))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[T]),a.useEffect(()=>{L(null!=r?r:j)},[r,j]);let E=a.useMemo(()=>({theme:j,setTheme:T,forcedTheme:r,resolvedTheme:"system"===j?N:j,themes:s?[...d,"system"]:d,systemTheme:s?N:void 0}),[j,T,r,N,s,d]);return a.createElement(l.Provider,{value:E},a.createElement(h,{forcedTheme:r,storageKey:c,attribute:v,enableSystem:s,enableColorScheme:i,defaultTheme:m,value:y,themes:d,nonce:p,scriptProps:k}),f)},h=a.memo(e=>{let{forcedTheme:r,storageKey:t,attribute:n,enableSystem:o,enableColorScheme:l,defaultTheme:i,value:c,themes:d,nonce:u,scriptProps:m}=e,h=JSON.stringify([n,t,i,r,d,c,o,l]).slice(1,-1);return a.createElement("script",{...m,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(s.toString(),")(").concat(h,")")}})}),g=(e,r)=>{let t;try{t=localStorage.getItem(e)||void 0}catch(e){}return t||r},x=e=>{let r=document.createElement("style");return e&&r.setAttribute("nonce",e),r.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(r),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(r)},1)}},b=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},62098:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},68731:(e,r,t)=>{Promise.resolve().then(t.bind(t,13896))},93509:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[464,817,874,408,189,45,441,684,358],()=>r(68731)),_N_E=e.O()}]);