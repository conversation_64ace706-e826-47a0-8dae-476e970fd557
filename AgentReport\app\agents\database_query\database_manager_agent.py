"""Database Manager Agent

This module provides a manager agent for handling a database with potentially many tables.
For large databases, it creates sub-agents to handle subsets of tables.
"""

import asyncio
import logging
import uuid
import math
from typing import Dict, List, Any, Optional, Set

from app.agents.base import Agent, AgentResponse
from app.agents.database_query.database_agent import EnhancedDatabaseAgent
from app.models.database import Database
from app.services.database_service import DatabaseService
from app.config import settings
from app.utils.error_utils import friendly_agent_errors

logger = logging.getLogger(__name__)

class DatabaseManagerAgent(Agent):
    """Manager agent responsible for a database, potentially with sub-agents."""
    
    def __init__(
        self, 
        database: Database,
        database_service: DatabaseService,
        agent_id: Optional[str] = None
    ):
        """Initialize the database manager agent.
        
        Args:
            database: The database this agent is responsible for
            database_service: The database service for interacting with the database
            agent_id: Optional ID for this agent (generated if not provided)
        """
        self.database = database
        self.database_service = database_service
        self.agent_id = agent_id or f"db_manager_{uuid.uuid4().hex[:8]}"
        self.sub_agents: List[EnhancedDatabaseAgent] = []
        self.initialized = False
        self.has_sub_agents = False
        self.max_tables_per_agent = settings.MAX_TABLES_PER_AGENT
        
    async def initialize(self) -> None:
        """Initialize the agent and create sub-agents if needed."""
        if self.initialized:
            return
            
        # Connect to the database to get table information
        success, error = await self.database_service.connect_database(self.database)
        if not success:
            logger.error(f"Failed to initialize agent {self.agent_id}: {error}")
            raise RuntimeError(f"Failed to connect to database: {error}")
            
        # Get list of tables
        try:
            table_names = await self.database_service.list_tables(self.database.id, schema=self.database.credentials.db_schema)
            
            # Determine if we need sub-agents
            if len(table_names) > self.max_tables_per_agent:
                self.has_sub_agents = True
                num_sub_agents = math.ceil(len(table_names) / self.max_tables_per_agent)
                
                logger.info(f"Creating {num_sub_agents} sub-agents for database {self.database.id} with {len(table_names)} tables")
                
                # Create table subsets for each sub-agent
                table_chunks = [table_names[i:i + self.max_tables_per_agent] 
                               for i in range(0, len(table_names), self.max_tables_per_agent)]
                
                # Create sub-agents
                for i, table_subset in enumerate(table_chunks):
                    sub_agent = EnhancedDatabaseAgent(
                        database=self.database,
                        database_service=self.database_service,
                        agent_id=f"{self.agent_id}_sub_{i}",
                        parent_agent_id=self.agent_id,
                        tables_subset=table_subset
                    )
                    self.sub_agents.append(sub_agent)
                    # Initialize each sub-agent
                    await sub_agent.initialize()
                    
                logger.info(f"Created and initialized {len(self.sub_agents)} sub-agents")
                
            else:
                # Create a single agent to handle all tables
                logger.info("Creating single agent to handle all tables")
                self.has_sub_agents = False
                sub_agent = EnhancedDatabaseAgent(
                    database=self.database,
                    database_service=self.database_service,
                    agent_id=f"{self.agent_id}_single",
                    parent_agent_id=self.agent_id
                )
                self.sub_agents.append(sub_agent)
                await sub_agent.initialize()
                
            self.initialized = True
            
        except Exception as e:
            logger.error(f"Error initializing manager agent {self.agent_id}: {str(e)}")
            raise
        
    @friendly_agent_errors("db-manager")
    async def process(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process a message by querying all sub-agents in parallel.
        
        Args:
            message: The message containing the user query
            
        Returns:
            An aggregated AgentResponse with all relevant information
        """
        if not self.initialized:
            await self.initialize()
            
        action = message.get("action")
        query = message.get("query", "")
        
        # Allow proceeding if action is 'get_schema', even with empty query string
        if not query and action != "get_schema": 
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error="No query provided"
            ).to_dict()
            
        try:
            # Process the query with all sub-agents in parallel using non-streaming version
            tasks = [agent.process_sync(message) for agent in self.sub_agents]
            results = await asyncio.gather(*tasks)
            
            # Collect all relevant tables from all sub-agents
            all_relevant_tables = []
            has_relevant_info = False
            
            for result in results:
                response = AgentResponse.from_dict(result)
                if response.has_relevant_info:
                    has_relevant_info = True
                    tables = response.data.get("tables", [])
                    all_relevant_tables.extend(tables)
            
            # Aggregate results
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=has_relevant_info,
                data={
                    "database_id": self.database.id,
                    "database_name": self.database.name,
                    "database_type": self.database.db_type,
                    "tables": all_relevant_tables
                },
                metadata={
                    "sub_agents": len(self.sub_agents),
                    "table_count": len(all_relevant_tables)
                }
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Error processing query in manager agent {self.agent_id}: {str(e)}")
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error=f"Error processing query: {str(e)}"
            ).to_dict() 
