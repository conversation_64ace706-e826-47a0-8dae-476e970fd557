"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { usePageTitle } from '@/hooks/usePageTitle';
import { usePageHeader } from '@/providers/PageHeaderContext';
import { Brain } from 'lucide-react';
import AnalysisProjectList from './AnalysisProjectList';
import AnalysisProjectView from './AnalysisProjectView';
import { useApi } from '@/providers/ApiContext';
import { AnalysisProject } from '@/types';

interface AnalysisNavigationState {
  currentView: 'list' | 'project';
  selectedProject: AnalysisProject | null;
  breadcrumbs: Array<{
    label: string;
    onClick?: () => void;
  }>;
}

const AIWorkflowsPageContent = () => {
  // API context
  const {
    listAnalysisProjects,
    createAnalysisProject,
    updateAnalysisProject,
    deleteAnalysisProject,
  } = useApi();

  // Navigation state
  const [navigationState, setNavigationState] = useState<AnalysisNavigationState>({
    currentView: 'list',
    selectedProject: null,
    breadcrumbs: [{ label: 'Analysis Projects' }],
  });

  // Analysis projects data
  const [analysisProjects, setAnalysisProjects] = useState<AnalysisProject[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);
  const [isCreatingProject, setIsCreatingProject] = useState(false);

  // Chat state for project view
  const [isChatOpen, setIsChatOpen] = useState(false);

  const { setPageActions } = usePageHeader();

  // Load analysis projects on component mount
  useEffect(() => {
    loadAnalysisProjects();
  }, []);

  const loadAnalysisProjects = useCallback(async () => {
    setIsLoadingProjects(true);
    try {
      const response = await listAnalysisProjects();
      if (response.success && response.data) {
        // Convert date strings to Date objects for compatibility
        const projects = response.data.projects.map((project: any) => ({
          ...project,
          createdAt: new Date(project.createdAt),
          updatedAt: new Date(project.updatedAt),
        }));
        setAnalysisProjects(projects);
      }
    } catch (error) {
      console.error('Failed to load analysis projects:', error);
      setAnalysisProjects([]); // Set to empty array on error
    } finally {
      setIsLoadingProjects(false);
    }
  }, [listAnalysisProjects]);

  // Navigation handlers
  const handleSelectProject = useCallback((project: AnalysisProject) => {
    setNavigationState({
      currentView: 'project',
      selectedProject: project,
      breadcrumbs: [
        { label: 'Analysis Projects', onClick: () => handleNavigateBack() },
        { label: project.name },
      ],
    });
  }, []);

  const handleNavigateBack = useCallback(() => {
    setNavigationState({
      currentView: 'list',
      selectedProject: null,
      breadcrumbs: [{ label: 'Analysis Projects' }],
    });
  }, []);

  // Set page title dynamically based on navigation state
  const pageConfig = useMemo(() => ({
    title: navigationState.currentView === 'project' && navigationState.selectedProject 
      ? navigationState.selectedProject.name
      : 'Analysis Projects',
    icon: Brain,
    breadcrumbs: navigationState.currentView === 'project' && navigationState.selectedProject
      ? [
          { label: 'Analysis Projects', onClick: () => handleNavigateBack() },
          { label: navigationState.selectedProject.name },
        ]
      : [{ label: 'Analysis Projects' }],
  }), [navigationState.currentView, navigationState.selectedProject, handleNavigateBack]);

  usePageTitle(pageConfig);

  // Project CRUD handlers
  const handleCreateProject = useCallback(async () => {
    setIsCreatingProject(true);
    try {
      // Create project with default name
      const defaultName = `Analysis Project ${analysisProjects.length + 1}`;
      const request = {
        name: defaultName,
        description: '',
      };

      const response = await createAnalysisProject(request);
      
      if (response.success && response.data) {
        // Convert date strings to Date objects
        const newProject = {
          ...response.data,
          createdAt: new Date(response.data.createdAt),
          updatedAt: new Date(response.data.updatedAt),
        };

        setAnalysisProjects(prev => [...prev, newProject]);

        // Immediately navigate to the new project
        handleSelectProject(newProject);
      }
    } catch (error) {
      console.error('Failed to create analysis project:', error);
    } finally {
      setIsCreatingProject(false);
    }
  }, [analysisProjects.length, createAnalysisProject, handleSelectProject]);

  const handleUpdateProject = useCallback(async (projectId: string, updates: Partial<AnalysisProject>) => {
    try {
      const response = await updateAnalysisProject(projectId, updates);
      
      if (response.success && response.data) {
        // Convert date strings to Date objects
        const updatedProject = {
          ...response.data,
          createdAt: new Date(response.data.createdAt),
          updatedAt: new Date(response.data.updatedAt),
        };

        setAnalysisProjects(prev => prev.map(p =>
          p.id === projectId ? updatedProject : p
        ));

        // Update the selected project if it's the one being edited
        if (navigationState.selectedProject?.id === projectId) {
          setNavigationState(prev => ({
            ...prev,
            selectedProject: updatedProject,
            breadcrumbs: [
              { label: 'Analysis Projects', onClick: () => handleNavigateBack() },
              { label: updatedProject.name },
            ],
          }));
        }
      }
    } catch (error) {
      console.error('Failed to update analysis project:', error);
    }
  }, [updateAnalysisProject, navigationState.selectedProject, handleNavigateBack]);

  const handleDeleteProject = useCallback(async (projectId: string) => {
    if (!confirm('Are you sure you want to delete this analysis project? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await deleteAnalysisProject(projectId);
      
      if (response.success) {
        setAnalysisProjects(prev => prev.filter(p => p.id !== projectId));

        // If we're currently viewing the deleted project, navigate back
        if (navigationState.selectedProject?.id === projectId) {
          handleNavigateBack();
        }
      }
    } catch (error) {
      console.error('Failed to delete analysis project:', error);
    }
  }, [deleteAnalysisProject, navigationState.selectedProject, handleNavigateBack]);

  // Update page actions when navigation state changes
  useEffect(() => {
    const isListView = navigationState.currentView === 'list';
    const isProjectView = navigationState.currentView === 'project' && navigationState.selectedProject;
    
    setPageActions({
      // Show New Analysis button only on list view
      onCreateAnalysis: isListView ? handleCreateProject : undefined,
      isCreatingAnalysis: isListView ? isCreatingProject : false,
      
      // Show export and chat actions only on project view
      onExport: isProjectView ? () => {
        console.log('Exporting project data...');
        // TODO: Implement export functionality
      } : undefined,
      onToggleChat: isProjectView ? () => setIsChatOpen(!isChatOpen) : undefined,
      isChatOpen: isProjectView ? isChatOpen : false,
    });
  }, [navigationState.currentView, navigationState.selectedProject, setPageActions, handleCreateProject, isCreatingProject, isChatOpen]);

  return (
    <div
      className="min-h-screen"
      style={{ backgroundColor: 'var(--sidebar-bg)' }}
    >
      <div className="container mx-auto p-6 space-y-6">

        {/* Main Content */}
        {navigationState.currentView === 'list' ? (
          <AnalysisProjectList
            projects={analysisProjects}
            onSelectProject={handleSelectProject}
            onDeleteProject={handleDeleteProject}
            isLoading={isLoadingProjects}
          />
        ) : navigationState.selectedProject ? (
          <AnalysisProjectView
            project={navigationState.selectedProject}
            onUpdateProject={handleUpdateProject}
            isChatOpen={isChatOpen}
            onToggleChat={() => setIsChatOpen(!isChatOpen)}
          />
        ) : null}
      </div>
    </div>
  );
};

export default AIWorkflowsPageContent;