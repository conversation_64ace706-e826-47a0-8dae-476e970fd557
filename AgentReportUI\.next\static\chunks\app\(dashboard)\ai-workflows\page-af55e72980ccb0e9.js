(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[546],{12486:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},14186:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},28857:(e,r,a)=>{"use strict";a.d(r,{k:()=>j});var s=a(95155),t=a(12115),l=a(46081),o=a(63655),i="Progress",[c,d]=(0,l.A)(i),[n,u]=c(i),x=t.forwardRef((e,r)=>{var a,t,l,i;let{__scopeProgress:c,value:d=null,max:u,getValueLabel:x=h,...m}=e;(u||0===u)&&!v(u)&&console.error((a="".concat(u),t="Progress","Invalid prop `max` of value `".concat(a,"` supplied to `").concat(t,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let b=v(u)?u:100;null===d||g(d,b)||console.error((l="".concat(d),i="Progress","Invalid prop `value` of value `".concat(l,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let f=g(d,b)?d:null,j=y(f)?x(f,b):void 0;return(0,s.jsx)(n,{scope:c,value:f,max:b,children:(0,s.jsx)(o.sG.div,{"aria-valuemax":b,"aria-valuemin":0,"aria-valuenow":y(f)?f:void 0,"aria-valuetext":j,role:"progressbar","data-state":p(f,b),"data-value":null!=f?f:void 0,"data-max":b,...m,ref:r})})});x.displayName=i;var m="ProgressIndicator",b=t.forwardRef((e,r)=>{var a;let{__scopeProgress:t,...l}=e,i=u(m,t);return(0,s.jsx)(o.sG.div,{"data-state":p(i.value,i.max),"data-value":null!=(a=i.value)?a:void 0,"data-max":i.max,...l,ref:r})});function h(e,r){return"".concat(Math.round(e/r*100),"%")}function p(e,r){return null==e?"indeterminate":e===r?"complete":"loading"}function y(e){return"number"==typeof e}function v(e){return y(e)&&!isNaN(e)&&e>0}function g(e,r){return y(e)&&!isNaN(e)&&e<=r&&e>=0}b.displayName=m;var f=a(46486);let j=t.forwardRef((e,r)=>{let{className:a,value:t,...l}=e;return(0,s.jsx)(x,{ref:r,className:(0,f.cn)("relative h-2 w-full overflow-hidden rounded-full bg-slate-100 dark:bg-slate-800",a),...l,children:(0,s.jsx)(b,{className:"h-full w-full flex-1 bg-slate-900 transition-all dark:bg-slate-50",style:{transform:"translateX(-".concat(100-(t||0),"%)")}})})});j.displayName=x.displayName},30095:(e,r,a)=>{"use strict";a.d(r,{Ay:()=>c});var s=a(95155),t=a(12115),l=a(74045),o=a(35695),i=a(45786);function c(e){let{children:r,requireAuth:a=!0,redirectTo:c="/login"}=e,{isAuthenticated:d,isLoading:n,isNewUser:u,signIn:x}=(0,l.A)(),m=(0,o.useRouter)(),b=(0,o.usePathname)(),[h,p]=(0,t.useState)(!0),[y,v]=(0,t.useState)(!1);return((0,t.useEffect)(()=>{(async()=>{if(!a||[i.bw.HOME,i.bw.LOGIN,i.bw.REGISTER,i.bw.AUTH.CALLBACK,i.bw.OAUTH.CALLBACK,i.bw.ONBOARDING].includes(b))return p(!1);if(d){if(u&&"/onboarding"!==b){console.log("New user on protected route, redirecting to onboarding"),m.push("/onboarding");return}if(!u&&"/onboarding"===b){console.log("Existing user on onboarding, redirecting to dashboard"),m.push("/dashboard");return}p(!1);return}if(!d&&!y&&!n){v(!0),console.log("Attempting automatic authentication from stored tokens...");try{await x(void 0,!1)}catch(e){console.log("Auto-authentication failed, redirecting to login"),m.push(c)}return}if(!d&&y&&!n){console.log("Not authenticated, redirecting to login"),m.push(c);return}p(!1)})()},[d,n,u,b,a,c,m,x,y]),h||n)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Checking authentication..."})]})}):a&&!d?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Authentication Required"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please sign in to access this page."}),(0,s.jsx)("button",{onClick:()=>m.push(c),className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Sign In"})]})}):(0,s.jsx)(s.Fragment,{children:r})}},40646:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},42837:(e,r,a)=>{Promise.resolve().then(a.bind(a,67671))},54416:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},57434:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},62523:(e,r,a)=>{"use strict";a.d(r,{p:()=>l});var s=a(95155);a(12115);var t=a(46486);function l(e){let{className:r,type:a,...l}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,t.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...l})}},62525:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},65112:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},66695:(e,r,a)=>{"use strict";a.d(r,{BT:()=>c,Wu:()=>d,ZB:()=>i,Zp:()=>l,aR:()=>o});var s=a(95155);a(12115);var t=a(46486);function l(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...a})}function o(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...a})}function i(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("leading-none font-semibold text-white",r),...a})}function c(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-muted-foreground text-sm",r),...a})}function d(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",r),...a})}},66932:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},67671:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>O});var s=a(95155),t=a(12115),l=a(74677),o=a(87914),i=a(10071),c=a(49376),d=a(30285),n=a(66695),u=a(57434),x=a(85690),m=a(40646),b=a(85339),h=a(44020),p=a(54213),y=a(14186),v=a(44838);let g=e=>{let{projects:r,onSelectProject:a,onDeleteProject:t,isLoading:l}=e,o=e=>{let r=new Date,a=e instanceof Date?e:new Date(e),s=r.getTime()-a.getTime(),t=s/36e5,l=s/864e5;return t<1?"Just now":t<24?"".concat(Math.floor(t),"h ago"):l<7?"".concat(Math.floor(l),"d ago"):a.toLocaleDateString()},i=e=>({draft:{color:"var(--sidebar-text-tertiary)",icon:u.A,text:"Draft"},running:{color:"#f59e0b",icon:x.A,text:"Running"},completed:{color:"#10b981",icon:m.A,text:"Completed"},error:{color:"#ef4444",icon:b.A,text:"Error"}})[e];return(0,s.jsx)("div",{className:"space-y-6",children:l?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,r)=>(0,s.jsxs)(n.Zp,{className:"border animate-pulse",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)"},children:[(0,s.jsx)(n.aR,{className:"pb-3",children:(0,s.jsx)("div",{className:"flex items-start justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-9 h-9 rounded-lg",style:{backgroundColor:"var(--sidebar-text-tertiary)"}}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"h-4 w-24 rounded",style:{backgroundColor:"var(--sidebar-text-tertiary)"}}),(0,s.jsx)("div",{className:"h-3 w-16 rounded",style:{backgroundColor:"var(--sidebar-text-tertiary)"}})]})]})})}),(0,s.jsx)(n.Wu,{className:"pt-0",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"h-3 w-full rounded",style:{backgroundColor:"var(--sidebar-text-tertiary)"}}),(0,s.jsx)("div",{className:"h-3 w-2/3 rounded",style:{backgroundColor:"var(--sidebar-text-tertiary)"}})]})})]},r))}):r.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map(e=>{let r=i(e.status),l=r.icon;return(0,s.jsxs)(n.Zp,{className:"group cursor-pointer transition-all duration-200 border hover:shadow-lg",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)"},onClick:()=>a(e),onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)",e.currentTarget.style.transform="translateY(-1px)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="var(--sidebar-surface-secondary)",e.currentTarget.style.transform="translateY(0)"},children:[(0,s.jsx)(n.aR,{className:"pb-3",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)(n.ZB,{className:"text-base mb-1 truncate",style:{color:"var(--sidebar-text-primary)"},children:e.name}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l,{className:"h-3 w-3",style:{color:r.color}}),(0,s.jsxs)("span",{className:"text-xs",style:{color:r.color},children:[r.text,"running"===e.status&&e.progress&&" (".concat(e.progress,"%)")]})]})]})}),(0,s.jsx)("div",{onClick:e=>e.stopPropagation(),children:(0,s.jsxs)(v.rI,{children:[(0,s.jsx)(v.ty,{asChild:!0,children:(0,s.jsx)(d.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 border-0",style:{color:"var(--sidebar-text-tertiary)"},children:(0,s.jsx)(h.A,{className:"h-4 w-4"})})}),(0,s.jsx)(v.SQ,{align:"end",className:"border-none shadow-xl rounded-xl p-2",style:{backgroundColor:"var(--sidebar-surface-secondary)",color:"var(--sidebar-text-primary)"},children:(0,s.jsx)(v._2,{onClick:()=>t(e.id),className:"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200",style:{color:"#ff8583",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="rgba(255, 133, 131, 0.1)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:"Delete Project"})})]})})]})}),(0,s.jsxs)(n.Wu,{className:"pt-0",children:[e.description&&(0,s.jsx)("p",{className:"text-sm mb-3 line-clamp-2",style:{color:"var(--sidebar-text-secondary)"},children:e.description}),(0,s.jsxs)("div",{className:"space-y-2",children:[e.dataSource&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-3 w-3",style:{color:"var(--sidebar-text-tertiary)"}}),(0,s.jsx)("span",{className:"text-xs",style:{color:"var(--sidebar-text-tertiary)"},children:e.dataSource})]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("span",{className:"text-xs",style:{color:"var(--sidebar-text-tertiary)"},children:[e.stepCount," ",1===e.stepCount?"step":"steps"]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(y.A,{className:"h-3 w-3",style:{color:"var(--sidebar-text-tertiary)"}}),(0,s.jsx)("span",{className:"text-xs",style:{color:"var(--sidebar-text-tertiary)"},children:o(e.updatedAt)})]})]})})]})]})]},e.id)})}):(0,s.jsx)(n.Zp,{className:"text-center py-12 border-dashed border-2",style:{backgroundColor:"var(--sidebar-bg)",borderColor:"var(--sidebar-border)"},children:(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"p-4 rounded-full w-fit mx-auto",style:{backgroundColor:"var(--surface-selected)"}}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",style:{color:"var(--sidebar-text-primary)"},children:"No analysis projects yet"}),(0,s.jsx)("p",{className:"mt-2",style:{color:"var(--sidebar-text-secondary)"},children:'Create your first analysis project using the "Create Analysis" button in the top right corner to get started with AI-powered data insights'})]})]})})})};var f=a(62523),j=a(66932),N=a(19946);let w=(0,N.A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]]);var k=a(65112),C=a(72713);let A=(0,N.A)("code-xml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]]);var M=a(13052);let P=(0,N.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),S=(0,N.A)("maximize-2",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"m21 3-7 7",key:"1l2asr"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M9 21H3v-6",key:"wtvkvv"}]]),T=(0,N.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),D=(0,N.A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]);var E=a(62525),z=a(91788),L=a(54416),V=a(12486),F=a(58189),R=a(28857);let B=e=>{let{project:r,onUpdateProject:a,isChatOpen:l,onToggleChat:o}=e,{getProjectData:c,getProjectSteps:u,uploadProjectFile:x,deleteProjectFile:m}=(0,F.g)(),[b,h]=(0,t.useState)("data"),[p,y]=(0,t.useState)([]),[v,g]=(0,t.useState)(null),[N,B]=(0,t.useState)(!1),[Z,I]=(0,t.useState)(!1),[O,q]=(0,t.useState)(new Set),[H,W]=(0,t.useState)(""),[$,U]=(0,t.useState)([]),[_,G]=(0,t.useState)(null),[K,Y]=(0,t.useState)(!1),[J,Q]=(0,t.useState)(!1),X=(0,t.useRef)(null),{setPageActions:ee}=(0,i.s)();(0,t.useEffect)(()=>{ee({onExport:()=>{console.log("Exporting results...")},onToggleChat:()=>o(),isChatOpen:l})},[l,ee]),(0,t.useEffect)(()=>{r.id&&(er(),"data"===b&&ea())},[r.id]),(0,t.useEffect)(()=>{"data"===b&&r.id&&!v&&ea()},[b,r.id]);let er=(0,t.useCallback)(async()=>{B(!0);try{let e=await u(r.id);e.success&&e.data&&y(e.data)}catch(e){console.error("Failed to load pipeline steps:",e),y([])}finally{B(!1)}},[r.id,u]),ea=(0,t.useCallback)(async()=>{I(!0);try{let e=await c(r.id);e.success&&e.data&&g(e.data)}catch(e){console.error("Failed to load project data:",e),g(null)}finally{I(!1)}},[r.id,c]),es=e=>{q(r=>{let a=new Set(r);return a.has(e)?a.delete(e):a.add(e),a})},et=e=>{switch(e){case"completed":return(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"});case"running":return(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"});case"error":return(0,s.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"});default:return(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"})}},el=p.filter(e=>"completed"===e.status).length,eo=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),ei=(e,r)=>null==e?"N/A":r.toLowerCase().includes("salary")&&"number"==typeof e?eo(e):"number"==typeof e&&e%1!=0?e.toFixed(2):String(e),ec=()=>{H.trim()&&(console.log("Sending message:",H),W(""))},ed=e=>{navigator.clipboard.writeText(e)},en=e=>{e&&0!==e.length&&eu(e[0])},eu=async e=>{if(!e)return;let a=[".csv",".xls",".xlsx"].some(r=>e.name.toLowerCase().endsWith(r));if(!["text/csv","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"].includes(e.type)&&!a)return void console.error("Invalid file type. Please upload CSV or Excel files only.");if(e.size>0xa00000)return void console.error("File size too large. Please upload files smaller than 10MB.");Y(!0),G(null);try{let a=await x({file:e,projectId:r.id,onProgress:e=>{G(e)}});if(a.success&&a.data){let r={id:a.data.fileId,filename:a.data.filename,size:e.size,type:e.type,uploadedAt:new Date().toISOString(),status:"completed"};U(e=>[...e,r]),g(a.data.previewData),console.log("File uploaded successfully:",a.data)}}catch(e){console.error("File upload failed:",e)}finally{Y(!1),G(null)}},ex=async e=>{try{(await m(r.id,e)).success&&(U(r=>r.filter(r=>r.id!==e)),1===$.length&&g(null))}catch(e){console.error("File deletion failed:",e)}},em=(0,t.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?Q(!0):"dragleave"===e.type&&Q(!1)},[]),eb=(0,t.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),Q(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&eu(e.dataTransfer.files[0])},[]),eh=e=>{if(0===e)return"0 Bytes";let r=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,r)).toFixed(2))+" "+["Bytes","KB","MB","GB"][r]};return(0,s.jsxs)("div",{className:"min-h-screen flex",style:{backgroundColor:"var(--sidebar-bg)"},role:"main","aria-label":"Analysis Project View",children:[(0,s.jsx)("div",{className:"flex-1 transition-all duration-300 ".concat(l?"mr-96":"mr-0"),children:(0,s.jsxs)("div",{className:"h-full flex flex-col p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"flex items-center gap-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(e=>{switch(e){case"completed":return(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"});case"running":return(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"});case"error":return(0,s.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"});default:return(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"})}})(r.status),(0,s.jsxs)("span",{className:"text-sm",style:{color:"var(--sidebar-text-tertiary)"},children:[el," of ",p.length," steps complete"]})]})}),(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsxs)(d.$,{variant:"ghost",size:"sm",className:"h-8 px-3 text-xs border-0",style:{color:"var(--sidebar-text-secondary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,s.jsx)(j.A,{className:"h-3 w-3 mr-1.5"}),"overview"===b?"All Steps":b.charAt(0).toUpperCase()+b.slice(1)]})})]}),(0,s.jsx)("div",{className:"flex items-center gap-1 mb-6",children:[{id:"overview",label:"Overview",icon:w},{id:"data",label:"Data",icon:k.A},{id:"charts",label:"Charts",icon:C.A},{id:"code",label:"Code",icon:A}].map(e=>{let r=e.icon,a=b===e.id;return(0,s.jsxs)(d.$,{variant:"ghost",size:"sm",onClick:()=>h(e.id),className:"h-8 px-3 text-xs border-0",style:{color:a?"var(--sidebar-text-primary)":"var(--sidebar-text-secondary)",backgroundColor:a?"var(--surface-selected)":"transparent"},onMouseEnter:e=>{a||(e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)")},onMouseLeave:e=>{a||(e.currentTarget.style.backgroundColor="transparent")},children:[(0,s.jsx)(r,{className:"h-3 w-3 mr-1.5"}),e.label]},e.id)})}),(0,s.jsxs)("div",{className:"flex-1 overflow-auto",children:["overview"===b&&(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsx)("div",{className:"grid gap-4",children:p.map((e,r)=>{var a;return(0,s.jsxs)(n.Zp,{className:"border cursor-pointer transition-all duration-200",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:O.has(e.id)?"#3b82f6":"var(--sidebar-border)"},onClick:()=>es(e.id),onMouseEnter:r=>{O.has(e.id)||(r.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)")},onMouseLeave:r=>{O.has(e.id)||(r.currentTarget.style.backgroundColor="var(--sidebar-surface-secondary)")},children:[(0,s.jsx)(n.aR,{className:"pb-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",style:{backgroundColor:"var(--surface-selected)",color:"var(--sidebar-text-primary)"},children:r+1}),(0,s.jsx)("div",{children:(0,s.jsx)(n.ZB,{className:"text-base",style:{color:"var(--sidebar-text-primary)"},children:e.title})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[et(e.status),(0,s.jsx)(M.A,{className:"h-4 w-4 transition-transform duration-200 ".concat(O.has(e.id)?"rotate-90":""),style:{color:"var(--sidebar-text-tertiary)"}})]})]})}),O.has(e.id)&&e.outputs&&(0,s.jsxs)(n.Wu,{className:"pt-0 space-y-4",children:[e.outputs.summary&&(0,s.jsx)("p",{className:"text-sm",style:{color:"var(--sidebar-text-secondary)"},children:e.outputs.summary}),e.outputs.data&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"text-sm font-medium mb-2 flex items-center gap-2",style:{color:"var(--sidebar-text-primary)"},children:[(0,s.jsx)(k.A,{className:"h-4 w-4"}),"Data Output"]}),(0,s.jsxs)("div",{className:"border rounded-lg p-3 text-center",style:{backgroundColor:"var(--sidebar-bg)",borderColor:"var(--sidebar-border)",color:"var(--sidebar-text-secondary)"},children:[(0,s.jsx)("p",{className:"text-sm",children:"Data output available"}),(0,s.jsx)("p",{className:"text-xs mt-1",style:{color:"var(--sidebar-text-tertiary)"},children:"View in Data tab for detailed table"})]})]}),e.outputs.visualization&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"text-sm font-medium mb-2 flex items-center gap-2",style:{color:"var(--sidebar-text-primary)"},children:[(0,s.jsx)(C.A,{className:"h-4 w-4"}),"Visualization"]}),(0,s.jsxs)("div",{className:"border rounded-lg p-4 text-center",style:{backgroundColor:"var(--sidebar-bg)",borderColor:"var(--sidebar-border)",color:"var(--sidebar-text-secondary)"},children:[(0,s.jsx)("p",{className:"text-sm",children:(null==(a=e.outputs.visualization.config)?void 0:a.title)||"Visualization"}),(0,s.jsx)("p",{className:"text-xs mt-1",style:{color:"var(--sidebar-text-tertiary)"},children:"View in Charts tab for interactive visualization"})]})]}),e.outputs.code&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("h4",{className:"text-sm font-medium flex items-center gap-2",style:{color:"var(--sidebar-text-primary)"},children:[(0,s.jsx)(A,{className:"h-4 w-4"}),"Generated Code"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.$,{variant:"ghost",size:"sm",onClick:r=>{r.stopPropagation(),ed(e.outputs.code)},className:"h-7 px-2 border-0",style:{color:"var(--sidebar-text-tertiary)"},children:(0,s.jsx)(P,{className:"h-3 w-3"})}),(0,s.jsx)(d.$,{variant:"ghost",size:"sm",onClick:r=>{r.stopPropagation(),es(e.id)},className:"h-7 px-2 border-0",style:{color:"var(--sidebar-text-tertiary)"},children:(0,s.jsx)(S,{className:"h-3 w-3"})})]})]}),(0,s.jsx)("div",{className:"border rounded-lg p-3 font-mono text-xs overflow-x-auto transition-all duration-200 ".concat(O.has(e.id)?"max-h-96":"max-h-32"),style:{backgroundColor:"var(--sidebar-bg)",borderColor:"var(--sidebar-border)",color:"var(--sidebar-text-secondary)"},children:(0,s.jsx)("pre",{children:e.outputs.code})})]})]})]},e.id)})})}),"data"===b&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"border border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer ".concat(J?"border-blue-400":""),style:{backgroundColor:J?"rgba(59, 130, 246, 0.05)":"var(--sidebar-surface-secondary)",borderColor:J?"#3b82f6":"var(--sidebar-border)"},onDragEnter:em,onDragLeave:em,onDragOver:em,onDrop:eb,onClick:()=>{var e;return null==(e=X.current)?void 0:e.click()},children:[(0,s.jsx)(T,{className:"h-5 w-5 mx-auto mb-2",style:{color:"var(--sidebar-text-tertiary)"}}),(0,s.jsxs)("p",{className:"text-xs",style:{color:"var(--sidebar-text-primary)"},children:["Drop files here or ",(0,s.jsx)("span",{className:"text-blue-500 hover:text-blue-600",children:"browse"})]}),(0,s.jsx)("p",{className:"text-xs mt-1",style:{color:"var(--sidebar-text-tertiary)"},children:"CSV, Excel up to 10MB"}),(0,s.jsx)("input",{ref:X,type:"file",accept:".csv,.xls,.xlsx",onChange:e=>en(e.target.files),className:"hidden"})]}),_&&(0,s.jsxs)("div",{className:"px-3 py-2 rounded-lg border",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)"},children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("span",{className:"text-xs",style:{color:"var(--sidebar-text-secondary)"},children:"Uploading..."}),(0,s.jsxs)("span",{className:"text-xs",style:{color:"var(--sidebar-text-tertiary)"},children:[_.percentage,"%"]})]}),(0,s.jsx)(R.k,{value:_.percentage,className:"h-1"})]}),$.length>0&&(0,s.jsx)("div",{className:"space-y-1",children:$.map(e=>(0,s.jsxs)("div",{className:"flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors duration-200",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="var(--sidebar-surface-secondary)"},children:[(0,s.jsx)(D,{className:"h-3 w-3 flex-shrink-0",style:{color:"var(--sidebar-icon)"}}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-xs font-medium truncate",style:{color:"var(--sidebar-text-primary)"},children:e.filename}),(0,s.jsx)("p",{className:"text-xs",style:{color:"var(--sidebar-text-tertiary)"},children:eh(e.size)})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:["completed"===e.status&&(0,s.jsx)("div",{className:"w-2 h-2 rounded-full",style:{backgroundColor:"#10b981"}}),(0,s.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>ex(e.id),className:"h-6 w-6 p-0 border-0 opacity-60 hover:opacity-100",style:{color:"var(--sidebar-text-tertiary)"},onMouseEnter:e=>{e.currentTarget.style.color="#ef4444"},onMouseLeave:e=>{e.currentTarget.style.color="var(--sidebar-text-tertiary)"},children:(0,s.jsx)(E.A,{className:"h-3 w-3"})})]})]},e.id))})]}),Z?(0,s.jsxs)(n.Zp,{className:"animate-pulse border",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)"},children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)("div",{className:"h-4 rounded w-1/4",style:{backgroundColor:"var(--interactive-bg-secondary-hover)"}})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"space-y-3",children:[1,2,3,4,5].map(e=>(0,s.jsx)("div",{className:"h-4 rounded",style:{backgroundColor:"var(--sidebar-bg)"}},e))})})]}):v&&v.totalRows>0?(0,s.jsxs)(n.Zp,{className:"border",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)"},children:[(0,s.jsxs)(n.aR,{className:"pb-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(n.ZB,{className:"text-sm font-medium",style:{color:"var(--sidebar-text-primary)"},children:"Data Preview"}),(0,s.jsx)("div",{className:"flex gap-2",children:(0,s.jsxs)(d.$,{variant:"ghost",size:"sm",className:"h-6 px-2 text-xs border-0",style:{color:"var(--sidebar-text-secondary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,s.jsx)(z.A,{className:"h-3 w-3 mr-1"}),"Export"]})})]}),(0,s.jsxs)("p",{className:"text-xs",style:{color:"var(--sidebar-text-tertiary)"},children:[v.totalRows," rows • ",v.columns.length," columns"]})]}),(0,s.jsx)(n.Wu,{className:"pt-0",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-sm",children:[(0,s.jsx)("thead",{children:(0,s.jsx)("tr",{style:{borderBottom:"1px solid var(--sidebar-border)"},children:v.columns.map(e=>(0,s.jsxs)("th",{className:"text-left p-2 text-xs font-medium",style:{color:"var(--sidebar-text-primary)",backgroundColor:"var(--sidebar-bg)"},children:[e.name,(0,s.jsxs)("span",{className:"ml-1",style:{color:"var(--sidebar-text-tertiary)"},children:["(",e.type,")"]})]},e.name))})}),(0,s.jsx)("tbody",{children:v.rows.map((e,r)=>(0,s.jsx)("tr",{className:"transition-colors duration-150",style:{borderBottom:"1px solid var(--sidebar-border)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:v.columns.map(r=>(0,s.jsx)("td",{className:"p-2 text-xs",style:{color:"var(--sidebar-text-secondary)"},children:ei(e[r.name],r.name)},r.name))},r))})]})})})]}):0===$.length?(0,s.jsx)(n.Zp,{className:"border",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)"},children:(0,s.jsxs)(n.Wu,{className:"p-8 text-center",children:[(0,s.jsx)(T,{className:"h-12 w-12 mx-auto mb-4",style:{color:"var(--sidebar-text-tertiary)"}}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",style:{color:"var(--sidebar-text-primary)"},children:"No data uploaded yet"}),(0,s.jsx)("p",{className:"mb-4",style:{color:"var(--sidebar-text-secondary)"},children:"Upload a CSV or Excel file to start analyzing your data"}),(0,s.jsxs)(d.$,{onClick:()=>{var e;return null==(e=X.current)?void 0:e.click()},className:"border-0",style:{backgroundColor:"var(--surface-selected)",color:"var(--sidebar-text-primary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--interactive-bg-secondary-hover)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="var(--surface-selected)"},children:[(0,s.jsx)(T,{className:"h-4 w-4 mr-2"}),"Upload Your First File"]})]})}):(0,s.jsx)(n.Zp,{className:"border",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)"},children:(0,s.jsx)(n.Wu,{className:"p-8 text-center",children:(0,s.jsx)("p",{style:{color:"var(--sidebar-text-secondary)"},children:"Processing uploaded files..."})})})]}),"charts"===b&&(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"p-8 text-center",children:[(0,s.jsx)("p",{className:"text-gray-500",children:"Charts and visualizations will be displayed here."}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Feature coming soon"})]})}),"code"===b&&(0,s.jsx)("div",{className:"space-y-6",children:p.filter(e=>{var r;return null==(r=e.outputs)?void 0:r.code}).map(e=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2",style:{color:"var(--sidebar-text-primary)"},children:[(0,s.jsx)(A,{className:"h-5 w-5"}),e.title," - Generated Code"]}),(0,s.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>ed(e.outputs.code),className:"border",style:{borderColor:"var(--sidebar-border)",color:"var(--sidebar-text-secondary)"},children:[(0,s.jsx)(P,{className:"h-4 w-4 mr-2"}),"Copy Code"]})]}),(0,s.jsx)("div",{className:"border rounded-lg p-4 font-mono text-sm overflow-x-auto",style:{backgroundColor:"var(--sidebar-bg)",borderColor:"var(--sidebar-border)",color:"var(--sidebar-text-secondary)"},children:(0,s.jsx)("pre",{children:e.outputs.code})})]},e.id))})]})]})}),l&&(0,s.jsxs)("div",{className:"fixed right-0 top-0 w-96 h-full border-l flex flex-col",style:{backgroundColor:"var(--sidebar-surface-secondary)",borderColor:"var(--sidebar-border)"},children:[(0,s.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",style:{borderColor:"var(--sidebar-border)"},children:[(0,s.jsx)("h3",{className:"font-medium",style:{color:"var(--sidebar-text-primary)"},children:"AI Assistant"}),(0,s.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>o(),className:"h-8 w-8 p-0 border-0",style:{color:"var(--sidebar-text-tertiary)"},children:(0,s.jsx)(L.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"flex-1 p-4 overflow-auto",children:(0,s.jsx)("div",{className:"text-sm text-center",style:{color:"var(--sidebar-text-tertiary)"},children:"Start a conversation to modify your analysis, ask questions, or generate new insights."})}),(0,s.jsx)("div",{className:"p-4 border-t",style:{borderColor:"var(--sidebar-border)"},children:(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(f.p,{placeholder:"Ask about your data or request changes...",value:H,onChange:e=>W(e.target.value),className:"flex-1 text-sm border",style:{backgroundColor:"var(--sidebar-bg)",borderColor:"var(--sidebar-border)",color:"var(--sidebar-text-primary)"},onKeyDown:e=>{"Enter"===e.key&&H.trim()&&ec()}}),(0,s.jsx)(d.$,{onClick:ec,disabled:!H.trim(),size:"sm",className:"px-3 border-0",style:{backgroundColor:H.trim()?"var(--surface-selected)":"transparent",color:H.trim()?"var(--sidebar-text-primary)":"var(--sidebar-text-tertiary)"},children:(0,s.jsx)(V.A,{className:"h-4 w-4"})})]})})]})]})},Z=()=>{let{listAnalysisProjects:e,createAnalysisProject:r,updateAnalysisProject:a,deleteAnalysisProject:l}=(0,F.g)(),[d,n]=(0,t.useState)({currentView:"list",selectedProject:null,breadcrumbs:[{label:"Analysis Projects"}]}),[u,x]=(0,t.useState)([]),[m,b]=(0,t.useState)(!1),[h,p]=(0,t.useState)(!1),[y,v]=(0,t.useState)(!1),{setPageActions:f}=(0,i.s)();(0,t.useEffect)(()=>{j()},[]);let j=(0,t.useCallback)(async()=>{b(!0);try{let r=await e();if(r.success&&r.data){let e=r.data.projects.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt)}));x(e)}}catch(e){console.error("Failed to load analysis projects:",e),x([])}finally{b(!1)}},[e]),N=(0,t.useCallback)(e=>{n({currentView:"project",selectedProject:e,breadcrumbs:[{label:"Analysis Projects",onClick:()=>w()},{label:e.name}]})},[]),w=(0,t.useCallback)(()=>{n({currentView:"list",selectedProject:null,breadcrumbs:[{label:"Analysis Projects"}]})},[]),k=(0,t.useMemo)(()=>({title:"project"===d.currentView&&d.selectedProject?d.selectedProject.name:"Analysis Projects",icon:c.A,breadcrumbs:"project"===d.currentView&&d.selectedProject?[{label:"Analysis Projects",onClick:()=>w()},{label:d.selectedProject.name}]:[{label:"Analysis Projects"}]}),[d.currentView,d.selectedProject,w]);(0,o.H)(k);let C=(0,t.useCallback)(async()=>{p(!0);try{let e="Analysis Project ".concat(u.length+1),a=await r({name:e,description:""});if(a.success&&a.data){let e={...a.data,createdAt:new Date(a.data.createdAt),updatedAt:new Date(a.data.updatedAt)};x(r=>[...r,e]),N(e)}}catch(e){console.error("Failed to create analysis project:",e)}finally{p(!1)}},[u.length,r,N]),A=(0,t.useCallback)(async(e,r)=>{try{let t=await a(e,r);if(t.success&&t.data){var s;let r={...t.data,createdAt:new Date(t.data.createdAt),updatedAt:new Date(t.data.updatedAt)};x(a=>a.map(a=>a.id===e?r:a)),(null==(s=d.selectedProject)?void 0:s.id)===e&&n(e=>({...e,selectedProject:r,breadcrumbs:[{label:"Analysis Projects",onClick:()=>w()},{label:r.name}]}))}}catch(e){console.error("Failed to update analysis project:",e)}},[a,d.selectedProject,w]),M=(0,t.useCallback)(async e=>{if(confirm("Are you sure you want to delete this analysis project? This action cannot be undone."))try{if((await l(e)).success){var r;x(r=>r.filter(r=>r.id!==e)),(null==(r=d.selectedProject)?void 0:r.id)===e&&w()}}catch(e){console.error("Failed to delete analysis project:",e)}},[l,d.selectedProject,w]);return(0,t.useEffect)(()=>{let e="list"===d.currentView,r="project"===d.currentView&&d.selectedProject;f({onCreateAnalysis:e?C:void 0,isCreatingAnalysis:!!e&&h,onExport:r?()=>{console.log("Exporting project data...")}:void 0,onToggleChat:r?()=>v(!y):void 0,isChatOpen:!!r&&y})},[d.currentView,d.selectedProject,f,C,h,y]),(0,s.jsx)("div",{className:"min-h-screen",style:{backgroundColor:"var(--sidebar-bg)"},children:(0,s.jsx)("div",{className:"container mx-auto p-6 space-y-6",children:"list"===d.currentView?(0,s.jsx)(g,{projects:u,onSelectProject:N,onDeleteProject:M,isLoading:m}):d.selectedProject?(0,s.jsx)(B,{project:d.selectedProject,onUpdateProject:A,isChatOpen:y,onToggleChat:()=>v(!y)}):null})})};var I=a(30095);function O(){return(0,s.jsx)(I.Ay,{children:(0,s.jsx)(l.A,{children:(0,s.jsx)(Z,{})})})}},85339:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},85690:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[464,817,874,786,826,189,45,179,30,441,684,358],()=>r(42837)),_N_E=e.O()}]);