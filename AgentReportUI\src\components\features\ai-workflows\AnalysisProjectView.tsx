"use client";

import React, { useState, useMemo, useEffect, useCallback, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { 
  MessageSquare,
  Code2,
  BarChart3,
  Table,
  Send,
  X,
  ChevronRight,
  Download,
  Copy,
  Maximize2,
  Database,
  Eye,
  Layers,
  Edit3,
  Filter,
  ChevronDown,
  Play,
  Upload,
  File,
  Trash2
} from 'lucide-react';
import { AnalysisProject, PipelineStep, DataOutput, FileUploadProgress, UploadedFile } from '@/types';
import { usePageHeader } from '@/providers/PageHeaderContext';
import { useApi } from '@/providers/ApiContext';
import Chat from '../chat/Chat';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface AnalysisProjectViewProps {
  project: AnalysisProject;
  onUpdateProject: (projectId: string, updates: Partial<AnalysisProject>) => void;
  isChatOpen: boolean;
  onToggleChat: () => void;
}

// Using imported types from @/types: AnalysisProject, PipelineStep, DataOutput

const AnalysisProjectView: React.FC<AnalysisProjectViewProps> = ({
  project,
  onUpdateProject,
  isChatOpen,
  onToggleChat
}) => {
  // API context
  const { getProjectData, getProjectSteps, uploadProjectFile, deleteProjectFile } = useApi();

  // State
  const [activeTab, setActiveTab] = useState<'overview' | 'data' | 'charts' | 'code'>('data');
  const [pipelineSteps, setPipelineSteps] = useState<PipelineStep[]>([]);
  const [projectData, setProjectData] = useState<DataOutput | null>(null);
  const [isLoadingSteps, setIsLoadingSteps] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());
  const [chatMessage, setChatMessage] = useState('');

  // File upload state
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { setPageActions } = usePageHeader();

  // Set page actions for the header
  useEffect(() => {
    setPageActions({
      // Export action
      onExport: () => {
        console.log('Exporting results...');
        // TODO: Implement export functionality
      },
      // Chat toggle action  
      onToggleChat: () => onToggleChat(),
      isChatOpen: isChatOpen,
    });
  }, [isChatOpen, setPageActions]);

  // Load pipeline steps and data on component mount or project change
  useEffect(() => {
    if (project.id) {
      loadPipelineSteps();
      if (activeTab === 'data') {
        loadProjectData();
      }
    }
  }, [project.id]);

  // Load data when switching to data tab
  useEffect(() => {
    if (activeTab === 'data' && project.id && !projectData) {
      loadProjectData();
    }
  }, [activeTab, project.id]);

  const loadPipelineSteps = useCallback(async () => {
    setIsLoadingSteps(true);
    try {
      const response = await getProjectSteps(project.id);
      if (response.success && response.data) {
        setPipelineSteps(response.data);
      }
    } catch (error) {
      console.error('Failed to load pipeline steps:', error);
      setPipelineSteps([]);
    } finally {
      setIsLoadingSteps(false);
    }
  }, [project.id, getProjectSteps]);

  const loadProjectData = useCallback(async () => {
    setIsLoadingData(true);
    try {
      const response = await getProjectData(project.id);
      if (response.success && response.data) {
        setProjectData(response.data);
      }
    } catch (error) {
      console.error('Failed to load project data:', error);
      setProjectData(null);
    } finally {
      setIsLoadingData(false);
    }
  }, [project.id, getProjectData]);

  const toggleStepExpansion = (stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stepId)) {
        newSet.delete(stepId);
      } else {
        newSet.add(stepId);
      }
      return newSet;
    });
  };

  const getStatusIcon = (status: AnalysisProject['status']) => {
    switch (status) {
      case 'completed':
        return <div className="w-2 h-2 bg-green-500 rounded-full" />;
      case 'running':
        return <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />;
      case 'error':
        return <div className="w-2 h-2 bg-red-500 rounded-full" />;
      default:
        return <div className="w-2 h-2 bg-gray-400 rounded-full" />;
    }
  };

  const getStepStatusIcon = (status: PipelineStep['status']) => {
    switch (status) {
      case 'completed':
        return <div className="w-2 h-2 bg-green-500 rounded-full" />;
      case 'running':
        return <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />;
      case 'error':
        return <div className="w-2 h-2 bg-red-500 rounded-full" />;
      default:
        return <div className="w-2 h-2 bg-gray-400 rounded-full" />;
    }
  };

  const completedSteps = pipelineSteps.filter(step => step.status === 'completed').length;

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatCellValue = (value: any, columnName: string): string => {
    if (value === null || value === undefined) return 'N/A';
    
    if (columnName.toLowerCase().includes('salary') && typeof value === 'number') {
      return formatCurrency(value);
    }
    
    if (typeof value === 'number' && value % 1 !== 0) {
      return value.toFixed(2);
    }
    
    return String(value);
  };

  // Handle chat functionality
  const handleSendMessage = () => {
    if (!chatMessage.trim()) return;
    console.log('Sending message:', chatMessage);
    setChatMessage('');
    // TODO: Implement chat functionality
  };

  // Copy code to clipboard
  const copyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    // TODO: Show toast notification
  };

  // File upload handlers
  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    handleFileUpload(file);
  };

  const handleFileUpload = async (file: File) => {
    if (!file) return;

    // Validate file type
    const allowedTypes = ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
    const allowedExtensions = ['.csv', '.xls', '.xlsx'];
    const hasValidExtension = allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
    
    if (!allowedTypes.includes(file.type) && !hasValidExtension) {
      console.error('Invalid file type. Please upload CSV or Excel files only.');
      return;
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      console.error('File size too large. Please upload files smaller than 10MB.');
      return;
    }

    setIsUploading(true);
    setUploadProgress(null);

    try {
      const response = await uploadProjectFile({
        file,
        projectId: project.id,
        onProgress: (progress: FileUploadProgress) => {
          setUploadProgress(progress);
        }
      });

      if (response.success && response.data) {
        // Add uploaded file to list
        const newFile: UploadedFile = {
          id: response.data.fileId,
          filename: response.data.filename,
          size: file.size,
          type: file.type,
          uploadedAt: new Date().toISOString(),
          status: 'completed'
        };

        setUploadedFiles(prev => [...prev, newFile]);

        // Update project data with the uploaded file's preview data
        setProjectData(response.data.previewData);

        console.log('File uploaded successfully:', response.data);
      }
    } catch (error) {
      console.error('File upload failed:', error);
    } finally {
      setIsUploading(false);
      setUploadProgress(null);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    try {
      const response = await deleteProjectFile(project.id, fileId);
      
      if (response.success) {
        setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
        
        // If this was the only file, clear project data
        if (uploadedFiles.length === 1) {
          setProjectData(null);
        }
      }
    } catch (error) {
      console.error('File deletion failed:', error);
    }
  };

  // Drag and drop handlers
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div
      className="min-h-screen flex"
      style={{ backgroundColor: 'var(--sidebar-bg)' }}
      role="main"
      aria-label="Analysis Project View"
    >
      {/* Main Content Area */}
      <div className={`flex-1 transition-all duration-300 ${isChatOpen ? 'mr-96' : 'mr-0'}`}>
        <div className="h-full flex flex-col p-6">
          
          {/* Simplified Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              {/* Status indicator */}
              <div className="flex items-center gap-2">
                {getStatusIcon(project.status)}
                <span className="text-sm" style={{ color: 'var(--sidebar-text-tertiary)' }}>
                  {completedSteps} of {pipelineSteps.length} steps complete
                </span>
              </div>
            </div>
            
            {/* Filter dropdown */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-3 text-xs border-0"
                style={{ color: 'var(--sidebar-text-secondary)' }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                <Filter className="h-3 w-3 mr-1.5" />
                {activeTab === 'overview' ? 'All Steps' : activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
              </Button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex items-center gap-1 mb-6">
            {[
              { id: 'overview', label: 'Overview', icon: Layers },
              { id: 'data', label: 'Data', icon: Table },
              { id: 'charts', label: 'Charts', icon: BarChart3 },
              { id: 'code', label: 'Code', icon: Code2 }
            ].map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              
              return (
                <Button
                  key={tab.id}
                  variant="ghost"
                  size="sm"
                  onClick={() => setActiveTab(tab.id as any)}
                  className="h-8 px-3 text-xs border-0"
                  style={{
                    color: isActive ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',
                    backgroundColor: isActive ? 'var(--surface-selected)' : 'transparent'
                  }}
                  onMouseEnter={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  <Icon className="h-3 w-3 mr-1.5" />
                  {tab.label}
                </Button>
              );
            })}
          </div>

          {/* Content Area */}
          <div className="flex-1 overflow-auto">
            
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Pipeline Steps */}
                <div className="grid gap-4">
                  {pipelineSteps.map((step, index) => (
                    <Card
                      key={step.id}
                      className="border cursor-pointer transition-all duration-200"
                      style={{
                        backgroundColor: 'var(--sidebar-surface-secondary)',
                        borderColor: expandedSteps.has(step.id) ? '#3b82f6' : 'var(--sidebar-border)'
                      }}
                      onClick={() => toggleStepExpansion(step.id)}
                      onMouseEnter={(e) => {
                        if (!expandedSteps.has(step.id)) {
                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!expandedSteps.has(step.id)) {
                          e.currentTarget.style.backgroundColor = 'var(--sidebar-surface-secondary)';
                        }
                      }}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div 
                              className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                              style={{ 
                                backgroundColor: 'var(--surface-selected)',
                                color: 'var(--sidebar-text-primary)'
                              }}
                            >
                              {index + 1}
                            </div>
                            <div>
                              <CardTitle 
                                className="text-base"
                                style={{ color: 'var(--sidebar-text-primary)' }}
                              >
                                {step.title}
                              </CardTitle>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            {getStepStatusIcon(step.status)}
                            <ChevronRight 
                              className={`h-4 w-4 transition-transform duration-200 ${
                                expandedSteps.has(step.id) ? 'rotate-90' : ''
                              }`}
                              style={{ color: 'var(--sidebar-text-tertiary)' }}
                            />
                          </div>
                        </div>
                      </CardHeader>
                      
                      {/* Expanded Step Content */}
                      {expandedSteps.has(step.id) && step.outputs && (
                        <CardContent className="pt-0 space-y-4">
                          {step.outputs.summary && (
                            <p 
                              className="text-sm"
                              style={{ color: 'var(--sidebar-text-secondary)' }}
                            >
                              {step.outputs.summary}
                            </p>
                          )}
                          
                          {/* Step Data */}
                          {step.outputs.data && (
                            <div>
                              <h4 
                                className="text-sm font-medium mb-2 flex items-center gap-2"
                                style={{ color: 'var(--sidebar-text-primary)' }}
                              >
                                <Table className="h-4 w-4" />
                                Data Output
                              </h4>
                              <div 
                                className="border rounded-lg p-3 text-center"
                                style={{ 
                                  backgroundColor: 'var(--sidebar-bg)',
                                  borderColor: 'var(--sidebar-border)',
                                  color: 'var(--sidebar-text-secondary)'
                                }}
                              >
                                <p className="text-sm">Data output available</p>
                                <p className="text-xs mt-1" style={{ color: 'var(--sidebar-text-tertiary)' }}>
                                  View in Data tab for detailed table
                                </p>
                              </div>
                            </div>
                          )}
                          
                          {/* Step Visualization */}
                          {step.outputs.visualization && (
                            <div>
                              <h4 
                                className="text-sm font-medium mb-2 flex items-center gap-2"
                                style={{ color: 'var(--sidebar-text-primary)' }}
                              >
                                <BarChart3 className="h-4 w-4" />
                                Visualization
                              </h4>
                              <div 
                                className="border rounded-lg p-4 text-center"
                                style={{ 
                                  backgroundColor: 'var(--sidebar-bg)',
                                  borderColor: 'var(--sidebar-border)',
                                  color: 'var(--sidebar-text-secondary)'
                                }}
                              >
                                <p className="text-sm">{step.outputs.visualization.config?.title || 'Visualization'}</p>
                                <p className="text-xs mt-1" style={{ color: 'var(--sidebar-text-tertiary)' }}>
                                  View in Charts tab for interactive visualization
                                </p>
                              </div>
                            </div>
                          )}
                          
                          {/* Step Code */}
                          {step.outputs.code && (
                            <div>
                              <div className="flex items-center justify-between mb-2">
                                <h4 
                                  className="text-sm font-medium flex items-center gap-2"
                                  style={{ color: 'var(--sidebar-text-primary)' }}
                                >
                                  <Code2 className="h-4 w-4" />
                                  Generated Code
                                </h4>
                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      copyCode(step.outputs!.code!);
                                    }}
                                    className="h-7 px-2 border-0"
                                    style={{ color: 'var(--sidebar-text-tertiary)' }}
                                  >
                                    <Copy className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleStepExpansion(step.id);
                                    }}
                                    className="h-7 px-2 border-0"
                                    style={{ color: 'var(--sidebar-text-tertiary)' }}
                                  >
                                    <Maximize2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                              <div 
                                className={`border rounded-lg p-3 font-mono text-xs overflow-x-auto transition-all duration-200 ${
                                  expandedSteps.has(step.id) ? 'max-h-96' : 'max-h-32'
                                }`}
                                style={{ 
                                  backgroundColor: 'var(--sidebar-bg)',
                                  borderColor: 'var(--sidebar-border)',
                                  color: 'var(--sidebar-text-secondary)'
                                }}
                              >
                                <pre>{step.outputs.code}</pre>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      )}
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Data Tab */}
            {activeTab === 'data' && (
              <div className="space-y-4">
                {/* Compact File Upload Section */}
                <div className="space-y-3">
                  {/* Upload Area - Compact */}
                  <div
                    className={`border border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer ${
                      dragActive 
                        ? 'border-blue-400' 
                        : ''
                    }`}
                    style={{
                      backgroundColor: dragActive ? 'rgba(59, 130, 246, 0.05)' : 'var(--sidebar-surface-secondary)',
                      borderColor: dragActive ? '#3b82f6' : 'var(--sidebar-border)',
                    }}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload 
                      className="h-5 w-5 mx-auto mb-2" 
                      style={{ color: 'var(--sidebar-text-tertiary)' }}
                    />
                    <p className="text-xs" style={{ color: 'var(--sidebar-text-primary)' }}>
                      Drop files here or <span className="text-blue-500 hover:text-blue-600">browse</span>
                    </p>
                    <p className="text-xs mt-1" style={{ color: 'var(--sidebar-text-tertiary)' }}>
                      CSV, Excel up to 10MB
                    </p>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".csv,.xls,.xlsx"
                      onChange={(e) => handleFileSelect(e.target.files)}
                      className="hidden"
                    />
                  </div>

                  {/* Upload Progress - Compact */}
                  {uploadProgress && (
                    <div 
                      className="px-3 py-2 rounded-lg border"
                      style={{
                        backgroundColor: 'var(--sidebar-surface-secondary)',
                        borderColor: 'var(--sidebar-border)'
                      }}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs" style={{ color: 'var(--sidebar-text-secondary)' }}>
                          Uploading...
                        </span>
                        <span className="text-xs" style={{ color: 'var(--sidebar-text-tertiary)' }}>
                          {uploadProgress.percentage}%
                        </span>
                      </div>
                      <Progress value={uploadProgress.percentage} className="h-1" />
                    </div>
                  )}

                  {/* Uploaded Files List - Compact (Cursor-style) */}
                  {uploadedFiles.length > 0 && (
                    <div className="space-y-1">
                      {uploadedFiles.map((file) => (
                        <div
                          key={file.id}
                          className="flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors duration-200"
                          style={{
                            backgroundColor: 'var(--sidebar-surface-secondary)',
                            borderColor: 'var(--sidebar-border)'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'var(--sidebar-surface-secondary)';
                          }}
                        >
                          <File className="h-3 w-3 flex-shrink-0" style={{ color: 'var(--sidebar-icon)' }} />
                          <div className="flex-1 min-w-0">
                            <p className="text-xs font-medium truncate" style={{ color: 'var(--sidebar-text-primary)' }}>
                              {file.filename}
                            </p>
                            <p className="text-xs" style={{ color: 'var(--sidebar-text-tertiary)' }}>
                              {formatFileSize(file.size)}
                            </p>
                          </div>
                          <div className="flex items-center gap-1 flex-shrink-0">
                            {file.status === 'completed' && (
                              <div 
                                className="w-2 h-2 rounded-full"
                                style={{ backgroundColor: '#10b981' }}
                              />
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteFile(file.id)}
                              className="h-6 w-6 p-0 border-0 opacity-60 hover:opacity-100"
                              style={{ color: 'var(--sidebar-text-tertiary)' }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.color = '#ef4444';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.color = 'var(--sidebar-text-tertiary)';
                              }}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Data Preview Section */}
                {isLoadingData ? (
                  <Card 
                    className="animate-pulse border"
                    style={{
                      backgroundColor: 'var(--sidebar-surface-secondary)',
                      borderColor: 'var(--sidebar-border)'
                    }}
                  >
                    <CardHeader>
                      <div 
                        className="h-4 rounded w-1/4"
                        style={{ backgroundColor: 'var(--interactive-bg-secondary-hover)' }}
                      ></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {[1, 2, 3, 4, 5].map(i => (
                          <div 
                            key={i} 
                            className="h-4 rounded"
                            style={{ backgroundColor: 'var(--sidebar-bg)' }}
                          ></div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ) : projectData && projectData.totalRows > 0 ? (
                  <Card 
                    className="border"
                    style={{
                      backgroundColor: 'var(--sidebar-surface-secondary)',
                      borderColor: 'var(--sidebar-border)'
                    }}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle 
                          className="text-sm font-medium"
                          style={{ color: 'var(--sidebar-text-primary)' }}
                        >
                          Data Preview
                        </CardTitle>
                        <div className="flex gap-2">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-6 px-2 text-xs border-0"
                            style={{ color: 'var(--sidebar-text-secondary)' }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent';
                            }}
                          >
                            <Download className="h-3 w-3 mr-1" />
                            Export
                          </Button>
                        </div>
                      </div>
                      <p className="text-xs" style={{ color: 'var(--sidebar-text-tertiary)' }}>
                        {projectData.totalRows} rows • {projectData.columns.length} columns
                      </p>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr style={{ borderBottom: `1px solid var(--sidebar-border)` }}>
                              {projectData.columns.map((column) => (
                                <th 
                                  key={column.name} 
                                  className="text-left p-2 text-xs font-medium"
                                  style={{ 
                                    color: 'var(--sidebar-text-primary)',
                                    backgroundColor: 'var(--sidebar-bg)'
                                  }}
                                >
                                  {column.name}
                                  <span className="ml-1" style={{ color: 'var(--sidebar-text-tertiary)' }}>
                                    ({column.type})
                                  </span>
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {projectData.rows.map((row, index) => (
                              <tr 
                                key={index} 
                                className="transition-colors duration-150"
                                style={{ borderBottom: `1px solid var(--sidebar-border)` }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.backgroundColor = 'transparent';
                                }}
                              >
                                {projectData.columns.map((column) => (
                                  <td 
                                    key={column.name} 
                                    className="p-2 text-xs"
                                    style={{ color: 'var(--sidebar-text-secondary)' }}
                                  >
                                    {formatCellValue(row[column.name], column.name)}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                ) : uploadedFiles.length === 0 ? (
                  <Card 
                    className="border"
                    style={{
                      backgroundColor: 'var(--sidebar-surface-secondary)',
                      borderColor: 'var(--sidebar-border)'
                    }}
                  >
                    <CardContent className="p-8 text-center">
                      <Upload 
                        className="h-12 w-12 mx-auto mb-4" 
                        style={{ color: 'var(--sidebar-text-tertiary)' }}
                      />
                      <h3 
                        className="text-lg font-medium mb-2"
                        style={{ color: 'var(--sidebar-text-primary)' }}
                      >
                        No data uploaded yet
                      </h3>
                      <p 
                        className="mb-4"
                        style={{ color: 'var(--sidebar-text-secondary)' }}
                      >
                        Upload a CSV or Excel file to start analyzing your data
                      </p>
                      <Button 
                        onClick={() => fileInputRef.current?.click()}
                        className="border-0"
                        style={{
                          backgroundColor: 'var(--surface-selected)',
                          color: 'var(--sidebar-text-primary)'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'var(--surface-selected)';
                        }}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Your First File
                      </Button>
                    </CardContent>
                  </Card>
                ) : (
                  <Card 
                    className="border"
                    style={{
                      backgroundColor: 'var(--sidebar-surface-secondary)',
                      borderColor: 'var(--sidebar-border)'
                    }}
                  >
                    <CardContent className="p-8 text-center">
                      <p style={{ color: 'var(--sidebar-text-secondary)' }}>
                        Processing uploaded files...
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* Charts Tab */}
            {activeTab === 'charts' && (
              <Card>
                <CardContent className="p-8 text-center">
                  <p className="text-gray-500">Charts and visualizations will be displayed here.</p>
                  <p className="text-xs text-gray-400 mt-1">Feature coming soon</p>
                </CardContent>
              </Card>
            )}

            {/* Code Tab */}
            {activeTab === 'code' && (
              <div className="space-y-6">
                {pipelineSteps
                  .filter(step => step.outputs?.code)
                  .map((step) => (
                    <div key={step.id}>
                      <div className="flex items-center justify-between mb-4">
                        <h3 
                          className="text-lg font-medium flex items-center gap-2"
                          style={{ color: 'var(--sidebar-text-primary)' }}
                        >
                          <Code2 className="h-5 w-5" />
                          {step.title} - Generated Code
                        </h3>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyCode(step.outputs!.code!)}
                          className="border"
                          style={{
                            borderColor: 'var(--sidebar-border)',
                            color: 'var(--sidebar-text-secondary)'
                          }}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Code
                        </Button>
                      </div>
                      <div 
                        className="border rounded-lg p-4 font-mono text-sm overflow-x-auto"
                        style={{ 
                          backgroundColor: 'var(--sidebar-bg)',
                          borderColor: 'var(--sidebar-border)',
                          color: 'var(--sidebar-text-secondary)'
                        }}
                      >
                        <pre>{step.outputs!.code}</pre>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Chat Panel */}
      {isChatOpen && (
        <div 
          className="fixed right-0 top-0 w-96 h-full border-l flex flex-col"
          style={{
            backgroundColor: 'var(--sidebar-surface-secondary)',
            borderColor: 'var(--sidebar-border)'
          }}
        >
          {/* Chat Header */}
          <div 
            className="p-4 border-b flex items-center justify-between"
            style={{ borderColor: 'var(--sidebar-border)' }}
          >
            <h3 
              className="font-medium"
              style={{ color: 'var(--sidebar-text-primary)' }}
            >
              AI Assistant
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleChat()}
              className="h-8 w-8 p-0 border-0"
              style={{ color: 'var(--sidebar-text-tertiary)' }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 p-4 overflow-auto">
            <div 
              className="text-sm text-center"
              style={{ color: 'var(--sidebar-text-tertiary)' }}
            >
              Start a conversation to modify your analysis, ask questions, or generate new insights.
            </div>
          </div>

          {/* Chat Input */}
          <div 
            className="p-4 border-t"
            style={{ borderColor: 'var(--sidebar-border)' }}
          >
            <div className="flex gap-2">
              <Input
                placeholder="Ask about your data or request changes..."
                value={chatMessage}
                onChange={(e) => setChatMessage(e.target.value)}
                className="flex-1 text-sm border"
                style={{
                  backgroundColor: 'var(--sidebar-bg)',
                  borderColor: 'var(--sidebar-border)',
                  color: 'var(--sidebar-text-primary)'
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && chatMessage.trim()) {
                    handleSendMessage();
                  }
                }}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!chatMessage.trim()}
                size="sm"
                className="px-3 border-0"
                style={{
                  backgroundColor: chatMessage.trim() ? 'var(--surface-selected)' : 'transparent',
                  color: chatMessage.trim() ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-tertiary)'
                }}
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalysisProjectView; 