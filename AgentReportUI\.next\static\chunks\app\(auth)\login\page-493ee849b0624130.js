(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[72],{30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var a=t(95155),s=t(12115),i=t(99708),o=t(74466),d=t(46486);let n=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((e,r)=>{let{className:t,variant:s,size:o,asChild:l=!1,...c}=e,u=l?i.DX:"button";return(0,a.jsx)(u,{className:(0,d.cn)(n({variant:s,size:o,className:t})),ref:r,...c})});l.displayName="Button"},46486:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var a=t(52596),s=t(39688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},47262:(e,r,t)=>{"use strict";t.d(r,{S:()=>d});var a=t(95155);t(12115);var s=t(76981),i=t(5196),o=t(46486);function d(e){let{className:r,...t}=e;return(0,a.jsx)(s.bL,{"data-slot":"checkbox",className:(0,o.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",r),...t,children:(0,a.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,a.jsx)(i.A,{className:"size-3.5"})})})}},54129:(e,r,t)=>{Promise.resolve().then(t.bind(t,66013))},62523:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var a=t(95155);t(12115);var s=t(46486);function i(e){let{className:r,type:t,...i}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...i})}},66013:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var a=t(95155),s=t(12115),i=t(35695),o=t(6874),d=t.n(o),n=t(62523),l=t(30285),c=t(47262),u=t(85057),h=t(74045),g=t(45786),b=t(28883),x=t(32919),m=t(78749),f=t(92657);let p=()=>{let[e,r]=(0,s.useState)(""),[t,o]=(0,s.useState)(""),[p,y]=(0,s.useState)(!1),[v,k]=(0,s.useState)(!1),[w,j]=(0,s.useState)(null),[N,A]=(0,s.useState)(null),[C,S]=(0,s.useState)(!1),{login:z}=(0,h.A)(),P=(0,i.useSearchParams)();(0,s.useEffect)(()=>{let e=P.get("oauth_error"),r=P.get("error");if("success"===P.get("registration"))A("Account created successfully! Please sign in with your credentials."),j(null);else if(e||r){let t="Authentication failed. Please try again.";"session_expired"===r?t="Your session has expired. Please sign in again.":"oauth_failed"===r?t="OAuth authentication failed. Please try again.":"missing_tokens"===r?t="Authentication tokens are missing. Please try again.":e&&(t="OAuth Error: ".concat(e)),j(t),A(null)}},[P]);let _=async r=>{r.preventDefault(),j(null),A(null),S(!0);try{await z({username:e,password:t})}catch(e){console.error("Login failed",e),e.response&&e.response.data&&e.response.data.detail?j(e.response.data.detail):e.message?j(e.message):j("Login failed. Please check your credentials and try again.")}S(!1)},L=async()=>{j(null),A(null),S(!0);try{let e=await fetch("".concat((0,g.hY)(),"/auth/google"),{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"},body:JSON.stringify({})});if(!e.ok){let r=await e.text();throw Error("Failed to get Google OAuth URL: ".concat(e.status," - ").concat(r))}let r=await e.json();if(r.auth_url)window.location.href=r.auth_url;else throw Error("No OAuth URL received from backend")}catch(e){console.error("Google OAuth failed",e),j("Failed to initiate Google login. Please try again."),S(!1)}};return(0,a.jsxs)("form",{onSubmit:_,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-5",children:[(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10",children:(0,a.jsx)(b.A,{className:"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200"})}),(0,a.jsx)(n.p,{id:"username",type:"text",value:e,onChange:e=>r(e.target.value),placeholder:"Email or username",className:"w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500",required:!0,disabled:C})]}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10",children:(0,a.jsx)(x.A,{className:"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200"})}),(0,a.jsx)(n.p,{id:"password",type:p?"text":"password",value:t,onChange:e=>o(e.target.value),placeholder:"Password",className:"w-full pl-12 pr-14 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500",required:!0,disabled:C}),(0,a.jsx)("button",{type:"button",onClick:()=>y(!p),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100 transition-colors duration-200 z-10 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700",disabled:C,children:p?(0,a.jsx)(m.A,{className:"h-5 w-5"}):(0,a.jsx)(f.A,{className:"h-5 w-5"})})]})]}),N&&(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-900/30 border-2 border-green-200 dark:border-green-500/50 rounded-2xl p-4 animate-in slide-in-from-top-2 duration-300 shadow-sm",children:(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300 text-center font-medium",children:N})}),w&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/30 border-2 border-red-200 dark:border-red-500/50 rounded-2xl p-4 animate-in slide-in-from-top-2 duration-300 shadow-sm",children:(0,a.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300 text-center font-medium",children:w})}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(c.S,{id:"rememberMe",checked:v,onCheckedChange:e=>k(e),className:"rounded-lg border-2 border-gray-300 dark:border-gray-500 text-blue-500 dark:text-blue-400 data-[state=checked]:bg-blue-500 dark:data-[state=checked]:bg-blue-400 data-[state=checked]:border-blue-500 dark:data-[state=checked]:border-blue-400",disabled:C}),(0,a.jsx)(u.J,{htmlFor:"rememberMe",className:"text-sm text-gray-700 dark:text-gray-300 font-medium cursor-pointer",children:"Remember me"})]}),(0,a.jsx)("a",{href:"#",className:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 font-medium hover:underline",children:"Forgot password?"})]}),(0,a.jsx)(l.$,{type:"submit",className:"w-full bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-500 dark:to-blue-600 hover:from-blue-700 hover:to-blue-800 dark:hover:from-blue-600 dark:hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg shadow-blue-500/30 dark:shadow-blue-400/20 hover:shadow-blue-500/50 dark:hover:shadow-blue-400/30 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:shadow-blue-500/25 text-base border-0",disabled:C,children:C?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-3"}),"Signing in..."]}):"Sign in"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("span",{className:"w-full border-t-2 border-gray-200 dark:border-gray-600"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-4 bg-white dark:bg-gray-900 text-gray-600 dark:text-gray-400 font-medium",children:"Or continue with"})})]}),(0,a.jsxs)(l.$,{type:"button",onClick:L,className:"w-full bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 font-semibold py-4 px-4 rounded-2xl border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 transition-all duration-300 focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:ring-offset-2 dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-sm dark:shadow-gray-900/20 hover:shadow-md dark:hover:shadow-gray-900/30",disabled:C,children:[(0,a.jsxs)("svg",{className:"w-5 h-5 mr-3",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,a.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,a.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,a.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]}),(0,a.jsx)("div",{className:"text-center pt-4",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Don't have an account?"," ",(0,a.jsx)(d(),{href:"/register",className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 font-semibold hover:underline",children:"Create one"})]})})]})},y=()=>(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("div",{className:"absolute top-0 left-0 p-6 z-20",children:(0,a.jsxs)(d(),{href:"/",className:"flex items-center space-x-2 group",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-xl bg-blue-500 group-hover:bg-blue-600 transition-colors duration-200 shadow-sm",children:(0,a.jsx)("svg",{viewBox:"0 0 24 24",fill:"none",className:"w-5 h-5 text-white",children:(0,a.jsx)("path",{d:"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,a.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-500 transition-colors duration-200",children:"Agent"})]})}),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center p-6",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 rounded-3xl border border-gray-200/50 dark:border-gray-700/50 shadow-xl",children:[(0,a.jsxs)("div",{className:"p-8 pb-0 text-center",children:[(0,a.jsx)("h1",{className:"text-3xl font-light text-gray-900 dark:text-white mb-2 tracking-tight",children:"Welcome back"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400 font-light",children:"Sign in to continue to your workspace"})]}),(0,a.jsx)("div",{className:"p-8",children:(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)("div",{className:"flex justify-center items-center h-32",children:"Loading..."}),children:(0,a.jsx)(p,{})})})]}),(0,a.jsx)("div",{className:"text-center mt-8",children:(0,a.jsxs)("p",{className:"text-sm text-gray-400 dark:text-gray-500",children:["Need assistance?"," ",(0,a.jsx)("a",{href:"#",className:"text-blue-500 hover:text-blue-600 transition-colors duration-200 font-medium",children:"Contact support"})]})})]})})]})},85057:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var a=t(95155);t(12115);var s=t(40968),i=t(46486);function o(e){let{className:r,...t}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}}},e=>{var r=r=>e(e.s=r);e.O(0,[464,817,874,655,189,45,441,684,358],()=>r(54129)),_N_E=e.O()}]);