import type { Metada<PERSON> } from "next";
import { Inter, Noto_Sans } from "next/font/google";
import "./globals.css";
import ClientLayout from "@/components/common/ClientLayout";
import { ChatHistoryProvider } from "@/providers/ChatHistoryContext";
import { ThemeProvider } from "@/providers/theme-provider";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

const notoSans = Noto_Sans({
  variable: "--font-noto-sans",
  subsets: ["latin"],
  weight: ["400", "500", "700", "900"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "Agent Platform",
  description: "Agent Platform for database queries",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="color-scheme" content="light dark" />
        {/* Tailwind CDN and Google Font links removed, handled by config and next/font */}
      </head>
      <body className={`${inter.variable} ${notoSans.variable} bg-white dark:bg-gray-800`}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <ClientLayout>
            <ChatHistoryProvider>
              {children}
            </ChatHistoryProvider>
          </ClientLayout>
        </ThemeProvider>
      </body>
    </html>
  );
}
