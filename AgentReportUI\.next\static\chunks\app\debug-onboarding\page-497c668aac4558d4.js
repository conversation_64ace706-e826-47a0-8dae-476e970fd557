(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[316],{27874:(e,t,o)=>{"use strict";o.d(t,{Hg:()=>s,Wr:()=>n,YL:()=>p,ZS:()=>r,qy:()=>l,uE:()=>c});let i="onboarding_progress";function n(e){try{localStorage.setItem(i,JSON.stringify(e))}catch(e){console.warn("Failed to save onboarding progress to localStorage:",e)}}function s(){try{let e=localStorage.getItem(i);if(!e)return null;let t=JSON.parse(e);if(!t.currentStep||!Array.isArray(t.completedSteps))return null;return t}catch(e){return console.warn("Failed to load onboarding progress from localStorage:",e),null}}function l(){try{localStorage.removeItem(i)}catch(e){console.warn("Failed to clear onboarding progress from localStorage:",e)}}function p(e,t){var o;let i=e.slice(0,t).filter(e=>e.isCompleted).map(e=>e.id);return{currentStep:(null==(o=e[t])?void 0:o.id)||"welcome",completedSteps:i,lastUpdated:new Date().toISOString(),canResume:i.length>0,totalSteps:e.length}}function r(e,t){var o;if(!t||!((o=t)&&"object"==typeof o&&"string"==typeof o.currentStep&&Array.isArray(o.completedSteps)&&"string"==typeof o.lastUpdated&&"boolean"==typeof o.canResume&&"number"==typeof o.totalSteps))return{steps:e,currentStepIndex:0};let i=e.map(e=>({...e,isCompleted:t.completedSteps.includes(e.id)})),n=function(e,t){let o=e.findIndex(e=>e.id===t);return o>=0?o:0}(i,t.currentStep);return{steps:i,currentStepIndex:n}}function c(e){let t=[],o=[],i=["completion","complete","finish","final"],n=e.filter(e=>!e.isOptional&&!i.includes(e.id.toLowerCase()));for(let t of(console.log("\uD83D\uDD0D Validating completion requirements:"),console.log("All steps:",e.map(e=>({id:e.id,title:e.title,completed:e.isCompleted,optional:e.isOptional}))),console.log("Required steps:",n.map(e=>({id:e.id,title:e.title,completed:e.isCompleted}))),n))t.isCompleted||o.push(t.title);console.log("Missing steps:",o),o.length>0&&t.push("Please complete the following required steps: ".concat(o.join(", ")));let s={canComplete:0===t.length,missingSteps:o,errors:t};return console.log("Validation result:",s),s}},39510:(e,t,o)=>{Promise.resolve().then(o.bind(o,66998))},66998:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>p});var i=o(95155),n=o(12115),s=o(27874);let l=()=>(0,i.jsx)("div",{children:"Mock Component"});function p(){let[e,t]=(0,n.useState)([]);return(0,i.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-6",children:(0,i.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:"Debug Onboarding Validation"}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Test the onboarding validation logic to debug completion issues"}),(0,i.jsx)("button",{onClick:()=>{let e=[];e.push("\uD83E\uDDEA Testing Onboarding Validation Logic...\n");let o=(0,s.uE)([{id:"welcome",title:"Welcome",description:"Welcome step",component:l,isCompleted:!1},{id:"profile",title:"Profile Setup",description:"Profile step",component:l,isCompleted:!1},{id:"preferences",title:"Preferences",description:"Preferences step",component:l,isCompleted:!1,isOptional:!0},{id:"completion",title:"Complete",description:"Completion step",component:l,isCompleted:!1}]);e.push("Test 1 - All incomplete:"),e.push("  Can complete: ".concat(o.canComplete," (expected: false)")),e.push("  Missing steps: [".concat(o.missingSteps.join(", "),"]")),e.push("  Errors: ".concat(o.errors.join("; "),"\n"));let i=(0,s.uE)([{id:"welcome",title:"Welcome",description:"Welcome step",component:l,isCompleted:!0},{id:"profile",title:"Profile Setup",description:"Profile step",component:l,isCompleted:!0},{id:"preferences",title:"Preferences",description:"Preferences step",component:l,isCompleted:!1,isOptional:!0},{id:"completion",title:"Complete",description:"Completion step",component:l,isCompleted:!1}]);e.push("Test 2 - Required complete, completion incomplete:"),e.push("  Can complete: ".concat(i.canComplete," (expected: true)")),e.push("  Missing steps: [".concat(i.missingSteps.join(", "),"]")),e.push("  Errors: ".concat(i.errors.join("; "),"\n"));let n=(0,s.uE)([{id:"welcome",title:"Welcome",description:"Welcome step",component:l,isCompleted:!0},{id:"profile",title:"Profile Setup",description:"Profile step",component:l,isCompleted:!0},{id:"preferences",title:"Preferences",description:"Preferences step",component:l,isCompleted:!0,isOptional:!0},{id:"completion",title:"Complete",description:"Completion step",component:l,isCompleted:!0}]);e.push("Test 3 - All complete:"),e.push("  Can complete: ".concat(n.canComplete," (expected: true)")),e.push("  Missing steps: [".concat(n.missingSteps.join(", "),"]")),e.push("  Errors: ".concat(n.errors.join("; "),"\n"));let p=(0,s.uE)([{id:"welcome",title:"Welcome",description:"Welcome step",component:l,isCompleted:!0},{id:"profile",title:"Profile Setup",description:"Profile step",component:l,isCompleted:!1},{id:"preferences",title:"Preferences",description:"Preferences step",component:l,isCompleted:!1,isOptional:!0},{id:"completion",title:"Complete",description:"Completion step",component:l,isCompleted:!1}]);e.push("Test 4 - Missing required step:"),e.push("  Can complete: ".concat(p.canComplete," (expected: false)")),e.push("  Missing steps: [".concat(p.missingSteps.join(", "),"]")),e.push("  Errors: ".concat(p.errors.join("; "),"\n"));let r=(0,s.uE)([{id:"welcome",title:"Welcome",description:"Welcome step",component:l,isCompleted:!0},{id:"profile",title:"Profile Setup",description:"Profile step",component:l,isCompleted:!0},{id:"finish",title:"Finish",description:"Finish step",component:l,isCompleted:!1}]);e.push("Test 5 - Different completion ID (finish):"),e.push("  Can complete: ".concat(r.canComplete," (expected: true)")),e.push("  Missing steps: [".concat(r.missingSteps.join(", "),"]")),e.push("  Errors: ".concat(r.errors.join("; "),"\n")),e.push("\uD83C\uDF89 Validation tests completed!"),t(e)},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg mb-6",children:"Run Validation Tests"}),e.length>0&&(0,i.jsx)("div",{className:"bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto",children:e.map((e,t)=>(0,i.jsx)("div",{className:"whitespace-pre-wrap",children:e},t))})]})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(39510)),_N_E=e.O()}]);