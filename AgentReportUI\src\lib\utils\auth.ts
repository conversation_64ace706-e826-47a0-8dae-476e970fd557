// Comprehensive authentication utilities

import { STORAGE_KEYS } from '@/lib/constants';
import { TokenResponse } from '@/types';
import { normalizeAuthResponse, validateAuthResponse } from './auth-error-handling';

/**
 * Type guard for TokenResponse
 */
export function isTokenResponse(data: any): data is TokenResponse {
  return validateAuthResponse(data);
}

/**
 * Normalize authentication response with legacy user support
 */
export function normalizeAuthResponseWithLegacy(response: any): TokenResponse {
  if (!validateAuthResponse(response)) {
    throw new Error('Invalid authentication response structure');
  }

  // Handle legacy users who may not have is_new_user field
  let isNewUser = response.is_new_user;
  
  if (isNewUser === undefined) {
    // For legacy users, determine new user status based on other factors
    // If user has a last_login date that's recent, they're likely not new
    if (response.last_login) {
      const lastLogin = new Date(response.last_login);
      const now = new Date();
      const daysSinceLastLogin = (now.getTime() - lastLogin.getTime()) / (1000 * 60 * 60 * 24);
      
      // If they logged in within the last 30 days, consider them existing user
      isNewUser = daysSinceLastLogin > 30;
    } else {
      // No last_login data, default to false for safety (existing user)
      isNewUser = false;
    }
  }

  return {
    access_token: response.access_token,
    token_type: response.token_type || 'bearer',
    expires_at: response.expires_at,
    refresh_token: response.refresh_token,
    user_id: response.user_id,
    is_new_user: isNewUser,
  };
}

/**
 * Check if token is expired or will expire soon
 */
export function isTokenExpired(expiresAt: string, bufferMinutes: number = 5): boolean {
  try {
    const expirationTime = new Date(expiresAt).getTime();
    const currentTime = new Date().getTime();
    const bufferTime = bufferMinutes * 60 * 1000; // Convert minutes to milliseconds
    
    return currentTime > (expirationTime - bufferTime);
  } catch (error) {
    console.error('Error checking token expiry:', error);
    return true; // Assume expired if we can't parse the date
  }
}

/**
 * Get stored authentication data
 */
export function getStoredAuthData(): {
  accessToken: string | null;
  refreshToken: string | null;
  userId: string | null;
  expiresAt: string | null;
  tokenType: string | null;
} {
  if (typeof window === 'undefined') {
    return {
      accessToken: null,
      refreshToken: null,
      userId: null,
      expiresAt: null,
      tokenType: null,
    };
  }

  return {
    accessToken: localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN),
    refreshToken: localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN),
    userId: localStorage.getItem(STORAGE_KEYS.USER_ID),
    expiresAt: localStorage.getItem(STORAGE_KEYS.EXPIRES_AT),
    tokenType: localStorage.getItem(STORAGE_KEYS.TOKEN_TYPE),
  };
}

/**
 * Store authentication data
 */
export function storeAuthData(tokenData: TokenResponse): void {
  if (typeof window === 'undefined') return;

  localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, tokenData.access_token);
  localStorage.setItem(STORAGE_KEYS.TOKEN_TYPE, tokenData.token_type);
  localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokenData.refresh_token);
  localStorage.setItem(STORAGE_KEYS.USER_ID, tokenData.user_id);
  localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, tokenData.expires_at);
}

/**
 * Clear all authentication data
 */
export function clearAuthData(): void {
  if (typeof window === 'undefined') return;

  localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
  localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
  localStorage.removeItem(STORAGE_KEYS.USER_ID);
  localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);
  localStorage.removeItem(STORAGE_KEYS.TOKEN_TYPE);
}

/**
 * Check if user is authenticated based on stored data
 */
export function isAuthenticated(): boolean {
  const { accessToken, expiresAt } = getStoredAuthData();
  
  if (!accessToken || !expiresAt) {
    return false;
  }
  
  return !isTokenExpired(expiresAt);
}

/**
 * Get current user ID from storage
 */
export function getCurrentUserId(): string | null {
  return getStoredAuthData().userId;
}

/**
 * Create TokenResponse from stored data
 */
export function createTokenResponseFromStorage(): TokenResponse | null {
  const storedData = getStoredAuthData();
  
  if (!storedData.accessToken || !storedData.userId || !storedData.refreshToken || !storedData.expiresAt) {
    return null;
  }
  
  return {
    access_token: storedData.accessToken,
    token_type: storedData.tokenType || 'bearer',
    expires_at: storedData.expiresAt,
    refresh_token: storedData.refreshToken,
    user_id: storedData.userId,
    // Note: is_new_user is not stored in localStorage, will be fetched from API
  };
}

/**
 * Validate and sanitize user input for authentication
 */
export function validateLoginCredentials(username: string, password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!username || username.trim().length === 0) {
    errors.push('Username is required');
  }
  
  if (!password || password.length === 0) {
    errors.push('Password is required');
  }
  
  if (username && username.length > 255) {
    errors.push('Username is too long');
  }
  
  if (password && password.length > 1000) {
    errors.push('Password is too long');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Generate a secure state parameter for OAuth
 */
export function generateOAuthState(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Validate OAuth state parameter
 */
export function validateOAuthState(receivedState: string, expectedState: string): boolean {
  return receivedState === expectedState && receivedState.length === 64;
}

/**
 * Extract OAuth tokens from URL parameters
 */
export function extractOAuthTokensFromUrl(searchParams: URLSearchParams): {
  accessToken: string | null;
  refreshToken: string | null;
  expiresIn: string | null;
  userId: string | null;
  tokenType: string | null;
  state: string | null;
  error: string | null;
} {
  return {
    accessToken: searchParams.get('access_token'),
    refreshToken: searchParams.get('refresh_token'),
    expiresIn: searchParams.get('expires_in'),
    userId: searchParams.get('user_id'),
    tokenType: searchParams.get('token_type'),
    state: searchParams.get('state'),
    error: searchParams.get('error'),
  };
}

/**
 * Format error message for user display
 */
export function formatAuthErrorForUser(error: any): string {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred. Please try again.';
}

/**
 * Check if authentication error requires logout
 */
export function shouldLogoutOnError(error: any): boolean {
  // 401 Unauthorized - token is invalid
  if (error?.response?.status === 401) {
    return true;
  }
  
  // Token-related errors
  if (error?.message?.includes('token') || error?.message?.includes('unauthorized')) {
    return true;
  }
  
  return false;
}
