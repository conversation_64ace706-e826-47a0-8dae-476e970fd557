"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { Dashboard } from '@/types';
import DashboardCard from './DashboardCard';

interface DashboardListProps {
  dashboards: Dashboard[];
  dashboardStats: Record<string, number>;
  onSelectDashboard: (dashboard: Dashboard) => void;
  onDeleteDashboard: (dashboardId: string) => void;
  className?: string;
}

const DashboardList: React.FC<DashboardListProps> = ({
  dashboards,
  dashboardStats,
  onSelectDashboard,
  onDeleteDashboard,
  className = '',
}) => {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Dashboard Grid */}
      <div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {dashboards.map((dashboard) => (
            <DashboardCard
              key={dashboard.id}
              dashboard={dashboard}
              chartCount={dashboardStats[dashboard.id] || 0}
              onSelect={onSelectDashboard}
              onDelete={onDeleteDashboard}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default DashboardList;
