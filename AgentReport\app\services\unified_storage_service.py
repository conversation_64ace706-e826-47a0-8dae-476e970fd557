"""
Unified Storage Service
========================
Provides a consistent, organized S3 file structure for all file operations.

Structure:
users/
├── {user_id}/
│   ├── uploads/                    # User-uploaded datasets
│   │   └── {dataset_id}.{ext}
│   └── sessions/
│       └── {session_id}/
│           ├── reports/            # Generated reports (CSV, XLSX)
│           │   └── {timestamp}_{type}.{ext}
│           ├── visualizations/     # Charts, graphs, images
│           │   └── {timestamp}_{chart_type}.png
│           ├── dashboards/         # HTML dashboards
│           │   └── dashboard.html
│           └── artifacts/          # JSON results, intermediate files
│               ├── exploration_results.json
│               ├── analysis_results.json
│               └── {stage}_results.json
"""

from __future__ import annotations

import uuid
import datetime as dt
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import mimetypes

import boto3
from botocore.client import Config
from botocore.exceptions import ClientError, NoCredentialsError

from app.config.settings import AWS_REGION, REPORTS_BUCKET, PRESIGN_TTL_SECONDS

logger = logging.getLogger(__name__)


class FileType:
    """File type categories for organized storage."""
    UPLOAD = "uploads"
    REPORT = "reports" 
    VISUALIZATION = "visualizations"
    DASHBOARD = "dashboards"
    ARTIFACT = "artifacts"


class UnifiedStorageService:
    """Unified S3 storage service with consistent file organization."""
    
    def __init__(self) -> None:
        self.bucket = REPORTS_BUCKET
        self.s3 = boto3.client(
            "s3", 
            region_name=AWS_REGION,
            config=Config(s3={"addressing_style": "virtual"})
        )

    # ─── Core Upload Methods ───
    
    def upload_bytes(
        self,
        blob: bytes,
        user_id: str,
        session_id: Optional[str] = None,
        file_type: str = FileType.REPORT,
        filename: Optional[str] = None,
        content_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Upload bytes to S3 with organized structure.
        
        Args:
            blob: File content as bytes
            user_id: User ID for organization
            session_id: Session ID (optional for uploads)
            file_type: Type of file (reports, visualizations, etc.)
            filename: Specific filename (optional, will generate if None)
            content_type: MIME type (will detect if None)
            
        Returns:
            Dict with key, url, filename, size, file_type
        """
        # Generate key based on file type and structure
        key = self._generate_key(
            user_id=user_id,
            session_id=session_id,
            file_type=file_type,
            filename=filename
        )
        
        # Detect content type if not provided
        if not content_type:
            content_type = self._detect_content_type(filename, blob)
        
        # Upload to S3
        try:
            self.s3.put_object(
                Bucket=self.bucket,
                Key=key,
                Body=blob,
                ContentType=content_type,
                ServerSideEncryption="AES256",
            )
            
            # Generate presigned URL
            url = self._presign(key)
            
            return {
                "key": key,
                "url": url,
                "filename": Path(key).name,
                "size": len(blob),
                "file_type": file_type,
                "content_type": content_type
            }
            
        except Exception as e:
            logger.error(f"Failed to upload to S3: {e}")
            raise Exception(f"Upload failed: {str(e)}")

    def download_bytes(self, key: str) -> bytes:
        """Download an object and return its bytes."""
        try:
            response = self.s3.get_object(Bucket=self.bucket, Key=key)
            return response['Body'].read()
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchKey':
                raise FileNotFoundError(f"Object not found: {key}")
            else:
                raise Exception(f"Failed to download {key}: {error_code}")

    # ─── Specialized Upload Methods ───
    
    def upload_user_dataset(
        self,
        blob: bytes,
        user_id: str,
        dataset_id: str,
        file_extension: str,
        content_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """Upload a user dataset file."""
        filename = f"{dataset_id}.{file_extension}"
        return self.upload_bytes(
            blob=blob,
            user_id=user_id,
            session_id=None,
            file_type=FileType.UPLOAD,
            filename=filename,
            content_type=content_type
        )
    
    def upload_report(
        self,
        blob: bytes,
        user_id: str,
        session_id: str,
        report_type: str = "analysis",
        file_extension: str = "csv",
        content_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """Upload a generated report file."""
        timestamp = dt.datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{report_type}.{file_extension}"
        return self.upload_bytes(
            blob=blob,
            user_id=user_id,
            session_id=session_id,
            file_type=FileType.REPORT,
            filename=filename,
            content_type=content_type
        )
    
    def upload_visualization(
        self,
        blob: bytes,
        user_id: str,
        session_id: str,
        chart_type: str = "chart",
        file_extension: str = "png",
        content_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """Upload a visualization file."""
        timestamp = dt.datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{chart_type}.{file_extension}"
        return self.upload_bytes(
            blob=blob,
            user_id=user_id,
            session_id=session_id,
            file_type=FileType.VISUALIZATION,
            filename=filename,
            content_type=content_type
        )
    
    def upload_dashboard(
        self,
        blob: bytes,
        user_id: str,
        session_id: str,
        content_type: str = "text/html"
    ) -> Dict[str, Any]:
        """Upload a dashboard HTML file."""
        return self.upload_bytes(
            blob=blob,
            user_id=user_id,
            session_id=session_id,
            file_type=FileType.DASHBOARD,
            filename="dashboard.html",
            content_type=content_type
        )
    
    def upload_artifact(
        self,
        blob: bytes,
        user_id: str,
        session_id: str,
        artifact_name: str,
        content_type: str = "application/json"
    ) -> Dict[str, Any]:
        """Upload an artifact file (JSON results, etc.)."""
        # Ensure artifact name has .json extension if it's JSON
        if content_type == "application/json" and not artifact_name.endswith('.json'):
            artifact_name = f"{artifact_name}.json"
            
        return self.upload_bytes(
            blob=blob,
            user_id=user_id,
            session_id=session_id,
            file_type=FileType.ARTIFACT,
            filename=artifact_name,
            content_type=content_type
        )

    # ─── Listing and Discovery Methods ───
    
    async def list_user_files(
        self,
        user_id: str,
        session_id: Optional[str] = None,
        file_type: Optional[str] = None,
        max_files: int = 100
    ) -> List[Dict[str, Any]]:
        """
        List files for a user, optionally filtered by session and/or file type.
        
        Args:
            user_id: User ID
            session_id: Session ID filter (optional)
            file_type: File type filter (optional)
            max_files: Maximum number of files to return
            
        Returns:
            List of file metadata dictionaries
        """
        try:
            # Build prefix based on filters
            if session_id and file_type:
                prefix = f"users/{user_id}/sessions/{session_id}/{file_type}/"
            elif session_id:
                prefix = f"users/{user_id}/sessions/{session_id}/"
            elif file_type == FileType.UPLOAD:
                prefix = f"users/{user_id}/uploads/"
            else:
                prefix = f"users/{user_id}/"
            
            logger.info(f"Listing files for user {user_id} with prefix: {prefix}")
            
            # List objects
            response = self.s3.list_objects_v2(
                Bucket=self.bucket,
                Prefix=prefix,
                MaxKeys=max_files
            )
            
            files = []
            for obj in response.get('Contents', []):
                try:
                    file_info = self._parse_file_info(obj)
                    files.append(file_info)
                except Exception as e:
                    logger.warning(f"Error parsing file {obj.get('Key', 'unknown')}: {e}")
                    continue
            
            # Sort by last modified (newest first)
            files.sort(key=lambda x: x['last_modified'], reverse=True)
            
            logger.info(f"Found {len(files)} files for user {user_id}")
            return files
            
        except Exception as e:
            logger.error(f"Failed to list files for user {user_id}: {e}")
            raise Exception(f"Failed to retrieve files: {str(e)}")

    async def list_session_files(
        self,
        user_id: str,
        session_id: str,
        max_files: int = 100
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        List all files for a specific session, organized by file type.
        
        Returns:
            Dict with keys: reports, visualizations, dashboards, artifacts
        """
        try:
            prefix = f"users/{user_id}/sessions/{session_id}/"
            
            response = self.s3.list_objects_v2(
                Bucket=self.bucket,
                Prefix=prefix,
                MaxKeys=max_files
            )
            
            organized_files = {
                FileType.REPORT: [],
                FileType.VISUALIZATION: [],
                FileType.DASHBOARD: [],
                FileType.ARTIFACT: []
            }
            
            for obj in response.get('Contents', []):
                try:
                    file_info = self._parse_file_info(obj)
                    file_type = file_info.get('file_type', 'unknown')
                    
                    if file_type in organized_files:
                        organized_files[file_type].append(file_info)
                        
                except Exception as e:
                    logger.warning(f"Error parsing session file {obj.get('Key', 'unknown')}: {e}")
                    continue
            
            # Sort each category by last modified
            for file_type in organized_files:
                organized_files[file_type].sort(
                    key=lambda x: x['last_modified'], 
                    reverse=True
                )
            
            return organized_files
            
        except Exception as e:
            logger.error(f"Failed to list session files for {user_id}/{session_id}: {e}")
            raise Exception(f"Failed to retrieve session files: {str(e)}")

    # ─── Helper Methods ───
    
    def _generate_key(
        self,
        user_id: str,
        session_id: Optional[str],
        file_type: str,
        filename: Optional[str] = None
    ) -> str:
        """Generate S3 key based on the unified structure."""
        
        if file_type == FileType.UPLOAD:
            # User uploads: users/{user_id}/uploads/{filename}
            if not filename:
                filename = f"{uuid.uuid4().hex}.dat"
            return f"users/{user_id}/uploads/{filename}"
        
        elif session_id:
            # Session files: users/{user_id}/sessions/{session_id}/{file_type}/{filename}
            if not filename:
                timestamp = dt.datetime.utcnow().strftime("%Y%m%d_%H%M%S")
                ext = self._get_extension_for_type(file_type)
                filename = f"{timestamp}_{uuid.uuid4().hex[:8]}.{ext}"
            
            return f"users/{user_id}/sessions/{session_id}/{file_type}/{filename}"
        
        else:
            # Fallback for edge cases
            if not filename:
                filename = f"{uuid.uuid4().hex}.dat"
            return f"users/{user_id}/misc/{filename}"

    def _parse_file_info(self, s3_object: Dict[str, Any]) -> Dict[str, Any]:
        """Parse S3 object into standardized file info."""
        key = s3_object['Key']
        parts = key.split('/')
        
        # Extract components from key
        if 'uploads' in parts:
            user_id = parts[1]
            session_id = None
            file_type = FileType.UPLOAD
            filename = parts[-1]
        elif 'sessions' in parts and len(parts) >= 5:
            user_id = parts[1]
            session_id = parts[3]
            file_type = parts[4]
            filename = parts[-1]
        else:
            # Fallback parsing
            user_id = parts[1] if len(parts) > 1 else "unknown"
            session_id = None
            file_type = "unknown"
            filename = parts[-1]

        # Get object metadata for content type
        try:
            head_response = self.s3.head_object(Bucket=self.bucket, Key=key)
            content_type = head_response.get('ContentType', 'application/octet-stream')
        except Exception:
            content_type = 'application/octet-stream'

        # Extract file format
        file_format = filename.split('.')[-1].lower() if '.' in filename else 'unknown'

        return {
            'key': key,
            'user_id': user_id,
            'session_id': session_id,
            'file_type': file_type,
            'filename': filename,
            'file_format': file_format,
            'size': s3_object['Size'],
            'last_modified': s3_object['LastModified'].isoformat(),
            'download_url': self._presign(key),
            'content_type': content_type
        }

    def _detect_content_type(self, filename: Optional[str], blob: bytes) -> str:
        """Detect content type from filename or blob."""
        if filename:
            content_type, _ = mimetypes.guess_type(filename)
            if content_type:
                return content_type
        
        # Fallback content type detection
        if blob.startswith(b'\x89PNG'):
            return 'image/png'
        elif blob.startswith(b'\xff\xd8\xff'):
            return 'image/jpeg'
        elif blob.startswith(b'PK'):
            return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif b'<!DOCTYPE html' in blob[:100] or b'<html' in blob[:100]:
            return 'text/html'
        elif filename and filename.endswith('.json'):
            return 'application/json'
        elif filename and filename.endswith('.csv'):
            return 'text/csv'
        else:
            return 'application/octet-stream'

    def _get_extension_for_type(self, file_type: str) -> str:
        """Get default file extension for file type."""
        defaults = {
            FileType.REPORT: "csv",
            FileType.VISUALIZATION: "png", 
            FileType.DASHBOARD: "html",
            FileType.ARTIFACT: "json",
            FileType.UPLOAD: "dat"
        }
        return defaults.get(file_type, "dat")

    def _presign(self, key: str) -> str:
        """Generate presigned URL for download."""
        return self.s3.generate_presigned_url(
            ClientMethod="get_object",
            Params={"Bucket": self.bucket, "Key": key},
            ExpiresIn=PRESIGN_TTL_SECONDS,
        )

    # ─── Legacy Support Methods ───
    
    def make_key(self, *, user_id: str, session_id: str, ext: str) -> str:
        """Legacy method for backward compatibility."""
        logger.warning("Using legacy make_key method - consider migrating to unified structure")
        timestamp = dt.datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{uuid.uuid4().hex[:8]}.{ext}"
        return f"users/{user_id}/sessions/{session_id}/reports/{filename}" 
