"""Database Models

This module defines the database models used by the application.
"""

from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field

class DatabaseType(str, Enum):
    """Enumeration of supported database types."""
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"
    SQLSERVER = "sqlserver"
    ORACLE = "oracle"
    MONGODB = "mongodb"
    SUPABASE = "supabase"

class DatabaseCredentials(BaseModel):
    """Credentials for connecting to a database."""
    host: str
    port: int
    username: str
    password: str
    database: str
    db_schema: Optional[str] = Field(None, alias="schema")  # Renamed to avoid conflict
    ssl_enabled: bool = False
    ssl_ca_cert: Optional[str] = None
    connection_options: Optional[Dict[str, Any]] = None

class Database(BaseModel):
    """Model representing a database connected to the system."""
    id: str
    user_id: str
    name: str
    description: Optional[str] = None
    db_type: DatabaseType
    credentials: DatabaseCredentials
    connection_string: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    agent_id: Optional[str] = None

class DatabaseTable(BaseModel):
    """Model representing a table in a database."""
    name: str
    db_schema: Optional[str] = Field(None, alias="schema")  # Renamed to avoid conflict
    description: Optional[str] = None
    columns: List["DatabaseColumn"] = []
    primary_keys: List[str] = []
    foreign_keys: List["ForeignKeyRelationship"] = []
    row_count: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None

class DatabaseColumn(BaseModel):
    """Model representing a column in a database table."""
    name: str
    data_type: str
    description: Optional[str] = None
    is_nullable: bool = True
    is_primary_key: bool = False
    is_foreign_key: bool = False
    is_unique: bool = False
    default_value: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class ForeignKeyRelationship(BaseModel):
    """Model representing a foreign key relationship."""
    column_name: str
    referenced_table_name: str
    referenced_column_name: str
    referenced_schema: Optional[str] = None

# Resolve forward references
DatabaseTable.update_forward_refs() 