"""
Database Query Agents Package

This package contains all agents responsible for the /api/ask/question endpoint.
These agents handle natural language to SQL query conversion and execution.

Agents:
- OrchestratorAgent: Main coordinator for database queries
- DatabaseManagerAgent: Database schema/metadata management
- SQLAgent: SQL generation and execution
- OutputAgent: Query result formatting
- PlannerAgent: Query planning (if used)
- QueryUnderstandingAgent: Enhanced query analysis and intent recognition
- AdvancedSQLAgent: Advanced SQL generation with validation
- DataInsightsAgent: Statistical analysis and insight generation (Phase 3)
- BusinessContextAgent: Domain-aware insight interpretation (Phase 3)
- ComparativeAnalysisAgent: Period-over-period and benchmarking analysis (Phase 3)
Pipeline Flow:
User Query → OrchestratorAgent → QueryUnderstandingAgent → DatabaseManagerAgent →
AdvancedSQLAgent → DataInsightsAgent → BusinessContextAgent → ComparativeAnalysisAgent →
OutputAgent
"""

from .orchestrator_agent import OrchestratorAgent
from .database_manager_agent import DatabaseManagerAgent
from .sql_agent import SQLAgent
from .output_agent import OutputAgent
from .planner_agent import Planner
from .database_agent import EnhancedDatabaseAgent
from .query_understanding_agent import QueryUnderstandingAgent
from .advanced_sql_agent import AdvancedSQLAgent

# Phase 3: Intelligent Data Analysis & Insights
try:
    from .data_insights_agent import DataInsightsAgent
    from .business_context_agent import BusinessContextAgent
    from .comparative_analysis_agent import ComparativeAnalysisAgent
    PHASE_3_AVAILABLE = True
except ImportError:
    # Phase 3 agents not yet implemented
    PHASE_3_AVAILABLE = False

__all__ = [
    "OrchestratorAgent",
    "DatabaseManagerAgent",
    "SQLAgent",
    "OutputAgent",
    "Planner",
    "EnhancedDatabaseAgent",
    "QueryUnderstandingAgent",
    "AdvancedSQLAgent"
]

# Add Phase 3 agents to exports if available
if PHASE_3_AVAILABLE:
    __all__.extend([
        "DataInsightsAgent",
        "BusinessContextAgent",
        "ComparativeAnalysisAgent"
    ])
