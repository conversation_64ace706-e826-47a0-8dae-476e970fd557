"use strict";exports.id=592,exports.ids=[592],exports.modules={12941:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},14952:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},26134:(e,t,r)=>{r.d(t,{UC:()=>ee,ZL:()=>J,bL:()=>z,bm:()=>et,hJ:()=>Q,l9:()=>Y});var n=r(43210),o=r(70569),a=r(98599),i=r(11273),l=r(96963),u=r(65551),s=r(31355),d=r(32547),c=r(25028),p=r(46059),f=r(14163),m=r(1359),h=r(42247),g=r(63376),v=r(8730),y=r(60687),w="Dialog",[x,M]=(0,i.A)(w),[b,R]=x(w),C=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:s=!0}=e,d=n.useRef(null),c=n.useRef(null),[p,f]=(0,u.i)({prop:o,defaultProp:a??!1,onChange:i,caller:w});return(0,y.jsx)(b,{scope:t,triggerRef:d,contentRef:c,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:s,children:r})};C.displayName=w;var D="DialogTrigger",j=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=R(D,r),l=(0,a.s)(t,i.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":V(i.open),...n,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});j.displayName=D;var k="DialogPortal",[A,I]=x(k,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,i=R(k,t);return(0,y.jsx)(A,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(p.C,{present:r||i.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};N.displayName=k;var _="DialogOverlay",E=n.forwardRef((e,t)=>{let r=I(_,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(_,e.__scopeDialog);return a.modal?(0,y.jsx)(p.C,{present:n||a.open,children:(0,y.jsx)(T,{...o,ref:t})}):null});E.displayName=_;var O=(0,v.TL)("DialogOverlay.RemoveScroll"),T=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(_,r);return(0,y.jsx)(h.A,{as:O,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":V(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",F=n.forwardRef((e,t)=>{let r=I(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(P,e.__scopeDialog);return(0,y.jsx)(p.C,{present:n||a.open,children:a.modal?(0,y.jsx)(S,{...o,ref:t}):(0,y.jsx)(L,{...o,ref:t})})});F.displayName=P;var S=n.forwardRef((e,t)=>{let r=R(P,e.__scopeDialog),i=n.useRef(null),l=(0,a.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,y.jsx)(G,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=n.forwardRef((e,t)=>{let r=R(P,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(G,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),G=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...u}=e,c=R(P,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,y.jsx)(s.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":V(c.open),...u,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(X,{titleId:c.titleId}),(0,y.jsx)($,{contentRef:p,descriptionId:c.descriptionId})]})]})}),K="DialogTitle";n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(K,r);return(0,y.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})}).displayName=K;var U="DialogDescription";n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(U,r);return(0,y.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})}).displayName=U;var B="DialogClose",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=R(B,r);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function V(e){return e?"open":"closed"}q.displayName=B;var W="DialogTitleWarning",[Z,H]=(0,i.q)(W,{contentName:P,titleName:K,docsSlug:"dialog"}),X=({titleId:e})=>{let t=H(W),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},$=({contentRef:e,descriptionId:t})=>{let r=H("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},z=C,Y=j,J=N,Q=E,ee=F,et=q},29398:(e,t,r)=>{r.d(t,{UC:()=>e3,q7:()=>e8,ZL:()=>e9,bL:()=>e6,l9:()=>e4});var n=r(43210),o=r(70569),a=r(98599),i=r(11273),l=r(65551),u=r(14163),s=r(9510),d=r(43),c=r(31355),p=r(1359),f=r(32547),m=r(96963),h=r(55509),g=r(25028),v=r(46059),y=r(13495),w=r(60687),x="rovingFocusGroup.onEntryFocus",M={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[R,C,D]=(0,s.N)(b),[j,k]=(0,i.A)(b,[D]),[A,I]=j(b),N=n.forwardRef((e,t)=>(0,w.jsx)(R.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(R.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(_,{...e,ref:t})})}));N.displayName=b;var _=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:s=!1,dir:c,currentTabStopId:p,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:m,onEntryFocus:h,preventScrollOnEntryFocus:g=!1,...v}=e,R=n.useRef(null),D=(0,a.s)(t,R),j=(0,d.jH)(c),[k,I]=(0,l.i)({prop:p,defaultProp:f??null,onChange:m,caller:b}),[N,_]=n.useState(!1),E=(0,y.c)(h),O=C(r),T=n.useRef(!1),[F,S]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(x,E),()=>e.removeEventListener(x,E)},[E]),(0,w.jsx)(A,{scope:r,orientation:i,dir:j,loop:s,currentTabStopId:k,onItemFocus:n.useCallback(e=>I(e),[I]),onItemShiftTab:n.useCallback(()=>_(!0),[]),onFocusableItemAdd:n.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>S(e=>e-1),[]),children:(0,w.jsx)(u.sG.div,{tabIndex:N||0===F?-1:0,"data-orientation":i,...v,ref:D,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{T.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!T.current;if(e.target===e.currentTarget&&t&&!N){let t=new CustomEvent(x,M);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=O().filter(e=>e.focusable);P([e.find(e=>e.active),e.find(e=>e.id===k),...e].filter(Boolean).map(e=>e.ref.current),g)}}T.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>_(!1))})})}),E="RovingFocusGroupItem",O=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:l,children:s,...d}=e,c=(0,m.B)(),p=l||c,f=I(E,r),h=f.currentTabStopId===p,g=C(r),{onFocusableItemAdd:v,onFocusableItemRemove:y,currentTabStopId:x}=f;return n.useEffect(()=>{if(a)return v(),()=>y()},[a,v,y]),(0,w.jsx)(R.ItemSlot,{scope:r,id:p,focusable:a,active:i,children:(0,w.jsx)(u.sG.span,{tabIndex:h?0:-1,"data-orientation":f.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?f.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>f.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=f.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>P(r))}}),children:"function"==typeof s?s({isCurrentTabStop:h,hasTabStop:null!=x}):s})})});O.displayName=E;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function P(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var F=r(8730),S=r(63376),L=r(42247),G=["Enter"," "],K=["ArrowUp","PageDown","End"],U=["ArrowDown","PageUp","Home",...K],B={ltr:[...G,"ArrowRight"],rtl:[...G,"ArrowLeft"]},q={ltr:["ArrowLeft"],rtl:["ArrowRight"]},V="Menu",[W,Z,H]=(0,s.N)(V),[X,$]=(0,i.A)(V,[H,h.Bk,k]),z=(0,h.Bk)(),Y=k(),[J,Q]=X(V),[ee,et]=X(V),er=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:i,modal:l=!0}=e,u=z(t),[s,c]=n.useState(null),p=n.useRef(!1),f=(0,y.c)(i),m=(0,d.jH)(a);return n.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,w.jsx)(h.bL,{...u,children:(0,w.jsx)(J,{scope:t,open:r,onOpenChange:f,content:s,onContentChange:c,children:(0,w.jsx)(ee,{scope:t,onClose:n.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:m,modal:l,children:o})})})};er.displayName=V;var en=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=z(r);return(0,w.jsx)(h.Mz,{...o,...n,ref:t})});en.displayName="MenuAnchor";var eo="MenuPortal",[ea,ei]=X(eo,{forceMount:void 0}),el=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=Q(eo,t);return(0,w.jsx)(ea,{scope:t,forceMount:r,children:(0,w.jsx)(v.C,{present:r||a.open,children:(0,w.jsx)(g.Z,{asChild:!0,container:o,children:n})})})};el.displayName=eo;var eu="MenuContent",[es,ed]=X(eu),ec=n.forwardRef((e,t)=>{let r=ei(eu,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=Q(eu,e.__scopeMenu),i=et(eu,e.__scopeMenu);return(0,w.jsx)(W.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(v.C,{present:n||a.open,children:(0,w.jsx)(W.Slot,{scope:e.__scopeMenu,children:i.modal?(0,w.jsx)(ep,{...o,ref:t}):(0,w.jsx)(ef,{...o,ref:t})})})})}),ep=n.forwardRef((e,t)=>{let r=Q(eu,e.__scopeMenu),i=n.useRef(null),l=(0,a.s)(t,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,S.Eq)(e)},[]),(0,w.jsx)(eh,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),ef=n.forwardRef((e,t)=>{let r=Q(eu,e.__scopeMenu);return(0,w.jsx)(eh,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),em=(0,F.TL)("MenuContent.ScrollLock"),eh=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:i=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:m,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:x,onDismiss:M,disableOutsideScroll:b,...R}=e,C=Q(eu,r),D=et(eu,r),j=z(r),k=Y(r),A=Z(r),[I,_]=n.useState(null),E=n.useRef(null),O=(0,a.s)(t,E,C.onContentChange),T=n.useRef(0),P=n.useRef(""),F=n.useRef(0),S=n.useRef(null),G=n.useRef("right"),B=n.useRef(0),q=b?L.A:n.Fragment,V=e=>{let t=P.current+e,r=A().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,a=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=Math.max(a,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(i=i.filter(e=>e!==r));let l=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}(r.map(e=>e.textValue),t,o),i=r.find(e=>e.textValue===a)?.ref.current;!function e(t){P.current=t,window.clearTimeout(T.current),""!==t&&(T.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};n.useEffect(()=>()=>window.clearTimeout(T.current),[]),(0,p.Oh)();let W=n.useCallback(e=>G.current===S.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],l=t[a],u=i.x,s=i.y,d=l.x,c=l.y;s>n!=c>n&&r<(d-u)*(n-s)/(c-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,S.current?.area),[]);return(0,w.jsx)(es,{scope:r,searchRef:P,onItemEnter:n.useCallback(e=>{W(e)&&e.preventDefault()},[W]),onItemLeave:n.useCallback(e=>{W(e)||(E.current?.focus(),_(null))},[W]),onTriggerLeave:n.useCallback(e=>{W(e)&&e.preventDefault()},[W]),pointerGraceTimerRef:F,onPointerGraceIntentChange:n.useCallback(e=>{S.current=e},[]),children:(0,w.jsx)(q,{...b?{as:em,allowPinchZoom:!0}:void 0,children:(0,w.jsx)(f.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),E.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,w.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:x,onDismiss:M,children:(0,w.jsx)(N,{asChild:!0,...k,dir:D.dir,orientation:"vertical",loop:i,currentTabStopId:I,onCurrentTabStopIdChange:_,onEntryFocus:(0,o.m)(m,e=>{D.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,w.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eU(C.open),"data-radix-menu-content":"",dir:D.dir,...j,...R,ref:O,style:{outline:"none",...R.style},onKeyDown:(0,o.m)(R.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&V(e.key));let o=E.current;if(e.target!==o||!U.includes(e.key))return;e.preventDefault();let a=A().filter(e=>!e.disabled).map(e=>e.ref.current);K.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(T.current),P.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eV(e=>{let t=e.target,r=B.current!==e.clientX;e.currentTarget.contains(t)&&r&&(G.current=e.clientX>B.current?"right":"left",B.current=e.clientX)}))})})})})})})});ec.displayName=eu;var eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,w.jsx)(u.sG.div,{role:"group",...n,ref:t})});eg.displayName="MenuGroup";var ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,w.jsx)(u.sG.div,{...n,ref:t})});ev.displayName="MenuLabel";var ey="MenuItem",ew="menu.itemSelect",ex=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:i,...l}=e,s=n.useRef(null),d=et(ey,e.__scopeMenu),c=ed(ey,e.__scopeMenu),p=(0,a.s)(t,s),f=n.useRef(!1);return(0,w.jsx)(eM,{...l,ref:p,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!r&&e){let t=new CustomEvent(ew,{bubbles:!0,cancelable:!0});e.addEventListener(ew,e=>i?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{f.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;r||t&&" "===e.key||G.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ex.displayName=ey;var eM=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:i=!1,textValue:l,...s}=e,d=ed(ey,r),c=Y(r),p=n.useRef(null),f=(0,a.s)(t,p),[m,h]=n.useState(!1),[g,v]=n.useState("");return n.useEffect(()=>{let e=p.current;e&&v((e.textContent??"").trim())},[s.children]),(0,w.jsx)(W.ItemSlot,{scope:r,disabled:i,textValue:l??g,children:(0,w.jsx)(O,{asChild:!0,...c,focusable:!i,children:(0,w.jsx)(u.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...s,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eV(e=>{i?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eV(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),eb=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,w.jsx)(eN,{scope:e.__scopeMenu,checked:r,children:(0,w.jsx)(ex,{role:"menuitemcheckbox","aria-checked":eB(r)?"mixed":r,...a,ref:t,"data-state":eq(r),onSelect:(0,o.m)(a.onSelect,()=>n?.(!!eB(r)||!r),{checkForDefaultPrevented:!1})})})});eb.displayName="MenuCheckboxItem";var eR="MenuRadioGroup",[eC,eD]=X(eR,{value:void 0,onValueChange:()=>{}}),ej=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,y.c)(n);return(0,w.jsx)(eC,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,w.jsx)(eg,{...o,ref:t})})});ej.displayName=eR;var ek="MenuRadioItem",eA=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=eD(ek,e.__scopeMenu),i=r===a.value;return(0,w.jsx)(eN,{scope:e.__scopeMenu,checked:i,children:(0,w.jsx)(ex,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":eq(i),onSelect:(0,o.m)(n.onSelect,()=>a.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});eA.displayName=ek;var eI="MenuItemIndicator",[eN,e_]=X(eI,{checked:!1}),eE=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=e_(eI,r);return(0,w.jsx)(v.C,{present:n||eB(a.checked)||!0===a.checked,children:(0,w.jsx)(u.sG.span,{...o,ref:t,"data-state":eq(a.checked)})})});eE.displayName=eI;var eO=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,w.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});eO.displayName="MenuSeparator";var eT=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=z(r);return(0,w.jsx)(h.i3,{...o,...n,ref:t})});eT.displayName="MenuArrow";var[eP,eF]=X("MenuSub"),eS="MenuSubTrigger",eL=n.forwardRef((e,t)=>{let r=Q(eS,e.__scopeMenu),i=et(eS,e.__scopeMenu),l=eF(eS,e.__scopeMenu),u=ed(eS,e.__scopeMenu),s=n.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=u,p={__scopeMenu:e.__scopeMenu},f=n.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return n.useEffect(()=>f,[f]),n.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,w.jsx)(en,{asChild:!0,...p,children:(0,w.jsx)(eM,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":eU(r.open),...e,ref:(0,a.t)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eV(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eV(e=>{f();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,a=t[o?"left":"right"],i=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:a,y:t.bottom}],side:n}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==u.searchRef.current;e.disabled||n&&" "===t.key||B[i.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eL.displayName=eS;var eG="MenuSubContent",eK=n.forwardRef((e,t)=>{let r=ei(eu,e.__scopeMenu),{forceMount:i=r.forceMount,...l}=e,u=Q(eu,e.__scopeMenu),s=et(eu,e.__scopeMenu),d=eF(eG,e.__scopeMenu),c=n.useRef(null),p=(0,a.s)(t,c);return(0,w.jsx)(W.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(v.C,{present:i||u.open,children:(0,w.jsx)(W.Slot,{scope:e.__scopeMenu,children:(0,w.jsx)(eh,{id:d.contentId,"aria-labelledby":d.triggerId,...l,ref:p,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{s.isUsingKeyboardRef.current&&c.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=q[s.dir].includes(e.key);t&&r&&(u.onOpenChange(!1),d.trigger?.focus(),e.preventDefault())})})})})})});function eU(e){return e?"open":"closed"}function eB(e){return"indeterminate"===e}function eq(e){return eB(e)?"indeterminate":e?"checked":"unchecked"}function eV(e){return t=>"mouse"===t.pointerType?e(t):void 0}eK.displayName=eG;var eW="DropdownMenu",[eZ,eH]=(0,i.A)(eW,[$]),eX=$(),[e$,ez]=eZ(eW),eY=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:i,onOpenChange:u,modal:s=!0}=e,d=eX(t),c=n.useRef(null),[p,f]=(0,l.i)({prop:a,defaultProp:i??!1,onChange:u,caller:eW});return(0,w.jsx)(e$,{scope:t,triggerId:(0,m.B)(),triggerRef:c,contentId:(0,m.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:s,children:(0,w.jsx)(er,{...d,open:p,onOpenChange:f,dir:o,modal:s,children:r})})};eY.displayName=eW;var eJ="DropdownMenuTrigger",eQ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...i}=e,l=ez(eJ,r),s=eX(r);return(0,w.jsx)(en,{asChild:!0,...s,children:(0,w.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...i,ref:(0,a.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eQ.displayName=eJ;var e0=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eX(t);return(0,w.jsx)(el,{...n,...r})};e0.displayName="DropdownMenuPortal";var e1="DropdownMenuContent",e5=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,i=ez(e1,r),l=eX(r),u=n.useRef(!1);return(0,w.jsx)(ec,{id:i.contentId,"aria-labelledby":i.triggerId,...l,...a,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||i.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!i.modal||n)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e5.displayName=e1,n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eX(r);return(0,w.jsx)(eg,{...o,...n,ref:t})}).displayName="DropdownMenuGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eX(r);return(0,w.jsx)(ev,{...o,...n,ref:t})}).displayName="DropdownMenuLabel";var e2=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eX(r);return(0,w.jsx)(ex,{...o,...n,ref:t})});e2.displayName="DropdownMenuItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eX(r);return(0,w.jsx)(eb,{...o,...n,ref:t})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eX(r);return(0,w.jsx)(ej,{...o,...n,ref:t})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eX(r);return(0,w.jsx)(eA,{...o,...n,ref:t})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eX(r);return(0,w.jsx)(eE,{...o,...n,ref:t})}).displayName="DropdownMenuItemIndicator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eX(r);return(0,w.jsx)(eO,{...o,...n,ref:t})}).displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eX(r);return(0,w.jsx)(eT,{...o,...n,ref:t})}).displayName="DropdownMenuArrow",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eX(r);return(0,w.jsx)(eL,{...o,...n,ref:t})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eX(r);return(0,w.jsx)(eK,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e6=eY,e4=eQ,e9=e0,e3=e5,e8=e2},31158:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},40083:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},46059:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(43210),o=r(98599),a=r(66156),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),u=n.useRef(null),s=n.useRef(e),d=n.useRef("none"),[c,p]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=l(u.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=u.current,r=s.current;if(r!==e){let n=d.current,o=l(t);e?p("MOUNT"):"none"===o||t?.display==="none"?p("UNMOUNT"):r&&n!==o?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=e}},[e,p]),(0,a.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,r=r=>{let n=l(u.current).includes(r.animationName);if(r.target===o&&n&&(p("ANIMATION_END"),!s.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(d.current=l(u.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{u.current=e?getComputedStyle(e):null,i(e)},[])}}(t),u="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),s=(0,o.s)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof r||i.isPresent?n.cloneElement(u,{ref:s}):null};function l(e){return e?.animationName||"none"}i.displayName="Presence"},49625:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},78200:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},81904:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},94713:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("message-circle-plus",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},96474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};