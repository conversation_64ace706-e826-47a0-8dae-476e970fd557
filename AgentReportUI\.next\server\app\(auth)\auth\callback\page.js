(()=>{var e={};e.id=73,e.ids=[73],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28027:(e,t,r)=>{Promise.resolve().then(r.bind(r,68717))},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46770:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),n=r(48088),i=r(88170),a=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["(auth)",{children:["auth",{children:["callback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68717)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(auth)\\auth\\callback\\page.tsx"]}]},{}]},{}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(auth)\\auth\\callback\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(auth)/auth/callback/page",pathname:"/auth/callback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65839:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),n=r(43210),i=r(16189),a=r(91010);function o(){(0,i.useRouter)(),(0,i.useSearchParams)();let{setUser:e}=(0,a.A)(),[t,r]=(0,n.useState)("loading"),[o,l]=(0,n.useState)("");return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8 p-8",children:(0,s.jsxs)("div",{className:"text-center",children:["loading"===t&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Completing sign in..."}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Please wait while we authenticate you with Google."})]}),"success"===t&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Sign in successful!"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Redirecting to dashboard..."})]}),"error"===t&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Authentication failed"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:o}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Redirecting to login page..."})]})]})})})}function l(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8 p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Loading..."}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Preparing authentication..."})]})})})}function d(){return(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)(l,{}),children:(0,s.jsx)(o,{})})}r(97616)},68717:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\NXTHUMANS\\\\internal-solutions-nxtwebsite\\\\AgentReport 2\\\\AgentReportUI\\\\src\\\\app\\\\(auth)\\\\auth\\\\callback\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\NXTHUMANS\\internal-solutions-nxtwebsite\\AgentReport 2\\AgentReportUI\\src\\app\\(auth)\\auth\\callback\\page.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81171:(e,t,r)=>{Promise.resolve().then(r.bind(r,65839))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,255,658,695],()=>r(46770));module.exports=s})();